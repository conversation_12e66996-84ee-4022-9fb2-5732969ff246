RSID_TTOTAN_START, 2000,,,
 ,[Offset], TALES_FLYER_1, 0,
 ,[Offset], Tales\PBTTotan, 1,
 ,[Offset], Tales\InstructionsENG, 2,
 ,[Offset], Tales\InstructionsFR, 3,
 ,[Offset], Tales\InstructionsITAL, 4,
 ,[Offset], Tales\InstructionsGERM, 5,
 ,[Offset], Tales\InstructionsSPAN, 6,
 ,[Offset], Tales\InstructionsENG, 7,
 ,[Offset], Tales\InstructionsENG, 8,
 ,[Offset], tables\Tales_BG_scroll, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TTOTAN_LIGHTS, 2001,,,
RSID_TTOTAN_CAMERAS, 2002,,,
RSID_TTOTAN_LAMP_TEXTURES, 2003,,,
 ,[Offset], L01_on, 0,
 ,[Offset], L01_off, 1,
 ,[Offset], L02_on, 2,
 ,[Offset], L02_off, 3,
 ,[Offset], L03_on, 4,
 ,[Offset], L03_off, 5,
 ,[Offset], L04_on, 6,
 ,[Offset], L04_off, 7,
 ,[Offset], L05_on, 8,
 ,[Offset], L05_off, 9,
 ,[Offset], L06_on, 10,
 ,[Offset], L06_off, 11,
 ,[Offset], L07_on, 12,
 ,[Offset], L07_off, 13,
 ,[Offset], L08_on, 14,
 ,[Offset], L08_off, 15,
 ,[Offset], L09_on, 16,
 ,[Offset], L09_off, 17,
 ,[Offset], L10_on, 18,
 ,[Offset], L10_off, 19,
 ,[Offset], L11_on, 20,
 ,[Offset], L11_off, 21,
 ,[Offset], L12_on, 22,
 ,[Offset], L12_off, 23,
 ,[Offset], L13_on, 24,
 ,[Offset], L13_off, 25,
 ,[Offset], L14_on, 26,
 ,[Offset], L14_off, 27,
 ,[Offset], L15_on, 28,
 ,[Offset], L15_off, 29,
 ,[Offset], L16_on, 30,
 ,[Offset], L16_off, 31,
 ,[Offset], L17_on, 32,
 ,[Offset], L17_off, 33,
 ,[Offset], L18_on, 34,
 ,[Offset], L18_off, 35,
 ,[Offset], L19_on, 36,
 ,[Offset], L19_off, 37,
 ,[Offset], L20_on, 38,
 ,[Offset], L20_off, 39,
 ,[Offset], L21_on, 40,
 ,[Offset], L21_off, 41,
 ,[Offset], L22_on, 42,
 ,[Offset], L22_off, 43,
 ,[Offset], L23_on, 44,
 ,[Offset], L23_off, 45,
 ,[Offset], L24_on, 46,
 ,[Offset], L24_off, 47,
 ,[Offset], L25_on, 48,
 ,[Offset], L25_off, 49,
 ,[Offset], L26_on, 50,
 ,[Offset], L26_off, 51,
 ,[Offset], L27_on, 52,
 ,[Offset], L27_off, 53,
 ,[Offset], L28_on, 54,
 ,[Offset], L28_off, 55,
 ,[Offset], L29_on, 56,
 ,[Offset], L29_off, 57,
 ,[Offset], L30_on, 58,
 ,[Offset], L30_off, 59,
 ,[Offset], L31_on, 60,
 ,[Offset], L31_off, 61,
 ,[Offset], L32_on, 62,
 ,[Offset], L32_off, 63,
 ,[Offset], L33_on, 64,
 ,[Offset], L33_off, 65,
 ,[Offset], L34_on, 66,
 ,[Offset], L34_off, 67,
 ,[Offset], L35_on, 68,
 ,[Offset], L35_off, 69,
 ,[Offset], L36_on, 70,
 ,[Offset], L36_off, 71,
 ,[Offset], L37_on, 72,
 ,[Offset], L37_off, 73,
 ,[Offset], L38_on, 74,
 ,[Offset], L38_off, 75,
 ,[Offset], L39_on, 76,
 ,[Offset], L39_off, 77,
 ,[Offset], L40_on, 78,
 ,[Offset], L40_off, 79,
 ,[Offset], L41_on, 80,
 ,[Offset], L41_off, 81,
 ,[Offset], L42_on, 82,
 ,[Offset], L42_off, 83,
 ,[Offset], L43_on, 84,
 ,[Offset], L43_off, 85,
 ,[Offset], L44_on, 86,
 ,[Offset], L44_off, 87,
 ,[Offset], L45_on, 88,
 ,[Offset], L45_off, 89,
 ,[Offset], L46_on, 90,
 ,[Offset], L46_off, 91,
 ,[Offset], L47_on, 92,
 ,[Offset], L47_off, 93,
 ,[Offset], L48_on, 94,
 ,[Offset], L48_off, 95,
 ,[Offset], L49_on, 96,
 ,[Offset], L49_off, 97,
 ,[Offset], L50_on, 98,
 ,[Offset], L50_off, 99,
 ,[Offset], L51_on, 100,
 ,[Offset], L51_off, 101,
 ,[Offset], L52_on, 102,
 ,[Offset], L52_off, 103,
 ,[Offset], L53_on, 104,
 ,[Offset], L53_off, 105,
 ,[Offset], L54_on, 106,
 ,[Offset], L54_off, 107,
 ,[Offset], L55_on, 108,
 ,[Offset], L55_off, 109,
 ,[Offset], L56_on, 110,
 ,[Offset], L56_off, 111,
 ,[Offset], L57_on, 112,
 ,[Offset], L57_off, 113,
 ,[Offset], L58_on, 114,
 ,[Offset], L58_off, 115,
 ,[Offset], L59_on, 116,
 ,[Offset], L59_off, 117,
 ,[Offset], L60_on, 118,
 ,[Offset], L60_off, 119,
 ,[Offset], L61_on, 120,
 ,[Offset], L61_off, 121,
 ,[Offset], L62_on, 122,
 ,[Offset], L62_off, 123,
 ,[Offset], L63_on, 124,
 ,[Offset], L63_off, 125,
 ,[Offset], L64_on, 126,
 ,[Offset], L64_off, 127,
 ,[Offset], L65_on, 128,
 ,[Offset], L65_off, 129,
 ,[Offset], L66_on, 130,
 ,[Offset], L66_off, 131,
 ,[Offset], L67_on, 132,
 ,[Offset], L67_off, 133,
 ,[Offset], L68_on, 134,
 ,[Offset], L68_off, 135,
 ,[Offset], L69_on, 136,
 ,[Offset], L69_off, 137,
 ,[Offset], L70_on, 138,
 ,[Offset], L70_off, 139,
 ,[Offset], L71_on, 140,
 ,[Offset], L71_off, 141,
 ,[Offset], L72_on, 142,
 ,[Offset], L72_off, 143,
 ,[Offset], L73_on, 144,
 ,[Offset], L73_off, 145,
 ,[Offset], L74_on, 146,
 ,[Offset], L74_off, 147,
 ,[Offset], L75_on, 148,
 ,[Offset], L75_off, 149,
 ,[Offset], L76_on, 150,
 ,[Offset], L76_off, 151,
 ,[Offset], L77_on, 152,
 ,[Offset], L77_off, 153,
 ,[Offset], L78_on, 154,
 ,[Offset], L78_off, 155,
 ,[Offset], L100_on, 156,
 ,[Offset], L100_off, 157,
RSID_TTOTAN_TEXTURES, 2004,,,
 ,[Offset], L100_off, 0,
 ,[Offset], color wheel, 1,
 ,[Offset], flipper, 2,
 ,[Offset], Buttons_Parts, 3,
 ,[Offset], CoinSlots, 4,
 ,[Offset], color wheel, 5,
 ,[Offset], decal, 6,
 ,[Offset], floor01a_0, 7,
 ,[Offset], floor01b_0, 8,
 ,[Offset], floor02a_0, 9,
 ,[Offset], floor02b_0, 10,
 ,[Offset], floor01c_0, 11,
 ,[Offset], floor01d_0, 12,
 ,[Offset], floor02c_0, 13,
 ,[Offset], floor02d_0, 14,
 ,[Offset], floor03a_0, 15,
 ,[Offset], floor03b_0, 16,
 ,[Offset], floor04a_0, 17,
 ,[Offset], floor04b_0, 18,
 ,[Offset], floor03c_0, 19,
 ,[Offset], floor03d_0, 20,
 ,[Offset], floor04c_0, 21,
 ,[Offset], floor04d_0, 22,
 ,[Offset], floor05a_0, 23,
 ,[Offset], floor05b_0, 24,
 ,[Offset], floor06a_0, 25,
 ,[Offset], floor06b_0, 26,
 ,[Offset], floor05c_0, 27,
 ,[Offset], floor05d_0, 28,
 ,[Offset], floor06c_0, 29,
 ,[Offset], floor06d_0, 30,
 ,[Offset], floor07a_0, 31,
 ,[Offset], floor07b_0, 32,
 ,[Offset], floor08a_0, 33,
 ,[Offset], floor08b_0, 34,
 ,[Offset], floor07c_0, 35,
 ,[Offset], floor07d_0, 36,
 ,[Offset], floor08c_0, 37,
 ,[Offset], floor08d_0, 38,
 ,[Offset], lamp_temp, 39,
 ,[Offset], glass, 40,
 ,[Offset], tmp_gray, 41,
 ,[Offset], post, 42,
 ,[Offset], rubber color, 43,
 ,[Offset], rubber, 44,
 ,[Offset], speaker, 45,
 ,[Offset], metal front, 46,
 ,[Offset], lantern, 47,
 ,[Offset], metal_bronze, 48,
 ,[Offset], metal-parts01 copy, 49,
 ,[Offset], Plastic_A10, 50,
 ,[Offset], Plastic_A11, 51,
 ,[Offset], Plastic_A12, 52,
 ,[Offset], Plastic_A13, 53,
 ,[Offset], Plastic_A14, 54,
 ,[Offset], Plastic_A15, 55,
 ,[Offset], Plastic_A2, 56,
 ,[Offset], Plastic_A3, 57,
 ,[Offset], Plastic_A4, 58,
 ,[Offset], Plastic_A5, 59,
 ,[Offset], Plastic_A6, 60,
 ,[Offset], ball, 61,
 ,[Offset], ball2, 62,
 ,[Offset], Plastic_A7, 63,
 ,[Offset], Plastic_A8, 64,
 ,[Offset], Plastic_A9, 65,
 ,[Offset], Trans_1, 66,
 ,[Offset], plunger, 67,
 ,[Offset], screw alt, 68,
 ,[Offset], screw, 69,
 ,[Offset], Tales_BackGlass, 70,
 ,[Offset], Tales_BackPlate_S, 71,
 ,[Offset], Tales_LowerTable_L, 72,
 ,[Offset], Tales_LowerTable_R, 73,
 ,[Offset], Tales_Table_All, 74,
 ,[Offset], target, 75,
 ,[Offset], target2, 76,
 ,[Offset], wood_strip, 77,
 ,[Offset], trap, 78,
 ,[Offset], magnet_wheel, 79,
 ,[Offset], table_trimmetal01, 80,
 ,[Offset], Plunger_Plate_Baked, 81,
 ,[Offset], plunger_Metal, 82,
 ,[Offset], metal_legs, 83,
 ,[Offset], metal_trim, 84,
 ,[Offset], Tales_LowerTable, 85,
 ,[Offset], lightbulb, 86,
 ,[Offset], Plastic_A1, 87,
 ,[Offset], Plastic_A1b, 88,
 ,[Offset], plastic_clear01, 89,
 ,[Offset], plastic_clear02, 90,
 ,[Offset], plastic_clear03, 91,
 ,[Offset], post red, 92,
 ,[Offset], swords_clear, 93,
 ,[Offset], target3, 94,
 ,[Offset], metal-parts01, 95,
 ,[Offset], Metal_Trim2, 96,
 ,[Offset], Tales_Lamp_Dif, 97,
 ,[Offset], Genie_Diffuse, 98,
 ,[Offset], color wheel, 99,
 ,[Offset], basemetalshot, 100,
 ,[Offset], habitrail, 101,
RSID_TTOTAN_MODELS, 2005,,,
 ,[Offset], \Projects\PinballStern\Data\Common\Tables\TableObjects\Flippers\flipper A, 0,
 ,[Offset], \Projects\PinballStern\Data\Common\Tables\TableObjects\Flippers\flipper A bottom, 1,
 ,[Offset], \Projects\PinballStern\Data\Common\Tables\TableObjects\Flippers\flipper B, 2,
 ,[Offset], \Projects\PinballStern\Data\Common\Tables\TableObjects\Flippers\flipper B bottom, 3,
 ,[Offset], plunger, 4,
 ,[Offset], tile, 5,
 ,[Offset], target A, 6,
 ,[Offset], target alt, 7,
 ,[Offset], target alt2, 8,
 ,[Offset], slingshot A, 9,
 ,[Offset], slingshot A ext, 10,
 ,[Offset], slingshot B, 11,
 ,[Offset], slingshot B ext, 12,
 ,[Offset], pop bumper A, 13,
 ,[Offset], wire A, 14,
 ,[Offset], magnet A, 15,
 ,[Offset], magnet B, 16,
 ,[Offset], trap A, 17,
 ,[Offset], trap B, 18,
 ,[Offset], trap C, 19,
 ,[Offset], generic A, 20,
 ,[Offset], generic B, 21,
 ,[Offset], generic C, 22,
 ,[Offset], generic D, 23,
 ,[Offset], generic E, 24,
 ,[Offset], generic F, 25,
 ,[Offset], generic G, 26,
 ,[Offset], generic H, 27,
 ,[Offset], generic I, 28,
 ,[Offset], generic J, 29,
 ,[Offset], generic K, 30,
 ,[Offset], generic L, 31,
 ,[Offset], generic M, 32,
 ,[Offset], generic N, 33,
 ,[Offset], Table_Floor, 34,
 ,[Offset], Table_Clip, 35,
 ,[Offset], Backglass_Reflection, 36,
 ,[Offset], Table_Glass, 37,
 ,[Offset], Table, 38,
 ,[Offset], Metal_Parts, 39,
 ,[Offset], Table_Lights, 40,
 ,[Offset], Transparent_Parts, 41,
 ,[Offset], Transparent02, 42,
 ,[Offset], Transparent01, 43,
RSID_TTOTAN_MODELS_LODS, 2006,,,
 ,[Offset], flipper A, 0,
 ,[Offset], flipper A bottom, 1,
 ,[Offset], flipper B, 2,
 ,[Offset], flipper B bottom, 3,
 ,[Offset], plunger, 4,
 ,[Offset], tile, 5,
 ,[Offset], target A, 6,
 ,[Offset], target alt, 7,
 ,[Offset], target alt2, 8,
 ,[Offset], slingshot A, 9,
 ,[Offset], slingshot A ext, 10,
 ,[Offset], slingshot B, 11,
 ,[Offset], slingshot B ext, 12,
 ,[Offset], pop bumper A, 13,
 ,[Offset], wire A, 14,
 ,[Offset], magnet A, 15,
 ,[Offset], magnet B, 16,
 ,[Offset], trap A, 17,
 ,[Offset], trap B, 18,
 ,[Offset], trap C, 19,
 ,[Offset], generic A, 20,
 ,[Offset], generic B, 21,
 ,[Offset], generic C, 22,
 ,[Offset], generic D, 23,
 ,[Offset], generic E, 24,
 ,[Offset], generic F, 25,
 ,[Offset], generic G, 26,
 ,[Offset], generic H, 27,
 ,[Offset], generic I, 28,
 ,[Offset], generic J, 29,
 ,[Offset], generic K, 30,
 ,[Offset], generic L, 31,
 ,[Offset], generic M, 32,
 ,[Offset], generic N, 33,
 ,[Offset], Table_Floor, 34,
 ,[Offset], Table_Clip, 35,
 ,[Offset], Backglass_Reflection, 36,
 ,[Offset], Table_Glass, 37,
 ,[Offset], Table, 38,
 ,[Offset], Metal_Parts, 39,
 ,[Offset], Table_Lights, 40,
 ,[Offset], Transparent_Parts, 41,
 ,[Offset], Transparent02, 42,
 ,[Offset], Transparent01, 43,
RSID_TTOTAN_COLLISIONS, 2007,,,
 ,[Offset], Collision_Wall, 0,
 ,[Offset], Collision_Floor, 1,
 ,[Offset], Collision_Arc, 2,
 ,[Offset], Collision_Arc, 3,
 ,[Offset], Collision_Alt_Wall, 4,
 ,[Offset], Collision_Alt_Skill, 5,
 ,[Offset], Collision_Alt_Skill, 6,
 ,[Offset], flipper A F col, 7,
 ,[Offset], flipper A B col, 8,
 ,[Offset], flipper B F col, 9,
 ,[Offset], flipper B B col, 10,
 ,[Offset], plunger_new_col, 11,
 ,[Offset], tile col, 12,
 ,[Offset], target A col, 13,
 ,[Offset], target alt col, 14,
 ,[Offset], target alt2 col, 15,
 ,[Offset], slingshot A col, 16,
 ,[Offset], slingshot B col, 17,
 ,[Offset], pop bumper A, 18,
 ,[Offset], wire A, 19,
 ,[Offset], wire B, 20,
 ,[Offset], magnet A col, 21,
 ,[Offset], magnet B col, 22,
 ,[Offset], magnet C col, 23,
 ,[Offset], trap A col, 24,
 ,[Offset], trap B col, 25,
 ,[Offset], trap C col, 26,
 ,[Offset], generic A col, 27,
 ,[Offset], generic B col, 28,
 ,[Offset], generic C col, 29,
 ,[Offset], generic D col, 30,
 ,[Offset], generic E col, 31,
 ,[Offset], generic F col, 32,
 ,[Offset], generic G col, 33,
 ,[Offset], generic H col, 34,
 ,[Offset], generic I col, 35,
 ,[Offset], generic J col, 36,
 ,[Offset], generic M col, 37,
 ,[Offset], generic K left, 38,
 ,[Offset], generic N col, 39,
 ,[Offset], generic O col, 40,
 ,[Offset], generic P col, 41,
 ,[Offset], generic Q, 42,
 ,[Offset], generic R, 43,
 ,[Offset], plunger area col, 44,
 ,[Offset], platform, 45,
 ,[Offset], generic K right, 46,
 ,[Offset], Collision_Rampmetal, 47,
 ,[Offset], Collision_Skillshot, 48,
 ,[Offset], Collision_Flipper_Lane, 49,
 ,[Offset], Col_Diverter_Vanish, 50,
 ,[Offset], Col_HabbiTrail, 51,
 ,[Offset], Col_RampLeft, 52,
 ,[Offset], Col_RampLeft_Front, 53,
 ,[Offset], Col_RampMiddle, 54,
 ,[Offset], Col_RampRight, 55,
 ,[Offset], Col_RampRight_Front, 56,
 ,[Offset], Col_RampSpiral, 57,
 ,[Offset], Col_RampSpiral_Vanish, 58,
RSID_TTOTAN_PLACEMENT, 2008,,,
 ,[Offset], placement, 0,
 ,[Offset], lights, 1,
RSID_TTOTAN_ROM, 2009,,,
 ,[Offset], totan_14, 0,
 ,[Offset], totan_reset, 1,
 ,[Offset], totan_player1start, 2,
 ,[Offset], totan_player2start, 3,
 ,[Offset], totan_player3start, 4,
 ,[Offset], totan_player4start, 5,
 ,[Offset], totan_14, 6,
 ,[Offset], totan_default, 7,
 ,[Offset], totan_14, 8,
RSID_TTOTAN_SOUNDS_START, 2010,,,
RSID_TTOTAN_EMU_SOUNDS, 2011,,,
 ,[Offset], S00A0_C6, 0,
 ,[Offset], S00A1_C-1, 1,
 ,[Offset], S000A_C3, 2,
 ,[Offset], S00C3_C-1, 3,
 ,[Offset], S00C4_C6, 4,
 ,[Offset], S00C9_C4, 5,
 ,[Offset], S00CD_C6, 6,
 ,[Offset], S00CF_C5, 7,
 ,[Offset], S00D0_C5, 8,
 ,[Offset], S00D4_C4, 9,
 ,[Offset], S00D5_C4, 10,
 ,[Offset], S00D6_C4, 11,
 ,[Offset], S00D9_C4, 12,
 ,[Offset], S00DA_C6, 13,
 ,[Offset], S00DB_C6, 14,
 ,[Offset], S00DC_C4, 15,
 ,[Offset], S00DD_C4, 16,
 ,[Offset], S00DE_C6, 17,
 ,[Offset], S00DF_C6, 18,
 ,[Offset], S00E0_C4, 19,
 ,[Offset], S00E1_C4, 20,
 ,[Offset], S00E5_C4, 21,
 ,[Offset], S00E6_C6, 22,
 ,[Offset], S00E7_C6, 23,
 ,[Offset], S00F0_C4, 24,
 ,[Offset], S00F1_C5, 25,
 ,[Offset], S00F2_C6, 26,
 ,[Offset], S00F3_C4, 27,
 ,[Offset], S00F4_C5, 28,
 ,[Offset], S00F5_C6, 29,
 ,[Offset], S00F6_C4, 30,
 ,[Offset], S00F7_C5, 31,
 ,[Offset], S00F8_C6, 32,
 ,[Offset], S00F9_C4, 33,
 ,[Offset], S00FA_C5, 34,
 ,[Offset], S00FB_C6, 35,
 ,[Offset], S00FC_C4, 36,
 ,[Offset], S00FD_C5, 37,
 ,[Offset], S00FE_C6, 38,
 ,[Offset], S0001_C3, 39,
 ,[Offset], S01A5_C5, 40,
 ,[Offset], S01A6_C5, 41,
 ,[Offset], S01C3_C5, 42,
 ,[Offset], S01C5_C5, 43,
 ,[Offset], S01C7_C5, 44,
 ,[Offset], S01C9_C5, 45,
 ,[Offset], S001C_C5, 46,
 ,[Offset], S01CB_C5, 47,
 ,[Offset], S01CD_C5, 48,
 ,[Offset], S01CE_C5, 49,
 ,[Offset], S01D0_C5, 50,
 ,[Offset], S01D5_C5, 51,
 ,[Offset], S001D_C3, 52,
 ,[Offset], S01DB_C5, 53,
 ,[Offset], S01DE_C5, 54,
 ,[Offset], S001E_C3, 55,
 ,[Offset], S01F5_C5, 56,
 ,[Offset], S01F6_C5, 57,
 ,[Offset], S01F7_C5, 58,
 ,[Offset], S01F8_C5, 59,
 ,[Offset], S001F_C3, 60,
 ,[Offset], S01FB_C5, 61,
 ,[Offset], S01FC_C5, 62,
 ,[Offset], S01FF_C5, 63,
 ,[Offset], S0002_C3, 64,
 ,[Offset], S02A1_C5, 65,
 ,[Offset], S02A3_C5, 66,
 ,[Offset], S02A5_C5, 67,
 ,[Offset], S02A6_C5, 68,
 ,[Offset], S02A7_C5, 69,
 ,[Offset], S02A8_C5, 70,
 ,[Offset], S02AA_C5, 71,
 ,[Offset], S02AB_C5, 72,
 ,[Offset], S02AC_C5, 73,
 ,[Offset], S02B0_C5, 74,
 ,[Offset], S02BC_C5, 75,
 ,[Offset], S02BE_C5, 76,
 ,[Offset], S02BF_C5, 77,
 ,[Offset], S02C0_C5, 78,
 ,[Offset], S02C1_C5, 79,
 ,[Offset], S02C2_C5, 80,
 ,[Offset], S02C3_C5, 81,
 ,[Offset], S02C4_C5, 82,
 ,[Offset], S02C5_C5, 83,
 ,[Offset], S02C6_C5, 84,
 ,[Offset], S02C8_C5, 85,
 ,[Offset], S02C9_C5, 86,
 ,[Offset], S02CA_C5, 87,
 ,[Offset], S02CB_C5, 88,
 ,[Offset], S02CC_C5, 89,
 ,[Offset], S02CD_C5, 90,
 ,[Offset], S02CE_C5, 91,
 ,[Offset], S02D0_C5, 92,
 ,[Offset], S02D2_C5, 93,
 ,[Offset], S02D3_C5, 94,
 ,[Offset], S02D4_C5, 95,
 ,[Offset], S02D6_C5, 96,
 ,[Offset], S02D7_C5, 97,
 ,[Offset], S02D8_C5, 98,
 ,[Offset], S02DA_C5, 99,
 ,[Offset], S02DB_C5, 100,
 ,[Offset], S02DC_C5, 101,
 ,[Offset], S02E1_C5, 102,
 ,[Offset], S02E2_C5, 103,
 ,[Offset], S02E4_C5, 104,
 ,[Offset], S02E6_C5, 105,
 ,[Offset], S02E8_C5, 106,
 ,[Offset], S02E9_C5, 107,
 ,[Offset], S02EA_C5, 108,
 ,[Offset], S02EB_C5, 109,
 ,[Offset], S02EC_C5, 110,
 ,[Offset], S02ED_C5, 111,
 ,[Offset], S02EE_C5, 112,
 ,[Offset], S02EF_C5, 113,
 ,[Offset], S02F0_C5, 114,
 ,[Offset], S02F1_C5, 115,
 ,[Offset], S02F2_C5, 116,
 ,[Offset], S02F3_C5, 117,
 ,[Offset], S02F4_C5, 118,
 ,[Offset], S02F5_C5, 119,
 ,[Offset], S02F6_C5, 120,
 ,[Offset], S02F7_C5, 121,
 ,[Offset], S02FA_C5, 122,
 ,[Offset], S02FB_C5, 123,
 ,[Offset], S02FC_C5, 124,
 ,[Offset], S02FE_C5, 125,
 ,[Offset], S02FF_C5, 126,
 ,[Offset], S0003_C3-p1, 127,
 ,[Offset], S0003_C3-p2, 128,
 ,[Offset], S03D4_C5, 129,
 ,[Offset], S03D5_C5, 130,
 ,[Offset], S03D6_C5, 131,
 ,[Offset], S03D7_C5, 132,
 ,[Offset], S03D8_C5, 133,
 ,[Offset], S03D9_C5, 134,
 ,[Offset], S03DA_C5, 135,
 ,[Offset], S03DB_C5, 136,
 ,[Offset], S03DC_C5, 137,
 ,[Offset], S03DD_C5, 138,
 ,[Offset], S0005_C3-p1, 139,
 ,[Offset], S0005_C3-p2, 140,
 ,[Offset], S0006_C3, 141,
 ,[Offset], S006A_C4, 142,
 ,[Offset], S006C_C4, 143,
 ,[Offset], S0007_C3, 144,
 ,[Offset], S007A_C6, 145,
 ,[Offset], S007B_C4, 146,
 ,[Offset], S007C_C4, 147,
 ,[Offset], S0008_C3, 148,
 ,[Offset], S008A_C4, 149,
 ,[Offset], S008B_C4, 150,
 ,[Offset], S008D_C4, 151,
 ,[Offset], S008E_C6, 152,
 ,[Offset], S008F_C6, 153,
 ,[Offset], S0009_C3, 154,
 ,[Offset], S009A_C6, 155,
 ,[Offset], S009B_C4, 156,
 ,[Offset], S009C_C4, 157,
 ,[Offset], S009D_C4, 158,
 ,[Offset], S009E_C5, 159,
 ,[Offset], S009F_C4, 160,
 ,[Offset], S0014_C6, 161,
 ,[Offset], S0015_C6, 162,
 ,[Offset], S0016_C6, 163,
 ,[Offset], S0017_C6, 164,
 ,[Offset], S0018_C3, 165,
 ,[Offset], S019B_C5, 166,
 ,[Offset], S0020_C6, 167,
 ,[Offset], S020A_C5, 168,
 ,[Offset], S020B_C5, 169,
 ,[Offset], S020C_C5, 170,
 ,[Offset], S020D_C5, 171,
 ,[Offset], S020F_C5, 172,
 ,[Offset], S0021_C6, 173,
 ,[Offset], S021B_C5, 174,
 ,[Offset], S021D_C5, 175,
 ,[Offset], S021F_C5, 176,
 ,[Offset], S0022_C6, 177,
 ,[Offset], S0023_C6, 178,
 ,[Offset], S0024_C3, 179,
 ,[Offset], S025A_C6, 180,
 ,[Offset], S025B_C6, 181,
 ,[Offset], S025C_C6, 182,
 ,[Offset], S025D_C6, 183,
 ,[Offset], S025E_C6, 184,
 ,[Offset], S025F_C5, 185,
 ,[Offset], S026A_C5, 186,
 ,[Offset], S026B_C5, 187,
 ,[Offset], S026C_C5, 188,
 ,[Offset], S026D_C5, 189,
 ,[Offset], S026E_C5, 190,
 ,[Offset], S026F_C5, 191,
 ,[Offset], S027A_C5, 192,
 ,[Offset], S027B_C5, 193,
 ,[Offset], S027C_C5, 194,
 ,[Offset], S027E_C5, 195,
 ,[Offset], S027F_C5, 196,
 ,[Offset], S028A_C5, 197,
 ,[Offset], S028D_C5, 198,
 ,[Offset], S028F_C5, 199,
 ,[Offset], S029B_C5, 200,
 ,[Offset], S029F_C5, 201,
 ,[Offset], S030A_C5, 202,
 ,[Offset], S030C_C5, 203,
 ,[Offset], S030D_C5, 204,
 ,[Offset], S030E_C5, 205,
 ,[Offset], S030F_C5, 206,
 ,[Offset], S031D_C5, 207,
 ,[Offset], S031E_C5, 208,
 ,[Offset], S031F_C5, 209,
 ,[Offset], S032A_C5, 210,
 ,[Offset], S032B_C5, 211,
 ,[Offset], S032C_C5, 212,
 ,[Offset], S032E_C5, 213,
 ,[Offset], S032F_C5, 214,
 ,[Offset], S033A_C5, 215,
 ,[Offset], S033B_C5, 216,
 ,[Offset], S033C_C5, 217,
 ,[Offset], S033D_C5, 218,
 ,[Offset], S033E_C5, 219,
 ,[Offset], S033F_C5, 220,
 ,[Offset], S0066_C4, 221,
 ,[Offset], S0069_C4, 222,
 ,[Offset], S0070_C4, 223,
 ,[Offset], S0075_C4, 224,
 ,[Offset], S0079_C4, 225,
 ,[Offset], S0082_C6, 226,
 ,[Offset], S0086_C6, 227,
 ,[Offset], S0087_C6, 228,
 ,[Offset], S0090_C4, 229,
 ,[Offset], S0091_C6, 230,
 ,[Offset], S0092_C6, 231,
 ,[Offset], S0093_C6, 232,
 ,[Offset], S0094_C4, 233,
 ,[Offset], S0095_C4, 234,
 ,[Offset], S0096_C6, 235,
 ,[Offset], S0097_C-1, 236,
 ,[Offset], S0098_C4, 237,
 ,[Offset], S0099_C6, 238,
 ,[Offset], S0194_C5, 239,
 ,[Offset], S0195_C-1, 240,
 ,[Offset], S0200_C5, 241,
 ,[Offset], S0208_C5, 242,
 ,[Offset], S0209_C5, 243,
 ,[Offset], S0210_C5, 244,
 ,[Offset], S0211_C5, 245,
 ,[Offset], S0220_C5, 246,
 ,[Offset], S0221_C5, 247,
 ,[Offset], S0222_C5, 248,
 ,[Offset], S0223_C5, 249,
 ,[Offset], S0227_C5, 250,
 ,[Offset], S0228_C5, 251,
 ,[Offset], S0229_C5, 252,
 ,[Offset], S0258_C6, 253,
 ,[Offset], S0259_C6, 254,
 ,[Offset], S0262_C5, 255,
 ,[Offset], S0266_C5, 256,
 ,[Offset], S0269_C5, 257,
 ,[Offset], S0270_C5, 258,
 ,[Offset], S0271_C5, 259,
 ,[Offset], S0272_C5, 260,
 ,[Offset], S0273_C5, 261,
 ,[Offset], S0274_C5, 262,
 ,[Offset], S0275_C5, 263,
 ,[Offset], S0276_C5, 264,
 ,[Offset], S0277_C5, 265,
 ,[Offset], S0278_C5, 266,
 ,[Offset], S0279_C5, 267,
 ,[Offset], S0280_C5, 268,
 ,[Offset], S0281_C5, 269,
 ,[Offset], S0282_C5, 270,
 ,[Offset], S0283_C5, 271,
 ,[Offset], S0284_C5, 272,
 ,[Offset], S0285_C5, 273,
 ,[Offset], S0286_C5, 274,
 ,[Offset], S0290_C5, 275,
 ,[Offset], S0291_C5, 276,
 ,[Offset], S0294_C5, 277,
 ,[Offset], S0297_C5, 278,
 ,[Offset], S0299_C5, 279,
 ,[Offset], S0300_C5, 280,
 ,[Offset], S0301_C5, 281,
 ,[Offset], S0302_C5, 282,
 ,[Offset], S0303_C5, 283,
 ,[Offset], S0304_C5, 284,
 ,[Offset], S0305_C5, 285,
 ,[Offset], S0306_C5, 286,
 ,[Offset], S0307_C5, 287,
 ,[Offset], S0308_C5, 288,
 ,[Offset], S0309_C5, 289,
 ,[Offset], S0310_C5, 290,
 ,[Offset], S0312_C5, 291,
 ,[Offset], S0314_C5, 292,
 ,[Offset], S0316_C5, 293,
 ,[Offset], S0317_C5, 294,
 ,[Offset], S0318_C5, 295,
 ,[Offset], S0319_C5, 296,
 ,[Offset], S0321_C5, 297,
 ,[Offset], S0322_C5, 298,
 ,[Offset], S0323_C5, 299,
 ,[Offset], S0324_C5, 300,
 ,[Offset], S0325_C5, 301,
 ,[Offset], S0326_C5, 302,
 ,[Offset], S0327_C5, 303,
 ,[Offset], S0328_C5, 304,
 ,[Offset], S0329_C5, 305,
 ,[Offset], S0330_C5, 306,
 ,[Offset], S0331_C5, 307,
 ,[Offset], S0332_C5, 308,
 ,[Offset], S0335_C5, 309,
 ,[Offset], S0337_C5, 310,
 ,[Offset], S0338_C5, 311,
 ,[Offset], S0339_C5, 312,
 ,[Offset], S0340_C5, 313,
 ,[Offset], S0341_C5, 314,
 ,[Offset], S0342_C5, 315,
 ,[Offset], S0343_C5, 316,
 ,[Offset], S0344_C5, 317,
 ,[Offset], S0345_C5, 318,
 ,[Offset], S0346_C5, 319,
 ,[Offset], S0F00_C6, 320,
RSID_TTOTAN_MECH_SOUNDS, 2012,,,
 ,[Offset], tales_genie_hit, 0,
 ,[Offset], tales_bumper1, 1,
 ,[Offset], tales_bumper2, 2,
 ,[Offset], tales_bumper3, 3,
 ,[Offset], tales_fireball1_catch, 4,
 ,[Offset], tales_fireball2_metal_escape_rails, 5,
 ,[Offset], tales_magnet_catch_and_fall_into_ball_lock, 6,
 ,[Offset], tales_outlane_catch, 7,
 ,[Offset], tales_skillshot_1st_hole, 8,
 ,[Offset], tales_skillshot_2nd_hole, 9,
 ,[Offset], tales_skillshot_3rd_hole, 10,
 ,[Offset], tales_fall_into_harem_sneak, 11,
 ,[Offset], tales_lt_slingshot, 12,
 ,[Offset], tales_rt_slingshot, 13,
 ,[Offset], tales_hit_captive_ball, 14,
 ,[Offset], tales_fall_into_orbs_eject, 15,
 ,[Offset], tales_single_lamp_spin, 16,
RSID_TOTAN_SAMPLES, 2013,,,
RSID_TTOTAN_SOUNDS_END, 2014,,,
RSID_TOTAN_VERSION, 2015,,,
RSID_TTOTAN_END, 2016,,,
