// Copyright 2025 <Copyright Owner>

#pragma once

#include "x86_64_cpu.h"
#include <atomic>
#include <cstdint>
#include <istream>
#include <mutex>
#include <ostream>
#include <stdexcept>
#include <string>
#include <unordered_map>

namespace x86_64 {

struct JITException : std::runtime_error {
  explicit JITException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief C-style functions for JIT operations, callable from generated code.
 */
extern "C" {
/**
 * @brief Reads 8-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @return The read value.
 */
uint64_t jitReadMem8(X86_64CPU *cpu, uint64_t addr);

/**
 * @brief Reads 16-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @return The read value.
 */
uint64_t jitReadMem16(X86_64CPU *cpu, uint64_t addr);

/**
 * @brief Reads 32-bit memory value for <PERSON><PERSON>.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @return The read value.
 */
uint64_t jitReadMem32(X86_64CPU *cpu, uint64_t addr);

/**
 * @brief Reads 64-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @return The read value.
 */
uint64_t jitReadMem64(X86_64CPU *cpu, uint64_t addr);

/**
 * @brief Writes 8-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @param value Value to write.
 */
void jitWriteMem8(X86_64CPU *cpu, uint64_t addr, uint64_t value);

/**
 * @brief Writes 16-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @param value Value to write.
 */
void jitWriteMem16(X86_64CPU *cpu, uint64_t addr, uint64_t value);

/**
 * @brief Writes 32-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @param value Value to write.
 */
void jitWriteMem32(X86_64CPU *cpu, uint64_t addr, uint64_t value);

/**
 * @brief Writes 64-bit memory value for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Memory address.
 * @param value Value to write.
 */
void jitWriteMem64(X86_64CPU *cpu, uint64_t addr, uint64_t value);

/**
 * @brief Reads XMM register for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Register index.
 * @param dest Destination buffer.
 * @param size Size to read.
 */
void jitReadXMM(X86_64CPU *cpu, uint64_t addr, uint8_t *dest, uint32_t size);

/**
 * @brief Writes XMM register for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Register index.
 * @param src Source buffer.
 * @param size Size to write.
 */
void jitWriteXMM(X86_64CPU *cpu, uint64_t addr, const uint8_t *src,
                 uint32_t size);

/**
 * @brief Pushes 64-bit value onto the stack for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param value Value to push.
 * @return Updated stack pointer.
 */
uint64_t jitPush64(X86_64CPU *cpu, uint64_t value);

/**
 * @brief Pops 64-bit value from the stack for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @return Popped value.
 */
uint64_t jitPop64(X86_64CPU *cpu);

/**
 * @brief Performs a direct branch for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Branch target address.
 */
void jitDirectBranch(X86_64CPU *cpu, uint64_t addr);

/**
 * @brief Performs 64-bit addition for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param op1 First operand.
 * @param op2 Second operand.
 * @param sizeInBits Operand size in bits.
 */
void jitAdd64(X86_64CPU *cpu, uint64_t op1, uint64_t op2, uint8_t sizeInBits);

/**
 * @brief Performs 64-bit subtraction for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param op1 First operand.
 * @param op2 Second operand.
 * @param sizeInBits Operand size in bits.
 */
void jitSub64(X86_64CPU *cpu, uint64_t op1, uint64_t op2, uint8_t sizeInBits);

/**
 * @brief Checks conditional jump condition for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param conditionCode Condition code.
 * @return True if condition is met, false otherwise.
 */
bool jitCheckCondition(X86_64CPU *cpu, uint8_t conditionCode);

/**
 * @brief Performs SIMD addition for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 */
void jitAddps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2);

/**
 * @brief Performs SIMD subtraction for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 */
void jitSubps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2);

/**
 * @brief Performs SIMD multiplication for JIT.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 */
void jitMulps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2);
}

/**
 * @brief Singleton class for managing JIT operation statistics and helpers.
 */
class MemoryHelper {
public:
  /**
   * @brief Retrieves the singleton instance of MemoryHelper.
   * @return Reference to the singleton instance.
   */
  static MemoryHelper &GetInstance() {
    static MemoryHelper instance;
    return instance;
  }

  /**
   * @brief Statistics for JIT operations.
   * RACE CONDITION FIX: Use atomic types for thread-safe access
   */
  struct Stats {
    std::atomic<uint64_t> memoryReads{0}; ///< Number of memory read operations
    std::atomic<uint64_t> memoryWrites{
        0};                            ///< Number of memory write operations
    std::atomic<uint64_t> pushes{0};   ///< Number of stack push operations
    std::atomic<uint64_t> pops{0};     ///< Number of stack pop operations
    std::atomic<uint64_t> branches{0}; ///< Number of branch operations
    std::atomic<uint64_t> simdAccesses{0}; ///< Number of SIMD register accesses
    std::atomic<uint64_t> cacheHits{0};    ///< Number of cache hits
    std::atomic<uint64_t> cacheMisses{0};  ///< Number of cache misses
    std::atomic<uint64_t> totalLatencyUs{
        0}; ///< Total latency in microseconds    // Copy constructor for atomic
            ///< members
    Stats() = default;
    Stats(const Stats &other)
        : memoryReads(other.memoryReads.load()),
          memoryWrites(other.memoryWrites.load()), pushes(other.pushes.load()),
          pops(other.pops.load()), branches(other.branches.load()),
          simdAccesses(other.simdAccesses.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()),
          totalLatencyUs(other.totalLatencyUs.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        memoryReads.store(other.memoryReads.load());
        memoryWrites.store(other.memoryWrites.load());
        pushes.store(other.pushes.load());
        pops.store(other.pops.load());
        branches.store(other.branches.load());
        simdAccesses.store(other.simdAccesses.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves JIT operation statistics.
   * RACE CONDITION FIX: Return copy instead of reference to avoid race
   * conditions
   * @return Copy of the statistics.
   */
  Stats GetStats() const noexcept {
    // No lock needed since all members are atomic
    return m_stats;
  }

  /**
   * @brief Increments memory read count atomically.
   */
  void IncrementMemoryReads() noexcept {
    m_stats.memoryReads.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments memory write count atomically.
   */
  void IncrementMemoryWrites() noexcept {
    m_stats.memoryWrites.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments cache hit count atomically.
   */
  void IncrementCacheHits() noexcept {
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments cache miss count atomically.
   */
  void IncrementCacheMisses() noexcept {
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments push count atomically.
   */
  void IncrementPushes() noexcept {
    m_stats.pushes.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments pop count atomically.
   */
  void IncrementPops() noexcept {
    m_stats.pops.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments branch count atomically.
   */
  void IncrementBranches() noexcept {
    m_stats.branches.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Increments SIMD access count atomically.
   */
  void IncrementSimdAccesses() noexcept {
    m_stats.simdAccesses.fetch_add(1, std::memory_order_relaxed);
  }

  /**
   * @brief Adds to total latency atomically.
   */
  void AddLatency(uint64_t latencyUs) noexcept {
    m_stats.totalLatencyUs.fetch_add(latencyUs, std::memory_order_relaxed);
  }

  /**
   * @brief Resets JIT operation statistics.
   * RACE CONDITION FIX: Use atomic operations instead of assignment
   */
  void ResetStats() {
    m_stats.memoryReads.store(0, std::memory_order_relaxed);
    m_stats.memoryWrites.store(0, std::memory_order_relaxed);
    m_stats.pushes.store(0, std::memory_order_relaxed);
    m_stats.pops.store(0, std::memory_order_relaxed);
    m_stats.branches.store(0, std::memory_order_relaxed);
    m_stats.simdAccesses.store(0, std::memory_order_relaxed);
    m_stats.cacheHits.store(0, std::memory_order_relaxed);
    m_stats.cacheMisses.store(0, std::memory_order_relaxed);
    m_stats.totalLatencyUs.store(0, std::memory_order_relaxed);
  }

  /**
   * @brief Saves JIT helper state to a stream.
   * @param out The output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads JIT helper state from a stream.
   * @param in The input stream.
   */
  void LoadState(std::istream &in);

private:
  // CRITICAL FIX: Explicitly initialize stats to zero to prevent uninitialized
  // data usage
  MemoryHelper() {
    // Ensure all stats are properly initialized to zero
    ResetStats();
  }
  Stats m_stats; ///< Operation statistics (now atomic)
  mutable std::mutex
      m_mutex; ///< Thread safety mutex (for save/load operations)
};

} // namespace x86_64
