
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The system is: Windows - 10.0.26120 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/5/2025 10:28:23 PM.
      
      Project "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:04.82
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/sss/build/CMakeFiles/3.30.5-msvc23/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/5/2025 10:28:28 PM.
      
      Project "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\sss\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.36
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/sss/build/CMakeFiles/3.30.5-msvc23/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-24gzrv"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-24gzrv"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-24gzrv'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1652e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:30 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24gzrv\\cmTC_1652e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_1652e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24gzrv\\Debug\\".
          Creating directory "cmTC_1652e.dir\\Debug\\cmTC_1652e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_1652e.dir\\Debug\\cmTC_1652e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_1652e.dir\\Debug\\cmTC_1652e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1652e.dir\\Debug\\\\" /Fd"cmTC_1652e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1652e.dir\\Debug\\\\" /Fd"cmTC_1652e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24gzrv\\Debug\\cmTC_1652e.exe" /INCREMENTAL /ILK:"cmTC_1652e.dir\\Debug\\cmTC_1652e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-24gzrv/Debug/cmTC_1652e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-24gzrv/Debug/cmTC_1652e.lib" /MACHINE:X64  /machine:x64 cmTC_1652e.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_1652e.vcxproj -> D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24gzrv\\Debug\\cmTC_1652e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_1652e.dir\\Debug\\cmTC_1652e.tlog\\unsuccessfulbuild".
          Touching "cmTC_1652e.dir\\Debug\\cmTC_1652e.tlog\\cmTC_1652e.lastbuildstate".
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24gzrv\\cmTC_1652e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-3kd3vx"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-3kd3vx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-3kd3vx'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_df134.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:32 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3kd3vx\\cmTC_df134.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_df134.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3kd3vx\\Debug\\".
          Creating directory "cmTC_df134.dir\\Debug\\cmTC_df134.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_df134.dir\\Debug\\cmTC_df134.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_df134.dir\\Debug\\cmTC_df134.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_df134.dir\\Debug\\\\" /Fd"cmTC_df134.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_df134.dir\\Debug\\\\" /Fd"cmTC_df134.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3kd3vx\\Debug\\cmTC_df134.exe" /INCREMENTAL /ILK:"cmTC_df134.dir\\Debug\\cmTC_df134.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-3kd3vx/Debug/cmTC_df134.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-3kd3vx/Debug/cmTC_df134.lib" /MACHINE:X64  /machine:x64 cmTC_df134.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_df134.vcxproj -> D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3kd3vx\\Debug\\cmTC_df134.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_df134.dir\\Debug\\cmTC_df134.tlog\\unsuccessfulbuild".
          Touching "cmTC_df134.dir\\Debug\\cmTC_df134.tlog\\cmTC_df134.lastbuildstate".
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3kd3vx\\cmTC_df134.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.63
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake:154 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindBZip2.cmake:96 (CHECK_SYMBOL_EXISTS)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config.cmake:42 (find_dependency)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:114 (find_package)"
    checks:
      - "Looking for BZ2_bzCompressInit"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-wdy576"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-wdy576"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg;D:/vcpkg/installed/x64-windows/share/libzip/modules"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "BZIP2_NEED_PREFIX"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-wdy576'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ef4e4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:36 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\cmTC_ef4e4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ef4e4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\Debug\\".
          Creating directory "cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ef4e4.dir\\Debug\\\\" /Fd"cmTC_ef4e4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\CheckSymbolExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ef4e4.dir\\Debug\\\\" /Fd"cmTC_ef4e4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\CheckSymbolExists.c"
          CheckSymbolExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\Debug\\cmTC_ef4e4.exe" /INCREMENTAL /ILK:"cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.ilk" /NOLOGO "D:\\vcpkg\\installed\\x64-windows\\debug\\lib\\bz2d.lib" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-wdy576/Debug/cmTC_ef4e4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-wdy576/Debug/cmTC_ef4e4.lib" /MACHINE:X64  /machine:x64 cmTC_ef4e4.dir\\Debug\\CheckSymbolExists.obj
          cmTC_ef4e4.vcxproj -> D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\Debug\\cmTC_ef4e4.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.tlog\\unsuccessfulbuild".
          Touching "cmTC_ef4e4.dir\\Debug\\cmTC_ef4e4.tlog\\cmTC_ef4e4.lastbuildstate".
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy576\\cmTC_ef4e4.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:116 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-56sy7z"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-56sy7z"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg;D:/vcpkg/installed/x64-windows/share/libzip/modules"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-56sy7z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ee4bb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:37 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\cmTC_ee4bb.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ee4bb.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\Debug\\".
          Creating directory "cmTC_ee4bb.dir\\Debug\\cmTC_ee4bb.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ee4bb.dir\\Debug\\cmTC_ee4bb.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ee4bb.dir\\Debug\\cmTC_ee4bb.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ee4bb.dir\\Debug\\\\" /Fd"cmTC_ee4bb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ee4bb.dir\\Debug\\\\" /Fd"cmTC_ee4bb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\src.c"
          src.c
        D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\cmTC_ee4bb.vcxproj]
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\cmTC_ee4bb.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\cmTC_ee4bb.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-56sy7z\\cmTC_ee4bb.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.42
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:116 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-nw9uot"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-nw9uot"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg;D:/vcpkg/installed/x64-windows/share/libzip/modules"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-nw9uot'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_443e3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:38 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\cmTC_443e3.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_443e3.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\Debug\\".
          Creating directory "cmTC_443e3.dir\\Debug\\cmTC_443e3.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_443e3.dir\\Debug\\cmTC_443e3.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_443e3.dir\\Debug\\cmTC_443e3.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_443e3.dir\\Debug\\\\" /Fd"cmTC_443e3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_443e3.dir\\Debug\\\\" /Fd"cmTC_443e3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\Debug\\cmTC_443e3.exe" /INCREMENTAL /ILK:"cmTC_443e3.dir\\Debug\\cmTC_443e3.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-nw9uot/Debug/cmTC_443e3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-nw9uot/Debug/cmTC_443e3.lib" /MACHINE:X64  /machine:x64 cmTC_443e3.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\cmTC_443e3.vcxproj]
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\cmTC_443e3.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\cmTC_443e3.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nw9uot\\cmTC_443e3.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.46
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:116 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-irxg9r"
      binary: "D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-irxg9r"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg;D:/vcpkg/installed/x64-windows/share/libzip/modules"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-irxg9r'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_694d1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/5/2025 10:28:39 PM.
        
        Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\cmTC_694d1.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_694d1.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\Debug\\".
          Creating directory "cmTC_694d1.dir\\Debug\\cmTC_694d1.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_694d1.dir\\Debug\\cmTC_694d1.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_694d1.dir\\Debug\\cmTC_694d1.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_694d1.dir\\Debug\\\\" /Fd"cmTC_694d1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_694d1.dir\\Debug\\\\" /Fd"cmTC_694d1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\Debug\\cmTC_694d1.exe" /INCREMENTAL /ILK:"cmTC_694d1.dir\\Debug\\cmTC_694d1.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-irxg9r/Debug/cmTC_694d1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/build/CMakeFiles/CMakeScratch/TryCompile-irxg9r/Debug/cmTC_694d1.lib" /MACHINE:X64  /machine:x64 cmTC_694d1.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\cmTC_694d1.vcxproj]
        Done Building Project "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\cmTC_694d1.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\cmTC_694d1.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\sss\\build\\CMakeFiles\\CMakeScratch\\TryCompile-irxg9r\\cmTC_694d1.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.48
        
      exitCode: 1
...
