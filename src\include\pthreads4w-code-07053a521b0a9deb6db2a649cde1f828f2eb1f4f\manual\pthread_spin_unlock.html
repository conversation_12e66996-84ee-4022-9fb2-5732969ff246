<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SPIN_UNLOCK(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_spin_unlock - unlock a spin lock object (<B>ADVANCED
REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_spin_unlock(pthread_spinlock_t *</B><I>lock</I><B>);
</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_spin_unlock</B> function shall release the spin
lock referenced by <I>lock</I> which was locked via the
<A HREF="pthread_spin_lock.html"><B>pthread_spin_lock</B>(3)</A> or
<A HREF="pthread_spin_lock.html"><B>pthread_spin_trylock</B>(3)</A>
functions. If there are threads spinning on the lock when
<B>pthread_spin_unlock</B> is called, the lock becomes available and
an unspecified spinning thread shall acquire the lock. 
</P>
<P><B>PThreads4W</B> does not check ownership of the lock and it is
therefore possible for a thread other than the locker to unlock the
spin lock. This is not a feature that should be exploited.</P>
<P>The results are undefined if this function is called with an
uninitialized thread spin lock. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, the <B>pthread_spin_unlock</B>
function shall return zero; otherwise, an error number shall be
returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_spin_unlock</B> function may fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD>
	An invalid argument was specified. 
	</DD><DD STYLE="margin-left: -2cm">
	<BR>
	</DD></DL>
<P>
This function shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P><B>PThreads4W</B> does not check ownership of the lock and it is
therefore possible for a thread other than the locker to unlock the
spin lock. This is not a feature that should be exploited.</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_spin_init.html"><B>pthread_spin_destroy</B>(3)</A>
<B>,</B> <A HREF="pthread_spin_lock.html"><B>pthread_spin_lock</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
