RSID_TDRDUDE_START, 3075,,,
 ,[Offset], FLYER_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON>\PBTDrDude, 1,
 ,[Offset], Dr<PERSON>ude\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON>ude\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsITAL, 4,
 ,[Offset], Dr<PERSON><PERSON>\InstructionsGERM, 5,
 ,[Offset], Dr<PERSON><PERSON>\InstructionsSPAN, 6,
 ,[Offset], DrDude\InstructionsPORT, 7,
 ,[Offset], DrDude\InstructionsDUTCH, 8,
 ,[Offset], DrDude\InstructionsDUTCH, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TDRDUDE_LIGHTS, 3076,,,
RSID_TDRDUDE_CAMERAS, 3077,,,
RSID_TDRDUDE_LAMP_TEXTURES, 3078,,,
 ,[Offset], L_01_Off, 0,
 ,[Offset], L_01_On, 1,
 ,[Offset], L_02_Off, 2,
 ,[Offset], L_02_On, 3,
 ,[Offset], L_03_Off, 4,
 ,[Offset], L_03_On, 5,
 ,[Offset], L_04_Off, 6,
 ,[Offset], L_04_On, 7,
 ,[Offset], L_05_Off, 8,
 ,[Offset], L_05_On, 9,
 ,[Offset], L_06_Off, 10,
 ,[Offset], L_06_On, 11,
 ,[Offset], L_07_Off, 12,
 ,[Offset], L_07_On, 13,
 ,[Offset], L_08_Off, 14,
 ,[Offset], L_08_On, 15,
 ,[Offset], L_09_Off, 16,
 ,[Offset], L_09_On, 17,
 ,[Offset], L_10_Off, 18,
 ,[Offset], L_10_On, 19,
 ,[Offset], L_11_Off, 20,
 ,[Offset], L_11_On, 21,
 ,[Offset], L_12_Off, 22,
 ,[Offset], L_12_On, 23,
 ,[Offset], L_13_Off, 24,
 ,[Offset], L_13_On, 25,
 ,[Offset], L_14_Off, 26,
 ,[Offset], L_14_On, 27,
 ,[Offset], L_15_Off, 28,
 ,[Offset], L_15_On, 29,
 ,[Offset], L_16_Off, 30,
 ,[Offset], L_16_On, 31,
 ,[Offset], L_17_Off, 32,
 ,[Offset], L_17_On, 33,
 ,[Offset], L_18_Off, 34,
 ,[Offset], L_18_On, 35,
 ,[Offset], L_19_Off, 36,
 ,[Offset], L_19_On, 37,
 ,[Offset], L_20_Off, 38,
 ,[Offset], L_20_On, 39,
 ,[Offset], L_21_Off, 40,
 ,[Offset], L_21_On, 41,
 ,[Offset], L_22_Off, 42,
 ,[Offset], L_22_On, 43,
 ,[Offset], L_23_Off, 44,
 ,[Offset], L_23_On, 45,
 ,[Offset], L_24_Off, 46,
 ,[Offset], L_24_On, 47,
 ,[Offset], L_25_Off, 48,
 ,[Offset], L_25_On, 49,
 ,[Offset], L_26_Off, 50,
 ,[Offset], L_26_On, 51,
 ,[Offset], L_27_Off, 52,
 ,[Offset], L_27_On, 53,
 ,[Offset], L_28_Off, 54,
 ,[Offset], L_28_On, 55,
 ,[Offset], L_29_Off, 56,
 ,[Offset], L_29_On, 57,
 ,[Offset], L_30_Off, 58,
 ,[Offset], L_30_On, 59,
 ,[Offset], L_31_Off, 60,
 ,[Offset], L_31_On, 61,
 ,[Offset], L_32_Off, 62,
 ,[Offset], L_32_On, 63,
 ,[Offset], L_33_Off, 64,
 ,[Offset], L_33_On, 65,
 ,[Offset], L_34_Off, 66,
 ,[Offset], L_34_On, 67,
 ,[Offset], L_35_Off, 68,
 ,[Offset], L_35_On, 69,
 ,[Offset], L_36_Off, 70,
 ,[Offset], L_36_On, 71,
 ,[Offset], L_37_Off, 72,
 ,[Offset], L_37_On, 73,
 ,[Offset], L_38_Off, 74,
 ,[Offset], L_38_On, 75,
 ,[Offset], L_39_Off, 76,
 ,[Offset], L_39_On, 77,
 ,[Offset], L_40_Off, 78,
 ,[Offset], L_40_On, 79,
 ,[Offset], L_41_Off, 80,
 ,[Offset], L_41_On, 81,
 ,[Offset], L_42_Off, 82,
 ,[Offset], L_42_On, 83,
 ,[Offset], L_43_Off, 84,
 ,[Offset], L_43_On, 85,
 ,[Offset], L_44_Off, 86,
 ,[Offset], L_44_On, 87,
 ,[Offset], L_45_Off, 88,
 ,[Offset], L_45_On, 89,
 ,[Offset], L_46_Off, 90,
 ,[Offset], L_46_On, 91,
 ,[Offset], L_47_Off, 92,
 ,[Offset], L_47_On, 93,
 ,[Offset], L_48_Off, 94,
 ,[Offset], L_48_On, 95,
 ,[Offset], L_49_Off, 96,
 ,[Offset], L_49_On, 97,
 ,[Offset], L_50_Off, 98,
 ,[Offset], L_50_On, 99,
 ,[Offset], L_51_Off, 100,
 ,[Offset], L_51_On, 101,
 ,[Offset], L_52_Off, 102,
 ,[Offset], L_52_On, 103,
 ,[Offset], L_53_Off, 104,
 ,[Offset], L_53_On, 105,
 ,[Offset], L_54_Off, 106,
 ,[Offset], L_54_On, 107,
 ,[Offset], L_55_Off, 108,
 ,[Offset], L_55_On, 109,
 ,[Offset], L_56_Off, 110,
 ,[Offset], L_56_On, 111,
 ,[Offset], L_57_Off, 112,
 ,[Offset], L_57_On, 113,
 ,[Offset], L_58_Off, 114,
 ,[Offset], L_58_On, 115,
 ,[Offset], L_59_Off, 116,
 ,[Offset], L_59_On, 117,
 ,[Offset], L_60_Off, 118,
 ,[Offset], L_60_On, 119,
 ,[Offset], L_61_Off, 120,
 ,[Offset], L_61_On, 121,
 ,[Offset], L_62_Off, 122,
 ,[Offset], L_62_On, 123,
 ,[Offset], L_63_Off, 124,
 ,[Offset], L_63_On, 125,
 ,[Offset], L_64_Off, 126,
 ,[Offset], L_64_On, 127,
 ,[Offset], F_01_On, 128,
 ,[Offset], F_02_On, 129,
 ,[Offset], F_03_On, 130,
 ,[Offset], F_04_Off, 131,
 ,[Offset], F_04_On, 132,
 ,[Offset], F_06_Off, 133,
 ,[Offset], F_06_On, 134,
 ,[Offset], F_07_Off, 135,
 ,[Offset], F_07_On, 136,
 ,[Offset], F_08_Off, 137,
 ,[Offset], F_08_On, 138,
 ,[Offset], F_15_Off, 139,
 ,[Offset], F_15_On, 140,
 ,[Offset], F_15B_Off, 141,
 ,[Offset], F_15B_On, 142,
 ,[Offset], F_05_Off, 143,
 ,[Offset], F_05_On, 144,
 ,[Offset], L_38B_Off, 145,
 ,[Offset], L_38B_On, 146,
RSID_TDRDUDE_TEXTURES, 3079,,,
 ,[Offset], DrDude_LED_Border, 0,
 ,[Offset], DrDude_LED_Border_Mobile, 1,
 ,[Offset], bigShot_t_c, 2,
 ,[Offset], blue1, 3,
 ,[Offset], blue2, 4,
 ,[Offset], BWplastics, 5,
 ,[Offset], clear_plastics, 6,
 ,[Offset], cords, 7,
 ,[Offset], HabitTrail01_t_c, 8,
 ,[Offset], HabitTRail02_t_c, 9,
 ,[Offset], metal, 10,
 ,[Offset], OrangeSTrip_Plastics_t_c, 11,
 ,[Offset], Playfield_Lower, 12,
 ,[Offset], Playfield_Upper, 13,
 ,[Offset], RAmp_T_C, 14,
 ,[Offset], RocknRoll_Plastics_t_c, 15,
 ,[Offset], rubberpeg, 16,
 ,[Offset], silver_case, 17,
 ,[Offset], upper_playfield, 18,
 ,[Offset], Silver Metal SCrews, 19,
 ,[Offset], bulb1, 20,
 ,[Offset], BUmper_Hamer, 21,
 ,[Offset], BUmper_Sensors, 22,
 ,[Offset], HarleyBUmperBOdy, 23,
 ,[Offset], RUbber Post_Temp, 24,
 ,[Offset], RUbberband_Temp, 25,
 ,[Offset], Flipper, 26,
 ,[Offset], RAils, 27,
 ,[Offset], Generic_Metal, 28,
 ,[Offset], PopBUmperBody, 29,
 ,[Offset], PopBUmper, 30,
 ,[Offset], CLearPlasticPost_01, 31,
 ,[Offset], Red_Target, 32,
 ,[Offset], Metal_Walls, 33,
 ,[Offset], backglass, 34,
 ,[Offset], black_metal, 35,
 ,[Offset], black_wood, 36,
 ,[Offset], blue_button, 37,
 ,[Offset], cabinet, 38,
 ,[Offset], cabinet_front, 39,
 ,[Offset], Coin_Slot1, 40,
 ,[Offset], Extra_Metal_Parts, 41,
 ,[Offset], Habi_trail3, 42,
 ,[Offset], Metal_Parts, 43,
 ,[Offset], Plunger, 44,
 ,[Offset], yellow_Button, 45,
 ,[Offset], Apron, 46,
 ,[Offset], Harley_Gate, 47,
 ,[Offset], Flasher, 48,
 ,[Offset], Flasher_Plate, 49,
 ,[Offset], Drop_Target, 50,
 ,[Offset], targets, 51,
 ,[Offset], habi_trail1, 52,
 ,[Offset], habi_trail2, 53,
 ,[Offset], targets_green, 54,
 ,[Offset], targets_yellow, 55,
 ,[Offset], metal_temp, 56,
RSID_TDRDUDE_MODELS, 3080,,,
 ,[Offset], TArget, 0,
 ,[Offset], TArget_REd, 1,
 ,[Offset], Turn_Table, 2,
 ,[Offset], Turn_TAble_POst, 3,
 ,[Offset], Back_Plastics, 4,
 ,[Offset], Bulbs, 5,
 ,[Offset], Clear_Plastics, 6,
 ,[Offset], Cords, 7,
 ,[Offset], Dr_Dude, 8,
 ,[Offset], Flipper, 9,
 ,[Offset], Flipper_Plastics, 10,
 ,[Offset], Left_Habit, 11,
 ,[Offset], Metal_Posts, 12,
 ,[Offset], Metal_Walls, 13,
 ,[Offset], Plastic_Case, 14,
 ,[Offset], Plastic_Pieces, 15,
 ,[Offset], Plastic_Posts, 16,
 ,[Offset], Plastic_Ramp, 17,
 ,[Offset], Playfield, 18,
 ,[Offset], Playfield_Upper, 19,
 ,[Offset], Pop_BUmpers, 20,
 ,[Offset], RUbber_Posts, 21,
 ,[Offset], SLingshot_Left, 22,
 ,[Offset], SLingshot_Right, 23,
 ,[Offset], Wire, 24,
 ,[Offset], Wooden_Rails, 25,
 ,[Offset], Backglass, 26,
 ,[Offset], Bumper_A, 27,
 ,[Offset], Cabinet, 28,
 ,[Offset], Cabinet_Buttons, 29,
 ,[Offset], Cabinet_Interior, 30,
 ,[Offset], Cabinet_Metal, 31,
 ,[Offset], Plunger, 32,
 ,[Offset], Habit_Right, 33,
 ,[Offset], Habit_Right_Curl, 34,
 ,[Offset], Apron, 35,
 ,[Offset], Drop_Target, 36,
 ,[Offset], Gate_A, 37,
 ,[Offset], One_Way_Gate_A, 38,
 ,[Offset], One_Way_Gate_B, 39,
 ,[Offset], Flashers, 40,
 ,[Offset], X_Ray, 41,
 ,[Offset], Light_Cutouts, 42,
 ,[Offset], Lips, 43,
 ,[Offset], X_Ray_Lamps, 44,
 ,[Offset], Target_Green, 45,
 ,[Offset], Target_Yellow, 46,
 ,[Offset], Flasher_Plane_A, 47,
 ,[Offset], Flasher_Plane_B, 48,
RSID_TDRDUDE_MODELS_LODS, 3081,,,
 ,[Offset], TArget, 0,
 ,[Offset], TArget_REd, 1,
 ,[Offset], Turn_Table, 2,
 ,[Offset], Turn_TAble_POst, 3,
 ,[Offset], Back_Plastics, 4,
 ,[Offset], Bulbs, 5,
 ,[Offset], Clear_Plastics, 6,
 ,[Offset], Cords, 7,
 ,[Offset], Dr_Dude, 8,
 ,[Offset], Flipper, 9,
 ,[Offset], Flipper_Plastics, 10,
 ,[Offset], Left_Habit, 11,
 ,[Offset], Metal_Posts, 12,
 ,[Offset], Metal_Walls, 13,
 ,[Offset], Plastic_Case, 14,
 ,[Offset], Plastic_Pieces, 15,
 ,[Offset], Plastic_Posts, 16,
 ,[Offset], Plastic_Ramp, 17,
 ,[Offset], Playfield, 18,
 ,[Offset], Playfield_Upper, 19,
 ,[Offset], Pop_BUmpers, 20,
 ,[Offset], RUbber_Posts, 21,
 ,[Offset], SLingshot_Left, 22,
 ,[Offset], SLingshot_Right, 23,
 ,[Offset], Wire, 24,
 ,[Offset], Wooden_Rails, 25,
 ,[Offset], Backglass, 26,
 ,[Offset], Bumper_A, 27,
 ,[Offset], Cabinet, 28,
 ,[Offset], Cabinet_Buttons, 29,
 ,[Offset], Cabinet_Interior, 30,
 ,[Offset], Cabinet_Metal, 31,
 ,[Offset], Plunger, 32,
 ,[Offset], Habit_Right, 33,
 ,[Offset], Habit_Right_Curl, 34,
 ,[Offset], Apron, 35,
 ,[Offset], Drop_Target, 36,
 ,[Offset], Gate_A, 37,
 ,[Offset], One_Way_Gate_A, 38,
 ,[Offset], One_Way_Gate_B, 39,
 ,[Offset], Flashers, 40,
 ,[Offset], X_Ray, 41,
 ,[Offset], Light_Cutouts, 42,
 ,[Offset], Lips, 43,
 ,[Offset], X_Ray_Lamps, 44,
 ,[Offset], Target_Green, 45,
 ,[Offset], Target_Yellow, 46,
 ,[Offset], Flasher_Plane_A, 47,
 ,[Offset], Flasher_Plane_B, 48,
RSID_TDRDUDE_COLLISION, 3082,,,
 ,[Offset], Playfield, 0,
 ,[Offset], Ball_Drain, 1,
 ,[Offset], Plunger, 2,
 ,[Offset], Flipper_Lane_Left, 3,
 ,[Offset], Flipper_Lane_Right, 4,
 ,[Offset], Plunger_Static, 5,
 ,[Offset], Rubber_A, 6,
 ,[Offset], Rubber_B, 7,
 ,[Offset], Rubber_C, 8,
 ,[Offset], Rubber_D, 9,
 ,[Offset], Rubber_E, 10,
 ,[Offset], Slingshot_Left, 11,
 ,[Offset], Slingshot_Right, 12,
 ,[Offset], Wall_Inner_Right, 13,
 ,[Offset], Wall_Left, 14,
 ,[Offset], Wall_Left_Upper, 15,
 ,[Offset], Wall_Lower, 16,
 ,[Offset], Wall_Right, 17,
 ,[Offset], Bumper, 18,
 ,[Offset], Flipper_Left_BAck, 19,
 ,[Offset], Flipper_Left_Front, 20,
 ,[Offset], Flipper_Right_Back, 21,
 ,[Offset], Flipper_Right_Front, 22,
 ,[Offset], Playfield_Upper, 23,
 ,[Offset], Target, 24,
 ,[Offset], Target_Red, 25,
 ,[Offset], Turn_Table, 26,
 ,[Offset], Turn_Table_Post, 27,
 ,[Offset], Habit_Left, 28,
 ,[Offset], Habit_Right, 29,
 ,[Offset], Habit_Right_Curl, 30,
 ,[Offset], Ramp, 31,
 ,[Offset], Drop_Target, 32,
 ,[Offset], Habit_Left_Trap, 33,
 ,[Offset], Habit_Right_Trap, 34,
 ,[Offset], Magnet, 35,
 ,[Offset], Gate, 36,
 ,[Offset], One_Way_Gate_A_Back, 37,
 ,[Offset], One_Way_Gate_A_Front, 38,
 ,[Offset], One_Way_Gate_B_Back, 39,
 ,[Offset], One_Way_Gate_B_Front, 40,
 ,[Offset], Switch_A, 41,
 ,[Offset], Switch_B, 42,
 ,[Offset], Switch_C, 43,
 ,[Offset], Switch_D, 44,
 ,[Offset], Slingshot_Left_Front, 45,
 ,[Offset], Slingshot_Right_Front, 46,
 ,[Offset], Playfield_Upper_Walls, 47,
RSID_TDRDUDE_PLACEMENT, 3083,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TDRDUDE_EMUROM, 3084,,,
 ,[Offset], dude_u26_l2, 0,
 ,[Offset], dude_u27_l2, 1,
 ,[Offset], dd_l2, 2,
 ,[Offset], dd_l2, 3,
 ,[Offset], dd_l2, 4,
 ,[Offset], dd_l2, 5,
 ,[Offset], dd_default, 6,
RSID_TDRDUDE_SOUNDS_START, 3085,,,
RSID_TDRDUDE_EMU_SOUNDS, 3086,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP, 1,
 ,[Offset], S0003-LP1, 2,
 ,[Offset], S0003-LP2, 3,
 ,[Offset], S0004-LP1, 4,
 ,[Offset], S0004-LP2, 5,
 ,[Offset], S0005-LP1, 6,
 ,[Offset], S0005-LP2, 7,
 ,[Offset], S0006-LP, 8,
 ,[Offset], S0007-LP1, 9,
 ,[Offset], S0007-LP2, 10,
 ,[Offset], S0008-LP, 11,
 ,[Offset], S0009-LP1, 12,
 ,[Offset], S0009-LP2, 13,
 ,[Offset], S000A-LP, 14,
 ,[Offset], S000B-LP1, 15,
 ,[Offset], S000B-LP2, 16,
 ,[Offset], S000C-LP, 17,
 ,[Offset], S000D-LP1, 18,
 ,[Offset], S000D-LP2, 19,
 ,[Offset], S000E-LP, 20,
 ,[Offset], S000F-LP1, 21,
 ,[Offset], S0014_C4, 22,
 ,[Offset], S0015_C4, 23,
 ,[Offset], S0016_C4, 24,
 ,[Offset], S0017_C4, 25,
 ,[Offset], S0030_C6, 26,
 ,[Offset], S0034_C6, 27,
 ,[Offset], S0035_C6, 28,
 ,[Offset], S0036_C6, 29,
 ,[Offset], S0037_C6, 30,
 ,[Offset], S0038_C6, 31,
 ,[Offset], S0039_C6, 32,
 ,[Offset], S003A_C6, 33,
 ,[Offset], S003B_C6, 34,
 ,[Offset], S003C_C6, 35,
 ,[Offset], S003D_C6, 36,
 ,[Offset], S003E_C6, 37,
 ,[Offset], S003F_C6, 38,
 ,[Offset], S0040_C6, 39,
 ,[Offset], S0044_C6, 40,
 ,[Offset], S0045_C6, 41,
 ,[Offset], S0046_C6, 42,
 ,[Offset], S0047_C6, 43,
 ,[Offset], S004A_C6, 44,
 ,[Offset], S004B_C6, 45,
 ,[Offset], S004C_C6, 46,
 ,[Offset], S004D_C6, 47,
 ,[Offset], S004E_C6, 48,
 ,[Offset], S004F_C6, 49,
 ,[Offset], S0050_C6, 50,
 ,[Offset], S0051_C6, 51,
 ,[Offset], S0052_C6, 52,
 ,[Offset], S0053_C6, 53,
 ,[Offset], S0054_C6, 54,
 ,[Offset], S0055_C6, 55,
 ,[Offset], S0056_C6, 56,
 ,[Offset], S0057_C6, 57,
 ,[Offset], S0058_C6, 58,
 ,[Offset], S0059_C6, 59,
 ,[Offset], S005A_C6, 60,
 ,[Offset], S005B_C6, 61,
 ,[Offset], S005C_C6, 62,
 ,[Offset], S005D_C6, 63,
 ,[Offset], S005E_C6, 64,
 ,[Offset], S005F_C6, 65,
 ,[Offset], S0080_C4, 66,
 ,[Offset], S0081_C4, 67,
 ,[Offset], S0082_C4, 68,
 ,[Offset], S0083_C4, 69,
 ,[Offset], S0084_C4, 70,
 ,[Offset], S0085_C4, 71,
 ,[Offset], S0086_C4, 72,
 ,[Offset], S0087_C4, 73,
 ,[Offset], S0088_C4, 74,
 ,[Offset], S0089_C4, 75,
 ,[Offset], S008A_C4, 76,
 ,[Offset], S008B_C4, 77,
 ,[Offset], S008C_C4, 78,
 ,[Offset], S008D_C4, 79,
 ,[Offset], S008E_C4, 80,
 ,[Offset], S008F_C4, 81,
 ,[Offset], S0090_C4, 82,
 ,[Offset], S0091_C4, 83,
 ,[Offset], S0092_C4, 84,
 ,[Offset], S0093_C4, 85,
 ,[Offset], S0094_C4, 86,
 ,[Offset], S0095_C4, 87,
 ,[Offset], S0096_C4, 88,
 ,[Offset], S0097_C4, 89,
 ,[Offset], S0098_C4, 90,
 ,[Offset], S0099_C4, 91,
 ,[Offset], S009A_C4, 92,
 ,[Offset], S009B_C4, 93,
 ,[Offset], S009C_C4, 94,
 ,[Offset], S009D_C4, 95,
 ,[Offset], S009E_C4, 96,
 ,[Offset], S009F_C4, 97,
 ,[Offset], S00A0_C4, 98,
 ,[Offset], S00A1_C4, 99,
 ,[Offset], S00A2_C4, 100,
 ,[Offset], S00A3_C4, 101,
 ,[Offset], S00A4_C4, 102,
 ,[Offset], S00A5_C4, 103,
 ,[Offset], S00A6_C4, 104,
 ,[Offset], S00A7_C4, 105,
 ,[Offset], S00A8_C4, 106,
 ,[Offset], S00A9_C4, 107,
 ,[Offset], S00AA_C4, 108,
 ,[Offset], S00AB_C4, 109,
 ,[Offset], S00AC_C4, 110,
 ,[Offset], S00AD_C4, 111,
 ,[Offset], S00AE_C4, 112,
 ,[Offset], S00AF_C4, 113,
 ,[Offset], S00B0_C4, 114,
 ,[Offset], S00B1_C4, 115,
 ,[Offset], S00B2_C4, 116,
 ,[Offset], S00B3_C4, 117,
 ,[Offset], S00B4_C4, 118,
 ,[Offset], S00B5_C4, 119,
 ,[Offset], S00B6_C4, 120,
 ,[Offset], S00B7_C4, 121,
 ,[Offset], S00B8_C4, 122,
 ,[Offset], S00B9_C4, 123,
 ,[Offset], S00BA_C4, 124,
 ,[Offset], S00BB_C4, 125,
 ,[Offset], S00BC_C4, 126,
 ,[Offset], S00BD_C4, 127,
 ,[Offset], S00BE_C4, 128,
 ,[Offset], S00BF_C4, 129,
 ,[Offset], S00C0_C4, 130,
 ,[Offset], S00C1_C4, 131,
 ,[Offset], S00C2_C4, 132,
 ,[Offset], S00C3_C4, 133,
 ,[Offset], S00C4_C4, 134,
 ,[Offset], S00C5_C4, 135,
 ,[Offset], S00C6_C4, 136,
 ,[Offset], S00C7_C4, 137,
 ,[Offset], S00C8_C4, 138,
 ,[Offset], S00C9_C4, 139,
 ,[Offset], S00CA_C4, 140,
 ,[Offset], S00CB_C4, 141,
 ,[Offset], S00CC_C4, 142,
 ,[Offset], S00CD_C4, 143,
 ,[Offset], S00CE_C4, 144,
 ,[Offset], S00CF_C4, 145,
 ,[Offset], S00D0_C4, 146,
 ,[Offset], S00D1_C4, 147,
 ,[Offset], S00D2_C4, 148,
 ,[Offset], S00D3_C4, 149,
 ,[Offset], S00D7_C4, 150,
 ,[Offset], S00D8_C4, 151,
 ,[Offset], S00D9_C4, 152,
 ,[Offset], S00DA_C4, 153,
 ,[Offset], S00DB_C4, 154,
 ,[Offset], S00DC_C4, 155,
 ,[Offset], S00DD_C4, 156,
 ,[Offset], S00DE_C4, 157,
 ,[Offset], S00DF_C4, 158,
 ,[Offset], S00E0_C4, 159,
 ,[Offset], S00E1_C4, 160,
 ,[Offset], S00E2_C4, 161,
 ,[Offset], S00E3_C4, 162,
 ,[Offset], S00E4_C4, 163,
 ,[Offset], S00E5_C4, 164,
 ,[Offset], S00E6_C4, 165,
 ,[Offset], S00E7_C4, 166,
 ,[Offset], S00E9_C4, 167,
 ,[Offset], S00EA_C4, 168,
 ,[Offset], S00EB_C4, 169,
 ,[Offset], S00EC_C4, 170,
 ,[Offset], S00ED_C4, 171,
 ,[Offset], S00EE_C4, 172,
 ,[Offset], S00EF_C4, 173,
 ,[Offset], S00F0_C4, 174,
 ,[Offset], S00F1_C4, 175,
 ,[Offset], S00F2_C4, 176,
 ,[Offset], S00F3_C4, 177,
 ,[Offset], S00F4_C4, 178,
 ,[Offset], S00F5_C4, 179,
 ,[Offset], S00F6_C4, 180,
 ,[Offset], S00F7_C4, 181,
 ,[Offset], S00F9_C4, 182,
 ,[Offset], S00FA_C4, 183,
 ,[Offset], S00FB_C4, 184,
 ,[Offset], S00FD_C4, 185,
 ,[Offset], S0201_C3, 186,
 ,[Offset], S0203_C3, 187,
 ,[Offset], S0205_C3, 188,
 ,[Offset], S0207_C3, 189,
 ,[Offset], S0209_C3, 190,
 ,[Offset], S020B_C3, 191,
 ,[Offset], S020D_C3, 192,
 ,[Offset], S020E_C3, 193,
RSID_TDRDUDE_MECH_SOUNDS, 3087,,,
 ,[Offset], bigshot, 0,
 ,[Offset], excellent_ray_vertical, 1,
 ,[Offset], gab_vertical, 2,
 ,[Offset], magnet, 3,
 ,[Offset], mixmaster, 4,
 ,[Offset], plunge, 5,
RSID_TDRDUDE_SOUNDS_END, 3088,,,
RSID_TDRDUDE_SAMPLES, 3089,,,
RSID_TDRDUDE_HUD, 3090,,,
RSID_TDRDUDE_END, 3091,,,
