#pragma once

#include <array>
#include <cstdint>
#include <memory>
#include <string>
#include <vector>

namespace ps4 {

/**
 * @brief SELF file header structure
 */
#pragma pack(push, 1)
struct SelfHeader {
  uint32_t magic;         // 'SCE\0' or '\x7FSCE'
  uint32_t version;       // SELF version
  uint32_t key_type;      // Key type (debug, retail, testkit)
  uint32_t header_size;   // Size of this header
  uint32_t meta_size;     // Size of metadata
  uint64_t file_size;     // Total file size
  uint64_t program_size;  // Size of program data
  uint32_t segment_count; // Number of segments
  uint32_t flags;         // SELF flags
  uint64_t reserved[4];   // Reserved fields
};
#pragma pack(pop)

// Constants
constexpr uint32_t SELF_MAGIC = 0x464C457F; // ELF Magic
constexpr uint32_t SCE_MAGIC = 0x1D3D154F; // SCE Magic
constexpr uint32_t NPD_MAGIC = 0x4E504400; // 'NPD\0'
constexpr size_t MIN_SELF_SIZE = sizeof(SelfHeader);
constexpr size_t MAX_SELF_SIZE = 0x10000000; // 256MB max

/**
 * @brief SELF key types
 */
enum class SelfKeyType : uint32_t {
  DEBUG = 0x00,
  RETAIL = 0x01,
  TESTKIT = 0x02
};

/**
 * @brief SELF section types
 */
enum class SelfSectionType : uint32_t {
  UNKNOWN = 0x00,
  PHDREXT = 0x01,
  DIGEST = 0x02,
  KEYS = 0x03
};

/**
 * @brief SELF segment information
 */
struct SelfSegmentInfo {
  uint64_t flags;         // Segment flags
  uint64_t file_offset;   // Offset in file
  uint64_t file_size;     // Size in file
  uint64_t memory_size;   // Size in memory
  uint64_t memory_offset; // Offset in memory
  uint32_t compression;   // Compression type (1 for zlib)
  uint32_t encryption;    // Encryption type (1 for AES-CBC, 2 for AES-CTR)
  uint32_t key_idx;       // Key index
  uint32_t iv_idx;        // IV index
};

struct AppInfo {
  uint64_t auth_id;
  uint32_t vendor_id;
  uint32_t self_type;
  uint64_t version;
  uint64_t padding;
};

struct ElfDigest {
  std::array<uint8_t, 32> digest;
};

struct ControlInfo {
  uint32_t type;
  uint32_t size;
  uint64_t next;
  std::vector<uint8_t> data;
};

struct NpdHeader {
  uint32_t magic; // 'NPD\0'
  uint32_t version;
  uint32_t license;
  uint32_t type;
  std::array<uint8_t, 48> content_id;
  std::array<uint8_t, 16> digest;
  std::array<uint8_t, 16> title_hash;
  std::array<uint8_t, 16> dev_hash;
  uint64_t unk1;
  uint64_t unk2;
};

struct SelfEntry {
  uint64_t props;
  uint64_t offset;
  uint64_t size;
  uint32_t algorithm;
  uint32_t key_idx;
  uint32_t iv_idx;
  uint32_t compressed;
};

struct DecryptionKey {
  std::vector<uint8_t> key;
  std::vector<uint8_t> iv;
  uint32_t key_type;
  uint32_t key_revision;
};

struct SelfDigestEntry;

struct SelfEncryptionInfo {
  uint32_t encryption_type;
  uint32_t key_revision;
  uint32_t self_type;
  std::vector<DecryptionKey> keys;
  std::vector<SelfDigestEntry> digests; // Added to store digest entries
};

// Missing structure definitions for SELF metadata parsing
#pragma pack(push, 1)
struct SelfMetadataHeader {
  uint64_t key_count;
  uint64_t key_table_offset;
  uint64_t section_count;
  uint64_t section_table_offset;
  uint64_t data_size;
  uint64_t data_offset;
  uint64_t reserved[2];
};

struct SelfSectionHeader {
  uint64_t data_offset;
  uint64_t data_size;
  uint32_t type;
  uint32_t program_idx;
  uint32_t hashed;
  uint32_t sha1_idx;
  uint32_t encrypted;
  uint32_t key_idx;
  uint32_t iv_idx;
  uint32_t compressed;
  uint64_t file_offset;
  uint64_t file_size;
  uint64_t reserved[2];
};

struct SelfKeyEntry {
  uint8_t key[32];
  uint8_t iv[16];
  uint32_t algorithm;
  uint32_t key_revision;
  uint64_t reserved[2];
};

struct SelfDigestEntry {
  uint8_t digest[32];
  uint64_t offset;
  uint64_t size;
  uint32_t algorithm;
  uint32_t reserved;
};
#pragma pack(pop)

/**
 * @brief SELF decryption utility class
 */
class SelfDecrypter {
public:
  SelfDecrypter();
  ~SelfDecrypter();

  // Core functionality
  bool LoadSelfFile(const std::string &filepath);
  bool LoadSelfData(const std::vector<uint8_t> &data);
  bool DecryptSelf();
  bool ExtractElfData(std::vector<uint8_t> &elf_data);

  // Static methods for convenience
  static bool IsEncryptedSelf(const std::vector<uint8_t> &data);
  static std::vector<uint8_t> DecryptSelf(const std::vector<uint8_t> &data);

  // Information getters
  const SelfHeader &GetSelfHeader() const { return self_header_; }
  const AppInfo &GetAppInfo() const { return app_info_; }
  const std::vector<SelfEntry> &GetEntries() const { return entries_; }

  // Validation
  bool IsValidSelf() const;
  bool IsEncrypted() const;
  uint32_t GetSelfType() const { return app_info_.self_type; }

  // Error handling
  const std::string &GetLastError() const { return last_error_; }

private:
  // Internal parsing methods
  bool ParseSelfHeader(const uint8_t *data, size_t size);
  bool ParseAppInfo(const uint8_t *data, size_t offset);
  bool ParseElfDigest(const uint8_t *data, size_t offset);
  bool ParseControlInfo(const uint8_t *data, size_t offset, size_t size);
  bool ParseNpdHeader(const uint8_t *data, size_t offset);
  bool ParseMetadata(const std::vector<uint8_t> &data, const SelfHeader &header, std::vector<SelfSegmentInfo> &segments);

  // Decryption methods
  bool DecryptSegment(const SelfEntry &entry, const uint8_t *encrypted_data,
                      std::vector<uint8_t> &decrypted_data);
  bool DecryptSegment(const std::vector<uint8_t> &input, const SelfSegmentInfo &seg_info,
                     std::vector<uint8_t> &output);
  bool DecompressSegment(std::vector<uint8_t> &data);
  bool DecompressSegment(const std::vector<uint8_t> &compressed_data,
                         std::vector<uint8_t> &decompressed_data);

  // Key management
  bool LoadSystemKeys();
  bool DeriveContentKey(const DecryptionKey &master_key,
                        const std::array<uint8_t, 16> &iv,
                        DecryptionKey &content_key) const;
  std::vector<uint8_t> GetKey(uint32_t key_idx) const;
  std::vector<uint8_t> GetIv(uint32_t iv_idx) const;
  bool VerifySegmentHash(const std::vector<uint8_t> &data,
                          const SelfSegmentInfo &seg_info);

  // Crypto operations
  bool AesDecrypt(const std::vector<uint8_t> &encrypted_data,
                  const DecryptionKey &key,
                  std::vector<uint8_t> &decrypted_data);
  bool AesCtrDecrypt(const std::vector<uint8_t> &encrypted_data,
                     const DecryptionKey &key,
                     const std::array<uint8_t, 16> &iv,
                     std::vector<uint8_t> &decrypted_data) const;

  // Utility methods
  bool ValidateDigest(const std::vector<uint8_t> &data,
                      const std::array<uint8_t, 32> &expected_digest);
  void SetError(const std::string &error);
  void ClearError();

  // Member variables
  std::vector<uint8_t> self_data_;
  SelfHeader self_header_;
  AppInfo app_info_;
  ElfDigest elf_digest_;
  NpdHeader npd_header_;
  std::vector<SelfEntry> entries_;
  std::vector<ControlInfo> control_info_;
  SelfEncryptionInfo encryption_info_;

  std::vector<uint8_t> decrypted_elf_;
  bool is_loaded_;
  bool is_decrypted_;
  std::string last_error_;

  // System keys (loaded from keystore)
  std::vector<DecryptionKey> system_keys_;
};

// Utility functions for SELF handling
namespace SelfUtils {
bool IsSelfFile(const std::vector<uint8_t> &data);
bool IsSelfFile(const std::string &filepath);
uint32_t GetSelfType(const std::vector<uint8_t> &data);
bool ExtractSelfInfo(const std::vector<uint8_t> &data, SelfHeader &header,
                     AppInfo &app_info);
} // namespace SelfUtils

} // namespace ps4