#pragma once

#include <algorithm>
#include <array>
#include <cstdint>
#include <memory>
#include <stdexcept>
#include <string>
#include <vector>

namespace ps4 {

// AES encryption/decryption utilities
class AESCrypto {
public:
#define AES_BLOCK_SIZE 16
#define AES_KEY_SIZE_128 16
#define AES_KEY_SIZE_256 32

  enum class Mode { ECB, CBC, CTR, GCM };

  // Initialize AES context
  static bool Initialize();
  static void Cleanup();

  // AES-128
  static bool
  EncryptAES128_ECB(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    std::vector<uint8_t> &ciphertext);
  static bool
  DecryptAES128_ECB(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    std::vector<uint8_t> &plaintext);
  static bool
  EncryptAES128_CBC(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext);
  static bool
  DecryptAES128_CBC(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &plaintext);
  static bool
  EncryptAES128_CTR(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext);
  static bool
  DecryptAES128_CTR(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &plaintext);
  static bool
  EncryptAES128_GCM(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext,
                    std::array<uint8_t, AES_BLOCK_SIZE> &tag);
  static bool
  DecryptAES128_GCM(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &tag,
                    std::vector<uint8_t> &plaintext);

  // AES-256
  static bool
  EncryptAES256_CBC(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext);
  static bool
  DecryptAES256_CBC(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &plaintext);
  static bool
  EncryptAES256_CTR(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext);
  static bool
  DecryptAES256_CTR(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &plaintext);
  static bool
  EncryptAES256_GCM(const std::vector<uint8_t> &plaintext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    std::vector<uint8_t> &ciphertext,
                    std::array<uint8_t, AES_BLOCK_SIZE> &tag);
  static bool
  DecryptAES256_GCM(const std::vector<uint8_t> &ciphertext,
                    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
                    const std::array<uint8_t, AES_BLOCK_SIZE> &tag,
                    std::vector<uint8_t> &plaintext);

private:
  static bool initialized_;
};

// SHA hashing utilities
class SHACrypto {
public:
  enum {
    SHA1_DIGEST_SIZE = 20,
    SHA256_DIGEST_SIZE = 32,
    SHA512_DIGEST_SIZE = 64
  };

  // SHA-256
  static bool ComputeSHA256(const std::vector<uint8_t> &data,
                            std::array<uint8_t, SHA256_DIGEST_SIZE> &digest);

  static bool ComputeSHA256(const uint8_t *data, size_t length,
                            std::array<uint8_t, SHA256_DIGEST_SIZE> &digest);

  // HMAC operations
  static bool ComputeHMAC_SHA256(const std::vector<uint8_t> &data,
                                 const std::vector<uint8_t> &key,
                                 std::array<uint8_t, SHA256_DIGEST_SIZE> &hmac);
};

// Random number generation
class RandomGenerator {
public:
  static bool Initialize();
  static void Cleanup();

  static bool GenerateRandomBytes(std::vector<uint8_t> &buffer, size_t size);
  static bool GenerateRandomBytes(uint8_t *buffer, size_t size);

private:
  static bool initialized_;
};
// Hex encoding/decoding
std::string BytesToHex(const std::vector<uint8_t> &bytes);
std::string BytesToHex(const uint8_t *bytes, size_t length);
bool HexToBytes(const std::string &hex, std::vector<uint8_t> &bytes);

// Constant time comparison
bool ConstantTimeCompare(const std::vector<uint8_t> &a,
                         const std::vector<uint8_t> &b);
bool ConstantTimeCompare(const uint8_t *a, const uint8_t *b, size_t length);

// Memory operations
void SecureZeroMemory(void *ptr, size_t size);
void SecureZeroMemory(std::vector<uint8_t> &data);

class AesCbcCrypto {
private:
  std::vector<uint8_t> key_;
  std::vector<uint8_t> iv_;

public:
  AesCbcCrypto(const std::vector<uint8_t> &key, const std::vector<uint8_t> &iv);
  std::vector<uint8_t> Decrypt(const std::vector<uint8_t> &ciphertext);
};

} // namespace ps4