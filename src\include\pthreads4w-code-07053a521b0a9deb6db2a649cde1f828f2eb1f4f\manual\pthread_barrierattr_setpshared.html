<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_BARRIERATTR_SETPSHARED(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_barrierattr_getpshared, pthread_barrierattr_setpshared -
get and set the process-shared attribute of the barrier attributes
object (<B>ADVANCED REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_barrierattr_getpshared(const pthread_barrierattr_t
* restrict</B> <I>attr</I><B>, int *restrict</B> <I>pshared</I><B>);
<BR>int pthread_barrierattr_setpshared(pthread_barrierattr_t *</B><I>attr</I><B>,
int</B> <I>pshared</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_barrierattr_getpshared</B> function shall obtain
the value of the <I>process-shared</I> attribute from the attributes
object referenced by <I>attr</I>. The <B>pthread_barrierattr_setpshared</B>
function shall set the <I>process-shared</I> attribute in an
initialized attributes object referenced by <I>attr</I>. 
</P>
<P>The <I>process-shared</I> attribute is set to
PTHREAD_PROCESS_SHARED to permit a barrier to be operated upon by any
thread that has access to the memory where the barrier is allocated.
If the <I>process-shared</I> attribute is PTHREAD_PROCESS_PRIVATE,
the barrier shall only be operated upon by threads created within the
same process as the thread that initialized the barrier; if threads
of different processes attempt to operate on such a barrier, the
behavior is undefined. The default value of the attribute shall be
PTHREAD_PROCESS_PRIVATE. Both constants PTHREAD_PROCESS_SHARED and
PTHREAD_PROCESS_PRIVATE are defined in <I>&lt;pthread.h&gt;</I>. 
</P>
<P><B>PThreads4W</B> defines _<B>POSIX_THREAD_PROCESS_SHARED</B> in
pthread.h as -1 to indicate that these routines are implemented but
that the process shared attribute is not supported.</P>
<P>Additional attributes, their default values, and the names of the
associated functions to get and set those attribute values are
implementation-defined. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>If successful, the <B>pthread_barrierattr_getpshared</B> function
shall return zero and store the value of the <I>process-shared</I>
attribute of <I>attr</I> into the object referenced by the <I>pshared</I>
parameter. Otherwise, an error number shall be returned to indicate
the error. 
</P>
<P>If successful, the <B>pthread_barrierattr_setpshared</B> function
shall return zero; otherwise, an error number shall be returned to
indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>These functions may fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>attr</I> is invalid. 
	</DD><DT>
	The <B>pthread_barrierattr_setpshared</B> function may fail if: 
	</DT><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The new value specified for the <I>process-shared</I> attribute is
	not one of the legal values <B>PTHREAD_PROCESS_SHARED</B> or
	<B>PTHREAD_PROCESS_PRIVATE</B>. 
	</DD><DT>
	<B>ENOSYS</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>attr</I> was <B>PTHREAD_PROCESS_SHARED</B>
	(PThreads4W).</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>The <B>pthread_barrierattr_getpshared</B> and
<B>pthread_barrierattr_setpshared</B> functions are part of the
Barriers option and need not be provided on all implementations. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_BARRIERS</B> and
<B>_POSIX_THREAD_PROCESS_SHARED</B> in pthread.h as -1 to indicate
that these routines are implemented and may be used, but do not
support the process shared option.</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_barrier_init.html"><B>pthread_barrier_destroy</B>(3)</A>
<B>,</B> <A HREF="pthread_barrierattr_init.html"><B>pthread_barrierattr_destroy</B>(3)</A>
<B>,</B> <A HREF="pthread_barrierattr_init.html"><B>pthread_barrierattr_init</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
