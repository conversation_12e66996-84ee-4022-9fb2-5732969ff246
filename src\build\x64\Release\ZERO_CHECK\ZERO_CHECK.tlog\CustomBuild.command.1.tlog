^D:\SSS\SRC\BUILD\CMAKEFILES\7A7BBF07622BCBAF7C37C1713F4F64B6\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/sss/src/build/PS4Emulator.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
