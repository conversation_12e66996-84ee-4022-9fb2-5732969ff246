// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/memory.h"
#include "../memory/ps4_mmu.h"
#include "fiber_manager.h"
#include "trophy_manager.h"
#include "zlib_wrapper.h"

// Forward declarations to avoid circular dependencies
namespace x86_64 {
class X86_64CPU;
class CPUContext;
}

namespace ps4 {
class PS4Emulator;
class SyscallHandler;
}
#include <atomic>
#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <shared_mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

namespace ps4 {

class PS4Emulator;

/**
 * @brief File descriptor information (local copy to avoid circular dependency).
 */
struct FDInfo {
  bool isOpen = false;      ///< Is the FD open?
  int hostFd = -1;          ///< Host OS file descriptor
  std::string path;         ///< File path
  int flags = 0;            ///< Open flags (O_RDONLY, etc.)
  int mode = 0;             ///< File mode (permissions)
  uint64_t offset = 0;      ///< Current file offset
  uint64_t cacheHits = 0;   ///< Cache hits for FD operations
  uint64_t cacheMisses = 0; ///< Cache misses for FD operations
};

/**
 * @brief Exception for OrbisOS-related errors.
 */
struct OSException : std::runtime_error {
  explicit OSException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Structure representing a process.
 */
struct Process {
  uint64_t id;          ///< Process ID
  std::string path;     ///< Path to executable
  uint64_t baseAddress; ///< Base virtual address
  uint64_t size;        ///< Memory size
  std::unique_ptr<std::vector<FDInfo>>
      fdTable;                                             ///< File descriptors
  std::unique_ptr<std::map<uint64_t, uint64_t>> memoryMap; ///< Memory mappings
  uint64_t executionTimeUs = 0; ///< Total execution time (microseconds)
  uint64_t cacheHits = 0;       ///< Cache hits
  uint64_t cacheMisses = 0;     ///< Cache misses

  Process(uint64_t pid, const std::string &path, uint64_t baseAddr, uint64_t sz)
      : id(pid), path(path), baseAddress(baseAddr), size(sz) {
    fdTable = std::make_unique<std::vector<FDInfo>>();
    memoryMap = std::make_unique<std::map<uint64_t, uint64_t>>();
  }
};

/**
 * @brief Structure representing a thread.
 */
struct Thread {
  uint64_t id = 0;                  ///< Thread ID
  uint64_t processId = 0;           ///< Associated process ID
  uint64_t entry = 0;               ///< Entry point address
  void *arg = nullptr;              ///< Argument for entry point
  int basePriority = 0;             ///< The base priority of the thread
  int priority = 0;                 ///< Current effective priority (lower is higher)
  int assignedCoreId = -1;          ///< Assigned CPU core
  std::thread hostThread;           ///< Host thread
  std::atomic<bool> running{false}; ///< Running state
  std::atomic<bool> waiting{false}; ///< Waiting state
  std::mutex mutex;                 ///< Thread mutex
  std::condition_variable cv;       ///< Condition variable
  std::unique_ptr<x86_64::CPUContext> cpuContext; ///< CPU state
  uint64_t executionTimeUs = 0; ///< Execution time (microseconds)
  uint64_t cacheHits = 0;       ///< Cache hits
  uint64_t cacheMisses = 0;     ///< Cache misses
  std::vector<uint64_t> heldMutexes; ///< List of mutexes held by this thread
};

/**
 * @brief Enhanced structure representing a mutex with PS4-specific features.
 */
struct Mutex {
  /** Constructor to initialize mutex with a name */
  explicit Mutex(const std::string &name_) : name(name_) {}
  std::string name;                    ///< Mutex name
  std::mutex internalMutex;            ///< Internal mutex for synchronization
  std::condition_variable cv;          ///< Condition variable for waiting
  uint64_t ownerThread = 0;            ///< Owning thread ID
  uint32_t recursionCount = 0;         ///< Recursion count
  std::queue<uint64_t> waitingThreads; ///< Waiting threads
  uint64_t lockCount = 0;              ///< Number of locks
  uint64_t unlockCount = 0;            ///< Number of unlocks

  // Enhanced PS4-specific features
  int originalPriority = 0;        ///< Original owner priority before inheritance
  int inheritedPriority = 0;       ///< Inherited priority
  bool priorityInheritance = true; ///< Priority inheritance enabled
  uint32_t attributes = 0;         ///< Mutex attributes
  std::chrono::steady_clock::time_point creationTime; ///< Creation time
  std::chrono::nanoseconds totalWaitTime{0};          ///< Total wait time
  uint64_t contentionCount = 0;                       ///< Number of contentions
};

/**
 * @brief Enhanced structure representing a semaphore with PS4-specific
 * features.
 */
struct Semaphore {
  /** Constructor to initialize semaphore with name and initial count */
  Semaphore(const std::string &name_, uint32_t initialCount_)
      : name(name_), count(initialCount_), maxCount(initialCount_) {}
  std::string name;                    ///< Semaphore name
  uint32_t count;                      ///< Current count
  uint32_t maxCount;                   ///< Maximum count
  std::mutex mutex;                    ///< Semaphore mutex
  std::condition_variable cv;          ///< Condition variable
  std::queue<uint64_t> waitingThreads; ///< Waiting threads
  uint64_t waitCount = 0;              ///< Number of waits
  uint64_t signalCount = 0;            ///< Number of signals

  // Enhanced PS4-specific features
  uint32_t attributes = 0;                            ///< Semaphore attributes
  std::chrono::steady_clock::time_point creationTime; ///< Creation time
  std::chrono::nanoseconds totalWaitTime{0};          ///< Total wait time
  uint64_t timeoutCount = 0;                          ///< Number of timeouts
  bool fifoOrdering = true; ///< FIFO vs priority ordering
};

/**
 * @brief Emulates the PS4 Orbis operating system.
 * @details Manages processes, threads, mutexes, semaphores, and fibers,
 * integrating with the PS4 emulator's MMU, CPU, and other components.
 */
class OrbisOS {
public:
  /**
   * @brief Constructs an OrbisOS instance.
   * @param emu Reference to the PS4 emulator.
   */
  explicit OrbisOS(PS4Emulator &emu);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~OrbisOS();

  /**
   * @brief Initializes the OrbisOS.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the OrbisOS, releasing resources.
   */
  void Shutdown();

  /**
   * @brief Creates a new process.
   * @param path Path to the executable.
   * @return Process ID, or 0 on failure.
   */
  uint64_t SceCreateProcess(const std::string &path);

  /**
   * @brief Handles memory allocation errors.
   * @param size Size that failed to allocate.
   * @param error The error message.
   * @throws ps4::MemoryException
   */
  void HandleMemoryError(size_t size, const std::string& error) {
    throw ps4::MemoryException(error);
  }

  /**
   * @brief Allocates virtual memory for a process.
   * @param size Size to allocate.
   * @param alignment Alignment requirement.
   * @param shared True if shared memory.
   * @return Virtual address, or 0 on failure.
   * @throws MemoryAllocationException on allocation failure
   */
  uint64_t AllocateVirtualMemory(size_t size, size_t alignment, bool shared);

  /**
   * @brief Sets memory protection for a region.
   * @param address Virtual address.
   * @param size Size of region.
   * @param protection Protection flags.
   * @return True on success, false otherwise.
   */
  bool ProtectMemory(uint64_t address, uint64_t size, int protection);

  /**
   * @brief Frees virtual memory.
   * @param address Virtual address.
   */
  void FreeVirtualMemory(uint64_t address);

  /**
   * @brief Exits the current process.
   * @param code Exit code.
   */
  void ExitProcess(int code);

  /**
   * @brief Creates a new thread.
   * @param processId Process ID.
   * @param entry Entry point address.
   * @param arg Argument for entry point.
   * @param priority Thread priority.
   * @return Thread ID, or 0 on failure.
   */
  uint64_t CreateThread(uint64_t processId, uint64_t entry, void *arg,
                        int priority);

  /**
   * @brief Exits the specified thread.
   * @param tid Thread ID.
   */
  void ExitThread(uint64_t tid);

  /**
   * @brief Schedules threads for execution.
   */
  void ScheduleThreads();

  /**
   * @brief Gets the current thread ID.
   * @return Thread ID, or 0 if not in a thread.
   */
  uint64_t GetCurrentThreadId();

  /**
   * @brief Creates a new mutex.
   * @param name Mutex name.
   * @return Mutex ID, or 0 on failure.
   */
  uint64_t SceCreateMutex(const char *name);

  /**
   * @brief Locks a mutex with a timeout.
   * @param mutexId Mutex ID.
   * @param timeoutUs Timeout in microseconds.
   * @return True on success, false if timed out or invalid.
   */
  bool LockMutex(uint64_t mutexId, uint64_t timeoutUs);

  /**
   * @brief Attempts to lock a mutex without waiting.
   * @param mutexId Mutex ID.
   * @return True on success, false if invalid or locked.
   */
  bool TryLockMutex(uint64_t mutexId);

  /**
   * @brief Unlocks a mutex.
   * @param mutexId Mutex ID.
   */
  void UnlockMutex(uint64_t mutexId);

  /**
   * @brief Creates a new semaphore.
   * @param name Semaphore name.
   * @param initialCount Initial count.
   * @return Semaphore ID, or 0 on failure.
   */
  uint64_t SceCreateSemaphore(const char *name, uint32_t initialCount);

  /**
   * @brief Waits on a semaphore with a timeout.
   * @param semId Semaphore ID.
   * @param timeoutUs Timeout in microseconds.
   * @return True on success, false if timed out or invalid.
   */
  bool WaitSemaphore(uint64_t semId, uint64_t timeoutUs);

  /**
   * @brief Signals a semaphore.
   * @param semId Semaphore ID.
   */
  void SignalSemaphore(uint64_t semId);

  /**
   * @brief Gets the current process ID.
   * @return Process ID.
   */
  uint64_t SceKernelGetProcessId();

  /**
   * @brief Sleeps the current thread.
   * @param microseconds Sleep duration.
   * @return 0 on success.
   */
  int SceKernelUsleep(uint32_t microseconds);

  /**
   * @brief Gets the CPU frequency.
   * @return CPU frequency in Hz.
   */
  int SceKernelGetCpuFrequency();

  /**
   * @brief Creates a new fiber.
   * @param name Fiber name.
   * @param entry Entry point address.
   * @param arg Argument for entry point.
   * @param stackSize Stack size in bytes.
   * @return Fiber ID, or 0 on failure.
   */
  uint64_t SceKernelCreateFiber(const char *name, uint64_t entry, void *arg,
                                uint64_t stackSize);

  /**
   * @brief Deletes a fiber.
   * @param fiberId Fiber ID.
   * @return 0 on success, -1 on failure.
   */
  int SceKernelDeleteFiber(uint64_t fiberId);

  /**
   * @brief Switches to a fiber.
   * @param fiberId Fiber ID.
   * @param argCount Number of arguments.
   * @param args Arguments.
   * @return 0 on success, -1 on failure.
   */
  int SceKernelSwitchToFiber(uint64_t fiberId, uint64_t argCount, void *args);

  /**
   * @brief Initializes the trophy system.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyInit();

  /**
   * @brief Creates a trophy context.
   * @param context Output context ID.
   * @param titleId Title ID.
   * @param titleSecret Title secret.
   * @param userData User data.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyCreateContext(uint32_t *context, const char *titleId,
                               const char *titleSecret, void *userData);

  /**
   * @brief Unlocks a trophy.
   * @param context Context ID.
   * @param handle Trophy handle.
   * @param trophyId Trophy ID.
   * @param platinumId Output platinum trophy ID.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyUnlockTrophy(uint32_t context, uint32_t handle,
                              uint32_t trophyId, uint32_t *platinumId);

  /**
   * @brief Decompresses data using zlib.
   * @param dst Output buffer.
   * @param dstSize Output size.
   * @param src Input buffer.
   * @param srcSize Input size.
   * @return 0 on success, -1 on failure.
   */
  int SceZlibDecompress(void *dst, size_t *dstSize, const void *src,
                        size_t srcSize);

  /**
   * @brief Compresses data using zlib.
   * @param dst Output buffer.
   * @param dstSize Output size.
   * @param src Input buffer.
   * @param srcSize Input size.
   * @param level Compression level.
   * @return 0 on success, -1 on failure.
   */
  int SceZlibCompress(void *dst, size_t *dstSize, const void *src,
                      size_t srcSize, int level);

  /**
   * @brief Saves the OrbisOS state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out);

  /**
   * @brief Loads the OrbisOS state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Operating system statistics with atomic members to prevent race
   * conditions.
   */
  struct Stats {
    std::atomic<uint64_t> processCount{0};   ///< Number of processes
    std::atomic<uint64_t> threadCount{0};    ///< Number of threads
    std::atomic<uint64_t> mutexCount{0};     ///< Number of mutexes
    std::atomic<uint64_t> semaphoreCount{0}; ///< Number of semaphores
    std::atomic<uint64_t> totalLatencyUs{
        0}; ///< Total operation latency (microseconds)
    std::atomic<uint64_t> cacheHits{0};   ///< Cache hits
    std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : processCount(other.processCount.load()),
          threadCount(other.threadCount.load()),
          mutexCount(other.mutexCount.load()),
          semaphoreCount(other.semaphoreCount.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        processCount.store(other.processCount.load());
        threadCount.store(other.threadCount.load());
        mutexCount.store(other.mutexCount.load());
        semaphoreCount.store(other.semaphoreCount.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves operating system statistics.
   * @return Current statistics.
   */
  Stats GetStats() const;

  /**
   * @brief Enhanced OS management methods.
   */
  bool SetThreadPriority(uint64_t tid, int priority);
  int GetThreadPriority(uint64_t tid);
  bool SetThreadAffinity(uint64_t tid, uint64_t affinityMask);
  uint64_t GetThreadAffinity(uint64_t tid);
  void YieldThread();
  bool SuspendThread(uint64_t tid);
  bool ResumeThread(uint64_t tid);
  std::vector<uint64_t> GetWaitingThreads(uint64_t mutexId);
  bool ValidateResourceLimits(uint64_t processId);
  void UpdateResourceUsage(uint64_t processId);

private:
  /**
   * @brief Thread entry point for execution.
   * @param tid Thread ID.
   * @param entry Entry point address.
   * @param arg Argument for entry point.
   */
  void ThreadEntry(uint64_t tid, uint64_t entry, void *arg);

  /**
   * @brief Handles priority inheritance for mutex contention.
   * @param mutexId The ID of the contended mutex.
   * @param waiterTid The ID of the thread waiting for the mutex.
   */
  void HandlePriorityInheritance(uint64_t mutexId, uint64_t waiterTid);

  /**
   * @brief Restores original priority after a mutex is unlocked.
   * @param ownerThread The thread that is releasing the mutex.
   * @param unlockedMutexId The ID of the mutex being released.
   */
  void RestoreOriginalPriority(Thread &ownerThread, uint64_t unlockedMutexId);

  PS4Emulator &m_emulator;                  ///< Reference to emulator
  mutable std::shared_mutex m_osMutex;      ///< Main mutex
  mutable std::shared_mutex m_tidMapMutex;  ///< Thread ID map mutex
  std::atomic<uint64_t> m_nextProcessId{1}; ///< Next process ID
  std::atomic<uint64_t> m_nextThreadId{1};  ///< Next thread ID
  std::atomic<uint64_t> m_nextMutexId{1};   ///< Next mutex ID
  std::atomic<uint64_t> m_nextSemId{1};     ///< Next semaphore ID
  std::map<uint64_t, Process> m_processes;  ///< Active processes
  std::map<uint64_t, Thread> m_threads;     ///< Active threads
  std::unordered_map<std::thread::id, uint64_t>
      m_hostToEmulatedTidMap;          ///< Host-to-emulated thread ID map
  std::map<uint64_t, Mutex> m_mutexes; ///< Active mutexes
  std::map<uint64_t, Semaphore> m_semaphores; ///< Active semaphores
  mutable Stats m_stats; ///< OS statistics (mutable for const updates)

  // Enhanced resource tracking
  std::unordered_map<uint64_t, uint64_t>
      m_threadAffinity; ///< Thread affinity masks
  std::unordered_map<uint64_t, bool>
      m_suspendedThreads; ///< Suspended thread flags
  std::unordered_map<uint64_t, uint64_t>
      m_processMemoryUsage; ///< Per-process memory usage
  std::unordered_map<uint64_t, uint64_t>
      m_processThreadCount; ///< Per-process thread count
  std::chrono::steady_clock::time_point
      m_lastScheduleTime; ///< Last scheduling time
};

} // namespace ps4