RSID_TCREATURE_START, 3250,,,
 ,[Offset], <PERSON>reature_Flyer_1, 0,
 ,[Offset], C<PERSON>tureLagoon\PBTCreature, 1,
 ,[Offset], CreatureLagoon\InstructionsENG, 2,
 ,[Offset], CreatureLagoon\InstructionsFR, 3,
 ,[Offset], CreatureLagoon\InstructionsITAL, 4,
 ,[Offset], CreatureLagoon\InstructionsGERM, 5,
 ,[Offset], CreatureLagoon\InstructionsSPAN, 6,
 ,[Offset], CreatureLagoon\InstructionsPORT, 7,
 ,[Offset], CreatureLagoon\InstructionsDUTCH, 8,
 ,[Offset], tables\Creature_BG_scroll, 9,
RSID_TCREATURE_LIGHTS, 3251,,,
RSID_TCREATURE_CAMERAS, 3252,,,
RSID_TCREATURE_LAMP_TEXTURES, 3253,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_87_Off, 124,
 ,[Offset], L_87_On, 125,
 ,[Offset], F_02_Off, 126,
 ,[Offset], F_02_On, 127,
 ,[Offset], F_08_Off, 128,
 ,[Offset], F_08_On, 129,
 ,[Offset], F_09_Off, 130,
 ,[Offset], F_09_On, 131,
 ,[Offset], F_10_Off, 132,
 ,[Offset], F_10_On, 133,
 ,[Offset], F_13_Off, 134,
 ,[Offset], F_13_On, 135,
 ,[Offset], F_14_Off, 136,
 ,[Offset], F_14_On, 137,
 ,[Offset], F_15_Off, 138,
 ,[Offset], F_15_On, 139,
 ,[Offset], F_16_Off, 140,
 ,[Offset], F_16_On, 141,
 ,[Offset], F_17_Off, 142,
 ,[Offset], F_17_On, 143,
 ,[Offset], F_18_Off, 144,
 ,[Offset], F_18_On, 145,
 ,[Offset], F_19_Off, 146,
 ,[Offset], F_19_On, 147,
 ,[Offset], F_22_Off, 148,
 ,[Offset], F_22_On, 149,
 ,[Offset], F_25_Off, 150,
 ,[Offset], F_25_On, 151,
 ,[Offset], F_28_Off, 152,
 ,[Offset], F_28_On, 153,
 ,[Offset], B_01_Off, 154,
 ,[Offset], B_01_On, 155,
 ,[Offset], B_02_Off, 156,
 ,[Offset], B_02_On, 157,
 ,[Offset], B_03_Off, 158,
 ,[Offset], B_03_On, 159,
 ,[Offset], B_04_Off, 160,
 ,[Offset], B_04_On, 161,
 ,[Offset], B_05_Off, 162,
 ,[Offset], B_05_On, 163,
 ,[Offset], B_06_Off, 164,
 ,[Offset], B_06_On, 165,
 ,[Offset], B_07_Off, 166,
 ,[Offset], B_07_On, 167,
 ,[Offset], B_08_Off, 168,
 ,[Offset], B_08_On, 169,
 ,[Offset], B_09_Off, 170,
 ,[Offset], B_09_On, 171,
 ,[Offset], B_10_Off, 172,
 ,[Offset], B_10_On, 173,
 ,[Offset], B_11_Off, 174,
 ,[Offset], B_11_On, 175,
 ,[Offset], B_12_Off, 176,
 ,[Offset], B_12_On, 177,
 ,[Offset], B_13_Off, 178,
 ,[Offset], B_13_On, 179,
 ,[Offset], B_14_Off, 180,
 ,[Offset], B_14_On, 181,
 ,[Offset], B_15_Off, 182,
 ,[Offset], B_15_On, 183,
 ,[Offset], B_16_Off, 184,
 ,[Offset], B_16_On, 185,
 ,[Offset], B_17_Off, 186,
 ,[Offset], B_17_On, 187,
 ,[Offset], B_18_Off, 188,
 ,[Offset], B_18_On, 189,
 ,[Offset], B_19_Off, 190,
 ,[Offset], B_19_On, 191,
 ,[Offset], B_20_Off, 192,
 ,[Offset], B_20_On, 193,
 ,[Offset], B_21_Off, 194,
 ,[Offset], B_21_On, 195,
 ,[Offset], B_22_Off, 196,
 ,[Offset], B_22_On, 197,
 ,[Offset], B_23_Off, 198,
 ,[Offset], B_23_On, 199,
 ,[Offset], B_24_Off, 200,
 ,[Offset], B_24_On, 201,
 ,[Offset], B_25_Off, 202,
 ,[Offset], B_25_On, 203,
 ,[Offset], B_26_Off, 204,
 ,[Offset], B_26_On, 205,
 ,[Offset], B_27_Off, 206,
 ,[Offset], B_27_On, 207,
 ,[Offset], B_28_Off, 208,
 ,[Offset], B_28_On, 209,
 ,[Offset], B_29_Off, 210,
 ,[Offset], B_29_On, 211,
 ,[Offset], B_30_Off, 212,
 ,[Offset], B_30_On, 213,
 ,[Offset], B_31_Off, 214,
 ,[Offset], B_31_On, 215,
 ,[Offset], B_32_Off, 216,
 ,[Offset], B_32_On, 217,
 ,[Offset], F_18_Off_B, 218,
 ,[Offset], F_18_On_B, 219,
 ,[Offset], F_02_Flasher_On, 220,
RSID_TCREATURE_TEXTURES, 3254,,,
 ,[Offset], Apron, 0,
 ,[Offset], Black_Grain, 1,
 ,[Offset], bulb1, 2,
 ,[Offset], Bumper_Hamer, 3,
 ,[Offset], Bumper_Sensors, 4,
 ,[Offset], Cabinet, 5,
 ,[Offset], Cabinet_Front, 6,
 ,[Offset], Cabinet_Head, 7,
 ,[Offset], caddy_boy_stars, 8,
 ,[Offset], Clear_Bumper, 9,
 ,[Offset], ClearPlasticPost_01, 10,
 ,[Offset], CRT_Table_backglass, 11,
 ,[Offset], Extra_Metal_Parts, 12,
 ,[Offset], Flipper_Button, 13,
 ,[Offset], Generic_Metal, 14,
 ,[Offset], Habit_1, 15,
 ,[Offset], Habit_2, 16,
 ,[Offset], Inner_Cabinet, 17,
 ,[Offset], large_plastics, 18,
 ,[Offset], LastHabit, 19,
 ,[Offset], metal_outlane, 20,
 ,[Offset], metal_walls, 21,
 ,[Offset], Orange_Plastic_Post, 22,
 ,[Offset], Plastic_CircleTop, 23,
 ,[Offset], PopBumperBody, 24,
 ,[Offset], rubberband_Temp, 25,
 ,[Offset], slingshot_film, 26,
 ,[Offset], Playfield_Upper, 27,
 ,[Offset], Playfield_Lower, 28,
 ,[Offset], underwater_plastics, 29,
 ,[Offset], Metal_Parts, 30,
 ,[Offset], Coin_Slot, 31,
 ,[Offset], Silver Metal Screws_Temp, 32,
 ,[Offset], Rubber Post_Temp, 33,
 ,[Offset], Rubber_Band_Black, 34,
 ,[Offset], BlackMatte_Temp, 35,
 ,[Offset], Flipper, 36,
 ,[Offset], Plunger, 37,
 ,[Offset], Target_White, 38,
 ,[Offset], ClearPlastic, 39,
 ,[Offset], Harley_Spinner, 40,
 ,[Offset], Signs, 41,
 ,[Offset], Metal_Temp, 42,
 ,[Offset], Fish_Sticker, 43,
 ,[Offset], Mid_Curl_Ramp, 44,
 ,[Offset], Mid_Curl_RampStickers, 45,
 ,[Offset], Right_Ramp, 46,
 ,[Offset], Left_Ramp, 47,
 ,[Offset], Creature_Cutout, 48,
 ,[Offset], Creature_Bracket, 49,
 ,[Offset], back, 50,
 ,[Offset], Rails, 51,
 ,[Offset], Rubber_Cover, 52,
 ,[Offset], display_frame_generic, 53,
 ,[Offset], Ramp_BlackPlastic, 54,
 ,[Offset], Bowl_Metal, 55,
RSID_TCREATURE_MOVIE, 3255,,,
 ,[Offset], Creature_00000, 0,
 ,[Offset], Creature_00001, 1,
 ,[Offset], Creature_00002, 2,
 ,[Offset], Creature_00003, 3,
 ,[Offset], Creature_00004, 4,
 ,[Offset], Creature_00005, 5,
 ,[Offset], Creature_00006, 6,
 ,[Offset], Creature_00007, 7,
 ,[Offset], Creature_00008, 8,
 ,[Offset], Creature_00009, 9,
 ,[Offset], Creature_00010, 10,
 ,[Offset], Creature_00011, 11,
 ,[Offset], Creature_00012, 12,
 ,[Offset], Creature_00013, 13,
 ,[Offset], Creature_00014, 14,
 ,[Offset], Creature_00015, 15,
 ,[Offset], Creature_00016, 16,
 ,[Offset], Creature_00017, 17,
 ,[Offset], Creature_00018, 18,
 ,[Offset], Creature_00019, 19,
 ,[Offset], Creature_00020, 20,
 ,[Offset], Creature_00021, 21,
 ,[Offset], Creature_00022, 22,
 ,[Offset], Creature_00023, 23,
 ,[Offset], Creature_00024, 24,
 ,[Offset], Creature_00025, 25,
 ,[Offset], Creature_00026, 26,
 ,[Offset], Creature_00027, 27,
 ,[Offset], Creature_00028, 28,
 ,[Offset], Creature_00029, 29,
 ,[Offset], Creature_00030, 30,
 ,[Offset], Creature_00031, 31,
 ,[Offset], Creature_00032, 32,
 ,[Offset], Creature_00033, 33,
 ,[Offset], Creature_00034, 34,
 ,[Offset], Creature_00035, 35,
 ,[Offset], Creature_00036, 36,
 ,[Offset], Creature_00037, 37,
 ,[Offset], Creature_00038, 38,
 ,[Offset], Creature_00039, 39,
 ,[Offset], Creature_00040, 40,
 ,[Offset], Creature_00041, 41,
 ,[Offset], Creature_00042, 42,
 ,[Offset], Creature_00043, 43,
 ,[Offset], Creature_00044, 44,
 ,[Offset], Creature_00045, 45,
 ,[Offset], Creature_00046, 46,
 ,[Offset], Creature_00047, 47,
 ,[Offset], Creature_00048, 48,
 ,[Offset], Creature_00049, 49,
 ,[Offset], Creature_00050, 50,
 ,[Offset], Creature_00051, 51,
 ,[Offset], Creature_00052, 52,
 ,[Offset], Creature_00053, 53,
 ,[Offset], Creature_00054, 54,
 ,[Offset], Creature_00055, 55,
 ,[Offset], Creature_00056, 56,
 ,[Offset], Creature_00057, 57,
 ,[Offset], Creature_00058, 58,
 ,[Offset], Creature_00059, 59,
 ,[Offset], Creature_00060, 60,
 ,[Offset], Creature_00061, 61,
 ,[Offset], Creature_00062, 62,
 ,[Offset], Creature_00063, 63,
 ,[Offset], Creature_00064, 64,
 ,[Offset], Creature_00065, 65,
 ,[Offset], Creature_00066, 66,
 ,[Offset], Creature_00067, 67,
 ,[Offset], Creature_00068, 68,
 ,[Offset], Creature_00069, 69,
 ,[Offset], Creature_00070, 70,
 ,[Offset], Creature_00071, 71,
 ,[Offset], Creature_00072, 72,
 ,[Offset], Creature_00073, 73,
 ,[Offset], Creature_00074, 74,
 ,[Offset], Creature_00075, 75,
 ,[Offset], Creature_00076, 76,
 ,[Offset], Creature_00077, 77,
 ,[Offset], Creature_00078, 78,
 ,[Offset], Creature_00079, 79,
 ,[Offset], Creature_00080, 80,
 ,[Offset], Creature_00081, 81,
 ,[Offset], Creature_00082, 82,
 ,[Offset], Creature_00083, 83,
 ,[Offset], Creature_00084, 84,
 ,[Offset], Creature_00085, 85,
 ,[Offset], Creature_00086, 86,
 ,[Offset], Creature_00087, 87,
 ,[Offset], Creature_00088, 88,
 ,[Offset], Creature_00089, 89,
 ,[Offset], Creature_00090, 90,
 ,[Offset], Creature_00091, 91,
 ,[Offset], Creature_00092, 92,
 ,[Offset], Creature_00093, 93,
 ,[Offset], Creature_00094, 94,
 ,[Offset], Creature_00095, 95,
 ,[Offset], Creature_00096, 96,
 ,[Offset], Creature_00097, 97,
 ,[Offset], Creature_00098, 98,
 ,[Offset], Creature_00099, 99,
 ,[Offset], Creature_00100, 100,
 ,[Offset], Creature_00101, 101,
 ,[Offset], Creature_00102, 102,
 ,[Offset], Creature_00103, 103,
 ,[Offset], Creature_00104, 104,
 ,[Offset], Creature_00105, 105,
 ,[Offset], Creature_00106, 106,
 ,[Offset], Creature_00107, 107,
 ,[Offset], Creature_00108, 108,
 ,[Offset], Creature_00109, 109,
 ,[Offset], Creature_00110, 110,
 ,[Offset], Creature_00111, 111,
 ,[Offset], Creature_00112, 112,
 ,[Offset], Creature_00113, 113,
 ,[Offset], Creature_00114, 114,
 ,[Offset], Creature_00115, 115,
 ,[Offset], Creature_00116, 116,
 ,[Offset], Creature_00117, 117,
 ,[Offset], Creature_00118, 118,
 ,[Offset], Creature_00119, 119,
 ,[Offset], Creature_00120, 120,
 ,[Offset], Creature_00121, 121,
 ,[Offset], Creature_00122, 122,
 ,[Offset], Creature_00123, 123,
 ,[Offset], Creature_00124, 124,
 ,[Offset], Creature_00125, 125,
 ,[Offset], Creature_00126, 126,
 ,[Offset], Creature_00127, 127,
 ,[Offset], Creature_00128, 128,
 ,[Offset], Creature_00129, 129,
 ,[Offset], Creature_00130, 130,
 ,[Offset], Creature_00131, 131,
 ,[Offset], Creature_00132, 132,
 ,[Offset], Creature_00133, 133,
 ,[Offset], Creature_00134, 134,
 ,[Offset], Creature_00135, 135,
 ,[Offset], Creature_00136, 136,
 ,[Offset], Creature_00137, 137,
 ,[Offset], Creature_00138, 138,
 ,[Offset], Creature_00139, 139,
 ,[Offset], Creature_00140, 140,
 ,[Offset], Creature_00141, 141,
 ,[Offset], Creature_00142, 142,
 ,[Offset], Creature_00143, 143,
 ,[Offset], Creature_00144, 144,
 ,[Offset], Creature_00145, 145,
 ,[Offset], Creature_00146, 146,
 ,[Offset], Creature_00147, 147,
 ,[Offset], Creature_00148, 148,
 ,[Offset], Creature_00149, 149,
 ,[Offset], Creature_00150, 150,
 ,[Offset], Creature_00151, 151,
 ,[Offset], Creature_00152, 152,
 ,[Offset], Creature_00153, 153,
 ,[Offset], Creature_00154, 154,
 ,[Offset], Creature_00155, 155,
 ,[Offset], Creature_00156, 156,
 ,[Offset], Creature_00157, 157,
 ,[Offset], Creature_00158, 158,
 ,[Offset], Creature_00159, 159,
 ,[Offset], Creature_00160, 160,
 ,[Offset], Creature_00161, 161,
 ,[Offset], Creature_00162, 162,
 ,[Offset], Creature_00163, 163,
 ,[Offset], Creature_00164, 164,
 ,[Offset], Creature_00165, 165,
 ,[Offset], Creature_00166, 166,
 ,[Offset], Creature_00167, 167,
 ,[Offset], Creature_00168, 168,
 ,[Offset], Creature_00169, 169,
 ,[Offset], Creature_00170, 170,
 ,[Offset], Creature_00171, 171,
 ,[Offset], Creature_00172, 172,
 ,[Offset], Creature_00173, 173,
 ,[Offset], Creature_00174, 174,
 ,[Offset], Creature_00175, 175,
 ,[Offset], Creature_00176, 176,
 ,[Offset], Creature_00177, 177,
 ,[Offset], Creature_00178, 178,
RSID_TCREATURE_MODELS, 3256,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bowl_Plastic, 1,
 ,[Offset], Bulbs, 2,
 ,[Offset], Cabinet_Backglass, 3,
 ,[Offset], Cabinet_Body, 4,
 ,[Offset], Cabinet_Interior, 5,
 ,[Offset], Cabinet_Metals, 6,
 ,[Offset], Habit_Trails, 7,
 ,[Offset], Left_Ramp, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Ramp, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Mid_Curl_Ramp, 12,
 ,[Offset], Plastic_Pieces, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], Playfield, 15,
 ,[Offset], Pop_Bumpers, 16,
 ,[Offset], Right_Ramp, 17,
 ,[Offset], Rubber_Pieces, 18,
 ,[Offset], Wooden_Rails, 19,
 ,[Offset], Bumper_Metal, 20,
 ,[Offset], Flipper, 21,
 ,[Offset], Left_Slingshot, 22,
 ,[Offset], Plunger, 23,
 ,[Offset], Ramp_Diverter, 24,
 ,[Offset], Right_Slingshot, 25,
 ,[Offset], Target, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Signs, 28,
 ,[Offset], Gate, 29,
 ,[Offset], One_Way_Gate, 30,
 ,[Offset], Switch, 31,
 ,[Offset], light_cutouts, 32,
 ,[Offset], Ramp_Lights, 33,
 ,[Offset], Mid_Curl_Ramp_Wall, 34,
 ,[Offset], Mid_Curl_Ramp_Floor, 35,
 ,[Offset], Creature_Cutout, 36,
 ,[Offset], Creature_Movie, 37,
 ,[Offset], Back_Plate, 38,
 ,[Offset], Plastic_Posts_2, 39,
 ,[Offset], Flashers, 40,
 ,[Offset], Backglass_Display, 41,
 ,[Offset], Left_Slingshot_Extended, 42,
 ,[Offset], Right_Slingshot_Extended, 43,
RSID_TCREATURE_MODELS_LODS, 3257,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bowl_Plastic, 1,
 ,[Offset], Bulbs, 2,
 ,[Offset], Cabinet_Backglass, 3,
 ,[Offset], Cabinet_Body, 4,
 ,[Offset], Cabinet_Interior, 5,
 ,[Offset], Cabinet_Metals, 6,
 ,[Offset], Habit_Trails, 7,
 ,[Offset], Left_Ramp, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Ramp, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Mid_Curl_Ramp, 12,
 ,[Offset], Plastic_Pieces, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], Playfield, 15,
 ,[Offset], Pop_Bumpers, 16,
 ,[Offset], Right_Ramp, 17,
 ,[Offset], Rubber_Pieces, 18,
 ,[Offset], Wooden_Rails, 19,
 ,[Offset], Bumper_Metal, 20,
 ,[Offset], Flipper, 21,
 ,[Offset], Left_Slingshot, 22,
 ,[Offset], Plunger, 23,
 ,[Offset], Ramp_Diverter, 24,
 ,[Offset], Right_Slingshot, 25,
 ,[Offset], Target, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Signs, 28,
 ,[Offset], Gate, 29,
 ,[Offset], One_Way_Gate, 30,
 ,[Offset], Switch, 31,
 ,[Offset], light_cutouts, 32,
 ,[Offset], Ramp_Lights, 33,
 ,[Offset], Mid_Curl_Ramp_Wall, 34,
 ,[Offset], Mid_Curl_Ramp_Floor, 35,
 ,[Offset], Creature_Cutout, 36,
 ,[Offset], Creature_Movie, 37,
 ,[Offset], Back_Plate, 38,
 ,[Offset], Plastic_Posts_2, 39,
 ,[Offset], Flashers, 40,
 ,[Offset], Backglass_Display, 41,
 ,[Offset], Left_Slingshot_Extended, 42,
 ,[Offset], Right_Slingshot_Extended, 43,
RSID_TCREATURE_COLLISION, 3258,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Right_Habit, 1,
 ,[Offset], Ball_Drain, 2,
 ,[Offset], Front_Right_Habit, 3,
 ,[Offset], Left_Flipper_Lane, 4,
 ,[Offset], Left_Ramp, 5,
 ,[Offset], Left_Slingshot, 6,
 ,[Offset], Mid_Curl_Ramp, 7,
 ,[Offset], Playfield, 8,
 ,[Offset], Ramp_Diverter, 9,
 ,[Offset], Right_Flipper_Lane, 10,
 ,[Offset], Right_Ramp, 11,
 ,[Offset], Right_Slingshot, 12,
 ,[Offset], Rubber_A, 13,
 ,[Offset], Rubber_B, 14,
 ,[Offset], Rubber_C, 15,
 ,[Offset], Rubber_D, 16,
 ,[Offset], Rubber_E, 17,
 ,[Offset], Wall_A, 18,
 ,[Offset], Wall_B, 19,
 ,[Offset], Wall_C, 20,
 ,[Offset], Wall_D, 21,
 ,[Offset], Wall_E, 22,
 ,[Offset], Left_Flipper_Back, 23,
 ,[Offset], Left_Flipper_Front, 24,
 ,[Offset], Left_Slingshot_Front, 25,
 ,[Offset], Plunger, 26,
 ,[Offset], Plunger_Back, 27,
 ,[Offset], Ramp_Diverter, 28,
 ,[Offset], Right_Flipper_Back, 29,
 ,[Offset], Right_Flipper_Front, 30,
 ,[Offset], Right_Slingshot_Front, 31,
 ,[Offset], Pop_bumper, 32,
 ,[Offset], Target, 33,
 ,[Offset], Trough, 34,
 ,[Offset], Trough_Trap, 35,
 ,[Offset], Start_Movie_Trap, 36,
 ,[Offset], Bowl, 37,
 ,[Offset], Mid_Habit_Trail, 38,
 ,[Offset], Gate, 39,
 ,[Offset], One_Way_Gate_Back, 40,
 ,[Offset], One_Way_Gate_Front, 41,
 ,[Offset], Bumper_Base, 42,
 ,[Offset], Left_Ramp_Down, 43,
 ,[Offset], Scoop, 44,
 ,[Offset], Bowl_Lower, 45,
RSID_TCREATURE_PLACEMENT, 3259,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TCREATURE_EMUROM, 3260,,,
 ,[Offset], cftbl_l4, 0,
 ,[Offset], cftbl_l4, 1,
 ,[Offset], cftbl_l4, 2,
RSID_TCREATURE_SOUNDS_START, 3261,,,
RSID_TCREATURE_EMU_SOUNDS, 3262,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP1, 1,
 ,[Offset], S0002-LP2, 2,
 ,[Offset], S0003-LP1, 3,
 ,[Offset], S0003-LP2, 4,
 ,[Offset], S0004-LP, 5,
 ,[Offset], S0005-LP1, 6,
 ,[Offset], S0005-LP2, 7,
 ,[Offset], S0006-LP1, 8,
 ,[Offset], S0006-LP2, 9,
 ,[Offset], S0007-LP1, 10,
 ,[Offset], S0007-LP2, 11,
 ,[Offset], S0008-LP1, 12,
 ,[Offset], S0008-LP2, 13,
 ,[Offset], S0009-LP1, 14,
 ,[Offset], S0009-LP2, 15,
 ,[Offset], S000A-LP1, 16,
 ,[Offset], S000A-LP2, 17,
 ,[Offset], S000B-LP1, 18,
 ,[Offset], S000B-LP2, 19,
 ,[Offset], S000C-LP1, 20,
 ,[Offset], S000C-LP2, 21,
 ,[Offset], S000D-LP1, 22,
 ,[Offset], S000D-LP2, 23,
 ,[Offset], S000E_C3, 24,
 ,[Offset], S0030_C2, 25,
 ,[Offset], S0031_C2, 26,
 ,[Offset], S0032_C2, 27,
 ,[Offset], S0033_C2, 28,
 ,[Offset], S0034_C2, 29,
 ,[Offset], S0035_C2, 30,
 ,[Offset], S0036_C2, 31,
 ,[Offset], S0037_C2, 32,
 ,[Offset], S0038_C2, 33,
 ,[Offset], S0039_C2, 34,
 ,[Offset], S003A_C2, 35,
 ,[Offset], S003B_C2, 36,
 ,[Offset], S003C_C2, 37,
 ,[Offset], S003D_C2, 38,
 ,[Offset], S003E_C2, 39,
 ,[Offset], S003F_C2, 40,
 ,[Offset], S0040_C2, 41,
 ,[Offset], S0041_C2, 42,
 ,[Offset], S0042_C2, 43,
 ,[Offset], S0043_C2, 44,
 ,[Offset], S0044_C2, 45,
 ,[Offset], S0045_C2, 46,
 ,[Offset], S0046_C2, 47,
 ,[Offset], S0047_C2, 48,
 ,[Offset], S0048_C2, 49,
 ,[Offset], S0049_C2, 50,
 ,[Offset], S004A_C2, 51,
 ,[Offset], S004B_C2, 52,
 ,[Offset], S004C_C2, 53,
 ,[Offset], S004D_C2, 54,
 ,[Offset], S004E_C2, 55,
 ,[Offset], S004F_C2, 56,
 ,[Offset], S0050_C-1, 57,
 ,[Offset], S0051_C-1, 58,
 ,[Offset], S0052_C4, 59,
 ,[Offset], S0053_C4, 60,
 ,[Offset], S0054_C4, 61,
 ,[Offset], S0055_C4, 62,
 ,[Offset], S0056_C4, 63,
 ,[Offset], S0057_C4, 64,
 ,[Offset], S0058_C4, 65,
 ,[Offset], S0059_C4, 66,
 ,[Offset], S005A_C2, 67,
 ,[Offset], S005B_C2, 68,
 ,[Offset], S005C_C2, 69,
 ,[Offset], S005D_C2, 70,
 ,[Offset], S0080_C4, 71,
 ,[Offset], S0081_C4, 72,
 ,[Offset], S0082_C4, 73,
 ,[Offset], S0083_C4, 74,
 ,[Offset], S0084_C4, 75,
 ,[Offset], S0085_C4, 76,
 ,[Offset], S0086_C4, 77,
 ,[Offset], S0087_C4, 78,
 ,[Offset], S0088_C4, 79,
 ,[Offset], S0089_C4, 80,
 ,[Offset], S008A_C4, 81,
 ,[Offset], S008B_C4, 82,
 ,[Offset], S008C_C4, 83,
 ,[Offset], S008D_C4, 84,
 ,[Offset], S008E_C4, 85,
 ,[Offset], S008F_C4, 86,
 ,[Offset], S0090_C4, 87,
 ,[Offset], S0091_C4, 88,
 ,[Offset], S0092_C4, 89,
 ,[Offset], S0096_C4, 90,
 ,[Offset], S0097_C4, 91,
 ,[Offset], S0098_C4, 92,
 ,[Offset], S0099_C4, 93,
 ,[Offset], S009A_C4, 94,
 ,[Offset], S009B_C4, 95,
 ,[Offset], S009C_C4, 96,
 ,[Offset], S009D_C4, 97,
 ,[Offset], S009E_C4, 98,
 ,[Offset], S009F_C4, 99,
 ,[Offset], S00A0_C4, 100,
 ,[Offset], S00A1_C4, 101,
 ,[Offset], S00A2_C4, 102,
 ,[Offset], S00A3_C4, 103,
 ,[Offset], S00A4_C4, 104,
 ,[Offset], S00A5_C4, 105,
 ,[Offset], S00A6_C4, 106,
 ,[Offset], S00A7_C4, 107,
 ,[Offset], S00A8_C4, 108,
 ,[Offset], S00A9_C4, 109,
 ,[Offset], S00AA_C4, 110,
 ,[Offset], S00AB_C4, 111,
 ,[Offset], S00AC_C4, 112,
 ,[Offset], S00AD_C4, 113,
 ,[Offset], S00AE_C4, 114,
 ,[Offset], S00AF_C4, 115,
 ,[Offset], S00B0_C4, 116,
 ,[Offset], S00B1_C4, 117,
 ,[Offset], S00B2_C4, 118,
 ,[Offset], S00B3_C4, 119,
 ,[Offset], S00B4-LP1, 120,
 ,[Offset], S00B4-LP2, 121,
 ,[Offset], S00B5_C4, 122,
 ,[Offset], S00B6_C4, 123,
 ,[Offset], S00B7_C4, 124,
 ,[Offset], S00B8_C4, 125,
 ,[Offset], S00B9_C4, 126,
 ,[Offset], S00BA_C4, 127,
 ,[Offset], S00BB_C4, 128,
 ,[Offset], S00BC_C4, 129,
 ,[Offset], S00BD_C4, 130,
 ,[Offset], S00BE_C4, 131,
 ,[Offset], S00BF_C4, 132,
 ,[Offset], S00C2_C4, 133,
 ,[Offset], S00C3_C4, 134,
 ,[Offset], S00C4_C4, 135,
 ,[Offset], S00C5_C4, 136,
 ,[Offset], S00C6_C4, 137,
 ,[Offset], S00C7_C4, 138,
 ,[Offset], S00C8_C4, 139,
 ,[Offset], S00C9_C4, 140,
 ,[Offset], S00CA_C4, 141,
 ,[Offset], S00CB_C4, 142,
 ,[Offset], S00CC_C4, 143,
 ,[Offset], S00CD_C4, 144,
 ,[Offset], S00CE_C4, 145,
 ,[Offset], S00CF_C4, 146,
 ,[Offset], S00D0_C2, 147,
 ,[Offset], S00D1_C2, 148,
 ,[Offset], S00D2_C2, 149,
 ,[Offset], S00D3_C2, 150,
 ,[Offset], S00D4_C2, 151,
 ,[Offset], S00D5_C2, 152,
 ,[Offset], S00D6_C2, 153,
 ,[Offset], S00D7_C2, 154,
 ,[Offset], S00D8_C2, 155,
 ,[Offset], S00D9_C2, 156,
 ,[Offset], S00DA_C2, 157,
 ,[Offset], S00DB_C2, 158,
 ,[Offset], S00DC_C2, 159,
 ,[Offset], S00DD_C2, 160,
 ,[Offset], S00DE_C2, 161,
 ,[Offset], S00DF_C4, 162,
 ,[Offset], S00E0-LP, 163,
 ,[Offset], S00E1_C4, 164,
 ,[Offset], S0100_C2, 165,
 ,[Offset], S0101_C2, 166,
 ,[Offset], S0102_C2, 167,
 ,[Offset], S0103_C2, 168,
 ,[Offset], S0104_C2, 169,
 ,[Offset], S0105_C2, 170,
 ,[Offset], S0106_C2, 171,
 ,[Offset], S0107_C2, 172,
 ,[Offset], S0108_C2, 173,
 ,[Offset], S0109_C2, 174,
 ,[Offset], S010A_C2, 175,
 ,[Offset], S010B_C2, 176,
 ,[Offset], S010C_C2, 177,
 ,[Offset], S010D_C2, 178,
 ,[Offset], S010E_C2, 179,
 ,[Offset], S010F_C2, 180,
 ,[Offset], S0110_C2, 181,
 ,[Offset], S0111_C2, 182,
 ,[Offset], S0112_C2, 183,
 ,[Offset], S0113_C2, 184,
 ,[Offset], S0114_C2, 185,
 ,[Offset], S0115_C2, 186,
 ,[Offset], S0116_C2, 187,
 ,[Offset], S0117_C2, 188,
 ,[Offset], S0118_C2, 189,
 ,[Offset], S0119_C2, 190,
 ,[Offset], S011A_C2, 191,
 ,[Offset], S011B_C2, 192,
 ,[Offset], S011C_C2, 193,
 ,[Offset], S011D_C2, 194,
 ,[Offset], S011E_C2, 195,
 ,[Offset], S011F_C2, 196,
 ,[Offset], S0120_C2, 197,
 ,[Offset], S0121_C2, 198,
 ,[Offset], S0122_C2, 199,
 ,[Offset], S0123_C2, 200,
 ,[Offset], S0124_C2, 201,
 ,[Offset], S0125_C2, 202,
 ,[Offset], S0126_C2, 203,
 ,[Offset], S0127_C2, 204,
 ,[Offset], S0128_C2, 205,
 ,[Offset], S0129_C2, 206,
 ,[Offset], S012A_C2, 207,
 ,[Offset], S012B_C2, 208,
 ,[Offset], S012C_C2, 209,
 ,[Offset], S012D_C2, 210,
 ,[Offset], S012E_C2, 211,
 ,[Offset], S012F_C2, 212,
 ,[Offset], S0130_C2, 213,
 ,[Offset], S0131_C2, 214,
 ,[Offset], S0132_C2, 215,
 ,[Offset], S0133_C2, 216,
 ,[Offset], S0134_C2, 217,
 ,[Offset], S0135_C2, 218,
 ,[Offset], S0136_C2, 219,
 ,[Offset], S0137_C2, 220,
 ,[Offset], S0138_C2, 221,
 ,[Offset], S0139_C2, 222,
 ,[Offset], S013A_C2, 223,
 ,[Offset], S013B_C2, 224,
 ,[Offset], S013C_C2, 225,
 ,[Offset], S013D_C2, 226,
 ,[Offset], S013E_C2, 227,
 ,[Offset], S013F_C2, 228,
 ,[Offset], S0140_C2, 229,
 ,[Offset], S0141_C2, 230,
 ,[Offset], S0142_C2, 231,
 ,[Offset], S0143_C2, 232,
 ,[Offset], S0144_C2, 233,
 ,[Offset], S0145_C2, 234,
 ,[Offset], S0146_C2, 235,
 ,[Offset], S0147_C2, 236,
 ,[Offset], S0148_C2, 237,
 ,[Offset], S0149_C2, 238,
 ,[Offset], S014A_C2, 239,
 ,[Offset], S014B_C2, 240,
 ,[Offset], S014C_C2, 241,
 ,[Offset], S014D_C2, 242,
 ,[Offset], S014E_C2, 243,
 ,[Offset], S014F_C2, 244,
 ,[Offset], S0150_C2, 245,
 ,[Offset], S0151_C2, 246,
 ,[Offset], S0152_C2, 247,
 ,[Offset], S0153_C2, 248,
 ,[Offset], S0154_C2, 249,
 ,[Offset], S0155_C2, 250,
 ,[Offset], S0156_C2, 251,
 ,[Offset], S0157_C2, 252,
 ,[Offset], S0158_C2, 253,
 ,[Offset], S0159_C2, 254,
 ,[Offset], S015A_C2, 255,
 ,[Offset], S015B_C2, 256,
 ,[Offset], S015C_C2, 257,
 ,[Offset], S0E02_C3, 258,
 ,[Offset], S0E03_C3, 259,
 ,[Offset], S0E04_C3, 260,
 ,[Offset], S0E05_C3, 261,
 ,[Offset], S0E06_C3, 262,
 ,[Offset], S0E08_C3, 263,
 ,[Offset], S0E0A_C3, 264,
 ,[Offset], S0E0B_C3, 265,
 ,[Offset], S0E0C_C3, 266,
 ,[Offset], S0E0D_C3, 267,
 ,[Offset], S0E0E_C3, 268,
RSID_TCREATURE_MECH_SOUNDS, 3263,,,
 ,[Offset], back_jump, 0,
 ,[Offset], front_jump, 1,
 ,[Offset], Ramp_down, 2,
RSID_TCREATURE_SOUNDS_END, 3264,,,
RSID_TCREATURE_SAMPLES, 3265,,,
RSID_TCREATURE_VERSION, 3266,,,
RSID_TCREATURE_END, 3267,,,
