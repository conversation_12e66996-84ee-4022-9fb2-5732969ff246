RSID_TBRIDE_START, 3200,,,
 ,[Offset], BRIDE_FLYER_1920, 0,
 ,[Offset], <PERSON>\PBTBride, 1,
 ,[Offset], <PERSON>\InstructionsENG, 2,
 ,[Offset], <PERSON>\InstructionsFR, 3,
 ,[Offset], <PERSON>\InstructionsITAL, 4,
 ,[Offset], <PERSON>\InstructionsGERM, 5,
 ,[Offset], <PERSON>\InstructionsSPAN, 6,
 ,[Offset], <PERSON>\InstructionsPORT, 7,
 ,[Offset], <PERSON>\InstructionsDUTCH, 8,
 ,[Offset], tables\BOP_BG, 9,
RSID_TBRIDE_LIGHTS, 3201,,,
RSID_TBRIDE_CAMERAS, 3202,,,
RSID_TBRIDE_LAMP_TEXTURES, 3203,,,
 ,[Offset], bigheartlight_off, 0,
 ,[Offset], bigheartlight_on, 1,
 ,[Offset], bulb_off, 2,
 ,[Offset], bulb_on, 3,
 ,[Offset], L011_off, 4,
 ,[Offset], L011_on, 5,
 ,[Offset], L012_off, 6,
 ,[Offset], L012_on, 7,
 ,[Offset], L013_off, 8,
 ,[Offset], L013_on, 9,
 ,[Offset], L014_off, 10,
 ,[Offset], L014_on, 11,
 ,[Offset], L015_off, 12,
 ,[Offset], L015_on, 13,
 ,[Offset], L016_off, 14,
 ,[Offset], L016_on, 15,
 ,[Offset], L017_off, 16,
 ,[Offset], L017_on, 17,
 ,[Offset], L018_off, 18,
 ,[Offset], L018_on, 19,
 ,[Offset], L021_off, 20,
 ,[Offset], L021_on, 21,
 ,[Offset], L022_off, 22,
 ,[Offset], L022_on, 23,
 ,[Offset], L023_off, 24,
 ,[Offset], L023_on, 25,
 ,[Offset], L024_off, 26,
 ,[Offset], L024_on, 27,
 ,[Offset], L025_off, 28,
 ,[Offset], L025_on, 29,
 ,[Offset], L026_off, 30,
 ,[Offset], L026_on, 31,
 ,[Offset], L027_off, 32,
 ,[Offset], L027_on, 33,
 ,[Offset], L028_off, 34,
 ,[Offset], L028_on, 35,
 ,[Offset], L031_off, 36,
 ,[Offset], L031_on, 37,
 ,[Offset], L032_off, 38,
 ,[Offset], L032_on, 39,
 ,[Offset], L033_off, 40,
 ,[Offset], L033_on, 41,
 ,[Offset], L034_off, 42,
 ,[Offset], L034_on, 43,
 ,[Offset], L035_off, 44,
 ,[Offset], L035_on, 45,
 ,[Offset], L036_off, 46,
 ,[Offset], L036_on, 47,
 ,[Offset], L037_off, 48,
 ,[Offset], L037_on, 49,
 ,[Offset], L038_off, 50,
 ,[Offset], L038_on, 51,
 ,[Offset], L041_off, 52,
 ,[Offset], L041_on, 53,
 ,[Offset], L042_off, 54,
 ,[Offset], L042_on, 55,
 ,[Offset], L043_off, 56,
 ,[Offset], L043_on, 57,
 ,[Offset], L044_off, 58,
 ,[Offset], L044_on, 59,
 ,[Offset], L045_off, 60,
 ,[Offset], L045_on, 61,
 ,[Offset], L051_off, 62,
 ,[Offset], L051_on, 63,
 ,[Offset], L052_off, 64,
 ,[Offset], L052_on, 65,
 ,[Offset], L053_off, 66,
 ,[Offset], L053_on, 67,
 ,[Offset], L054_off, 68,
 ,[Offset], L054_on, 69,
 ,[Offset], L055_off, 70,
 ,[Offset], L055_on, 71,
 ,[Offset], L056_off, 72,
 ,[Offset], L056_on, 73,
 ,[Offset], L057_off, 74,
 ,[Offset], L057_on, 75,
 ,[Offset], L058_off, 76,
 ,[Offset], L058_on, 77,
 ,[Offset], L061_off, 78,
 ,[Offset], L061_on, 79,
 ,[Offset], L062_off, 80,
 ,[Offset], L062_on, 81,
 ,[Offset], L063_off, 82,
 ,[Offset], L063_on, 83,
 ,[Offset], L064_off, 84,
 ,[Offset], L064_on, 85,
 ,[Offset], L065_off, 86,
 ,[Offset], L065_on, 87,
 ,[Offset], L066_off, 88,
 ,[Offset], L066_on, 89,
 ,[Offset], L067_off, 90,
 ,[Offset], L067_on, 91,
 ,[Offset], L068_off, 92,
 ,[Offset], L068_on, 93,
 ,[Offset], L086_off, 94,
 ,[Offset], L086_on, 95,
 ,[Offset], L087_off, 96,
 ,[Offset], L087_on, 97,
 ,[Offset], L088_off, 98,
 ,[Offset], L088_on, 99,
 ,[Offset], Helmet_01_Off, 100,
 ,[Offset], Helmet_01_On, 101,
 ,[Offset], Helmet_02_Off, 102,
 ,[Offset], Helmet_02_On, 103,
 ,[Offset], Helmet_03_Off, 104,
 ,[Offset], Helmet_03_On, 105,
 ,[Offset], Helmet_04_Off, 106,
 ,[Offset], Helmet_04_On, 107,
 ,[Offset], Helmet_05_Off, 108,
 ,[Offset], Helmet_05_On, 109,
 ,[Offset], Helmet_06_Off, 110,
 ,[Offset], Helmet_06_On, 111,
 ,[Offset], Helmet_07_Off, 112,
 ,[Offset], Helmet_07_On, 113,
 ,[Offset], Helmet_08_Off, 114,
 ,[Offset], Helmet_08_On, 115,
 ,[Offset], Helmet_09_Off, 116,
 ,[Offset], Helmet_09_On, 117,
 ,[Offset], Helmet_10_Off, 118,
 ,[Offset], Helmet_10_On, 119,
 ,[Offset], Helmet_11_Off, 120,
 ,[Offset], Helmet_11_On, 121,
 ,[Offset], Helmet_12_Off, 122,
 ,[Offset], Helmet_12_On, 123,
 ,[Offset], Helmet_13_Off, 124,
 ,[Offset], Helmet_13_On, 125,
 ,[Offset], Helmet_14_Off, 126,
 ,[Offset], Helmet_14_On, 127,
 ,[Offset], Helmet_15_Off, 128,
 ,[Offset], Helmet_15_On, 129,
 ,[Offset], Helmet_16_Off, 130,
 ,[Offset], Helmet_16_On, 131,
 ,[Offset], F_21_Off, 132,
 ,[Offset], F_21_On, 133,
 ,[Offset], F_22_Off, 134,
 ,[Offset], F_22_On, 135,
 ,[Offset], F_23_Off, 136,
 ,[Offset], F_23_On, 137,
 ,[Offset], F_24_Off, 138,
 ,[Offset], F_24_On, 139,
 ,[Offset], L037B_off, 140,
 ,[Offset], L037B_on, 141,
 ,[Offset], L046_off, 142,
 ,[Offset], L046_on, 143,
 ,[Offset], L047_off, 144,
 ,[Offset], L047_on, 145,
 ,[Offset], L048_off, 146,
 ,[Offset], L048_on, 147,
 ,[Offset], Face_Mouth_Eye_Left_Off, 148,
 ,[Offset], Face_Mouth_Eye_Left_On, 149,
 ,[Offset], Face_Mouth_Eye_Right_Off, 150,
 ,[Offset], Face_Mouth_Eye_Right_On, 151,
 ,[Offset], Face_Mouth_Mouth_Off, 152,
 ,[Offset], Face_Mouth_Mouth_On, 153,
 ,[Offset], Face_Eyes_Eye_Left_Off, 154,
 ,[Offset], Face_Eyes_Eye_Left_On, 155,
 ,[Offset], Face_Eyes_Eye_Right_Off, 156,
 ,[Offset], Face_Eyes_Eye_Right_On, 157,
 ,[Offset], Face_Eyes_Mouth_Off, 158,
 ,[Offset], Face_Eyes_Mouth_On, 159,
 ,[Offset], Face_Robot_Eye_Left_Off, 160,
 ,[Offset], Face_Robot_Eye_Left_On, 161,
 ,[Offset], Face_Robot_Eye_Right_Off, 162,
 ,[Offset], Face_Robot_Eye_Right_On, 163,
 ,[Offset], Face_Robot_Mouth_Off, 164,
 ,[Offset], Face_Robot_Mouth_On, 165,
 ,[Offset], Face_Human_Eye_Left_Off, 166,
 ,[Offset], Face_Human_Eye_Left_On, 167,
 ,[Offset], Face_Human_Eye_Right_Off, 168,
 ,[Offset], Face_Human_Eye_Right_On, 169,
 ,[Offset], Face_Human_Mouth_Off, 170,
 ,[Offset], Face_Human_Mouth_On, 171,
 ,[Offset], F_019_Off, 172,
 ,[Offset], F_019_On, 173,
 ,[Offset], Shuttle_Light_Off, 174,
 ,[Offset], Shuttle_Light_On, 175,
 ,[Offset], Habbi_Texture_Off, 176,
 ,[Offset], Habbi_Texture_On, 177,
 ,[Offset], Metal_Plates_Wheels_Off, 178,
 ,[Offset], Metal_Plates_Wheels_On, 179,
 ,[Offset], Rubber_Wheels_Off, 180,
 ,[Offset], Rubber_Wheels_On, 181,
 ,[Offset], Shuttle_Light_Floor_Off, 182,
 ,[Offset], Shuttle_Light_Floor_On, 183,
 ,[Offset], Black_Grain_Off, 184,
 ,[Offset], Black_Grain_On, 185,
RSID_TBRIDE_TEXTURES, 3204,,,
 ,[Offset], Apron, 0,
 ,[Offset], Box, 1,
 ,[Offset], ClearPlastic, 2,
 ,[Offset], Coin_Slot, 3,
 ,[Offset], Metal_Parts, 4,
 ,[Offset], Plastic_Robot, 5,
 ,[Offset], Plastics_Bottom, 6,
 ,[Offset], Plastics_Top, 7,
 ,[Offset], Rubber Post_Temp, 8,
 ,[Offset], Metal_Temp, 9,
 ,[Offset], Silver Metal Screws_Temp, 10,
 ,[Offset], Playfield_Bottom, 11,
 ,[Offset], Playfield_Top, 12,
 ,[Offset], Screen_Speakers, 13,
 ,[Offset], Spinner, 14,
 ,[Offset], blackrubberband, 15,
 ,[Offset], BlueandRedPost, 16,
 ,[Offset], Flipper, 17,
 ,[Offset], Pop_Bumpers, 18,
 ,[Offset], Targets, 19,
 ,[Offset], whiterubberband, 20,
 ,[Offset], Habbi_Texture, 21,
 ,[Offset], Habbi_Texture_s, 22,
 ,[Offset], Metals, 23,
 ,[Offset], PinBot_Helmet, 24,
 ,[Offset], Rails, 25,
 ,[Offset], Plastic_Ramp, 26,
 ,[Offset], TransparentRedPlastic_Temp, 27,
 ,[Offset], PinBotValues, 28,
 ,[Offset], BallLockedWhenLit, 29,
 ,[Offset], Bumper_Spine, 30,
 ,[Offset], Bumper_Head, 31,
 ,[Offset], Plastic_Thing, 32,
 ,[Offset], Rubber_Cover_Black, 33,
 ,[Offset], Rubber_Cover, 34,
 ,[Offset], Extra_Metal_Parts, 35,
 ,[Offset], Black_Grain, 36,
 ,[Offset], Sticker_Parts, 37,
 ,[Offset], Metal_Trail_Walls, 38,
 ,[Offset], Metal_Plates_Wheels, 39,
 ,[Offset], plunger, 40,
 ,[Offset], swing_brackets, 41,
 ,[Offset], Thick_swing_brackets, 42,
 ,[Offset], Rubber_Wheels, 43,
 ,[Offset], Blue_Plastic, 44,
 ,[Offset], Flipper_Button, 45,
 ,[Offset], PinBotKey, 46,
 ,[Offset], Bumper_Hamer, 47,
 ,[Offset], Bumper_Sensors, 48,
 ,[Offset], PinBotWires, 49,
 ,[Offset], Skillshot_Bracket, 50,
 ,[Offset], bulb1, 51,
 ,[Offset], Skillshot_25k_Off, 52,
 ,[Offset], Skillshot_25k_On, 53,
 ,[Offset], Skillshot_200k_Off, 54,
 ,[Offset], Skillshot_200k_On, 55,
 ,[Offset], Skillshot_100k_Off, 56,
 ,[Offset], Skillshot_100k_On, 57,
 ,[Offset], Skillshot_75k_Off, 58,
 ,[Offset], Skillshot_75k_On, 59,
 ,[Offset], Skillshot_50k_Off, 60,
 ,[Offset], Skillshot_50k_On, 61,
 ,[Offset], Box_Face_eyes, 62,
 ,[Offset], Box_Face_Human, 63,
 ,[Offset], Box_Face_Mouth, 64,
 ,[Offset], Box_Face_Robot, 65,
RSID_TBRIDE_MODELS, 3205,,,
 ,[Offset], Playfield, 0,
 ,[Offset], Cabinet, 1,
 ,[Offset], Plastic_Ramps, 2,
 ,[Offset], Plastics, 3,
 ,[Offset], Apron, 4,
 ,[Offset], Rubber_Posts, 5,
 ,[Offset], Metal_Pieces, 6,
 ,[Offset], Habbi_Trails, 7,
 ,[Offset], Clear_Plastics, 8,
 ,[Offset], Flashers, 9,
 ,[Offset], Flipper, 10,
 ,[Offset], Table_lights, 11,
 ,[Offset], OneWaySpinner, 12,
 ,[Offset], Pinbot_Helmet, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], PopBumper, 15,
 ,[Offset], PopBumperMetal, 16,
 ,[Offset], Spinner, 17,
 ,[Offset], Target, 18,
 ,[Offset], Slingshot_Left, 19,
 ,[Offset], Slingshot_Right, 20,
 ,[Offset], Bride_Head, 21,
 ,[Offset], Slingshot_Left_Extend, 22,
 ,[Offset], Slingshot_Right_Extend, 23,
 ,[Offset], StopPost, 24,
 ,[Offset], TwoWaySpinner, 25,
 ,[Offset], TwoWaySpinner2, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Bride_Head_Guards, 28,
 ,[Offset], table_metal_trim, 29,
 ,[Offset], cabinet_interior, 30,
 ,[Offset], cabinet_interior, 31,
 ,[Offset], Plunger, 32,
 ,[Offset], Mini_Playfield_Left_Switch, 33,
 ,[Offset], Light_Bulbs, 34,
 ,[Offset], Flashers_Helmet, 35,
 ,[Offset], Flashers_Helmet, 36,
 ,[Offset], TwoWaySpinner3, 37,
 ,[Offset], Heart_Flasher, 38,
RSID_TBRIDE_MODELS_LODS, 3206,,,
 ,[Offset], Playfield, 0,
 ,[Offset], Cabinet, 1,
 ,[Offset], Plastic_Ramps, 2,
 ,[Offset], Plastics, 3,
 ,[Offset], Apron, 4,
 ,[Offset], Rubber_Posts, 5,
 ,[Offset], Metal_Pieces, 6,
 ,[Offset], Habbi_Trails, 7,
 ,[Offset], Clear_Plastics, 8,
 ,[Offset], Flashers, 9,
 ,[Offset], Flipper, 10,
 ,[Offset], Table_lights, 11,
 ,[Offset], OneWaySpinner, 12,
 ,[Offset], Pinbot_Helmet, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], PopBumper, 15,
 ,[Offset], PopBumperMetal, 16,
 ,[Offset], Spinner, 17,
 ,[Offset], Target, 18,
 ,[Offset], Slingshot_Left, 19,
 ,[Offset], Slingshot_Right, 20,
 ,[Offset], Bride_Head, 21,
 ,[Offset], Slingshot_Left_Extend, 22,
 ,[Offset], Slingshot_Right_Extend, 23,
 ,[Offset], StopPost, 24,
 ,[Offset], TwoWaySpinner, 25,
 ,[Offset], TwoWaySpinner2, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Bride_Head_Guards, 28,
 ,[Offset], table_metal_trim, 29,
 ,[Offset], cabinet_interior, 30,
 ,[Offset], Lights, 31,
 ,[Offset], Plunger, 32,
 ,[Offset], Mini_Playfield_Left_Switch, 33,
 ,[Offset], Light_Bulbs, 34,
 ,[Offset], Flashers_Helmet, 35,
 ,[Offset], Flashers_Helmet, 36,
 ,[Offset], TwoWaySpinner3, 37,
 ,[Offset], Heart_Flasher, 38,
RSID_TBRIDE_COLLISION, 3207,,,
 ,[Offset], Playfield_Col, 0,
 ,[Offset], Apron, 1,
 ,[Offset], Back_Stop, 2,
 ,[Offset], Left_Slingshot_Switch, 3,
 ,[Offset], Right_Slingshot_Switch, 4,
 ,[Offset], Right_Slingshot_Switch, 5,
 ,[Offset], Left_Top_Flipper, 6,
 ,[Offset], Left_Bottom_Flipper, 7,
 ,[Offset], Right_Top_Flipper, 8,
 ,[Offset], Right_Bottom_Flipper, 9,
 ,[Offset], Head_Habbi_Trail, 10,
 ,[Offset], Stopper_Post, 11,
 ,[Offset], Bride_RobotHumanSide, 12,
 ,[Offset], Mouth, 13,
 ,[Offset], Left_Eye, 14,
 ,[Offset], Right_Eye, 15,
 ,[Offset], Heart_Gate, 16,
 ,[Offset], Heart_Ramp, 17,
 ,[Offset], Spinner, 18,
 ,[Offset], One_Way_Gate, 19,
 ,[Offset], Loops, 20,
 ,[Offset], Power_Stop, 21,
 ,[Offset], Pop_Bumper, 22,
 ,[Offset], Target, 23,
 ,[Offset], Rubber_Posts, 24,
 ,[Offset], Shuttle_Ramp, 25,
 ,[Offset], Shuttle_Ramp_Gate, 26,
 ,[Offset], Skill_Shot_Rubbers, 27,
 ,[Offset], Skill_Shot_Trail, 28,
 ,[Offset], Mini_Playfield, 29,
 ,[Offset], Mini_Playfield_Gate_L, 30,
 ,[Offset], Bride_MouthSide, 31,
 ,[Offset], Bride_EyeSide, 32,
 ,[Offset], One_Way_Gate_Back, 33,
 ,[Offset], Skill_Shot_Kicker, 34,
 ,[Offset], Jets_Slingshot, 35,
 ,[Offset], LeftFlipperLane, 36,
 ,[Offset], LeftFlipperLaneUpper, 37,
 ,[Offset], LeftSlingshotLower, 38,
 ,[Offset], LeftSlingshotUpper, 39,
 ,[Offset], RightFlipperLaneCol, 40,
 ,[Offset], RightFlipperLaneUpper, 41,
 ,[Offset], RightFlipperLaneUpperPost, 42,
 ,[Offset], RightSlingshotLower, 43,
 ,[Offset], RightSlingshotUpper, 44,
 ,[Offset], Shuttle_Ramp_Exit_Front, 45,
 ,[Offset], Shuttle_Ramp_Exit_Back, 46,
 ,[Offset], Mini_Playfield_EnterGate_Front, 47,
 ,[Offset], Mini_Playfield_EnterGate_Back, 48,
 ,[Offset], Mini_Playfield_Left_Switch, 49,
 ,[Offset], Mini_Playfield_Right_Switch, 50,
 ,[Offset], Plunger, 51,
 ,[Offset], powerstop_trap, 52,
 ,[Offset], Walls, 53,
 ,[Offset], Head_Habbi_Trail, 54,
 ,[Offset], Heart_Ramp, 55,
 ,[Offset], Head_Gate, 56,
 ,[Offset], LeftEye_Diverter, 57,
 ,[Offset], RightEye_Diverter, 58,
 ,[Offset], Plunger_Stop, 59,
 ,[Offset], Head_Habbi_Trail_2, 60,
 ,[Offset], Rubber_Posts_B, 61,
 ,[Offset], Rubber_Posts_C, 62,
 ,[Offset], Rubber_Posts_D, 63,
RSID_TBRIDE_PLACEMENT, 3208,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TBRIDE_EMUROM, 3209,,,
 ,[Offset], bop_l7, 0,
 ,[Offset], bop_l7, 1,
 ,[Offset], bop_l7, 2,
 ,[Offset], bop_l7_1P, 3,
 ,[Offset], bop_l7, 4,
 ,[Offset], bop_l6, 5,
 ,[Offset], bop_l6, 6,
RSID_TABLE_BOP_SOUNDS_START, 3210,,,
RSID_TABLE_BOP_EMU_SOUNDS, 3211,,,
 ,[Offset], S0001_C3_LP1, 0,
 ,[Offset], S0001_C3_LP2, 1,
 ,[Offset], S0002_C3_LP1, 2,
 ,[Offset], S0002_C3_LP2, 3,
 ,[Offset], S0003_C3, 4,
 ,[Offset], S0004_C3_LP1, 5,
 ,[Offset], S0004_C3_LP2, 6,
 ,[Offset], S0005_C3_LP1, 7,
 ,[Offset], S0005_C3_LP2, 8,
 ,[Offset], S0006_C3_LP1, 9,
 ,[Offset], S0006_C3_LP2, 10,
 ,[Offset], S0007_C3, 11,
 ,[Offset], S0008_C3_LP1, 12,
 ,[Offset], S0008_C3_LP2, 13,
 ,[Offset], S0009_C3, 14,
 ,[Offset], S000A_C3_LP1, 15,
 ,[Offset], S000A_C3_LP2, 16,
 ,[Offset], S000B_C3_LP1, 17,
 ,[Offset], S000B_C3_LP2, 18,
 ,[Offset], S000C_C3_LP1, 19,
 ,[Offset], S000C_C3_LP2, 20,
 ,[Offset], S000D_C3_LP1, 21,
 ,[Offset], S000D_C3_LP2, 22,
 ,[Offset], S0041_C3_LP1, 23,
 ,[Offset], S0041_C3_LP2, 24,
 ,[Offset], S0042_C3_LP1, 25,
 ,[Offset], S0042_C3_LP2, 26,
 ,[Offset], S0043_C3, 27,
 ,[Offset], S0044_C3, 28,
 ,[Offset], S0045_C3, 29,
 ,[Offset], S0046_C3_LP1, 30,
 ,[Offset], S0046_C3_LP2, 31,
 ,[Offset], S0050_C4, 32,
 ,[Offset], S0051_C4, 33,
 ,[Offset], S0052_C4, 34,
 ,[Offset], S0053_C4, 35,
 ,[Offset], S0054_C4, 36,
 ,[Offset], S0055_C4, 37,
 ,[Offset], S0056_C4, 38,
 ,[Offset], S0057_C4, 39,
 ,[Offset], S0058_C4, 40,
 ,[Offset], S0059_C4, 41,
 ,[Offset], S0080_C4, 42,
 ,[Offset], S0081_C4, 43,
 ,[Offset], S0082_C4, 44,
 ,[Offset], S0083_C4, 45,
 ,[Offset], S0084_C4, 46,
 ,[Offset], S0085_C4, 47,
 ,[Offset], S0086_C4, 48,
 ,[Offset], S0087_C4, 49,
 ,[Offset], S0088_C4, 50,
 ,[Offset], S0089_C4, 51,
 ,[Offset], S008A_C4, 52,
 ,[Offset], S008B_C4, 53,
 ,[Offset], S008C_C4, 54,
 ,[Offset], S008D_C4, 55,
 ,[Offset], S008E_C4, 56,
 ,[Offset], S008F_C4, 57,
 ,[Offset], S0090_C4, 58,
 ,[Offset], S0091_C4, 59,
 ,[Offset], S0092_C4, 60,
 ,[Offset], S0093_C2, 61,
 ,[Offset], S0094_C4, 62,
 ,[Offset], S0095_C4, 63,
 ,[Offset], S0096_C4, 64,
 ,[Offset], S0097_C4, 65,
 ,[Offset], S0098_C4, 66,
 ,[Offset], S0099_C4, 67,
 ,[Offset], S009A_C4, 68,
 ,[Offset], S009B_C4, 69,
 ,[Offset], S009C_C4, 70,
 ,[Offset], S009D_C4, 71,
 ,[Offset], S009E_C4, 72,
 ,[Offset], S009F_C4, 73,
 ,[Offset], S00A0_C4, 74,
 ,[Offset], S00A1_C4, 75,
 ,[Offset], S00A2_C4, 76,
 ,[Offset], S00A3_C4, 77,
 ,[Offset], S00A4_C4, 78,
 ,[Offset], S00A5_C4, 79,
 ,[Offset], S00A6_C4, 80,
 ,[Offset], S00A7_C4, 81,
 ,[Offset], S00A8_C4, 82,
 ,[Offset], S00A9_C4, 83,
 ,[Offset], S00AA_C4, 84,
 ,[Offset], S00AB_C4, 85,
 ,[Offset], S00AC_C4, 86,
 ,[Offset], S00AF_C4, 87,
 ,[Offset], S00B0_C4, 88,
 ,[Offset], S00B1_C4, 89,
 ,[Offset], S00B2_C4, 90,
 ,[Offset], S00B3_C4, 91,
 ,[Offset], S00B4_C4, 92,
 ,[Offset], S00B5_C4, 93,
 ,[Offset], S00B6_C4, 94,
 ,[Offset], S00B7_C2, 95,
 ,[Offset], S00B8_C4, 96,
 ,[Offset], S00B9_C4, 97,
 ,[Offset], S00BA_C4, 98,
 ,[Offset], S00BB_C4, 99,
 ,[Offset], S00BC_C4, 100,
 ,[Offset], S00BD_C4, 101,
 ,[Offset], S00BE_C4, 102,
 ,[Offset], S00BF_C4, 103,
 ,[Offset], S00C0_C4, 104,
 ,[Offset], S00C1_C4, 105,
 ,[Offset], S00C2_C4, 106,
 ,[Offset], S00C3_C4, 107,
 ,[Offset], S00C4_C4, 108,
 ,[Offset], S00C5_C4, 109,
 ,[Offset], S00C6_C4, 110,
 ,[Offset], S00C7_C4, 111,
 ,[Offset], S00C8_C4, 112,
 ,[Offset], S00C9_C4, 113,
 ,[Offset], S00CA_C4, 114,
 ,[Offset], S00CB_C4, 115,
 ,[Offset], S00CC_C4, 116,
 ,[Offset], S00CD_C4, 117,
 ,[Offset], S00CF_C4, 118,
 ,[Offset], S00D0_C4, 119,
 ,[Offset], S00D1_C4, 120,
 ,[Offset], S00D2_C4, 121,
 ,[Offset], S00D3_C4, 122,
 ,[Offset], S00D4_C4, 123,
 ,[Offset], S00D5_C4, 124,
 ,[Offset], S00D6_C2, 125,
 ,[Offset], S00D7_C2, 126,
 ,[Offset], S00DA_C4, 127,
 ,[Offset], S00DB_C4, 128,
 ,[Offset], S00DC_C4, 129,
 ,[Offset], S00DE_C4, 130,
 ,[Offset], S00DF_C4, 131,
 ,[Offset], S00E0_C4, 132,
 ,[Offset], S00E1_C4, 133,
 ,[Offset], S00E2_C4, 134,
 ,[Offset], S00E3_C4, 135,
 ,[Offset], S00E4_C4, 136,
 ,[Offset], S00E5_C4, 137,
 ,[Offset], S00E6_C4, 138,
 ,[Offset], S00E7_C4, 139,
 ,[Offset], S00E8_C4, 140,
 ,[Offset], S00E9_C4, 141,
 ,[Offset], S00EA_C4, 142,
 ,[Offset], S00EC_C3_LP1, 143,
 ,[Offset], S00EC_C3_LP2, 144,
 ,[Offset], S00ED_C4, 145,
 ,[Offset], S00EE_C4, 146,
 ,[Offset], S00EF_C4, 147,
 ,[Offset], S00F0_C2, 148,
 ,[Offset], S00F1_C2, 149,
 ,[Offset], S00F2_C2, 150,
 ,[Offset], S0100_C2, 151,
 ,[Offset], S0101_C2, 152,
 ,[Offset], S0102_C2, 153,
 ,[Offset], S0103_C2, 154,
 ,[Offset], S0104_C2, 155,
 ,[Offset], S0105_C2, 156,
 ,[Offset], S0106_C2, 157,
 ,[Offset], S0107_C2, 158,
 ,[Offset], S0108_C2, 159,
 ,[Offset], S0109_C2, 160,
 ,[Offset], S010A_C2, 161,
 ,[Offset], S010B_C2, 162,
 ,[Offset], S010C_C2, 163,
 ,[Offset], S010D_C2, 164,
 ,[Offset], S010E_C2, 165,
 ,[Offset], S010F_C2, 166,
 ,[Offset], S0110_C2, 167,
 ,[Offset], S0111_C2, 168,
 ,[Offset], S0112_C2, 169,
 ,[Offset], S0113_C2, 170,
 ,[Offset], S0114_C2, 171,
 ,[Offset], S0115_C2, 172,
 ,[Offset], S0116_C2, 173,
 ,[Offset], S0117_C2, 174,
 ,[Offset], S0118_C2, 175,
 ,[Offset], S0119_C2, 176,
 ,[Offset], S011A_C2, 177,
 ,[Offset], S011B_C2, 178,
 ,[Offset], S011C_C2, 179,
 ,[Offset], S011D_C2, 180,
 ,[Offset], S011F_C2, 181,
 ,[Offset], S0120_C2, 182,
 ,[Offset], S0121_C2, 183,
 ,[Offset], S0122_C2, 184,
 ,[Offset], S0123_C2, 185,
 ,[Offset], S0124_C2, 186,
 ,[Offset], S0125_C2, 187,
 ,[Offset], S0126_C2, 188,
 ,[Offset], S0127_C2, 189,
 ,[Offset], S0128_C2, 190,
 ,[Offset], S0129_C2, 191,
 ,[Offset], S012A_C2, 192,
 ,[Offset], S012B_C2, 193,
 ,[Offset], S012C_C2, 194,
 ,[Offset], S012D_C2, 195,
 ,[Offset], S012E_C2, 196,
 ,[Offset], S012F_C2, 197,
 ,[Offset], S0130_C2, 198,
 ,[Offset], S0131_C2, 199,
 ,[Offset], S0132_C2, 200,
 ,[Offset], S0133_C2, 201,
 ,[Offset], S0134_C2, 202,
 ,[Offset], S0135_C2, 203,
 ,[Offset], S0136_C2, 204,
 ,[Offset], S0137_C2, 205,
 ,[Offset], S0138_C2, 206,
 ,[Offset], S0139_C2, 207,
 ,[Offset], S013C_C2, 208,
 ,[Offset], S013D_C2, 209,
 ,[Offset], S013E_C2, 210,
 ,[Offset], S013F_C2, 211,
RSID_TABLE_BOP_MECH_SOUNDS, 3212,,,
 ,[Offset], head_motor, 0,
 ,[Offset], ball_lock_kickout, 1,
 ,[Offset], skill_shot_drop, 2,
RSID_TABLE_BOP_SOUNDS_END, 3213,,,
RSID_TABLE_BOP_SAMPLES, 3214,,,
RSID_TABLE_BOP_HUD, 3215,,,
RSID_TABLE_BOP_VERSION, 3216,,,
RSID_TBRIDE_END, 3217,,,
