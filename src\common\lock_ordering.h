// Copyright 2025 <Copyright Owner>

#pragma once

/**
 * @file lock_ordering.h
 * @brief Defines strict lock ordering to prevent deadlocks in the PS4 emulator
 *
 * CRITICAL: All code must follow this lock ordering to prevent deadlocks.
 * Locks must be acquired in the order specified below and released in reverse
 * order.
 *
 * Lock Hierarchy (acquire in this order):
 * 1. PS4Emulator::m_emulatorMutex (Global emulator state)
 * 2. IOManager mutexes (m_deviceMutex, m_pitMutex, m_eventMutex, m_irqMutex)
 * 3. Memory system mutexes (PS4MMU::m_mutex, Cache::m_mutex)
 * 4. CPU mutexes (X86_64CPU::mutex)
 * 5. InterruptHandler::m_mutex (recursive)
 * 6. JIT compiler mutexes (X86_64JITCompiler::m_cacheMutex)
 * 7. Component-specific mutexes (GPU, Audio, etc.)
 *
 * DEADLOCK PREVENTION RULES:
 *
 * 1. NEVER acquire locks in reverse order
 * 2. NEVER hold a higher-level lock when acquiring a lower-level lock
 * 3. Release locks before calling external code or callbacks when possible
 * 4. Use RAII lock guards to ensure proper cleanup
 * 5. Minimize lock scope - hold locks for the shortest time possible
 * 6. Prefer read locks (std::shared_lock) over write locks when possible
 * 7. Use recursive mutexes only when absolutely necessary (InterruptHandler)
 *
 * SPECIFIC DEADLOCK SCENARIOS TO AVOID:
 *
 * 1. CPU -> InterruptHandler deadlock:
 *    - NEVER call TriggerInterrupt while holding CPU mutex
 *    - InterruptHandler uses recursive mutex to handle re-entrant calls
 *
 * 2. IOManager -> CPU deadlock:
 *    - NEVER call TriggerInterrupt while holding IOManager mutexes
 *    - Update IRQ state first, then trigger interrupt without holding mutex
 *
 * 3. PS4Emulator -> Component deadlock:
 *    - NEVER hold emulator mutex when calling component methods
 *    - Minimize emulator mutex scope to state updates only
 *
 * 4. Memory allocation deadlock:
 *    - ElfLoader -> OrbisOS -> PS4MMU chain is safe (consistent order)
 *    - NEVER call memory allocation while holding component mutexes
 *
 * THREAD SANITIZER SUPPORT:
 *
 * Compile with -fsanitize=thread to detect race conditions and deadlocks:
 * - GCC: g++ -fsanitize=thread -g -O1
 * - Clang: clang++ -fsanitize=thread -g -O1
 * - MSVC: Use /fsanitize=thread (Visual Studio 2022+)
 */

#include <mutex>
#include <shared_mutex>
#include <type_traits>
#include <vector>

namespace ps4 {

/**
 * @brief Lock ordering levels for deadlock prevention
 */
enum class LockLevel : int {
  EMULATOR = 1000,      ///< PS4Emulator global mutex
  IO_MANAGER = 2000,    ///< IOManager mutexes
  MEMORY_SYSTEM = 3000, ///< Memory and MMU mutexes
  CPU = 4000,           ///< CPU mutexes
  INTERRUPT = 5000,     ///< InterruptHandler mutex
  JIT = 6000,           ///< JIT compiler mutexes
  COMPONENTS = 7000     ///< Component-specific mutexes
};

/**
 * @brief Debug lock ordering checker (enabled in debug builds)
 */
class LockOrderChecker {
public:
  static void AcquireLock(LockLevel level, const char *name);
  static void ReleaseLock(LockLevel level, const char *name);
  static void CheckOrder(LockLevel level, const char *name);

private:
  static thread_local std::vector<std::pair<LockLevel, const char *>>
      s_heldLocks;
};

/**
 * @brief RAII lock guard with ordering validation
 */
template <typename MutexType> class OrderedLockGuard {
public:
  OrderedLockGuard(MutexType &mutex, LockLevel level, const char *name)
      : m_lock(mutex), m_level(level), m_name(name), m_owns_lock(true) {
#ifdef _DEBUG
    LockOrderChecker::CheckOrder(level, name);
    LockOrderChecker::AcquireLock(level, name);
#endif
  }

  ~OrderedLockGuard() {
    if (m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
    }
  }

  // Manual lock control methods
  void lock() {
    if (!m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::CheckOrder(m_level, m_name);
#endif
      m_lock.lock();
      m_owns_lock = true;
#ifdef _DEBUG
      LockOrderChecker::AcquireLock(m_level, m_name);
#endif
    }
  }

  void unlock() {
    if (m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
      m_lock.unlock();
      m_owns_lock = false;
    }
  }

  bool owns_lock() const noexcept {
    return m_owns_lock;
  }

  // Non-copyable, non-movable
  OrderedLockGuard(const OrderedLockGuard &) = delete;
  OrderedLockGuard &operator=(const OrderedLockGuard &) = delete;
  OrderedLockGuard(OrderedLockGuard &&) = delete;
  OrderedLockGuard &operator=(OrderedLockGuard &&) = delete;

private:
  std::unique_lock<std::remove_reference_t<MutexType>> m_lock;
  LockLevel m_level;
  const char *m_name;
  bool m_owns_lock;
};

/**
 * @brief RAII shared lock guard with ordering validation
 */
template <typename MutexType> class OrderedSharedLockGuard {
public:
  OrderedSharedLockGuard(MutexType &mutex, LockLevel level, const char *name)
      : m_lock(mutex), m_level(level), m_name(name), m_owns_lock(true) {
#ifdef _DEBUG
    LockOrderChecker::CheckOrder(level, name);
    LockOrderChecker::AcquireLock(level, name);
#endif
  }

  ~OrderedSharedLockGuard() {
    if (m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
    }
  }

  // Manual lock control methods
  void lock() {
    if (!m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::CheckOrder(m_level, m_name);
#endif
      m_lock.lock();
      m_owns_lock = true;
#ifdef _DEBUG
      LockOrderChecker::AcquireLock(m_level, m_name);
#endif
    }
  }

  void unlock() {
    if (m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
      m_lock.unlock();
      m_owns_lock = false;
    }
  }

  bool owns_lock() const noexcept {
    return m_owns_lock;
  }

  // Non-copyable, non-movable
  OrderedSharedLockGuard(const OrderedSharedLockGuard &) = delete;
  OrderedSharedLockGuard &operator=(const OrderedSharedLockGuard &) = delete;
  OrderedSharedLockGuard(OrderedSharedLockGuard &&) = delete;
  OrderedSharedLockGuard &operator=(OrderedSharedLockGuard &&) = delete;

private:
  std::shared_lock<std::remove_reference_t<MutexType>> m_lock;
  LockLevel m_level;
  const char *m_name;
  bool m_owns_lock;
};

/**
 * @brief RAII timed lock guard with ordering validation
 * Supports complex locking patterns with timeouts and try_lock
 */
template <typename MutexType> class OrderedTimedLockGuard {
public:
  // Constructor for immediate locking
  OrderedTimedLockGuard(MutexType &mutex, LockLevel level, const char *name)
      : m_lock(mutex), m_level(level), m_name(name), m_owns_lock(true) {
#ifdef _DEBUG
    LockOrderChecker::CheckOrder(level, name);
    LockOrderChecker::AcquireLock(level, name);
#endif
  }

  // Constructor for deferred locking
  OrderedTimedLockGuard(MutexType &mutex, LockLevel level, const char *name, std::defer_lock_t)
      : m_lock(mutex, std::defer_lock), m_level(level), m_name(name), m_owns_lock(false) {
    // No order checking yet - will check when actually locked
  }

  // Constructor for try_to_lock
  OrderedTimedLockGuard(MutexType &mutex, LockLevel level, const char *name, std::try_to_lock_t)
      : m_lock(mutex, std::try_to_lock), m_level(level), m_name(name), m_owns_lock(m_lock.owns_lock()) {
#ifdef _DEBUG
    if (m_owns_lock) {
      LockOrderChecker::CheckOrder(level, name);
      LockOrderChecker::AcquireLock(level, name);
    }
#endif
  }

  // Constructor for adopt_lock
  OrderedTimedLockGuard(MutexType &mutex, LockLevel level, const char *name, std::adopt_lock_t)
      : m_lock(mutex, std::adopt_lock), m_level(level), m_name(name), m_owns_lock(true) {
#ifdef _DEBUG
    LockOrderChecker::CheckOrder(level, name);
    LockOrderChecker::AcquireLock(level, name);
#endif
  }

  ~OrderedTimedLockGuard() {
#ifdef _DEBUG
    if (m_owns_lock) {
      LockOrderChecker::ReleaseLock(m_level, m_name);
    }
#endif
  }

  // Lock operations
  void lock() {
    if (!m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::CheckOrder(m_level, m_name);
#endif
      m_lock.lock();
      m_owns_lock = true;
#ifdef _DEBUG
      LockOrderChecker::AcquireLock(m_level, m_name);
#endif
    }
  }

  bool try_lock() {
    if (!m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::CheckOrder(m_level, m_name);
#endif
      if (m_lock.try_lock()) {
        m_owns_lock = true;
#ifdef _DEBUG
        LockOrderChecker::AcquireLock(m_level, m_name);
#endif
        return true;
      }
    }
    return false;
  }

  template<class Rep, class Period>
  bool try_lock_for(const std::chrono::duration<Rep, Period>& timeout_duration) {
    if (!m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::CheckOrder(m_level, m_name);
#endif
      if (m_lock.try_lock_for(timeout_duration)) {
        m_owns_lock = true;
#ifdef _DEBUG
        LockOrderChecker::AcquireLock(m_level, m_name);
#endif
        return true;
      }
    }
    return false;
  }

  void unlock() {
    if (m_owns_lock) {
#ifdef _DEBUG
      LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
      m_lock.unlock();
      m_owns_lock = false;
    }
  }

  bool owns_lock() const noexcept {
    return m_owns_lock;
  }

  // Assignment for adopt_lock pattern
  OrderedTimedLockGuard& operator=(OrderedTimedLockGuard&& other) noexcept {
    if (this != &other) {
      if (m_owns_lock) {
#ifdef _DEBUG
        LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
      }
      m_lock = std::move(other.m_lock);
      m_level = other.m_level;
      m_name = other.m_name;
      m_owns_lock = other.m_owns_lock;
      other.m_owns_lock = false;
    }
    return *this;
  }

  // Move constructor
  OrderedTimedLockGuard(OrderedTimedLockGuard&& other) noexcept
      : m_lock(std::move(other.m_lock)), m_level(other.m_level),
        m_name(other.m_name), m_owns_lock(other.m_owns_lock) {
    other.m_owns_lock = false;
  }

  // Non-copyable
  OrderedTimedLockGuard(const OrderedTimedLockGuard &) = delete;
  OrderedTimedLockGuard &operator=(const OrderedTimedLockGuard &) = delete;

private:
  std::unique_lock<std::remove_reference_t<MutexType>> m_lock;
  LockLevel m_level;
  const char *m_name;
  bool m_owns_lock;
};

/**
 * @brief Convenience macros for ordered locking
 */
#define ORDERED_LOCK(mutex, level, name)                                       \
  ps4::OrderedLockGuard<std::remove_reference_t<decltype(mutex)>> lock(mutex, level, name)

#define ORDERED_SHARED_LOCK(mutex, level, name)                               \
  ps4::OrderedSharedLockGuard<std::remove_reference_t<decltype(mutex)>> lock(mutex, level, name)

#define EMULATOR_LOCK(mutex)                                                   \
  ORDERED_LOCK(mutex, ps4::LockLevel::EMULATOR, "EmulatorMutex")

#define IO_LOCK(mutex, name)                                                   \
  ORDERED_LOCK(mutex, ps4::LockLevel::IO_MANAGER, name)

#define MEMORY_LOCK(mutex, name)                                               \
  ORDERED_LOCK(mutex, ps4::LockLevel::MEMORY_SYSTEM, name)

#define MEMORY_SHARED_LOCK(mutex, name)                                        \
  ORDERED_SHARED_LOCK(mutex, ps4::LockLevel::MEMORY_SYSTEM, name)

#define CPU_LOCK(mutex) ORDERED_LOCK(mutex, ps4::LockLevel::CPU, "CPUMutex")

#define CPU_TIMED_LOCK(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "CPUMutex")

#define CPU_TIMED_LOCK_DEFER(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "CPUMutex", std::defer_lock)

#define CPU_TIMED_LOCK_TRY(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "CPUMutex", std::try_to_lock)

#define CPU_TIMED_LOCK_ADOPT(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock)

#define PIPELINE_TIMED_LOCK(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "PipelineMutex")

#define PIPELINE_TIMED_LOCK_DEFER(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "PipelineMutex", std::defer_lock)

#define PIPELINE_TIMED_LOCK_TRY(mutex) \
  ps4::OrderedTimedLockGuard<std::remove_reference_t<decltype(mutex)>> lock_##__LINE__(mutex, ps4::LockLevel::CPU, "PipelineMutex", std::try_to_lock)

#define INTERRUPT_LOCK(mutex)                                                  \
  ORDERED_LOCK(mutex, ps4::LockLevel::INTERRUPT, "InterruptMutex")

#define JIT_LOCK(mutex) ORDERED_LOCK(mutex, ps4::LockLevel::JIT, "JITMutex")

#define COMPONENT_LOCK(mutex, name)                                            \
  ORDERED_LOCK(mutex, ps4::LockLevel::COMPONENTS, name)

#define COMPONENT_SHARED_LOCK(mutex, name)                                     \
  ORDERED_SHARED_LOCK(mutex, ps4::LockLevel::COMPONENTS, name)

} // namespace ps4
