#pragma once

#include <array>
#include <atomic>
#include <chrono>
#include <cstdint>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

/**
 * @brief Information about page access patterns.
 */
struct PageAccessInfo {
  uint64_t accessCount = 0;                             ///< Number of accesses
  std::chrono::steady_clock::time_point lastAccessTime; ///< Last access time
  bool isFrequentlyAccessed = false; ///< Frequent access flag
  uint64_t compressedId = 0;         ///< Compressed page ID
};

/**
 * @brief Statistics for memory compression.
 */
struct CompressionStats {
  uint64_t pagesCompressed = 0;       ///< Number of pages compressed
  uint64_t pagesDecompressed = 0;     ///< Number of pages decompressed
  uint64_t totalBytesOriginal = 0;    ///< Total original bytes
  uint64_t totalBytesCompressed = 0;  ///< Total compressed bytes
  uint64_t totalBytesSaved = 0;       ///< Total bytes saved
  double compressionRatio = 1.0;      ///< Average compression ratio
  double compressionEfficiency = 0.0; ///< Bytes saved per original byte
  uint64_t totalLatencyUs = 0; ///< Total compression latency (microseconds)
  uint64_t cacheHits = 0;      ///< Number of cache hits
  uint64_t cacheMisses = 0;    ///< Number of cache misses
};

/**
 * @brief Manages memory compression to reduce usage for rarely accessed pages.
 */
class MemoryCompressor {
public:
  /**
   * @brief Compression algorithm options.
   */
  enum CompressionAlgorithm {
    ALGO_LZ4,  ///< Fast compression/decompression
    ALGO_ZSTD, ///< Better compression ratio, slower
    ALGO_ZLIB, ///< Balanced performance
    ALGO_NONE  ///< No compression (for testing)
  };

  /**
   * @brief Compression policy options.
   */
  enum CompressionPolicy {
    POLICY_AGGRESSIVE,   ///< Compress more pages, higher performance cost
    POLICY_BALANCED,     ///< Balance memory savings and performance
    POLICY_CONSERVATIVE, ///< Compress only rarely accessed pages
    POLICY_CUSTOM        ///< Use custom thresholds
  };

  /**
   * @brief Constructs a MemoryCompressor instance.
   * @param algorithm Compression algorithm to use.
   * @param policy Compression policy to use.
   */
  MemoryCompressor(CompressionAlgorithm algorithm = ALGO_LZ4,
                   CompressionPolicy policy = POLICY_BALANCED);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~MemoryCompressor() noexcept;

  // Disable copy and move
  MemoryCompressor(const MemoryCompressor &) = delete;
  MemoryCompressor &operator=(const MemoryCompressor &) = delete;
  MemoryCompressor(MemoryCompressor &&) = delete;
  MemoryCompressor &operator=(MemoryCompressor &&) = delete;

  /**
   * @brief Initializes the compressor.
   * @return True on success, false on failure.
   */
  bool Initialize();

  /**
   * @brief Compresses a memory page.
   * @param pageData Pointer to page data.
   * @param pageSize Size of the page.
   * @param outCompressedId Output parameter for compressed page ID.
   * @return True if compression succeeds, false otherwise.
   */
  bool CompressPage(const uint8_t *pageData, size_t pageSize,
                    uint64_t &outCompressedId);

  /**
   * @brief Decompresses a memory page.
   * @param compressedId ID of the compressed page.
   * @param outPageData Output buffer for decompressed data.
   * @param pageSize Size of the output buffer.
   * @return True if decompression succeeds, false otherwise.
   */
  bool DecompressPage(uint64_t compressedId, uint8_t *outPageData,
                      size_t pageSize);

  /**
   * @brief Frees a compressed page.
   * @param compressedId ID of the compressed page.
   * @return True if successful, false otherwise.
   */
  bool FreeCompressedPage(uint64_t compressedId);

  /**
   * @brief Sets the compression algorithm.
   * @param algorithm Compression algorithm to use.
   */
  void SetCompressionAlgorithm(CompressionAlgorithm algorithm);

  /**
   * @brief Sets the compression policy.
   * @param policy Compression policy to use.
   */
  void SetCompressionPolicy(CompressionPolicy policy);
  /**
   * @brief Checks if a page should be compressed based on access patterns.
   * @param accessCount Number of times the page has been accessed.
   * @param lastAccessTime Last time the page was accessed.
   * @return True if the page should be compressed, false otherwise.
   */
  bool ShouldCompressPage(
      uint64_t accessCount,
      const std::chrono::steady_clock::time_point &lastAccessTime) const;

  /**
   * @brief Checks if page data can be compressed.
   * @param pageData Pointer to page data.
   * @param pageSize Size of the page.
   * @return True if the page data is compressible, false otherwise.
   */
  bool IsCompressible(const uint8_t *pageData, size_t pageSize) const;

  /**
   * @brief Retrieves compression statistics.
   * @return Current compression statistics.
   */
  CompressionStats GetStats() const;

  /**
   * @brief Resets compression statistics.
   */
  void ResetStats();

  /**
   * @brief Sets custom compression thresholds.
   * @param accessThreshold Maximum access count for compression eligibility.
   * @param idleTimeMs Minimum idle time in milliseconds for compression
   * eligibility.
   */
  void SetCustomThresholds(uint64_t accessThreshold, uint64_t idleTimeMs);

  /**
   * @brief Clears all compressed pages.
   */
  void ClearAllCompressedPages();

  /**
   * @brief Gets the number of compressed pages currently stored.
   * @return Number of compressed pages.
   */
  size_t GetCompressedPageCount() const;

  /**
   * @brief Gets the current compression ratio (compressed/original).
   * @return Compression ratio.
   */
  double GetCompressionRatio() const;

  /**
   * @brief Gets compression efficiency (bytes saved/original).
   * @return Compression efficiency.
   */
  double GetCompressionEfficiency() const;

  /**
   * @brief Logs current compression statistics.
   */
  void LogStats() const;

  /**
   * @brief Registers an access to a memory page for compression decisions.
   * @param pageAddress Unique identifier of the page (e.g., starting address).
   * @param pageData Pointer to page data.
   * @param pageSize Size of the page.
   */
  void RegisterPageAccess(uint64_t pageAddress, const uint8_t *pageData,
                          size_t pageSize);

  /**
   * @brief Saves the compressor state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the compressor state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Gets threshold values based on the current policy.
   * @param outAccessThreshold Output parameter for access threshold.
   * @param outIdleTimeMs Output parameter for idle time threshold.
   */
  void GetPolicyThresholds(double &outHeatThreshold,
                           uint64_t &outIdleTimeMs) const;

  /**
   * @brief Compresses data using the current algorithm.
   * @param data Data to compress.
   * @param size Size of data.
   * @param outCompressed Output parameter for compressed data.
   * @return True if compression succeeds, false otherwise.
   */
  bool CompressData(const uint8_t *data, size_t size,
                    std::vector<uint8_t> &outCompressed);

  /**
   * @brief Decompresses data using the current algorithm.
   * @param compressedData Compressed data.
   * @param compressedSize Size of compressed data.
   * @param outData Output buffer for decompressed data.
   * @param outSize Size of output buffer.
   * @return True if decompression succeeds, false otherwise.
   */
  bool DecompressData(const uint8_t *compressedData, size_t compressedSize,
                      uint8_t *outData, size_t outSize);
  /**
   * @brief Generates a unique ID for a compressed page.
   * @return Unique compressed page ID.
   */
  uint64_t GenerateCompressedId();

  /**
   * @brief Checks if data is compressible based on entropy analysis.
   * @param data Data to analyze.
   * @param size Size of data.
   * @return True if data appears compressible, false otherwise.
   */
  bool IsCompressible(const uint8_t *data, size_t size);

  CompressionAlgorithm m_algorithm; ///< Current compression algorithm
  CompressionPolicy m_policy;       ///< Current compression policy
  std::atomic<uint64_t> m_nextCompressedId{1}; ///< Next compressed page ID
  std::unordered_map<uint64_t, std::vector<uint8_t>>
      m_compressedPages;                       ///< Compressed pages
  mutable std::mutex m_compressedPagesMutex;   ///< Protect compressed pages map
  CompressionStats m_stats;                    ///< Compression statistics
  mutable std::mutex m_statsMutex;             ///< Protect stats
  mutable std::shared_mutex m_compressorMutex; ///< (unused) existing mutex
  double m_customHeatThreshold{5.0};        ///< Custom heat threshold
  uint64_t m_customIdleTimeMs{5000};           ///< Custom idle time (ms)
  std::unordered_map<uint64_t, PageAccessInfo>
      m_pageAccessInfo;         ///< Page access info
  std::mutex m_pageAccessMutex; ///< Protect access info map
  std::unordered_map<uint64_t, uint64_t>
      m_pageToCompressedId;           ///< Page→compressed ID map
  std::mutex m_pageToCompressedMutex; ///< Protect page→ID map
};

} // namespace ps4