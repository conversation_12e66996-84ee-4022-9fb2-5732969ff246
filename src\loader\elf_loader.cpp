#define NOMINMAX
#include "elf_loader.h"
#include "self_decrypter.h"
#include "key_store.h"
#include "../memory/ps4_mmu.h"
#include "../ps4/ps4_emulator.h"
#include <algorithm>
#include <fstream>
#include <chrono>
#include <cstring>
#include <filesystem>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <system_error>

namespace ps4 {

struct ElfLoadException : std::runtime_error {
    explicit ElfLoadException(const std::string& msg) : std::runtime_error(msg) {}
};

void LoadedElf::Save(std::ostream& out) const {
    try {
        uint32_t version = 1;
        out.write(reinterpret_cast<const char*>(&version), sizeof(version));
        uint64_t segCount = loadedSegments.size();
        out.write(reinterpret_cast<const char*>(&segCount), sizeof(segCount));
        for (const auto& seg : loadedSegments) {
            out.write(reinterpret_cast<const char*>(&seg), sizeof(seg));
        }
        out.write(reinterpret_cast<const char*>(&entryPoint), sizeof(entryPoint));
        out.write(reinterpret_cast<const char*>(&baseLoadAddress), sizeof(baseLoadAddress));
        out.write(reinterpret_cast<const char*>(&phdrAddress), sizeof(phdrAddress));
        out.write(reinterpret_cast<const char*>(&phdrEntrySize), sizeof(phdrEntrySize));
        out.write(reinterpret_cast<const char*>(&phdrNum), sizeof(phdrNum));
        out.write(reinterpret_cast<const char*>(&dynamicAddress), sizeof(dynamicAddress));
        out.write(reinterpret_cast<const char*>(&dynamicSize), sizeof(dynamicSize));
        out.write(reinterpret_cast<const char*>(&dynSymTableAddr), sizeof(dynSymTableAddr));
        out.write(reinterpret_cast<const char*>(&dynStrTableAddr), sizeof(dynStrTableAddr));
        out.write(reinterpret_cast<const char*>(&dynStrTableSize), sizeof(dynStrTableSize));
        out.write(reinterpret_cast<const char*>(&relaDynAddr), sizeof(relaDynAddr));
        out.write(reinterpret_cast<const char*>(&relaDynSize), sizeof(relaDynSize));
        out.write(reinterpret_cast<const char*>(&relaPltAddr), sizeof(relaPltAddr));
        out.write(reinterpret_cast<const char*>(&relaPltSize), sizeof(relaPltSize));
        out.write(reinterpret_cast<const char*>(&pltGotAddr), sizeof(pltGotAddr));
        out.write(reinterpret_cast<const char*>(&metadataAddr), sizeof(metadataAddr));
        out.write(reinterpret_cast<const char*>(&metadataSize), sizeof(metadataSize));
        out.write(reinterpret_cast<const char*>(&isSelf), sizeof(isSelf));
        uint64_t selfSegCount = selfSegments.size();
        out.write(reinterpret_cast<const char*>(&selfSegCount), sizeof(selfSegCount));
        for (const auto& seg : selfSegments) {
            out.write(reinterpret_cast<const char*>(&seg), sizeof(seg));
        }
        uint64_t symCount = dynSymbols.size();
        out.write(reinterpret_cast<const char*>(&symCount), sizeof(symCount));
        for (const auto& sym : dynSymbols) {
            sym.Save(out);
        }
        uint64_t strSize = dynStringTable.size();
        out.write(reinterpret_cast<const char*>(&strSize), sizeof(strSize));
        out.write(dynStringTable.data(), strSize);
        uint64_t resSymCount = resolvedSymbols.size();
        out.write(reinterpret_cast<const char*>(&resSymCount), sizeof(resSymCount));
        for (const auto& [name, addr] : resolvedSymbols) {
            uint32_t nameLen = static_cast<uint32_t>(name.size());
            out.write(reinterpret_cast<const char*>(&nameLen), sizeof(nameLen));
            out.write(name.data(), nameLen);
            out.write(reinterpret_cast<const char*>(&addr), sizeof(addr));
        }
        uint64_t selfDataSize = self_data_.size();
        out.write(reinterpret_cast<const char*>(&selfDataSize), sizeof(selfDataSize));
        out.write(reinterpret_cast<const char*>(self_data_.data()), selfDataSize);
        if (!out.good()) throw ElfLoadException("Failed to write ELF metadata");
        spdlog::info("LoadedElf state saved: {} segments, {} symbols", segCount, symCount);
    } catch (const std::exception& e) {
        spdlog::error("Failed to save LoadedElf state: {}", e.what());
        throw ElfLoadException("Save state failure");
    }
}

void LoadedElf::Load(std::istream& in) {
    try {
        uint32_t version;
        in.read(reinterpret_cast<char*>(&version), sizeof(version));
        if (version != 1) throw ElfLoadException("Invalid LoadedElf state version");
        uint64_t segCount;
        in.read(reinterpret_cast<char*>(&segCount), sizeof(segCount));
        loadedSegments.resize(segCount);
        for (auto& seg : loadedSegments) {
            in.read(reinterpret_cast<char*>(&seg), sizeof(seg));
        }
        in.read(reinterpret_cast<char*>(&entryPoint), sizeof(entryPoint));
        in.read(reinterpret_cast<char*>(&baseLoadAddress), sizeof(baseLoadAddress));
        in.read(reinterpret_cast<char*>(&phdrAddress), sizeof(phdrAddress));
        in.read(reinterpret_cast<char*>(&phdrEntrySize), sizeof(phdrEntrySize));
        in.read(reinterpret_cast<char*>(&phdrNum), sizeof(phdrNum));
        in.read(reinterpret_cast<char*>(&dynamicAddress), sizeof(dynamicAddress));
        in.read(reinterpret_cast<char*>(&dynamicSize), sizeof(dynamicSize));
        in.read(reinterpret_cast<char*>(&dynSymTableAddr), sizeof(dynSymTableAddr));
        in.read(reinterpret_cast<char*>(&dynStrTableAddr), sizeof(dynStrTableAddr));
        in.read(reinterpret_cast<char*>(&dynStrTableSize), sizeof(dynStrTableSize));
        in.read(reinterpret_cast<char*>(&relaDynAddr), sizeof(relaDynAddr));
        in.read(reinterpret_cast<char*>(&relaDynSize), sizeof(relaDynSize));
        in.read(reinterpret_cast<char*>(&relaPltAddr), sizeof(relaPltAddr));
        in.read(reinterpret_cast<char*>(&relaPltSize), sizeof(relaPltSize));
        in.read(reinterpret_cast<char*>(&pltGotAddr), sizeof(pltGotAddr));
        in.read(reinterpret_cast<char*>(&metadataAddr), sizeof(metadataAddr));
        in.read(reinterpret_cast<char*>(&metadataSize), sizeof(metadataSize));
        in.read(reinterpret_cast<char*>(&isSelf), sizeof(isSelf));
        uint64_t selfSegCount;
        in.read(reinterpret_cast<char*>(&selfSegCount), sizeof(selfSegCount));
        selfSegments.resize(selfSegCount);
        for (auto& seg : selfSegments) {
            in.read(reinterpret_cast<char*>(&seg), sizeof(seg));
        }
        uint64_t symCount;
        in.read(reinterpret_cast<char*>(&symCount), sizeof(symCount));
        dynSymbols.resize(symCount);
        for (auto& sym : dynSymbols) {
            sym.Load(in);
        }
        uint64_t strSize;
        in.read(reinterpret_cast<char*>(&strSize), sizeof(strSize));
        dynStringTable.resize(strSize);
        in.read(dynStringTable.data(), strSize);
        uint64_t resSymCount;
        in.read(reinterpret_cast<char*>(&resSymCount), sizeof(resSymCount));
        resolvedSymbols.clear();
        for (uint64_t i = 0; i < resSymCount; ++i) {
            uint32_t nameLen;
            in.read(reinterpret_cast<char*>(&nameLen), sizeof(nameLen));
            std::string name(nameLen, '\0');
            in.read(name.data(), nameLen);
            uint64_t addr;
            in.read(reinterpret_cast<char*>(&addr), sizeof(addr));
            resolvedSymbols[name] = addr;
        }
        uint64_t selfDataSize;
        in.read(reinterpret_cast<char*>(&selfDataSize), sizeof(selfDataSize));
        self_data_.resize(selfDataSize);
        in.read(reinterpret_cast<char*>(self_data_.data()), selfDataSize);
        if (!in.good()) throw ElfLoadException("Failed to read ELF metadata");
        spdlog::info("LoadedElf state loaded: {} segments, {} symbols", segCount, symCount);
    } catch (const std::exception& e) {
        spdlog::error("Failed to load LoadedElf state: {}", e.what());
        throw ElfLoadException("Load state failure");
    }
}

ElfLoader::ElfLoader(PS4Emulator& emu) : m_emulator(emu), m_currentProcessId(1) {
    spdlog::info("ElfLoader initialized");
}

ElfLoader::~ElfLoader() noexcept {
    COMPONENT_LOCK(m_mutex, "ElfLoaderMutex");
    m_stats.clear();
    spdlog::info("ElfLoader destroyed");
}

bool ElfLoader::Load(const std::string& filename, LoadedElf& loadedElf,
                     bool isSharedObject, uint64_t processId) {
    auto start = std::chrono::steady_clock::now();
    COMPONENT_LOCK(m_mutex, "ElfLoaderMutex");
    m_currentProcessId = processId;
    spdlog::info("Loading ELF/SELF: {}, process ID: {}, shared: {}", filename, processId, isSharedObject);

    try {
        std::string resolvedPath = m_emulator.GetFilesystem().MapToHostPath(filename);
        if (!std::filesystem::exists(resolvedPath)) {
            spdlog::error("File does not exist: {}", resolvedPath);
            auto parent = std::filesystem::path(resolvedPath).parent_path();
            if (!std::filesystem::exists(parent)) {
                std::filesystem::create_directories(parent);
                spdlog::warn("Created parent directory: {}", parent.string());
            }
            throw ElfLoadException("File does not exist");
        }

        std::ifstream file(resolvedPath, std::ios::binary);
        if (!file.is_open()) {
            spdlog::error("Failed to open file: {}", resolvedPath);
            throw ElfLoadException("File open failure");
        }

        file.seekg(0, std::ios::end);
        std::streamsize fileSize = file.tellg();
        file.seekg(0, std::ios::beg);
        spdlog::debug("File size: {} bytes", fileSize);

        if (fileSize < static_cast<std::streamsize>(sizeof(elf::Elf64_Ehdr))) {
            spdlog::error("File too small to be a valid ELF: {} bytes", fileSize);
            throw ElfLoadException("File too small");
        }

        std::vector<uint8_t> fileData(fileSize);
        file.read(reinterpret_cast<char*>(fileData.data()), fileData.size());
        loadedElf.self_data_ = fileData;

        // Check if this is an encrypted SELF file
        bool isEncryptedSelf = SelfDecrypter::IsEncryptedSelf(fileData);
        std::vector<uint8_t> decryptedData = fileData;

        if (isEncryptedSelf) {
            spdlog::info("Detected encrypted SELF file: {}, attempting decryption", filename);
            SelfDecrypter decrypter;
            if (!decrypter.LoadSelfData(fileData)) {
                spdlog::error("Failed to load SELF data: {}", decrypter.GetLastError());
                throw ElfLoadException("SELF load failure: " + decrypter.GetLastError());
            }
            if (!decrypter.DecryptSelf()) {
                std::string errorMsg = fmt::format(
                    "Failed to decrypt SELF file: {}\n"
                    "Reason: {}\n"
                    "This emulator requires a decrypted ELF file or valid PS4 decryption keys.\n"
                    "Please replace '{}' with a decrypted eboot.bin (starting with 0x7F454C46) or provide keys in ./ps4_root/keys.json.",
                    filename, decrypter.GetLastError(), filename);
                spdlog::error("{}", errorMsg);
                throw ElfLoadException(errorMsg);
            }
            if (!decrypter.ExtractElfData(decryptedData)) {
                spdlog::error("Failed to extract decrypted ELF: {}", decrypter.GetLastError());
                throw ElfLoadException("ELF extraction failure: " + decrypter.GetLastError());
            }
            spdlog::info("Successfully decrypted SELF file: {} bytes", decryptedData.size());
        } else if (fileData.size() >= 4 && fileData[0] == 0x7F && fileData[1] == 'E' &&
                   fileData[2] == 'L' && fileData[3] == 'F') {
            spdlog::info("Detected decrypted ELF file: {}, no decryption needed", filename);
        } else {
            // Check for all zeros or invalid header
            bool allZeros = true;
            size_t checkSize = std::min(fileData.size(), size_t(64));
            for (size_t i = 0; i < checkSize; ++i) {
                if (fileData[i] != 0) {
                    allZeros = false;
                    break;
                }
            }

            std::stringstream hexDump;
            for (size_t i = 0; i < std::min(fileData.size(), size_t(32)); ++i) {
                hexDump << std::hex << std::setw(2) << std::setfill('0')
                        << static_cast<int>(fileData[i]) << " ";
                if ((i + 1) % 16 == 0) hexDump << "\n";
            }

            std::string errorMsg;
            if (allZeros && fileData.size() > 32) {
                errorMsg = fmt::format(
                    "Not a valid ELF or SELF file: {}\n"
                    "File appears to be all zeros (first {} bytes):\n{}\n"
                    "Possible causes:\n"
                    "1. File is encrypted and requires decryption keys\n"
                    "2. File is corrupted or not properly dumped\n"
                    "3. PKG extraction failed\n"
                    "Please provide a decrypted ELF file (starting with 0x7F454C46) or valid PS4 decryption keys in ./ps4_root/keys.json",
                    filename, checkSize, hexDump.str());
            } else {
                errorMsg = fmt::format(
                    "Not a valid ELF file: {}\n"
                    "First 32 bytes at offset 0x0:\n{}\n"
                    "Expected ELF magic: 0x7F 'E' 'L' 'F' (0x7F454C46)\n"
                    "Please provide a decrypted ELF file for '{}' or valid PS4 decryption keys in ./ps4_root/keys.json.",
                    filename, hexDump.str(), filename);
            }
            spdlog::error("{}", errorMsg);
            throw ElfLoadException(errorMsg);
        }

        // Parse ELF header
        if (decryptedData.size() < sizeof(elf::Elf64_Ehdr)) {
            throw ElfLoadException("File too small to be a valid ELF");
        }

        elf::Elf64_Ehdr elfHeader;
        std::memcpy(&elfHeader, decryptedData.data(), sizeof(elfHeader));

        // Log ELF header details for debugging
        spdlog::debug("ELF header details: magic=0x{:02X}{:02X}{:02X}{:02X}, "
                      "class=0x{:02X}, data=0x{:02X}, version=0x{:02X}, "
                      "machine=0x{:04X}, type=0x{:04X}, entry=0x{:016X}, "
                      "phoff=0x{:016X}, shoff=0x{:016X}, phnum={}, shnum={}",
                      elfHeader.e_ident[0], elfHeader.e_ident[1], elfHeader.e_ident[2], elfHeader.e_ident[3],
                      elfHeader.e_ident[elf::EI_CLASS], elfHeader.e_ident[elf::EI_DATA], elfHeader.e_ident[elf::EI_VERSION],
                      elfHeader.e_machine, elfHeader.e_type, elfHeader.e_entry,
                      elfHeader.e_phoff, elfHeader.e_shoff, elfHeader.e_phnum, elfHeader.e_shnum);

        // Validate ELF header
        if (!elfHeader.isValidElf64()) {
            std::stringstream ss;
            ss << "Invalid ELF header in file: " << filename << "\n";
            ss << "Magic: ";
            for (int i = 0; i < 4; i++) {
                ss << std::hex << std::setw(2) << std::setfill('0')
                   << (int)elfHeader.e_ident[i] << " ";
            }
            ss << "\nClass: " << (int)elfHeader.e_ident[elf::EI_CLASS] << " (expected: 2 for 64-bit)";
            ss << "\nData: " << (int)elfHeader.e_ident[elf::EI_DATA] << " (expected: 1 for LSB)";
            ss << "\nVersion: " << (int)elfHeader.e_ident[elf::EI_VERSION] << " (expected: 1)";
            ss << "\nOS/ABI: " << (int)elfHeader.e_ident[elf::EI_OSABI];
            ss << "\nABI Version: " << (int)elfHeader.e_ident[elf::EI_ABIVERSION];
            spdlog::error(ss.str());
            throw ElfLoadException("Invalid ELF header");
        }

        // Support PS4-specific ELF types
        if (elfHeader.e_type != elf::ET_EXEC && elfHeader.e_type != elf::ET_DYN &&
            elfHeader.e_type != elf::ET_SCE_EXEC && elfHeader.e_type != elf::ET_SCE_DYNEXEC &&
            elfHeader.e_type != elf::ET_SCE_DYNAMIC) {
            spdlog::error("Unsupported ELF type: 0x{:04X}", elfHeader.e_type);
            throw ElfLoadException("Unsupported ELF type");
        }

        // Handle PS4 static executable (ET_SCE_EXEC) like ET_EXEC
        bool isDynamic = (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNEXEC ||
                          elfHeader.e_type == elf::ET_SCE_DYNAMIC);
        spdlog::info("ELF type: 0x{:04X} ({})", elfHeader.e_type,
                     isDynamic ? "Dynamic" : "Static");

        // Set loadedElf properties
        loadedElf.isSelf = isEncryptedSelf;

        // Pass decrypted data to LoadSegments
        std::ifstream decryptedFile;
        if (isEncryptedSelf) {
            std::string tempPath = resolvedPath + ".decrypted";
            std::ofstream tempFile(tempPath, std::ios::binary);
            if (!tempFile.is_open()) {
                spdlog::error("Failed to create temporary decrypted file: {}", tempPath);
                throw ElfLoadException("Temporary file creation failure");
            }
            tempFile.write(reinterpret_cast<const char*>(decryptedData.data()), decryptedData.size());
            tempFile.close();
            decryptedFile.open(tempPath, std::ios::binary);
            if (!decryptedFile.is_open()) {
                spdlog::error("Failed to open temporary decrypted file: {}", tempPath);
                throw ElfLoadException("Temporary file open failure");
            }
            file.swap(decryptedFile);
        }

        if (!LoadSegments(file, elfHeader, loadedElf)) {
            throw ElfLoadException("Failed to load segments");
        }
        if (loadedElf.dynamicAddress != 0 && !ParseDynamicSection(loadedElf)) {
            throw ElfLoadException("Failed to parse dynamic section");
        }
        if (loadedElf.dynSymTableAddr != 0 && !ReadDynamicTables(loadedElf)) {
            throw ElfLoadException("Failed to read dynamic tables");
        }
        if (loadedElf.relaDynAddr != 0 &&
            !ProcessRelocations(loadedElf, loadedElf.relaDynAddr, loadedElf.relaDynSize, false)) {
            throw ElfLoadException("Failed to process dynamic relocations");
        }
        if (loadedElf.relaPltAddr != 0 &&
            !ProcessRelocations(loadedElf, loadedElf.relaPltAddr, loadedElf.relaPltSize, true)) {
            throw ElfLoadException("Failed to process PLT relocations");
        }

        // Set entry point, adjusting for dynamic ELFs
        loadedElf.entryPoint = elfHeader.e_entry + (isDynamic ? loadedElf.baseLoadAddress : 0);

        m_emulator.SetCPUEntryPoints(loadedElf.entryPoint);
        m_emulator.AddLoadedModule(filename, loadedElf.entryPoint);
        m_stats[filename + "_load_count"]++;
        m_stats[filename + "_relocations"] +=
            loadedElf.relaDynSize / sizeof(elf::Elf64_Rela) + loadedElf.relaPltSize / sizeof(elf::Elf64_Rela);
        m_stats[filename + "_symbols_resolved"] += loadedElf.resolvedSymbols.size();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::steady_clock::now() - start).count();
        m_stats[filename + "_load_time_ms"] = duration;
        spdlog::info("ELF/SELF '{}' loaded: entry=0x{:x}, pid={}, time={}ms, relocations={}, symbols={}",
                     filename, loadedElf.entryPoint, processId, duration,
                     m_stats[filename + "_relocations"], m_stats[filename + "_symbols_resolved"]);
        return true;
    } catch (const ElfLoadException& e) {
        spdlog::error("ELF/SELF load failed for '{}': {}", filename, e.what());
        return false;
    } catch (const std::system_error& e) {
        spdlog::error("System error loading ELF/SELF '{}': {} (code: {})", filename, e.what(), e.code().value());
        return false;
    } catch (const std::exception& e) {
        spdlog::error("Unexpected error loading ELF/SELF '{}': {}", filename, e.what());
        return false;
    }
}

bool ElfLoader::LoadSegments(std::ifstream& file, const elf::Elf64_Ehdr& elfHeader,
                             LoadedElf& loadedElf) {
    try {
        spdlog::debug("LoadSegments: Starting segment loading for ELF type 0x{:04X}", elfHeader.e_type);

        uint64_t minVaddr = UINT64_MAX, maxVaddrEnd = 0;
        std::vector<elf::Elf64_Phdr> headers(elfHeader.e_phnum);

        spdlog::debug("LoadSegments: Reading {} program headers from offset 0x{:016X}", elfHeader.e_phnum, elfHeader.e_phoff);

        file.seekg(elfHeader.e_phoff);
        file.read(reinterpret_cast<char*>(headers.data()), headers.size() * sizeof(elf::Elf64_Phdr));
        if (!file.good()) {
            spdlog::error("LoadSegments: Program header read failure");
            throw ElfLoadException("Program header read failure");
        }

        // Log program headers for debugging and check for infinite loops
        for (size_t i = 0; i < headers.size(); ++i) {
            const auto& phdr = headers[i];
            spdlog::debug("Program header {}: type=0x{:08X}, offset=0x{:016X}, "
                          "vaddr=0x{:016X}, filesz=0x{:016X}, memsz=0x{:016X}, "
                          "flags=0x{:08X}, align=0x{:016X}",
                          i, phdr.p_type, phdr.p_offset, phdr.p_vaddr,
                          phdr.p_filesz, phdr.p_memsz, phdr.p_flags, phdr.p_align);

            // Validate program header to prevent infinite loops
            if (phdr.p_type == elf::PT_LOAD) {
                if (phdr.p_vaddr == 0 && phdr.p_memsz == 0) {
                    spdlog::warn("LoadSegments: Skipping empty LOAD segment {}", i);
                    continue;
                }
                if (phdr.p_memsz > 0x100000000ULL) { // 4GB limit
                    spdlog::error("LoadSegments: Segment {} memory size too large: 0x{:016X}", i, phdr.p_memsz);
                    throw ElfLoadException("Segment memory size too large");
                }
            }
        }

        // Validate program headers
        for (const auto& phdr : headers) {
            if (!phdr.isValid()) {
                spdlog::error("Invalid program header: type=0x{:08X}, align=0x{:016X}", phdr.p_type, phdr.p_align);
                throw ElfLoadException("Invalid program header");
            }
            if (phdr.p_type == elf::PT_LOAD && phdr.p_memsz > 0) {
                minVaddr = std::min(minVaddr, phdr.p_vaddr);
                maxVaddrEnd = std::max(maxVaddrEnd, phdr.p_vaddr + phdr.p_memsz);
            }
        }

        bool isDynamic = (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNEXEC ||
                          elfHeader.e_type == elf::ET_SCE_DYNAMIC);

        spdlog::debug("LoadSegments: isDynamic={}, minVaddr=0x{:016X}, maxVaddrEnd=0x{:016X}",
                      isDynamic, minVaddr, maxVaddrEnd);

        if (isDynamic && minVaddr != UINT64_MAX && maxVaddrEnd > minVaddr) {
            uint64_t totalSize = maxVaddrEnd - minVaddr;
            spdlog::debug("LoadSegments: Allocating base address for dynamic ELF, size=0x{:016X}", totalSize);

            loadedElf.baseLoadAddress = m_emulator.GetOrbisOS().AllocateVirtualMemory(totalSize, 4096, false);
            if (!loadedElf.baseLoadAddress) {
                spdlog::error("LoadSegments: Base address allocation failure");
                throw ElfLoadException("Base address allocation failure");
            }
            m_stats["memory_allocations"]++;
            spdlog::debug("Allocated base address for dynamic ELF: 0x{:016X}", loadedElf.baseLoadAddress);
        }

        // Skip SelfDecrypter for decrypted ELFs
        std::vector<uint8_t> decrypted_self_data;
        if (loadedElf.isSelf) {
            spdlog::info("Processing SELF segments for decryption");
            SelfDecrypter decrypter;
            if (!decrypter.LoadSelfData(loadedElf.self_data_)) {
                throw ElfLoadException("Failed to load SELF data for decryption: " + decrypter.GetLastError());
            }
            if (!decrypter.DecryptSelf()) {
                throw ElfLoadException("Failed to decrypt SELF file: " + decrypter.GetLastError());
            }
            if (!decrypter.ExtractElfData(decrypted_self_data)) {
                throw ElfLoadException("Failed to extract decrypted ELF data: " + decrypter.GetLastError());
            }
            spdlog::info("Decrypted SELF file: size=0x{:X} bytes", decrypted_self_data.size());
        } else {
            spdlog::debug("Skipping SelfDecrypter for decrypted ELF");
        }

        int loadedSegmentCount = 0;
        for (size_t headerIndex = 0; headerIndex < headers.size(); ++headerIndex) {
            const auto& phdr = headers[headerIndex];

            spdlog::debug("LoadSegments: Processing header {} of {}, type=0x{:08X}",
                          headerIndex + 1, headers.size(), phdr.p_type);

            switch (phdr.p_type) {
                case elf::PT_LOAD: {
                    if (phdr.p_memsz == 0) {
                        spdlog::debug("LoadSegments: Skipping empty LOAD segment {}", headerIndex);
                        break;
                    }

                    auto segmentStart = std::chrono::steady_clock::now();
                    const auto segmentTimeout = std::chrono::seconds(30); // Reduced timeout to prevent long hangs

                    spdlog::info("LoadSegments: Processing LOAD segment {}: vaddr=0x{:016X}, memsz=0x{:016X}, filesz=0x{:016X}",
                                 headerIndex, phdr.p_vaddr, phdr.p_memsz, phdr.p_filesz);

                    // Step 1: Allocate virtual memory
                    spdlog::info("LoadSegments: Step 1/4 - Allocating virtual memory for segment {} (size={}MB, {}KB, {} pages)",
                                headerIndex, phdr.p_memsz / (1024*1024), phdr.p_memsz / 1024, phdr.p_memsz / 4096);
                    uint64_t vaddr = phdr.p_vaddr + (isDynamic ? loadedElf.baseLoadAddress : 0);

                    auto allocStart = std::chrono::steady_clock::now();
                    uint64_t addr = m_emulator.GetOrbisOS().AllocateVirtualMemory(phdr.p_memsz, 4096, false);
                    auto allocDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::steady_clock::now() - allocStart);

                    spdlog::info("LoadSegments: Virtual memory allocation completed in {}ms", allocDuration.count());
                    if (!addr) {
                        spdlog::error("LoadSegments: Segment allocation failure for segment {}", headerIndex);
                        throw ElfLoadException("Segment allocation failure");
                    }

                    // Check timeout after allocation
                    if (std::chrono::steady_clock::now() - segmentStart > segmentTimeout) {
                        spdlog::error("LoadSegments: Timeout during allocation for segment {}", headerIndex);
                        throw ElfLoadException("Segment loading timeout during allocation");
                    }

                    // Step 2: Set memory protection
                    spdlog::debug("LoadSegments: Step 2/4 - Setting memory protection for segment {}", headerIndex);
                    int prot = (phdr.p_flags & elf::PF_R ? ps4::PROT_READ : 0) |
                               (phdr.p_flags & elf::PF_W ? ps4::PROT_WRITE : 0) |
                               (phdr.p_flags & elf::PF_X ? ps4::PROT_EXEC : 0);
                    m_emulator.GetOrbisOS().ProtectMemory(addr, phdr.p_memsz, prot);

                    // Step 3: Read segment data
                    spdlog::debug("LoadSegments: Step 3/4 - Reading segment data for segment {} (size=0x{:x})",
                                  headerIndex, phdr.p_filesz);
                    uint64_t fileOffset = phdr.p_offset;
                    std::vector<uint8_t> data(phdr.p_filesz);

                    if (loadedElf.isSelf) {
                        for (const auto& seg : loadedElf.selfSegments) {
                            if (seg.IsBlocked() && fileOffset >= phdr.p_offset &&
                                fileOffset < phdr.p_offset + phdr.p_filesz) {
                                fileOffset = (fileOffset - phdr.p_offset) + seg.file_offset;
                                break;
                            }
                        }
                        if (fileOffset + phdr.p_filesz <= decrypted_self_data.size()) {
                            data.assign(decrypted_self_data.begin() + fileOffset,
                                        decrypted_self_data.begin() + fileOffset + phdr.p_filesz);
                        } else {
                            throw ElfLoadException("Decrypted SELF data size mismatch for segment at offset 0x" + std::to_string(fileOffset));
                        }
                    } else {
                        if (phdr.p_filesz > 0) {
                            file.seekg(fileOffset);
                            file.read(reinterpret_cast<char*>(data.data()), phdr.p_filesz);
                            if (!file.good()) {
                                spdlog::error("LoadSegments: Segment data read failure for segment {}", headerIndex);
                                throw ElfLoadException("Segment data read failure");
                            }
                        }
                    }

                    // Check timeout after reading
                    if (std::chrono::steady_clock::now() - segmentStart > segmentTimeout) {
                        spdlog::error("LoadSegments: Timeout during data reading for segment {}", headerIndex);
                        throw ElfLoadException("Segment loading timeout during data reading");
                    }

                    // Step 4: Write data to virtual memory
                    if (phdr.p_filesz > 0) {
                        spdlog::info("LoadSegments: Step 4a/4 - Writing segment data to virtual memory (0x{:x} bytes)", phdr.p_filesz);
                        if (!m_emulator.GetMMU().WriteVirtual(addr, data.data(), phdr.p_filesz, m_currentProcessId)) {
                            spdlog::error("LoadSegments: Failed to write segment data for segment {}", headerIndex);
                            throw ElfLoadException("Segment data write failure");
                        }

                        // Check timeout after writing
                        if (std::chrono::steady_clock::now() - segmentStart > segmentTimeout) {
                            spdlog::error("LoadSegments: Timeout during data writing for segment {}", headerIndex);
                            throw ElfLoadException("Segment loading timeout during data writing");
                        }
                    }

                    // Step 5: Map virtual to physical
                    spdlog::debug("LoadSegments: Step 4b/4 - Mapping virtual to physical for segment {}", headerIndex);
                    if (!m_emulator.GetMMU().MapVirtualToPhysical(vaddr, addr, phdr.p_memsz, m_currentProcessId)) {
                        spdlog::error("LoadSegments: Failed to map virtual to physical for segment {}", headerIndex);
                        throw ElfLoadException("Virtual to physical mapping failure");
                    }

                    loadedElf.loadedSegments.push_back({addr, phdr.p_memsz, prot});
                    m_stats["memory_allocations"]++;
                    loadedSegmentCount++;

                    auto segmentEnd = std::chrono::steady_clock::now();
                    auto segmentDuration = std::chrono::duration_cast<std::chrono::milliseconds>(segmentEnd - segmentStart);
                    spdlog::info("LoadSegments: Successfully loaded segment {} in {}ms: addr=0x{:016X}, size=0x{:016X}, prot=0x{:X}",
                                 headerIndex, segmentDuration.count(), addr, phdr.p_memsz, prot);
                    break;
                }
                case elf::PT_DYNAMIC:
                    loadedElf.dynamicAddress = phdr.p_vaddr + (isDynamic ? loadedElf.baseLoadAddress : 0);
                    loadedElf.dynamicSize = phdr.p_memsz;
                    spdlog::debug("Found PT_DYNAMIC: addr=0x{:016X}, size=0x{:016X}", loadedElf.dynamicAddress, loadedElf.dynamicSize);
                    break;
                case elf::PT_PHDR:
                    loadedElf.phdrAddress = phdr.p_vaddr + (isDynamic ? loadedElf.baseLoadAddress : 0);
                    loadedElf.phdrEntrySize = elfHeader.e_phentsize;
                    loadedElf.phdrNum = elfHeader.e_phnum;
                    spdlog::debug("Found PT_PHDR: addr=0x{:016X}, entry_size=0x{:X}, num={}",
                                  loadedElf.phdrAddress, loadedElf.phdrEntrySize, loadedElf.phdrNum);
                    break;
                case elf::PT_SCE_DYNLIBDATA:
                    spdlog::debug("Found PT_SCE_DYNLIBDATA: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                case elf::PT_SCE_PROCPARAM:
                    spdlog::debug("Found PT_SCE_PROCPARAM: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                case elf::PT_SCE_MODULE_PARAM:
                    spdlog::debug("Found PT_SCE_MODULE_PARAM: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                case elf::PT_SCE_RELA:
                    spdlog::debug("Found PT_SCE_RELA: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                case elf::PT_SCE_COMMENT:
                    spdlog::debug("Found PT_SCE_COMMENT: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                case elf::PT_SCE_LIBVERSION:
                    spdlog::debug("Found PT_SCE_LIBVERSION: addr=0x{:016X}, size=0x{:016X}", phdr.p_vaddr, phdr.p_memsz);
                    break;
                default:
                    spdlog::debug("Unrecognized program header type: 0x{:08X}, addr=0x{:016X}, size=0x{:016X}",
                                 phdr.p_type, phdr.p_vaddr, phdr.p_memsz);
                    break;
            }
        }

        spdlog::debug("LoadSegments: Loaded {} LOAD segments", loadedSegmentCount);

        // Process section headers if present
        if (elfHeader.e_shoff != 0 && elfHeader.e_shnum > 0) {
            spdlog::debug("LoadSegments: Processing {} section headers from offset 0x{:016X}",
                          elfHeader.e_shnum, elfHeader.e_shoff);

            std::vector<elf::Elf64_Shdr> sections(elfHeader.e_shnum);
            file.seekg(elfHeader.e_shoff);
            file.read(reinterpret_cast<char*>(sections.data()), sections.size() * sizeof(elf::Elf64_Shdr));
            if (!file.good()) {
                spdlog::warn("LoadSegments: Section header read failure - continuing without sections");
            } else {
                for (const auto& shdr : sections) {
                    if (shdr.sh_type == elf::SHT_PS4_METADATA) {
                        loadedElf.metadataAddr = shdr.sh_addr + (isDynamic ? loadedElf.baseLoadAddress : 0);
                        loadedElf.metadataSize = shdr.sh_size;
                        spdlog::debug("Found PS4 metadata section: addr=0x{:016X}, size=0x{:016X}",
                                      loadedElf.metadataAddr, loadedElf.metadataSize);
                        m_stats["metadata_sections"]++;
                    }
                }
            }
        }

        spdlog::debug("LoadSegments: Completed successfully");
        return true;
    } catch (const ElfLoadException& e) {
        spdlog::error("Segment load failed: {}", e.what());
        return false;
    } catch (const std::exception& e) {
        spdlog::error("Unexpected error in segment load: {}", e.what());
        return false;
    }
}

bool ElfLoader::ParseDynamicSection(LoadedElf& loadedElf) {
    try {
        size_t numEntries = loadedElf.dynamicSize / sizeof(elf::Elf64_Dyn);
        std::vector<elf::Elf64_Dyn> entries(numEntries);
        m_emulator.GetMMU().ReadVirtual(loadedElf.dynamicAddress, entries.data(),
                                        loadedElf.dynamicSize, m_currentProcessId);

        for (const auto& entry : entries) {
            uint64_t value = entry.d_un.d_ptr;
            bool isPtr = false;
            switch (entry.d_tag) {
                case elf::DT_NULL: return true;
                case elf::DT_PLTGOT:
                case elf::DT_STRTAB:
                case elf::DT_SYMTAB:
                case elf::DT_RELA:
                case elf::DT_JMPREL:
                case elf::DT_SCE_MODULE_INFO:
                case elf::DT_SCE_NEEDED_MODULE:
                case elf::DT_SCE_EXPORT_LIB:
                case elf::DT_SCE_IMPORT_LIB:
                    isPtr = true;
                    break;
            }
            if (isPtr && loadedElf.baseLoadAddress != 0 && value != 0) {
                value += loadedElf.baseLoadAddress;
            }
            switch (entry.d_tag) {
                case elf::DT_NEEDED: break;
                case elf::DT_PLTRELSZ: loadedElf.relaPltSize = entry.d_un.d_val; break;
                case elf::DT_PLTGOT: loadedElf.pltGotAddr = value; break;
                case elf::DT_STRTAB: loadedElf.dynStrTableAddr = value; break;
                case elf::DT_SYMTAB: loadedElf.dynSymTableAddr = value; break;
                case elf::DT_RELA: loadedElf.relaDynAddr = value; break;
                case elf::DT_RELASZ: loadedElf.relaDynSize = entry.d_un.d_val; break;
                case elf::DT_RELAENT:
                    if (entry.d_un.d_val != sizeof(elf::Elf64_Rela))
                        throw ElfLoadException("Invalid RELA entry size");
                    break;
                case elf::DT_STRSZ: loadedElf.dynStrTableSize = entry.d_un.d_val; break;
                case elf::DT_SYMENT:
                    if (entry.d_un.d_val != sizeof(elf::Elf64_Sym))
                        throw ElfLoadException("Invalid symbol entry size");
                    break;
                case elf::DT_PLTREL:
                    if (entry.d_un.d_val != elf::DT_RELA)
                        throw ElfLoadException("Invalid PLT relocation type");
                    break;
                case elf::DT_JMPREL: loadedElf.relaPltAddr = value; break;
                case elf::DT_SCE_MODULE_INFO:
                case elf::DT_SCE_NEEDED_MODULE:
                case elf::DT_SCE_EXPORT_LIB:
                case elf::DT_SCE_IMPORT_LIB:
                    spdlog::debug("Processed PS4 dynamic tag: {}", entry.d_tag);
                    break;
                default: spdlog::debug("Ignored dynamic tag: {}", entry.d_tag); break;
            }
        }
        spdlog::trace("Parsed dynamic section: {} entries", numEntries);
        return true;
    } catch (const ElfLoadException& e) {
        spdlog::error("Dynamic section parsing failed: {}", e.what());
        return false;
    } catch (const std::exception& e) {
        spdlog::error("Unexpected error in dynamic section parsing: {}", e.what());
        return false;
    }
}

bool ElfLoader::ReadDynamicTables(LoadedElf& loadedElf) {
    try {
        if (loadedElf.dynSymTableAddr == 0 || loadedElf.dynStrTableAddr == 0 ||
            loadedElf.dynStrTableSize == 0) {
            spdlog::warn("No dynamic symbol or string table present");
            return false;
        }
        uint64_t symTableSize = (loadedElf.dynStrTableAddr > loadedElf.dynSymTableAddr)
                                    ? (loadedElf.dynStrTableAddr - loadedElf.dynSymTableAddr)
                                    : (4096 * sizeof(elf::Elf64_Sym));
        if (symTableSize == 0 || symTableSize % sizeof(elf::Elf64_Sym) != 0) {
            spdlog::error("Invalid symbol table size: {}", symTableSize);
            return false;
        }
        size_t numSymbols = symTableSize / sizeof(elf::Elf64_Sym);
        loadedElf.dynSymbols.resize(numSymbols);
        loadedElf.dynStringTable.resize(loadedElf.dynStrTableSize);
        m_emulator.GetMMU().ReadVirtual(loadedElf.dynSymTableAddr, loadedElf.dynSymbols.data(),
                                        symTableSize, m_currentProcessId);
        m_emulator.GetMMU().ReadVirtual(loadedElf.dynStrTableAddr, loadedElf.dynStringTable.data(),
                                        loadedElf.dynStrTableSize, m_currentProcessId);
        spdlog::info("Read dynamic tables: symbols={}, strings={} bytes", numSymbols, loadedElf.dynStrTableSize);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to read dynamic tables: {}", e.what());
        return false;
    }
}

bool ElfLoader::ProcessRelocations(LoadedElf& loadedElf, uint64_t relaAddr, uint64_t relaSize, bool isPlt) {
    try {
        if (relaAddr == 0 || relaSize == 0) {
            spdlog::trace("No relocations to process (isPlt={})", isPlt);
            return true;
        }
        size_t entrySize = sizeof(elf::Elf64_Rela);
        if (relaSize % entrySize != 0) throw ElfLoadException("Invalid relocation table size");
        size_t numRelocs = relaSize / entrySize;
        std::vector<elf::Elf64_Rela> relocs(numRelocs);
        m_emulator.GetMMU().ReadVirtual(relaAddr, relocs.data(), relaSize, m_currentProcessId);

        auto start = std::chrono::steady_clock::now();
        for (const auto& rela : relocs) {
            uint64_t offset = rela.r_offset + loadedElf.baseLoadAddress;
            uint64_t symIndex = ELF64_R_SYM(rela.r_info);
            uint32_t relaType = ELF64_R_TYPE(rela.r_info);
            uint64_t S = 0;
            uint64_t A = rela.r_addend;
            uint64_t B = loadedElf.baseLoadAddress;
            uint64_t P = offset;

            if (symIndex < loadedElf.dynSymbols.size() &&
                loadedElf.dynSymbols[symIndex].st_name < loadedElf.dynStringTable.size()) {
                std::string symName(&loadedElf.dynStringTable[loadedElf.dynSymbols[symIndex].st_name]);
                S = ResolveSymbol(symName, loadedElf);
            }

            uint64_t value = 0;
            try {
                switch (relaType) {
                    case elf::R_X86_64_RELATIVE:
                    case elf::R_X86_64_PS4_RELATIVE:
                        value = B + A;
                        m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t*>(&value),
                                                        sizeof(value), m_currentProcessId);
                        break;
                    case elf::R_X86_64_64:
                    case elf::R_X86_64_PS4_64:
                        value = S + A;
                        m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t*>(&value),
                                                        sizeof(value), m_currentProcessId);
                        break;
                    case elf::R_X86_64_GLOB_DAT:
                    case elf::R_X86_64_JUMP_SLOT:
                    case elf::R_X86_64_PS4_GLOB_DAT:
                        value = S;
                        m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t*>(&value),
                                                        sizeof(value), m_currentProcessId);
                        break;
                    case elf::R_X86_64_PC32:
                        {
                            value = S + A - P;
                            uint32_t v32 = static_cast<uint32_t>(value);
                            m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t*>(&v32),
                                                            sizeof(v32), m_currentProcessId);
                            break;
                        }
                    case elf::R_X86_64_COPY:
                        if (S == 0) throw ElfLoadException("Undefined symbol for COPY relocation");
                        m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t*>(&S),
                                                        sizeof(S), m_currentProcessId);
                        break;
                    default:
                        spdlog::warn("Unsupported relocation type {} at offset 0x{:x}", relaType, offset);
                        continue;
                }
                spdlog::trace("Applied relocation type={} at offset=0x{:x}, value=0x{:x}", relaType, offset, value);
            } catch (const std::exception& e) {
                spdlog::error("Relocation failed at 0x{:x}: {}", offset, e.what());
                throw ElfLoadException("Relocation application failure");
            }
        }
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            std::chrono::steady_clock::now() - start).count();
        m_stats[isPlt ? "plt_relocations" : "dyn_relocations"] += numRelocs;
        m_stats[isPlt ? "plt_relocation_time_us" : "dyn_relocation_time_us"] += duration;
        spdlog::debug("Processed {} relocations (isPlt={}) in {}us", numRelocs, isPlt, duration);
        return true;
    } catch (const ElfLoadException& e) {
        spdlog::error("Relocation processing failed (isPlt={}): {}", isPlt, e.what());
        return false;
    } catch (const std::exception& e) {
        spdlog::error("Unexpected error in relocation processing (isPlt={}): {}", isPlt, e.what());
        return false;
    }
}

uint64_t ElfLoader::ResolveSymbol(const std::string& name, LoadedElf& currentElf) {
    try {
        auto it = currentElf.resolvedSymbols.find(name);
        if (it != currentElf.resolvedSymbols.end()) {
            m_stats["symbol_cache_hits"]++;
            return it->second;
        }
        for (const auto& sym : currentElf.dynSymbols) {
            if (sym.st_name < currentElf.dynStringTable.size()) {
                std::string symName(&currentElf.dynStringTable[sym.st_name]);
                if (symName == name && sym.st_value != 0) {
                    uint64_t addr = sym.st_value + currentElf.baseLoadAddress;
                    currentElf.resolvedSymbols[name] = addr;
                    m_stats["symbol_resolutions"]++;
                    spdlog::trace("Resolved symbol '{}' to 0x{:x}", name, addr);
                    return addr;
                }
            }
        }
        spdlog::warn("Unresolved symbol: '{}'", name);
        m_stats["symbol_unresolved"]++;
        return 0;
    } catch (const std::exception& e) {
        spdlog::error("Symbol resolution failed for '{}': {}", name, e.what());
        return 0;
    }
}

} // namespace ps4