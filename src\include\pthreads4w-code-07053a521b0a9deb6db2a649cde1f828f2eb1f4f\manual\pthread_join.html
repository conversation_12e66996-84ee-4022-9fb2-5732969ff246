<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREAD_JOIN(3) manual page</TITLE>
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows &ndash; REFERENCE -
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_join - wait for termination of another thread</P>
<P>pthread_timedjoin_np - wait for termination of another thread with
a timeout 
</P>
<P>pthread_tryjoin_np &ndash; join another thread without waiting</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_join(pthread_t </B><I>th</I><B>, void
**</B><I>thread_return</I><B>);</B> 
</P>
<P><B>int pthread_timedjoin_np(pthread_t </B><I>th</I><B>, void
**</B><I>thread_return, </I><SPAN STYLE="font-style: normal"><B>const
struct timespec *</B></SPAN><I>abstime</I><B>);</B> 
</P>
<P><B>int pthread_tryjoin_np(pthread_t </B><I>th</I><B>, void
**</B><I>thread_return</I><B>);</B> 
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_join</B> suspends the execution of the calling thread
until the thread identified by <I>th</I> terminates, either by
calling <A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> or by
being cancelled. 
</P>
<P>If <I>thread_return</I> is not <B>NULL</B>, the return value of <I>th</I>
is stored in the location pointed to by <I>thread_return</I>. The
return value of <I>th</I> is either the argument it gave to
<A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> , or
<B>PTHREAD_CANCELED</B> if <I>th</I> was cancelled. 
</P>
<P>The joined thread <I><SPAN STYLE="font-weight: normal">th</SPAN></I>
must be in the joinable state: it must not have been detached using
<A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A> or the
<B>PTHREAD_CREATE_DETACHED</B> attribute to <A HREF="pthread_create.html"><B>pthread_create</B>(3)</A>
. 
</P>
<P>When a joinable thread terminates, its memory resources (thread
descriptor and stack) are not deallocated until another thread
performs <B>pthread_join</B> on it. Therefore, <B>pthread_join</B>
must be called once for each joinable thread created to avoid memory
leaks.</P>
<P><B>pthread_timedjoin_np</B> is identical to pthread_join except
that it will return the error ETIMEDOUT if the target thread <I>th</I>
has not exited before <I>abstime</I> passes. If <I>abstime</I> is
NULL the function will wait forever and it's behaviour will therefore
be identical to pthread_join.</P>
<P><B>pthread_tryjoin_np</B> is identical to pthread_join except that
it will return immediately with the error EBUSY if the target thread
<I>th</I> has not exited.</P>
<P>At most one thread can wait for the termination of a given thread.
Calling <B>pthread_join</B> on a thread <I>th</I> on which another
thread is already waiting for termination returns an error. 
</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P><B>pthread_join, pthread_tryjoin_np</B> <SPAN STYLE="font-weight: normal">and
</SPAN><B>pthread_timedjoin_np</B> are cancellation points. If a
thread is cancelled while suspended in <SPAN STYLE="font-weight: normal">either
function</SPAN>, the thread execution resumes immediately and the
cancellation is executed without waiting for the <I>th</I> thread to
terminate. If cancellation occurs during either function, the <I>th</I>
thread remains not joined. 
</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P>On success, the return value of <I>th</I> is stored in the
location pointed to by <I>thread_return</I>, and 0 is returned. On
error, a non-zero error code is returned. 
</P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<DL>
	<DT><B>ESRCH</B> 
	</DT><DD>
	No thread could be found corresponding to that specified by <I>th</I>.
		</DD><DT>
	<B>EINVAL</B> 
	</DT><DD>
	The <I>th</I> thread has been detached. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD>
	Another thread is already waiting on termination of <I>th</I>. 
	</DD><DT>
	<B>ETIMEDOUT</B> 
	</DT><DD>
	<SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">(pthread_timedjoin_np
	only):</SPAN></SPAN> <I>abstime</I> <SPAN STYLE="font-style: normal">passed
	before </SPAN><I>th</I> <SPAN STYLE="font-style: normal">could be
	joined</SPAN>. 
	</DD><DT>
	<B>EDEADLK</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The <I>th</I> argument refers to the calling thread. 
	</DD></DL>
<H2 CLASS="western">
<A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.
</P>
<H2 CLASS="western"><A HREF="#toc7" NAME="sect7">See Also</A></H2>
<P><A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> ,
<A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A> ,
<A HREF="pthread_create.html"><B>pthread_create</B>(3)</A> ,
<A HREF="pthread_attr_setdetachstate.html"><B>pthread_attr_setdetachstate</B>(3)</A>
, <A HREF="pthread_cleanup_push.html"><B>pthread_cleanup_push</B>(3)</A>
, <A HREF="pthread_key_create.html"><B>pthread_key_create</B>(3)</A>
. 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Author</A>
		</P>
	<LI><P><A HREF="#sect7" NAME="toc7">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>