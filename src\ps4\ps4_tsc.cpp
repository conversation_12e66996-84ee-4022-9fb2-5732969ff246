// Copyright 2025 <Copyright Owner>

#include "ps4_tsc.h"
#include "../common/lock_ordering.h"
#include <chrono>
#include <iostream>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <thread>

#if defined(_WIN32)
#include <windows.h>
#elif defined(__linux__)
#include <fstream>
#include <sstream>
#include <time.h>
#include <unistd.h>
#endif

namespace ps4 {

static PS4TSC g_tsc;

/**
 * @brief Constructs the TSC emulator.
 */
PS4TSC::PS4TSC()
    : m_tsc(0), m_cpuFrequency(1600000000), m_cyclesPerNanosecond(1.6) {
  auto start = std::chrono::steady_clock::now();
  m_stats = Stats();
  spdlog::info("PS4TSC constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4TSC::~PS4TSC() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("PS4TSC destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the TSC.
 * @return True on success, false otherwise.
 */
bool PS4TSC::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_calibrationMutex);
  try {
    m_tsc.store(0, std::memory_order_relaxed);
    m_cpuFrequency = EstimateHostFrequency();
    m_cyclesPerNanosecond = static_cast<double>(m_cpuFrequency) / 1e9;
    m_lastCalibration = std::chrono::high_resolution_clock::now();
    m_stats = Stats();
    Calibrate();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4TSC initialized with CPU frequency: {} Hz, latency={}us",
                 m_cpuFrequency, m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("PS4TSC initialization failed: {}", e.what());
    m_stats.cacheMisses++;
    throw TSCException("TSC initialization failed");
  }
}

/**
 * @brief Shuts down the TSC.
 */
void PS4TSC::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_calibrationMutex);
  try {
    m_tsc.store(0, std::memory_order_relaxed);
    m_stats = Stats();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4TSC shutdown, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("PS4TSC shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Gets the current TSC value.
 * @return TSC cycles.
 */
uint64_t PS4TSC::GetTSC() const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    uint64_t value = m_tsc.load(std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("GetTSC failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Gets the CPU frequency.
 * @return Frequency in Hz.
 */
uint64_t PS4TSC::GetCPUFrequency() const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return m_cpuFrequency;
  } catch (const std::exception &e) {
    spdlog::error("GetCPUFrequency failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return m_cpuFrequency;
  }
}

/**
 * @brief Increments the TSC.
 * @param cycles Cycles to add.
 */
void PS4TSC::IncrementTSC(uint64_t cycles) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_calibrationMutex);
  try {
    m_tsc.fetch_add(cycles, std::memory_order_relaxed);
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                       now - m_lastCalibration)
                       .count();
    if (elapsed >= 60) {
      Calibrate();
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::trace("Incremented TSC by {} cycles, total={}, latency={}us",
                  cycles, m_tsc.load(), latency);
  } catch (const std::exception &e) {
    spdlog::error("IncrementTSC failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Converts TSC cycles to nanoseconds.
 * @param cycles TSC cycles.
 * @return Nanoseconds.
 */
uint64_t PS4TSC::TSCToNanoseconds(uint64_t cycles) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    uint64_t ns = static_cast<uint64_t>(static_cast<double>(cycles) /
                                        m_cyclesPerNanosecond);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return ns;
  } catch (const std::exception &e) {
    spdlog::error("TSCToNanoseconds failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Converts nanoseconds to TSC cycles.
 * @param nanoseconds Nanoseconds.
 * @return TSC cycles.
 */
uint64_t PS4TSC::NanosecondsToTSC(uint64_t nanoseconds) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    uint64_t cycles = static_cast<uint64_t>(static_cast<double>(nanoseconds) *
                                            m_cyclesPerNanosecond);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return cycles;
  } catch (const std::exception &e) {
    spdlog::error("NanosecondsToTSC failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Calibrates the TSC with the host.
 * @note Assumes the caller already holds m_calibrationMutex.
 */
void PS4TSC::Calibrate() {
  auto start = std::chrono::steady_clock::now();
  try {
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::nanoseconds>(
                       now - m_lastCalibration)
                       .count();
    if (elapsed <= 0) {
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    uint64_t hostFrequency = EstimateHostFrequency();
    uint64_t expectedIncrement = static_cast<uint64_t>(
        static_cast<double>(elapsed) * (hostFrequency / 1e9));
    uint64_t currentTSC = m_tsc.load(std::memory_order_relaxed);
    uint64_t targetTSC = currentTSC + expectedIncrement;
    double drift = static_cast<double>(targetTSC - currentTSC) /
                   (currentTSC ? currentTSC : 1) * 100.0;
    m_stats.driftPercent.store(std::abs(drift), std::memory_order_relaxed);
    m_tsc.store(targetTSC, std::memory_order_relaxed);
    m_lastCalibration = now;
    m_stats.calibrationCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    // Multi-core TSC synchronization simulation
    if (m_stats.calibrationCount.load(std::memory_order_relaxed) % 10 ==
        0) { // Sync every 10 calibrations
      spdlog::trace("Performing multi-core TSC synchronization");
      uint64_t syncTSC = m_tsc.load(std::memory_order_acquire);
      spdlog::debug("TSC synchronized across cores: value=0x{:x}", syncTSC);
      m_stats.syncCount.fetch_add(1, std::memory_order_relaxed);
    }

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4TSC calibrated: elapsed={}ns, increment={} cycles, "
                 "host_freq={} Hz, drift={:.2f}%, latency={}us",
                 elapsed, expectedIncrement, hostFrequency,
                 m_stats.driftPercent.load(std::memory_order_relaxed), latency);
  } catch (const std::exception &e) {
    spdlog::error("Calibrate failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Estimates the host CPU frequency using platform-specific APIs.
 * @return Estimated frequency in Hz.
 */
uint64_t PS4TSC::EstimateHostFrequency() const {
  auto start = std::chrono::steady_clock::now();
  try {
#if defined(_WIN32)
    LARGE_INTEGER freq;
    if (QueryPerformanceFrequency(&freq)) {
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs.fetch_add(
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count(),
          std::memory_order_relaxed);
      return static_cast<uint64_t>(freq.QuadPart);
    } else {
      throw std::runtime_error("QueryPerformanceFrequency failed");
    }
#elif defined(__linux__)
    // Method 1: Try TSC frequency from sysfs (most accurate)
    std::ifstream tscFreqFile("/sys/devices/system/cpu/cpu0/tsc_freq_khz");
    if (tscFreqFile.is_open()) {
      uint64_t tscKhz;
      if (tscFreqFile >> tscKhz) {
        tscFreqFile.close();
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        spdlog::debug("TSC frequency from sysfs: {} KHz", tscKhz);
        return tscKhz * 1000; // Convert KHz to Hz
      }
      tscFreqFile.close();
    }

    // Method 2: Try CPUID instruction (x86-64 specific)
#if defined(__x86_64__) || defined(__i386__)
    uint32_t eax, ebx, ecx, edx;
    // Check if TSC frequency is available via CPUID
    asm volatile("cpuid"
                 : "=a"(eax), "=b"(ebx), "=c"(ecx), "=d"(edx)
                 : "a"(0x15)); // CPUID leaf 0x15: TSC/Crystal Clock Information
    if (ebx != 0 && ecx != 0) {
      uint64_t crystalHz = ecx;
      uint64_t tscRatio = ebx;
      uint64_t tscFreq = (crystalHz * tscRatio) / eax;
      if (tscFreq > 0) {
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        spdlog::debug("TSC frequency from CPUID: {} Hz", tscFreq);
        return tscFreq;
      }
    }
#endif

    // Method 3: Try CPU max frequency from cpufreq
    std::ifstream maxFreqFile(
        "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq");
    if (maxFreqFile.is_open()) {
      uint64_t maxKhz;
      if (maxFreqFile >> maxKhz) {
        maxFreqFile.close();
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        spdlog::debug("CPU max frequency from cpufreq: {} KHz", maxKhz);
        return maxKhz * 1000; // Convert KHz to Hz
      }
      maxFreqFile.close();
    }

    // Method 4: Parse /proc/cpuinfo (legacy method)
    std::ifstream cpuinfo("/proc/cpuinfo");
    std::string line;
    while (std::getline(cpuinfo, line)) {
      if (line.find("cpu MHz") != std::string::npos) {
        std::istringstream iss(line);
        std::string token;
        iss >> token >> token >> token; // Skip to frequency value
        double mhz;
        iss >> mhz;
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        spdlog::debug("CPU frequency from /proc/cpuinfo: {} MHz", mhz);
        return static_cast<uint64_t>(mhz * 1e6);
      }
    }
    cpuinfo.close();

    // Method 5: Calibrate using clock_gettime (most robust fallback)
    struct timespec start_ts, end_ts;
    if (clock_gettime(CLOCK_MONOTONIC_RAW, &start_ts) == 0) {
      uint64_t start_tsc, end_tsc;

#if defined(__x86_64__) || defined(__i386__)
      // Read TSC at start
      unsigned int lo, hi;
      asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
      start_tsc = (static_cast<uint64_t>(hi) << 32) | lo;

      // Sleep for a short period
      struct timespec sleep_ts = {0, 10000000}; // 10ms
      nanosleep(&sleep_ts, nullptr);

      // Read TSC at end
      asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
      end_tsc = (static_cast<uint64_t>(hi) << 32) | lo;

      if (clock_gettime(CLOCK_MONOTONIC_RAW, &end_ts) == 0) {
        uint64_t elapsed_ns =
            (end_ts.tv_sec - start_ts.tv_sec) * 1000000000ULL +
            (end_ts.tv_nsec - start_ts.tv_nsec);
        uint64_t elapsed_tsc = end_tsc - start_tsc;

        if (elapsed_ns > 0 && elapsed_tsc > 0) {
          uint64_t tscFreq = (elapsed_tsc * 1000000000ULL) / elapsed_ns;
          m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
          auto end = std::chrono::steady_clock::now();
          m_stats.totalLatencyUs.fetch_add(
              std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                  .count(),
              std::memory_order_relaxed);
          spdlog::debug("TSC frequency from calibration: {} Hz", tscFreq);
          return tscFreq;
        }
      }
#endif
    }

    throw std::runtime_error("Failed to estimate CPU frequency on Linux");
#else
    // Fallback for unsupported platforms
    spdlog::warn(
        "No platform-specific frequency detection available, using default");
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return 1600000000; // Default 1.6 GHz
#endif
  } catch (const std::exception &e) {
    spdlog::error("EstimateHostFrequency failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return 1600000000; // Fallback to default 1.6 GHz
  }
}

/**
 * @brief Retrieves TSC statistics.
 * @return Current statistics.
 */
PS4TSC::Stats PS4TSC::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return m_stats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return m_stats;
  }
}

/**
 * @brief Saves the TSC state to a stream.
 * @param out Output stream.
 */
void PS4TSC::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_calibrationMutex, "TSCMutex");
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t tscValue = m_tsc.load(std::memory_order_relaxed);
    out.write(reinterpret_cast<const char *>(&tscValue), sizeof(tscValue));
    out.write(reinterpret_cast<const char *>(&m_cpuFrequency),
              sizeof(m_cpuFrequency));
    out.write(reinterpret_cast<const char *>(&m_cyclesPerNanosecond),
              sizeof(m_cyclesPerNanosecond));
    auto duration = m_lastCalibration.time_since_epoch().count();
    out.write(reinterpret_cast<const char *>(&duration), sizeof(duration));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw std::runtime_error("Failed to write TSC state");
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("SaveState: Saved TSC state, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Loads the TSC state from a stream.
 * @param in Input stream.
 */
void PS4TSC::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_calibrationMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported TSC state version: {}", version);
      throw std::runtime_error("Invalid TSC state version");
    }
    uint64_t tscValue;
    in.read(reinterpret_cast<char *>(&tscValue), sizeof(tscValue));
    m_tsc.store(tscValue, std::memory_order_relaxed);
    in.read(reinterpret_cast<char *>(&m_cpuFrequency), sizeof(m_cpuFrequency));
    in.read(reinterpret_cast<char *>(&m_cyclesPerNanosecond),
            sizeof(m_cyclesPerNanosecond));
    int64_t duration;
    in.read(reinterpret_cast<char *>(&duration), sizeof(duration));
    m_lastCalibration = std::chrono::high_resolution_clock::time_point(
        std::chrono::nanoseconds(duration));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw std::runtime_error("Failed to read TSC state");
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("LoadState: Loaded TSC state, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

extern "C" {
uint64_t sceKernelReadTsc() {
  try {
    return g_tsc.GetTSC();
  } catch (const std::exception &e) {
    spdlog::error("sceKernelReadTsc failed: {}", e.what());
    return 0;
  }
}

uint64_t sceKernelGetTscFrequency() {
  try {
    return g_tsc.GetCPUFrequency();
  } catch (const std::exception &e) {
    spdlog::error("sceKernelGetTscFrequency failed: {}", e.what());
    return 1600000000;
  }
}

int sceKernelDelay(uint64_t microseconds) {
  try {
    uint64_t nanoseconds = microseconds * 1000;
    uint64_t cycles = g_tsc.NanosecondsToTSC(nanoseconds);
    g_tsc.IncrementTSC(cycles);
    std::this_thread::sleep_for(std::chrono::microseconds(microseconds));
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceKernelDelay failed: {}", e.what());
    return -1;
  }
}

int sceKernelUsleep(uint64_t microseconds) {
  return sceKernelDelay(microseconds);
}
}

} // namespace ps4