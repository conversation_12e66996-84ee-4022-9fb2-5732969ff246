// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <cstring>
#include <iostream>
#include <stdexcept>

namespace elf {

struct ELFException : std::runtime_error {
  explicit ELFException(const std::string &msg) : std::runtime_error(msg) {}
};

// ELF identification indices
enum {
  EI_MAG0 = 0, EI_MAG1 = 1, EI_MAG2 = 2, EI_MAG3 = 3, EI_CLASS = 4,
  EI_DATA = 5, EI_VERSION = 6, EI_OSABI = 7, EI_ABIVERSION = 8, EI_NIDENT = 16
};

// ELF magic number
static constexpr unsigned char ElfMagic[] = {0x7f, 'E', 'L', 'F'};
static constexpr size_t ElfMagicLen = sizeof(ElfMagic);

// ELF file classes
enum { ELFCLASS32 = 1, ELFCLASS64 = 2 };

// ELF data encoding
enum { ELFDATA2LSB = 1, ELFDATA2MSB = 2 };

// ELF object file types
constexpr uint16_t ET_EXEC = 2;
constexpr uint16_t ET_DYN = 3;
constexpr uint16_t ET_SCE_EXEC = 0xFE00;
constexpr uint16_t ET_SCE_DYNEXEC = 0xFE10;
constexpr uint16_t ET_SCE_DYNAMIC = 0xFE18;

// ELF machine types
enum { EM_X86_64 = 62 };

// ELF program header types
constexpr uint32_t PT_LOAD = 1;
constexpr uint32_t PT_DYNAMIC = 2;
constexpr uint32_t PT_PHDR = 6;
constexpr uint32_t PT_SCE_RELA = 0x60000000;
constexpr uint32_t PT_SCE_DYNLIBDATA = 0x61000000;
constexpr uint32_t PT_SCE_PROCPARAM = 0x61000001;
constexpr uint32_t PT_SCE_MODULE_PARAM = 0x61000002;
constexpr uint32_t PT_SCE_RELRO = 0x61000010;
constexpr uint32_t PT_SCE_COMMENT = 0x6FFFFF00;
constexpr uint32_t PT_SCE_LIBVERSION = 0x6FFFFF01;

// ELF program header flags
constexpr uint32_t PF_X = 1;
constexpr uint32_t PF_W = 2;
constexpr uint32_t PF_R = 4;

// ELF section header types
enum {
  SHT_NULL = 0, SHT_PROGBITS = 1, SHT_SYMTAB = 2, SHT_STRTAB = 3, SHT_RELA = 4,
  SHT_REL = 9, SHT_DYNSYM = 11, SHT_PS4_METADATA = 0x60000000
};

// ELF section header flags
enum { SHF_WRITE = 1, SHF_ALLOC = 2, SHF_EXECINSTR = 4 };

// SELF header structure (from shadPS4)
struct self_header {
  static constexpr uint32_t signature = 0x1D3D154Fu;
  uint32_t magic;
  uint8_t version;
  uint8_t mode;
  uint8_t endian; // 1 = little endian
  uint8_t attributes;
  uint8_t category;
  uint8_t program_type;
  uint16_t padding1;
  uint16_t header_size;
  uint16_t meta_size;
  uint32_t file_size;
  uint32_t padding2;
  uint16_t segment_count;
  uint16_t unknown1A; // Typically 0x22
  uint32_t padding3;

  bool isValid() const {
    return magic == signature && version == 0x00 && mode == 0x01 && endian == 0x01 &&
           attributes == 0x12 && category == 0x01 && program_type == 0x01;
  }
};

// SELF segment header structure (from shadPS4)
struct self_segment_header {
  bool IsBlocked() const { return (flags & 0x800) != 0; }
  uint32_t GetId() const { return (flags >> 20) & 0xFFF; }
  bool IsEncrypted() const { return (flags & 2) != 0; }
  bool IsCompressed() const { return (flags & 8) != 0; }

  uint64_t flags;
  uint64_t file_offset;
  uint64_t file_size;
  uint64_t memory_size;
};

// ELF64 header structure
struct Elf64_Ehdr {
  unsigned char e_ident[EI_NIDENT];
  uint16_t e_type;
  uint16_t e_machine;
  uint32_t e_version;
  uint64_t e_entry;
  uint64_t e_phoff;
  uint64_t e_shoff;
  uint32_t e_flags;
  uint16_t e_ehsize;
  uint16_t e_phentsize;
  uint16_t e_phnum;
  uint16_t e_shentsize;
  uint16_t e_shnum;
  uint16_t e_shstrndx;

  bool isValidElf64() const {
    return std::memcmp(e_ident, ElfMagic, ElfMagicLen) == 0 &&
           e_ident[EI_CLASS] == ELFCLASS64 &&
           e_ident[EI_DATA] == ELFDATA2LSB &&
           e_ident[EI_VERSION] == 1 &&
           e_version == 1 &&
           e_machine == EM_X86_64 &&
           e_ehsize >= sizeof(Elf64_Ehdr);
  }
};

// ELF64 program header structure
struct Elf64_Phdr {
  uint32_t p_type;
  uint32_t p_flags;
  uint64_t p_offset;
  uint64_t p_vaddr;
  uint64_t p_paddr;
  uint64_t p_filesz;
  uint64_t p_memsz;
  uint64_t p_align;

  bool isValid() const { return p_align == 0 || (p_align & (p_align - 1)) == 0; }

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported program header version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read program header");
  }
};

// ELF64 section header structure
struct Elf64_Shdr {
  uint32_t sh_name;
  uint32_t sh_type;
  uint64_t sh_flags;
  uint64_t sh_addr;
  uint64_t sh_offset;
  uint64_t sh_size;
  uint32_t sh_link;
  uint32_t sh_info;
  uint64_t sh_addralign;
  uint64_t sh_entsize;

  bool isValid() const { return sh_addralign == 0 || (sh_addralign & (sh_addralign - 1)) == 0; }

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported section header version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read section header");
  }
};

// ELF64 symbol structure
struct Elf64_Sym {
  uint32_t st_name;
  unsigned char st_info;
  unsigned char st_other;
  uint16_t st_shndx;
  uint64_t st_value;
  uint64_t st_size;

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported symbol version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read symbol");
  }
};

// ELF64 dynamic section structure
struct Elf64_Dyn {
  int64_t d_tag;
  union {
    uint64_t d_val;
    uint64_t d_ptr;
  } d_un;

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported dynamic entry version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read dynamic entry");
  }
};

// Dynamic section tags (including PS4-specific from shadPS4)
constexpr int64_t DT_NULL = 0;
constexpr int64_t DT_NEEDED = 1;
constexpr int64_t DT_PLTRELSZ = 2;
constexpr int64_t DT_PLTGOT = 3;
constexpr int64_t DT_STRTAB = 5;
constexpr int64_t DT_SYMTAB = 6;
constexpr int64_t DT_RELA = 7;
constexpr int64_t DT_RELASZ = 8;
constexpr int64_t DT_RELAENT = 9;
constexpr int64_t DT_STRSZ = 10;
constexpr int64_t DT_SYMENT = 11;
constexpr int64_t DT_PLTREL = 20;
constexpr int64_t DT_JMPREL = 23;
constexpr int64_t DT_SCE_MODULE_INFO = 0x6100000D;
constexpr int64_t DT_SCE_NEEDED_MODULE = 0x6100000F;
constexpr int64_t DT_SCE_EXPORT_LIB = 0x61000013;
constexpr int64_t DT_SCE_IMPORT_LIB = 0x61000015;

// Symbol binding and type macros
#define ELF64_ST_BIND(val) ((val) >> 4)
#define ELF64_ST_TYPE(val) ((val) & 0xf)
#define ELF64_ST_INFO(bind, type) (((bind) << 4) | ((type) & 0xf))

// Symbol binding types
constexpr int STB_LOCAL = 0;
constexpr int STB_GLOBAL = 1;
constexpr int STB_WEAK = 2;

// Symbol types
enum {
  STT_NOTYPE = 0, STT_OBJECT = 1, STT_FUNC = 2, STT_SECTION = 3, STT_SCE = 11
};

// ELF64 relocation entry (without addend)
struct Elf64_Rel {
  uint64_t r_offset;
  uint64_t r_info;

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported relocation version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read relocation entry");
  }
};

// ELF64 relocation entry (with addend)
struct Elf64_Rela {
  uint64_t r_offset;
  uint64_t r_info;
  int64_t r_addend;

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ELFException("Unsupported relocation version");
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) throw ELFException("Failed to read relocation entry");
  }
};

// Relocation macros
#define ELF64_R_SYM(info) ((info) >> 32)
#define ELF64_R_TYPE(info) ((info) & 0xffffffffL)
#define ELF64_R_INFO(sym, type) (((uint64_t)(sym) << 32) + (type))

// Standard x86_64 relocation types
constexpr uint32_t R_X86_64_NONE = 0;
constexpr uint32_t R_X86_64_64 = 1;
constexpr uint32_t R_X86_64_PC32 = 2;
constexpr uint32_t R_X86_64_COPY = 5;
constexpr uint32_t R_X86_64_GLOB_DAT = 6;
constexpr uint32_t R_X86_64_JUMP_SLOT = 7;
constexpr uint32_t R_X86_64_RELATIVE = 8;

// PS4-specific relocation types
constexpr uint32_t R_X86_64_PS4_64 = 0x60000000;
constexpr uint32_t R_X86_64_PS4_GLOB_DAT = 0x60000001;
constexpr uint32_t R_X86_64_PS4_RELATIVE = 0x60000002;

} // namespace elf