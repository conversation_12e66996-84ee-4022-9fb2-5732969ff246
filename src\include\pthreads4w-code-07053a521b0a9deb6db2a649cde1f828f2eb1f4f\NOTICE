PThreads4W - POSIX threads for Windows
Copyright 1998 John <PERSON>
Copyright 1999-2018, Pthreads4w contributors

This product includes software developed through the colaborative
effort of several individuals, each of whom is listed in the file
CONTRIBUTORS included with this software.

The following files are not covered under the Copyrights
listed above:

    [1] tests/rwlock7.c
    [1] tests/rwlock7_1.c
    [1] tests/rwlock8.c
    [1] tests/rwlock8_1.c
    [2] tests/threestage.c

[1] The file tests/rwlock7.c and those similarly named are derived from
code written by <PERSON> for his book 'Programming With POSIX(R)
Threads'. The original code was obtained by free download from his
website http://home.earthlink.net/~anneart/family/Threads/source.html

[2] The file tests/threestage.c is taken directly from examples in the
book "Windows System Programming, Edition 4" by <PERSON> (<PERSON>) Hart
Session 6, Chapter 10. ThreeStage.c
Several required additional header and source files from the
book examples have been included inline to simplify compilation.
The only modification to the code has been to provide default
values when run without arguments.
