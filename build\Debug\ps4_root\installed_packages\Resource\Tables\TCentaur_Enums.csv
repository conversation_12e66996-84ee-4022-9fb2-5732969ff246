RSID_TCENTAUR_START, 3700,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON>\PBTCentaur, 1,
 ,[Offset], Centaur\InstructionsENG, 2,
 ,[Offset], Centaur\InstructionsFR, 3,
 ,[Offset], Ce<PERSON>ur\InstructionsITAL, 4,
 ,[Offset], Centaur\InstructionsGERM, 5,
 ,[Offset], Centaur\InstructionsSPAN, 6,
 ,[Offset], Centaur\InstructionsPORT, 7,
 ,[Offset], Centaur\InstructionsDUTCH, 8,
 ,[Offset], Centaur\InstructionsDUTCH, 9,
RSID_TCENTAUR_LIGHTS, 3701,,,
RSID_TCENTAUR_CAMERAS, 3702,,,
RSID_TCENTAUR_LAMP_TEXTURES, 3703,,,
 ,[Offset], CentaurCameras, 0,
 ,[Offset], CentaurCameras, 1,
 ,[Offset], L_02_off, 2,
 ,[Offset], L_02_on, 3,
 ,[Offset], L_03_off, 4,
 ,[Offset], L_03_on, 5,
 ,[Offset], L_04_off, 6,
 ,[Offset], L_04_on, 7,
 ,[Offset], L_05_off, 8,
 ,[Offset], L_05_on, 9,
 ,[Offset], L_06_off, 10,
 ,[Offset], L_06_on, 11,
 ,[Offset], L_07_off, 12,
 ,[Offset], L_07_on, 13,
 ,[Offset], L_08_off, 14,
 ,[Offset], L_08_on, 15,
 ,[Offset], L_09_off, 16,
 ,[Offset], L_09_on, 17,
 ,[Offset], L_10_off, 18,
 ,[Offset], L_10_on, 19,
 ,[Offset], L_11_off, 20,
 ,[Offset], L_11_on, 21,
 ,[Offset], L_12_off, 22,
 ,[Offset], L_12_on, 23,
 ,[Offset], L_13_off, 24,
 ,[Offset], L_13_on, 25,
 ,[Offset], L_14_off, 26,
 ,[Offset], L_14_on, 27,
 ,[Offset], L_15_off, 28,
 ,[Offset], L_15_on, 29,
 ,[Offset], L_16_off, 30,
 ,[Offset], L_16_on, 31,
 ,[Offset], L_17_off, 32,
 ,[Offset], L_17_on, 33,
 ,[Offset], L_18_off, 34,
 ,[Offset], L_18_on, 35,
 ,[Offset], L_19_off, 36,
 ,[Offset], L_19_on, 37,
 ,[Offset], L_20_off, 38,
 ,[Offset], L_20_on, 39,
 ,[Offset], L_21_off, 40,
 ,[Offset], L_21_on, 41,
 ,[Offset], L_22_off, 42,
 ,[Offset], L_22_on, 43,
 ,[Offset], L_23_off, 44,
 ,[Offset], L_23_on, 45,
 ,[Offset], L_24_off, 46,
 ,[Offset], L_24_on, 47,
 ,[Offset], L_25_off, 48,
 ,[Offset], L_25_on, 49,
 ,[Offset], L_26_off, 50,
 ,[Offset], L_26_on, 51,
 ,[Offset], L_27_off, 52,
 ,[Offset], L_27_on, 53,
 ,[Offset], L_28_off, 54,
 ,[Offset], L_28_on, 55,
 ,[Offset], L_29_off, 56,
 ,[Offset], L_29_on, 57,
 ,[Offset], L_30_off, 58,
 ,[Offset], L_30_on, 59,
 ,[Offset], L_31_off, 60,
 ,[Offset], L_31_on, 61,
 ,[Offset], L_32_off, 62,
 ,[Offset], L_32_on, 63,
 ,[Offset], L_33_off, 64,
 ,[Offset], L_33_on, 65,
 ,[Offset], L_34_off, 66,
 ,[Offset], L_34_on, 67,
 ,[Offset], L_35_off, 68,
 ,[Offset], L_35_on, 69,
 ,[Offset], L_36_off, 70,
 ,[Offset], L_36_on, 71,
 ,[Offset], L_37_off, 72,
 ,[Offset], L_37_on, 73,
 ,[Offset], L_38_off, 74,
 ,[Offset], L_38_on, 75,
 ,[Offset], L_39_off, 76,
 ,[Offset], L_39_on, 77,
 ,[Offset], L_40_off, 78,
 ,[Offset], L_40_on, 79,
 ,[Offset], L_40_on, 80,
 ,[Offset], L_40_on, 81,
 ,[Offset], L_40_on, 82,
 ,[Offset], L_40_on, 83,
 ,[Offset], L_40_on, 84,
 ,[Offset], L_40_on, 85,
 ,[Offset], L_40_on, 86,
 ,[Offset], L_40_on, 87,
 ,[Offset], L_45_off, 88,
 ,[Offset], L_45_on, 89,
 ,[Offset], L_46_off, 90,
 ,[Offset], L_46_on, 91,
 ,[Offset], L_47_off, 92,
 ,[Offset], L_47_on, 93,
 ,[Offset], L_48_off, 94,
 ,[Offset], L_48_on, 95,
 ,[Offset], L_48_on, 96,
 ,[Offset], L_48_on, 97,
 ,[Offset], L_48_on, 98,
 ,[Offset], L_48_on, 99,
 ,[Offset], L_48_on, 100,
 ,[Offset], L_48_on, 101,
 ,[Offset], L_48_on, 102,
 ,[Offset], L_48_on, 103,
 ,[Offset], L_53_off, 104,
 ,[Offset], L_53_on, 105,
 ,[Offset], L_54_off, 106,
 ,[Offset], L_54_on, 107,
 ,[Offset], L_55_off, 108,
 ,[Offset], L_55_on, 109,
 ,[Offset], L_56_off, 110,
 ,[Offset], L_56_on, 111,
 ,[Offset], L_57_off, 112,
 ,[Offset], L_57_on, 113,
 ,[Offset], L_58_off, 114,
 ,[Offset], L_58_on, 115,
 ,[Offset], L_59_off, 116,
 ,[Offset], L_59_on, 117,
 ,[Offset], L_60_off, 118,
 ,[Offset], L_60_on, 119,
 ,[Offset], L_61_off, 120,
 ,[Offset], L_61_on, 121,
 ,[Offset], L_62_off, 122,
 ,[Offset], L_62_on, 123,
 ,[Offset], L_63_off, 124,
 ,[Offset], L_63_on, 125,
 ,[Offset], L_63_on, 126,
 ,[Offset], L_63_on, 127,
 ,[Offset], L_63_on, 128,
 ,[Offset], L_63_on, 129,
 ,[Offset], L_63_on, 130,
 ,[Offset], L_63_on, 131,
 ,[Offset], L_67_off, 132,
 ,[Offset], L_67_on, 133,
 ,[Offset], L_68_off, 134,
 ,[Offset], L_68_on, 135,
RSID_TCENTAUR_TEXTURES, 3704,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass, 1,
 ,[Offset], black_tile, 2,
 ,[Offset], black_wood, 3,
 ,[Offset], bulb, 4,
 ,[Offset], cabinet front, 5,
 ,[Offset], cabinet, 6,
 ,[Offset], cabinet_metal, 7,
 ,[Offset], Centaur_Flipper, 8,
 ,[Offset], Centaur_MetalGate, 9,
 ,[Offset], Centaur_PopBumper, 10,
 ,[Offset], chrome_button, 11,
 ,[Offset], coinbox, 12,
 ,[Offset], coinslot, 13,
 ,[Offset], Extra_Metal_Parts, 14,
 ,[Offset], Flipper_Button, 15,
 ,[Offset], Generic_Metal, 16,
 ,[Offset], legs, 17,
 ,[Offset], metal front, 18,
 ,[Offset], metal_parts01, 19,
 ,[Offset], Metal_Walls, 20,
 ,[Offset], plastic_post, 21,
 ,[Offset], plastic1, 22,
 ,[Offset], plastic2, 23,
 ,[Offset], plastic3, 24,
 ,[Offset], Plunger_Plate, 25,
 ,[Offset], PopBumperBody, 26,
 ,[Offset], red plastic gates, 27,
 ,[Offset], Red_targets, 28,
 ,[Offset], rubberband, 29,
 ,[Offset], Silver_screws, 30,
 ,[Offset], speaker, 31,
 ,[Offset], targets, 32,
 ,[Offset], white_nut, 33,
 ,[Offset], white_tiles, 34,
 ,[Offset], post, 35,
 ,[Offset], blue_posts, 36,
 ,[Offset], top_playfield, 37,
 ,[Offset], bottom_playfield, 38,
 ,[Offset], Bumper rubber2, 39,
 ,[Offset], Plunger, 40,
 ,[Offset], metal_plate, 41,
 ,[Offset], Centaur_LED_Border, 42,
 ,[Offset], pop_bumper_plastic, 43,
 ,[Offset], inside walls, 44,
 ,[Offset], plastic3A, 45,
RSID_TCENTAUR_MODELS, 3705,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass, 1,
 ,[Offset], bulbs, 2,
 ,[Offset], buttons, 3,
 ,[Offset], cabinet, 4,
 ,[Offset], cabinet_metal, 5,
 ,[Offset], legs, 6,
 ,[Offset], metal_parts, 7,
 ,[Offset], metal_walls, 8,
 ,[Offset], nuts, 9,
 ,[Offset], plastic_gates, 10,
 ,[Offset], plastic_posts, 11,
 ,[Offset], plastics, 12,
 ,[Offset], playfield, 13,
 ,[Offset], pop_bumpers, 14,
 ,[Offset], rubbers, 15,
 ,[Offset], screws, 16,
 ,[Offset], wooden_rails, 17,
 ,[Offset], 1_white_tile, 18,
 ,[Offset], 2_white_tile, 19,
 ,[Offset], 3_white_tile, 20,
 ,[Offset], 4_white_tile, 21,
 ,[Offset], B_white_tile, 22,
 ,[Offset], black_drop_tile, 23,
 ,[Offset], bumper, 24,
 ,[Offset], left_flipper, 25,
 ,[Offset], left_slingshot, 26,
 ,[Offset], O_white_tile, 27,
 ,[Offset], one_way_gate, 28,
 ,[Offset], plunger, 29,
 ,[Offset], popbumper_ring, 30,
 ,[Offset], R_white_tile, 31,
 ,[Offset], red_target, 32,
 ,[Offset], right_flipper, 33,
 ,[Offset], right_slingshot, 34,
 ,[Offset], S_white_tile, 35,
 ,[Offset], white_target, 36,
 ,[Offset], wire, 37,
 ,[Offset], wire_one_way_gate, 38,
 ,[Offset], metal_plate_down, 39,
 ,[Offset], metal_plate_up, 40,
 ,[Offset], Light_Cutouts, 41,
 ,[Offset], left_slingshot_extended, 42,
 ,[Offset], right_slingshot_extended, 43,
 ,[Offset], pop_bumper_plastic, 44,
 ,[Offset], rollover_stars, 45,
RSID_TCENTAUR_MODELS_LODS, 3706,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass, 1,
 ,[Offset], bulbs, 2,
 ,[Offset], buttons, 3,
 ,[Offset], cabinet, 4,
 ,[Offset], cabinet_metal, 5,
 ,[Offset], legs, 6,
 ,[Offset], metal_parts, 7,
 ,[Offset], metal_walls, 8,
 ,[Offset], nuts, 9,
 ,[Offset], plastic_gates, 10,
 ,[Offset], plastic_posts, 11,
 ,[Offset], plastics, 12,
 ,[Offset], playfield, 13,
 ,[Offset], pop_bumpers, 14,
 ,[Offset], rubbers, 15,
 ,[Offset], screws, 16,
 ,[Offset], wooden_rails, 17,
 ,[Offset], 1_white_tile, 18,
 ,[Offset], 2_white_tile, 19,
 ,[Offset], 3_white_tile, 20,
 ,[Offset], 4_white_tile, 21,
 ,[Offset], B_white_tile, 22,
 ,[Offset], black_drop_tile, 23,
 ,[Offset], bumper, 24,
 ,[Offset], left_flipper, 25,
 ,[Offset], left_slingshot, 26,
 ,[Offset], O_white_tile, 27,
 ,[Offset], one_way_gate, 28,
 ,[Offset], plunger, 29,
 ,[Offset], popbumper_ring, 30,
 ,[Offset], R_white_tile, 31,
 ,[Offset], red_target, 32,
 ,[Offset], right_flipper, 33,
 ,[Offset], right_slingshot, 34,
 ,[Offset], S_white_tile, 35,
 ,[Offset], white_target, 36,
 ,[Offset], wire, 37,
 ,[Offset], wire_one_way_gate, 38,
 ,[Offset], metal_plate_down, 39,
 ,[Offset], metal_plate_up, 40,
 ,[Offset], Light_Cutouts, 41,
 ,[Offset], left_slingshot_extended, 42,
 ,[Offset], right_slingshot_extended, 43,
 ,[Offset], pop_bumper_plastic, 44,
 ,[Offset], rollover_stars, 45,
RSID_TCENTAUR_COLLISION, 3707,,,
 ,[Offset], apron_col, 0,
 ,[Offset], ball_drain_col, 1,
 ,[Offset], captive_ball_lane_col, 2,
 ,[Offset], left_inlane_col, 3,
 ,[Offset], plastic_wall1_col, 4,
 ,[Offset], plastic_wall2_col, 5,
 ,[Offset], plastic_wall3_col, 6,
 ,[Offset], plunger_lane_col, 7,
 ,[Offset], right_inlane_col, 8,
 ,[Offset], rubber1_col, 9,
 ,[Offset], rubber2_col, 10,
 ,[Offset], rubber3_col, 11,
 ,[Offset], rubber4_col, 12,
 ,[Offset], rubber5_col, 13,
 ,[Offset], rubber6_col, 14,
 ,[Offset], rubber7_col, 15,
 ,[Offset], rubber8_col, 16,
 ,[Offset], rubber9_col, 17,
 ,[Offset], top_arc_col, 18,
 ,[Offset], wall1_col, 19,
 ,[Offset], wall2_col, 20,
 ,[Offset], wall3_col, 21,
 ,[Offset], wall4_col, 22,
 ,[Offset], playfield_col, 23,
 ,[Offset], wire_one_way_gate_front, 24,
 ,[Offset], black_drop_tile_col, 25,
 ,[Offset], bumperA, 26,
 ,[Offset], bumperB, 27,
 ,[Offset], bumperC, 28,
 ,[Offset], bumperD, 29,
 ,[Offset], bumperE, 30,
 ,[Offset], left_flipper_back, 31,
 ,[Offset], left_flipper_front, 32,
 ,[Offset], left_slingshot_col, 33,
 ,[Offset], one_way_gate_back, 34,
 ,[Offset], one_way_gate_front, 35,
 ,[Offset], plunger_col, 36,
 ,[Offset], popbumper_col, 37,
 ,[Offset], right_flipper_back, 38,
 ,[Offset], right_flipper_front, 39,
 ,[Offset], right_slingshot_col, 40,
 ,[Offset], target_col, 41,
 ,[Offset], wire_one_way_gate_back, 42,
 ,[Offset], captive_ball_back, 43,
 ,[Offset], captive_ball_front, 44,
 ,[Offset], magnet, 45,
 ,[Offset], rubber4front_col, 46,
 ,[Offset], popbumperplastic_col, 47,
 ,[Offset], rubber10_col, 48,
 ,[Offset], rubber11_col, 49,
RSID_TCENTAUR_PLACEMENT, 3708,,,
RSID_TCENTAUR_EMUROM, 3709,,,
 ,[Offset], centaur_1_u2, 0,
 ,[Offset], centaur_1_u6, 1,
 ,[Offset], centaur_1, 2,
RSID_TCENTAUR_SOUNDS_START, 3710,,,
RSID_TCENTAUR_EMU_SOUNDS, 3711,,,
 ,[Offset], S0006_LP_R01, 0,
 ,[Offset], S0006_LP_R02, 1,
 ,[Offset], S0006_LP_R03, 2,
 ,[Offset], S0006_LP_R04, 3,
 ,[Offset], S0006_LP_R05, 4,
 ,[Offset], S0006_LP_R06, 5,
 ,[Offset], S0006_LP_R07, 6,
 ,[Offset], S0006_LP_R08, 7,
 ,[Offset], S0006_LP_R09, 8,
 ,[Offset], S0006_LP_R10, 9,
 ,[Offset], S0006_LP_R11, 10,
 ,[Offset], S0006_LP_R12, 11,
 ,[Offset], S0006_LP_R13, 12,
 ,[Offset], S0006_LP_R14, 13,
 ,[Offset], S0006_LP_R15, 14,
 ,[Offset], S0006_LP_R16, 15,
 ,[Offset], S0006_LP_R17, 16,
 ,[Offset], S0006_LP_R18, 17,
 ,[Offset], S0006_LP_R19, 18,
 ,[Offset], S0006_LP_R20, 19,
 ,[Offset], S0006_LP_R21, 20,
 ,[Offset], S0006_LP_R22, 21,
 ,[Offset], S0006_LP_R23, 22,
 ,[Offset], S0006_LP_R24, 23,
 ,[Offset], S0006_LP_R25, 24,
 ,[Offset], S0006_LP_R26, 25,
 ,[Offset], S0006_LP_R27, 26,
 ,[Offset], S0006_LP_R28, 27,
 ,[Offset], S0006_LP_R29, 28,
 ,[Offset], S0006_LP_R30, 29,
 ,[Offset], S0006_LP_R31, 30,
 ,[Offset], S0006_LP_R32, 31,
 ,[Offset], S0006_LP_R33, 32,
 ,[Offset], S0006_LP_R34, 33,
 ,[Offset], S0006_LP_R35, 34,
 ,[Offset], S0006_LP_R36, 35,
 ,[Offset], S0006_LP_R37, 36,
 ,[Offset], S0006_LP_R38, 37,
 ,[Offset], S0006_LP_R39, 38,
 ,[Offset], S0006_LP_R40, 39,
 ,[Offset], S0006_LP_R41, 40,
 ,[Offset], S0006_LP_R42, 41,
 ,[Offset], S0006_LP_R43, 42,
 ,[Offset], S0006_LP_R44, 43,
 ,[Offset], S0006_LP_R45, 44,
 ,[Offset], S0006_LP_R46, 45,
 ,[Offset], S0006_LP_R47, 46,
 ,[Offset], S0006_LP_R48, 47,
 ,[Offset], S0006_LP_R49, 48,
 ,[Offset], S0006_LP_R50, 49,
 ,[Offset], S0006_LP_R51, 50,
 ,[Offset], S000B_C1, 51,
 ,[Offset], S000C_C1, 52,
 ,[Offset], S000D_C1, 53,
 ,[Offset], S000E_C1, 54,
 ,[Offset], S000F_C1, 55,
 ,[Offset], S0010_C1, 56,
 ,[Offset], S0011_C1, 57,
 ,[Offset], S0012_C1, 58,
 ,[Offset], S0013_C1, 59,
 ,[Offset], S0014_C1, 60,
 ,[Offset], S0015_C1, 61,
 ,[Offset], S0016_C1, 62,
 ,[Offset], S0017_C1, 63,
 ,[Offset], S0018_C1, 64,
 ,[Offset], S0019_C1, 65,
 ,[Offset], S001A_C1, 66,
 ,[Offset], S001B_C1, 67,
 ,[Offset], S001C_C1, 68,
 ,[Offset], S001D_C1, 69,
 ,[Offset], S001E_C1, 70,
 ,[Offset], S001E_C1_R01, 71,
 ,[Offset], S001E_C1_R02, 72,
 ,[Offset], S001E_C1_R03, 73,
 ,[Offset], S001E_C1_R04, 74,
 ,[Offset], S001E_C1_R05, 75,
 ,[Offset], S001E_C1_R06, 76,
 ,[Offset], S001E_C1_R07, 77,
 ,[Offset], S001E_C1_R08, 78,
 ,[Offset], S001E_C1_R09, 79,
 ,[Offset], S001E_C1_R10, 80,
 ,[Offset], S001E_C1_R11, 81,
 ,[Offset], S001E_C1_R12, 82,
 ,[Offset], S001F_C1, 83,
 ,[Offset], S0020_C1, 84,
 ,[Offset], S0021_C1, 85,
 ,[Offset], S0022_C1, 86,
 ,[Offset], S0023_C1, 87,
 ,[Offset], S0024_C1, 88,
 ,[Offset], S0025_C1, 89,
 ,[Offset], S0026_C1, 90,
 ,[Offset], S0028_C1, 91,
 ,[Offset], S002A_C1, 92,
 ,[Offset], S002C_C1, 93,
 ,[Offset], S0030_C2, 94,
 ,[Offset], S0031_C2, 95,
 ,[Offset], S0032_C2, 96,
 ,[Offset], S0033_C2, 97,
 ,[Offset], S0034_C2, 98,
 ,[Offset], S0035_C2, 99,
 ,[Offset], S0036_C2, 100,
 ,[Offset], S0037_C2, 101,
 ,[Offset], S0038_C2, 102,
 ,[Offset], S0039_C2, 103,
 ,[Offset], S003A_C2, 104,
 ,[Offset], S003B_C2, 105,
 ,[Offset], S003C_C2, 106,
 ,[Offset], S003D_C2, 107,
 ,[Offset], S003E_C2, 108,
 ,[Offset], S003F_C2, 109,
 ,[Offset], S0040_C2, 110,
 ,[Offset], S0041_C2, 111,
 ,[Offset], S0042_C2, 112,
 ,[Offset], S0043_C2, 113,
 ,[Offset], S0044_C2, 114,
 ,[Offset], S0045_C2, 115,
 ,[Offset], S0046_C2, 116,
 ,[Offset], S0047_C2, 117,
 ,[Offset], S0048_C2, 118,
 ,[Offset], S0049_C2, 119,
 ,[Offset], S004A_C2, 120,
 ,[Offset], S004B_C2, 121,
 ,[Offset], S004C_C2, 122,
 ,[Offset], S004D_C2, 123,
 ,[Offset], S004E_C2, 124,
 ,[Offset], S004F_C2, 125,
 ,[Offset], S0050_C2, 126,
 ,[Offset], S0051_C2, 127,
 ,[Offset], S0052_C2, 128,
 ,[Offset], S0053_C2, 129,
 ,[Offset], S0054_C2, 130,
RSID_TCENTAUR_MECH_SOUNDS, 3712,,,
 ,[Offset], drop_targets_reset, 0,
RSID_TCENTAUR_SOUNDS_END, 3713,,,
RSID_TCENTAUR_SAMPLES, 3714,,,
RSID_TCENTAUR_HUD, 3715,,,
RSID_TCENTAUR_END, 3716,,,
