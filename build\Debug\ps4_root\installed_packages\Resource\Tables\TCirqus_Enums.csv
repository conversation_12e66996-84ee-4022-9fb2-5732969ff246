RSID_TCIRQUS_START, 3225,,,
 ,[Offset], CIRCUS_FLYER_1, 0,
 ,[Offset], <PERSON>irqus<PERSON>oltaire\PBCirqusVoltaire, 1,
 ,[Offset], <PERSON>irqusVoltaire\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON>qus<PERSON>oltaire\InstructionsFR, 3,
 ,[Offset], <PERSON>irqusVoltaire\InstructionsITAL, 4,
 ,[Offset], CirqusVoltaire\InstructionsGERM, 5,
 ,[Offset], CirqusVoltaire\InstructionsSPAN, 6,
 ,[Offset], CirqusVoltaire\InstructionsPORT, 7,
 ,[Offset], CirqusVoltaire\InstructionsDUTCH, 8,
 ,[Offset], tables\CIRCUS_BG_Scroll, 9,
RSID_TCIRQUS_LIGHTS, 3226,,,
 ,[Offset], LightSets, 0,
 ,[Offset], LightSets, 1,
 ,[Offset], LightSets_Aqua, 2,
 ,[Offset], LightSets_Blue, 3,
 ,[Offset], LightSets_Green, 4,
 ,[Offset], LightSets_Orange, 5,
 ,[Offset], LightSets_Pink, 6,
 ,[Offset], LightSets_Yellow, 7,
RSID_TCIRQUS_CAMERAS, 3227,,,
RSID_TCIRQUS_LAMP_TEXTURES, 3228,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_78_Off, 108,
 ,[Offset], L_78_On, 109,
 ,[Offset], L_81_Off, 110,
 ,[Offset], L_81_On, 111,
 ,[Offset], F_12_Off, 112,
 ,[Offset], F_12_On, 113,
 ,[Offset], F_13_Off, 114,
 ,[Offset], F_13_On, 115,
 ,[Offset], F_17_Off, 116,
 ,[Offset], F_17_On, 117,
 ,[Offset], F_18_Off, 118,
 ,[Offset], F_18_On, 119,
 ,[Offset], F_19_Off, 120,
 ,[Offset], F_19_On, 121,
 ,[Offset], F_20_Off, 122,
 ,[Offset], F_20_On, 123,
 ,[Offset], F_25_Off, 124,
 ,[Offset], F_25_On, 125,
 ,[Offset], F_23_Off, 126,
 ,[Offset], F_23_On, 127,
 ,[Offset], F_26_Off, 128,
 ,[Offset], F_26_On, 129,
 ,[Offset], F_21_Off, 130,
 ,[Offset], F_21_On, 131,
 ,[Offset], F_24_Off, 132,
 ,[Offset], F_24_On, 133,
 ,[Offset], F_27_Off, 134,
 ,[Offset], F_27_On, 135,
 ,[Offset], F_28_Off, 136,
 ,[Offset], F_28_On, 137,
 ,[Offset], L_77_Off, 138,
 ,[Offset], L_77_On, 139,
 ,[Offset], L_83_Off, 140,
 ,[Offset], L_83_On, 141,
 ,[Offset], L_85_Off, 142,
 ,[Offset], L_85_On, 143,
 ,[Offset], L_86_Off, 144,
 ,[Offset], L_86_On, 145,
 ,[Offset], L_87_Off, 146,
 ,[Offset], L_87_On, 147,
 ,[Offset], Face_Elements_Off, 148,
 ,[Offset], Face_Elements_On, 149,
 ,[Offset], Face_Off, 150,
 ,[Offset], Face_On, 151,
 ,[Offset], Flasher_Off, 152,
 ,[Offset], Neon_Tube, 153,
 ,[Offset], Neon_Tube_Red, 154,
 ,[Offset], Ramp2_PT1, 155,
 ,[Offset], Ramp2_PT1_On, 156,
 ,[Offset], Ramp2_PT2, 157,
 ,[Offset], Ramp2_PT2_On, 158,
RSID_TCIRQUS_TEXTURES, 3229,,,
 ,[Offset], RedCircusBall, 0,
 ,[Offset], AquaCircusBall, 1,
 ,[Offset], BlueCircusBall, 2,
 ,[Offset], GreenCircusBall, 3,
 ,[Offset], OrangeCircusBall, 4,
 ,[Offset], PinkCircusBall, 5,
 ,[Offset], YellowCircusBall, 6,
 ,[Offset], display_frame_generic, 7,
 ,[Offset], Apron, 8,
 ,[Offset], Apron_Rules, 9,
 ,[Offset], Backglass, 10,
 ,[Offset], Boom_PopBumper, 11,
 ,[Offset], Cabinet, 12,
 ,[Offset], Cabinet_Front, 13,
 ,[Offset], Cabinet_Head, 14,
 ,[Offset], Coin_Slot, 15,
 ,[Offset], DotMationBoard, 16,
 ,[Offset], DotMationPlastic, 17,
 ,[Offset], Eject_Scoop, 18,
 ,[Offset], Face, 19,
 ,[Offset], Face_Elements, 20,
 ,[Offset], Face_Top, 21,
 ,[Offset], LG1, 22,
 ,[Offset], LG2, 23,
 ,[Offset], Metal_Posts, 24,
 ,[Offset], Metal_Walls, 25,
 ,[Offset], Neon_Metal, 26,
 ,[Offset], P1, 27,
 ,[Offset], P2, 28,
 ,[Offset], P3, 29,
 ,[Offset], Plastic1, 30,
 ,[Offset], Plastic2, 31,
 ,[Offset], Plastic3, 32,
 ,[Offset], Plastic4, 33,
 ,[Offset], Plastic5, 34,
 ,[Offset], Playfield_Lower, 35,
 ,[Offset], Playfield_Top, 36,
 ,[Offset], PopBumper, 37,
 ,[Offset], PopBumperBody, 38,
 ,[Offset], Ramp1, 39,
 ,[Offset], Ramp3, 40,
 ,[Offset], Red_Plastic_Post, 41,
 ,[Offset], Red_Target, 42,
 ,[Offset], SmallHabitTrail, 43,
 ,[Offset], Stand, 44,
 ,[Offset], WoodMagnent, 45,
 ,[Offset], Yellow_Target, 46,
 ,[Offset], Plunger, 47,
 ,[Offset], Flipper_Button, 48,
 ,[Offset], Black_Grain, 49,
 ,[Offset], Extra_Metal_Parts, 50,
 ,[Offset], Metal_Parts, 51,
 ,[Offset], Generic_Metal, 52,
 ,[Offset], bulb1, 53,
 ,[Offset], Silver Metal Screws_Temp, 54,
 ,[Offset], Rubber_Band_Black, 55,
 ,[Offset], Bumper_Sensors, 56,
 ,[Offset], Bumper_Hamer, 57,
 ,[Offset], Rails, 58,
 ,[Offset], Harley_Spinner, 59,
 ,[Offset], Plastic_Post_Red, 60,
 ,[Offset], Rubber Post_Temp, 61,
 ,[Offset], BlackMatte_Temp, 62,
 ,[Offset], Flipper, 63,
 ,[Offset], Circus_BallFrame, 64,
 ,[Offset], LG_Habit, 65,
 ,[Offset], Spinner, 66,
 ,[Offset], Backglass_Playfield, 67,
 ,[Offset], Spinning_Wheel, 68,
 ,[Offset], Juggler_Plastic, 69,
 ,[Offset], Juggler_Metal, 70,
 ,[Offset], Plastic_Piece, 71,
 ,[Offset], Ramp_Stickers, 72,
 ,[Offset], Ramp_Sticker_A, 73,
RSID_TCIRQUS_MODELS, 3230,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet_Backglass, 1,
 ,[Offset], Cabinet_Body, 2,
 ,[Offset], Cabinet_Interior, 3,
 ,[Offset], Cabinet_Metals, 4,
 ,[Offset], DMD, 5,
 ,[Offset], Habit_Trail_A, 6,
 ,[Offset], Habit_Trail_B, 7,
 ,[Offset], Light_Bulbs, 8,
 ,[Offset], Magnet, 9,
 ,[Offset], Metal_Pieces, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Plastic_Pieces, 12,
 ,[Offset], Plastic_Posts, 13,
 ,[Offset], Plastic_Ramp_A, 14,
 ,[Offset], Plastic_Ramp_B, 15,
 ,[Offset], Plastic_Ramp_C, 16,
 ,[Offset], Playfield, 17,
 ,[Offset], Pop_Bumpers, 18,
 ,[Offset], Rope_Light, 19,
 ,[Offset], Rubber_Posts, 20,
 ,[Offset], Wooden_Rails, 21,
 ,[Offset], BigYellow_Target, 22,
 ,[Offset], Boom_Bumper, 23,
 ,[Offset], Button, 24,
 ,[Offset], Flipper_Left, 25,
 ,[Offset], Flipper_Right, 26,
 ,[Offset], Head, 27,
 ,[Offset], MultiBall_Post_Trap, 28,
 ,[Offset], Ramp_Diverter, 29,
 ,[Offset], Red_Target, 30,
 ,[Offset], Slingshot_Right, 31,
 ,[Offset], Slingshot_Left, 32,
 ,[Offset], Wire, 33,
 ,[Offset], Yellow_Target, 34,
 ,[Offset], Ball_Cage, 35,
 ,[Offset], Trapped_Ball, 36,
 ,[Offset], Plunger, 37,
 ,[Offset], Bumper_A, 38,
 ,[Offset], Bumper_Metal, 39,
 ,[Offset], Head_Base, 40,
 ,[Offset], Spinner, 41,
 ,[Offset], Switch_A, 42,
 ,[Offset], Switch_B, 43,
 ,[Offset], Switch_C, 44,
 ,[Offset], Switch_D, 45,
 ,[Offset], Flasher_A, 46,
 ,[Offset], Cabinet_Backglass_Playfield, 47,
 ,[Offset], Spinner_Wheel, 48,
 ,[Offset], Light_Cutouts, 49,
 ,[Offset], Light_Cutouts_2, 50,
 ,[Offset], Bumper_Tops, 51,
 ,[Offset], Button, 52,
 ,[Offset], Button_B, 53,
 ,[Offset], Button_C, 54,
 ,[Offset], Button_D, 55,
 ,[Offset], Slingshot_Left_Extended, 56,
 ,[Offset], Slingshot_Right_Extended, 57,
RSID_TCIRQUS_MODELS_LODS, 3231,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet_Backglass, 1,
 ,[Offset], Cabinet_Body, 2,
 ,[Offset], Cabinet_Interior, 3,
 ,[Offset], Cabinet_Metals, 4,
 ,[Offset], DMD, 5,
 ,[Offset], Habit_Trail_A, 6,
 ,[Offset], Habit_Trail_B, 7,
 ,[Offset], Light_Bulbs, 8,
 ,[Offset], Magnet, 9,
 ,[Offset], Metal_Pieces, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Plastic_Pieces, 12,
 ,[Offset], Plastic_Posts, 13,
 ,[Offset], Plastic_Ramp_A, 14,
 ,[Offset], Plastic_Ramp_B, 15,
 ,[Offset], Plastic_Ramp_C, 16,
 ,[Offset], Playfield, 17,
 ,[Offset], Pop_Bumpers, 18,
 ,[Offset], Rope_Light, 19,
 ,[Offset], Rubber_Posts, 20,
 ,[Offset], Wooden_Rails, 21,
 ,[Offset], BigYellow_Target, 22,
 ,[Offset], Boom_Bumper, 23,
 ,[Offset], Button, 24,
 ,[Offset], Flipper_Left, 25,
 ,[Offset], Flipper_Right, 26,
 ,[Offset], Head, 27,
 ,[Offset], MultiBall_Post_Trap, 28,
 ,[Offset], Ramp_Diverter, 29,
 ,[Offset], Red_Target, 30,
 ,[Offset], Slingshot_Right, 31,
 ,[Offset], Slingshot_Left, 32,
 ,[Offset], Wire, 33,
 ,[Offset], Yellow_Target, 34,
 ,[Offset], Ball_Cage, 35,
 ,[Offset], Trapped_Ball, 36,
 ,[Offset], Plunger, 37,
 ,[Offset], Bumper_A, 38,
 ,[Offset], Bumper_Metal, 39,
 ,[Offset], Head_Base, 40,
 ,[Offset], Spinner, 41,
 ,[Offset], Switch_A, 42,
 ,[Offset], Switch_B, 43,
 ,[Offset], Switch_C, 44,
 ,[Offset], Switch_D, 45,
 ,[Offset], Flasher_A, 46,
 ,[Offset], Cabinet_Backglass_Playfield, 47,
 ,[Offset], Spinner_Wheel, 48,
 ,[Offset], Light_Cutouts, 49,
 ,[Offset], Light_Cutouts_2, 50,
 ,[Offset], Bumper_Tops, 51,
 ,[Offset], Button, 52,
 ,[Offset], Button_B, 53,
 ,[Offset], Button_C, 54,
 ,[Offset], Button_D, 55,
 ,[Offset], Slingshot_Left_Extended, 56,
 ,[Offset], Slingshot_Right_Extended, 57,
RSID_TCIRQUS_COLLISION, 3232,,,
 ,[Offset], Apron, 0,
 ,[Offset], Ball_Drain, 1,
 ,[Offset], Floor, 2,
 ,[Offset], Habit_Trail_Left, 3,
 ,[Offset], Habit_Trail_Middle, 4,
 ,[Offset], Magnet, 5,
 ,[Offset], Metal_Wall_A, 6,
 ,[Offset], Metal_Wall_B, 7,
 ,[Offset], Metal_Wall_C, 8,
 ,[Offset], Plunger, 9,
 ,[Offset], Pop_Bumper, 10,
 ,[Offset], Ramp_Middle, 11,
 ,[Offset], Ramp_Right, 12,
 ,[Offset], Rubber_A, 13,
 ,[Offset], Rubber_B, 14,
 ,[Offset], Rubber_C, 15,
 ,[Offset], Rubber_D, 16,
 ,[Offset], Slingshot_Left, 17,
 ,[Offset], Slingshot_Right, 18,
 ,[Offset], Wall_Left, 19,
 ,[Offset], Wall_Right, 20,
 ,[Offset], Plunger_Ramp, 21,
 ,[Offset], Big_Yellow_Target, 22,
 ,[Offset], Boom_Bumper, 23,
 ,[Offset], Boom_Post, 24,
 ,[Offset], Head, 25,
 ,[Offset], Left_Flipper_Back, 26,
 ,[Offset], Left_Flipper_Front, 27,
 ,[Offset], Right_Flipper_Back, 28,
 ,[Offset], Right_Flipper_Front, 29,
 ,[Offset], Yellow_Target, 30,
 ,[Offset], Red_Target, 31,
 ,[Offset], Ramp_Diverter, 32,
 ,[Offset], Post_Stopper, 33,
 ,[Offset], Slingshot_Left_Front, 34,
 ,[Offset], Slingshot_Right_Front, 35,
 ,[Offset], Moving_Plunger, 36,
 ,[Offset], Bumper, 37,
 ,[Offset], BackTable_Trap, 38,
 ,[Offset], Diverter_Post, 39,
 ,[Offset], Head_Trap, 40,
 ,[Offset], Magnet_A, 41,
 ,[Offset], Magnet_B, 42,
 ,[Offset], Magnet_C, 43,
 ,[Offset], Head_Sensor, 44,
 ,[Offset], Slingshot_A, 45,
 ,[Offset], Juggler_A, 46,
 ,[Offset], Juggler_B, 47,
 ,[Offset], Backglass, 48,
 ,[Offset], Backglass_Trap, 49,
 ,[Offset], Spinner_Wheel, 50,
 ,[Offset], Slingshot_Left_Front, 51,
 ,[Offset], Slingshot_Right_Front, 52,
 ,[Offset], Backglass_Posts, 53,
 ,[Offset], Habit_Trail_Middle_Trap, 54,
 ,[Offset], Backglass_B, 55,
 ,[Offset], Backglass_Floor, 56,
 ,[Offset], Spinner, 57,
RSID_TCIRQUS_PLACEMENT, 3233,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TCIRQUS_EMUROM, 3234,,,
 ,[Offset], cv_14, 0,
 ,[Offset], cv_14, 1,
RSID_TCIRQUS_SOUNDS_START, 3235,,,
RSID_TCIRQUS_EMU_SOUNDS, 3236,,,
 ,[Offset], S0002-LP, 0,
 ,[Offset], S0003_C1, 1,
 ,[Offset], S0004-LP, 2,
 ,[Offset], S0005-LP, 3,
 ,[Offset], S0006-LP, 4,
 ,[Offset], S0007_C1, 5,
 ,[Offset], S0008_C1, 6,
 ,[Offset], S000A-LP, 7,
 ,[Offset], S000B-LP, 8,
 ,[Offset], S000C-LP, 9,
 ,[Offset], S000D-LP, 10,
 ,[Offset], S000E_C1, 11,
 ,[Offset], S000F-LP, 12,
 ,[Offset], S0010_C1, 13,
 ,[Offset], S0011_C5, 14,
 ,[Offset], S0012-LP, 15,
 ,[Offset], S0014-LP1, 16,
 ,[Offset], S0014-LP2, 17,
 ,[Offset], S0015-LP1, 18,
 ,[Offset], S0015-LP2, 19,
 ,[Offset], S0016-LP, 20,
 ,[Offset], S0018_C1, 21,
 ,[Offset], S0019_C1, 22,
 ,[Offset], S001A_C1, 23,
 ,[Offset], S001B_C1, 24,
 ,[Offset], S001E_C1, 25,
 ,[Offset], S0020_C1, 26,
 ,[Offset], S0021_C1, 27,
 ,[Offset], S0023-LP, 28,
 ,[Offset], S0024_C1, 29,
 ,[Offset], S0025_C1, 30,
 ,[Offset], S0026-LP, 31,
 ,[Offset], S0027_C1, 32,
 ,[Offset], S0029-LP1, 33,
 ,[Offset], S0029-LP2, 34,
 ,[Offset], S002A-LP, 35,
 ,[Offset], S002C_C3, 36,
 ,[Offset], S002D-LP, 37,
 ,[Offset], S002E-LP, 38,
 ,[Offset], S002F-LP1, 39,
 ,[Offset], S002F-LP2, 40,
 ,[Offset], S0030-LP, 41,
 ,[Offset], S0031-LP1, 42,
 ,[Offset], S0032_C1, 43,
 ,[Offset], S0033_C1, 44,
 ,[Offset], S0034_C1, 45,
 ,[Offset], S0035_C1, 46,
 ,[Offset], S0036_C1, 47,
 ,[Offset], S0038_C1, 48,
 ,[Offset], S003A_C1, 49,
 ,[Offset], S003E-LP, 50,
 ,[Offset], S003F-LP, 51,
 ,[Offset], S0043-LP1, 52,
 ,[Offset], S0045_C3, 53,
 ,[Offset], S0046_C4, 54,
 ,[Offset], S0048_C4, 55,
 ,[Offset], S004A_C4, 56,
 ,[Offset], S004C_C4, 57,
 ,[Offset], S004E_C1, 58,
 ,[Offset], S0050_C1, 59,
 ,[Offset], S0052_C1, 60,
 ,[Offset], S0054_C1, 61,
 ,[Offset], S0057_C5, 62,
 ,[Offset], S005A_C4, 63,
 ,[Offset], S005C_C4, 64,
 ,[Offset], S005E_C4, 65,
 ,[Offset], S0061_C1, 66,
 ,[Offset], S0063_C5, 67,
 ,[Offset], S0066_C4, 68,
 ,[Offset], S0068_C4, 69,
 ,[Offset], S0069_C4, 70,
 ,[Offset], S006A_C4, 71,
 ,[Offset], S006B_C4, 72,
 ,[Offset], S006C_C4, 73,
 ,[Offset], S006D_C4, 74,
 ,[Offset], S006E_C4, 75,
 ,[Offset], S006F_C4, 76,
 ,[Offset], S0070_C4, 77,
 ,[Offset], S0071_C4, 78,
 ,[Offset], S0072_C4, 79,
 ,[Offset], S0074_C4, 80,
 ,[Offset], S0075_C4, 81,
 ,[Offset], S0078_C2, 82,
 ,[Offset], S0079_C2, 83,
 ,[Offset], S007A_C4, 84,
 ,[Offset], S007B_C4, 85,
 ,[Offset], S007C_C4, 86,
 ,[Offset], S007D_C4, 87,
 ,[Offset], S007E_C4, 88,
 ,[Offset], S0080_C4, 89,
 ,[Offset], S0083_C4, 90,
 ,[Offset], S0086_C4, 91,
 ,[Offset], S0087_C4, 92,
 ,[Offset], S008A_C4, 93,
 ,[Offset], S008B_C4, 94,
 ,[Offset], S008C_C4, 95,
 ,[Offset], S008D_C4, 96,
 ,[Offset], S008E_C4, 97,
 ,[Offset], S008F_C4, 98,
 ,[Offset], S0090_C4, 99,
 ,[Offset], S0091_C4, 100,
 ,[Offset], S0092_C4, 101,
 ,[Offset], S0096_C4, 102,
 ,[Offset], S0098_C4, 103,
 ,[Offset], S0099_C4, 104,
 ,[Offset], S009A_C2, 105,
 ,[Offset], S009B_C4, 106,
 ,[Offset], S009C_C4, 107,
 ,[Offset], S009D_C4, 108,
 ,[Offset], S009F_C4, 109,
 ,[Offset], S00A0_C4, 110,
 ,[Offset], S00A1_C4, 111,
 ,[Offset], S00A2_C4, 112,
 ,[Offset], S00A3_C4, 113,
 ,[Offset], S00A4_C4, 114,
 ,[Offset], S00A5_C4, 115,
 ,[Offset], S00A7_C4, 116,
 ,[Offset], S00AA_C4, 117,
 ,[Offset], S00AD_C4, 118,
 ,[Offset], S00AE_C4, 119,
 ,[Offset], S00AF_C4, 120,
 ,[Offset], S00B2_C4, 121,
 ,[Offset], S00B3_C4, 122,
 ,[Offset], S00B4_C4, 123,
 ,[Offset], S00B5_C4, 124,
 ,[Offset], S00B8_C4, 125,
 ,[Offset], S00BA_C4, 126,
 ,[Offset], S00BB_C4, 127,
 ,[Offset], S00BC_C4, 128,
 ,[Offset], S00BE_C4, 129,
 ,[Offset], S00BF_C4, 130,
 ,[Offset], S00C0_C4, 131,
 ,[Offset], S00C1_C4, 132,
 ,[Offset], S00C2_C4, 133,
 ,[Offset], S00C3_C4, 134,
 ,[Offset], S00C8_C4, 135,
 ,[Offset], S00C9_C4, 136,
 ,[Offset], S00CC_C4, 137,
 ,[Offset], S00CE_C4, 138,
 ,[Offset], S00D1_C4, 139,
 ,[Offset], S00D5_C4, 140,
 ,[Offset], S00D6_C4, 141,
 ,[Offset], S00D7_C4, 142,
 ,[Offset], S00DA_C4, 143,
 ,[Offset], S00DB_C4, 144,
 ,[Offset], S00DC_C4, 145,
 ,[Offset], S00DD_C1, 146,
 ,[Offset], S00E3_C4, 147,
 ,[Offset], S00E8_C4, 148,
 ,[Offset], S00EA_C4, 149,
 ,[Offset], S00EC_C4, 150,
 ,[Offset], S00F1_C4, 151,
 ,[Offset], S00F2_C4, 152,
 ,[Offset], S00F3_C4, 153,
 ,[Offset], S00F4_C4, 154,
 ,[Offset], S00F8_C4, 155,
 ,[Offset], S00F9_C4, 156,
 ,[Offset], S0101_C4, 157,
 ,[Offset], S0102_C4, 158,
 ,[Offset], S0103_C4, 159,
 ,[Offset], S0104_C4, 160,
 ,[Offset], S0108_C4, 161,
 ,[Offset], S0109_C4, 162,
 ,[Offset], S0110_C4, 163,
 ,[Offset], S0112_C4, 164,
 ,[Offset], S0113_C4, 165,
 ,[Offset], S0115_C4, 166,
 ,[Offset], S0120_C4, 167,
 ,[Offset], S0128_C4, 168,
 ,[Offset], S012A_C4, 169,
 ,[Offset], S012C_C4, 170,
 ,[Offset], S012D_C4, 171,
 ,[Offset], S0130_C4, 172,
 ,[Offset], S0131_C4, 173,
 ,[Offset], S0132_C4, 174,
 ,[Offset], S0133_C4, 175,
 ,[Offset], S0134_C4, 176,
 ,[Offset], S0135_C4, 177,
 ,[Offset], S0136_C4, 178,
 ,[Offset], S0137_C4, 179,
 ,[Offset], S01A0_C4, 180,
 ,[Offset], S0203_C5, 181,
 ,[Offset], S0242_C5, 182,
 ,[Offset], S028C_C5, 183,
 ,[Offset], S0327_C4, 184,
 ,[Offset], S0328_C4, 185,
 ,[Offset], S03D4_C-1, 186,
 ,[Offset], S03D5_C-1, 187,
 ,[Offset], S03D6_C5, 188,
 ,[Offset], S03D7_C5, 189,
 ,[Offset], S03D8_C5, 190,
 ,[Offset], S03D9_C-1, 191,
 ,[Offset], S03DA_C-1, 192,
 ,[Offset], S03DB_C5, 193,
 ,[Offset], S03DC_C5, 194,
 ,[Offset], S03DD_C5, 195,
 ,[Offset], S03DE_C-1, 196,
 ,[Offset], S0450_C6, 197,
 ,[Offset], S0451_C6, 198,
 ,[Offset], S0452_C6, 199,
 ,[Offset], S0453_C6, 200,
 ,[Offset], S0455_C6, 201,
 ,[Offset], S0456_C6, 202,
 ,[Offset], S0457_C6, 203,
 ,[Offset], S0458_C6, 204,
 ,[Offset], S0459_C6, 205,
 ,[Offset], S045A_C6, 206,
 ,[Offset], S045B_C6, 207,
 ,[Offset], S045C_C6, 208,
 ,[Offset], S045D_C6, 209,
 ,[Offset], S045E_C6, 210,
 ,[Offset], S045F_C6, 211,
 ,[Offset], S0460_C6, 212,
 ,[Offset], S0461_C6, 213,
 ,[Offset], S046A_C6, 214,
 ,[Offset], S046C_C6, 215,
 ,[Offset], S046D_C6, 216,
 ,[Offset], S0470_C6, 217,
 ,[Offset], S0471_C6, 218,
 ,[Offset], S047E_C6, 219,
 ,[Offset], S047F_C6, 220,
 ,[Offset], S0480_C6, 221,
 ,[Offset], S0483_C6, 222,
 ,[Offset], S0484_C6, 223,
 ,[Offset], S0487_C6, 224,
 ,[Offset], S0488_C6, 225,
 ,[Offset], S0489_C6, 226,
 ,[Offset], S0492_C6, 227,
 ,[Offset], S0495_C6, 228,
 ,[Offset], S0496_C6, 229,
 ,[Offset], S049C_C6, 230,
 ,[Offset], S049D_C6, 231,
 ,[Offset], S049F_C6, 232,
 ,[Offset], S04A0_C6, 233,
 ,[Offset], S04A1_C4, 234,
 ,[Offset], S04B1_C6, 235,
 ,[Offset], S04B2_C6, 236,
 ,[Offset], S04B3_C6, 237,
 ,[Offset], S04B7_C6, 238,
 ,[Offset], S04B8_C6, 239,
 ,[Offset], S04B9_C6, 240,
 ,[Offset], S04BA_C6, 241,
 ,[Offset], S04BB_C6, 242,
 ,[Offset], S04BC_C6, 243,
 ,[Offset], S04BD_C6, 244,
 ,[Offset], S04BE_C6, 245,
 ,[Offset], S04BF_C6, 246,
 ,[Offset], S04C0_C6, 247,
 ,[Offset], S04C1_C6, 248,
 ,[Offset], S04C3_C6, 249,
 ,[Offset], S04C4_C6, 250,
 ,[Offset], S04C7_C6, 251,
 ,[Offset], S04C8_C6, 252,
 ,[Offset], S04CA_C6, 253,
 ,[Offset], S04CB_C6, 254,
 ,[Offset], S04CD_C6, 255,
 ,[Offset], S04CE_C6, 256,
 ,[Offset], S04D0_C6, 257,
 ,[Offset], S04D1_C6, 258,
 ,[Offset], S04D2_C6, 259,
 ,[Offset], S04D3_C6, 260,
 ,[Offset], S04D4_C6, 261,
 ,[Offset], S04D5_C6, 262,
 ,[Offset], S04DB_C6, 263,
 ,[Offset], S04DC_C6, 264,
 ,[Offset], S04DF_C6, 265,
 ,[Offset], S04E0_C6, 266,
 ,[Offset], S04E2_C6, 267,
 ,[Offset], S04E3_C6, 268,
 ,[Offset], S04E4_C6, 269,
 ,[Offset], S04E5_C6, 270,
 ,[Offset], S04E6_C6, 271,
 ,[Offset], S04E7_C6, 272,
 ,[Offset], S04E8_C6, 273,
 ,[Offset], S04EA_C6, 274,
 ,[Offset], S04EB_C6, 275,
 ,[Offset], S04EC_C6, 276,
 ,[Offset], S04ED_C6, 277,
 ,[Offset], S04EE_C6, 278,
 ,[Offset], S04EF_C6, 279,
 ,[Offset], S04F1_C6, 280,
 ,[Offset], S04F2_C6, 281,
 ,[Offset], S04F3_C6, 282,
 ,[Offset], S04F5_C6, 283,
 ,[Offset], S04F6_C6, 284,
 ,[Offset], S04F7_C6, 285,
 ,[Offset], S04F8_C6, 286,
 ,[Offset], S04F9_C6, 287,
 ,[Offset], S04FB_C6, 288,
 ,[Offset], S04FD_C6, 289,
 ,[Offset], S04FE_C6, 290,
 ,[Offset], S0501_C6, 291,
 ,[Offset], S0502_C6, 292,
 ,[Offset], S0503_C6, 293,
 ,[Offset], S0506_C6, 294,
 ,[Offset], S0507_C6, 295,
 ,[Offset], S050A_C6, 296,
 ,[Offset], S050B_C6, 297,
 ,[Offset], S050C_C6, 298,
 ,[Offset], S050D_C6, 299,
 ,[Offset], S050E_C6, 300,
 ,[Offset], S050F_C6, 301,
 ,[Offset], S0510_C6, 302,
 ,[Offset], S0511_C6, 303,
 ,[Offset], S0512_C6, 304,
 ,[Offset], S05DC_C6, 305,
 ,[Offset], S05DF_C6, 306,
 ,[Offset], S05E3_C6, 307,
 ,[Offset], S05E5_C6, 308,
 ,[Offset], S05E6_C6, 309,
 ,[Offset], S05E7_C6, 310,
 ,[Offset], S05E8_C6, 311,
 ,[Offset], S05EC_C6, 312,
 ,[Offset], S05F3_C6, 313,
 ,[Offset], S05F5_C6, 314,
 ,[Offset], S05F8_C6, 315,
 ,[Offset], S05F9_C6, 316,
 ,[Offset], S05FA_C6, 317,
 ,[Offset], S05FB_C6, 318,
 ,[Offset], S05FC_C6, 319,
 ,[Offset], S05FD_C6, 320,
 ,[Offset], S05FE_C6, 321,
 ,[Offset], S05FF_C6, 322,
 ,[Offset], S0601_C6, 323,
 ,[Offset], S0605_C6, 324,
 ,[Offset], S060A_C6, 325,
 ,[Offset], S060E_C6, 326,
 ,[Offset], S0611_C6, 327,
 ,[Offset], S0612_C6, 328,
 ,[Offset], S0615_C6, 329,
 ,[Offset], S0616_C6, 330,
 ,[Offset], S0619_C6, 331,
 ,[Offset], S061A_C6, 332,
 ,[Offset], S061D_C6, 333,
 ,[Offset], S061E_C6, 334,
 ,[Offset], S0622_C6, 335,
 ,[Offset], S0625_C6, 336,
 ,[Offset], S0627_C6, 337,
 ,[Offset], S0629_C6, 338,
 ,[Offset], S0630_C6, 339,
 ,[Offset], S0633_C6, 340,
 ,[Offset], S0635_C6, 341,
 ,[Offset], S063F_C6, 342,
 ,[Offset], S0642_C6, 343,
 ,[Offset], S0647_C6, 344,
 ,[Offset], S0649_C6, 345,
 ,[Offset], S064B_C6, 346,
 ,[Offset], S064D_C6, 347,
 ,[Offset], S064E_C6, 348,
 ,[Offset], S0651_C6, 349,
 ,[Offset], S0654_C6, 350,
 ,[Offset], S0660_C6, 351,
 ,[Offset], S0661_C6, 352,
 ,[Offset], S0664_C6, 353,
 ,[Offset], S0666_C6, 354,
 ,[Offset], S0667_C6, 355,
 ,[Offset], S0668_C6, 356,
 ,[Offset], S066D_C6, 357,
 ,[Offset], S066F_C6, 358,
 ,[Offset], S0672_C6, 359,
 ,[Offset], S0673_C6, 360,
 ,[Offset], S0676_C6, 361,
 ,[Offset], S0678_C6, 362,
 ,[Offset], S067A_C6, 363,
 ,[Offset], S067D_C6, 364,
 ,[Offset], S0682_C6, 365,
 ,[Offset], S0683_C6, 366,
 ,[Offset], S0686_C6, 367,
 ,[Offset], S068A_C6, 368,
 ,[Offset], S068F_C6, 369,
 ,[Offset], S0692_C6, 370,
 ,[Offset], S0695_C6, 371,
 ,[Offset], S0698_C6, 372,
 ,[Offset], S069D_C6, 373,
 ,[Offset], S069E_C6, 374,
 ,[Offset], S06A1_C6, 375,
 ,[Offset], S06A6_C6, 376,
 ,[Offset], S06A8_C6, 377,
 ,[Offset], S06A9_C6, 378,
 ,[Offset], S06AA_C6, 379,
 ,[Offset], S06AB_C6, 380,
 ,[Offset], S06AC_C6, 381,
 ,[Offset], S06AE_C6, 382,
 ,[Offset], S06AF_C6, 383,
 ,[Offset], S06B0_C6, 384,
 ,[Offset], S06B3_C6, 385,
 ,[Offset], S06B4_C6, 386,
 ,[Offset], S06B9_C6, 387,
 ,[Offset], S06BB_C6, 388,
 ,[Offset], S06BC_C6, 389,
 ,[Offset], S0708_C6, 390,
 ,[Offset], S070D_C6, 391,
 ,[Offset], S070E_C6, 392,
 ,[Offset], S070F_C6, 393,
 ,[Offset], S0710_C6, 394,
 ,[Offset], S0711_C6, 395,
 ,[Offset], S0715_C6, 396,
 ,[Offset], S0716_C6, 397,
 ,[Offset], S0718_C6, 398,
 ,[Offset], S071A_C6, 399,
 ,[Offset], S071D_C6, 400,
 ,[Offset], S071E_C6, 401,
 ,[Offset], S071F_C6, 402,
 ,[Offset], S0720_C6, 403,
 ,[Offset], S0721_C6, 404,
 ,[Offset], S0722_C6, 405,
 ,[Offset], S0723_C6, 406,
 ,[Offset], S0727_C6, 407,
 ,[Offset], S0728_C6, 408,
 ,[Offset], S0729_C6, 409,
 ,[Offset], S072A_C6, 410,
 ,[Offset], S072B_C6, 411,
 ,[Offset], S072C_C6, 412,
 ,[Offset], S0733_C6, 413,
 ,[Offset], S0736_C6, 414,
 ,[Offset], S0738_C6, 415,
 ,[Offset], S0741_C6, 416,
 ,[Offset], S0742_C6, 417,
 ,[Offset], S0743_C6, 418,
 ,[Offset], S0745_C6, 419,
 ,[Offset], S0746_C6, 420,
 ,[Offset], S0747_C6, 421,
 ,[Offset], S0749_C6, 422,
 ,[Offset], S074A_C6, 423,
 ,[Offset], S074B_C6, 424,
 ,[Offset], S074C_C6, 425,
 ,[Offset], S074D_C6, 426,
 ,[Offset], S074E_C6, 427,
 ,[Offset], S074F_C6, 428,
 ,[Offset], S0750_C6, 429,
 ,[Offset], S0751_C6, 430,
 ,[Offset], S0759_C6, 431,
 ,[Offset], S075E_C6, 432,
 ,[Offset], S0760_C6, 433,
 ,[Offset], S0761_C6, 434,
 ,[Offset], S0765_C6, 435,
 ,[Offset], S0766_C6, 436,
 ,[Offset], S0767_C6, 437,
 ,[Offset], S077C_C6, 438,
RSID_TCIRQUS_SOUNDS_END, 3237,,,
RSID_TCIRQUS_SOUNDS_MECHANICAL, 3238,,,
 ,[Offset], boomballoon, 0,
RSID_TCIRQUS_SAMPLES, 3239,,,
RSID_TCIRQUS_VERSION, 3240,,,
RSID_TCIRQUS_END, 3241,,,
