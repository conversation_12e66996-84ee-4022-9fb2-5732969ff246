# Set vcpkg toolchain file
set(CMAKE_TOOLCHAIN_FILE "D:/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE PATH "Vcpkg toolchain file")

cmake_minimum_required(VERSION 3.16)

project(PS4Emulator)

# Set Vulkan SDK path
set(ENV{VULKAN_SDK} "D:/VulkanSDK/1.4.309.0")
set(VULKAN_SDK "D:/VulkanSDK/1.4.309.0")

# Intel oneAPI configuration
set(ONEAPI_ROOT "C:/Program Files (x86)/Intel/oneAPI" CACHE PATH "Intel oneAPI installation path")
set(CMAKE_CXX_COMPILER "${ONEAPI_ROOT}/compiler/latest/bin/icx.exe" CACHE FILEPATH "Intel C++ Compiler")
set(CMAKE_C_COMPILER "${ONEAPI_ROOT}/compiler/latest/bin/icx.exe" CACHE FILEPATH "Intel C Compiler")

# Fallback to <PERSON> if MSVC or Intel compiler isn't available
if(NOT MSVC AND NOT EXISTS "${CMAKE_CXX_COMPILER}")
    set(CMAKE_GENERATOR "Ninja" CACHE STRING "" FORCE)
endif()

# C++23 standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Add defines
add_definitions(-DWINDOWS_IGNORE_PACKING_MISMATCH)

# Source files
file(GLOB_RECURSE PS4EMU_SOURCES
   "${CMAKE_SOURCE_DIR}/src/gui/backends/*.cpp"  # SDL2 backend
    "${CMAKE_SOURCE_DIR}/main.cpp"
    "${CMAKE_SOURCE_DIR}/cache/*.cpp"
    "${CMAKE_SOURCE_DIR}/cpu/*.cpp"
    "${CMAKE_SOURCE_DIR}/emulator/*.cpp"
    "${CMAKE_SOURCE_DIR}/gui/*.cpp"
    "${CMAKE_SOURCE_DIR}/jit/*.cpp"
    "${CMAKE_SOURCE_DIR}/loader/*.cpp"
    "${CMAKE_SOURCE_DIR}/loader/self_decrypter.cpp"
    "${CMAKE_SOURCE_DIR}/loader/crypto_utils.cpp"
    "${CMAKE_SOURCE_DIR}/memory/*.cpp"
    "${CMAKE_SOURCE_DIR}/ps4/*.cpp"
    "${CMAKE_SOURCE_DIR}/common/*.cpp"
    "${CMAKE_SOURCE_DIR}/syscall/*.cpp"
    "${CMAKE_SOURCE_DIR}/video_core/*.cpp"
)

find_package(PkgConfig REQUIRED)

# List FFmpeg modules
pkg_check_modules(FFMPEG_PKGS REQUIRED
    libavcodec
    libavdevice
    libavfilter
    libavformat
    libavutil
    libswresample
    libswscale
)

# Use these values if vcpkg didn't already set them
if(NOT FFMPEG_INCLUDE_DIRS)
    set(FFMPEG_INCLUDE_DIRS ${FFMPEG_PKGS_INCLUDE_DIRS})
endif()
if(NOT FFMPEG_LIBRARIES)
    set(FFMPEG_LIBRARIES ${FFMPEG_PKGS_LIBRARIES})
endif()
if(NOT FFMPEG_LIBRARY_DIRS)
    link_directories(${FFMPEG_PKGS_LIBRARY_DIRS})
endif()

# Main executable
add_executable(PS4Emulator ${PS4EMU_SOURCES})

# Specify local LLVM installation
set(LLVM_DIR "D:/bin/llvm-project-main/llvm/cmake/modules")
find_path(HALF_INCLUDE_DIRS "half.hpp")
link_directories("C:/Program Files (x86)/Windows Kits/10/Lib/10.0.26100.0/um/x64")


# Include directories
target_include_directories(PS4Emulator PRIVATE
    "${CMAKE_SOURCE_DIR}"
    "${CMAKE_SOURCE_DIR}/cache"
    "${CMAKE_SOURCE_DIR}/cpu"
    "${CMAKE_SOURCE_DIR}/emulator"
    "${CMAKE_SOURCE_DIR}/gui//backends"
    "${CMAKE_SOURCE_DIR}/gui"
    "${CMAKE_SOURCE_DIR}/jit"
    "${CMAKE_SOURCE_DIR}/loader"
    "${CMAKE_SOURCE_DIR}/memory"
    "${CMAKE_SOURCE_DIR}/ps4"
    "${CMAKE_SOURCE_DIR}/common"
    "${CMAKE_SOURCE_DIR}/syscall"
    "${CMAKE_SOURCE_DIR}/video_core"
    "${CMAKE_CURRENT_BINARY_DIR}"
    "${SDL2_INCLUDE_DIRS}"
    "${Vulkan_INCLUDE_DIRS}"
    "${IMGUI_INCLUDE_DIRS}"
    "${SPDLOG_INCLUDE_DIRS}"
    "${FFMPEG_INCLUDE_DIRS}"
    "${LLVM_INCLUDE_DIRS}"
    "${HALF_INCLUDE_DIRS}"
    "${ONEAPI_ROOT}/mkl/latest/include" # Intel MKL
)
# Add FFmpeg module path
list(APPEND CMAKE_MODULE_PATH "D:/vcpkg/installed/x64-windows/share/ffmpeg")


# Find dependencies using vcpkg
find_package(SDL2 CONFIG REQUIRED)
find_package(Vulkan REQUIRED)
find_package(libzip CONFIG REQUIRED)
find_package(imgui CONFIG REQUIRED)
find_package(spdlog CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
find_package(portaudio CONFIG REQUIRED)
find_package(ZLIB REQUIRED)
find_package(unofficial-spirv-reflect CONFIG REQUIRED)
find_package(xbyak CONFIG REQUIRED)
find_package(TBB CONFIG REQUIRED)
find_package(pugixml CONFIG REQUIRED)
find_package(tsl-robin-map CONFIG REQUIRED)
find_package(toml11 CONFIG REQUIRED)
find_package(Tracy CONFIG REQUIRED)
find_package(xxHash CONFIG REQUIRED)
find_package(zlib-ng CONFIG REQUIRED)
find_package(zydis CONFIG REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(PNG REQUIRED)
find_package(elfio CONFIG REQUIRED)
find_package(glslang CONFIG REQUIRED)
find_package(fmt CONFIG REQUIRED)
find_package(LLVM REQUIRED CONFIG)
find_package(Freetype REQUIRED)
find_package(plutosvg CONFIG REQUIRED)
find_package(capstone CONFIG REQUIRED)
find_package(FFMPEG REQUIRED)



# Link libraries
target_link_libraries(PS4Emulator PRIVATE
    SDL2::SDL2main
    SDL2::SDL2
    Vulkan::Vulkan
    libzip::zip
    imgui::imgui
    spdlog::spdlog
    nlohmann_json::nlohmann_json
    portaudio
    ZLIB::ZLIB
    xbyak::xbyak
    TBB::tbb
    capstone::capstone
    ${FFMPEG_LIBRARIES}
    pugixml::pugixml
    tsl::robin_map
    toml11::toml11
    Tracy::TracyClient
    xxHash::xxhash
    Zydis::Zydis
    OpenSSL::SSL
    OpenSSL::Crypto
    ${OPENSSL_LIBRARIES}
    PNG::PNG
    glslang::glslang
    glslang::glslang-default-resource-limits
    glslang::SPIRV
    glslang::SPVRemapper
    plutosvg::plutosvg
    Freetype::Freetype
    ${LLVM_LIBS}
)

# Set a default PS4 physical memory size (8 GiB)
target_compile_definitions(PS4Emulator PRIVATE
    PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000
    _M_X64
    _AMD64_
    WIN64
    _WIN64
    SPDLOG_NO_COMPILE_TIME_FMT
    IMGUI_ENABLE_FREETYPE
)

# Platform-specific settings
if(MSVC OR "${CMAKE_CXX_COMPILER_ID}" STREQUAL "Intel")
    # DIA SDK for Visual Studio 2022 Community
    set(DIA_SDK_PATH "C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK")
    target_include_directories(PS4Emulator PRIVATE "${DIA_SDK_PATH}/include")
    target_link_directories(PS4Emulator PRIVATE "${DIA_SDK_PATH}/lib/amd64")
    target_link_libraries(PS4Emulator PRIVATE diaguids.lib)

    # Windows libraries
    target_link_libraries(PS4Emulator PRIVATE kernel32 user32 gdi32 winspool shell32 ole32 oleaut32 uuid comdlg32 advapi32)
    target_compile_options(PS4Emulator PRIVATE /EHsc /Qopenmp /Qpar) # Intel-specific options
    # Use static runtime for consistency
    foreach(flag_var CMAKE_CXX_FLAGS CMAKE_CXX_FLAGS_DEBUG CMAKE_CXX_FLAGS_RELEASE CMAKE_CXX_FLAGS_MINSIZEREL CMAKE_CXX_FLAGS_RELWITHDEBINFO)
        if(${flag_var} MATCHES "/MD")
            string(REGEX REPLACE "/MD" "/MT" ${flag_var} "${${flag_var}}")
        endif()
    endforeach()
elseif(MINGW)
    target_link_libraries(PS4Emulator PRIVATE -static-libgcc -static-libstdc++ -lpthread)
endif()

# Debug build settings
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(PS4Emulator PRIVATE
        $<$<CXX_COMPILER_ID:MSVC,Intel>:/Zi /Od>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC,Intel>>:-g -O0>
    )
endif()

# Enable optimizations for Release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(PS4Emulator PRIVATE
        $<$<CXX_COMPILER_ID:MSVC,Intel>:/O2 /Qvec /Qipo>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC,Intel>>:-O3 -march=native>
    )
endif()

# Status messages
message(STATUS "Using CMAKE_CXX_COMPILER: ${CMAKE_CXX_COMPILER}")
message(STATUS "CMAKE_CXX_FLAGS: ${CMAKE_CXX_FLAGS}")
message(STATUS "LLVM_FOUND: ${LLVM_FOUND}")
message(STATUS "MKL_FOUND: ${MKL_FOUND}")