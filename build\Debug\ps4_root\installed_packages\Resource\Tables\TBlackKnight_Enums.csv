RSID_TBLACKKNIGHT_START, 3475,,,
 ,[Offset], BlackKnight_Flyer_1, 0,
 ,[Offset], BlackKnight\PBTBlackKnight, 1,
 ,[Offset], BlackKnight\iOS and Android\InstructionsENG, 2,
 ,[Offset], BlackKnight\iOS and Android\InstructionsFR, 3,
 ,[Offset], BlackKnight\iOS and Android\InstructionsITAL, 4,
 ,[Offset], BlackKnight\iOS and Android\InstructionsGERM, 5,
 ,[Offset], BlackKnight\iOS and Android\InstructionsSPAN, 6,
 ,[Offset], BlackKnight\InstructionsPORT, 7,
 ,[Offset], BlackKnight\InstructionsDUTCH, 8,
 ,[Offset], tables\BlackKnight_BG_scroll, 9,
RSID_TBLACKKNIGHT_SCRIPT, 3476,,,
RSID_TBLACKKNIGHT_LIGHTS, 3477,,,
RSID_TBLACKKNIGHT_CAMERAS, 3478,,,
RSID_TBLACKKNIGHT_LAMP_TEXTURES, 3479,,,
 ,[Offset], lamp01_on, 0,
 ,[Offset], lamp01_off, 1,
 ,[Offset], lamp02_on, 2,
 ,[Offset], lamp02_off, 3,
 ,[Offset], lamp03_on, 4,
 ,[Offset], lamp03_off, 5,
 ,[Offset], lamp04_on, 6,
 ,[Offset], lamp04_off, 7,
 ,[Offset], lamp05_on, 8,
 ,[Offset], lamp05_off, 9,
 ,[Offset], lamp06_on, 10,
 ,[Offset], lamp06_off, 11,
 ,[Offset], lamp07_on, 12,
 ,[Offset], lamp07_off, 13,
 ,[Offset], lamp08_on, 14,
 ,[Offset], lamp08_off, 15,
 ,[Offset], lamp09_on, 16,
 ,[Offset], lamp09_off, 17,
 ,[Offset], lamp10_on, 18,
 ,[Offset], lamp10_off, 19,
 ,[Offset], lamp11_on, 20,
 ,[Offset], lamp11_off, 21,
 ,[Offset], lamp12_on, 22,
 ,[Offset], lamp12_off, 23,
 ,[Offset], lamp13_on, 24,
 ,[Offset], lamp13_off, 25,
 ,[Offset], lamp14_on, 26,
 ,[Offset], lamp14_off, 27,
 ,[Offset], lamp15_on, 28,
 ,[Offset], lamp15_off, 29,
 ,[Offset], lamp16_on, 30,
 ,[Offset], lamp16_off, 31,
 ,[Offset], lamp17_on, 32,
 ,[Offset], lamp17_off, 33,
 ,[Offset], lamp18_on, 34,
 ,[Offset], lamp18_off, 35,
 ,[Offset], lamp19_on, 36,
 ,[Offset], lamp19_off, 37,
 ,[Offset], lamp20_on, 38,
 ,[Offset], lamp20_off, 39,
 ,[Offset], lamp21_on, 40,
 ,[Offset], lamp21_off, 41,
 ,[Offset], lamp22_on, 42,
 ,[Offset], lamp22_off, 43,
 ,[Offset], lamp23_on, 44,
 ,[Offset], lamp23_off, 45,
 ,[Offset], lamp24_on, 46,
 ,[Offset], lamp24_off, 47,
 ,[Offset], lamp25_on, 48,
 ,[Offset], lamp25_off, 49,
 ,[Offset], lamp26_on, 50,
 ,[Offset], lamp26_off, 51,
 ,[Offset], lamp27_on, 52,
 ,[Offset], lamp27_off, 53,
 ,[Offset], lamp28_on, 54,
 ,[Offset], lamp28_off, 55,
 ,[Offset], lamp29_on, 56,
 ,[Offset], lamp29_off, 57,
 ,[Offset], lamp30_on, 58,
 ,[Offset], lamp30_off, 59,
 ,[Offset], lamp31_on, 60,
 ,[Offset], lamp31_off, 61,
 ,[Offset], lamp32_on, 62,
 ,[Offset], lamp32_off, 63,
 ,[Offset], lamp33_on, 64,
 ,[Offset], lamp33_off, 65,
 ,[Offset], lamp34_on, 66,
 ,[Offset], lamp34_off, 67,
 ,[Offset], lamp35_on, 68,
 ,[Offset], lamp35_off, 69,
 ,[Offset], lamp36_on, 70,
 ,[Offset], lamp36_off, 71,
 ,[Offset], lamp37_on, 72,
 ,[Offset], lamp37_off, 73,
 ,[Offset], lamp38_on, 74,
 ,[Offset], lamp38_off, 75,
 ,[Offset], lamp39_on, 76,
 ,[Offset], lamp39_off, 77,
 ,[Offset], lamp40_on, 78,
 ,[Offset], lamp40_off, 79,
 ,[Offset], lamp41_on, 80,
 ,[Offset], lamp41_off, 81,
 ,[Offset], lamp42_on, 82,
 ,[Offset], lamp42_off, 83,
 ,[Offset], lamp43_bumper_on, 84,
 ,[Offset], lamp43_bumper_off, 85,
 ,[Offset], lamp44_on, 86,
 ,[Offset], lamp44_off, 87,
 ,[Offset], lamp45_on, 88,
 ,[Offset], lamp45_off, 89,
 ,[Offset], lamp46_on, 90,
 ,[Offset], lamp46_off, 91,
 ,[Offset], lamp47_on, 92,
 ,[Offset], lamp47_off, 93,
 ,[Offset], lamp48_on, 94,
 ,[Offset], lamp48_off, 95,
 ,[Offset], lamp49_on, 96,
 ,[Offset], lamp49_off, 97,
 ,[Offset], lamp50_on, 98,
 ,[Offset], lamp50_off, 99,
 ,[Offset], lamp51_on, 100,
 ,[Offset], lamp51_off, 101,
 ,[Offset], lamp52_on, 102,
 ,[Offset], lamp52_off, 103,
RSID_TBLACKKNIGHT_TEXTURES, 3480,,,
 ,[Offset], BK_A1_0, 0,
 ,[Offset], BK_A2_0, 1,
 ,[Offset], BK_B1_0, 2,
 ,[Offset], BK_B2_0, 3,
 ,[Offset], BK_A3_0, 4,
 ,[Offset], BK_A4_0, 5,
 ,[Offset], BK_B3_0, 6,
 ,[Offset], BK_B4_0, 7,
 ,[Offset], BK_A5_0, 8,
 ,[Offset], BK_A6_0, 9,
 ,[Offset], BK_B5_0, 10,
 ,[Offset], BK_B6_0, 11,
 ,[Offset], BK_A7_0, 12,
 ,[Offset], BK_A8_0, 13,
 ,[Offset], BK_B7_0, 14,
 ,[Offset], BK_B8_0, 15,
 ,[Offset], BK_C1_0, 16,
 ,[Offset], BK_C2_0, 17,
 ,[Offset], BK_D1_0, 18,
 ,[Offset], BK_D2_0, 19,
 ,[Offset], BK_C3_0, 20,
 ,[Offset], BK_C4_0, 21,
 ,[Offset], BK_D3_0, 22,
 ,[Offset], BK_D4_0, 23,
 ,[Offset], BK_C5_0, 24,
 ,[Offset], BK_C6_0, 25,
 ,[Offset], BK_D5_0, 26,
 ,[Offset], BK_D6_0, 27,
 ,[Offset], BK_C7_0, 28,
 ,[Offset], BK_C8_0, 29,
 ,[Offset], BK_D7_0, 30,
 ,[Offset], BK_D8_0, 31,
 ,[Offset], BK_E1_0, 32,
 ,[Offset], BK_E2_0, 33,
 ,[Offset], BK_F1_0, 34,
 ,[Offset], BK_F2_0, 35,
 ,[Offset], BK_E3_0, 36,
 ,[Offset], BK_E4_0, 37,
 ,[Offset], BK_F3_0, 38,
 ,[Offset], BK_F4_0, 39,
 ,[Offset], BK_E5_0, 40,
 ,[Offset], BK_E6_0, 41,
 ,[Offset], BK_F5_0, 42,
 ,[Offset], BK_F6_0, 43,
 ,[Offset], BK_E7_0, 44,
 ,[Offset], BK_E8_0, 45,
 ,[Offset], BK_F7_0, 46,
 ,[Offset], BK_F8_0, 47,
 ,[Offset], BK_G1_0, 48,
 ,[Offset], BK_G2_0, 49,
 ,[Offset], BK_H1_0, 50,
 ,[Offset], BK_H2_0, 51,
 ,[Offset], BK_G3_0, 52,
 ,[Offset], BK_G4_0, 53,
 ,[Offset], BK_H3_0, 54,
 ,[Offset], BK_H4_0, 55,
 ,[Offset], BK_G5_0, 56,
 ,[Offset], BK_G6_0, 57,
 ,[Offset], BK_H5_0, 58,
 ,[Offset], BK_H6_0, 59,
 ,[Offset], BK_G7_0, 60,
 ,[Offset], BK_G8_0, 61,
 ,[Offset], BK_H7_0, 62,
 ,[Offset], BK_H8_0, 63,
 ,[Offset], bumperA_0, 64,
 ,[Offset], bumperB_0, 65,
 ,[Offset], bumperC_0, 66,
 ,[Offset], bumperD_0, 67,
 ,[Offset], bumperE_0, 68,
 ,[Offset], TableRules_R1_0, 69,
 ,[Offset], TableRules_R2_0, 70,
 ,[Offset], TableRules_R3_0, 71,
 ,[Offset], TableRules_R4_0, 72,
 ,[Offset], TableRules_L1_0, 73,
 ,[Offset], TableRules_L2_0, 74,
 ,[Offset], TableRules_L3_0, 75,
 ,[Offset], TableRules_L4_0, 76,
 ,[Offset], backGlass_0, 77,
 ,[Offset], blank, 78,
 ,[Offset], backglassA_0, 79,
 ,[Offset], backglassB_0, 80,
 ,[Offset], backglassC_0, 81,
 ,[Offset], backglassD_0, 82,
 ,[Offset], backglassE_0, 83,
 ,[Offset], backglassF_0, 84,
 ,[Offset], backglassG_0, 85,
 ,[Offset], backglassH_0, 86,
 ,[Offset], plunger_plate_baked, 87,
 ,[Offset], plunger_metal, 88,
 ,[Offset], Ball Bumper Stop, 89,
 ,[Offset], TableSide, 90,
 ,[Offset], floor 1A_0, 91,
 ,[Offset], WoodEdge_Tile, 92,
 ,[Offset], glass, 93,
 ,[Offset], BK_TableFront_Side, 94,
 ,[Offset], GG_TableFront_Side, 95,
 ,[Offset], Buttons_Parts, 96,
 ,[Offset], metal-parts01 copy, 97,
 ,[Offset], metal_parts01, 98,
 ,[Offset], metal_parts02, 99,
 ,[Offset], metal_parts03, 100,
 ,[Offset], tmp_gray, 101,
 ,[Offset], tmp_orange, 102,
 ,[Offset], speaker, 103,
 ,[Offset], post red, 104,
 ,[Offset], plunger, 105,
 ,[Offset], CoinSlots, 106,
 ,[Offset], screw alt, 107,
 ,[Offset], screw white, 108,
 ,[Offset], screw, 109,
 ,[Offset], rubber, 110,
 ,[Offset], post, 111,
 ,[Offset], GorGar_Table_down_B, 112,
 ,[Offset], metal_legs, 113,
 ,[Offset], metal_trim, 114,
 ,[Offset], flipper, 115,
 ,[Offset], tile, 116,
 ,[Offset], blackscrew, 117,
 ,[Offset], spinner, 118,
 ,[Offset], metal front, 119,
 ,[Offset], side_plate, 120,
 ,[Offset], side_plate2, 121,
 ,[Offset], lightbulb, 122,
 ,[Offset], clear, 123,
 ,[Offset], pop_bumper, 124,
 ,[Offset], ramps, 125,
 ,[Offset], white_screw, 126,
 ,[Offset], metal-parts01, 127,
 ,[Offset], Extra_Metal_Parts, 128,
 ,[Offset], Generic_Metal, 129,
 ,[Offset], wooden_rails, 130,
RSID_TBLACKKNIGHT_MODELS, 3481,,,
 ,[Offset], floor, 0,
 ,[Offset], flipper A, 6,
 ,[Offset], flipper B, 7,
 ,[Offset], plunger, 8,
 ,[Offset], tile, 9,
 ,[Offset], gate oneway, 10,
 ,[Offset], wire a, 11,
 ,[Offset], gate, 12,
 ,[Offset], slingshot a, 13,
 ,[Offset], slingshot a ext, 14,
 ,[Offset], slingshot b, 15,
 ,[Offset], slingshot b ext, 16,
 ,[Offset], pop bumper a, 17,
 ,[Offset], trap A, 18,
 ,[Offset], trap B, 19,
 ,[Offset], spinner, 20,
 ,[Offset], generic a, 21,
 ,[Offset], apron, 22,
 ,[Offset], Bolts, 23,
 ,[Offset], Cabinet_Backglass, 24,
 ,[Offset], Cabinet_Body, 25,
 ,[Offset], cabinet_metals, 26,
 ,[Offset], metal_1, 27,
 ,[Offset], Metal_2, 28,
 ,[Offset], Metal_3, 29,
 ,[Offset], metal_posts, 30,
 ,[Offset], plastic_pieces, 31,
 ,[Offset], pop_bumper, 32,
 ,[Offset], red_posts, 33,
 ,[Offset], rubber_pieces, 34,
 ,[Offset], wooden_rails, 35,
 ,[Offset], Light_Cutouts, 36,
 ,[Offset], Metal_Ramp_A, 37,
 ,[Offset], Metal_Ramp_B, 38,
 ,[Offset], Metal_Ramp_C, 39,
RSID_TBLACKKNIGHT_MODELS_LODS, 3482,,,
 ,[Offset], floor, 0,
 ,[Offset], flipper A, 6,
 ,[Offset], flipper B, 7,
 ,[Offset], plunger, 8,
 ,[Offset], tile, 9,
 ,[Offset], gate oneway, 10,
 ,[Offset], wire a, 11,
 ,[Offset], gate, 12,
 ,[Offset], slingshot a, 13,
 ,[Offset], slingshot a ext, 14,
 ,[Offset], slingshot b, 15,
 ,[Offset], slingshot b ext, 16,
 ,[Offset], pop bumper a, 17,
 ,[Offset], trap A, 18,
 ,[Offset], trap B, 19,
 ,[Offset], spinner, 20,
 ,[Offset], generic a, 21,
 ,[Offset], apron, 22,
 ,[Offset], Bolts, 23,
 ,[Offset], Cabinet_Backglass, 24,
 ,[Offset], Cabinet_Body, 25,
 ,[Offset], cabinet_metals, 26,
 ,[Offset], metal_1, 27,
 ,[Offset], Metal_2, 28,
 ,[Offset], Metal_3, 29,
 ,[Offset], metal_posts, 30,
 ,[Offset], plastic_pieces, 31,
 ,[Offset], pop_bumper, 32,
 ,[Offset], red_posts, 33,
 ,[Offset], rubber_pieces, 34,
 ,[Offset], wooden_rails, 35,
 ,[Offset], Light_Cutouts, 36,
 ,[Offset], Metal_Ramp_A, 37,
 ,[Offset], Metal_Ramp_B, 38,
 ,[Offset], Metal_Ramp_C, 39,
RSID_TBLACKKNIGHT_COLLISIONS, 3483,,,
 ,[Offset], col wall 01, 0,
 ,[Offset], col wall 02, 1,
 ,[Offset], col wall 03, 2,
 ,[Offset], col wall 04, 3,
 ,[Offset], col wall 05, 4,
 ,[Offset], col floor 01, 5,
 ,[Offset], col floor 02, 6,
 ,[Offset], col floor 03, 7,
 ,[Offset], col floor 04, 8,
 ,[Offset], col alt wall 01, 9,
 ,[Offset], col alt wall 02, 10,
 ,[Offset], col alt wall 03, 11,
 ,[Offset], col alt wall 04, 12,
 ,[Offset], col alt wall 05, 13,
 ,[Offset], col alt wall 06, 14,
 ,[Offset], col alt wall 07, 15,
 ,[Offset], col alt wall 08, 16,
 ,[Offset], col alt wall 09, 17,
 ,[Offset], col alt wall 10, 18,
 ,[Offset], col alt wall 11, 19,
 ,[Offset], col alt wall 12, 20,
 ,[Offset], col alt wall 13, 21,
 ,[Offset], col arc 01, 22,
 ,[Offset], col arc 02, 23,
 ,[Offset], col arc 03, 24,
 ,[Offset], col arc 04, 25,
 ,[Offset], col arc 05, 26,
 ,[Offset], col arc 06, 27,
 ,[Offset], col arc 07, 28,
 ,[Offset], col arc 08, 29,
 ,[Offset], col arc 09, 30,
 ,[Offset], col arc 10, 31,
 ,[Offset], col arc 11, 32,
 ,[Offset], col ramp 01, 33,
 ,[Offset], col ramp 02, 34,
 ,[Offset], col ramp 03, 35,
 ,[Offset], platform 1, 36,
 ,[Offset], flipper A f col, 37,
 ,[Offset], flipper B f col, 38,
 ,[Offset], flipper A B col, 39,
 ,[Offset], flipper B B col, 40,
 ,[Offset], plunger col, 41,
 ,[Offset], plunger area col, 42,
 ,[Offset], tile col, 43,
 ,[Offset], gate oneway col, 44,
 ,[Offset], slingshot a col, 45,
 ,[Offset], slingshot b col, 46,
 ,[Offset], gate col, 47,
 ,[Offset], pop bumper a col, 48,
 ,[Offset], trap a, 49,
 ,[Offset], trap b, 50,
 ,[Offset], magnet A col, 51,
 ,[Offset], spinner, 52,
 ,[Offset], generic a col, 53,
 ,[Offset], generic b col, 54,
 ,[Offset], Left_Flipper_Back, 55,
 ,[Offset], Left_Flipper_Front, 56,
 ,[Offset], Right_Flipper_Back, 57,
 ,[Offset], Right_Flipper_Front, 58,
 ,[Offset], Ball_Jump, 59,
 ,[Offset], Ramp_Fix, 60,
RSID_TBLACKKNIGHT_PLACEMENT, 3484,,,
 ,[Offset], placement, 0,
 ,[Offset], Flipper, 1,
 ,[Offset], Lights, 2,
RSID_TBLACKKNIGHT_SOUNDS, 3485,,,
 ,[Offset], Lights, 0,
 ,[Offset], Lights, 1,
 ,[Offset], Lights, 2,
 ,[Offset], Lights, 3,
 ,[Offset], Lights, 4,
 ,[Offset], Lights, 5,
 ,[Offset], Lights, 6,
 ,[Offset], Lights, 7,
 ,[Offset], Lights, 8,
 ,[Offset], Lights, 9,
 ,[Offset], Lights, 10,
 ,[Offset], Lights, 11,
 ,[Offset], Lights, 12,
 ,[Offset], Lights, 13,
 ,[Offset], Lights, 14,
 ,[Offset], Lights, 15,
 ,[Offset], Lights, 16,
 ,[Offset], Lights, 17,
 ,[Offset], Lights, 18,
 ,[Offset], Lights, 19,
 ,[Offset], Lights, 20,
 ,[Offset], Lights, 21,
 ,[Offset], Lights, 22,
 ,[Offset], Lights, 23,
 ,[Offset], Lights, 24,
 ,[Offset], Lights, 25,
 ,[Offset], Lights, 26,
 ,[Offset], Lights, 27,
 ,[Offset], Lights, 28,
 ,[Offset], Lights, 29,
 ,[Offset], Lights, 30,
 ,[Offset], Lights, 31,
 ,[Offset], Lights, 32,
 ,[Offset], Lights, 33,
 ,[Offset], Lights, 34,
 ,[Offset], BK_spinner, 35,
 ,[Offset], BK_drop_targets_complete_loop, 36,
 ,[Offset], BK_phone, 37,
 ,[Offset], You_win_the_right, 38,
 ,[Offset], single_explosion, 39,
 ,[Offset], BK_HIT3_loop, 40,
 ,[Offset], BK_5_explosions, 41,
 ,[Offset], BK_center_trap, 42,
 ,[Offset], BK_crowd sounds, 43,
 ,[Offset], BK_Down_left_ramp_from_top, 44,
 ,[Offset], BK_drop_targets, 45,
 ,[Offset], BK_HIT1_loop, 46,
 ,[Offset], BK_Hoofbeats_wire_rollover_loop1, 47,
 ,[Offset], BK_long_asender, 48,
 ,[Offset], BK_long_desender, 49,
 ,[Offset], BK_Ray_gun_fade, 50,
 ,[Offset], BK_slingshot, 51,
 ,[Offset], BK_spinner, 52,
 ,[Offset], BK_TILT, 53,
 ,[Offset], BK_defend Thyself Knight, 54,
 ,[Offset], i_challange_thee_to_fight_me, 55,
 ,[Offset], you_cannot_fight_and_win, 56,
 ,[Offset], BK_i_will_slay_you, 57,
 ,[Offset], BK_The_Black_Knight_Will_win, 58,
 ,[Offset], BK_Black Knight will slay you, 59,
 ,[Offset], fight_against_me_the_black_knight, 60,
 ,[Offset], i_will_slay_you_knight, 61,
 ,[Offset], fight_against_2_enemies, 62,
 ,[Offset], fight_against_3_enemies, 63,
 ,[Offset], laughter, 64,
 ,[Offset], i_cannot_slay_you_you_win, 65,
 ,[Offset], fight_me_again_knight, 66,
 ,[Offset], BK_cannot fight the Black Knight again, 67,
 ,[Offset], you_win_one_fight, 68,
 ,[Offset], the_black_knight_will_win_again, 69,
 ,[Offset], BK_will_you_challange, 70,
 ,[Offset], BK_Background_loop1, 71,
 ,[Offset], BK_Background_loop2_(opening), 72,
 ,[Offset], BK_Background_loop3, 73,
 ,[Offset], BK_Background_loop4, 74,
 ,[Offset], BK_Background_loop5, 75,
 ,[Offset], BK_Background_loop6, 76,
 ,[Offset], BK_Background_loop7, 77,
 ,[Offset], BK_Background_loop8, 78,
 ,[Offset], BK_Background_loop9, 79,
 ,[Offset], BK_Background_loop10, 80,
 ,[Offset], BK_Background_loop11, 81,
 ,[Offset], BK_Background_loop12, 82,
 ,[Offset], BK_Background_loop13, 83,
 ,[Offset], BK_Background_loop14, 84,
 ,[Offset], BK_Background_loop15, 85,
 ,[Offset], BK_Background_loop16, 86,
 ,[Offset], BK_Background_loop17, 87,
 ,[Offset], BK_Background_loop18, 88,
 ,[Offset], BK_Background_loop19, 89,
 ,[Offset], BK_Background_loop20, 90,
 ,[Offset], BK_Background_loop21, 91,
 ,[Offset], BK_Background_loop22, 92,
 ,[Offset], BK_Background_loop23, 93,
 ,[Offset], BK_Background_loop24, 94,
 ,[Offset], BK_Background_loop25, 95,
 ,[Offset], BK_Background_loop26, 96,
 ,[Offset], BK_Background_loop27, 97,
RSID_TBLACKKNIGHT_HUD, 3486,,,
RSID_TBLACKKNIGHT_HUD_720, 3487,,,
RSID_TBLACKKNIGHT_HUD_1080, 3488,,,
RSID_FONTTABLE_KNIGHT_FONT, 3489,,,
RSID_FONT_KNIGHT_HUD, 3490,,,
RSID_FONTTABLE_KNIGHT_FONTHD, 3491,,,
RSID_FONT_KNIGHT_HUDHD, 3492,,,
RSID_FONTTABLE_KNIGHT_FONTHDD, 3493,,,
RSID_FONT_KNIGHT_HUDHDD, 3494,,,
RSID_TBLACKKNIGHT_VERSION, 3495,,,
RSID_TBLACKKNIGHT_END, 3496,,,
