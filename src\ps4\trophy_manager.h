// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <functional>
#include <istream>
#include <map>
#include <ostream>
#include <pugixml.hpp>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>


namespace ps4 {

/**
 * @brief Enum for trophy grades.
 */
enum class TrophyGrade { BRONZE, SILVER, GOLD, PLATINUM };

/**
 * @brief Enum for trophy types.
 */
enum class TrophyType { NORMAL, HIDDEN };

/**
 * @brief Structure representing a trophy.
 */
struct Trophy {
  uint32_t id = 0;                         ///< Trophy ID
  std::string name;                        ///< Trophy name
  std::string description;                 ///< Trophy description
  TrophyGrade grade = TrophyGrade::BRONZE; ///< Trophy grade
  TrophyType type = TrophyType::NORMAL;    ///< Trophy type
  bool unlocked = false;                   ///< Unlocked state
  uint64_t unlockTime = 0;                 ///< Unlock timestamp
  std::string iconPath;                    ///< Icon file path
  uint64_t cacheHits = 0;                  ///< Cache hits for trophy access
  uint64_t cacheMisses = 0;                ///< Cache misses for trophy access
};

/**
 * @brief Structure representing a trophy set.
 */
struct TrophySet {
  std::string id;               ///< Set ID
  std::string title;            ///< Set title (alias for name)
  std::string name;             ///< Set name
  std::string description;      ///< Set description
  bool initialized = false;     ///< Initialized state
  std::vector<Trophy> trophies; ///< Trophies in set
  uint32_t unlockedCount = 0;   ///< Number of unlocked trophies
  uint32_t totalCount = 0;      ///< Total trophies
  uint32_t platinum = 0;        ///< Platinum trophy count
  uint32_t gold = 0;            ///< Gold trophy count
  uint32_t silver = 0;          ///< Silver trophy count
  uint32_t bronze = 0;          ///< Bronze trophy count
  uint32_t progress = 0;        ///< Unlock progress (0-100)
};

/**
 * @brief Enhanced online sync status.
 */
enum class SyncStatus {
  NOT_SYNCED = 0, ///< Not synchronized
  SYNCING = 1,    ///< Currently syncing
  SYNCED = 2,     ///< Successfully synchronized
  SYNC_FAILED = 3 ///< Synchronization failed
};

/**
 * @brief Enhanced trophy metadata for robust handling.
 */
struct TrophyMetadata {
  std::string gameId;        ///< Game identifier
  std::string version;       ///< Trophy set version
  std::string platform;      ///< Platform (PS4, PS5, etc.)
  uint64_t creationTime = 0; ///< Creation timestamp
  uint64_t lastModified = 0; ///< Last modification timestamp
  SyncStatus syncStatus = SyncStatus::NOT_SYNCED; ///< Sync status
  std::string checksum;                           ///< Data integrity checksum
  bool encrypted = false;                         ///< Encryption status
};

/**
 * @brief Enhanced structure for trophy manager statistics.
 */
struct TrophyStats {
  uint64_t totalUnlocks = 0;        ///< Total trophies unlocked
  uint64_t saveLatencyUs = 0;       ///< Total save latency (microseconds)
  uint64_t syncOperations = 0;      ///< Total sync operations
  uint64_t cacheHits = 0;           ///< Cache hits for trophy operations
  uint64_t cacheMisses = 0;         ///< Cache misses for trophy operations
  uint64_t xmlParseErrors = 0;      ///< XML parsing errors
  uint64_t xmlValidationErrors = 0; ///< XML validation errors
  uint64_t networkErrors = 0;       ///< Network-related errors
  uint64_t syncSuccesses = 0;       ///< Successful sync operations
  uint64_t syncFailures = 0;        ///< Failed sync operations
  uint64_t dataCorruptions = 0;     ///< Data corruption detections
  uint64_t backupOperations = 0;    ///< Backup operations performed
};

/**
 * @brief Exception for trophy-related errors.
 */
struct TrophyException : std::runtime_error {
  explicit TrophyException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Manages PS4 trophies, including unlocking and synchronization.
 * @details Handles trophy sets, XML loading, and online syncing, with
 * thread-safe access and metrics.
 */
class TrophyManager {
public:
  /**
   * @brief Constructs the trophy manager.
   */
  TrophyManager();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~TrophyManager();

  /**
   * @brief Initializes the trophy manager.
   * @param userId User ID for trophy data.
   * @return True on success, false otherwise.
   */
  bool Initialize(const std::string &userId);

  /**
   * @brief Loads a trophy set from an XML file.
   * @param path XML file path.
   * @param outSet Output trophy set.
   * @return True on success, false otherwise.
   */
  bool LoadTrophySetFromXml(const std::string &path, TrophySet &outSet);

  /**
   * @brief Unlocks a trophy in a set.
   * @param setId Trophy set ID.
   * @param trophyId Trophy ID.
   * @return True on success, false otherwise.
   */
  bool UnlockTrophy(const std::string &setId, uint32_t trophyId);

  /**
   * @brief Retrieves trophy information.
   * @param setId Trophy set ID.
   * @param trophyId Trophy ID.
   * @param outTrophy Output trophy data.
   * @return True on success, false otherwise.
   */
  bool GetTrophyInfo(const std::string &setId, uint32_t trophyId,
                     Trophy &outTrophy) const;

  /**
   * @brief Retrieves trophy information by user ID and trophy ID.
   * @param userId User ID.
   * @param trophyId Trophy ID.
   * @param outTrophy Output trophy data.
   * @return True on success, false otherwise.
   */
  bool GetTrophyInfo(const std::string &userId, uint32_t trophyId,
                     Trophy &outTrophy);

  /**
   * @brief Loads a trophy icon.
   * @param setId Trophy set ID.
   * @param trophyId Trophy ID.
   * @param outIcon Output icon data.
   * @return True on success, false otherwise.
   */
  bool LoadTrophyIcon(const std::string &setId, uint32_t trophyId,
                      std::vector<uint8_t> &outIcon);

  /**
   * @brief Synchronizes trophies with an online service.
   * @param setId Trophy set ID.
   * @return True on success, false otherwise.
   */
  bool SyncTrophiesOnline(const std::string &setId);

  /**
   * @brief Gets the unlock progress for a trophy set.
   * @param setId Trophy set ID.
   * @return Progress percentage (0-100).
   */
  uint32_t GetTrophyUnlockProgress(const std::string &setId) const;

  /**
   * @brief Shuts down the trophy manager.
   */
  void Shutdown();

  /**
   * @brief Saves the trophy manager state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the trophy manager state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Retrieves trophy manager statistics.
   * @return Current statistics.
   */
  TrophyStats GetStats() const;

  /**
   * @brief Enhanced trophy management methods.
   */
  bool ValidateXMLStructure(const std::string &xmlPath);
  bool BackupTrophyData(const std::string &setId);
  bool RestoreTrophyData(const std::string &setId,
                         const std::string &backupPath);
  bool VerifyDataIntegrity(const std::string &setId);
  std::string CalculateChecksum(const TrophySet &set);
  bool SyncTrophiesOnlineEnhanced(const std::string &setId,
                                  const std::string &serverUrl);
  bool HandleNetworkError(const std::string &operation, int errorCode);
  bool RetryOperation(std::function<bool()> operation, int maxRetries = 3);

private:
  /**
   * @brief Updates the progress of a trophy set.
   * @param set Trophy set to update.
   */
  void UpdateTrophySetProgress(TrophySet &set);

  /**
   * @brief Saves a trophy set to an XML file.
   * @param setId Trophy set ID.
   * @return True on success, false otherwise.
   */
  bool SaveTrophySetToXml(const std::string &setId);

  std::unordered_map<std::string, TrophySet> m_trophySets; ///< Trophy sets
  std::string m_userId;                                    ///< User ID
  mutable TrophyStats m_stats;             ///< Trophy statistics
  mutable std::shared_mutex m_trophyMutex; ///< Mutex for thread safety

  // Enhanced trophy management data
  std::unordered_map<std::string, TrophyMetadata>
      m_trophyMetadata;                                ///< Trophy metadata
  std::string m_backupDirectory = "trophies/backups/"; ///< Backup directory
  std::string m_serverUrl =
      "https://api.playstation.com/"; ///< Default server URL
  bool m_onlineMode = false;          ///< Online mode enabled
  uint32_t m_maxRetries = 3;          ///< Maximum retry attempts
  uint64_t m_retryDelayMs = 1000;     ///< Retry delay in milliseconds

  // XML validation helpers
  bool ValidateXMLNode(const pugi::xml_node &node,
                       const std::string &expectedName);
  bool ValidateXMLAttribute(const pugi::xml_node &node,
                            const std::string &attrName, bool required = true);
  bool SanitizeXMLData(std::string &data);

  // Network helpers
  bool SendHTTPRequest(const std::string &url, const std::string &data,
                       std::string &response);
  bool ParseServerResponse(const std::string &response, std::string &status,
                           std::string &message);

  // Data integrity helpers
  bool CreateBackup(const std::string &setId, const std::string &backupPath);
  bool VerifyBackup(const std::string &backupPath);
  std::string GenerateTimestamp();
};

} // namespace ps4