#pragma once
#ifndef NOMINMAX
#define NOMINMAX
#endif

#undef min
#undef max

#include <array>
#include <atomic>
#include <chrono>
#include <cstdint>
#include <functional>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <string>
#include <unordered_map>
#include <vector>

#include "../cpu/device.h"

namespace ps4 {
class PS4Emulator;
}

namespace x86_64 {
class InterruptHandler;

// I/O Port Constants
constexpr uint16_t PIT_PORT = 0x40;        ///< Programmable Interval Timer port
constexpr uint16_t PIC_MASTER_PORT = 0x20; ///< Master PIC port
constexpr uint16_t PIC_SLAVE_PORT = 0xA0;  ///< Slave PIC port
constexpr uint16_t KEYBOARD_PORT = 0x60;   ///< PS/2 Keyboard port
constexpr uint16_t DMA_PORT = 0x00;        ///< DMA controller port

/**
 * @brief Base class for I/O devices.
 */
class IODevice {
public:
  virtual ~IODevice() = default;
  /**
   * @brief Reads from the device.
   * @param addr Device-relative address.
   * @param data Output buffer.
   * @param size Data size.
   */
  virtual void Read(uint64_t addr, uint8_t *data, size_t size) = 0;
  /**
   * @brief Writes to the device.
   * @param addr Device-relative address.
   * @param data Input buffer.
   * @param size Data size.
   */
  virtual void Write(uint64_t addr, const uint8_t *data, size_t size) = 0;
  /**
   * @brief Saves device state.
   * @param out Output stream.
   */
  virtual void SaveState(std::ostream &out) const = 0;
  /**
   * @brief Loads device state.
   * @param in Input stream.
   */
  virtual void LoadState(std::istream &in) = 0;
  /**
   * @brief Gets the device name.
   * @return Device name.
   */
  virtual std::string GetName() const = 0;
};

/**
 * @brief I/O manager class handling device registration and I/O.
 */
class IOManager {
public:
  IOManager(ps4::PS4Emulator &emu, InterruptHandler &irqH)
      : m_emulator(emu), m_interruptHandler(irqH),
        m_eventQueue([](const Event &a, const Event &b) {
          return a.timestamp > b.timestamp;
        }) {}

  /**
   * @brief Destructs the IOManager, ensuring cleanup.
   */
  ~IOManager();

  /**
   * @brief Registers a legacy device with read/write handlers.
   * @param name Device name.
   * @param baseAddr Base address for MMIO/port I/O.
   * @param size Address range size.
   * @param readHandler Read handler function.
   * @param writeHandler Write handler function.
   * @return True on success, false if already registered.
   */
  bool RegisterDevice(
      const std::string &name, uint64_t baseAddr, uint64_t size,
      std::function<void(uint64_t, uint8_t *, size_t)> readHandler,
      std::function<void(uint64_t, const uint8_t *, size_t)> writeHandler);

  /**
   * @brief Registers a modern device with a Device interface.
   * @param device Unique pointer to the device.
   * @param baseAddr Base address for MMIO/port I/O.
   * @param size Address range size.
   * @return True on success, false if already registered.
   */
  bool RegisterDevice(std::unique_ptr<x86_64::Device> device, uint64_t baseAddr,
                      uint64_t size);

  /**
   * @brief Unregisters a device by name.
   * @param name Device name.
   */
  void UnregisterDevice(const std::string &name);

public:
  /**
   * @brief Initializes standard devices (PIT, PIC, Keyboard, DMA).
   * @return True on success, false on failure.
   */
  bool InitializeStandardDevices();

  /**
   * @brief Reads from an MMIO address.
   * @param addr MMIO address.
   * @param data Output buffer.
   * @param size Data size.
   * @return True on success, false if no device is mapped.
   */
  bool ReadMMIO(uint64_t addr, uint8_t *data, size_t size);

  /**
   * @brief Writes to an MMIO address.
   * @param addr MMIO address.
   * @param data Input buffer.
   * @param size Data size.
   * @return True on success, false if no device is mapped.
   */
  bool WriteMMIO(uint64_t addr, const uint8_t *data, size_t size);

  /**
   * @brief Reads from a port.
   * @param port Port number.
   * @param data Output buffer.
   * @param size Data size.
   * @return True on success, false if no device is mapped.
   */
  bool ReadPort(uint16_t port, uint8_t *data, size_t size);

  /**
   * @brief Writes to a port.
   * @param port Port number.
   * @param data Input buffer.
   * @param size Data size.
   * @return True on success, false if no device is mapped.
   */
  bool WritePort(uint16_t port, const uint8_t *data, size_t size);

  /**
   * @brief Raises an IRQ with specified priority.
   * @param irqNumber IRQ number.
   * @param priority Interrupt priority.
   */
  void RaiseIRQ(uint8_t irqNumber, uint8_t priority = 0);

  /**
   * @brief Lowers an IRQ.
   * @param irqNumber IRQ number.
   */
  void LowerIRQ(uint8_t irqNumber);

  /**
   * @brief Checks if an IRQ is active.
   * @param irqNumber IRQ number.
   * @return True if active, false otherwise.
   */
  bool IsIRQActive(uint8_t irqNumber) const;

  /**
   * @brief Retrieves all pending IRQs.
   * @return Vector of active IRQ numbers.
   */
  std::vector<uint8_t> GetPendingIRQs() const;

  /**
   * @brief Retrieves the keyboard state.
   * @param state Output buffer.
   * @param size Buffer size.
   * @return True if data was read, false if buffer is empty.
   */
  bool GetKeyboardState(uint8_t *state, size_t size);

  /**
   * @brief Sets the PIT frequency.
   * @param hz Frequency in Hz.
   */
  void SetTimerFrequency(uint64_t hz);

  /**
   * @brief Retrieves the current PIT tick count.
   * @return Number of ticks.
   */
  uint64_t GetTimerTicks() const;

  /**
   * @brief Initializes the PIT device.
   */
  void InitializePIT();

  /**
   * @brief Initializes the PIC devices (master and slave).
   */
  void InitializePIC();

  /**
   * @brief Initializes the PS/2 keyboard device.
   */
  void InitializeKeyboard();

  /**
   * @brief Initializes the DMA controller.
   */
  void InitializeDMA();

  /**
   * @brief Handles port I/O operations.
   * @param port Port number.
   * @param data Data buffer.
   * @param size Data size.
   * @param isRead True for read, false for write.
   * @return True on success, false if no device is mapped.
   */
  bool HandlePortIO(uint16_t port, uint8_t *data, size_t size, bool isRead);

  /**
   * @brief Processes the event queue and updates PIT ticks.
   */
  void Cycle();

  /**
   * @brief Saves the IOManager state.
   * @param out The output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the IOManager state.
   * @param in The input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief I/O manager statistics with atomic members to prevent race
   * conditions.
   */
  struct Stats {
    std::atomic<uint64_t> reads{0};       ///< Number of read operations
    std::atomic<uint64_t> writes{0};      ///< Number of write operations
    std::atomic<uint64_t> irqsRaised{0};  ///< Number of IRQs raised
    std::atomic<uint64_t> irqsLowered{0}; ///< Number of IRQs lowered
    std::atomic<uint64_t> device_registrations{
        0}; ///< Number of device registrations
    std::atomic<uint64_t> device_unregistrations{
        0}; ///< Number of device unregistrations
    std::atomic<uint64_t> keyboard_reads{
        0}; ///< Number of keyboard read operations
    std::atomic<uint64_t> keyboard_writes{
        0}; ///< Number of keyboard write operations
    std::atomic<uint64_t> pit_frequency_changes{
        0};                              ///< Number of PIT frequency changes
    std::atomic<uint64_t> pit_reads{0};  ///< Number of PIT read operations
    std::atomic<uint64_t> pit_writes{0}; ///< Number of PIT write operations
    std::atomic<uint64_t> pic_reads{0};  ///< Number of PIC read operations
    std::atomic<uint64_t> pic_writes{0}; ///< Number of PIC write operations
    std::atomic<uint64_t> pit_ticks{0};  ///< Number of PIT ticks
    std::atomic<uint64_t> event_callbacks{
        0};                              ///< Number of event callbacks executed
    std::atomic<uint64_t> dma_reads{0};  ///< Number of DMA read operations
    std::atomic<uint64_t> dma_writes{0}; ///< Number of DMA write operations
    std::atomic<uint64_t> total_latency_us{
        0}; ///< Total I/O latency in microseconds

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : reads(other.reads.load()), writes(other.writes.load()),
          irqsRaised(other.irqsRaised.load()),
          irqsLowered(other.irqsLowered.load()),
          device_registrations(other.device_registrations.load()),
          device_unregistrations(other.device_unregistrations.load()),
          keyboard_reads(other.keyboard_reads.load()),
          keyboard_writes(other.keyboard_writes.load()),
          pit_frequency_changes(other.pit_frequency_changes.load()),
          pit_reads(other.pit_reads.load()),
          pit_writes(other.pit_writes.load()),
          pic_reads(other.pic_reads.load()),
          pic_writes(other.pic_writes.load()),
          pit_ticks(other.pit_ticks.load()),
          event_callbacks(other.event_callbacks.load()),
          dma_reads(other.dma_reads.load()),
          dma_writes(other.dma_writes.load()),
          total_latency_us(other.total_latency_us.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        reads.store(other.reads.load());
        writes.store(other.writes.load());
        irqsRaised.store(other.irqsRaised.load());
        irqsLowered.store(other.irqsLowered.load());
        device_registrations.store(other.device_registrations.load());
        device_unregistrations.store(other.device_unregistrations.load());
        keyboard_reads.store(other.keyboard_reads.load());
        keyboard_writes.store(other.keyboard_writes.load());
        pit_frequency_changes.store(other.pit_frequency_changes.load());
        pit_reads.store(other.pit_reads.load());
        pit_writes.store(other.pit_writes.load());
        pic_reads.store(other.pic_reads.load());
        pic_writes.store(other.pic_writes.load());
        pit_ticks.store(other.pit_ticks.load());
        event_callbacks.store(other.event_callbacks.load());
        dma_reads.store(other.dma_reads.load());
        dma_writes.store(other.dma_writes.load());
        total_latency_us.store(other.total_latency_us.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves I/O manager statistics.
   * @return Current statistics.
   */
  Stats GetStats() const {
    // RACE CONDITION FIX: Create a copy with atomic loads
    Stats result;
    result.reads.store(m_stats.reads.load());
    result.writes.store(m_stats.writes.load());
    result.irqsRaised.store(m_stats.irqsRaised.load());
    result.irqsLowered.store(m_stats.irqsLowered.load());
    result.device_registrations.store(m_stats.device_registrations.load());
    result.device_unregistrations.store(m_stats.device_unregistrations.load());
    result.keyboard_reads.store(m_stats.keyboard_reads.load());
    result.keyboard_writes.store(m_stats.keyboard_writes.load());
    result.pit_frequency_changes.store(m_stats.pit_frequency_changes.load());
    result.pit_reads.store(m_stats.pit_reads.load());
    result.pit_writes.store(m_stats.pit_writes.load());
    result.pic_reads.store(m_stats.pic_reads.load());
    result.pic_writes.store(m_stats.pic_writes.load());
    result.pit_ticks.store(m_stats.pit_ticks.load());
    result.event_callbacks.store(m_stats.event_callbacks.load());
    result.dma_reads.store(m_stats.dma_reads.load());
    result.dma_writes.store(m_stats.dma_writes.load());
    result.total_latency_us.store(m_stats.total_latency_us.load());
    return result;
  }

private:
  /**
   * @brief Represents a legacy device with read/write handlers.
   */
  struct LegacyDevice {
    std::string name;      ///< Device name
    uint64_t baseAddr = 0; ///< Base address
    uint64_t size = 0;     ///< Address range size
    std::function<void(uint64_t, uint8_t *, size_t)>
        readHandler; ///< Read handler
    std::function<void(uint64_t, const uint8_t *, size_t)>
        writeHandler; ///< Write handler
  };

  /**
   * @brief Represents a registered modern device.
   */
  struct RegisteredDevice {
    std::unique_ptr<x86_64::Device> device; ///< Device instance
    uint64_t baseAddr = 0;          ///< Base address
    uint64_t size = 0;              ///< Address range size
  };

  /**
   * @brief Represents the state of an IRQ.
   */
  struct IRQState {
    bool active = false;    ///< Whether the IRQ is active
    uint8_t priority = 0;   ///< IRQ priority
    uint32_t nestCount = 0; ///< Nesting count for IRQ
  };

  /**
   * @brief Represents the state of the PIT.
   * CRITICAL FIX: Enhanced with proper channel support
   */
  struct PITState {
    uint64_t ticks = 0;           ///< Current tick count
    uint64_t frequency = 1193182; ///< Frequency in Hz
    struct Channel {
      uint16_t counter = 0;       ///< Channel counter value
      uint8_t access_mode = 0;    ///< Access mode (latch/LSB/MSB/both)
      uint8_t operating_mode = 0; ///< Operating mode (0-5)
      bool bcd_mode = false;      ///< BCD mode flag
      bool latch_mode = false;    ///< Latch mode for 16-bit access
    };
    std::array<Channel, 3> channels; ///< PIT channels 0-2
  };

  /**
   * @brief Represents the state of the keyboard.
   */
  struct KeyboardState {
    uint8_t buffer[256] = {}; ///< Keyboard input buffer
    size_t head = 0;          ///< Buffer head index
    size_t tail = 0;          ///< Buffer tail index
  };

  /**
   * @brief Represents the state of the PIC.
   * CRITICAL FIX: Enhanced with proper initialization and EOI support
   */
  struct PICState {
    uint8_t imr = 0xFF;  ///< Interrupt Mask Register (start masked)
    uint8_t isr = 0;     ///< In-Service Register
    uint8_t irr = 0;     ///< Interrupt Request Register
    uint8_t icw[4] = {}; ///< Initialization Command Words
    uint8_t init_state =
        0; ///< Initialization state (0=ready, 1-3=expecting ICW)
    bool read_isr = false; ///< Read ISR vs IRR flag
  };

  /**
   * @brief Represents the state of the DMA controller.
   * CRITICAL FIX: Enhanced with proper channel support and control registers
   */
  struct DMAState {
    struct Channel {
      uint16_t address = 0;       ///< Channel address
      uint16_t count = 0;         ///< Channel count
      uint8_t mode = 0;           ///< Channel mode register
      bool enabled = false;       ///< Channel enable status
      bool address_latch = false; ///< Address latch for 16-bit access
      bool count_latch = false;   ///< Count latch for 16-bit access
    };
    std::array<Channel, 4> channels; ///< DMA channels 0-3
    uint8_t command = 0;             ///< Command register
    uint8_t status = 0;              ///< Status register
    uint8_t request = 0;             ///< Request register
    uint8_t mask = 0xF;              ///< Mask register (start with all masked)
  };

  /**
   * @brief Represents an I/O event.
   */
  struct Event {
    enum class Type { IRQ, Timer, Custom }; ///< Event type
    Type type = Type::Custom;               ///< Current event type
    uint64_t timestamp = 0;                 ///< Event timestamp (ns)
    std::function<void()> callback;         ///< Event callback
  };
  ps4::PS4Emulator &m_emulator;         ///< Reference to the emulator
  InterruptHandler &m_interruptHandler; ///< Reference to interrupt handler
  std::unordered_map<std::string, LegacyDevice>
      m_legacyDevices; ///< Legacy devices
  std::unordered_map<std::string, RegisteredDevice>
      m_devices;                                     ///< Modern devices
  std::unordered_map<uint8_t, IRQState> m_irqStates; ///< IRQ states
  std::priority_queue<Event, std::vector<Event>,
                      std::function<bool(const Event &, const Event &)>>
      m_eventQueue;                   ///< Event queue
  mutable std::mutex m_deviceMutex;   ///< Mutex for device access
  mutable std::mutex m_irqMutex;      ///< Mutex for IRQ access
  mutable std::mutex m_pitMutex;      ///< Mutex for PIT access
  mutable std::mutex m_keyboardMutex; ///< Mutex for keyboard access
  mutable std::mutex m_eventMutex;    ///< Mutex for event queue access
  PITState m_pit;                     ///< PIT state
  KeyboardState m_keyboard;           ///< Keyboard state
  PICState m_pic;                     ///< PIC state
  DMAState m_dma;                     ///< DMA state
  Stats m_stats;                      ///< Statistics
};

} // namespace x86_64