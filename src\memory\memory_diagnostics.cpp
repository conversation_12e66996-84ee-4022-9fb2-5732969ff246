#include "memory_diagnostics.h"
#include "../cpu/x86_64_cpu.h"
#include "../jit/x86_64_jit_compiler.h"
#include "../ps4/fiber_manager.h"
#include "../ps4/ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <spdlog/spdlog.h>

namespace ps4 {
/**
 * @brief Constructs a MemoryDiagnostics singleton instance.
 */
MemoryDiagnostics::MemoryDiagnostics()
    : m_metrics(), m_mutex(), mmu_(PS4Emulator::GetInstance().GetMMU()) {
  try {
    ResetMetrics();
    spdlog::info("MemoryDiagnostics initialized");
  } catch (const std::exception &e) {
    spdlog::error("MemoryDiagnostics initialization failed: {}", e.what());
    // Initialize with empty metrics to prevent crashes
    m_metrics.clear();
  }
}

/**
 * @brief Retrieves the singleton instance of MemoryDiagnostics.
 * @return Reference to the singleton instance.
 */
MemoryDiagnostics &MemoryDiagnostics::GetInstance() {
  static MemoryDiagnostics instance;
  return instance;
}

/**
 * @brief Updates memory-related metrics.
 * @details Collects metrics from PS4MMU, CPU, JIT, and fibers, including
 * compression and access patterns.
 */
void MemoryDiagnostics::UpdateMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    ps4::PS4Emulator &emulator = ps4::PS4Emulator::GetInstance();

    // Check if emulator components are initialized before accessing them
    if (emulator.GetCPUCount() == 0) {
      spdlog::trace("MemoryDiagnostics: CPUs not initialized yet, skipping update");
      return;
    }

    auto &memory = emulator.GetMMU();
    auto &cpu = emulator.GetCPU(0);
    auto &jit = cpu.GetJITCompiler();
    auto &fiberManager = emulator.GetFiberManager();

  // Memory access metrics
  m_metrics["access_count"] = memory.GetReadCount() + memory.GetWriteCount();
  m_metrics["read_count"] = memory.GetReadCount();
  m_metrics["write_count"] = memory.GetWriteCount();
  m_metrics["page_faults"] = memory.GetPageFaultCount();
  m_metrics["tlb_hits"] = memory.GetTLB().GetStats().hits;
  m_metrics["tlb_misses"] = memory.GetTLB().GetStats().misses;

  // Compression metrics
  if (memory.IsCompressionEnabled()) {
    m_metrics["compressed_pages"] = memory.GetCompressedPageCount();
    m_metrics["compression_ratio"] = memory.GetCompressionRatio();
    m_metrics["compression_cycles"] = memory.GetCompressionCycles();
    m_metrics["decompression_cycles"] = memory.GetDecompressionCycles();
  } else {
    m_metrics["compressed_pages"] = 0;
    m_metrics["compression_ratio"] = 0.0f;
    m_metrics["compression_cycles"] = 0;
    m_metrics["decompression_cycles"] = 0;
  }

  // Prefetcher metrics
  if (memory.GetPrefetchHint() != PrefetchHint::PREFETCH_NONE) {
    auto prefetchStats = memory.GetPrefetcherStats();
    m_metrics["prefetch_requests"] = prefetchStats.prefetchRequests;
    m_metrics["prefetch_hits"] = prefetchStats.prefetchHits;
    m_metrics["prefetch_hit_rate"] = prefetchStats.hitRate;
    m_metrics["sequential_patterns"] = prefetchStats.sequentialPatterns;
    m_metrics["stride_patterns"] = prefetchStats.stridePatterns;
  }

  // CPU-related memory metrics
  auto pipelineStats = cpu.GetPipeline().GetStats();
  m_metrics["memory_stalls"] = pipelineStats.memory_stalls;
  m_metrics["data_hazard_stalls"] = pipelineStats.data_hazard_stalls;

  // JIT-related memory metrics
  auto jitStats = jit.GetStats();
  m_metrics["jit_cache_usage"] =
      static_cast<uint64_t>(jit.GetCacheUsage() * 100);
  m_metrics["jit_blocks_compiled"] = jitStats.blocksCompiled;
  m_metrics["jit_executions"] = jitStats.executions;
  m_metrics["jit_cache_clears"] = jitStats.cacheClears;
  m_metrics["jit_simd_instructions"] = jitStats.simdInstructions;

  // Fiber-related memory access metrics
  uint64_t fiberMemoryAccesses = 0;
  for (uint64_t i = 1; i <= fiberManager.GetFiberCount(); ++i) {
    std::string name;
    FiberState state;
    uint8_t priority;
    uint64_t executionTimeUs;
    uint32_t switchCount;
    if (fiberManager.GetFiberInfo(i, name, state, priority, executionTimeUs,
                                  switchCount)) {
      fiberMemoryAccesses += executionTimeUs / 1000; // Approximate accesses
    }
  }
  m_metrics["fiber_memory_accesses"] = fiberMemoryAccesses;

  // APIC-related metrics
  auto apicStats = cpu.GetAPIC().GetStats();
  m_metrics["apic_operations"] = apicStats.operationCount;
  m_metrics["apic_latency_us"] = apicStats.totalLatencyUs;

  // Additional memory diagnostics expansions
  m_metrics["memory_total_allocated"] = memory.GetTotalAllocated();
  m_metrics["memory_total_freed"] = memory.GetTotalFreed();
  m_metrics["memory_current_allocated"] = memory.GetCurrentAllocated();
  m_metrics["memory_peak_allocated"] = memory.GetPeakAllocated();
  m_metrics["memory_allocation_count"] = memory.GetAllocationCount();
  m_metrics["memory_free_count"] = memory.GetFreeCount();

  spdlog::trace("Memory diagnostics updated: accesses={}, page_faults={}, "
                "tlb_hits={}, compressed_pages={}, total_allocated={}",
                m_metrics["access_count"], m_metrics["page_faults"],
                m_metrics["tlb_hits"], m_metrics["compressed_pages"],
                m_metrics["memory_total_allocated"]);
  } catch (const std::exception &e) {
    spdlog::warn("MemoryDiagnostics::UpdateMetrics failed: {}", e.what());
  }
}

/**
 * @brief Resets all diagnostic metrics.
 */
void MemoryDiagnostics::ResetMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  m_metrics.clear();
  m_metrics["access_count"] = 0;
  m_metrics["read_count"] = 0;
  m_metrics["write_count"] = 0;
  m_metrics["page_faults"] = 0;
  m_metrics["tlb_hits"] = 0;
  m_metrics["tlb_misses"] = 0;
  m_metrics["compressed_pages"] = 0;
  m_metrics["compression_ratio"] = 0.0f;
  m_metrics["compression_cycles"] = 0;
  m_metrics["decompression_cycles"] = 0;
  m_metrics["prefetch_requests"] = 0;
  m_metrics["prefetch_hits"] = 0;
  m_metrics["prefetch_hit_rate"] = 0.0f;
  m_metrics["sequential_patterns"] = 0;
  m_metrics["stride_patterns"] = 0;
  m_metrics["memory_stalls"] = 0;
  m_metrics["data_hazard_stalls"] = 0;
  m_metrics["jit_cache_usage"] = 0;
  m_metrics["jit_blocks_compiled"] = 0;
  m_metrics["jit_executions"] = 0;
  m_metrics["jit_cache_clears"] = 0;
  m_metrics["jit_simd_instructions"] = 0;
  m_metrics["fiber_memory_accesses"] = 0;
  m_metrics["apic_operations"] = 0;
  m_metrics["apic_latency_us"] = 0;
  spdlog::info("Memory diagnostics reset");
}

/**
 * @brief Retrieves the current diagnostic metrics.
 * @return Map of metric names to values.
 */
const std::unordered_map<std::string, uint64_t> &
MemoryDiagnostics::GetMetrics() const {
  std::lock_guard<std::mutex> lock(m_mutex);
  return m_metrics;
}

/**
 * @brief Logs detailed diagnostic information.
 */
void MemoryDiagnostics::LogDiagnostics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  spdlog::info("Memory Diagnostics Report:");
  for (const auto &[key, value] : m_metrics) {
    spdlog::info("  {}: {}", key, value);
  }
}

/**
 * @brief Retrieves memory usage statistics.
 * @return A UsageStats structure containing memory usage statistics.
 */
MemoryDiagnostics::UsageStats MemoryDiagnostics::GetUsageStats() const {
  std::lock_guard<std::mutex> lock(m_mutex);
  return stats_;
}

/**
 * @brief Records a memory allocation.
 */
void MemoryDiagnostics::RecordAllocation(uint64_t virtAddr, uint64_t size,
                                         uint64_t processId) {
  std::lock_guard<std::mutex> lock(m_mutex);
  // Track allocation
  allocations_[processId][virtAddr] = {virtAddr, size, processId,
                                       std::chrono::steady_clock::now()};
  stats_.totalAllocated += size;
  stats_.currentAllocated += size;
  stats_.allocationCount++;
  if (stats_.currentAllocated > stats_.peakAllocated) {
    stats_.peakAllocated = stats_.currentAllocated;
  }
}

/**
 * @brief Records a memory deallocation.
 */
void MemoryDiagnostics::RecordDeallocation(uint64_t virtAddr,
                                           uint64_t processId) {
  std::lock_guard<std::mutex> lock(m_mutex);
  auto itProcess = allocations_.find(processId);
  if (itProcess != allocations_.end()) {
    auto &allocMap = itProcess->second;
    auto itAlloc = allocMap.find(virtAddr);
    if (itAlloc != allocMap.end()) {
      uint64_t size = itAlloc->second.size;
      stats_.totalFreed += size;
      stats_.currentAllocated -= size;
      stats_.freeCount++;
      allocMap.erase(itAlloc);
    }
  }
}

} // namespace ps4