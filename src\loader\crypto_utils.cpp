#include "crypto_utils.h"
#include <algorithm>
#include <cstring>
#include <iomanip>
#include <openssl/aes.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <openssl/ec.h>
#include <openssl/err.h>
#include <openssl/evp.h>
#include <openssl/hmac.h>
#include <openssl/pem.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>

namespace ps4 {
// Helper function to get OpenSSL error string
std::string GetOpenSSLError() {
  std::string error;
  unsigned long err;
  while ((err = ERR_get_error()) != 0) {
    char buf[256];
    ERR_error_string_n(err, buf, sizeof(buf));
    error += buf;
    error += "; ";
  }
  return error.empty() ? "Unknown OpenSSL error" : error;
}
// AESCrypto implementation
bool AESCrypto::initialized_ = false;

bool AESCrypto::Initialize() {
  if (!initialized_) {
    OpenSSL_add_all_algorithms();
    initialized_ = true;
    spdlog::info("AESCrypto initialized");
  }
  return true;
}

void AESCrypto::Cleanup() {
  if (initialized_) {
    EVP_cleanup();
    initialized_ = false;
    spdlog::info("AESCrypto cleaned up");
  }
}

bool AESCrypto::EncryptAES128_ECB(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    std::vector<uint8_t> &ciphertext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_128_ecb(), nullptr, key.data(),
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-128-ECB encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  // Disable padding for ECB as per PS4 requirements
  EVP_CIPHER_CTX_set_padding(ctx.get(), 0);

  ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
  int len, ciphertext_len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-128-ECB encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len = len;

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-128-ECB encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len += len;

  ciphertext.resize(ciphertext_len);
  spdlog::debug("AES-128-ECB encryption successful: {} bytes", ciphertext_len);
  return true;
}

bool AESCrypto::DecryptAES128_ECB(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_128_ecb(), nullptr, key.data(),
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-128-ECB decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  EVP_CIPHER_CTX_set_padding(ctx.get(), 0);

  plaintext.resize(ciphertext.size() + AES_BLOCK_SIZE);
  int len, plaintext_len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-128-ECB decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len = len;

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
    spdlog::error("AES-128-ECB decryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len += len;

  plaintext.resize(plaintext_len);
  spdlog::debug("AES-128-ECB decryption successful: {} bytes", plaintext_len);
  return true;
}

bool AESCrypto::EncryptAES128_CBC(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_128_cbc(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-128-CBC encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
  int len, ciphertext_len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-128-CBC encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len = len;

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-128-CBC encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len += len;

  ciphertext.resize(ciphertext_len);
  spdlog::debug("AES-128-CBC encryption successful: {} bytes", ciphertext_len);
  return true;
}

bool AESCrypto::DecryptAES128_CBC(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_128_cbc(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-128-CBC decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size() + AES_BLOCK_SIZE);
  int len, plaintext_len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-128-CBC decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len = len;

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
    spdlog::error("AES-128-CBC decryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len += len;

  plaintext.resize(plaintext_len);
  spdlog::debug("AES-128-CBC decryption successful: {} bytes", plaintext_len);
  return true;
}

bool AESCrypto::EncryptAES128_CTR(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_128_ctr(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-128-CTR encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size());
  int len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-128-CTR encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-128-CTR encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }

  spdlog::debug("AES-128-CTR encryption successful: {} bytes",
                ciphertext.size());
  return true;
}

bool AESCrypto::DecryptAES128_CTR(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_128_ctr(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-128-CTR decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size());
  int len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-128-CTR decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
    spdlog::error("AES-128-CTR decryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }

  spdlog::debug("AES-128-CTR decryption successful: {} bytes",
                plaintext.size());
  return true;
}

bool AESCrypto::EncryptAES128_GCM(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext, std::array<uint8_t, 16> &tag) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_128_gcm(), nullptr, nullptr,
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-128-GCM encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_IVLEN, AES_BLOCK_SIZE,
                          nullptr) != 1) {
    spdlog::error("Failed to set IV length for AES-128-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), nullptr, nullptr, key.data(), iv.data()) !=
      1) {
    spdlog::error("Failed to set key/IV for AES-128-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
  int len, ciphertext_len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-128-GCM encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len = len;

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-128-GCM encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len += len;

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_GET_TAG, 16, tag.data()) !=
      1) {
    spdlog::error("Failed to get AES-128-GCM tag: {}", GetOpenSSLError());
    return false;
  }

  ciphertext.resize(ciphertext_len);
  spdlog::debug("AES-128-GCM encryption successful: {} bytes, tag generated",
                ciphertext_len);
  return true;
}

bool AESCrypto::DecryptAES128_GCM(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_128> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    const std::array<uint8_t, 16> &tag, std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_128_gcm(), nullptr, nullptr,
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-128-GCM decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_IVLEN, AES_BLOCK_SIZE,
                          nullptr) != 1) {
    spdlog::error("Failed to set IV length for AES-128-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), nullptr, nullptr, key.data(), iv.data()) !=
      1) {
    spdlog::error("Failed to set key/IV for AES-128-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size());
  int len, plaintext_len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-128-GCM decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len = len;

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_TAG, 16,
                          const_cast<uint8_t *>(tag.data())) != 1) {
    spdlog::error("Failed to set AES-128-GCM tag: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) <= 0) {
    spdlog::error(
        "AES-128-GCM decryption finalization failed (tag mismatch?): {}",
        GetOpenSSLError());
    return false;
  }
  plaintext_len += len;

  plaintext.resize(plaintext_len);
  spdlog::debug("AES-128-GCM decryption successful: {} bytes", plaintext_len);
  return true;
}

bool AESCrypto::EncryptAES256_CBC(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_256_cbc(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-256-CBC encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
  int len, ciphertext_len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-256-CBC encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len = len;

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-256-CBC encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len += len;

  ciphertext.resize(ciphertext_len);
  spdlog::debug("AES-256-CBC encryption successful: {} bytes", ciphertext_len);
  return true;
}

bool AESCrypto::DecryptAES256_CBC(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_256_cbc(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-256-CBC decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size() + AES_BLOCK_SIZE);
  int len, plaintext_len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-256-CBC decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len = len;

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
    spdlog::error("AES-256-CBC decryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len += len;

  plaintext.resize(plaintext_len);
  spdlog::debug("AES-256-CBC decryption successful: {} bytes", plaintext_len);
  return true;
}

bool AESCrypto::EncryptAES256_CTR(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_256_ctr(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-256-CTR encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size());
  int len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-256-CTR encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-256-CTR encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }

  spdlog::debug("AES-256-CTR encryption successful: {} bytes",
                ciphertext.size());
  return true;
}

bool AESCrypto::DecryptAES256_CTR(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_256_ctr(), nullptr, key.data(),
                         iv.data()) != 1) {
    spdlog::error("Failed to initialize AES-256-CTR decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size());
  int len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-256-CTR decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
    spdlog::error("AES-256-CTR decryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }

  spdlog::debug("AES-256-CTR decryption successful: {} bytes",
                plaintext.size());
  return true;
}

bool AESCrypto::EncryptAES256_GCM(
    const std::vector<uint8_t> &plaintext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    std::vector<uint8_t> &ciphertext, std::array<uint8_t, 16> &tag) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), EVP_aes_256_gcm(), nullptr, nullptr,
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-256-GCM encryption: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_IVLEN, AES_BLOCK_SIZE,
                          nullptr) != 1) {
    spdlog::error("Failed to set IV length for AES-256-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_EncryptInit_ex(ctx.get(), nullptr, nullptr, key.data(), iv.data()) !=
      1) {
    spdlog::error("Failed to set key/IV for AES-256-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
  int len, ciphertext_len;

  if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, plaintext.data(),
                        plaintext.size()) != 1) {
    spdlog::error("AES-256-GCM encryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len = len;

  if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
    spdlog::error("AES-256-GCM encryption finalization failed: {}",
                  GetOpenSSLError());
    return false;
  }
  ciphertext_len += len;

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_GET_TAG, 16, tag.data()) !=
      1) {
    spdlog::error("Failed to get AES-256-GCM tag: {}", GetOpenSSLError());
    return false;
  }

  ciphertext.resize(ciphertext_len);
  spdlog::debug("AES-256-GCM encryption successful: {} bytes, tag generated",
                ciphertext_len);
  return true;
}

bool AESCrypto::DecryptAES256_GCM(
    const std::vector<uint8_t> &ciphertext,
    const std::array<uint8_t, AES_KEY_SIZE_256> &key,
    const std::array<uint8_t, AES_BLOCK_SIZE> &iv,
    const std::array<uint8_t, 16> &tag, std::vector<uint8_t> &plaintext) {
  if (!initialized_)
    Initialize();

  std::unique_ptr<EVP_CIPHER_CTX, decltype(&EVP_CIPHER_CTX_free)> ctx(
      EVP_CIPHER_CTX_new(), EVP_CIPHER_CTX_free);
  if (!ctx) {
    spdlog::error("Failed to create EVP cipher context: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), EVP_aes_256_gcm(), nullptr, nullptr,
                         nullptr) != 1) {
    spdlog::error("Failed to initialize AES-256-GCM decryption: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_IVLEN, AES_BLOCK_SIZE,
                          nullptr) != 1) {
    spdlog::error("Failed to set IV length for AES-256-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptInit_ex(ctx.get(), nullptr, nullptr, key.data(), iv.data()) !=
      1) {
    spdlog::error("Failed to set key/IV for AES-256-GCM: {}",
                  GetOpenSSLError());
    return false;
  }

  plaintext.resize(ciphertext.size());
  int len, plaintext_len;

  if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(),
                        ciphertext.size()) != 1) {
    spdlog::error("AES-256-GCM decryption update failed: {}",
                  GetOpenSSLError());
    return false;
  }
  plaintext_len = len;

  if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_TAG, 16,
                          const_cast<uint8_t *>(tag.data())) != 1) {
    spdlog::error("Failed to set AES-256-GCM tag: {}", GetOpenSSLError());
    return false;
  }

  if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) <= 0) {
    spdlog::error(
        "AES-256-GCM decryption finalization failed (tag mismatch?): {}",
        GetOpenSSLError());
    return false;
  }
  plaintext_len += len;

  plaintext.resize(plaintext_len);
  spdlog::debug("AES-256-GCM decryption successful: {} bytes", plaintext_len);
  return true;
}

// SHACrypto implementation
bool SHACrypto::ComputeSHA256(const std::vector<uint8_t> &data,
                              std::array<uint8_t, SHA256_DIGEST_SIZE> &digest) {
  return ComputeSHA256(data.data(), data.size(), digest);
}

bool SHACrypto::ComputeSHA256(const uint8_t *data, size_t length,
                              std::array<uint8_t, SHA256_DIGEST_SIZE> &digest) {
  SHA256_CTX ctx;
  if (SHA256_Init(&ctx) != 1) {
    spdlog::error("Failed to initialize SHA256 context: {}", GetOpenSSLError());
    return false;
  }
  if (SHA256_Update(&ctx, data, length) != 1) {
    spdlog::error("Failed to update SHA256: {}", GetOpenSSLError());
    return false;
  }
  if (SHA256_Final(digest.data(), &ctx) != 1) {
    spdlog::error("Failed to finalize SHA256: {}", GetOpenSSLError());
    return false;
  }
  spdlog::debug("SHA256 computed successfully");
  return true;
}

bool SHACrypto::ComputeHMAC_SHA256(
    const std::vector<uint8_t> &data, const std::vector<uint8_t> &key,
    std::array<uint8_t, SHA256_DIGEST_SIZE> &hmac) {
  unsigned int hmac_len = SHA256_DIGEST_SIZE;
  unsigned char *result =
      HMAC(EVP_sha256(), key.data(), key.size(), data.data(), data.size(),
           hmac.data(), &hmac_len);
  if (!result || hmac_len != SHA256_DIGEST_SIZE) {
    spdlog::error("Failed to compute HMAC-SHA256: {}", GetOpenSSLError());
    return false;
  }
  spdlog::debug("HMAC-SHA256 computed successfully");
  return true;
}

// RandomGenerator implementation
bool RandomGenerator::initialized_ = false;

bool RandomGenerator::Initialize() {
  if (!initialized_) {
    if (RAND_poll() != 1) {
      spdlog::error("Failed to initialize random number generator: {}",
                    GetOpenSSLError());
      return false;
    }
    initialized_ = true;
    spdlog::info("RandomGenerator initialized");
  }
  return true;
}

void RandomGenerator::Cleanup() {
  if (initialized_) {
    RAND_cleanup();
    initialized_ = false;
    spdlog::info("RandomGenerator cleaned up");
  }
}

bool RandomGenerator::GenerateRandomBytes(std::vector<uint8_t> &buffer,
                                          size_t size) {
  if (!initialized_) {
    if (!Initialize())
      return false;
  }

  buffer.resize(size);
  if (RAND_bytes(buffer.data(), size) != 1) {
    spdlog::error("Failed to generate random bytes: {}", GetOpenSSLError());
    return false;
  }
  spdlog::debug("Generated {} random bytes", size);
  return true;
}

bool RandomGenerator::GenerateRandomBytes(uint8_t *buffer, size_t size) {
  if (!initialized_) {
    if (!Initialize())
      return false;
  }

  if (RAND_bytes(buffer, size) != 1) {
    spdlog::error("Failed to generate random bytes: {}", GetOpenSSLError());
    return false;
  }
  spdlog::debug("Generated {} random bytes", size);
  return true;
}
std::string BytesToHex(const std::vector<uint8_t> &bytes) {
  return BytesToHex(bytes.data(), bytes.size());
}

std::string BytesToHex(const uint8_t *bytes, size_t length) {
  std::stringstream ss;
  ss << std::hex << std::setfill('0');
  for (size_t i = 0; i < length; ++i) {
    ss << std::setw(2) << static_cast<unsigned>(bytes[i]);
  }
  return ss.str();
}

bool HexToBytes(const std::string &hex, std::vector<uint8_t> &bytes) {
  if (hex.length() % 2 != 0) {
    spdlog::error("Invalid hex string length: {}", hex.length());
    return false;
  }

  bytes.clear();
  bytes.reserve(hex.length() / 2);

  for (size_t i = 0; i < hex.length(); i += 2) {
    std::string byte_string = hex.substr(i, 2);
    char *end;
    unsigned long byte = std::strtoul(byte_string.c_str(), &end, 16);
    if (end != byte_string.c_str() + 2) {
      spdlog::error("Invalid hex string at position {}: {}", i, byte_string);
      bytes.clear();
      return false;
    }
    bytes.push_back(static_cast<uint8_t>(byte));
  }
  spdlog::debug("Converted hex string to {} bytes", bytes.size());
  return true;
}

bool ConstantTimeCompare(const std::vector<uint8_t> &a,
                         const std::vector<uint8_t> &b) {
  if (a.size() != b.size())
    return false;
  return ConstantTimeCompare(a.data(), b.data(), a.size());
}

bool ConstantTimeCompare(const uint8_t *a, const uint8_t *b, size_t length) {
  volatile uint8_t result = 0;
  for (size_t i = 0; i < length; ++i) {
    result |= a[i] ^ b[i];
  }
  return result == 0;
}

void SecureZeroMemory(void *ptr, size_t size) {
  volatile uint8_t *p = static_cast<volatile uint8_t *>(ptr);
  for (size_t i = 0; i < size; ++i) {
    p[i] = 0;
  }
}

void SecureZeroMemory(std::vector<uint8_t> &data) {
  SecureZeroMemory(data.data(), data.size());
}

AesCbcCrypto::AesCbcCrypto(const std::vector<uint8_t> &key,
                           const std::vector<uint8_t> &iv)
    : key_(key), iv_(iv) {
  if (key.size() != 16 && key.size() != 24 && key.size() != 32) {
    throw std::invalid_argument("Invalid AES key size: " +
                                std::to_string(key.size()));
  }
  if (iv.size() != AES_BLOCK_SIZE) {
    throw std::invalid_argument("Invalid IV size for AES-CBC: " +
                                std::to_string(iv.size()));
  }
  spdlog::debug("AesCbcCrypto initialized with key size: {}, IV size: {}",
                key.size(), iv.size());
}

std::vector<uint8_t>
AesCbcCrypto::Decrypt(const std::vector<uint8_t> &ciphertext) {
  if (ciphertext.empty()) {
    spdlog::warn("Empty ciphertext provided for decryption");
    return {};
  }

  std::vector<uint8_t> plaintext;
  std::array<uint8_t, AES_BLOCK_SIZE> iv_array;
  std::copy(iv_.begin(), iv_.end(), iv_array.begin());

  try {
    if (key_.size() == 16) {
      std::array<uint8_t, 16> key_array;
      std::copy(key_.begin(), key_.end(), key_array.begin());
      if (!AESCrypto::DecryptAES128_CBC(ciphertext, key_array, iv_array,
                                        plaintext)) {
        spdlog::error("AES-128-CBC decryption failed");
        return {};
      }
    } else if (key_.size() == 32) {
      std::array<uint8_t, 32> key_array;
      std::copy(key_.begin(), key_.end(), key_array.begin());
      if (!AESCrypto::DecryptAES256_CBC(ciphertext, key_array, iv_array,
                                        plaintext)) {
        spdlog::error("AES-256-CBC decryption failed");
        return {};
      }
    } else {
      spdlog::error("Unsupported key size for AES-CBC: {}", key_.size());
      return {};
    }

    spdlog::debug("AesCbcCrypto decryption successful: {} bytes",
                  plaintext.size());
    return plaintext;
  } catch (const std::exception &e) {
    spdlog::error("Exception during AesCbcCrypto decryption: {}", e.what());
    return {};
  }
}

} // namespace ps4