{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "D:/vc/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64", "D:/vc/VC/Tools/MSVC/14.43.34808/lib/**", "D:/vc/VC/Tools/MSVC/14.43.34808/include/**", "D:/sss/src/**", "${workspaceFolder}/src/include/portaudio-main/**", "D:/vcpkg/installed/x64-windows/share/spdlog", "D:/pthreads/", "D:/vcpkg/installed/x64-windows/include/**", "D:/imgui-master/**", "D:/vcpkg/**", "C:/ProgramData/Microsoft/Windows/Start Menu/Programs/Node.js", "D:/VulkanSDK/1.4.309.0/Include/**", "D:/vcpkg/packages/vulkan-headers_x64-windows/include/**", "D:/sss/src/include/**", "D:/vcpkg/buildtrees/fmt/src/11.0.2-c30c0a133f.clean/test/gtest", "C:/Users/<USER>/.local/bin", "C:/Users/<USER>/Downloads/magic_enum-master/include/magic_enum/"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.26100.0", "cStandard": "c23", "cppStandard": "c++23", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools", "mergeConfigurations": true, "browse": {"limitSymbolsToIncludedHeaders": false}, "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/cl.exe"}], "version": 4}