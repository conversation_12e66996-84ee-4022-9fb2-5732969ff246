<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>SCHED_GETSCHEDULER(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>sched_getscheduler - get scheduling policy (<B>REALTIME</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;sched.h&gt; </B>
</P>
<P><B>int sched_getscheduler(pid_t</B> <I>pid</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>sched_getscheduler</B> function shall return the scheduling
policy of the process specified by <I>pid</I>. If the value of <I>pid</I>
is negative, the behavior of the <B>sched_getscheduler</B> function
is unspecified. 
</P>
<P>The values that can be returned by <B>sched_getscheduler</B> are
defined in the <I>&lt;sched.h&gt;</I> header. 
</P>
<P><B>PThreads4W</B> only supports the <B>SCHED_OTHER</B> policy,
which is the only value that can be returned. However, checks on <I>pid</I>
and permissions are performed first so that the other useful side
effects of this routine are retained.</P>
<P>If a process specified by <I>pid</I> exists, and if the calling
process has permission, the scheduling policy shall be returned for
the process whose process ID is equal to <I>pid</I>. 
</P>
<P>If <I>pid</I> is zero, the scheduling policy shall be returned for
the calling process. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, the <B>sched_getscheduler</B> function
shall return the scheduling policy of the specified process. If
unsuccessful, the function shall return -1 and set <I>errno</I> to
indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>sched_getscheduler</B> function shall fail if: 
</P>
<DL>
	<DT><B>EPERM</B> 
	</DT><DD>
	The requesting process does not have permission to determine the
	scheduling policy of the specified process. 
	</DD><DT>
	<B>ESRCH</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	No process can be found corresponding to that specified by <I>pid</I>.
		</DD></DL>
<P>
<I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="sched_setscheduler.html"><B>sched_setscheduler</B>(3)</A>
, the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;sched.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
