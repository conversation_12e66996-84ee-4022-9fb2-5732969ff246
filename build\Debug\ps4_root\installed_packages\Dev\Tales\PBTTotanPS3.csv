//Tales table


//check


Table = PBTable:Totan
	Resources, RSID_TTOTAN_START, RSID_TTOTAN_END,
	Dil, RSID_TTOTAN_PLACEMENT, -1,
	<PERSON><PERSON>, <PERSON>lipper<PERSON><PERSON><PERSON>, 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>2, 5, <PERSON><PERSON><PERSON><PERSON><PERSON>2, 7,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 31, 32, <PERSON><PERSON><PERSON><PERSON><PERSON>, 29, 30, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>2, -1, -1, <PERSON><PERSON><PERSON><PERSON><PERSON>2, -1, -1,

	<PERSON><PERSON>, 4, 3,  9, 1248.0, -2256.0, 1015.0, 75,0,0, 345, 
	
//           Slow Gravity     Table Rotation   Fast Gravity       Max Speed for Slow Gravity
	Gravity, 0.0,0.0,-60.0,       -1.3,            0.0,0.0,-32.0,               90.0,
	HDR, <PERSON><PERSON><PERSON><PERSON><PERSON>, 0.3369, BloomS<PERSON>, 1.47, TightDeviation, .3309, BroadDeviation, 1.0, Tight<PERSON><PERSON>ness, 4.32, <PERSON><PERSON><PERSON><PERSON>, 1.03, <PERSON><PERSON><PERSON>, 1.5, <PERSON><PERSON><PERSON><PERSON>nan<PERSON><PERSON><PERSON>, 4.7, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>ATUR<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 0.56, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 10.76, <PERSON><PERSON><PERSON><PERSON>, TR<PERSON>, End, 
	<PERSON>R_ps3, MiddleGray<PERSON>ey, 0.3369, BloomScale, 0.8, TightDeviation, 1.0, BroadDeviation, 1.0, TightBrightness, 4.32, BroadBrightness, 1.03, StarScale, 1.5, AdaptLuminanceRate, 160, RenderStar, FALSE, GlareDefType, GLT_NATURAL, BrightnessThreshhold, 0.56, BrightPassOffset, 10.76, BlueShift, TRUE, End,
	SPU_FX, GScale0, 0.60, Source0, 1.29, Bright0, 0.47, Bright3, 1.66, ThresholdR, 0.30, ThresholdG, 0.21, ThresholdB, 0.17, TintR, 1.00, TintG, 1.00, TintB, 1.00, End, 
	
	
//	Balls, 1, 3,  9, 0.0, 0.0, 1150.0, 75,0,0,	// testing
//	Gravity, 0.0, 0.0, -0.5, 0.0,
	LightsFile, Dev/Tales/LightSets.txt, RSID_TTOTAN_LIGHTS
	Emulated,	
	PlungeOnFlipper,
	Dimensions, 10, 21,
	Display, Dot, 
	Friction, 0.003,
	CamerasFile, Dev/Tales/TalesCameras.csv, RSID_TTOTAN_CAMERAS,
	DefaultPhysics, 0.5, 0, 10, 0,
	TiltSwitch, 14,
	SkillShotTime, 3000,
	MipBias, -1.0,
	BallTrough, 4, 32, 33, 34, 35, 
	FlipperSteps, 6, 
	KnockerSolenoid, 7,
	ReflectionAlpha, 250,
	VibrateEject, 0.2, 1.5, 120,
	DemoScore, 3684580,
	BallRender, Main, EnvironmentBlend, 0.59, EnvBlendMode, Normal, Reflection, BlendMode, Normal, End,
					// Off rgb	dark rgb	med rgb			light rgb
	DMDColor,		0,0,0,		135,77,38,	180,102,51,		225,128,64,
	MiniDMDColor,	30,30,0,	153,76,0,	204,102,0,		255,128,0,
TableEnd = PBTable:Totan

////////////////////////////////////flippers
Table = PBObject:FlipperRight
	Object, PB_OBJECTTYPE_FLIPPER, 2500,
	Switch, 11, 
//	DilPos, flipper R, 0,
	Pos, 360.710083,-2138.04053,955.487671,   1.0, 1.0, 1.0,   -1.95524752,-3.37774730,123,
	Models, 1, RSID_TTOTAN_MODELS, 0,
	EnvMapReflection, True,
	Lights, Def_Wood,
	Collision, Mesh, 2, RSID_TTOTAN_COLLISIONS, 9, RSID_TTOTAN_COLLISIONS, 10,
	SwitchEOS, 4, 
	Vars, 2, -50.0, -75.0,
	//1(min bal velocity) 2(angle max in unit vector coord) 
	LiveCatchPhysics, 125.0, -0.25 		
	//1(length) 2(angle speed down) 3(elasticity when flipper static) 4 (elasticity when dropping) 5(bottom speed mult) 6(number of trnsfer pts)
	Physics, Right, 380, -25, 0.62, 0.15, 0.3, 16, 
 		TransferPoint, 0.15, -31,   175, 150,  0,  0,
		TransferPoint, 0.20, -15,   200, 170,  0,  0,
		TransferPoint, 0.25,  -9,   300, 200,  0,  0,
		TransferPoint, 0.33,  -3,   380, 270,  0,  0,
		TransferPoint, 0.45,   1.5,   390, 300,  0,  0,
		TransferPoint, 0.50,   3.5,   400, 300,  0,  0,		
		TransferPoint, 0.57,   6.55,   410, 300,  0,  0,		
		TransferPoint, 0.70,   6.85,   410, 320,  0,  0,		
		TransferPoint, 0.76,  11,   350, 350,  0,  0,		
		TransferPoint, 0.80,  15,   300, 320,  0,  0,		
		TransferPoint, 0.83,  19,   275, 320,  0,  0,
		TransferPoint, 0.90,  27,   195, 320,  0,  0,
		TransferPoint, 0.95,  45,   130, 300,  0,  0,
		TransferPoint, 0.97,  65,   115, 270,  0,  0,
		TransferPoint, 0.99,  80,   110, 200,  0,  0,		
		TransferPoint, 1.00,  90,    80, 150,  0,  0,	
TableEnd = PBObject:FlipperRight



Table = PBObject:FlipperLeft
	Object, PB_OBJECTTYPE_FLIPPER, 2500,
	Switch, 12, 
//	DilPos, flipper L, 0,
	Pos, -603.555420,-2143.65674,957.234741,   1.0, 1.0, 1.0,   =-1.93746126,3.50023103,-123,
	Models, 1, RSID_TTOTAN_MODELS, 2,
	EnvMapReflection, True,
	Lights, Def_Wood,
	SwitchEOS, 2, 
	Collision, Mesh, 2, RSID_TTOTAN_COLLISIONS, 7, RSID_TTOTAN_COLLISIONS, 8,	
	Vars, 2, 50.0, 75.0,
	//1(min bal velocity) 2(angle max in unit vector coord) 
	LiveCatchPhysics, 125.0, -0.25 	
	//1(length) 2(angle speed down) 3(elasticity when flipper static) 4 (elasticity when dropping) 5(bottom speed mult) 6(number of trnsfer pts)
	Physics, Left, 360, 25, 0.65, 0.15, 0.3, 15, 
		TransferPoint, 0.15, -34,   175, 150,  0,  0,
		TransferPoint, 0.20, -15,   200, 170,  0,  0,
		TransferPoint, 0.25,  -9,   255, 200,  0,  0,
		TransferPoint, 0.33,  -3,   400, 220,  0,  0,
		TransferPoint, 0.50,   1,   415, 300,  0,  0,
		TransferPoint, 0.58,   6,   425, 310,  0,  0,		
		TransferPoint, 0.66,   9,   425, 320,  0,  0,		
		TransferPoint, 0.72,  12,   350, 350,  0,  0,		
		TransferPoint, 0.80,  15,   330, 320,  0,  0,		
		TransferPoint, 0.83,  19,   275, 320,  0,  0,
		TransferPoint, 0.90,  27,   195, 320,  0,  0,
		TransferPoint, 0.95,  65,   110, 300,  0,  0,
		TransferPoint, 0.97,  75,   105, 270,  0,  0,
		TransferPoint, 0.99,  80,   100, 200,  0,  0,		
		TransferPoint, 1.00,  90,    80, 150,  0,  0,		
TableEnd = PBObject:FlipperLeft


//visible objects

Table = PBObject:VisibleClip
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 35,
	Lights, Def_Wood,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleClip

//Table = PBObject:VisibleGlass
//	Object, PB_OBJECTTYPE_VISUAL, 2000,
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	Models, 1, RSID_TTOTAN_MODELS, 37,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleGlass

Table = PBObject:VisibleTable
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 38,
	Lights, Def_Metaltrim,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTable

Table = PBObject:VisibleMetal
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 39,
	Lights, Def_Metal,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleMetal

Table = PBObject:VisibleTrans1
	Object, PB_OBJECTTYPE_VISUAL, 2220,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 41,
	Lights, Def_Plasticpost,
TableEnd = PBObject:VisibleTrans1

Table = PBObject:VisibleTrans2
	Object, PB_OBJECTTYPE_VISUAL, 2210,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 42,
	Lights, Def_Plastic2,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTrans2

Table = PBObject:VisibleTrans3
	Object, PB_OBJECTTYPE_VISUAL, 2200,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 43,
	Lights, Def_Plastic3,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTrans3

Table = PBObject:VisibleLights
	Object, PB_OBJECTTYPE_LAMPSET, 2599,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 40,
	EnvMapReflection, True,
	Flags, zWrite, False, zTest, False, End,
	Lights, Def_Bulbs,
	//cmd lampSet	lampNum	 onOffset		offOffset	RSID							Type (EmuLamp, EmuFlasher, Scripted)
	Lamp, 0,		17,		 0,				1,			RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		15,		 2,				3,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		0,		 4,				5,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		1,		 6,				7,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		2,		 8,				9,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		3,		10,				11,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		4,		12,				13,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		5,		14,				15,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		6,		16,				17,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		7,		18,				19,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		15,		20,				21,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		39,		22,				23,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		38,		24,				25,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		20,		26,				27,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		21,		28,				29,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		22,		30,				31,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		36,		32,				33,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		37,		34,				35,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		53,		36,				37,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		54,		38,				39,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		55,		40,				41,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		23,		42,				43,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		46,		44,				45,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		45,		46,				47,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		44,		48,				49,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		43,		50,				51,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		42,		52,				53,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		41,		54,				55,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		40,		56,				57,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		24,		58,				59,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		25,		60,				61,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		26,		62,				63,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		27,		64,				65,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		28,		66,				67,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		29,		68,				69,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		30,		70,				71,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		31,		72,				73,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		32,		74,				75,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		21,		76,				77,			RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,	
	Lamp, 0,		52,		78,				79,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		51,		80,				81,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		50,		82,				83,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		49,		84,				85,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		48,		86,				87,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		62,		88,				89,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		16,		90,				91,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		17,		92,				93,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		19,		94,				95,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		18,		96,				97,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		60,		98,				99,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		59,		100,			101,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		58,		102,			103,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		57,		104,			105,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		56,		106,			107,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		23,		108,			109,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,	
	Lamp, 0,		33,		110,			111,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		34,		112,			113,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		35,		114,			115,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		24,		116,			117,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		14,		118,			119,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		61,		120,			121,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		47,		122,			123,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		18,		124,			125,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		27,		126,			127,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		9,		128,			129,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		10,		130,			131,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		11,		132,			133,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		12,		134,			135,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		13,		136,			137,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		8,		138,			139,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		26,		140,			141,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		25,		142,			143,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		22,		144,			145,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		12,		146,			147,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		13,		148,			149,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		11,		150,			151,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
TableEnd = PBObject:VisibleLights

///////////////////////////////////////////////pinball objects


//////////////////////////////////////////////////Plunger
Table = PBObject:Plunger
	Object, PB_OBJECTTYPE_PLUNGER, 2500,
	DilPos, plunger, 0,
	Models, 1, RSID_TTOTAN_MODELS, 4,
	Lights, Def_metaltrim,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 11,
	Vars, 8, 1265.0, -2350.0, 420.0, 0, 600.0, 0, 300,  -26,
	Vibrate, Solenoid, 1.5, 1.5, 150, End,
TableEnd = PBObject:Plunger

//////////////////////////////////////////////////Tiles
// Originally these were Tiles, but they really act as Targets since they don't drop.  Keeping them named Tile so the code matches, but they are Target Object types
Table = PBObject:Tile1
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 0,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:Tile1

Table = PBObject:Tile2
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 1,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile2

Table = PBObject:Tile3
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 2,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile3

Table = PBObject:Tile4
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 3,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile4

Table = PBObject:Tile5
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 4,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile5

Table = PBObject:Tile6
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 5,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Lights, Def_Rubberdull,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile6

//////////////////////////////////////////////////Targets

Table = PBObject:TargetA
	Object, PB_OBJECTTYPE_TARGET, 2500,
//	Switch, 58, // TOTAN_PF_SW_CAPTIVEBALL_L
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target A, 0,
	Lights, Def_Plastic3,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetA

Table = PBObject:TargetB
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 23, // TOTAN_PF_SW_GENIE_STANDUP
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target B, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Physics, Elasticity, 0.3, End,	
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetB

Table = PBObject:TargetC
	Object, PB_OBJECTTYPE_TARGET, 2500,
//	Switch, 48, // TOTAN_PF_SW_CAPTIVEBALL_R
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target C, 0,
	Lights, Def_Plastic3,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetC

Table = PBObject:TargetD
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 46, // TOTAN_PF_SW_MINISTANDUPS
	Models, 1, RSID_TTOTAN_MODELS, 7,
	DilPos, target D, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 14,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetD

Table = PBObject:TargetE
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 46, // TOTAN_PF_SW_MINISTANDUPS
	Models, 1, RSID_TTOTAN_MODELS, 7,
	DilPos, target E, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 14,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetE

Table = PBObject:TargetF
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 65, // TOTAN_PF_SW_SKILL_BOT
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target F, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetF

Table = PBObject:TargetG
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 64, // TOTAN_PF_SW_SKILL_MID
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target G, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetG

Table = PBObject:TargetH
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 63, // TOTAN_PF_SW_SKILL_TOP
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target H, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetH

//////////////////////////////////////////////////Slingshots

Table = PBObject:SlingShotLeft
	Object, PB_OBJECTTYPE_SLINGSHOT, 2500,
	Solenoid, 10,
	Switch, 51, // TOTAN_PF_SW_SLING_L
	Models, 2, RSID_TTOTAN_MODELS, 9, RSID_TTOTAN_MODELS, 10,
	DilPos, slingshot A, 0,
	Lights, Def_metaltrim,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 16,
	Physics, Friction,0.45, MaxTransferVel,145,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT
	Vibrate, Solenoid, 0.75, 2.55, 120, End,
TableEnd = PBObject:SlingShotLeft

Table = PBObject:SlingShotRight
	Object, PB_OBJECTTYPE_SLINGSHOT, 2500,
	Solenoid, 11,
	Switch, 52, // TOTAN_PF_SW_SLING_R
	Models, 2, RSID_TTOTAN_MODELS, 11, RSID_TTOTAN_MODELS, 12,
	DilPos, slingshot B, 0,
	Lights, Def_metaltrim,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 17,
	Physics, Friction,0.45, MaxTransferVel,145,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT
	Vibrate, Solenoid, 0.75, 2.55, 120, End,
TableEnd = PBObject:SlingShotRight


//////////////////////////////////////////////////PopBumpers
// left = 12, right = 13, middle = 14
// switch 
Table = PBObject:PopBumper1
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 53,  // TOTAN_PF_SW_RIGHTJET 
	Solenoid, 12,
	Models, 1, RSID_TTOTAN_MODELS, 13,
	DilPos, pop bumper A, 0,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper1

Table = PBObject:PopBumper2
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 55,  // TOTAN_PF_SW_MIDJET 
	Solenoid, 14,
	Models, 1, RSID_TTOTAN_MODELS, 13,
	DilPos, pop bumper A, 1,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper2

Table = PBObject:PopBumper3
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 54, // TOTAN_PF_SW_LEFTJET 
	Solenoid, 13,
	Models, 1, RSID_TTOTAN_MODELS, 13, 
	DilPos, pop bumper A, 2,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper3


//////////////////////////////////////////////////Wires

Table = PBObject:WireA1
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 13,
	Switch, 41, // TOTAN_PF_SW_RAMPMADE_L,
TableEnd = PBObject:WireA1

Table = PBObject:WireA2
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 12,
	Switch, 12, // TOTAN_PF_SW_VANISH
TableEnd = PBObject:WireA2
Table = PBObject:WireA3
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 11,
	Switch, 11, // TOTAN_PF_SW_HAREM,
TableEnd = PBObject:WireA3

Table = PBObject:WireA4
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 10,
	Switch, 28, //TOTAN_PF_SW_LEFTWIRE 
TableEnd = PBObject:WireA4

Table = PBObject:WireA5
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 9,
	Switch, 18, // TOTAN_PF_SW_SHOOTER,
TableEnd = PBObject:WireA5

Table = PBObject:WireA6
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 8,
	Switch, 43, //TOTAN_PF_SW_LEFTLOOP_O,
TableEnd = PBObject:WireA6

Table = PBObject:WireA7
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 7,
	Switch, 44, // TOTAN_PF_SW_LEFTLOOP_I,
TableEnd = PBObject:WireA7

Table = PBObject:WireA8
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 6,
	Switch, 45, // TOTAN_PF_SW_RIGHTLOOP_I,
TableEnd = PBObject:WireA8

Table = PBObject:WireA9
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 5,
	Switch, 0, // Ball lock 2, handled in lock stack
TableEnd = PBObject:WireA9

Table = PBObject:WireA10
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 4,
	Switch, 17, // TOTAN_PF_SW_INLANE_R
TableEnd = PBObject:WireA10

Table = PBObject:WireA11
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 3,
	Switch, 26, // TOTAN_PF_SW_INLANE_L, 
TableEnd = PBObject:WireA11

Table = PBObject:WireA12
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 2,
	Switch, 47, //TOTAN_PF_SW_RAMPMADE_R  
TableEnd = PBObject:WireA12

Table = PBObject:WireA13
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 1,
	Switch, 68, // TOTAN_PF_SW_BALLLOCK_3,
TableEnd = PBObject:WireA13

//Table = PBObject:WireA14
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
//	DilPos, wire A, 13,
//	Switch, 25 // Bazzar, handled in trap
//TableEnd = PBObject:WireA14

// There are only 14 wires in the dil file
//Table = PBObject:WireA15
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	DilPos, wire A, 13,
//	Switch, 47, // TOTAN_PF_SW_RAMPMADE_R,
//TableEnd = PBObject:WireA15

//Table = PBObject:WireA16
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	DilPos, wire A, 13,
//	Switch, 18, // TOTAN_PF_SW_SHOOTER,
//TableEnd = PBObject:WireA16


Table = PBObject:WireB1
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Sphere, Manual, 0.0,0.0,0.0,100,
	DilPos, wire B, 0,
	Switch, 37, //TOTAN_PF_SW_R_CAGE_OPTO
TableEnd = PBObject:WireB1

Table = PBObject:WireB2
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Sphere, Manual, 0.0,0.0,0.0,100,
	DilPos, wire B, 1,
	Switch, 36, //TOTAN_PF_SW_L_CAGE_OPTO
TableEnd = PBObject:WireB2


//////////////////////////////////////////////////Magnets

//Table = PBObject:MagnetA
//	Object, PB_OBJECTTYPE_MAGNET, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 15,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 21,
//	DilPos, magnet A, 0,
//	Solenoid, 39, // TOTAN_SOLENOID_VANISH_MAGNET
//	Vars, 5, 1, 70, 400, 0.8, 9000,
//	AffectPhysics, False,
//TableEnd = PBObject:MagnetA

Table = PBObject:MagnetB
	Object, PB_OBJECTTYPE_MAGNET, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 16,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 22,
	DilPos, Magnet B, 0,
	Solenoid, 6, // TOTAN_SOLENOID_LOCK_MAGNET
//		     Dampening True	  DampeningMax    Divisor    Percent    InvMulti-Stength of Magnet
	Vars, 5,       1,             70,           1000,       0.3,          12000,
	AffectPhysics, False,
	Vibrate, Solenoid, 0.25, 1.0, 1000, End,
TableEnd = PBObject:MagnetB

//Table = PBObject:MagnetC
//	Object, PB_OBJECTTYPE_MAGNET, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 23,
//	DilPos, magnet C, 0,
//	Solenoid, 8, // TOTAN_SOLENOID_RAMP_MAGNET
//	Vars, 5, 0, 0, 100, 1.0, 100,
//	AffectPhysics, False,
//TableEnd = PBObject:MagnetC


//////////////////////////////////////////////////Captive Balls

//Table = PBObject:VisibleTrappedBumper1
//	Object, PB_OBJECTTYPE_VISUAL, 2350,
//	DilPos, generic K, 0,
//	Models, 1, RSID_TTOTAN_MODELS, 30,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleTrappedBumper1

//Table = PBObject:VisibleTrappedBumper2
//	Object, PB_OBJECTTYPE_VISUAL, 2350,
//	DilPos, generic K, 1,
//	Models, 1, RSID_TTOTAN_MODELS, 30,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleTrappedBumper2


// Generic K
Table = PBObject:TrappedBallBumper1
	Object, PB_OBJECTTYPE_TRAPPED_BUMPER, 2340,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 46,
	//Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Physics, Elasticity,0.1, End,	
	Pos, 832.215, -348.288, 1126.686,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	DilPos, generic K, 0,
	Link, TrappedBall1,
	Vibrate, Collision, 0.25, 1.0, 50, End, 
	EnvMap, 0, 0.59,
TableEnd = PBObject:TrappedBallBumper1

// Generic L
Table = PBObject:TrappedBall1
	Object, PB_OBJECTTYPE_TRAPPED_BALL, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 31,
	DilPos, generic L, 0,
	Vars, 4,  70, 304.0, 10.0, -1,
	Verts, 1, 1016.0, 127.949, 1138.466,
	Switch, 58, // TOTAN_PF_SW_CAPTIVEBALL_L
	Sound, Generic, GEN_SOUND_HIT_METAL,
	EnvMap, 1, 0.59,
TableEnd = PBObject:TrappedBall1

// Generic K
Table = PBObject:TrappedBallBumper2
	Object, PB_OBJECTTYPE_TRAPPED_BUMPER, 2340,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 38,
	Pos, -811.034, 527.855, 1198.128,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	DilPos, generic K, 1,
	Physics, Elasticity,0.1, End,
	Link, TrappedBall2,
	Sound, Generic, GEN_SOUND_HIT_METAL,
	Vibrate, Collision, 0.25, 1.0, 50, End,
	EnvMap, 2, 0.59,
TableEnd = PBObject:TrappedBallBumper2

// Generic L
Table = PBObject:TrappedBall2
	Object, PB_OBJECTTYPE_TRAPPED_BALL, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 31,
	DilPos, generic L, 1,
	Vars, 4, 100, 124.0, 10.0, -1,
	Verts, 1, -860.893, 810.342, 1209.908, 
	Switch, 48, // TOTAN_PF_SW_CAPTIVEBALL_R
	Sound, Generic, GEN_SOUND_HIT_Metal,
	EnvMap, 3, 0.59,
TableEnd = PBObject:TrappedBall2



//////////////////////////////////////////////////Traps

//ORB
Table = PBObject:TrapA
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 24,
	DilPos, Trap A, 0,
	Switch, 38,
	Solenoid, 15,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 0,-132,40,   12.0,-30.0,15.0, 10.0, 0,
TableEnd = PBObject:TrapA

//Multiball
// Trap settle speed of 10,000 to instantly trap
Table = PBObject:TrapB
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 18,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 25,
	DilPos, Trap B, 0,
	Solenoid, 4,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 72,-132,70,   10,-80.0,20.0,  100000.0, 0,		
	BallStack, 3, 66, 67, 68,
TableEnd = PBObject:TrapB

//Bazaar
Table = PBObject:TrapC
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 19,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 26,
	DilPos, trap c, 0,
	Switch, 25,
	Solenoid, 5,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 517.0,309.0,1172.0,   -16.0,-70.0,65.0, 10.0, 0,
	Vibrate, Collision, 1.0, 1.5, 70, Solenoid, 1.0, 1.5, 120, End,
TableEnd = PBObject:TrapC


//////////////////////////////////////////////////Generics

//generic H = 1 outlane left   TOTAN_PF_SW_OUTLANE_L
Table = PBObject:GenericH
	Models, 1, RSID_TTOTAN_MODELS, 27,
	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 27,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, generic H, 0,
	Switch, 16, //TOTAN_PF_SW_OUTLANE_L
TableEnd = PBObject:GenericH

//generic I = 1 outlane right  TOTAN_PF_SW_OUTLANE_R
Table = PBObject:GenericI
	Models, 1, RSID_TTOTAN_MODELS, 28,
	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 28,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, generic I, 0,
	Switch, 27, //TOTAN_PF_SW_OUTLANE_R
TableEnd = PBObject:GenericI

//generic Q = 1 left loop TOTAN_PF_SW_LEFTLOOP_O
Table = PBObject:GenericQ
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 42,
	DilPos, generic Q, 0,
	Switch, 43, //TOTAN_PF_SW_LEFTLOOP_O
TableEnd = PBObject:GenericQ

//generic J = 1 Rollunder spinner typ  TOTAN_PF_SW_RAMP_ENTER
Table = PBObject:GenericJ
	Object, PB_OBJECTTYPE_GATE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 29,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 36,
	DilPos, generic J, 0,
	Switch, 15, //TOTAN_PF_SW_RAMP_ENTER
	Vars, 4,	90.0,	-0.01,	0.83,   75.0,
TableEnd = PBObject:GenericJ

//generic B = 1 left cage
Table = PBObject:GenericB
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic B, 0,
	Models, 1, RSID_TTOTAN_MODELS, 21,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 28,
	Vars, 2, 150, 150,
	Solenoid, 1, 
	SolenoidSound, Unique, RSID_TTOTAN_MECH_SOUNDS, 7, RSID_TTOTAN_MECH_SOUNDS, 7,
	Vibrate, Solenoid, 0.25, 1.5, 1200, End, 
	EnvMapReflection, True,
TableEnd = PBObject:GenericB

//generic C = 1 right cage
Table = PBObject:GenericC
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic C, 0,
	Models, 1, RSID_TTOTAN_MODELS, 22,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 29,
	Vars, 2, 150, 150,
	Solenoid, 2, 
	SolenoidSound, Unique, RSID_TTOTAN_MECH_SOUNDS, 7, RSID_TTOTAN_MECH_SOUNDS, 7, 
	Vibrate, Solenoid, 0.25, 1.5, 1200, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericC


//generic O = 1 diverter collision closed
Table = PBObject:GenericO
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 40,
	DilPos, generic O, 0,
TableEnd = PBObject:GenericO

//generic P = 1 diverter collision open
Table = PBObject:GenericP
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 41,
	DilPos, generic P, 0,
TableEnd = PBObject:GenericP

//generic N = 1 left loop diverter (uses generic O and P for collisions) 
Table = PBObject:GenericN
	Object, PB_OBJECTTYPE_DIVERTER, 2500,
	DilPos, generic N, 0,
	Models, 1, RSID_TTOTAN_MODELS, 33,
	Vars, 3, -20, -20, 15,
	Link, GenericO;GenericP, 
	Solenoid, 34, //TOTAN_SOLENOID_LEFT_DIV_HOLD
TableEnd = PBObject:GenericN


//generic M = 1 loop post diverter
Table = PBObject:GenericM
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic M, 0,
	Models, 1, RSID_TTOTAN_MODELS, 32,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 37,
	Vars, 3, 150, 150, 15
	Solenoid, 36, //TOTAN_SOLENOID_LOOP_POST_DIV
TableEnd = PBObject:GenericM

//generic R = 1 ball lock wall
Table = PBObject:GenericR
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 43,
	DilPos, generic R, 0,
TableEnd = PBObject:GenericR


//generic D = 1 lamp
Table = PBObject:GenericD
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic D, 0,
	Models, 1, RSID_TTOTAN_MODELS, 23,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 30,
	Func, CPBTalesLampSetCB,
	Sound, Generic, GEN_SOUND_HIT_default
	Lights, Def_pop_Harley,
	Vibrate, Collision, 0.25, 0.50, 50, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericD

//Magnet A - Unique type cause it has special code vs regular magnet must be before generic A
Table = PBObject:MagnetA
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 15,
	Lights, Def_MTLtrim_HRL,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 21,
	DilPos, magnet A, 0,
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Func, CPBTalesMagnetASetCB,
	Solenoid, 35, // TOTAN_SOLENOID_VANISH_MAGNET
	//Vars, 5, 1, 70, 400, 0.65, 18000,	// old way
	Vars, 5, 1, 70, 900, 0.5, 18000,	// ball wobbles less
	AffectPhysics, False,
	Vibrate, Solenoid, 1.5, 0.25, 50, End,
TableEnd = PBObject:MagnetA

//generic A = 1 drop magnet (drops -1000 on z when solenoid event TOTAN_SOLENOID_VANISH_DROP) (Also drops magnet A)
Table = PBObject:GenericA
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic A, 0,
	Models, 1, RSID_TTOTAN_MODELS, 20,
	Lights, Def_MTLtrim_HRL,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 27,
	Func, CPBTalesDropMagnetSetCB,
	Solenoid, 3, // TOTAN_SOLENOID_VANISH_DROP
	Vibrate, Solenoid, 1.5, 1.0, 1000, End,
TableEnd = PBObject:GenericA

//generic E = 1 genie  TOTAN_PF_SW_GENIE
Table = PBObject:GenericE
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic E, 0,
	Models, 1, RSID_TTOTAN_MODELS, 24,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 31,
	Func, CPBTalesGenieSetCB,
	Switch, 42, //TOTAN_PF_SW_GENIE
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
	Lights, Def_Floor,
	Vibrate, Collision, 0.2, 1.0, 40, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericE


//generic G = 1 Round magnet ball gets pulled inside of along the fireball ramp
Table = PBObject:GenericG
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 26,
	Lights, Def_pop_Harley,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 33,
	DilPos, generic G, 0,
	Func, CPBTalesRingMagnetSetCB,
	AffectPhysics, False,
TableEnd = PBObject:GenericG

//Magnet C - Unique type cause it has special code vs regular magnet
Table = PBObject:MagnetC
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 23,
	DilPos, magnet C, 0,
	Solenoid, 8, // TOTAN_SOLENOID_RAMP_MAGNET
	Vars, 5, 0, 0, 100, 1.0, 100,
	AffectPhysics, False,
	Func, CPBTalesMagnetCSetCB,
	Vibrate, Solenoid, 0.25, 1.5, 100, End,
TableEnd = PBObject:MagnetC

//////////////////////////////////////////////////Collisions


Table = PBObject:PlungerArea
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	DilPos, plunger, 0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 44,
	AffectPhysics, False,	// used to change from plunger cam to normal
	Physics, Elasticity,0.25, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-30,0 Dampening,1,1,1,End,	
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:PlungerArea

Table = PBObject:AltSkill
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 5,
	Physics, Elasticity,0.25, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-25,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:AltSkill

Table = PBObject:AltWall
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 4,
	Physics, Elasticity,0.6, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-35,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:AltWall

// Ramp

Table = PBObject:Vanish
	Object, PB_OBJECTTYPE_UNIQUE, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 58,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Func, CPBTalesVanishRampSetCB,
	Vars, 2, 25.0, 60,	// speed which ball drops, delay
TableEnd = PBObject:Vanish

//generic F = 1 Ramp diverter
Table = PBObject:GenericF
	Object, PB_OBJECTTYPE_DIVERTER, 2500,
	DilPos, generic F, 0,
	Models, 1, RSID_TTOTAN_MODELS, 25,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 32,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-22,0 Dampening,1,1,1,End,
	Vars, 3, 30, 30, 10,
	Solenoid, 21, //TOTAN_SOLENOID_RAMP_DIVERTER
TableEnd = PBObject:GenericF

Table = PBObject:Col_Diverter_Vanish
	Object, PB_OBJECTTYPE_DIVERTER_ONOFF, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 50,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Vars, 4, 30,30,10,1,
	Solenoid, 21, //TOTAN_SOLENOID_RAMP_DIVERTER
TableEnd = PBObject:Col_Diverter_Vanish

Table = PBObject:RampMetal
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 47,
	Physics, Elasticity,0.0001, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,0.000000001, Gravity,0,0,-60,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:RampMetal

Table = PBObject:RampFrontLeft
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 53,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampFrontLeft

Table = PBObject:RampFrontRight
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 56,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampFrontRight

Table = PBObject:RampCol_HabbiTrail
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 51,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_HabbiTrail

Table = PBObject:RampCol_RampLeft
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 52,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampLeft

Table = PBObject:RampCol_RampMiddle
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 54,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-22,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampMiddle

Table = PBObject:RampCol_RampRight
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 55,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampRight

Table = PBObject:RampCol_RampSpiral
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 57,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampSpiral



//Table = PBObject:Ramp
//	Object, PB_OBJECTTYPE_COLLISION, 10000, 
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 3,
//	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-24,0 Dampening,1,1,1,End,
//	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
//TableEnd = PBObject:Ramp


Table = PBObject:Arc
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 2,
	Physics, Elasticity,0.35, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,.5 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:Arc

Table = PBObject:Collision_Flipper_Lane
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 49,
	Physics, Elasticity,0.35, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,.5 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:Collision_Flipper_Lane

Table = PBObject:Wall
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 0,
	Physics, Elasticity,0.1, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,0 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:Wall

Table = PBObject:Platform
	Object, PB_OBJECTTYPE_BALLDRAIN, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 45,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,0,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
	Vibrate, Collision, 0.20, 1.0, 200, End,
TableEnd = PBObject:Platform

Table = PBObject:SkillShot
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 48,
	Physics, Elasticity,0.1, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-70,0 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:SkillShot

Table = PBObject:Floor
	Object, PB_OBJECTTYPE_FLOOR, 2600, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 34,
	Lights, Def_Floor,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 1,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,0,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_GROUND,
	EnvMapReflection, True,
TableEnd = PBObject:Floor

Table = PBObject:CameraTrigger
	Object, PB_OBJECTTYPE_CAMERA_TRIGGER, 1000,
	Pos, 0,0,0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	AffectPhysics, False,
//               	
	Vars, 8, 1237.0,0.0,1650.0,50, 1250,165,1415,60,
TableEnd = PBObject:CameraTrigger

Table = PBObject:PlungerExit
	Object, PB_OBJECTTYPE_PLUNGER_EXIT, 1000,
	Pos, 0,0,0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	AffectPhysics, False,
	Vars, 8, 1237.0, -835.0, 1346.0,50, 1236.0, -1603.0, 1074.0,50,
TableEnd = PBObject:PlungerExit

// Environment
Table = PBObject:GenericEnvironment
	Object, PB_OBJECTTYPE_VISUAL, 150,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	EnvironmentModel, 0,
	Lights, Def_generic_env,
	EnvMapReflection, False,
TableEnd = PBObject:GenericEnvironment

Table = PBObject:GenericEnvironmentCeiling
	Object, PB_OBJECTTYPE_VISUAL, 150,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	EnvironmentModel, 1,
	Lights, Def_generic_env,
	EnvMapReflection, True,
TableEnd = PBObject:GenericEnvironmentCeiling



// flipper R = 360.7101, -2138.0405, 955.4877
// flipper L = -603.5554, -2143.6567, 957.2347
// plunger = 1266.1338, -3558.0581, 910.6148
// tile = -1134.4805, -261.8982, 1121.5990
// tile = -1118.4543, -55.4221, 1136.0372
// tile = -1149.5691, -469.3819, 1107.0903
// tile = 188.1027, 1498.1730, 1244.6752
// tile = 263.0561, 1304.7662, 1231.1508
// tile = 370.4385, 1132.6183, 1219.1130
// target A = -889.3380, 919.0559, 1197.2909
// target B = 0.9089, 2257.7563, 1290.9012
// target C = 1037.2719, 236.6223, 1149.5702
// target D = -481.8063, 1376.3110, 1235.6205
// target E = 671.6626, 251.2317, 1156.9476
// target F = 1239.0867, 275.4537, 1527.9266
// target G = 1224.7332, 662.2930, 1566.4452
// target H = 1212.1437, 1062.9045, 1605.4091
// slingshot A = -792.4070, -1471.6940, 1060.4587
// slingshot B = 528.7260, -1471.6940, 1060.4587
// pop bumper A = 347.7016, 2563.0825, 1398.3002
// pop bumper A = 942.6722, 2514.5400, 1394.9061
// pop bumper A = 876.5354, 1856.6926, 1348.9052
// wire A = -976.0285, 2501.9219, 1601.9269
// wire A = -8.6627, 1333.0244, 979.6151
// wire A = -109.4578, 2260.1436, 1052.6184
// wire A = -927.8615, 1891.9006, 1573.1903
// wire A = 1267.9352, -2366.4795, 947.2161
// wire A = -1141.9889, 2271.9053, 1256.8906
// wire A = -548.9714, 2533.7197, 1275.1985
// wire A = 626.9449, 1744.9133, 1220.0399
// wire A = 310.7825, 1499.4248, 1210.4258
// wire A = 756.3146, -1427.1373, 998.2285
// wire A = -1034.5651, -1438.1260, 997.4601
// wire A = 779.9927, 2023.6858, 1722.2205
// wire A = 311.6658, 1688.9109, 1227.1195
// wire B = 750.7159, -1023.2439, 1013.4198
// wire B = -1030.3644, -1023.2439, 1013.4198
// Magnet B = 323.2439, 2089.7759, 1220.2484
// generic K = -808.3806, 522.6459, 1198.1283
// generic L = 896.8094, -173.9697, 1138.4657
// generic K = 829.4173, -329.5883, 1126.6859
// generic L = -836.6783, 678.2645, 1209.9081
// Trap A = -782.1801, 379.9615, 1091.9789
// Trap B = 399.6355, 1371.1245, 1153.1566
// trap c = 0.0000, 0.0000, -0.0001
// generic H = -1256.6378, -1936.9868, 939.2474
// generic I = 951.7297, -1915.1860, 940.7714
// generic Q = 0.0000, 0.0000, 0.0000
// generic J = -905.9058, 1744.3557, 1420.7572
// generic B = -1030.0459, -1021.7445, 909.0013
// generic C = 753.3507, -1016.2350, 907.8347
// generic O = 0.0000, 0.0000, 0.0000
// generic P = 0.0000, 0.0000, 0.0000
// generic N = -1074.7311, 2218.3667, 1267.5443
// generic F = 337.2099, 2269.6528, 1716.6156
// generic M = 934.3212, 2811.0874, 850.3143
// generic R = 0.0000, 0.0000, 0.0000
// generic D = 126.3894, 640.9906, 1488.8861
// magnet A = -231.4876, 1663.6775, 1191.0629
// generic A = -232.3552, 1663.4944, 1197.7211
// generic E = -353.6015, 2226.3193, 1675.0170
// generic G = -972.8077, 2179.8677, 1737.1039
// magnet C = -952.7455, 2170.3081, 1736.5258
// plunger = 1266.1338, -3558.0581, 910.6148




















