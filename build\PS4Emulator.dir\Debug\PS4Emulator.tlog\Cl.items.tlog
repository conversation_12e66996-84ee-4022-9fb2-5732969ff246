D:\sss\src\cache\cache.cpp;D:\sss\build\PS4Emulator.dir\Debug\cache.obj
D:\sss\src\common\lock_ordering.cpp;D:\sss\build\PS4Emulator.dir\Debug\lock_ordering.obj
D:\sss\src\cpu\cpu_diagnostics.cpp;D:\sss\build\PS4Emulator.dir\Debug\cpu_diagnostics.obj
D:\sss\src\cpu\instruction_decoder.cpp;D:\sss\build\PS4Emulator.dir\Debug\instruction_decoder.obj
D:\sss\src\cpu\thunk_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\thunk_manager.obj
D:\sss\src\cpu\x86_64_cpu.cpp;D:\sss\build\PS4Emulator.dir\Debug\x86_64_cpu.obj
D:\sss\src\cpu\x86_64_pipeline.cpp;D:\sss\build\PS4Emulator.dir\Debug\x86_64_pipeline.obj
D:\sss\src\emulator\adaptive_emulation_orchestrator.cpp;D:\sss\build\PS4Emulator.dir\Debug\adaptive_emulation_orchestrator.obj
D:\sss\src\emulator\apic.cpp;D:\sss\build\PS4Emulator.dir\Debug\apic.obj
D:\sss\src\emulator\interrupt_handler.cpp;D:\sss\build\PS4Emulator.dir\Debug\interrupt_handler.obj
D:\sss\src\emulator\io_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\io_manager.obj
D:\sss\src\gui\backends\imgui_impl_sdl2.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_impl_sdl2.obj
D:\sss\src\gui\backends\imgui_impl_vulkan.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_impl_vulkan.obj
D:\sss\src\gui\game_browser.cpp;D:\sss\build\PS4Emulator.dir\Debug\game_browser.obj
D:\sss\src\gui\imgui.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui.obj
D:\sss\src\gui\imgui_demo.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_demo.obj
D:\sss\src\gui\imgui_draw.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_draw.obj
D:\sss\src\gui\imgui_freetype.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_freetype.obj
D:\sss\src\gui\imgui_stdlib.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_stdlib.obj
D:\sss\src\gui\imgui_tables.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_tables.obj
D:\sss\src\gui\imgui_widgets.cpp;D:\sss\build\PS4Emulator.dir\Debug\imgui_widgets.obj
D:\sss\src\gui\input_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\input_manager.obj
D:\sss\src\gui\input_settings.cpp;D:\sss\build\PS4Emulator.dir\Debug\input_settings.obj
D:\sss\src\gui\performance_overlay.cpp;D:\sss\build\PS4Emulator.dir\Debug\performance_overlay.obj
D:\sss\src\jit\jit_diagnostics.cpp;D:\sss\build\PS4Emulator.dir\Debug\jit_diagnostics.obj
D:\sss\src\jit\x86_64_jit_compiler.cpp;D:\sss\build\PS4Emulator.dir\Debug\x86_64_jit_compiler.obj
D:\sss\src\jit\x86_64_jit_helpers.cpp;D:\sss\build\PS4Emulator.dir\Debug\x86_64_jit_helpers.obj
D:\sss\src\loader\crypto_utils.cpp;D:\sss\build\PS4Emulator.dir\Debug\crypto_utils.obj
D:\sss\src\loader\elf_loader.cpp;D:\sss\build\PS4Emulator.dir\Debug\elf_loader.obj
D:\sss\src\loader\key_store.cpp;D:\sss\build\PS4Emulator.dir\Debug\key_store.obj
D:\sss\src\loader\pkg_installer.cpp;D:\sss\build\PS4Emulator.dir\Debug\pkg_installer.obj
D:\sss\src\loader\self_decrypter.cpp;D:\sss\build\PS4Emulator.dir\Debug\self_decrypter.obj
D:\sss\src\main.cpp;D:\sss\build\PS4Emulator.dir\Debug\main.obj
D:\sss\src\memory\memory_compressor.cpp;D:\sss\build\PS4Emulator.dir\Debug\memory_compressor.obj
D:\sss\src\memory\memory_diagnostics.cpp;D:\sss\build\PS4Emulator.dir\Debug\memory_diagnostics.obj
D:\sss\src\memory\memory_prefetcher.cpp;D:\sss\build\PS4Emulator.dir\Debug\memory_prefetcher.obj
D:\sss\src\memory\physical_memory_allocator.cpp;D:\sss\build\PS4Emulator.dir\Debug\physical_memory_allocator.obj
D:\sss\src\memory\ps4_mmu.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_mmu.obj
D:\sss\src\memory\swap_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\swap_manager.obj
D:\sss\src\memory\tlb.cpp;D:\sss\build\PS4Emulator.dir\Debug\tlb.obj
D:\sss\src\ps4\fiber_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\fiber_manager.obj
D:\sss\src\ps4\orbis_os.cpp;D:\sss\build\PS4Emulator.dir\Debug\orbis_os.obj
D:\sss\src\ps4\ps4_audio.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_audio.obj
D:\sss\src\ps4\ps4_controllers.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_controllers.obj
D:\sss\src\ps4\ps4_emulator.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_emulator.obj
D:\sss\src\ps4\ps4_filesystem.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_filesystem.obj
D:\sss\src\ps4\ps4_gpu.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_gpu.obj
D:\sss\src\ps4\ps4_tsc.cpp;D:\sss\build\PS4Emulator.dir\Debug\ps4_tsc.obj
D:\sss\src\ps4\trophy_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\trophy_manager.obj
D:\sss\src\ps4\zlib_wrapper.cpp;D:\sss\build\PS4Emulator.dir\Debug\zlib_wrapper.obj
D:\sss\src\syscall\syscall_handler.cpp;D:\sss\build\PS4Emulator.dir\Debug\syscall_handler.obj
D:\sss\src\video_core\command_processor.cpp;D:\sss\build\PS4Emulator.dir\Debug\command_processor.obj
D:\sss\src\video_core\gnm_shader_translator.cpp;D:\sss\build\PS4Emulator.dir\Debug\gnm_shader_translator.obj
D:\sss\src\video_core\gnm_state.cpp;D:\sss\build\PS4Emulator.dir\Debug\gnm_state.obj
D:\sss\src\video_core\shader_emulator.cpp;D:\sss\build\PS4Emulator.dir\Debug\shader_emulator.obj
D:\sss\src\video_core\tile_manager.cpp;D:\sss\build\PS4Emulator.dir\Debug\tile_manager.obj
