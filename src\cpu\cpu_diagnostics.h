#pragma once

#include <mutex>
#include <string>
#include <unordered_map>

#include "x86_64_cpu.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {

// IMPROVEMENT: Enhanced diagnostic metrics
enum class DiagnosticLevel {
  Basic,      // Essential metrics only
  Detailed,   // Include performance counters
  Verbose     // Include all debug information
};

/**
 * @brief Singleton class for collecting and reporting CPU diagnostic metrics.
 * IMPROVEMENT: Enhanced with performance monitoring and configurable detail levels.
 */
class CPUDiagnostics {
public:
  /**
   * @brief Retrieves the singleton instance of CPUDiagnostics.
   * @return Reference to the singleton instance.
   */
  static CPUDiagnostics &GetInstance();

  /**
   * @brief Updates CPU-related diagnostic metrics.
   * @details Collects metrics from all CPUs, pipelines, APIC, fibers, and caches.
   * @param level Detail level for metrics collection
   */
  void UpdateMetrics(DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief Updates CPU-related diagnostic metrics with provided data.
   * @details Collects metrics from provided CPU data to break circular dependency.
   * @param cpus Vector of CPU pointers to collect metrics from
   * @param fiberManager Reference to fiber manager for fiber count
   * @param level Detail level for metrics collection
   */
  void UpdateMetricsWithData(const std::vector<class X86_64CPU*>& cpus,
                            const class ps4::FiberManager& fiberManager,
                            DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief Resets all diagnostic metrics to default values.
   */
  void ResetMetrics();

  /**
   * @brief Retrieves the current diagnostic metrics.
   * @param level Detail level for returned metrics
   * @return Map of metric names to values.
   */
  std::unordered_map<std::string, uint64_t> GetMetrics(DiagnosticLevel level = DiagnosticLevel::Detailed) const;

  /**
   * @brief Logs detailed diagnostic information to spdlog.
   * @param level Detail level for logging
   */
  void LogDiagnostics(DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief IMPROVEMENT: Get performance summary
   * @return Human-readable performance summary
   */
  std::string GetPerformanceSummary() const;

  /**
   * @brief IMPROVEMENT: Export metrics to JSON format
   * @return JSON string containing all metrics
   */
  std::string ExportToJSON() const;

  /**
   * @brief IMPROVEMENT: Set update interval for automatic metrics collection
   * @param intervalMs Update interval in milliseconds
   */
  void SetUpdateInterval(uint32_t intervalMs);

private:
  CPUDiagnostics();
  ~CPUDiagnostics() = default;

  // Prevent copying
  CPUDiagnostics(const CPUDiagnostics&) = delete;
  CPUDiagnostics& operator=(const CPUDiagnostics&) = delete;

  void CollectBasicMetrics();
  void CollectDetailedMetrics();
  void CollectVerboseMetrics();

  mutable std::mutex m_mutex;
  std::unordered_map<std::string, uint64_t> m_metrics;
  std::unordered_map<std::string, double> m_performance_metrics;
  uint32_t m_update_interval_ms = 1000; // Default 1 second
  std::chrono::steady_clock::time_point m_last_update;
};

} // namespace x86_64