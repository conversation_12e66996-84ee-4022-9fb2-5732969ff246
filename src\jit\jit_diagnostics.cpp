// Copyright 2025 <Copyright Owner>

#include "jit/jit_diagnostics.h"

// C++ system headers
#include <chrono>
#include <mutex>
#include <string>
#include <unordered_map>

// Third-party headers
#include <spdlog/spdlog.h>

// Project headers
#include "ps4/ps4_emulator.h"

namespace x86_64 {
/**
 * @brief Constructs a JITDiagnostics singleton instance.
 */
JITDiagnostics::JITDiagnostics() {
  try {
    ResetMetrics();
    spdlog::info("JITDiagnostics initialized");
  } catch (const std::exception &e) {
    spdlog::error("JITDiagnostics initialization failed: {}", e.what());
    // Initialize with empty metrics to prevent crashes
    m_metrics.clear();
  }
}

/**
 * @brief Retrieves the singleton instance of JITDiagnostics.
 * @return Reference to the singleton instance.
 */
JITDiagnostics &JITDiagnostics::GetInstance() {
  static JITDiagnostics instance;
  return instance;
}

/**
 * @brief Updates JIT-related diagnostic metrics.
 * @details Collects metrics from all JIT compilers, including cache usage,
 * executions, hot blocks, compilation latency, and cache hit/miss ratios.
 */
void JITDiagnostics::UpdateMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("JIT diagnostics: UpdateMetrics called but emulator data not available");
    return;
  } catch (const std::exception &e) {
    spdlog::warn("JITDiagnostics::UpdateMetrics failed: {}", e.what());
  }
}

/**
 * @brief Updates JIT-related diagnostic metrics with provided data.
 * @details Collects metrics from provided JIT compilers to break circular dependency.
 */
void JITDiagnostics::UpdateMetricsWithData(const std::vector<x86_64::X86_64JITCompiler*>& jitCompilers) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    size_t cpu_count = jitCompilers.size();
    if (cpu_count == 0) {
      spdlog::warn("JIT diagnostics: no JIT compilers available");
      return;
    }

    uint64_t total_blocks_compiled = 0;
    uint64_t total_executions = 0;
    uint64_t total_cache_clears = 0;
    uint64_t total_simd_instructions = 0;
    uint64_t total_cache_usage = 0;
    uint64_t total_hot_block_count = 0;
    uint64_t total_hot_block_executions = 0;
    uint64_t total_compilation_latency_us = 0;
    uint64_t total_cache_hits = 0;
    uint64_t total_cache_misses = 0;

    for (size_t i = 0; i < cpu_count; ++i) {
      auto &jit = *jitCompilers[i];
      auto stats = jit.GetStats();

      total_blocks_compiled += stats.blocksCompiled;
      total_executions += stats.executions;
      total_cache_clears += stats.cacheClears;
      total_simd_instructions += stats.simdInstructions;
      total_cache_usage += static_cast<uint64_t>(jit.GetCacheUsage() * 100);

      auto hotBlocks = jit.GetHotBlocks(5);
      total_hot_block_count += hotBlocks.size();
      uint64_t hotExecutions = 0;
      for (size_t j = 0; j < hotBlocks.size(); ++j) {
        m_metrics["hot_block_cpu" + std::to_string(i) + "_" +
                  std::to_string(j) + "_executions"] = hotBlocks[j].second;
        hotExecutions += hotBlocks[j].second;
      }
      total_hot_block_executions += hotExecutions;

      total_compilation_latency_us += stats.compilationLatencyUs;
      total_cache_hits += stats.cacheHits;
      total_cache_misses += stats.cacheMisses;
    }

    m_metrics["blocks_compiled"] = total_blocks_compiled / cpu_count;
    m_metrics["executions"] = total_executions / cpu_count;
    m_metrics["cache_clears"] = total_cache_clears / cpu_count;
    m_metrics["simd_instructions"] = total_simd_instructions / cpu_count;
    m_metrics["cache_usage_percent"] = total_cache_usage / cpu_count;
    m_metrics["hot_block_count"] = total_hot_block_count / cpu_count;
    m_metrics["total_hot_block_executions"] = total_hot_block_executions / cpu_count;
    m_metrics["compilation_latency_us"] = total_compilation_latency_us / cpu_count;
    m_metrics["cache_hit_ratio"] =
        (total_cache_hits + total_cache_misses) > 0
            ? (total_cache_hits * 100) / (total_cache_hits + total_cache_misses)
            : 0;

    spdlog::trace(
        "JIT diagnostics updated: blocks_compiled={}, executions={}, "
        "cache_usage={}%, compilation_latency={}us, cache_hit_ratio={}%",
        static_cast<uint64_t>(m_metrics["blocks_compiled"]),
        static_cast<uint64_t>(m_metrics["executions"]),
        static_cast<uint64_t>(m_metrics["cache_usage_percent"]),
        static_cast<uint64_t>(m_metrics["compilation_latency_us"]),
        static_cast<uint64_t>(m_metrics["cache_hit_ratio"]));
  } catch (const std::exception &e) {
    spdlog::warn("JITDiagnostics::UpdateMetricsWithData failed: {}", e.what());
  }
}

/**
 * @brief Resets all diagnostic metrics to zero.
 */
void JITDiagnostics::ResetMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  m_metrics.clear();
  for (const auto &key :
       {"blocks_compiled", "executions", "cache_clears", "simd_instructions",
        "cache_usage_percent", "hot_block_count", "total_hot_block_executions",
        "compilation_latency_us", "cache_hit_ratio"}) {
    m_metrics[key] = 0;
  }
  spdlog::info("JIT diagnostics reset");
}

/**
 * @brief Retrieves the current diagnostic metrics.
 * @return Map of metric names to values.
 */
const std::unordered_map<std::string, uint64_t> &
JITDiagnostics::GetMetrics() const {
  std::lock_guard<std::mutex> lock(m_mutex);
  return m_metrics;
}

/**
 * @brief Logs detailed diagnostic information.
 */
void JITDiagnostics::LogDiagnostics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  spdlog::info("JIT Diagnostics Report:");
  for (const auto &[key, value] : m_metrics) {
    spdlog::info("  {}: {}", key, value);
  }
}

/**
 * @brief Saves JIT diagnostics state to a stream.
 * @param out Output stream.
 */
void JITDiagnostics::SaveState(std::ostream &out) const {
  std::lock_guard<std::mutex> lock(m_mutex);
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  uint32_t metricCount = static_cast<uint32_t>(m_metrics.size());
  out.write(reinterpret_cast<const char *>(&metricCount), sizeof(metricCount));
  for (const auto &[key, value] : m_metrics) {
    uint32_t keyLen = static_cast<uint32_t>(key.size());
    out.write(reinterpret_cast<const char *>(&keyLen), sizeof(keyLen));
    out.write(key.data(), keyLen);
    out.write(reinterpret_cast<const char *>(&value), sizeof(value));
  }
  spdlog::info("JITDiagnostics state saved");
}

/**
 * @brief Loads JIT diagnostics state from a stream.
 * @param in Input stream.
 */
void JITDiagnostics::LoadState(std::istream &in) {
  std::lock_guard<std::mutex> lock(m_mutex);
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported JITDiagnostics state version: {}", version);
    throw std::runtime_error("Invalid JITDiagnostics state version");
  }
  m_metrics.clear();
  uint32_t metricCount;
  in.read(reinterpret_cast<char *>(&metricCount), sizeof(metricCount));
  for (uint32_t i = 0; i < metricCount && in.good(); ++i) {
    uint32_t keyLen;
    in.read(reinterpret_cast<char *>(&keyLen), sizeof(keyLen));
    std::string key(keyLen, '\0');
    in.read(key.data(), keyLen);
    uint64_t value;
    in.read(reinterpret_cast<char *>(&value), sizeof(value));
    m_metrics[key] = value;
  }
  spdlog::info("JITDiagnostics state loaded");
}
} // namespace x86_64