/*
 * nmake file for uwin pthread library
 */

VERSION 	= -
CCFLAGS 	= -V -g $(CC.DLL)
HAVE_CONFIG_H	== 1
_MT		== 1
_timeb		== timeb
_ftime		== ftime
_errno		== _ast_errno

$(INCLUDEDIR)	:INSTALLDIR:	pthread.h sched.h

pthread $(VERSION) :LIBRARY: attr.c barrier.c cancel.c cleanup.c condvar.c \
	create.c dll.c exit.c fork.c global.c misc.c mutex.c private.c \
	rwlock.c sched.c semaphore.c spin.c sync.c tsd.c nonportable.c

:: ANNOUNCE CONTRIBUTORS COPYING.LIB ChangeLog FAQ GNUmakefile MAINTAINERS \
	Makefile Makefile.in Makefile.vc NEWS PROGRESS README README.WinCE \
	TODO WinCE-PORT install-sh errno.c tests tests.mk acconfig.h \
	config.guess config.h.in config.sub configure configure.in signal.c \
	README.CV README.NONPORTABLE pthread.dsp pthread.dsw

