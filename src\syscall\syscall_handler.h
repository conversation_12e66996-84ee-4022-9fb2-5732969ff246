#pragma once

#include <atomic>
#include <cstdint>
#include <fcntl.h>
#include <functional>
#include <shared_mutex>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <unordered_map>
#include <vector>

#ifndef _WIN32
#include <sys/resource.h>
#include <sys/time.h>
#endif

#include "../cpu/x86_64_cpu.h"
#include "../memory/ps4_mmu.h"

namespace ps4 {

/**
 * @brief Exception for system call errors.
 */
struct SyscallException : std::runtime_error {
  explicit SyscallException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief PS4 error codes for system calls.
 */
constexpr int PS4_ENOSYS = 38;  ///< Function not implemented
constexpr int PS4_ENOTSUP = 45; ///< Operation not supported
constexpr int PS4_EINVAL = 22;  ///< Invalid argument
constexpr int PS4_EBADF = 9;    ///< Bad file descriptor
constexpr int PS4_ENOMEM = 12;  ///< Out of memory
constexpr int PS4_EMFILE = 24;  ///< Too many open files
constexpr int PS4_EAGAIN = 11;  ///< Resource temporarily unavailable
constexpr int PS4_ESRCH = 3;    ///< No such process
constexpr int PS4_EPERM = 1;    ///< Permission denied
constexpr int PS4_EFAULT = 14;  ///< Bad address
constexpr int PS4_ENOENT = 2;   ///< No such file or directory

/**
 * @brief Memory mapping flags for mmap.
 */
constexpr int MAP_SHARED = 0x01;      ///< Shared mapping
constexpr int MAP_PRIVATE = 0x02;     ///< Private mapping
constexpr int MAP_FIXED = 0x10;       ///< Map at fixed address
constexpr int MAP_ANONYMOUS = 0x1000; ///< Anonymous mapping

class PS4Emulator;

/**
 * @brief Handles PS4 system calls for the emulator.
 * @details Manages file descriptors, syscall dispatching, and metrics in a
 *          thread-safe manner. Supports serialization with versioning and
 *          multi-core diagnostics.
 */
class SyscallHandler {
public:
  /**
   * @brief File descriptor information.
   */
  struct FDInfo {
    bool isOpen = false;      ///< Is the FD open?
    int hostFd = -1;          ///< Host OS file descriptor
    std::string path;         ///< File path
    int flags = 0;            ///< Open flags (O_RDONLY, etc.)
    int mode = 0;             ///< File mode (permissions)
    uint64_t offset = 0;      ///< Current file offset
    uint64_t cacheHits = 0;   ///< Cache hits for FD operations
    uint64_t cacheMisses = 0; ///< Cache misses for FD operations
  };

  /**
   * @brief Syscall handler function type.
   */
  using SyscallFunc = std::function<void()>;

  /**
   * @brief Enhanced argument validation and syscall support.
   */
  enum class ArgumentType { Pointer, Integer, String, Buffer, FileDescriptor };

  struct ArgumentInfo {
    ArgumentType type;
    size_t minValue = 0;
    size_t maxValue = UINT64_MAX;
    bool nullable = false;
    size_t bufferSize = 0;
  };

  /**
   * @brief Constructs the syscall handler.
   * @param emulator Reference to the PS4 emulator.
   * @details Initializes FD table and metrics. Thread-safe.
   */
  explicit SyscallHandler(PS4Emulator &emulator);

  /**
   * @brief Destructor, cleaning up resources.
   * @details Closes open file descriptors. Thread-safe.
   */
  ~SyscallHandler();

  /**
   * @brief Initializes the syscall handler.
   * @return True on success, false on failure.
   * @throws SyscallException on initialization errors.
   * @details Registers syscall handlers. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Handles a system call based on the CPU state.
   * @details Dispatches to the appropriate syscall handler. Thread-safe.
   * @throws SyscallException on invalid syscalls or execution errors.
   */
  void HandleSyscall();

  /**
   * @brief Sets the syscall error code.
   * @param error PS4 error code (e.g., PS4_EINVAL).
   * @details Updates CPU registers with error. Thread-safe.
   */
  void SetError(int error);

  /**
   * @brief Sets the syscall error code with logging.
   * @param error PS4 error code.
   * @param message Error message to log.
   * @details Logs error and updates CPU registers. Thread-safe.
   */
  void SetErrorWithLog(int error, const std::string &message);

  /**
   * @brief Statistics for syscall operations with atomic members to prevent
   * race conditions.
   */
  struct Stats {
    std::atomic<uint64_t> callCount{0};      ///< Total syscall invocations
    std::atomic<uint64_t> totalLatencyUs{0}; ///< Total latency in microseconds
    std::atomic<uint64_t> cacheHits{0}; ///< Cache hits for syscall operations
    std::atomic<uint64_t> cacheMisses{
        0}; ///< Cache misses for syscall operations
    std::atomic<uint64_t> errorCount{0}; ///< Total errors encountered

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : callCount(other.callCount.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()),
          errorCount(other.errorCount.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        callCount.store(other.callCount.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
        errorCount.store(other.errorCount.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves syscall handler statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  Stats GetStats() const;

  /**
   * @brief Saves the syscall handler state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the syscall handler state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws SyscallException on invalid state data.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Gets a syscall argument from CPU registers.
   * @param index Argument index (0-5).
   * @return Argument value.
   * @details Thread-safe (read-only). Uses RDI, RSI, RDX, RCX, R8, R9.
   */
  uint64_t GetArg(int index) const;

  /**
   * @brief Gets the syscall number from the CPU.
   * @return Syscall number.
   * @details Thread-safe (read-only). Reads RAX register.
   */
  uint64_t GetSyscallNumber() const;

  /**
   * @brief Sets the syscall return value.
   * @param value Return value.
   * @details Thread-safe. Writes to RAX register.
   */
  void SetReturnValue(uint64_t value);

  /**
   * @brief Gets the current break address.
   * @return Break address.
   * @details Thread-safe (read-only). Reads from virtual memory.
   */
  uint64_t GetBrk() const;

  /**
   * @brief Sets the break address.
   * @param addr New break address.
   * @return True on success, false on failure.
   * @details Thread-safe. Writes to virtual memory.
   */
  bool SetBrk(uint64_t addr);

  /**
   * @brief Reads a null-terminated string from virtual memory.
   * @param addr Virtual address.
   * @return String content.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  std::string ReadString(uint64_t addr);

  /**
   * @brief Allocates a file descriptor.
   * @return File descriptor index, or -1 on failure.
   * @throws SyscallException if no FDs are available.
   * @details Thread-safe. Updates FD table and metrics.
   */
  int AllocateFD();

  /**
   * @brief Frees a file descriptor.
   * @param fd File descriptor index.
   * @details Thread-safe. Closes host FD and updates FD table.
   */
  void FreeFD(int fd);

  /**
   * @brief Registers a syscall handler.
   * @param number Syscall number.
   * @param handler Syscall handler function.
   * @details Thread-safe. Updates syscall table.
   */
  void RegisterSyscall(uint64_t number, SyscallFunc handler);

  /**
   * @brief Registers all syscall handlers.
   * @details Thread-safe. Populates syscall table.
   */
  void RegisterAllSyscalls();

  // Syscall handler methods (thread-safe, update metrics)
  void SysExit();                       ///< Exit process
  void SysFork();                       ///< Fork process (stubbed)
  void SysRead();                       ///< Read from FD
  void SysWrite();                      ///< Write to FD
  void SysOpen();                       ///< Open file
  void SysClose();                      ///< Close FD
  void SysStat();                       ///< Get file status
  void SysFstat();                      ///< Get FD status
  void SysLseek();                      ///< Seek in FD
  void SysGetdents();                   ///< Get directory entries
  void SysUnlink();                     ///< Delete file
  void SysChdir();                      ///< Change directory
  void SysMknod();                      ///< Create node (stubbed on Windows)
  void SysGetpid();                     ///< Get process ID
  void SysKill();                       ///< Send signal
  void SysDup();                        ///< Duplicate FD
  void SysGetppid();                    ///< Get parent process ID
  void SysGetuid();                     ///< Get user ID
  void SysGeteuid();                    ///< Get effective user ID
  void SysGetgid();                     ///< Get group ID
  void SysGetegid();                    ///< Get effective group ID
  void SysIoctl();                      ///< I/O control (stubbed on Windows)
  void SysReboot();                     ///< Reboot system (not permitted)
  void SysMmap();                       ///< Map memory
  void SysMunmap();                     ///< Unmap memory
  void SysBrk();                        ///< Set break address
  void SysRtSigaction();                ///< Signal action (stubbed)
  void SysRtSigprocmask();              ///< Signal mask (stubbed)
  void SysGettimeofday();               ///< Get time of day
  void SysGetrusage();                  ///< Get resource usage
  void SysClockGettime();               ///< Get clock time
  void SysNanosleep();                  ///< Sleep for nanoseconds
  void SysSceKernelDlsym();             ///< Dynamic symbol resolution (stubbed)
  void SysSceKernelUsleep();            ///< Microsecond sleep
  void SysSceKernelGetCpuFreq();        ///< Get CPU frequency
  void SysSceKernelGetProcessId();      ///< Get process ID (SCE)
  void SysSceKernelUuidCreate();        ///< Create UUID (stubbed)
  void SysSceKernelCreateFiber();       ///< Create fiber
  void SysSceKernelDeleteFiber();       ///< Delete fiber
  void SysSceKernelSwitchToFiber();     ///< Switch to fiber
  void SysSceNpTrophyInit();            ///< Initialize trophy system
  void SysSceNpTrophyCreateContext();   ///< Create trophy context
  void SysSceNpTrophyUnlockTrophy();    ///< Unlock trophy
  void SysSceZlibDecompress();          ///< Decompress data
  void SysSceZlibCompress();            ///< Compress data
  void SysSceKernelReadTsc();           ///< Read TSC
  void SysSceKernelGetTscFrequency();   ///< Get TSC frequency
  void SysSceGnmSubmitCommandBuffers(); ///< Submit GNM command buffers
  void SysSceGnmCreateTiledSurface();   ///< Create tiled surface
  void SysSceGnmLoadShader();           ///< Load shader
  void SysSceGnmMapComputeQueue();      ///< Map compute queue

  /**
   * @brief Enhanced syscall validation and implementation methods.
   */
  bool ValidateArguments(const std::vector<ArgumentInfo> &argInfo);
  bool ValidatePointer(uint64_t ptr, size_t size, bool write = false);
  bool ValidateFileDescriptor(int fd);
  bool ValidateString(uint64_t strPtr, size_t maxLen = 4096);
  bool ValidateBuffer(uint64_t bufPtr, size_t size, bool write = false);

  // Enhanced syscall implementations
  void SysAdvancedMmap();
  void SysAdvancedMunmap();
  void SysAdvancedMprotect();
  void SysEnhancedRtSigaction();
  void SysEnhancedRtSigprocmask();
  void SysAdvancedSocket();
  void SysAdvancedBind();
  void SysAdvancedListen();
  void SysAdvancedAccept();
  void SysAdvancedConnect();
  void SysAdvancedSendto();
  void SysAdvancedRecvfrom();
  void SysAdvancedPipe();
  void SysAdvancedFork();
  void SysAdvancedExecve();
  void SysAdvancedWaitpid();

private:
  PS4Emulator &m_emulator;       ///< Reference to the PS4 emulator
  std::vector<FDInfo> m_fdTable; ///< File descriptor table
  std::unordered_map<uint64_t, SyscallFunc>
      m_syscallTable;                  ///< Syscall handlers
  mutable std::shared_mutex m_fdMutex; ///< Mutex for thread safety
  size_t m_maxFds;                     ///< Maximum file descriptors
  mutable Stats m_stats;               ///< Syscall statistics

  // Enhanced signal handling support
  struct SignalHandler {
    uint64_t handler = 0;      ///< Signal handler address
    uint64_t flags = 0;        ///< Signal flags
    uint64_t mask[2] = {0, 0}; ///< Signal mask
    bool installed = false;    ///< Handler installed flag
  };

  std::unordered_map<int, SignalHandler> m_signalHandlers; ///< Signal handlers
  uint64_t m_signalMask[2] = {0, 0}; ///< Process signal mask
  std::unordered_map<uint64_t, uint64_t>
      m_memoryMappings; ///< Memory mappings for validation
};

} // namespace ps4