// Copyright 2025 <Copyright Owner>

#pragma once

#include <atomic>
#include <chrono>
#include <cstdint>
#include <shared_mutex>
#include <string>

namespace ps4 {

/**
 * @brief Exception for TSC-related errors.
 */
struct TSCException : std::runtime_error {
  explicit TSCException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Emulates the PS4 Time Stamp Counter (TSC).
 * @details Manages cycle counting, frequency estimation, and calibration, with
 * thread-safe access and metrics.
 */
class PS4TSC {
public:
  /**
   * @brief Constructs the TSC emulator.
   */
  PS4TSC();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~PS4TSC();

  /**
   * @brief Initializes the TSC.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the TSC.
   */
  void Shutdown();

  /**
   * @brief Gets the current TSC value.
   * @return TSC cycles.
   */
  uint64_t GetTSC() const;

  /**
   * @brief Gets the CPU frequency.
   * @return Frequency in Hz.
   */
  uint64_t GetCPUFrequency() const;

  /**
   * @brief Increments the TSC.
   * @param cycles Cycles to add.
   */
  void IncrementTSC(uint64_t cycles);

  /**
   * @brief Converts TSC cycles to nanoseconds.
   * @param cycles TSC cycles.
   * @return Nanoseconds.
   */
  uint64_t TSCToNanoseconds(uint64_t cycles) const;

  /**
   * @brief Converts nanoseconds to TSC cycles.
   * @param nanoseconds Nanoseconds.
   * @return TSC cycles.
   */
  uint64_t NanosecondsToTSC(uint64_t nanoseconds) const;

  /**
   * @brief Calibrates the TSC with the host.
   */
  void Calibrate();

  /**
   * @brief TSC statistics with atomic members to prevent race conditions.
   */
  struct Stats {
    std::atomic<uint64_t> calibrationCount{0}; ///< Number of calibrations
    std::atomic<double> driftPercent{0.0};     ///< Calibration drift percentage
    std::atomic<uint64_t> totalLatencyUs{
        0}; ///< Total calibration latency (microseconds)
    std::atomic<uint64_t> cacheHits{0};   ///< Cache hits for TSC access
    std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses for TSC access
    std::atomic<uint64_t> syncCount{
        0}; ///< Number of multi-core synchronizations

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : calibrationCount(other.calibrationCount.load()),
          driftPercent(other.driftPercent.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()),
          syncCount(other.syncCount.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        calibrationCount.store(other.calibrationCount.load());
        driftPercent.store(other.driftPercent.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
        syncCount.store(other.syncCount.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves TSC statistics.
   * @return Current statistics.
   */
  Stats GetStats() const;

  /**
   * @brief Saves the TSC state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the TSC state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Estimates the host CPU frequency.
   * @return Estimated frequency in Hz.
   */
  uint64_t EstimateHostFrequency() const;

  std::atomic<uint64_t> m_tsc{0};       ///< Current TSC value
  uint64_t m_cpuFrequency = 1600000000; ///< CPU frequency (1.6 GHz)
  double m_cyclesPerNanosecond = 1.6;   ///< Cycles per nanosecond
  std::chrono::high_resolution_clock::time_point
      m_lastCalibration;                        ///< Last calibration time
  mutable std::shared_mutex m_calibrationMutex; ///< Mutex for thread safety
  mutable Stats m_stats;                        ///< TSC statistics (mutable)
};

// PS4 API functions
extern "C" {
uint64_t sceKernelReadTsc();
uint64_t sceKernelGetTscFrequency();
int sceKernelDelay(uint64_t microseconds);
int sceKernelUsleep(uint64_t microseconds);
}

} // namespace ps4