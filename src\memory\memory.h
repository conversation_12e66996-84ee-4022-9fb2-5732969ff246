#pragma once
#include "memory_types.h"
#include "ps4_mmu.h"
#include <atomic>
#include <cstdint>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <stdexcept>
#include <thread>
#include <unordered_map>
#include <vector>

namespace ps4 {

struct MemoryException : std::runtime_error {
  explicit MemoryException(const std::string &msg) : std::runtime_error(msg) {}
};

class Memory {
public:
  static constexpr size_t PAGE_SIZE = 4096;
  static constexpr size_t TOTAL_SIZE = 4ULL * 1024 * 1024 * 1024;

  // Stats structure for memory access tracking
  struct Stats {
    std::atomic<uint64_t> hits{0};
    std::atomic<uint64_t> misses{0};
    std::atomic<uint64_t> reads{0};
    std::atomic<uint64_t> writes{0};
    std::atomic<uint64_t> totalLatencyUs{0};

    // Allow copying by loading atomics
    Stats() = default;
    Stats(const Stats &other)
        : hits(other.hits.load()), misses(other.misses.load()),
          reads(other.reads.load()), writes(other.writes.load()),
          totalLatencyUs(other.totalLatencyUs.load()) {}
  };

  Memory();
  bool Initialize(size_t size = TOTAL_SIZE);
  void Shutdown();
  bool ReadPhys(uint64_t address, uint8_t *buffer, size_t size) const;
  bool WritePhys(uint64_t address, const uint8_t *buffer, size_t size);
  uint8_t ReadBytePhys(uint64_t physAddr) const;
  void WriteBytePhys(uint64_t physAddr, uint8_t value);
  uint8_t ReadByteVirt(uint64_t virtAddr, uint64_t processId) const;
  void WriteByteVirt(uint64_t virtAddr, uint8_t value, uint64_t processId);
  bool ReadVirt(uint64_t virtAddr, void *buffer, size_t size,
                uint64_t processId) const;
  bool WriteVirt(uint64_t virtAddr, const void *buffer, size_t size,
                 uint64_t processId);
  uint64_t AllocateVirtual(uint64_t processId, uint64_t size,
                           uint64_t alignment, int protection,
                           bool shared = false,
                           MemoryType type = MemoryType::Default);
  void FreeVirtual(uint64_t virtAddr, uint64_t processId);
  bool Protect(uint64_t virtAddr, uint64_t size, uint64_t processId,
               int protection);
  void DumpMemory(uint64_t start, uint64_t size) const;
  uint64_t VirtualToPhysical(uint64_t virtAddr, uint64_t processId,
                             bool write) const;
  int GetPageProtection(uint64_t virtAddr, uint64_t processId) const;
  MemoryType GetMemoryType(uint64_t virtAddr, uint64_t processId) const;
  bool SetMemoryType(uint64_t virtAddr, uint64_t size, uint64_t processId,
                     MemoryType type);
  float GetBandwidthUsage() const;
  void ResetBandwidthCounters();
  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);

  template <typename T> T Read(uint64_t address, uint64_t processId = 1) {
    T value;
    ReadVirt(address, &value, sizeof(T), processId);
    return value;
  }

  template <typename T>
  void Write(uint64_t address, const T &value, uint64_t processId = 1) {
    WriteVirt(address, &value, sizeof(T), processId);
  }

  // Get memory access statistics
  const Stats &GetStats() const { return m_stats; }

private:
  mutable std::shared_mutex m_mutex;
  std::unique_ptr<PS4MMU> m_mmu{nullptr}; // initialize to null explicitly
  mutable Stats m_stats;
};

} // namespace ps4