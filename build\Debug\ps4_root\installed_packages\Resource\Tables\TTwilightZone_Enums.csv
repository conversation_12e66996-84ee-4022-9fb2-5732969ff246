RSID_TTWILIGHTZONE_START, 3300,,,
 ,[Offset], TWILIGHT_ZONE_FLYER_1, 0,
 ,[Offset], Twilight<PERSON><PERSON>\PBTTwilightZone, 1,
 ,[Offset], Twilight<PERSON><PERSON>\InstructionsENG, 2,
 ,[Offset], Twilight<PERSON><PERSON>\InstructionsFR, 3,
 ,[Offset], Twilight<PERSON><PERSON>\InstructionsITAL, 4,
 ,[Offset], TwilightZ<PERSON>\InstructionsGERM, 5,
 ,[Offset], TwilightZ<PERSON>\InstructionsSPAN, 6,
 ,[Offset], TwilightZ<PERSON>\InstructionsPORT, 7,
 ,[Offset], TwilightZone\InstructionsDUTCH, 8,
 ,[Offset], tables\TwilightZone_BG_scroll, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TTWILIGHTZONE_LIGHTS, 3301,,,
RSID_TTWILIGHTZONE_CAMERAS, 3302,,,
RSID_TTWILIGHTZONE_LAMP_TEXTURES, 3303,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_18_On, 16,
 ,[Offset], L_18_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_86_On, 124,
 ,[Offset], L_86_On, 125,
 ,[Offset], L_86_On, 126,
 ,[Offset], L_86_On, 127,
 ,[Offset], L_31_b_Off, 128,
 ,[Offset], L_31_b_On, 129,
 ,[Offset], F_17_off, 130,
 ,[Offset], F_17_on, 131,
 ,[Offset], F_17_B_off, 132,
 ,[Offset], F_17_B_on, 133,
 ,[Offset], F_18_B_off, 134,
 ,[Offset], F_18_B_on, 135,
 ,[Offset], F_18_on, 136,
 ,[Offset], F_20_off, 137,
 ,[Offset], F_20_on, 138,
 ,[Offset], F_28_off, 139,
 ,[Offset], F_28_on, 140,
 ,[Offset], F_37_off, 141,
 ,[Offset], F_37_on, 142,
 ,[Offset], F_38_off, 143,
 ,[Offset], F_38_on, 144,
 ,[Offset], F_39_off, 145,
 ,[Offset], F_39_on, 146,
 ,[Offset], F_40_off, 147,
 ,[Offset], F_40_on, 148,
 ,[Offset], F_41_off, 149,
 ,[Offset], F_41_on, 150,
 ,[Offset], L_28_flasher, 151,
 ,[Offset], L_36_flasher, 152,
 ,[Offset], L_74_flasher, 153,
 ,[Offset], F_19_Off, 154,
 ,[Offset], F_19_On, 155,
RSID_TTWILIGHTZONE_TEXTURES, 3304,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], BackGlassOn, 1,
 ,[Offset], Black_Metal, 2,
 ,[Offset], Black_Posts, 3,
 ,[Offset], Black_Wood, 4,
 ,[Offset], Cabinet, 5,
 ,[Offset], Cabinet_Front, 6,
 ,[Offset], Coin_Slot, 7,
 ,[Offset], defeatThePower_t_c, 8,
 ,[Offset], Extra_Ball_Button, 9,
 ,[Offset], Extra_Metal_Parts, 10,
 ,[Offset], Flipper_Button, 11,
 ,[Offset], GumBall1, 12,
 ,[Offset], jramp_t_c, 13,
 ,[Offset], longramp_t_c, 14,
 ,[Offset], magnaFlip_t_c, 15,
 ,[Offset], Metal_Parts, 16,
 ,[Offset], Rails, 17,
 ,[Offset], red_plastics, 18,
 ,[Offset], red_stack, 19,
 ,[Offset], seperators_t_c, 20,
 ,[Offset], shootHere_t_c, 21,
 ,[Offset], slingshotcover_t_c, 22,
 ,[Offset], sramp_t_c, 23,
 ,[Offset], start button, 24,
 ,[Offset], upper_playfield, 25,
 ,[Offset], WoodFine, 26,
 ,[Offset], Metalramp_t_c, 27,
 ,[Offset], Cabinet_Head, 28,
 ,[Offset], rubberband_temp, 29,
 ,[Offset], Bumper_Hamer, 30,
 ,[Offset], Bumper_Sensors, 31,
 ,[Offset], Rubber Post_Temp, 32,
 ,[Offset], ClearPlasticPost_01, 33,
 ,[Offset], Generic_Metal, 34,
 ,[Offset], Silver Metal Screws_Temp, 35,
 ,[Offset], frog_targets, 36,
 ,[Offset], Plastic_Ramp_02, 37,
 ,[Offset], PopBumperBody, 38,
 ,[Offset], Twilight_Zone_Playfield_Bottom, 39,
 ,[Offset], Twilight_Zone_Playfield_Top, 40,
 ,[Offset], Plunger, 41,
 ,[Offset], Flipper, 42,
 ,[Offset], Targets, 43,
 ,[Offset], metal_temp, 44,
 ,[Offset], yellow_target, 45,
 ,[Offset], metalPiece_t_c, 46,
 ,[Offset], onewaygate, 47,
 ,[Offset], upper_playfield_Metal, 48,
 ,[Offset], Rubber_cover, 49,
 ,[Offset], Harley_Spinner, 50,
 ,[Offset], Harley_gate, 51,
 ,[Offset], Apron, 52,
 ,[Offset], bluePiece_t_c, 53,
 ,[Offset], decal_t_c, 54,
 ,[Offset], rampMetal_t_c, 55,
 ,[Offset], Red_targets, 56,
 ,[Offset], Green_Target, 57,
 ,[Offset], HarleyBumperBody, 58,
 ,[Offset], Clock_t_c, 59,
 ,[Offset], Blue_Light_Off, 60,
 ,[Offset], red_light_Off, 61,
 ,[Offset], white_light_Off, 62,
 ,[Offset], bulb1, 63,
 ,[Offset], Blue_Plastics, 64,
 ,[Offset], Metal_Walls, 65,
 ,[Offset], plasticRamp_t_c, 66,
 ,[Offset], purplePlastic_t_C, 67,
 ,[Offset], ClearPlastic_t_c, 68,
 ,[Offset], GumBall2, 69,
 ,[Offset], Red_Bumper, 70,
 ,[Offset], Orange_Bumper, 71,
 ,[Offset], Clear_Bumper, 72,
 ,[Offset], roundBumper_t_c, 73,
 ,[Offset], blackPlastics_t_c, 74,
RSID_TTWILIGHTZONE_MODELS, 3305,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Habit_Trail, 1,
 ,[Offset], Backglass, 2,
 ,[Offset], Blue_Plastic_Pieces, 3,
 ,[Offset], Cabinet, 4,
 ,[Offset], Cabinet_Buttons, 5,
 ,[Offset], Cabinet_Metals, 6,
 ,[Offset], Clear_Plastics, 7,
 ,[Offset], Clock, 8,
 ,[Offset], Curl_Habit_Trail, 9,
 ,[Offset], Flashers, 10,
 ,[Offset], Flipper_Lane_Plastics, 11,
 ,[Offset], Gumball_Machine, 12,
 ,[Offset], Light_Bulbs, 13,
 ,[Offset], Metal_Pieces, 14,
 ,[Offset], Metal_Ramp, 15,
 ,[Offset], Metal_Walls, 16,
 ,[Offset], Middle_Habit_Trail, 17,
 ,[Offset], Plastic_Posts, 18,
 ,[Offset], Plastic_Ramp, 19,
 ,[Offset], Playfield, 20,
 ,[Offset], Pop_Bumpers, 21,
 ,[Offset], Red_Plastic_Pieces, 22,
 ,[Offset], Right_Habit_Trail, 23,
 ,[Offset], Upper_Playfield, 24,
 ,[Offset], Vertical_Plastic_Pieces, 25,
 ,[Offset], Wooden_Rails, 26,
 ,[Offset], Bumper, 27,
 ,[Offset], flipper, 28,
 ,[Offset], Flipper_Stub, 29,
 ,[Offset], Plunger, 30,
 ,[Offset], Rocket_Kicker, 31,
 ,[Offset], Slingshot_Left, 32,
 ,[Offset], Slingshot_Right, 33,
 ,[Offset], Target, 34,
 ,[Offset], Target_Yellow, 35,
 ,[Offset], Rubber_Covers, 36,
 ,[Offset], Upper_Playfield_Metals, 37,
 ,[Offset], Upper_Playfield_Plastics, 38,
 ,[Offset], Wire, 39,
 ,[Offset], Rubber_Pieces, 40,
 ,[Offset], Plunger_Diverter, 41,
 ,[Offset], Dump_Diverter, 42,
 ,[Offset], One_Way_Gate, 43,
 ,[Offset], Gate, 44,
 ,[Offset], Plastic_Ramp_Wire, 45,
 ,[Offset], Gumball_Diverter, 46,
 ,[Offset], Ramp_Diverter, 47,
 ,[Offset], Clock_Big_Hand, 48,
 ,[Offset], Clock_Little_Hand, 49,
 ,[Offset], Clock_Box, 50,
 ,[Offset], Gumball_Plastics, 51,
 ,[Offset], Gumball_Spinner, 52,
 ,[Offset], Gumballs, 53,
 ,[Offset], Gumball_Glass, 54,
 ,[Offset], Screws_And_Bolts, 55,
 ,[Offset], Light_Cutouts, 56,
 ,[Offset], Pop_Bumper_Insides, 57,
 ,[Offset], Pop_Bumpers_Underside, 58,
 ,[Offset], Target_Red, 59,
 ,[Offset], Target_Green, 60,
 ,[Offset], Target_Green, 61,
 ,[Offset], Target_Green, 62,
 ,[Offset], Target_Green, 63,
 ,[Offset], Target_Green, 64,
 ,[Offset], Upper_Playfield_Flasher, 65,
RSID_TTWILIGHTZONE_MODELS_LODS, 3306,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Habit_Trail, 1,
 ,[Offset], Backglass, 2,
 ,[Offset], Blue_Plastic_Pieces, 3,
 ,[Offset], Cabinet, 4,
 ,[Offset], Cabinet_Buttons, 5,
 ,[Offset], Cabinet_Metals, 6,
 ,[Offset], Clear_Plastics, 7,
 ,[Offset], Clock, 8,
 ,[Offset], Curl_Habit_Trail, 9,
 ,[Offset], Flashers, 10,
 ,[Offset], Flipper_Lane_Plastics, 11,
 ,[Offset], Gumball_Machine, 12,
 ,[Offset], Light_Bulbs, 13,
 ,[Offset], Metal_Pieces, 14,
 ,[Offset], Metal_Ramp, 15,
 ,[Offset], Metal_Walls, 16,
 ,[Offset], Middle_Habit_Trail, 17,
 ,[Offset], Plastic_Posts, 18,
 ,[Offset], Plastic_Ramp, 19,
 ,[Offset], Playfield, 20,
 ,[Offset], Pop_Bumpers, 21,
 ,[Offset], Red_Plastic_Pieces, 22,
 ,[Offset], Right_Habit_Trail, 23,
 ,[Offset], Upper_Playfield, 24,
 ,[Offset], Vertical_Plastic_Pieces, 25,
 ,[Offset], Wooden_Rails, 26,
 ,[Offset], Bumper, 27,
 ,[Offset], flipper, 28,
 ,[Offset], Flipper_Stub, 29,
 ,[Offset], Plunger, 30,
 ,[Offset], Rocket_Kicker, 31,
 ,[Offset], Slingshot_Left, 32,
 ,[Offset], Slingshot_Right, 33,
 ,[Offset], Target, 34,
 ,[Offset], Target_Yellow, 35,
 ,[Offset], Rubber_Covers, 36,
 ,[Offset], Upper_Playfield_Metals, 37,
 ,[Offset], Upper_Playfield_Plastics, 38,
 ,[Offset], Wire, 39,
 ,[Offset], Rubber_Pieces, 40,
 ,[Offset], Plunger_Diverter, 41,
 ,[Offset], Dump_Diverter, 42,
 ,[Offset], One_Way_Gate, 43,
 ,[Offset], Gate, 44,
 ,[Offset], Plastic_Ramp_Wire, 45,
 ,[Offset], Gumball_Diverter, 46,
 ,[Offset], Ramp_Diverter, 47,
 ,[Offset], Clock_Big_Hand, 48,
 ,[Offset], Clock_Little_Hand, 49,
 ,[Offset], Clock_Box, 50,
 ,[Offset], Gumball_Plastics, 51,
 ,[Offset], Gumball_Spinner, 52,
 ,[Offset], Gumballs, 53,
 ,[Offset], Gumball_Glass, 54,
 ,[Offset], Screws_And_Bolts, 55,
 ,[Offset], Light_Cutouts, 56,
 ,[Offset], Pop_Bumper_Insides, 57,
 ,[Offset], Pop_Bumpers_Underside, 58,
 ,[Offset], Target_Red, 59,
 ,[Offset], Target_Green, 60,
 ,[Offset], Flasher_Planes_A, 61,
 ,[Offset], Flasher_Planes_B, 62,
 ,[Offset], Flasher_Planes_C, 63,
 ,[Offset], Light_Cutouts_2, 64,
 ,[Offset], Upper_Playfield_Flasher, 65,
RSID_TTWILIGHTZONE_COLLISION, 3307,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Wall, 1,
 ,[Offset], Ball_Drain, 2,
 ,[Offset], Bumper, 3,
 ,[Offset], Flipper_Lane_Left, 4,
 ,[Offset], Flipper_Lane_Right, 5,
 ,[Offset], flipper_Left_Back, 6,
 ,[Offset], Flipper_Left_Front, 7,
 ,[Offset], Flipper_Right_Back, 8,
 ,[Offset], Flipper_Right_Front, 9,
 ,[Offset], Flipper_Stub_Back, 10,
 ,[Offset], Flipper_Stub_Front, 11,
 ,[Offset], Left_Wall, 12,
 ,[Offset], PLayfield, 13,
 ,[Offset], Plunger_Lane, 14,
 ,[Offset], Plunger_Moving, 15,
 ,[Offset], PLunger_Rest, 16,
 ,[Offset], Right_Outer_Wall, 17,
 ,[Offset], Right_Wall, 18,
 ,[Offset], Rocket_Kicker, 19,
 ,[Offset], Rubber_Back, 20,
 ,[Offset], Rubber_Left, 21,
 ,[Offset], Rubber_Right, 22,
 ,[Offset], Slingshot_Left, 23,
 ,[Offset], Slingshot_Left_Front, 24,
 ,[Offset], Slingshot_Right, 25,
 ,[Offset], Slingshot_Right_Front, 26,
 ,[Offset], Target, 27,
 ,[Offset], Target_Yellow, 28,
 ,[Offset], Plunger_Diverter, 29,
 ,[Offset], Dump_Diverter, 30,
 ,[Offset], Dump_Diverter_Trap, 31,
 ,[Offset], One_Way_Gate_Back, 32,
 ,[Offset], One_Way_Gate_Front, 33,
 ,[Offset], Gumball_Entry_Trap, 34,
 ,[Offset], Scoop_Trap, 35,
 ,[Offset], Trough, 36,
 ,[Offset], Gate, 37,
 ,[Offset], Plunger_Moving_Auto, 38,
 ,[Offset], Gumball_Diverter, 39,
 ,[Offset], Lower_Left_Playfield_Switch, 40,
 ,[Offset], Lower_Right_Playfield_Switch, 41,
 ,[Offset], Upper_Left_Playfield_Switch, 42,
 ,[Offset], Upper_Right_Playfield_Switch, 43,
 ,[Offset], Opto_Plane, 44,
 ,[Offset], Ramp_Diverter, 45,
 ,[Offset], Back_Habit, 46,
 ,[Offset], Curl_Habit, 47,
 ,[Offset], Gumball_Trap, 48,
 ,[Offset], Left_Habit, 49,
 ,[Offset], Magnet, 50,
 ,[Offset], Metal_Ramp, 51,
 ,[Offset], PLastic_Ramp, 52,
 ,[Offset], Right_Habit, 53,
 ,[Offset], Upper_Playfield, 54,
 ,[Offset], Curl_Habit_Trap, 55,
 ,[Offset], Curl_Habit_Floor, 56,
 ,[Offset], Plunger_Floor, 57,
 ,[Offset], Plunger_Side, 58,
 ,[Offset], Metal_Ramp_Floor, 59,
 ,[Offset], MultiBall_Trap, 60,
 ,[Offset], Rubber_Ramp_Posts, 61,
 ,[Offset], Flipper_Catch, 62,
 ,[Offset], Left_Wall_Hole, 63,
 ,[Offset], Left_Outer_Wall, 64,
 ,[Offset], Left_Rubber_Top, 65,
 ,[Offset], Left_Upper_Wall, 66,
 ,[Offset], Right_Inner_Wall, 67,
 ,[Offset], Right_Middle_Wall, 68,
 ,[Offset], Upper_Rubber, 69,
 ,[Offset], Gumball_Wall, 70,
 ,[Offset], Scoop, 71,
RSID_TTWILIGHTZONE_PLACEMENT, 3308,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TTWILIGHTZONE_EMUROM, 3309,,,
 ,[Offset], tz_92, 0,
 ,[Offset], tz_92_attract, 1,
 ,[Offset], tz_92_attract, 2,
RSID_TTWILIGHTZONE_SOUNDS_START, 3310,,,
RSID_TTWILIGHTZONE_EMU_SOUNDS, 3311,,,
 ,[Offset], S0002-LP, 0,
 ,[Offset], S0003-LP, 1,
 ,[Offset], S0004-LP1, 2,
 ,[Offset], S0004-LP2, 3,
 ,[Offset], S0005-LP, 4,
 ,[Offset], S0006-LP, 5,
 ,[Offset], S0007-LP1, 6,
 ,[Offset], S0007-LP2, 7,
 ,[Offset], S0008-LP, 8,
 ,[Offset], S0009-LP, 9,
 ,[Offset], S000A-LP1, 10,
 ,[Offset], S000A-LP2, 11,
 ,[Offset], S000B-LP, 12,
 ,[Offset], S000C-LP1, 13,
 ,[Offset], S000D-LP1, 14,
 ,[Offset], S000E-LP1, 15,
 ,[Offset], S000F-LP, 16,
 ,[Offset], S0010-LP1, 17,
 ,[Offset], S0010-LP2, 18,
 ,[Offset], S0011-LP, 19,
 ,[Offset], S0012-LP, 20,
 ,[Offset], S0013-LP, 21,
 ,[Offset], S0014-LP, 22,
 ,[Offset], S0015-LP, 23,
 ,[Offset], S0016-LP, 24,
 ,[Offset], S0017-LP, 25,
 ,[Offset], S0018-LP, 26,
 ,[Offset], S0019-LP, 27,
 ,[Offset], S001A-LP, 28,
 ,[Offset], S001B-LP, 29,
 ,[Offset], S001C-LP, 30,
 ,[Offset], S001D-LP1, 31,
 ,[Offset], S001D-LP2, 32,
 ,[Offset], S001E-LP, 33,
 ,[Offset], S001F-LP, 34,
 ,[Offset], S0030_C3, 35,
 ,[Offset], S0040-LP, 36,
 ,[Offset], S0041-LP1, 37,
 ,[Offset], S0042-LP1, 38,
 ,[Offset], S0042-LP2, 39,
 ,[Offset], S0043-LP1, 40,
 ,[Offset], S0043-LP2, 41,
 ,[Offset], S0044-LP, 42,
 ,[Offset], S0045-LP1, 43,
 ,[Offset], S0045-LP2, 44,
 ,[Offset], S0050_C4, 45,
 ,[Offset], S0051_C4, 46,
 ,[Offset], S0052_C4, 47,
 ,[Offset], S0053_C4, 48,
 ,[Offset], S0054_C4, 49,
 ,[Offset], S0055_C4, 50,
 ,[Offset], S0056_C4, 51,
 ,[Offset], S0057_C4, 52,
 ,[Offset], S0058_C4, 53,
 ,[Offset], S0059_C4, 54,
 ,[Offset], S0081_C4, 55,
 ,[Offset], S0082_C4, 56,
 ,[Offset], S0084_C4, 57,
 ,[Offset], S0085_C4, 58,
 ,[Offset], S0086_C4, 59,
 ,[Offset], S0087_C4, 60,
 ,[Offset], S0088_C4, 61,
 ,[Offset], S0089_C4, 62,
 ,[Offset], S008A_C4, 63,
 ,[Offset], S008D_C4, 64,
 ,[Offset], S008E_C4, 65,
 ,[Offset], S008F_C4, 66,
 ,[Offset], S0090_C4, 67,
 ,[Offset], S0091_C4, 68,
 ,[Offset], S0093_C4, 69,
 ,[Offset], S0094_C4, 70,
 ,[Offset], S0095_C4, 71,
 ,[Offset], S0096_C4, 72,
 ,[Offset], S0097_C4, 73,
 ,[Offset], S0098_C4, 74,
 ,[Offset], S009B_C4, 75,
 ,[Offset], S009C_C4, 76,
 ,[Offset], S009D_C4, 77,
 ,[Offset], S009E_C4, 78,
 ,[Offset], S009F_C4, 79,
 ,[Offset], S00A0_C4, 80,
 ,[Offset], S00A1_C4, 81,
 ,[Offset], S00A2_C4, 82,
 ,[Offset], S00A3_C2, 83,
 ,[Offset], S00A4_C2, 84,
 ,[Offset], S00A5_C2, 85,
 ,[Offset], S00A7_C4, 86,
 ,[Offset], S00A8_C4, 87,
 ,[Offset], S00A9_C4, 88,
 ,[Offset], S00AB_C2, 89,
 ,[Offset], S00AC_C2, 90,
 ,[Offset], S00AD_C4, 91,
 ,[Offset], S00AE_C4, 92,
 ,[Offset], S00AF_C2, 93,
 ,[Offset], S00B0_C4, 94,
 ,[Offset], S00B1_C4, 95,
 ,[Offset], S00B2_C4, 96,
 ,[Offset], S00B3_C4, 97,
 ,[Offset], S00B4_C4, 98,
 ,[Offset], S00B5_C4, 99,
 ,[Offset], S00B6_C4, 100,
 ,[Offset], S00B7_C4, 101,
 ,[Offset], S00B8_C4, 102,
 ,[Offset], S00B9_C4, 103,
 ,[Offset], S00BA_C4, 104,
 ,[Offset], S00BB_C4, 105,
 ,[Offset], S00BC_C4, 106,
 ,[Offset], S00BD_C4, 107,
 ,[Offset], S00BE_C4, 108,
 ,[Offset], S00BF_C4, 109,
 ,[Offset], S00C0_C4, 110,
 ,[Offset], S00C1_C4, 111,
 ,[Offset], S00C2_C4, 112,
 ,[Offset], S00C3_C4, 113,
 ,[Offset], S00C4_C4, 114,
 ,[Offset], S00C5_C4, 115,
 ,[Offset], S00C6_C4, 116,
 ,[Offset], S00C7_C4, 117,
 ,[Offset], S00C8_C4, 118,
 ,[Offset], S00C9_C4, 119,
 ,[Offset], S00CA_C4, 120,
 ,[Offset], S00CB_C4, 121,
 ,[Offset], S00CC_C4, 122,
 ,[Offset], S00CD_C4, 123,
 ,[Offset], S00CE_C4, 124,
 ,[Offset], S00CF_C4, 125,
 ,[Offset], S00D0_C4, 126,
 ,[Offset], S00D1_C4, 127,
 ,[Offset], S00D2_C4, 128,
 ,[Offset], S00D3_C4, 129,
 ,[Offset], S00D4_C4, 130,
 ,[Offset], S00D5_C4, 131,
 ,[Offset], S00D6_C4, 132,
 ,[Offset], S00D7_C4, 133,
 ,[Offset], S00D8_C4, 134,
 ,[Offset], S00D9_C4, 135,
 ,[Offset], S00DA_C4, 136,
 ,[Offset], S00DB_C4, 137,
 ,[Offset], S00DC_C4, 138,
 ,[Offset], S00DD_C4, 139,
 ,[Offset], S00E0_C4, 140,
 ,[Offset], S00E1_C4, 141,
 ,[Offset], S00E2_C4, 142,
 ,[Offset], S00E3_C4, 143,
 ,[Offset], S00E4_C4, 144,
 ,[Offset], S00E5_C4, 145,
 ,[Offset], S00E6_C4, 146,
 ,[Offset], S00E7_C4, 147,
 ,[Offset], S00E8_C4, 148,
 ,[Offset], S00E9_C4, 149,
 ,[Offset], S00EA_C4, 150,
 ,[Offset], S00EB_C4, 151,
 ,[Offset], S00EC_C4, 152,
 ,[Offset], S00ED_C4, 153,
 ,[Offset], S00EE_C4, 154,
 ,[Offset], S00EF_C4, 155,
 ,[Offset], S00F0_C4, 156,
 ,[Offset], S00F1_C4, 157,
 ,[Offset], S00F2_C4, 158,
 ,[Offset], S00F3_C4, 159,
 ,[Offset], S00F4_C4, 160,
 ,[Offset], S00F5_C4, 161,
 ,[Offset], S00F6_C4, 162,
 ,[Offset], S00F7_C4, 163,
 ,[Offset], S00F8_C4, 164,
 ,[Offset], S00FA_C4, 165,
 ,[Offset], S00FB_C4, 166,
 ,[Offset], S00FC_C4, 167,
 ,[Offset], S00FD_C4, 168,
 ,[Offset], S00FE_C4, 169,
 ,[Offset], S00FF_C4, 170,
 ,[Offset], S0101_C2, 171,
 ,[Offset], S0102_C2, 172,
 ,[Offset], S0103_C2, 173,
 ,[Offset], S0104_C2, 174,
 ,[Offset], S0107_C2, 175,
 ,[Offset], S0108_C2, 176,
 ,[Offset], S0109_C2, 177,
 ,[Offset], S010A_C2, 178,
 ,[Offset], S010B_C2, 179,
 ,[Offset], S010C_C2, 180,
 ,[Offset], S010D_C2, 181,
 ,[Offset], S010E_C2, 182,
 ,[Offset], S010F_C2, 183,
 ,[Offset], S0110_C2, 184,
 ,[Offset], S0111_C2, 185,
 ,[Offset], S0112_C2, 186,
 ,[Offset], S0113_C2, 187,
 ,[Offset], S0114_C2, 188,
 ,[Offset], S0115_C2, 189,
 ,[Offset], S0116_C2, 190,
 ,[Offset], S0117_C2, 191,
 ,[Offset], S0118_C2, 192,
 ,[Offset], S0119_C2, 193,
 ,[Offset], S011A_C2, 194,
 ,[Offset], S011B_C2, 195,
 ,[Offset], S011C_C2, 196,
 ,[Offset], S011D_C2, 197,
 ,[Offset], S011E_C2, 198,
 ,[Offset], S0120_C2, 199,
 ,[Offset], S0121_C2, 200,
 ,[Offset], S0122_C2, 201,
 ,[Offset], S0123_C2, 202,
 ,[Offset], S0124_C2, 203,
 ,[Offset], S0125_C2, 204,
 ,[Offset], S0126_C2, 205,
 ,[Offset], S0127_C2, 206,
 ,[Offset], S0128_C2, 207,
 ,[Offset], S0129_C2, 208,
 ,[Offset], S012A_C2, 209,
 ,[Offset], S012B_C2, 210,
 ,[Offset], S012C_C2, 211,
 ,[Offset], S012D_C2, 212,
 ,[Offset], S012E_C2, 213,
 ,[Offset], S0132_C2, 214,
 ,[Offset], S0133_C2, 215,
 ,[Offset], S0134_C2, 216,
 ,[Offset], S0135_C2, 217,
 ,[Offset], S0137_C2, 218,
 ,[Offset], S0138_C2, 219,
 ,[Offset], S0139_C2, 220,
 ,[Offset], S013A_C2, 221,
 ,[Offset], S013B_C2, 222,
 ,[Offset], S013D_C2, 223,
 ,[Offset], S013E_C2, 224,
 ,[Offset], S013F_C2, 225,
 ,[Offset], S0140_C2, 226,
 ,[Offset], S0141_C2, 227,
 ,[Offset], S0142_C2, 228,
 ,[Offset], S0143_C2, 229,
 ,[Offset], S0144_C2, 230,
 ,[Offset], S0145_C2, 231,
 ,[Offset], S0146_C2, 232,
 ,[Offset], S0147_C2, 233,
 ,[Offset], S0148_C2, 234,
 ,[Offset], S014A_C2, 235,
 ,[Offset], S014B_C2, 236,
 ,[Offset], S014C_C2, 237,
 ,[Offset], S014D_C2, 238,
 ,[Offset], S014E_C2, 239,
 ,[Offset], S014F_C2, 240,
 ,[Offset], S0150_C2, 241,
 ,[Offset], S0151_C2, 242,
 ,[Offset], S0153_C2, 243,
 ,[Offset], S0154_C2, 244,
 ,[Offset], S0155_C2, 245,
 ,[Offset], S0156_C2, 246,
 ,[Offset], S0159_C2, 247,
 ,[Offset], S015A_C2, 248,
 ,[Offset], S015B_C2, 249,
 ,[Offset], S015C_C2, 250,
 ,[Offset], S015D_C2, 251,
 ,[Offset], S015E_C2, 252,
 ,[Offset], S015F_C2, 253,
 ,[Offset], S0160_C2, 254,
 ,[Offset], S0161_C2, 255,
 ,[Offset], S0162_C2, 256,
 ,[Offset], S0163_C2, 257,
 ,[Offset], S0164_C2, 258,
 ,[Offset], S0166_C2, 259,
 ,[Offset], S0167_C2, 260,
 ,[Offset], S0168_C2, 261,
 ,[Offset], S0169_C2, 262,
 ,[Offset], S016A_C2, 263,
 ,[Offset], S016B_C2, 264,
 ,[Offset], S016C_C2, 265,
 ,[Offset], S016D_C2, 266,
 ,[Offset], S016E_C2, 267,
 ,[Offset], S016F_C2, 268,
 ,[Offset], S0170_C2, 269,
 ,[Offset], S0171_C2, 270,
 ,[Offset], S0172_C2, 271,
 ,[Offset], S0173_C2, 272,
 ,[Offset], S0174_C2, 273,
 ,[Offset], S0175_C2, 274,
 ,[Offset], S0176_C2, 275,
 ,[Offset], S0177_C2, 276,
 ,[Offset], S0178_C2, 277,
 ,[Offset], S017A_C2, 278,
 ,[Offset], S017B_C2, 279,
 ,[Offset], S017E_C2, 280,
 ,[Offset], S017F_C2, 281,
 ,[Offset], S0180_C2, 282,
 ,[Offset], S0181_C2, 283,
 ,[Offset], S0182_C2, 284,
 ,[Offset], S0183_C2, 285,
 ,[Offset], S0184_C2, 286,
 ,[Offset], S0185_C2, 287,
 ,[Offset], S0186_C2, 288,
 ,[Offset], S0187_C2, 289,
 ,[Offset], S0188_C2, 290,
 ,[Offset], S018A_C2, 291,
 ,[Offset], S018B_C2, 292,
 ,[Offset], S018C_C2, 293,
 ,[Offset], S018F_C2, 294,
 ,[Offset], S0190_C2, 295,
 ,[Offset], S0194_C2, 296,
 ,[Offset], S0195_C2, 297,
 ,[Offset], S0196_C2, 298,
 ,[Offset], S0197_C4, 299,
 ,[Offset], S0198_C4, 300,
 ,[Offset], S0199_C4, 301,
 ,[Offset], S019A_C4, 302,
 ,[Offset], S019B_C4, 303,
 ,[Offset], S019C_C4, 304,
 ,[Offset], S019D_C4, 305,
 ,[Offset], S019E_C4, 306,
 ,[Offset], S019F_C4, 307,
 ,[Offset], S01A0_C4, 308,
 ,[Offset], S01A1_C4, 309,
 ,[Offset], S01A2_C4, 310,
 ,[Offset], S01A3_C4, 311,
RSID_TTWILIGHTZONE_MECH_SOUNDS, 3312,,,
 ,[Offset], clock_fast_loop, 0,
 ,[Offset], clock_slow_loop, 1,
 ,[Offset], dump_diverter_dump, 2,
 ,[Offset], dump_diverter_reset, 3,
 ,[Offset], load_gumball_jump_ramp, 4,
 ,[Offset], release_gumball_1, 5,
 ,[Offset], release_gumball_2, 6,
RSID_TTWILIGHTZONE_SOUNDS_END, 3313,,,
RSID_TTWILIGHTZONE_SAMPLES, 3314,,,
RSID_TTWILIGHTZONE_POWERBALL_TEXTURES, 3315,,,
RSID_TTWILIGHTZONE_END, 3316,,,
