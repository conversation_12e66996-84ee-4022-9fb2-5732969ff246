#include "memory_prefetcher.h"

#include <cstdint>
#include <deque>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <utility>
#include "spdlog/spdlog.h"
#include "ps4/ps4_emulator.h" // Assuming this provides PS4Emulator class
#include "memory/ps4_mmu.h" // Assuming this provides PS4MMU class

#define DEBUG_PREFETCHER 0 // Set to 1 to enable debug logging for prefetcher

// Constants
constexpr size_t MAX_PATTERNS = 100;
constexpr size_t MAX_RECENT_ACCESSES = 1000;
constexpr uint32_t PATTERN_CONFIDENCE_THRESHOLD = 3; // Threshold for pattern confidence
constexpr size_t MIN_CONSECUTIVE_STRIDES_FOR_PATTERN = 3; // Minimum consecutive strides to consider a pattern
constexpr size_t STRIDE_HISTORY_LENGTH = 100; // Length of stride history for each process
#include <algorithm>
#include <chrono>
#include <cmath>
#include <spdlog/spdlog.h>
#include <thread>

namespace ps4 {

constexpr size_t kCacheLineSize = 64; // Standard cache line size in bytes
constexpr size_t kPatternCheckInterval = 10; // Interval for checking access patterns
constexpr size_t kVisualizationInterval = 100; // Interval for updating visualization data
constexpr size_t kMaxPrefetchCount = 5; // Maximum number of addresses to prefetch per access
constexpr uint64_t kPatternExpirationNs = 1'000'000'000ULL; // 1 second expiration for patterns to keep them relevant

// Helper to get current time in nanoseconds using steady_clock
static inline uint64_t CurrentTimeNs() {
  return std::chrono::duration_cast<std::chrono::nanoseconds>(
             std::chrono::steady_clock::now().time_since_epoch())
      .count();
}

MemoryPrefetcher::MemoryPrefetcher() {
  m_prefetchHits = 0;
  m_prefetchMisses = 0;
  m_totalAccesses = 0;
  m_totalPrefetchRequests = 0;
  m_totalPrefetches = 0;
  m_sequentialPatterns = 0;
  m_stridePatterns = 0;

  m_recentPatterns.reserve(MAX_PATTERNS);
  m_patterns.reserve(MAX_PATTERNS);
  m_prefetchedAddresses.reserve(200);
}

bool MemoryPrefetcher::Initialize() {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  m_recentAccesses.clear();
  m_patterns.clear();
  m_lastAccessByProcess.clear();
  m_prefetchedAddresses.clear();
  m_recentPatterns.clear();
  m_strideHistory.clear();

  m_prefetchHits = 0;
  m_prefetchMisses = 0;
  m_totalAccesses = 0;
  m_totalPrefetchRequests = 0;
  m_totalPrefetches = 0;
  m_sequentialPatterns = 0;
  m_stridePatterns = 0;

  spdlog::info("Memory prefetcher initialized");
  return true;
}

void MemoryPrefetcher::RegisterMemoryAccess(uint64_t address, size_t size,
                                            bool isWrite, uint64_t processId) {
  if (!m_enabled) {
    return;
  }

  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  uint64_t timestamp = CurrentTimeNs();

  if (m_prefetchedAddresses.count(address)) {
    RecordPrefetchHit(address);
    m_prefetchedAddresses.erase(address);
  } else {
    RecordPrefetchMiss(address);
  }

  // We only detect patterns on read accesses as they are more indicative of future reads.
  if (!isWrite) {
    auto it = m_lastAccessByProcess.find(processId);
    if (it != m_lastAccessByProcess.end()) {
        int64_t stride = static_cast<int64_t>(address) - static_cast<int64_t>(it->second);
        auto& history = m_strideHistory[processId];
        history.push_back(stride);
        if (history.size() > STRIDE_HISTORY_LENGTH) {
            history.pop_front();
        }
    }
    m_lastAccessByProcess[processId] = address;
  }

  m_recentAccesses.push_back({address, size, isWrite, processId, timestamp});
  if (m_recentAccesses.size() > MAX_RECENT_ACCESSES) {
    m_recentAccesses.pop_front();
  }

  if (m_totalAccesses % kPatternCheckInterval == 0) {
    DetectPatterns();
  }

  PrefetchData(address, processId);

  if (m_totalAccesses % kVisualizationInterval == 0) {
    UpdateVisualizationData();
    CleanupStalePrefetchData(timestamp);
  }
}

std::vector<uint64_t> MemoryPrefetcher::GetPrefetchAddresses(uint64_t address,
                                                             size_t size) {
  if (!m_enabled) {
    return {};
  }

  std::vector<uint64_t> prefetchAddresses;

  for (const auto &[baseAddr, pattern] : m_patterns) {
    if (pattern.confidence < PATTERN_CONFIDENCE_THRESHOLD) {
      continue;
    }

    if (pattern.stride != 0 && (address >= baseAddr && address < baseAddr + std::abs(pattern.stride) * 32)) {
      int64_t offset = static_cast<int64_t>(address - baseAddr);
      int64_t position = offset / pattern.stride;
      int prefetchCount = static_cast<int>(m_aggressiveness * kMaxPrefetchCount);

      for (int i = 1; i <= prefetchCount; ++i) {
        uint64_t prefetchAddr = baseAddr + (position + i) * pattern.stride;
        prefetchAddresses.push_back(prefetchAddr);
        m_prefetchedAddresses[prefetchAddr] = CurrentTimeNs();
      }
    }
  }

  // Simple next-line prefetching as a fallback
  uint64_t nextLine = (address / kCacheLineSize + 1) * kCacheLineSize;
  if(std::find(prefetchAddresses.begin(), prefetchAddresses.end(), nextLine) == prefetchAddresses.end()) {
      prefetchAddresses.push_back(nextLine);
      m_prefetchedAddresses[nextLine] = CurrentTimeNs();
  }

  return prefetchAddresses;
}

void MemoryPrefetcher::ExecutePrefetchHint(uint64_t address, size_t size) {
  if (!m_enabled) {
    return;
  }

  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  uint64_t alignedAddress = address & ~(kCacheLineSize - 1);
  uint64_t currentTime = CurrentTimeNs();

  if (m_prefetchedAddresses.count(alignedAddress) && (currentTime - m_prefetchedAddresses[alignedAddress]) < 1000000) {
    return;
  }

  m_prefetchedAddresses[alignedAddress] = currentTime;

  std::thread([this, alignedAddress, size]() {
    try {
      std::this_thread::sleep_for(std::chrono::microseconds(100)); // Simulate latency
      spdlog::trace("Prefetch completed for address 0x{:x}, size={}", alignedAddress, size);
      std::lock_guard<std::mutex> lock(m_prefetcherMutex);
      m_totalPrefetches++;
    } catch (const std::exception &e) {
      spdlog::warn("Prefetch failed for address 0x{:x}: {}", alignedAddress, e.what());
    }
  }).detach();

  spdlog::debug("Initiated prefetch for address 0x{:x}, size={}", alignedAddress, size);
}

MemoryPrefetcher::Stats MemoryPrefetcher::GetStats() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  Stats stats;
  stats.hitRate = (m_prefetchHits + m_prefetchMisses) > 0 ? 100.0f * static_cast<float>(m_prefetchHits) / static_cast<float>(m_prefetchHits + m_prefetchMisses) : 0.0f;
  stats.totalAccesses = m_totalAccesses;
  stats.prefetchRequests = m_totalPrefetchRequests;
  stats.prefetchHits = m_prefetchHits;
  stats.sequentialPatterns = m_sequentialPatterns;
  stats.stridePatterns = m_stridePatterns;
  return stats;
}

std::vector<std::pair<uint64_t, int64_t>> MemoryPrefetcher::GetRecentPatterns() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_recentPatterns;
}

uint64_t MemoryPrefetcher::GetTotalPrefetchRequests() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_totalPrefetchRequests;
}

uint64_t MemoryPrefetcher::GetTotalPrefetches() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_totalPrefetches;
}

float MemoryPrefetcher::GetAveragePrefetchesPerRequest() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  if (m_totalPrefetchRequests == 0) return 0.0f;
  return static_cast<float>(m_totalPrefetches) / static_cast<float>(m_totalPrefetchRequests);
}

void MemoryPrefetcher::SetAggressiveness(float aggressiveness) {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  m_aggressiveness = std::clamp(aggressiveness, 0.0f, 3.0f);
}

void MemoryPrefetcher::SetEnabled(bool enabled) {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  m_enabled = enabled;
}

void MemoryPrefetcher::Reset() {
  Initialize();
}

/**
 * @brief REFINED: High-level function to orchestrate pattern detection across all processes.
 */
void MemoryPrefetcher::DetectPatterns() {
  std::unordered_map<uint64_t, bool> activeProcesses;
  for (const auto &access : m_recentAccesses) {
    if (!access.isWrite) {
      activeProcesses[access.processId] = true;
    }
  }

  for (const auto &[processId, _] : activeProcesses) {
    AnalyzeStridePatterns(processId);
  }

  // Cleanup stale patterns
  const uint64_t currentTime = CurrentTimeNs();
  for (auto it = m_patterns.begin(); it != m_patterns.end();) {
    if (currentTime - it->second.lastTimestamp > kPatternExpirationNs) {
      it = m_patterns.erase(it);
    } else {
      ++it;
    }
  }
}

/**
 * @brief REFINED: Analyzes the stride history for a given process to find stable patterns.
 * This function looks for a consistent stride that repeats multiple times in a row.
 */
void MemoryPrefetcher::AnalyzeStridePatterns(uint64_t processId) {
    auto history_it = m_strideHistory.find(processId);
    if (history_it == m_strideHistory.end() || history_it->second.size() < MIN_CONSECUTIVE_STRIDES_FOR_PATTERN) {
        return; // Not enough data to find a pattern
    }

    const auto& history = history_it->second;
    const int64_t last_stride = history.back();

    if (last_stride == 0) {
        return; // Zero stride is not a useful pattern
    }

    size_t consecutive_count = 0;
    for (auto it = history.rbegin(); it != history.rend(); ++it) {
        if (*it == last_stride) {
            consecutive_count++;
        } else {
            break; // The sequence of identical strides was broken
        }
    }

    if (consecutive_count >= MIN_CONSECUTIVE_STRIDES_FOR_PATTERN) {
        // A stable pattern has been detected.
        uint64_t last_addr = m_lastAccessByProcess[processId];
        uint64_t base_addr = last_addr - (consecutive_count - 1) * last_stride;

        auto pattern_it = m_patterns.find(base_addr);
        if (pattern_it != m_patterns.end() && pattern_it->second.stride == last_stride) {
            // We've seen this pattern before, increase its confidence.
            pattern_it->second.confidence = std::min(pattern_it->second.confidence + 1, 10u);
            pattern_it->second.lastTimestamp = CurrentTimeNs();
        } else {
            // This is a new pattern.
            PatternEntry new_pattern;
            new_pattern.baseAddress = base_addr;
            new_pattern.stride = last_stride;
            new_pattern.confidence = 1;
            new_pattern.lastTimestamp = CurrentTimeNs();
            m_patterns[base_addr] = new_pattern;

            if (std::abs(last_stride) == kCacheLineSize) {
                m_sequentialPatterns++;
            } else {
                m_stridePatterns++;
            }
        }
    }
}

void MemoryPrefetcher::PrefetchData(uint64_t address, uint64_t processId) {
  m_totalPrefetchRequests++;
  auto prefetchAddresses = GetPrefetchAddresses(address, 8); // Assume 8-byte access size
  m_totalPrefetches += prefetchAddresses.size();

  if (!prefetchAddresses.empty() && spdlog::should_log(spdlog::level::debug)) {
    spdlog::debug("Prefetching {} addresses for process {}",
                  prefetchAddresses.size(), processId);
  }
}

void MemoryPrefetcher::RecordPrefetchHit(uint64_t address) {
  m_prefetchHits++;
  m_totalAccesses++;
  spdlog::trace("Prefetch hit at address 0x{:x}", address);
}

void MemoryPrefetcher::RecordPrefetchMiss(uint64_t address) {
  m_prefetchMisses++;
  m_totalAccesses++;
}

void MemoryPrefetcher::UpdateVisualizationData() {
  m_recentPatterns.clear();
  for (const auto &[baseAddr, pattern] : m_patterns) {
    if (pattern.confidence >= PATTERN_CONFIDENCE_THRESHOLD) {
      m_recentPatterns.emplace_back(pattern.baseAddress, pattern.stride);
    }
  }
}

void MemoryPrefetcher::CleanupStalePrefetchData(uint64_t currentTime) {
  for (auto it = m_prefetchedAddresses.begin(); it != m_prefetchedAddresses.end();) {
    if (currentTime - it->second > kPatternExpirationNs) {
      it = m_prefetchedAddresses.erase(it);
    } else {
      ++it;
    }
  }
#ifdef DEBUG_PREFETCHER
  spdlog::info("Cleaned up stale prefetch data, remaining entries: {}",
               m_prefetchedAddresses.size());
#endif
}

} // namespace ps4