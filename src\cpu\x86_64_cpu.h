#ifndef X86_64_CPU_H
#define X86_64_CPU_H

#include <array>
#include <atomic>
#include <chrono>
#include <cstdint>
#include <immintrin.h> // For SIMD intrinsics
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

// Include register definitions first
#include "cpu/register.h"

// Forward declarations to avoid circular dependencies
namespace ps4 {
class PS4Emulator;
class PS4MMU;
class TLB;
} // namespace ps4

namespace x86_64 {
class Device;
class APIC;
class Pipeline;
class X86_64JITCompiler;
class InstructionDecoder;
class DecodedInstruction;
} // namespace x86_64

// Include necessary headers after forward declarations
#include "cache/cache.h"
#include "cpu/decoded_instruction.h"
#include "cpu/device.h"
#include "cpu/instruction_decoder.h"
#include "cpu/x86_64_pipeline.h"
#include "emulator/apic.h"
#include "emulator/interrupt_handler.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/ps4_mmu.h"
#include "memory/tlb.h"

namespace x86_64 {

// Constants for default CPU state
const uint64_t DEFAULT_STACK_POINTER = 0x7FFFFFF000ULL; // Example stack pointer
const uint64_t DEFAULT_ENTRY_POINT = 0x1000ULL;         // Example entry point
const uint64_t DEFAULT_RFLAGS = 0x202ULL; // Default RFLAGS (IF=1, always 1)

// Segment register values (example for flat model)
const uint16_t KERNEL_CS = 0x08; // Kernel Code Segment
const uint16_t KERNEL_DS = 0x10; // Kernel Data Segment
const uint16_t USER_CS = 0x1B;   // User Code Segment
const uint16_t USER_DS = 0x23;   // User Data Segment

// Exception Vectors (Intel Manual Vol 3A, Chapter 6)
enum ExceptionVector : uint8_t {
  EXC_DE = 0x00,  // Divide Error
  EXC_DB = 0x01,  // Debug
  EXC_NMI = 0x02, // Non-Maskable Interrupt
  EXC_BP = 0x03,  // Breakpoint
  EXC_OF = 0x04,  // Overflow
  EXC_BR = 0x05,  // BOUND Range Exceeded
  EXC_UD = 0x06,  // Invalid Opcode
  EXC_NM = 0x07,  // Device Not Available (No Math Coprocessor)
  EXC_DF = 0x08,  // Double Fault
  EXC_TS = 0x0A,  // Invalid TSS
  EXC_NP = 0x0B,  // Segment Not Present
  EXC_SS = 0x0C,  // Stack-Segment Fault
  EXC_GP = 0x0D,  // General Protection
  EXC_PF = 0x0E,  // Page Fault
  EXC_MF = 0x10,  // x87 FPU Floating-Point Error
  EXC_AC = 0x11,  // Alignment Check
  EXC_MC = 0x12,  // Machine Check
  EXC_XM = 0x13,  // SIMD Floating-Point Exception
  EXC_VE = 0x14,  // Virtualization Exception
  EXC_CP = 0x15,  // Control Protection Exception
};

// RFLAGS bits
enum RFLAGS_BITS : uint64_t {
  FLAG_CF = (1ULL << 0),    // Carry Flag
  FLAG_PF = (1ULL << 2),    // Parity Flag
  FLAG_AF = (1ULL << 4),    // Auxiliary Carry Flag
  FLAG_ZF = (1ULL << 6),    // Zero Flag
  FLAG_SF = (1ULL << 7),    // Sign Flag
  FLAG_TF = (1ULL << 8),    // Trap Flag
  FLAG_IF = (1ULL << 9),    // Interrupt Enable Flag
  FLAG_DF = (1ULL << 10),   // Direction Flag
  FLAG_OF = (1ULL << 11),   // Overflow Flag
  FLAG_IOPL = (3ULL << 12), // I/O Privilege Level (bits 12-13)
  FLAG_NT = (1ULL << 14),   // Nested Task Flag
  FLAG_RF = (1ULL << 16),   // Resume Flag
  FLAG_VM = (1ULL << 17),   // Virtual-8086 Mode Flag
  FLAG_AC = (1ULL << 18),   // Alignment Check Flag
  FLAG_VIF = (1ULL << 19),  // Virtual Interrupt Flag
  FLAG_VIP = (1ULL << 20),  // Virtual Interrupt Pending Flag
  FLAG_ID = (1ULL << 21),   // ID Flag

  // Mask for arithmetic flags that are typically updated together
  ARITHMETIC_FLAGS_MASK =
      FLAG_CF | FLAG_PF | FLAG_AF | FLAG_ZF | FLAG_SF | FLAG_OF,
};

// Register and MSR enums are now defined in cpu/register.h

// Custom exception for CPU errors
struct CPUException : std::runtime_error {
  explicit CPUException(const std::string &msg) : std::runtime_error(msg) {}
};

// Helper for priority queue of interrupts
struct InterruptRequest {
  uint8_t vector;
  uint8_t priority; // Higher value means higher priority

  bool operator<(const InterruptRequest &other) const {
    return priority < other.priority;
  }
};

// Device interface is defined in cpu/device.h

// Programmable Interrupt Controller (PIC)
class PIC : public x86_64::Device {
public:
  PIC();
  void Reset() override;
  void Write(uint64_t address, uint64_t value, uint8_t size) override;
  uint64_t Read(uint64_t address, uint8_t size) override;
  std::string GetName() const override;
  void SaveState(std::ostream &out) const override;
  void LoadState(std::istream &in) override;
  void SignalInterrupt(uint8_t irq);
  void UpdateInterrupts();
  void SetCPU(std::weak_ptr<X86_64CPU> cpu) { this->cpu = cpu; }

private:
  uint8_t icw1, icw4;
  uint8_t imr; // Interrupt mask register
  uint8_t isr; // In-service register
  uint8_t irr; // Interrupt request register
  uint8_t baseVector;
  uint8_t currentCommand;
  uint8_t initSequence;
  std::weak_ptr<X86_64CPU> cpu;
};

// APIC class is defined in emulator/apic.h

// Programmable Interval Timer (PIT)
class PIT : public x86_64::Device {
public:
  PIT();
  void Reset() override;
  void Write(uint64_t address, uint64_t value, uint8_t size) override;
  uint64_t Read(uint64_t address, uint8_t size) override;
  std::string GetName() const override;
  void SaveState(std::ostream &out) const override;
  void LoadState(std::istream &in) override;
  void Tick();
  void SetCPU(std::weak_ptr<X86_64CPU> cpu) { this->cpu = cpu; }

private:
  uint16_t counters[3];
  uint8_t controlWord;
  uint8_t currentChannel;
  bool lowByte = false;
  std::weak_ptr<X86_64CPU> cpu;
};

// Device manager
class DeviceManager {
public:
  void RegisterDevice(uint64_t base, uint64_t size,
                      std::weak_ptr<x86_64::Device> device);
  std::weak_ptr<x86_64::Device> FindDevice(uint64_t address);

private:
  struct DeviceMapping {
    uint64_t base;
    uint64_t size;
    std::weak_ptr<x86_64::Device> device;
  };
  std::vector<DeviceMapping> devices;
};

// FPU State structure (simplified FXSAVE/FXRSTOR format)
struct FPUState {
  uint16_t controlWord;
  uint16_t statusWord;
  uint16_t tagWord;
  uint16_t opcode;
  uint64_t lastInstructionPointer;
  uint64_t lastDataPointer;
  std::array<double, 8> st; // FPU stack registers
  // Add XMM/YMM/ZMM state if needed for full FXSAVE/XSAVE
};

// MMX State structure (part of FPU state)
struct MMXState {
  bool mmxMode;
  // MMX registers are aliased with FPU registers, so no separate array needed
};

// CPU Context for saving/restoring state (e.g., for fiber switching)
struct CPUContext {
  std::array<uint64_t, 17> registers; // RAX-R15, RIP
  uint64_t rflags;
  std::array<__m256i, 16> xmmRegisters; // XMM0-XMM15
  uint64_t rip;
  uint64_t rsp;
  uint16_t cs, ds, es, fs, gs, ss;
  uint64_t cr0, cr2, cr3, cr4;
  uint8_t fpu_state_bytes[512]; // Placeholder for FPU state (FXSAVE area)
};

// VMCS structure definition (simplified)
struct VMCS {
  uint32_t revision_id;
  uint32_t abort_indicator;
  uint64_t guest_rip;
  uint64_t guest_rflags;
  uint64_t guest_cr0;
  uint64_t guest_cr3;
  uint64_t guest_cr4;
  uint64_t host_cr0;
  uint64_t host_cr3;
  uint64_t host_cr4;
  uint64_t host_rip;
  // ... other VMCS fields ...
};

// SMM Saved State (simplified)
struct SMMSavedState {
  uint64_t rip;
  uint64_t rflags;
  uint64_t rax;
  // ... other registers and state
};

const uint32_t VMX_REVISION_ID = 0x00000001;         // Example VMX revision ID
const uint64_t SMM_HANDLER_ENTRY_POINT = 0xFFF00000; // Example SMM entry point

const size_t GENERAL_REGISTER_COUNT = 17; // RAX-R15, RIP
const size_t SEGMENT_REGISTER_COUNT = 6;  // ES, CS, SS, DS, FS, GS
const size_t CONTROL_REGISTER_COUNT = 5; // CR0, CR2, CR3, CR4 (CR1 is reserved)
const size_t DEBUG_REGISTER_COUNT = 6;   // DR0-DR3, DR6, DR7
const size_t XMM_REGISTER_COUNT = 16;    // XMM0-XMM15
const size_t ZMM_REGISTER_COUNT = 32;    // ZMM0-ZMM31
const size_t K_REGISTER_COUNT = 8;       // K0-K7

const size_t TLB_SIZE = 256;               // Number of TLB entries
const size_t INSTRUCTION_BUFFER_SIZE = 16; // Max instruction length + padding
const size_t MAX_INSTRUCTION_LENGTH = 15;  // Max x86-64 instruction length
const size_t MAX_REP_ITERATIONS = 1000000; // Max iterations for REP string ops

const uint64_t MWAIT_MAX_CYCLES = 1000000; // Example MWAIT timeout

class X86_64CPU : public std::enable_shared_from_this<X86_64CPU> {
public:
  X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu, uint32_t cpuId);
  ~X86_64CPU();

  // CPU Lifecycle
  bool Initialize();
  void Shutdown();
  void ResetState();
  void ExecuteCycle();
  void Execute(); // Single instruction execution

  // Register Access
  uint64_t GetRegister(Register r) const;
  bool SetRegister(Register r, uint64_t v);
  uint64_t GetRflags() const;
  void SetRflags(uint64_t f);
  bool GetFlag(uint64_t m) const;
  void SetFlag(uint64_t flag, bool value);

  // Control Register Access
  uint64_t GetControlRegister(Register r) const;
  void SetControlRegister(Register r, uint64_t v);
  uint64_t GetCR0() const;
  uint64_t GetCR2() const;
  uint64_t GetCR3() const;
  uint64_t GetCR4() const;
  void SetCR0(uint64_t v);
  void SetCR2(uint64_t v);
  void SetCR3(uint64_t v);
  void SetCR4(uint64_t v);

  // Debug Register Access
  uint64_t GetDebugRegister(Register r) const;
  void SetDebugRegister(Register r, uint64_t v);

  // MSR Access
  uint64_t GetMSR(MSR msr_index) const;
  void SetMSR(MSR msr_index, uint64_t value);

  // Segment Register Access
  uint16_t GetCS() const;
  void SetCS(uint16_t v);
  uint16_t GetSS() const;
  void SetSS(uint16_t v);
  uint16_t GetDS() const;
  void SetDS(uint16_t v);
  uint16_t GetES() const;
  void SetES(uint16_t v);
  uint16_t GetFS() const;
  void SetFS(uint16_t v);
  uint16_t GetGS() const;
  void SetGS(uint16_t v);

  // Descriptor Table Register Access
  void SetGDTR(uint64_t base, uint16_t limit);
  void SetIDTR(uint64_t base, uint16_t limit);
  void SetLDTR(uint16_t selector);
  void SetTR(uint16_t selector);
  uint64_t GetGDTRBase() const;
  uint16_t GetGDTRLimit() const;
  uint64_t GetIDTRBase() const;
  uint16_t GetIDTRLimit() const;
  uint16_t GetLDTR() const;
  uint16_t GetTR() const;
  uint64_t GetTSSBase() const;
  uint16_t GetTSSLimit() const;
  uint16_t GetKernelSS() const;

  // Privilege Level
  uint8_t GetCPL() const;
  uint8_t GetIOPL() const;

  // Memory Management Unit (MMU) and TLB
  ps4::PS4MMU &GetMemory();
  ps4::PS4MMU &GetMMU() { return GetMemory(); }
  uint64_t TranslateAddress(uint64_t virtualAddr);
  void InvalidateTLB(uint64_t virtAddr);

  // Interrupts
  void QueueInterrupt(uint8_t vector, uint8_t priority);
  void TriggerInterrupt(uint8_t vector, uint64_t errorCode,
                        bool isSoftwareInterrupt);
  void TriggerInterrupt(uint8_t vector); // Simple overload for no error code

  // I/O Port Access
  uint64_t ReadIOPort(uint16_t port, uint8_t size);
  void WriteIOPort(uint16_t port, uint64_t value, uint8_t size);

  // Pipeline support methods
  uint64_t
  CalculateMemoryAddress(const DecodedInstruction::Operand &operand) const;
  void Compare(uint64_t op1, uint64_t op2, uint8_t size);
  void Push(uint64_t value, uint8_t sizeInBytes = 8);
  uint64_t Pop(uint8_t sizeInBytes = 8);

  // State Management
  void SaveState(std::ostream &out);
  void LoadState(std::istream &in);
  void SetContext(const CPUContext &ctx);
  bool IsRunning() const;

  // Diagnostics
  std::unordered_map<std::string, uint64_t> GetDiagnostics() const;
  float GetUtilization() const { return utilization; }

  // APIC Access
  x86_64::APIC &GetAPIC() { return *apic; }
  const x86_64::APIC &GetAPIC() const { return *apic; }

  // XMM Register Access
  __m256i GetXMMRegister(uint8_t reg) const;
  void SetXMMRegister(uint8_t reg, const __m256i &value);

  // Mask Register Access (AVX-512)
  uint16_t GetMaskRegister(uint8_t reg) const;
  void SetMaskRegister(uint8_t reg, uint16_t value);

  // Arithmetic flags (make public)
  bool CheckCondition(uint8_t conditionCode);
  void UpdateArithmeticFlags(uint64_t op1, uint64_t op2, uint64_t result,
                             uint8_t sizeInBits, bool isSubtract);

  // JIT and Pipeline Access
  X86_64JITCompiler &GetJITCompiler();
  Pipeline &GetPipeline();

  // Process Management
  uint64_t GetProcessId() const;
  void SetProcessId(uint64_t pid);

  // CPU Identification
  uint32_t GetCPUId() const;

  // Fiber Management
  bool SwitchToFiber(uint64_t fiberId);

private:
  // Private helper functions for internal register access (no mutex)
  uint64_t _getRegister(Register r) const;
  void _setRegister(Register r, uint64_t v);

  // Internal CPU state
  ps4::PS4Emulator &m_emulator;
  ps4::PS4MMU &mmu;
  uint32_t m_cpuId;

  // Registers
  std::array<uint64_t, GENERAL_REGISTER_COUNT> registers;
  uint64_t rflags;
  std::array<uint16_t, SEGMENT_REGISTER_COUNT> segmentRegisters;
  std::array<uint64_t, CONTROL_REGISTER_COUNT> controlRegisters;
  std::array<uint64_t, DEBUG_REGISTER_COUNT> debugRegisters;
  std::unordered_map<uint32_t, uint64_t> msrRegisters;
  // SIMD Registers
  std::array<__m256i, XMM_REGISTER_COUNT> xmmRegisters; // XMM/YMM registers
  std::array<__m512i, ZMM_REGISTER_COUNT> zmmRegisters; // ZMM registers
  std::array<uint16_t, K_REGISTER_COUNT> kRegisters;    // K registers (mask)

  // FPU State
  FPUState fpuState;
  MMXState mmxState;

  // Descriptor Table Registers
  uint64_t gdtrBase;
  uint16_t gdtrLimit;
  uint64_t idtrBase;
  uint16_t idtrLimit;
  uint16_t ldtr;
  uint16_t tr;
  uint64_t tssBase;
  uint16_t tssLimit;
  uint16_t kernelSS; // Kernel stack segment for SYSCALL/SYSRET

  // CPU Control
  bool running;
  bool halted; // True if CPU is in HLT state
  uint64_t processId;

  // Components
  std::unique_ptr<X86_64JITCompiler> jit;
  std::unique_ptr<InstructionDecoder> decoder;
  std::unique_ptr<Pipeline> pipeline;
  ps4::TLB tlb;

  // Devices
  DeviceManager deviceManager;
  std::shared_ptr<PIC> pic;
  std::shared_ptr<x86_64::APIC> apic;
  std::shared_ptr<PIT> pit;

  // Interrupt Queue
  std::priority_queue<InterruptRequest> interruptQueue;

  // Mutex for thread safety
  mutable std::recursive_timed_mutex mutex;

  // Performance metrics
  float utilization;
  std::chrono::steady_clock::time_point lastCycleStart;

  // MWAIT state
  bool mwaitState = false;
  uint64_t mwaitMonitorAddr = 0;
  uint64_t mwaitSavedValue = 0;
  uint64_t mwaitWakeupCounter = 0;

  // VMX state
  bool vmxEnabled = false;
  bool inVMXNonRootOperation = false;
  uint64_t vmxonRegion = 0;
  uint64_t currentVMCS = 0; // Physical address of current VMCS
  VMCS vmcs;                // In-memory representation of current VMCS
  VMCS vmcsState;           // Saved VMCS state for VMCLEAR

  // SMM state
  bool inSMM = false;
  SMMSavedState smmSavedState;

  // CPU instruction dispatch table for efficient instruction execution
  using CPUInstructionHandler =
      std::function<void(const DecodedInstruction &, uint64_t &)>;
  std::unordered_map<x86_64::InstructionType, CPUInstructionHandler>
      cpuOpcodeDispatchTable;

  // Private helper methods
  void InitializeCPUOpcodeDispatchTable();
  void HandleUnhandledInstruction(const DecodedInstruction &instr,
                                  uint64_t &nextRip);
  void UpdateUtilization(const std::chrono::steady_clock::time_point &start);
  bool CalculateParity(uint64_t value);
  void UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits);
  uint64_t ReadOperandValue(const DecodedInstruction::Operand &operand);
  void WriteOperandValue(const DecodedInstruction::Operand &operand,
                         uint64_t value);
  void WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                          uint64_t value);
  __m256i ReadXmmOperandValue(const DecodedInstruction::Operand &operand);
  void WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                            const __m256i &value);
  void WriteXmmOperand(const DecodedInstruction::Operand &operand,
                       const __m256i &value); // Helper for WriteOperandValue
  void UpdateFlags(uint64_t result, uint64_t op1, uint64_t op2,
                   uint8_t sizeInBits, bool isSubtract);
  bool CheckPagePermissions(uint64_t pte_entry, uint8_t cpl, bool is_write,
                            bool is_execute) const;
  void DeviceTick();
  void FetchDecodeExecute();
  void UpdateStringIndexes(uint8_t size, bool forward = true);
  void ExecuteStringOperation(const DecodedInstruction &instr,
                              uint64_t &nextRip);
  bool ValidateIOPortAccess(uint16_t port, uint8_t size);

  // FPU/SIMD/VMX/SMM instruction handlers
  void ExecuteFloatingPointInstruction(const DecodedInstruction &instr,
                                       uint64_t &nextRip);
  void ExecuteAVXInstruction(const DecodedInstruction &instr,
                             uint64_t &nextRip);
  void ExecuteAVX512Instruction(const DecodedInstruction &instr,
                                uint64_t &nextRip);

  // VMX instructions
  void VMXON(uint64_t region);
  void VMCLEAR(uint64_t region);
  void VMPTRLD(uint64_t region);
  void VMLAUNCH();
  void VMRESUME();
  void VMXOFF();

  // SMM instructions
  void EnterSMM();
  void ExitSMM();
  void RSM(); // Resume from SMM

  // Power Management
  void ExecuteMWAIT();
  void CheckMWAITWakeup();

  // Cache Control
  void CLFLUSH(uint64_t addr);
  void PREFETCH(DecodedInstruction::Operand::Type hint, uint64_t addr);
};

} // namespace x86_64

#endif // X86_64_CPU_H
