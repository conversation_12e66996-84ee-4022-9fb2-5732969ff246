RSID_TBLACK_HOLE_START, 2100,,,
 ,[Offset], BLACHHOLE_FLYER_1, 0,
 ,[Offset], PBTBlackHole, 1,
 ,[Offset], BlackHole\InstructionsENG, 2,
 ,[Offset], BlackHole\InstructionsFR, 3,
 ,[Offset], BlackHole\InstructionsITAL, 4,
 ,[Offset], BlackHole\InstructionsGERM, 5,
 ,[Offset], BlackHole\InstructionsSPAN, 6,
 ,[Offset], BlackHole\InstructionsPORT, 7,
 ,[Offset], BlackHole\InstructionsDUTCH, 8,
 ,[Offset], tables\BHOLE_BG_scroll, 9,
RSID_TBLACK_HOLE_LIGHTS, 2101,,,
RSID_TBLACK_HOLE_CAMERAS, 2102,,,
RSID_TBLACK_HOLE_LAMP_TEXTURES, 2103,,,
 ,[Offset], lamp 01, 0,
 ,[Offset], lamp 01 lit, 1,
 ,[Offset], lamp 02, 2,
 ,[Offset], lamp 02 lit, 3,
 ,[Offset], lamp 03, 4,
 ,[Offset], lamp 03 lit, 5,
 ,[Offset], lamp 04, 6,
 ,[Offset], lamp 04 lit, 7,
 ,[Offset], lamp 05, 8,
 ,[Offset], lamp 05 lit, 9,
 ,[Offset], lamp 06, 10,
 ,[Offset], lamp 06 lit, 11,
 ,[Offset], lamp 07, 12,
 ,[Offset], lamp 07 lit, 13,
 ,[Offset], lamp 08, 14,
 ,[Offset], lamp 08 lit, 15,
 ,[Offset], lamp 09, 16,
 ,[Offset], lamp 09 lit, 17,
 ,[Offset], lamp 10, 18,
 ,[Offset], lamp 10 lit, 19,
 ,[Offset], lamp 11, 20,
 ,[Offset], lamp 11 lit, 21,
 ,[Offset], lamp 12, 22,
 ,[Offset], lamp 12 lit, 23,
 ,[Offset], lamp 13, 24,
 ,[Offset], lamp 13 lit, 25,
 ,[Offset], lamp 14, 26,
 ,[Offset], lamp 14 lit, 27,
 ,[Offset], lamp 15, 28,
 ,[Offset], lamp 15 lit, 29,
 ,[Offset], lamp 16, 30,
 ,[Offset], lamp 16 lit, 31,
 ,[Offset], lamp 17, 32,
 ,[Offset], lamp 17 lit, 33,
 ,[Offset], lamp 18, 34,
 ,[Offset], lamp 18 lit, 35,
 ,[Offset], lamp 19, 36,
 ,[Offset], lamp 19 lit, 37,
 ,[Offset], lamp 20, 38,
 ,[Offset], lamp 20 lit, 39,
 ,[Offset], lamp 21, 40,
 ,[Offset], lamp 21 lit, 41,
 ,[Offset], lamp 22, 42,
 ,[Offset], lamp 22 lit, 43,
 ,[Offset], lamp 23, 44,
 ,[Offset], lamp 23 lit, 45,
 ,[Offset], lamp 24, 46,
 ,[Offset], lamp 24 lit, 47,
 ,[Offset], lamp 25, 48,
 ,[Offset], lamp 25 lit, 49,
 ,[Offset], lamp 26, 50,
 ,[Offset], lamp 26 lit, 51,
 ,[Offset], lamp 27, 52,
 ,[Offset], lamp 27 lit, 53,
 ,[Offset], lamp 28, 54,
 ,[Offset], lamp 28 lit, 55,
 ,[Offset], lamp 29, 56,
 ,[Offset], lamp 29 lit, 57,
 ,[Offset], lamp 30, 58,
 ,[Offset], lamp 30 lit, 59,
 ,[Offset], lamp 31, 60,
 ,[Offset], lamp 31 lit, 61,
 ,[Offset], lamp 32, 62,
 ,[Offset], lamp 32 lit, 63,
 ,[Offset], lamp 33, 64,
 ,[Offset], lamp 33 lit, 65,
 ,[Offset], lamp 34, 66,
 ,[Offset], lamp 34 lit, 67,
 ,[Offset], lamp 35, 68,
 ,[Offset], lamp 35 lit, 69,
 ,[Offset], lamp 36, 70,
 ,[Offset], lamp 36 lit, 71,
 ,[Offset], lamp 37, 72,
 ,[Offset], lamp 37 lit, 73,
 ,[Offset], lamp 38, 74,
 ,[Offset], lamp 38 lit, 75,
 ,[Offset], lamp 39, 76,
 ,[Offset], lamp 39 lit, 77,
 ,[Offset], lamp 40, 78,
 ,[Offset], lamp 40 lit, 79,
 ,[Offset], lamp 41, 80,
 ,[Offset], lamp 41 lit, 81,
 ,[Offset], lamp 42, 82,
 ,[Offset], lamp 42 lit, 83,
 ,[Offset], lamp 43, 84,
 ,[Offset], lamp 43 lit, 85,
 ,[Offset], lamp 44, 86,
 ,[Offset], lamp 44 lit, 87,
 ,[Offset], ablank, 88,
 ,[Offset], a00, 89,
 ,[Offset], a01, 90,
 ,[Offset], a02, 91,
 ,[Offset], a03, 92,
 ,[Offset], a04, 93,
 ,[Offset], a05, 94,
 ,[Offset], a06, 95,
 ,[Offset], a07, 96,
 ,[Offset], a08, 97,
 ,[Offset], a09, 98,
 ,[Offset], bblank, 99,
 ,[Offset], b00, 100,
 ,[Offset], b01, 101,
 ,[Offset], b02, 102,
 ,[Offset], b03, 103,
 ,[Offset], b04, 104,
 ,[Offset], b05, 105,
 ,[Offset], b06, 106,
 ,[Offset], b07, 107,
 ,[Offset], b08, 108,
 ,[Offset], b09, 109,
 ,[Offset], cblank, 110,
 ,[Offset], c00, 111,
 ,[Offset], c01, 112,
 ,[Offset], c02, 113,
 ,[Offset], c03, 114,
 ,[Offset], c04, 115,
 ,[Offset], c05, 116,
 ,[Offset], c06, 117,
 ,[Offset], c07, 118,
 ,[Offset], c08, 119,
 ,[Offset], c09, 120,
 ,[Offset], dblank, 121,
 ,[Offset], d00, 122,
 ,[Offset], d01, 123,
 ,[Offset], d02, 124,
 ,[Offset], d03, 125,
 ,[Offset], d04, 126,
 ,[Offset], d05, 127,
 ,[Offset], d06, 128,
 ,[Offset], d07, 129,
 ,[Offset], d08, 130,
 ,[Offset], d09, 131,
 ,[Offset], eblank, 132,
 ,[Offset], e00, 133,
 ,[Offset], e01, 134,
 ,[Offset], e02, 135,
 ,[Offset], e03, 136,
 ,[Offset], e04, 137,
 ,[Offset], e05, 138,
 ,[Offset], e06, 139,
 ,[Offset], e07, 140,
 ,[Offset], e08, 141,
 ,[Offset], e09, 142,
 ,[Offset], fblank, 143,
 ,[Offset], f00, 144,
 ,[Offset], f01, 145,
 ,[Offset], f02, 146,
 ,[Offset], f03, 147,
 ,[Offset], f04, 148,
 ,[Offset], f05, 149,
 ,[Offset], f06, 150,
 ,[Offset], f07, 151,
 ,[Offset], f08, 152,
 ,[Offset], f09, 153,
RSID_TBLACK_HOLE_TEXTURES, 2104,,,
 ,[Offset], blank, 0,
 ,[Offset], BHplay_TOP, 1,
 ,[Offset], BHplay_BOTTOM, 2,
 ,[Offset], 1A, 3,
 ,[Offset], 1B, 4,
 ,[Offset], 1C, 5,
 ,[Offset], 1D, 6,
 ,[Offset], 2A, 7,
 ,[Offset], 2B, 8,
 ,[Offset], 2C, 9,
 ,[Offset], 2D, 10,
 ,[Offset], backglass BH, 11,
 ,[Offset], Ball Bumper Stop, 12,
 ,[Offset], BH_Floor_CnterBase, 13,
 ,[Offset], BH_frontplate, 14,
 ,[Offset], BH_Floor_lower, 15,
 ,[Offset], BH_playfieldBOTTOM, 16,
 ,[Offset], BH_playfieldTOP, 17,
 ,[Offset], metal_tex, 18,
 ,[Offset], Lowertable_R, 19,
 ,[Offset], Lowertable_L, 20,
 ,[Offset], Lowertable, 21,
 ,[Offset], G_tableSide_front, 22,
 ,[Offset], G_BackGlass, 23,
 ,[Offset], EL_BackGlassD, 24,
 ,[Offset], EL_BackGlassC, 25,
 ,[Offset], EL_BackGlassB, 26,
 ,[Offset], EL_BackGlassA, 27,
 ,[Offset], Ebackglass, 28,
 ,[Offset], CP_tableSide, 29,
 ,[Offset], BumperSideR, 30,
 ,[Offset], WoodTrim_white, 31,
 ,[Offset], TopEdgeBoard, 32,
 ,[Offset], TableFront_Side, 33,
 ,[Offset], red_base, 34,
 ,[Offset], rubber, 35,
 ,[Offset], RoundBumpers, 36,
 ,[Offset], PLASTIC_BAKE, 37,
 ,[Offset], missile, 38,
 ,[Offset], BlackHole, 39,
 ,[Offset], Bumper_up, 40,
 ,[Offset], metal, 41,
 ,[Offset], metal01, 42,
 ,[Offset], yellow_base, 43,
 ,[Offset], glass, 44,
 ,[Offset], white_t, 45,
 ,[Offset], b_base_t, 46,
 ,[Offset], wt_base_t, 47,
 ,[Offset], trans, 48,
 ,[Offset], ClearPlasticPost_01, 49,
 ,[Offset], Generic_Metal, 50,
 ,[Offset], PopBumper_Base, 51,
 ,[Offset], PopBumper_Bottom, 52,
 ,[Offset], rubberband_Temp, 53,
 ,[Offset], Plunger_Plate, 54,
 ,[Offset], Plunger, 55,
 ,[Offset], Extra_Metal_Parts, 56,
 ,[Offset], Metal_Parts, 57,
 ,[Offset], Silver Metal Screws_Temp, 58,
 ,[Offset], Generic_Screw, 59,
 ,[Offset], Apron, 60,
 ,[Offset], Flipper, 61,
 ,[Offset], metal_trim, 62,
 ,[Offset], metal_brace, 63,
 ,[Offset], metal_guard, 64,
 ,[Offset], Rails, 65,
 ,[Offset], target_yellow, 66,
 ,[Offset], tube, 67,
RSID_TBLACK_HOLE_MODELS, 2105,,,
 ,[Offset], floor, 0,
 ,[Offset], clip, 1,
 ,[Offset], backglass, 2,
 ,[Offset], flipper L, 3,
 ,[Offset], flipper L bottom, 4,
 ,[Offset], flipper R, 5,
 ,[Offset], flipper R bottom, 6,
 ,[Offset], gate oneway, 7,
 ,[Offset], generic A, 8,
 ,[Offset], generic B, 9,
 ,[Offset], gate, 10,
 ,[Offset], gate, 11,
 ,[Offset], main, 12,
 ,[Offset], metal, 13,
 ,[Offset], oneway gate B, 14,
 ,[Offset], platform 1, 15,
 ,[Offset], platform 2, 16,
 ,[Offset], plunger, 17,
 ,[Offset], pop bumper A, 18,
 ,[Offset], rubber, 19,
 ,[Offset], rubber 2, 20,
 ,[Offset], slingshot A, 21,
 ,[Offset], slingshot B, 22,
 ,[Offset], slingshot C, 23,
 ,[Offset], slingshot D, 24,
 ,[Offset], slingshot E, 25,
 ,[Offset], slingshot F, 26,
 ,[Offset], slingshot G, 27,
 ,[Offset], slingshot H, 28,
 ,[Offset], slingshot I, 29,
 ,[Offset], target A, 30,
 ,[Offset], target B, 31,
 ,[Offset], target C, 32,
 ,[Offset], target D, 33,
 ,[Offset], target E, 34,
 ,[Offset], target F, 35,
 ,[Offset], target G, 36,
 ,[Offset], target H, 37,
 ,[Offset], target I, 38,
 ,[Offset], target J, 39,
 ,[Offset], target K, 40,
 ,[Offset], target L, 41,
 ,[Offset], tile, 42,
 ,[Offset], trap A, 43,
 ,[Offset], trap B, 44,
 ,[Offset], trap C, 45,
 ,[Offset], trap D, 46,
 ,[Offset], wire A, 47,
 ,[Offset], wire D, 48,
 ,[Offset], trans, 49,
 ,[Offset], plasticposts, 50,
 ,[Offset], popbumpers, 51,
 ,[Offset], Apron, 52,
 ,[Offset], Light_Cutouts, 53,
 ,[Offset], floor_lower, 54,
 ,[Offset], Light_Cutouts_Lower, 55,
 ,[Offset], Plastic_White, 56,
 ,[Offset], Metal_arc, 57,
 ,[Offset], popbumpers_lower, 58,
 ,[Offset], main_lower, 59,
 ,[Offset], cabinet, 60,
 ,[Offset], Trans2, 61,
 ,[Offset], Slingshot C_Extended, 62,
 ,[Offset], Slingshot_Lower, 63,
 ,[Offset], Slingshot_Lower_Extended, 64,
 ,[Offset], Light_Cutouts_Bonus, 65,
RSID_TBLACK_HOLE_MODELS_LODS, 2106,,,
 ,[Offset], floor, 0,
 ,[Offset], clip, 1,
 ,[Offset], backglass, 2,
 ,[Offset], flipper L, 3,
 ,[Offset], flipper L bottom, 4,
 ,[Offset], flipper R, 5,
 ,[Offset], flipper R bottom, 6,
 ,[Offset], gate oneway, 7,
 ,[Offset], generic A, 8,
 ,[Offset], generic B, 9,
 ,[Offset], gate, 10,
 ,[Offset], gate, 11,
 ,[Offset], main, 12,
 ,[Offset], metal, 13,
 ,[Offset], oneway gate B, 14,
 ,[Offset], platform 1, 15,
 ,[Offset], platform 2, 16,
 ,[Offset], plunger, 17,
 ,[Offset], pop bumper A, 18,
 ,[Offset], rubber, 19,
 ,[Offset], rubber 2, 20,
 ,[Offset], slingshot A, 21,
 ,[Offset], slingshot B, 22,
 ,[Offset], slingshot C, 23,
 ,[Offset], slingshot D, 24,
 ,[Offset], slingshot E, 25,
 ,[Offset], slingshot F, 26,
 ,[Offset], slingshot G, 27,
 ,[Offset], slingshot H, 28,
 ,[Offset], slingshot I, 29,
 ,[Offset], target A, 30,
 ,[Offset], target B, 31,
 ,[Offset], target C, 32,
 ,[Offset], target D, 33,
 ,[Offset], target E, 34,
 ,[Offset], target F, 35,
 ,[Offset], target G, 36,
 ,[Offset], target H, 37,
 ,[Offset], target I, 38,
 ,[Offset], target J, 39,
 ,[Offset], target K, 40,
 ,[Offset], target L, 41,
 ,[Offset], tile, 42,
 ,[Offset], trap A, 43,
 ,[Offset], trap B, 44,
 ,[Offset], trap C, 45,
 ,[Offset], trap D, 46,
 ,[Offset], wire A, 47,
 ,[Offset], wire D, 48,
 ,[Offset], trans, 49,
 ,[Offset], plasticposts, 50,
 ,[Offset], popbumpers, 51,
 ,[Offset], Apron, 52,
 ,[Offset], Light_Cutouts, 53,
 ,[Offset], floor_lower, 54,
 ,[Offset], Light_Cutouts_Lower, 55,
 ,[Offset], Plastic_White, 56,
 ,[Offset], Metal_arc, 57,
 ,[Offset], popbumpers_lower, 58,
 ,[Offset], main_lower, 59,
 ,[Offset], cabinet, 60,
 ,[Offset], Trans2, 61,
 ,[Offset], Slingshot C_Extended, 62,
 ,[Offset], Slingshot_Lower, 63,
 ,[Offset], Slingshot_Lower_Extended, 64,
 ,[Offset], Light_Cutouts_Bonus, 65,
RSID_TBLACK_HOLE_COLLISIONS, 2107,,,
 ,[Offset], arc col, 0,
 ,[Offset], flipper L F col, 1,
 ,[Offset], flipper L B col, 2,
 ,[Offset], flipper R F col, 3,
 ,[Offset], flipper R B col, 4,
 ,[Offset], floor col bottom, 5,
 ,[Offset], floor col, 6,
 ,[Offset], gate col, 7,
 ,[Offset], gate oneway col, 8,
 ,[Offset], generic A col, 9,
 ,[Offset], generic B col, 10,
 ,[Offset], generic H col, 11,
 ,[Offset], generic I col, 12,
 ,[Offset], helix col, 13,
 ,[Offset], platform 1, 14,
 ,[Offset], platform 2, 15,
 ,[Offset], plunger area col, 16,
 ,[Offset], plunger col, 17,
 ,[Offset], rubber col, 18,
 ,[Offset], slingshot A col, 19,
 ,[Offset], slingshot B col, 20,
 ,[Offset], slingshot C col, 21,
 ,[Offset], slingshot D col, 22,
 ,[Offset], slingshot E col, 23,
 ,[Offset], slingshot F col, 24,
 ,[Offset], slingshot G col, 25,
 ,[Offset], slingshot H col, 26,
 ,[Offset], slingshot I col, 27,
 ,[Offset], trap A, 28,
 ,[Offset], trap B, 29,
 ,[Offset], trap C, 30,
 ,[Offset], trap D, 31,
 ,[Offset], wall col, 32,
 ,[Offset], pop bumper A col, 33,
 ,[Offset], target A col, 34,
 ,[Offset], target G col, 35,
 ,[Offset], target K col, 36,
 ,[Offset], target L col, 37,
 ,[Offset], target J col, 38,
 ,[Offset], slingshot J col, 39,
RSID_TBLACK_HOLE_PLACEMENT, 2108,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_TBLACK_HOLE_SOUNDS_START, 2109,,,
RSID_TBLACK_HOLE_SOUND_NEW_BALL, 2110,,,
RSID_TBLACK_HOLE_SOUND_TABLE_RESET, 2111,,,
RSID_TBLACK_HOLE_SOUND_INSERT_COIN, 2112,,,
RSID_TBLACK_HOLE_SOUND_TABLENUDGE, 2113,,,
RSID_TBLACK_HOLE_SOUND_TABLENUDGE2, 2114,,,
RSID_TBLACK_HOLE_SOUND_TABLENUDGE3, 2115,,,
RSID_TBLACK_HOLE_SOUND_TABLENUDGE4, 2116,,,
RSID_TBLACK_HOLE_SOUND_TABLENUDGE5, 2117,,,
RSID_TBLACK_HOLE_SOUND_PLUNGER_NO_BALL, 2118,,,
RSID_TBLACK_HOLE_SOUND_PLUNGER_AND_BALL, 2119,,,
RSID_TBLACK_HOLE_SOUND_BALL_AFTER_PLUNGER, 2120,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_UP, 2121,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_UP2, 2122,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_UP3, 2123,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_DOWN, 2124,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_DOWN2, 2125,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_DOWN3, 2126,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_BUZZ, 2127,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_BALLHIT, 2128,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_BALLHIT2, 2129,,,
RSID_TBLACK_HOLE_SOUND_FLIPPER_BALLHIT3, 2130,,,
RSID_TBLACK_HOLE_SOUND_BALLROLL, 2131,,,
RSID_TBLACK_HOLE_SOUND_WALL, 2132,,,
RSID_TBLACK_HOLE_SOUND_ALTWALL, 2133,,,
RSID_TBLACK_HOLE_SOUND_METALWALL, 2134,,,
RSID_TBLACK_HOLE_SOUND_ALTCOLL3, 2135,,,
RSID_TBLACK_HOLE_SOUND_TENS, 2136,,,
RSID_TBLACK_HOLE_SOUND_HUNDREDS, 2137,,,
RSID_TBLACK_HOLE_SOUND_THOUSANDS, 2138,,,
RSID_TBLACK_HOLE_SOUND_TEN_THOUSANDS, 2139,,,
RSID_TBLACK_HOLE_SOUND_100_THOUSANDS, 2140,,,
RSID_TBLACK_HOLE_SOUND_MILLIONS, 2141,,,
RSID_TBLACK_HOLE_SOUND_TEN_MILLIONS, 2142,,,
RSID_TBLACK_HOLE_SOUND_100_MILLIONS, 2143,,,
RSID_TBLACK_HOLE_SOUND_POPBUMPER, 2144,,,
RSID_TBLACK_HOLE_SOUND_METALGATE, 2145,,,
RSID_TBLACK_HOLE_SOUND_CAPTURED, 2146,,,
RSID_TBLACK_HOLE_SOUND_CAPTIVEHOLE, 2147,,,
RSID_TBLACK_HOLE_SOUND_DROPTARGET, 2148,,,
RSID_TBLACK_HOLE_SOUND_GFORCE, 2149,,,
RSID_TBLACK_HOLE_SOUND_REENTRY_SUCCESS, 2150,,,
RSID_TBLACK_HOLE_SOUND_REENTRY_FAILED, 2151,,,
RSID_TBLACK_HOLE_SOUND_EFFECT1, 2152,,,
RSID_TBLACK_HOLE_SOUND_EFFECT11, 2153,,,
RSID_TBLACK_HOLE_SOUND_WIRE_SIDES, 2154,,,
RSID_TBLACK_HOLE_SOUND_WIRE_MID, 2155,,,
RSID_TBLACK_HOLE_SOUND_SPOTTARGET1, 2156,,,
RSID_TBLACK_HOLE_SOUND_EFFECT2, 2157,,,
RSID_TBLACK_HOLE_SOUND_LOWER_ENTRY, 2158,,,
RSID_TBLACK_HOLE_SOUND_EFFECT4, 2159,,,
RSID_TBLACK_HOLE_SOUND_EFFECT7, 2160,,,
RSID_TBLACK_HOLE_SOUND_EFFECT6, 2161,,,
RSID_TBLACK_HOLE_SOUND_EFFECT9, 2162,,,
RSID_TBLACK_HOLE_SOUND_SPINNER, 2163,,,
RSID_TBLACK_HOLE_SOUND_BONUS, 2164,,,
RSID_TBLACK_HOLE_SOUND_EXTRABALL, 2165,,,
RSID_TBLACK_HOLE_SOUND_MAINMUSIC, 2166,,,
RSID_TBLACK_HOLE_SOUND_LOWERMUSIC, 2167,,,
RSID_TBLACK_HOLE_SOUND_EFFECT13, 2168,,,
RSID_TBLACK_HOLE_SOUND_NO_ESCAPE, 2169,,,
RSID_TBLACK_HOLE_SOUND_EFFECT12, 2170,,,
RSID_TBLACK_HOLE_SOUND_RESET_DROP_TARGETS, 2171,,,
RSID_TBLACK_HOLE_SOUNDS_END, 2172,,,
RSID_TBLACK_HOLE_HUD, 2173,,,
RSID_TBLACK_HOLE_HUD_HD, 2174,,,
RSID_TBLACK_HOLE_VERSION, 2175,,,
RSID_TBLACK_HOLE_END, 2176,,,
