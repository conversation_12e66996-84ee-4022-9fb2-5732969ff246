RSID_TELVIRAPARTYMONSTERS_START, 3100,,,
 ,[Offset], <PERSON><PERSON><PERSON>_FLYER, 0,
 ,[Offset], ElviraPartyMonsters\PBTElviraPartyMonsters, 1,
 ,[Offset], ElviraPartyMonsters\InstructionsENG, 2,
 ,[Offset], ElviraPartyMonsters\InstructionsFR, 3,
 ,[Offset], ElviraPartyMonsters\InstructionsITAL, 4,
 ,[Offset], ElviraPartyMonsters\InstructionsGERM, 5,
 ,[Offset], ElviraPartyMonsters\InstructionsSPAN, 6,
 ,[Offset], ElviraPartyMonsters\InstructionsPORT, 7,
 ,[Offset], ElviraPartyMonsters\InstructionsDUTCH, 8,
 ,[Offset], tables\Elvira_BG_scroll, 9,
RSID_TELVIRAPARTYMONSTERS_LIGHTS, 3101,,,
RSID_TELVIRAPARTYMONSTERS_CAMERAS, 3102,,,
RSID_TELVIRAPARTYMONSTERS_LAMP_TEXTURES, 3103,,,
 ,[Offset], L_01_Off, 0,
 ,[Offset], L_01_On, 1,
 ,[Offset], L_02_Off, 2,
 ,[Offset], L_02_On, 3,
 ,[Offset], L_03_Off, 4,
 ,[Offset], L_03_On, 5,
 ,[Offset], L_04_Off, 6,
 ,[Offset], L_04_On, 7,
 ,[Offset], L_05_Off, 8,
 ,[Offset], L_05_On, 9,
 ,[Offset], L_06_Off, 10,
 ,[Offset], L_06_On, 11,
 ,[Offset], L_07_Off, 12,
 ,[Offset], L_07_On, 13,
 ,[Offset], L_08_Off, 14,
 ,[Offset], L_08_On, 15,
 ,[Offset], L_09_Off, 16,
 ,[Offset], L_09_On, 17,
 ,[Offset], L_10_Off, 18,
 ,[Offset], L_10_On, 19,
 ,[Offset], L_11_Off, 20,
 ,[Offset], L_11_On, 21,
 ,[Offset], L_12_Off, 22,
 ,[Offset], L_12_On, 23,
 ,[Offset], L_13_Off, 24,
 ,[Offset], L_13_On, 25,
 ,[Offset], L_14_Off, 26,
 ,[Offset], L_14_On, 27,
 ,[Offset], L_15_Off, 28,
 ,[Offset], L_15_On, 29,
 ,[Offset], L_16_Off, 30,
 ,[Offset], L_16_On, 31,
 ,[Offset], L_17_Off, 32,
 ,[Offset], L_17_On, 33,
 ,[Offset], L_18_Off, 34,
 ,[Offset], L_18_On, 35,
 ,[Offset], L_19_Off, 36,
 ,[Offset], L_19_On, 37,
 ,[Offset], L_20_Off, 38,
 ,[Offset], L_20_On, 39,
 ,[Offset], L_21_Off, 40,
 ,[Offset], L_21_On, 41,
 ,[Offset], L_22_Off, 42,
 ,[Offset], L_22_On, 43,
 ,[Offset], L_23_Off, 44,
 ,[Offset], L_23_On, 45,
 ,[Offset], L_24_Off, 46,
 ,[Offset], L_24_On, 47,
 ,[Offset], L_25_Off, 48,
 ,[Offset], L_25_On, 49,
 ,[Offset], L_26_Off, 50,
 ,[Offset], L_26_On, 51,
 ,[Offset], L_27_Off, 52,
 ,[Offset], L_27_On, 53,
 ,[Offset], L_28_Off, 54,
 ,[Offset], L_28_On, 55,
 ,[Offset], L_29_Off, 56,
 ,[Offset], L_29_On, 57,
 ,[Offset], L_30_Off, 58,
 ,[Offset], L_30_On, 59,
 ,[Offset], L_31_Off, 60,
 ,[Offset], L_31_On, 61,
 ,[Offset], L_32_Off, 62,
 ,[Offset], L_32_On, 63,
 ,[Offset], L_33_Off, 64,
 ,[Offset], L_33_On, 65,
 ,[Offset], L_34_Off, 66,
 ,[Offset], L_34_On, 67,
 ,[Offset], L_35_Off, 68,
 ,[Offset], L_35_On, 69,
 ,[Offset], L_36_Off, 70,
 ,[Offset], L_36_On, 71,
 ,[Offset], L_37_Off, 72,
 ,[Offset], L_37_On, 73,
 ,[Offset], L_38_Off, 74,
 ,[Offset], L_38_On, 75,
 ,[Offset], L_39_Off, 76,
 ,[Offset], L_39_On, 77,
 ,[Offset], L_40_Off, 78,
 ,[Offset], L_40_On, 79,
 ,[Offset], L_41_Off, 80,
 ,[Offset], L_41_On, 81,
 ,[Offset], L_42_Off, 82,
 ,[Offset], L_42_On, 83,
 ,[Offset], L_43_Off, 84,
 ,[Offset], L_43_On, 85,
 ,[Offset], L_44_Off, 86,
 ,[Offset], L_44_On, 87,
 ,[Offset], L_45_Off, 88,
 ,[Offset], L_45_On, 89,
 ,[Offset], L_46_Off, 90,
 ,[Offset], L_46_On, 91,
 ,[Offset], L_47_Off, 92,
 ,[Offset], L_47_On, 93,
 ,[Offset], L_48_Off, 94,
 ,[Offset], L_48_On, 95,
 ,[Offset], L_49_Off, 96,
 ,[Offset], L_49_On, 97,
 ,[Offset], L_50_Off, 98,
 ,[Offset], L_50_On, 99,
 ,[Offset], L_51_Off, 100,
 ,[Offset], L_51_On, 101,
 ,[Offset], L_52_Off, 102,
 ,[Offset], L_52_On, 103,
 ,[Offset], L_53_Off, 104,
 ,[Offset], L_53_On, 105,
 ,[Offset], L_54_Off, 106,
 ,[Offset], L_54_On, 107,
 ,[Offset], L_55_Off, 108,
 ,[Offset], L_55_On, 109,
 ,[Offset], L_56_Off, 110,
 ,[Offset], L_56_On, 111,
 ,[Offset], L_57_Off, 112,
 ,[Offset], L_57_On, 113,
 ,[Offset], L_58_Off, 114,
 ,[Offset], L_58_On, 115,
 ,[Offset], L_59_Off, 116,
 ,[Offset], L_59_On, 117,
 ,[Offset], F_01C_Off, 118,
 ,[Offset], F_01C_On, 119,
 ,[Offset], F_02C_Off, 120,
 ,[Offset], F_02C_On, 121,
 ,[Offset], F_03C_Off, 122,
 ,[Offset], F_03C_On, 123,
 ,[Offset], F_03C_B_Off, 124,
 ,[Offset], F_03C_B_On, 125,
 ,[Offset], F_04C_Off, 126,
 ,[Offset], F_04C_On, 127,
 ,[Offset], F_04C_B_Off, 128,
 ,[Offset], F_04C_B_On, 129,
 ,[Offset], F_05C_On, 130,
 ,[Offset], F_06C_Off, 131,
 ,[Offset], F_06C_On, 132,
 ,[Offset], F_06C_B_Off, 133,
 ,[Offset], F_06C_B_On, 134,
 ,[Offset], F_07C_Off, 135,
 ,[Offset], F_07C_On, 136,
 ,[Offset], F_07C_B_Off, 137,
 ,[Offset], F_07C_B_On, 138,
 ,[Offset], F_13_Off, 139,
 ,[Offset], F_13_On, 140,
 ,[Offset], F_15_Off, 141,
 ,[Offset], F_15_On, 142,
 ,[Offset], L_11B_Off, 143,
 ,[Offset], L_11B_On, 144,
 ,[Offset], L_20B_Off, 145,
 ,[Offset], L_20B_On, 146,
 ,[Offset], Elvira_Skull_Off, 147,
 ,[Offset], Elvira_Skull_On, 148,
 ,[Offset], F_16_off, 149,
 ,[Offset], F_16_On, 150,
 ,[Offset], L_25_B_Off, 151,
 ,[Offset], L_25_B_On, 152,
RSID_TELVIRAPARTYMONSTERS_TEXTURES, 3104,,,
 ,[Offset], Elvira_LED_Border, 0,
 ,[Offset], backboard, 1,
 ,[Offset], BBQ_PlasticTop, 2,
 ,[Offset], BBQ_Stickers, 3,
 ,[Offset], cabinet, 4,
 ,[Offset], clear, 5,
 ,[Offset], Elvira_BBQ, 6,
 ,[Offset], Flipper, 7,
 ,[Offset], Habit_2, 8,
 ,[Offset], Habit1, 9,
 ,[Offset], Red_Cap, 10,
 ,[Offset], top_cabinet, 11,
 ,[Offset], Coin_Slot, 12,
 ,[Offset], Extra_Metal_Parts, 13,
 ,[Offset], Metal_Parts, 14,
 ,[Offset], Flipper_Button, 15,
 ,[Offset], Plunger, 16,
 ,[Offset], Generic_Screw, 17,
 ,[Offset], Generic_Metal, 18,
 ,[Offset], rubberband_temp, 19,
 ,[Offset], silver metal screws_temp, 20,
 ,[Offset], Rails, 21,
 ,[Offset], Red_Plastic_Post, 22,
 ,[Offset], ClearPlasticPost_01, 23,
 ,[Offset], PopBumperBody, 24,
 ,[Offset], Rubber Post_Temp, 25,
 ,[Offset], Rubber_Band_Black, 26,
 ,[Offset], bulb1, 27,
 ,[Offset], Bumper_Hamer, 28,
 ,[Offset], Bumper_Sensors, 29,
 ,[Offset], backglass, 30,
 ,[Offset], 3_deadheads, 31,
 ,[Offset], blob_Frank_bat, 32,
 ,[Offset], boogie_men, 33,
 ,[Offset], Grave_Target, 34,
 ,[Offset], green_monster, 35,
 ,[Offset], j-a-m_organ_player, 36,
 ,[Offset], red_monster, 37,
 ,[Offset], Apron, 38,
 ,[Offset], Metal_Walls, 39,
 ,[Offset], Playfield_Upper, 40,
 ,[Offset], Playfield_Lower, 41,
 ,[Offset], ClearPlasticPost_02, 42,
 ,[Offset], Harley_Gate, 43,
 ,[Offset], Harley_Spinner, 44,
 ,[Offset], Drop_Target_A, 45,
 ,[Offset], Drop_Target_B, 46,
 ,[Offset], Drop_Target_C, 47,
 ,[Offset], Grave_Target_B, 48,
 ,[Offset], Grave_Target_C, 49,
 ,[Offset], Grave_Target_D, 50,
 ,[Offset], Grave_Target_E, 51,
 ,[Offset], Plastic_Ramp_02, 52,
 ,[Offset], Ramp1_Fix, 53,
 ,[Offset], Ramp_4Stickers, 54,
 ,[Offset], outlanesandclear, 55,
RSID_TELVIRAPARTYMONSTERS_MODELS, 3105,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Box, 1,
 ,[Offset], BBQ_Plastic, 2,
 ,[Offset], Cabinet, 3,
 ,[Offset], Cabinet_Backglass, 4,
 ,[Offset], Cabinet_Metals, 5,
 ,[Offset], Habit_Left, 6,
 ,[Offset], Habit_Right, 7,
 ,[Offset], Metal_Pieces, 8,
 ,[Offset], Metal_Walls, 9,
 ,[Offset], Plastic_Pieces, 10,
 ,[Offset], Plastic_Posts, 11,
 ,[Offset], Playfield, 12,
 ,[Offset], Pop_Bumpers, 13,
 ,[Offset], Ramp_Back_Right, 14,
 ,[Offset], Ramp_Back_Left, 15,
 ,[Offset], Ramp_Curvy, 16,
 ,[Offset], Rubber_Pieces, 17,
 ,[Offset], Skull_Rock, 18,
 ,[Offset], Wooden_Rails, 19,
 ,[Offset], Bulbs, 20,
 ,[Offset], Flipper, 21,
 ,[Offset], Bumper_Metal_A, 22,
 ,[Offset], Slingshot_Left, 23,
 ,[Offset], Slingshot_Right, 24,
 ,[Offset], Target_A, 25,
 ,[Offset], Wire, 26,
 ,[Offset], Plunger, 27,
 ,[Offset], BBQ_Target_Right, 28,
 ,[Offset], BBQ_Target_Left, 29,
 ,[Offset], Gate_A, 30,
 ,[Offset], Gate_B, 31,
 ,[Offset], Light_Cutouts, 32,
 ,[Offset], Target_C, 33,
 ,[Offset], Target_D, 34,
 ,[Offset], Target_E, 35,
 ,[Offset], Target_F, 36,
 ,[Offset], Target_G, 37,
 ,[Offset], Target_H, 38,
 ,[Offset], Target_I, 39,
 ,[Offset], Monster_Left, 40,
 ,[Offset], Monster_Right, 41,
 ,[Offset], Bumper_Metal_B, 42,
 ,[Offset], Bumper_Metal_C, 43,
 ,[Offset], Plastic_Pieces_Alpha_NoTriangle, 44,
 ,[Offset], DeadHead_Cutouts, 45,
 ,[Offset], Ramp_Stickers, 46,
 ,[Offset], Target_J, 47,
 ,[Offset], Slingshot_Left_Extended, 48,
 ,[Offset], Slingshot_Right_Extended, 49,
 ,[Offset], Cabinet_Backglass_HUD, 50,
RSID_TELVIRAPARTYMONSTERS_MODELS_LODS, 3106,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Box, 1,
 ,[Offset], BBQ_Plastic, 2,
 ,[Offset], Cabinet, 3,
 ,[Offset], Cabinet_Backglass, 4,
 ,[Offset], Cabinet_Metals, 5,
 ,[Offset], Habit_Left, 6,
 ,[Offset], Habit_Right, 7,
 ,[Offset], Metal_Pieces, 8,
 ,[Offset], Metal_Walls, 9,
 ,[Offset], Plastic_Pieces, 10,
 ,[Offset], Plastic_Posts, 11,
 ,[Offset], Playfield, 12,
 ,[Offset], Pop_Bumpers, 13,
 ,[Offset], Ramp_Back_Right, 14,
 ,[Offset], Ramp_Back_Left, 15,
 ,[Offset], Ramp_Curvy, 16,
 ,[Offset], Rubber_Pieces, 17,
 ,[Offset], Skull_Rock, 18,
 ,[Offset], Wooden_Rails, 19,
 ,[Offset], Bulbs, 20,
 ,[Offset], Flipper, 21,
 ,[Offset], Bumper_Metal_A, 22,
 ,[Offset], Slingshot_Left, 23,
 ,[Offset], Slingshot_Right, 24,
 ,[Offset], Target_A, 25,
 ,[Offset], Wire, 26,
 ,[Offset], Plunger, 27,
 ,[Offset], BBQ_Target_Right, 28,
 ,[Offset], BBQ_Target_Left, 29,
 ,[Offset], Gate_A, 30,
 ,[Offset], Gate_B, 31,
 ,[Offset], Light_Cutouts, 32,
 ,[Offset], Target_C, 33,
 ,[Offset], Target_D, 34,
 ,[Offset], Target_E, 35,
 ,[Offset], Target_F, 36,
 ,[Offset], Target_G, 37,
 ,[Offset], Target_H, 38,
 ,[Offset], Target_I, 39,
 ,[Offset], Monster_Left, 40,
 ,[Offset], Monster_Right, 41,
 ,[Offset], Bumper_Metal_B, 42,
 ,[Offset], Bumper_Metal_C, 43,
 ,[Offset], Plastic_Pieces_Alpha_NoTriangle, 44,
 ,[Offset], DeadHead_Cutouts, 45,
 ,[Offset], Ramp_Stickers, 46,
 ,[Offset], Target_J, 47,
 ,[Offset], Slingshot_Left_Extended, 48,
 ,[Offset], Slingshot_Right_Extended, 49,
 ,[Offset], Cabinet_Backglass_HUD, 50,
RSID_TELVIRAPARTYMONSTERS_COLLISION, 3107,,,
 ,[Offset], Ball_Drain, 0,
 ,[Offset], Flipper_lane_Left, 1,
 ,[Offset], Flipper_lane_Right, 2,
 ,[Offset], Flipper_Left_Front, 3,
 ,[Offset], Flipper_Left_Back, 4,
 ,[Offset], Flipper_Right_Front, 5,
 ,[Offset], Flipper_Right_Back, 6,
 ,[Offset], Habit_Left, 7,
 ,[Offset], Habit_Right, 8,
 ,[Offset], Lower_Wall, 9,
 ,[Offset], Metal_Walls, 10,
 ,[Offset], Playfield, 11,
 ,[Offset], Plunger_Rest, 12,
 ,[Offset], Ramp_Back_Left, 13,
 ,[Offset], Ramp_Back_Right, 14,
 ,[Offset], Ramp_Curvy, 15,
 ,[Offset], Rubber, 16,
 ,[Offset], Slingshot_Left, 17,
 ,[Offset], Slingshot_Right, 18,
 ,[Offset], PopBumper, 19,
 ,[Offset], Slingshot_Left_Front, 20,
 ,[Offset], Slingshot_Right_Front, 21,
 ,[Offset], Target, 22,
 ,[Offset], Plunger_Moving, 23,
 ,[Offset], Habit_Trap, 24,
 ,[Offset], JAM_Trap, 25,
 ,[Offset], Gate, 26,
 ,[Offset], Kicker_Trap, 27,
 ,[Offset], Target_J, 28,
 ,[Offset], Habit_Wall, 29,
RSID_TELVIRAPARTYMONSTERS_PLACEMENT, 3108,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TELVIRAPARTYMONSTERS_EMUROM, 3109,,,
 ,[Offset], eatpm_l4_u26, 0,
 ,[Offset], eatpm_l4_u27, 1,
 ,[Offset], eatpm_l4, 2,
 ,[Offset], eatpm_l4, 3,
RSID_TELVIRAPARTYMONSTERS_SOUNDS_START, 3110,,,
RSID_TELVIRAPARTYMONSTERS_EMU_SOUNDS, 3111,,,
 ,[Offset], S0001-LP1, 0,
 ,[Offset], S0001-LP2, 1,
 ,[Offset], S0002-LP1, 2,
 ,[Offset], S0002-LP2, 3,
 ,[Offset], S0003-LP, 4,
 ,[Offset], S0004-LP1, 5,
 ,[Offset], S0004-LP2, 6,
 ,[Offset], S0005-LP, 7,
 ,[Offset], S0006-LP1, 8,
 ,[Offset], S0006-LP2, 9,
 ,[Offset], S0007_C3, 10,
 ,[Offset], S0008-LP1, 11,
 ,[Offset], S0008-LP2, 12,
 ,[Offset], S0009-LP1, 13,
 ,[Offset], S0009-LP2, 14,
 ,[Offset], S000A-LP, 15,
 ,[Offset], S000B-LP1, 16,
 ,[Offset], S000C-LP1, 17,
 ,[Offset], S000D-LP1, 18,
 ,[Offset], S000E-LP1, 19,
 ,[Offset], S000F-LP1, 20,
 ,[Offset], S0010-LP1, 21,
 ,[Offset], S0012-LP1, 22,
 ,[Offset], S0013-LP1, 23,
 ,[Offset], S0014-LP1, 24,
 ,[Offset], S0015-LP1, 25,
 ,[Offset], S0016-LP1, 26,
 ,[Offset], S0016-LP2, 27,
 ,[Offset], S0017_C3, 28,
 ,[Offset], S0018-LP1, 29,
 ,[Offset], S0018-LP2, 30,
 ,[Offset], S0019-LP1, 31,
 ,[Offset], S0019-LP2, 32,
 ,[Offset], S001A_C3, 33,
 ,[Offset], S001B-LP1, 34,
 ,[Offset], S001B-LP2, 35,
 ,[Offset], S0030_C6, 36,
 ,[Offset], S0031_C6, 37,
 ,[Offset], S0032_C6, 38,
 ,[Offset], S0033_C6, 39,
 ,[Offset], S0034_C6, 40,
 ,[Offset], S0035_C6, 41,
 ,[Offset], S0036_C6, 42,
 ,[Offset], S0037_C6, 43,
 ,[Offset], S0038_C6, 44,
 ,[Offset], S003A_C6, 45,
 ,[Offset], S003B_C6, 46,
 ,[Offset], S003C_C6, 47,
 ,[Offset], S0082_C4, 48,
 ,[Offset], S0083_C4, 49,
 ,[Offset], S0084_C4, 50,
 ,[Offset], S0085_C4, 51,
 ,[Offset], S0087_C4, 52,
 ,[Offset], S0088_C4, 53,
 ,[Offset], S0089_C4, 54,
 ,[Offset], S008A_C4, 55,
 ,[Offset], S008B_C4, 56,
 ,[Offset], S008C_C4, 57,
 ,[Offset], S008D_C4, 58,
 ,[Offset], S008E_C4, 59,
 ,[Offset], S008F_C4, 60,
 ,[Offset], S0090_C4, 61,
 ,[Offset], S0091_C4, 62,
 ,[Offset], S0092_C4, 63,
 ,[Offset], S0093_C4, 64,
 ,[Offset], S0094_C4, 65,
 ,[Offset], S0095_C4, 66,
 ,[Offset], S0096_C4, 67,
 ,[Offset], S0097_C4, 68,
 ,[Offset], S0098_C4, 69,
 ,[Offset], S0099_C4, 70,
 ,[Offset], S009A_C4, 71,
 ,[Offset], S009B_C4, 72,
 ,[Offset], S009C_C4, 73,
 ,[Offset], S009D_C4, 74,
 ,[Offset], S009E_C4, 75,
 ,[Offset], S009F_C4, 76,
 ,[Offset], S00A0_C4, 77,
 ,[Offset], S00A1_C4, 78,
 ,[Offset], S00A2_C4, 79,
 ,[Offset], S00A3_C4, 80,
 ,[Offset], S00A4_C4, 81,
 ,[Offset], S00A5_C4, 82,
 ,[Offset], S00A6-LP_C4, 83,
 ,[Offset], S00A7_C4, 84,
 ,[Offset], S00A8-LP_C4, 85,
 ,[Offset], S00A9_C4, 86,
 ,[Offset], S00AA_C4, 87,
 ,[Offset], S00AB_C4, 88,
 ,[Offset], S00AC_C4, 89,
 ,[Offset], S00AD_C4, 90,
 ,[Offset], S00AE_C4, 91,
 ,[Offset], S00AF_C4, 92,
 ,[Offset], S00B0_C4, 93,
 ,[Offset], S00B1_C4, 94,
 ,[Offset], S00B2_C4, 95,
 ,[Offset], S00B3_C4, 96,
 ,[Offset], S00B5_C4, 97,
 ,[Offset], S00B6_C4, 98,
 ,[Offset], S00B7_C4, 99,
 ,[Offset], S00B8_C4, 100,
 ,[Offset], S00B9_C4, 101,
 ,[Offset], S00BA_C4, 102,
 ,[Offset], S00BB_C4, 103,
 ,[Offset], S00BC_C4, 104,
 ,[Offset], S00BD_C4, 105,
 ,[Offset], S00BE_C4, 106,
 ,[Offset], S00BF_C4, 107,
 ,[Offset], S00C0_C4, 108,
 ,[Offset], S00C1_C4, 109,
 ,[Offset], S00C2_C4, 110,
 ,[Offset], S00C3_C4, 111,
 ,[Offset], S00C4_C4, 112,
 ,[Offset], S00C5_C4, 113,
 ,[Offset], S00C6_C4, 114,
 ,[Offset], S00C7_C4, 115,
 ,[Offset], S00C8_C4, 116,
 ,[Offset], S00C9_C4, 117,
 ,[Offset], S00CA_C4, 118,
 ,[Offset], S00CB_C4, 119,
 ,[Offset], S00CC_C4, 120,
 ,[Offset], S00CD_C4, 121,
 ,[Offset], S00CE_C4, 122,
 ,[Offset], S00CF_C4, 123,
 ,[Offset], S00D0_C4, 124,
 ,[Offset], S00D1_C4, 125,
 ,[Offset], S00D2_C4, 126,
 ,[Offset], S00D3_C4, 127,
 ,[Offset], S00D4_C4, 128,
 ,[Offset], S00D5_C4, 129,
 ,[Offset], S00D6_C4, 130,
 ,[Offset], S0101_C2, 131,
 ,[Offset], S0102_C2, 132,
 ,[Offset], S0103_C2, 133,
 ,[Offset], S0104_C2, 134,
 ,[Offset], S0105_C2, 135,
 ,[Offset], S0106_C2, 136,
 ,[Offset], S0107_C2, 137,
 ,[Offset], S0108_C2, 138,
 ,[Offset], S0109_C2, 139,
 ,[Offset], S010A_C2, 140,
 ,[Offset], S010B_C2, 141,
 ,[Offset], S010C_C2, 142,
 ,[Offset], S010D_C2, 143,
 ,[Offset], S010E_C2, 144,
 ,[Offset], S010F_C2, 145,
 ,[Offset], S0110_C2, 146,
 ,[Offset], S0111_C2, 147,
 ,[Offset], S0112_C2, 148,
 ,[Offset], S0113_C2, 149,
 ,[Offset], S0114_C2, 150,
 ,[Offset], S0115_C2, 151,
 ,[Offset], S0116_C2, 152,
 ,[Offset], S0118_C2, 153,
 ,[Offset], S0119_C2, 154,
 ,[Offset], S011A_C2, 155,
 ,[Offset], S011B_C2, 156,
 ,[Offset], S011C_C2, 157,
 ,[Offset], S011D_C2, 158,
 ,[Offset], S011E_C2, 159,
 ,[Offset], S0203_C3, 160,
 ,[Offset], S0282_C4, 161,
 ,[Offset], S0283_C4, 162,
 ,[Offset], S0284_C4, 163,
 ,[Offset], S0287_C4, 164,
 ,[Offset], S0288_C4, 165,
 ,[Offset], S0289_C4, 166,
 ,[Offset], S028A_C4, 167,
 ,[Offset], S028B_C4, 168,
 ,[Offset], S028C_C4, 169,
 ,[Offset], S028D_C4, 170,
 ,[Offset], S0290_C4, 171,
 ,[Offset], S0291_C4, 172,
 ,[Offset], S0292_C4, 173,
 ,[Offset], S0293_C4, 174,
 ,[Offset], S0294_C4, 175,
 ,[Offset], S0295_C4, 176,
 ,[Offset], S0296_C4, 177,
 ,[Offset], S0298_C4, 178,
 ,[Offset], S0299_C4, 179,
 ,[Offset], S029A_C4, 180,
 ,[Offset], S029B_C4, 181,
 ,[Offset], S029C_C4, 182,
 ,[Offset], S029D_C4, 183,
 ,[Offset], S029E_C4, 184,
 ,[Offset], S02A1_C4, 185,
 ,[Offset], S02A2_C4, 186,
 ,[Offset], S02AA_C4, 187,
 ,[Offset], S02B7_C4, 188,
 ,[Offset], S02BD_C4, 189,
 ,[Offset], S02BE_C4, 190,
 ,[Offset], S02C0_C4, 191,
 ,[Offset], S02C1_C4, 192,
 ,[Offset], S02C2_C4, 193,
 ,[Offset], S02C3_C4, 194,
 ,[Offset], S02C4_C4, 195,
 ,[Offset], S02C5_C4, 196,
 ,[Offset], S02CA_C4, 197,
 ,[Offset], S02CB_C4, 198,
 ,[Offset], S02CC_C4, 199,
 ,[Offset], S02CF_C4, 200,
 ,[Offset], S02D0_C4, 201,
 ,[Offset], S0382_C4, 202,
 ,[Offset], S0383_C4, 203,
 ,[Offset], S0384_C4, 204,
 ,[Offset], S0387_C4, 205,
 ,[Offset], S0388_C4, 206,
 ,[Offset], S0389_C4, 207,
 ,[Offset], S038A_C4, 208,
 ,[Offset], S038B_C4, 209,
 ,[Offset], S038C_C4, 210,
 ,[Offset], S038D_C4, 211,
 ,[Offset], S0390_C4, 212,
 ,[Offset], S0391_C4, 213,
 ,[Offset], S0392_C4, 214,
 ,[Offset], S0393_C4, 215,
 ,[Offset], S0394_C4, 216,
 ,[Offset], S0395_C4, 217,
 ,[Offset], S0396_C4, 218,
 ,[Offset], S0398_C4, 219,
 ,[Offset], S0399_C4, 220,
 ,[Offset], S039A_C4, 221,
 ,[Offset], S039B_C4, 222,
 ,[Offset], S039C_C4, 223,
 ,[Offset], S039D_C4, 224,
 ,[Offset], S039E_C4, 225,
 ,[Offset], S039F_C4, 226,
 ,[Offset], S03A1_C4, 227,
 ,[Offset], S03A2_C4, 228,
 ,[Offset], S03A4_C4, 229,
 ,[Offset], S03A5_C4, 230,
 ,[Offset], S03A6-LP_C4, 231,
 ,[Offset], S03A7_C4, 232,
 ,[Offset], S03A8-LP_C4, 233,
 ,[Offset], S03A9_C4, 234,
 ,[Offset], S03AA_C4, 235,
 ,[Offset], S03AB_C4, 236,
 ,[Offset], S03AC_C4, 237,
 ,[Offset], S03AD_C4, 238,
 ,[Offset], S03AE_C4, 239,
 ,[Offset], S03B0_C4, 240,
 ,[Offset], S03B3_C4, 241,
 ,[Offset], S03B5_C4, 242,
 ,[Offset], S03B6_C4, 243,
 ,[Offset], S03B7_C4, 244,
 ,[Offset], S03B9_C4, 245,
 ,[Offset], S03BA_C4, 246,
 ,[Offset], S03BB_C4, 247,
 ,[Offset], S03BC_C4, 248,
 ,[Offset], S03BD_C4, 249,
 ,[Offset], S03BE_C4, 250,
 ,[Offset], S03BF_C4, 251,
 ,[Offset], S03C0_C4, 252,
 ,[Offset], S03C1_C4, 253,
 ,[Offset], S03C2_C4, 254,
 ,[Offset], S03C3_C4, 255,
 ,[Offset], S03C4_C4, 256,
 ,[Offset], S03C5_C4, 257,
 ,[Offset], S03C6_C4, 258,
 ,[Offset], S03C7_C4, 259,
 ,[Offset], S03C8_C4, 260,
 ,[Offset], S03C9_C4, 261,
 ,[Offset], S03CA_C4, 262,
 ,[Offset], S03CB_C4, 263,
 ,[Offset], S03CC_C4, 264,
 ,[Offset], S03CE_C4, 265,
 ,[Offset], S03CF_C4, 266,
 ,[Offset], S03D0_C4, 267,
 ,[Offset], S03D1_C4, 268,
 ,[Offset], S03D2_C4, 269,
 ,[Offset], S03D3_C4, 270,
 ,[Offset], S03D4_C4, 271,
 ,[Offset], S03D6_C4, 272,
 ,[Offset], S0582_C4, 273,
 ,[Offset], S0583_C4, 274,
 ,[Offset], S0584_C4, 275,
 ,[Offset], S0587_C4, 276,
 ,[Offset], S0588_C4, 277,
 ,[Offset], S0589_C4, 278,
 ,[Offset], S058A_C4, 279,
 ,[Offset], S058B_C4, 280,
 ,[Offset], S058C_C4, 281,
 ,[Offset], S058D_C4, 282,
 ,[Offset], S0590_C4, 283,
 ,[Offset], S0591_C4, 284,
 ,[Offset], S0592_C4, 285,
 ,[Offset], S0593_C4, 286,
 ,[Offset], S0594_C4, 287,
 ,[Offset], S0595_C4, 288,
 ,[Offset], S0596_C4, 289,
 ,[Offset], S0598_C4, 290,
 ,[Offset], S0599_C4, 291,
 ,[Offset], S059A_C4, 292,
 ,[Offset], S059B_C4, 293,
 ,[Offset], S059C_C4, 294,
 ,[Offset], S059D_C4, 295,
 ,[Offset], S059E_C4, 296,
 ,[Offset], S05A1_C4, 297,
 ,[Offset], S05A2_C4, 298,
 ,[Offset], S05AA_C4, 299,
 ,[Offset], S05B7_C4, 300,
 ,[Offset], S05BD_C4, 301,
 ,[Offset], S05BE_C4, 302,
 ,[Offset], S05C0_C4, 303,
 ,[Offset], S05C1_C4, 304,
 ,[Offset], S05C2_C4, 305,
 ,[Offset], S05C3_C4, 306,
 ,[Offset], S05C4_C4, 307,
 ,[Offset], S05C5_C4, 308,
 ,[Offset], S05CA_C4, 309,
 ,[Offset], S05CB_C4, 310,
 ,[Offset], S05CC_C4, 311,
 ,[Offset], S05CF_C4, 312,
 ,[Offset], S05D0_C4, 313,
RSID_TELVIRAPARTYMONSTERS_MECH_SOUNDS, 3112,,,
 ,[Offset], inlane_spin, 0,
 ,[Offset], inlane_no_spin, 1,
 ,[Offset], boogiemen, 2,
RSID_TELVIRAPARTYMONSTERS_SOUNDS_END, 3113,,,
RSID_TELVIRAPARTYMONSTERS_SAMPLES, 3114,,,
RSID_TELVIRAPARTYMONSTERS_HUD, 3115,,,
RSID_TELVIRAPARTYMONSTERS_END, 3116,,,
