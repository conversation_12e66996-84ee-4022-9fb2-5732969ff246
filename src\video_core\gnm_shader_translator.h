// MultipleFiles/gnm_shader_translator.h
#pragma once

#include "gcn_types.h" // Assuming this defines GCNShaderType and GCNInstruction
#include <cstdint>
#include <functional>
#include <istream>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for shader translation errors.
 */
struct ShaderTranslatorException : std::runtime_error {
  explicit ShaderTranslatorException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief GCN register types.
 */
enum class GCNRegisterType { SCALAR, VECTOR, SPECIAL };

/**
 * @brief Shader parsing state for GCN bytecode.
 */
struct ShaderParseState {
  const std::vector<uint32_t> *bytecode = nullptr; ///< Input bytecode
  size_t position = 0;                             ///< Current parsing position (in dwords)
  GCNShaderType type = GCNShaderType::VERTEX;      ///< Shader type
  std::vector<GCNInstruction> instructions;        ///< Parsed instructions
  std::unordered_map<uint32_t, std::string> labelMap; ///< Label mappings (address to name)
};

/**
 * @brief Shader cache entry for translated shaders.
 */
struct ShaderCacheEntry {
  std::vector<uint32_t> spirvCode; ///< Cached SPIR-V code
  std::string glslCode;            ///< Cached GLSL code
  GCNShaderType type;              ///< Shader type
  mutable uint64_t cacheHits = 0;          ///< Cache hits for this entry
  mutable uint64_t cacheMisses = 0;        ///< Cache misses for this entry
};

/**
 * @brief Enhanced GCN instruction types for comprehensive coverage.
 */
enum class GCNInstructionType {
  // Scalar ALU Instructions
  S_MOV, S_ADD, S_SUB, S_MUL, S_AND, S_OR, S_XOR, S_NOT,
  S_LSHL, S_LSHR, S_ASHR, S_CMP,

  // Vector ALU Instructions
  V_MOV, V_ADD_F32, V_SUB_F32, V_MUL_F32, V_DIV_F32, V_MAD_F32, V_FMA_F32,
  V_MIN_F32, V_MAX_F32, V_ADD_I32, V_SUB_I32, V_MUL_I32,
  V_AND_B32, V_OR_B32, V_XOR_B32, V_NOT_B32,
  V_LSHL_B32, V_LSHR_B32, V_ASHR_I32,
  V_CMP_EQ_F32, V_CMP_NE_F32, V_CMP_GT_F32, V_CMP_LT_F32, V_CMP_GE_F32, V_CMP_LE_F32,

  // Memory Instructions
  BUFFER_LOAD, BUFFER_STORE, IMAGE_LOAD, IMAGE_STORE,
  DS_READ, DS_WRITE, FLAT_LOAD, FLAT_STORE,

  // Control Flow
  S_BRANCH, S_CBRANCH, S_ENDPGM, S_BARRIER, S_WAITCNT, S_SENDMSG,

  // Texture/Sampling
  IMAGE_SAMPLE, IMAGE_GATHER, IMAGE_GET_RESINFO,

  // Export Instructions
  EXP_POS, EXP_PARAM, EXP_MRT,

  UNKNOWN // For unsupported or unrecognized instructions
};

/**
 * @brief Enhanced GCN instruction information for translation.
 */
struct EnhancedGCNInstruction {
  GCNInstructionType type;     ///< Categorized type of the instruction
  uint32_t opcode;             ///< Raw opcode value
  std::vector<uint32_t> operands; ///< Raw operand values (dst, src0, src1, src2, imm)
  std::string mnemonic;        ///< Human-readable instruction mnemonic (e.g., "s_mov_b32")
  bool hasDestination;         ///< True if the instruction writes to a destination register
  uint32_t destinationMask;    ///< Mask indicating which components of the destination are written (e.g., 0xF for all 4 components)
  bool isControlFlow;          ///< True if the instruction alters program flow (branch, call, return)
  bool isMemoryAccess;         ///< True if the instruction performs a memory read or write
  uint32_t cycleCount;         ///< Estimated execution cycles for this instruction
};

/**
 * @brief Statistics for shader translator operations.
 */
struct ShaderTranslatorStats {
  uint64_t operationCount = 0;      ///< Total translation operations (Disassemble, TranslateToSPIRV, TranslatetoGLSL)
  uint64_t totalLatencyUs = 0;      ///< Total accumulated latency in microseconds across all operations
  uint64_t cacheHits = 0;           ///< Total cache hits for translation operations
  uint64_t cacheMisses = 0;         ///< Total cache misses for translation operations
  uint64_t errorCount = 0;          ///< Total errors encountered during any operation
  uint64_t instructionsCovered = 0; ///< Number of GCN instructions successfully mapped/translated
  uint64_t instructionsSkipped = 0; ///< Number of GCN instructions that could not be translated (unknown/unsupported)
  uint64_t spirvGenerations = 0;    ///< Number of times SPIR-V code was generated from GCN
  uint64_t glslGenerations = 0;     ///< Number of times GLSL code was generated from GCN
  uint64_t optimizationPasses = 0;  ///< Number of optimization passes performed on translated code
};

// Forward declarations to avoid circular dependencies
class PS4GPU; // Assuming PS4GPU might interact with this translator

/**
 * @brief Callback function types for notifying PS4GPU of shader translations.
 * @param type The type of shader (e.g., VERTEX, PIXEL).
 * @param bytecodeHash A hash of the original GCN bytecode, useful for identification.
 * @param spirvCode The generated SPIR-V code.
 */
using ShaderTranslationCallback_SPIRV =
    std::function<void(GCNShaderType type, uint64_t bytecodeHash,
                       const std::vector<uint32_t> &spirvCode)>;

/**
 * @brief Callback function types for notifying PS4GPU of shader translations.
 * @param type The type of shader (e.g., VERTEX, PIXEL).
 * @param bytecodeHash A hash of the original GCN bytecode, useful for identification.
 * @param glslCode The generated GLSL code.
 */
using ShaderTranslationCallback_GLSL = std::function<void(
    GCNShaderType type, uint64_t bytecodeHash, const std::string &glslCode)>;

/**
 * @brief Translates GCN shaders to SPIR-V or GLSL and disassembles for debugging.
 * @details Manages shader translation in a thread-safe manner, with caching to
 * reduce redundant translations. Integrates with GNMRegisterState for
 * register data and TileManager for tiled surfaces. Supports
 * serialization with versioning and multi-core diagnostics.
 */
class GNMShaderTranslator {
public:
  /**
   * @brief Constructs the shader translator.
   * @details Initializes opcode tables and metrics. Thread-safe.
   */
  GNMShaderTranslator();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Calls Shutdown() to ensure proper cleanup. Thread-safe.
   */
  ~GNMShaderTranslator();

  /**
   * @brief Initializes the shader translator.
   * @return True on success, false on failure.
   * @throws ShaderTranslatorException on initialization errors.
   * @details Populates opcode tables and resets metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the shader translator.
   * @details Clears opcode tables and cache. Resets statistics. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Disassembles GCN bytecode into a human-readable string.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return Disassembled shader string.
   * @throws ShaderTranslatorException on parsing errors or invalid bytecode.
   * @details Thread-safe. Updates metrics and cache.
   */
  std::string Disassemble(const std::vector<uint32_t> &bytecode,
                          GCNShaderType type);

  /**
   * @brief Translates GCN bytecode to SPIR-V.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return SPIR-V code as a vector of uint32_t.
   * @throws ShaderTranslatorException on parsing or translation errors.
   * @details Thread-safe. Checks cache first, updates metrics. Notifies via callback if set.
   * @note May use GNMRegisterState for register data (though not directly implemented here).
   */
  std::vector<uint32_t> TranslateToSPIRV(const std::vector<uint32_t> &bytecode,
                                         GCNShaderType type);

  /**
   * @brief Translates GCN bytecode to GLSL.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return GLSL code as a string.
   * @throws ShaderTranslatorException on parsing or translation errors.
   * @details Thread-safe. Checks cache first, updates metrics. Notifies via callback if set.
   */
  std::string TranslatetoGLSL(const std::vector<uint32_t> &bytecode,
                              GCNShaderType type);

  /**
   * @brief Translates SPIR-V to GNM binary.
   * @param spirv Input SPIR-V code.
   * @param gnmBinary Output GNM binary (vector of uint32_t).
   * @return True on success, false on failure.
   * @throws ShaderTranslatorException on translation errors or invalid SPIR-V.
   * @details This is a complex reverse translation. Current implementation is a stub. Thread-safe. Updates metrics.
   */
  bool Translate(const std::vector<uint32_t> &spirv,
                 std::vector<uint32_t> &gnmBinary);

  /**
   * @brief Retrieves a cached shader.
   * @param bytecodeHash Hash of the GCN bytecode.
   * @param type Shader type.
   * @param spirvCode Output SPIR-V code (if cached).
   * @param glslCode Output GLSL code (if cached).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics for the specific entry and overall.
   */
  bool GetCachedShader(uint64_t bytecodeHash, GCNShaderType type,
                       std::vector<uint32_t> &spirvCode,
                       std::string &glslCode) const;

  /**
   * @brief Clears the entire shader cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearShaderCache();

  /**
   * @brief Retrieves shader translator statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  ShaderTranslatorStats GetStats() const;

  /**
   * @brief Saves the translator state to an output stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes internal opcode tables, cache, and statistics with versioning (version 1).
   * @throws ShaderTranslatorException on write errors.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the translator state from an input stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format. Clears existing state before loading.
   * @throws ShaderTranslatorException on invalid state data or read errors.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the callback for SPIR-V shader translation notifications.
   * @param callback Callback function to call when SPIR-V translation completes. Can be empty to disable.
   */
  void SetShaderTranslationCallback_SPIRV(
      const ShaderTranslationCallback_SPIRV &callback);

  /**
   * @brief Sets the callback for GLSL shader translation notifications.
   * @param callback Callback function to call when GLSL translation completes. Can be empty to disable.
   */
  void SetShaderTranslationCallback_GLSL(
      const ShaderTranslationCallback_GLSL &callback);

  /**
   * @brief Translates a single GCN instruction into SPIR-V and GLSL code snippets.
   * @param instr The enhanced GCN instruction to translate.
   * @param spirvCode Output string for the SPIR-V representation (assembly-like).
   * @param glslCode Output string for the GLSL representation.
   * @return True if translation was successful for both, false otherwise.
   * @details This method is primarily used internally by TranslateToSPIRV and TranslatetoGLSL.
   *          It dispatches to specific Process* methods based on instruction type.
   */
  bool TranslateGCNInstruction(const EnhancedGCNInstruction &instr,
                               std::string &spirvCode, std::string &glslCode);

  /**
   * @brief Performs optimization passes on SPIR-V code.
   * @param spirvCode The SPIR-V code to optimize (modified in-place).
   * @return True if optimization was successful, false otherwise.
   * @details Current implementation is a stub. Future work could integrate SPIRV-Tools.
   */
  bool OptimizeShader(std::vector<uint32_t> &spirvCode);

  /**
   * @brief Validates the integrity and correctness of SPIR-V code.
   * @param spirvCode The SPIR-V code to validate.
   * @return True if the SPIR-V code is valid, false otherwise.
   * @details Current implementation performs a basic header check. Future work could integrate SPIRV-Tools validation.
   */
  bool ValidateShader(const std::vector<uint32_t> &spirvCode);

  /**
   * @brief Generates GLSL code from SPIR-V code.
   * @param spirvCode The SPIR-V code to convert.
   * @return The generated GLSL code string.
   * @details Current implementation is a stub. Future work would involve a SPIR-V to GLSL cross-compiler.
   */
  std::string GenerateGLSLFromSPIRV(const std::vector<uint32_t> &spirvCode);

  /**
   * @brief Analyzes the complexity of a GCN shader.
   * @param bytecode The GCN bytecode to analyze.
   * @param instructionCount Output: total number of instructions.
   * @param cycleEstimate Output: estimated total CPU cycles for execution.
   * @return True if analysis was successful, false otherwise.
   * @details Parses the shader and sums up estimated cycles for each instruction.
   */
  bool AnalyzeShaderComplexity(const std::vector<uint32_t> &bytecode,
                               uint32_t &instructionCount,
                               uint32_t &cycleEstimate);

private:
  /**
   * @brief Parses GCN shader bytecode into a list of GCNInstruction objects.
   * @param state Parsing state, including input bytecode and output instructions.
   * @return True on success, false on failure.
   * @details Thread-safe. Updates parse state and metrics. Identifies branch targets for labels.
   */
  bool ParseShader(ShaderParseState &state);

  /**
   * @brief Parses a single GCN instruction from the bytecode stream.
   * @param state Parsing state, including current position in bytecode.
   * @param instr Output GCNInstruction object.
   * @return True on success, false on failure.
   * @details Thread-safe. Advances parse position. Handles 1-dword and 2-dword instructions.
   */
  bool ParseInstruction(ShaderParseState &state, GCNInstruction &instr);

  /**
   * @brief Disassembles a GCN instruction into its mnemonic and operands.
   * @param instr Instruction to disassemble.
   * @param address Instruction address (for label generation).
   * @return Disassembled string (e.g., "s_mov_b32 s0, s1").
   * @details Thread-safe. Formats based on instruction type by calling specific Format*Instruction methods.
   */
  std::string DisassembleInstruction(const GCNInstruction &instr,
                                     uint32_t address);

  /**
   * @brief Gets the human-readable name of a GCN register.
   * @param type Register type (SCALAR, VECTOR, SPECIAL).
   * @param index Register index.
   * @return Register name (e.g., "s0", "vcc").
   * @details Thread-safe (read-only).
   */
  std::string GetRegisterName(GCNRegisterType type, uint32_t index) const;

  /**
   * @brief Gets the human-readable name of a GCN opcode.
   * @param opcode Opcode value.
   * @return Opcode name (e.g., "s_mov_b32"). Returns "UNKNOWN_XX" for unrecognized opcodes.
   * @details Thread-safe (read-only). Looks up in pre-initialized opcode tables.
   */
  std::string GetOpcodeName(uint32_t opcode) const;

  /**
   * @brief Formats a scalar instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatScalarInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a vector instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatVectorInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a memory instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatMemoryInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a flow control instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatFlowControlInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a texture instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatTextureInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats an export instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatExportInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Initializes internal opcode tables and instruction cycle estimates.
   * @details Populates m_scalarOpcodes, m_vectorOpcodes, etc., and m_opcodeToType, m_instructionCycles.
   *          Called during Initialize().
   */
  void InitializeOpcodeTables();

  /**
   * @brief Processes a scalar ALU instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessScalarALU(const EnhancedGCNInstruction &instr,
                        std::string &output);

  /**
   * @brief Processes a vector ALU instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessVectorALU(const EnhancedGCNInstruction &instr,
                        std::string &output);

  /**
   * @brief Processes a memory instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessMemoryInstruction(const EnhancedGCNInstruction &instr,
                                std::string &output);

  /**
   * @brief Processes a control flow instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessControlFlow(const EnhancedGCNInstruction &instr,
                          std::string &output);

  /**
   * @brief Processes a texture instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessTextureInstruction(const EnhancedGCNInstruction &instr,
                                 std::string &output);

  /**
   * @brief Processes an export instruction for SPIR-V and GLSL generation.
   * @param instr The enhanced GCN instruction.
   * @param output The string stream to append the generated code to.
   * @return True on success, false on failure.
   */
  bool ProcessExportInstruction(const EnhancedGCNInstruction &instr,
                                std::string &output);

  /**
   * @brief Generates the standard SPIR-V header.
   * @return SPIR-V header string.
   */
  std::string GenerateSPIRVHeader();

  /**
   * @brief Generates SPIR-V type and variable declarations.
   * @param type The shader type, influencing specific declarations (e.g., input/output variables).
   * @return SPIR-V declarations string.
   */
  std::string GenerateSPIRVDeclarations(GCNShaderType type);

  /**
   * @brief Generates the SPIR-V main function body.
   * @param instructions A vector of enhanced GCN instructions to translate into the main function.
   * @return SPIR-V main function body string.
   */
  std::string
  GenerateSPIRVMain(const std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief Generates the standard GLSL header.
   * @param type The shader type, influencing layout declarations.
   * @return GLSL header string.
   */
  std::string GenerateGLSLHeader(GCNShaderType type);

  /**
   * @brief Generates GLSL variable declarations (registers, textures, buffers).
   * @param type The shader type, influencing specific declarations.
   * @return GLSL declarations string.
   */
  std::string GenerateGLSLDeclarations(GCNShaderType type);

  /**
   * @brief Generates the GLSL main function body.
   * @param instructions A vector of enhanced GCN instructions to translate into the main function.
   * @return GLSL main function body string.
   */
  std::string
  GenerateGLSLMain(const std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief Performs basic data flow analysis on a sequence of instructions.
   * @param instructions The sequence of enhanced GCN instructions.
   * @return True if analysis was successful, false otherwise.
   * @details Current implementation is a stub. Could be expanded for liveness, def-use chains, etc.
   */
  bool AnalyzeDataFlow(const std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief Optimizes a sequence of enhanced GCN instructions.
   * @param instructions The sequence of instructions to optimize (modified in-place).
   * @return True if optimization was successful, false otherwise.
   * @details Current implementation performs a simple redundant MOV removal.
   */
  bool OptimizeInstructionSequence(
      std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief Estimates the number of CPU cycles an instruction takes.
   * @param instr The enhanced GCN instruction.
   * @return Estimated cycle count. Returns 1 for unknown instructions.
   * @details Uses a pre-defined map of instruction types to cycle counts.
   */
  uint32_t EstimateInstructionCycles(const EnhancedGCNInstruction &instr);

  // Internal data structures for opcode mapping and instruction properties
  std::unordered_map<uint32_t, std::string> m_scalarOpcodes;      ///< Map of scalar opcode values to their mnemonic names
  std::unordered_map<uint32_t, std::string> m_vectorOpcodes;      ///< Map of vector opcode values to their mnemonic names
  std::unordered_map<uint32_t, std::string> m_memoryOpcodes;      ///< Map of memory opcode values to their mnemonic names
  std::unordered_map<uint32_t, std::string> m_flowControlOpcodes; ///< Map of flow control opcode values to their mnemonic names
  std::unordered_map<uint32_t, std::string> m_textureOpcodes;     ///< Map of texture opcode values to their mnemonic names
  std::unordered_map<uint32_t, std::string> m_exportOpcodes;      ///< Map of export opcode values to their mnemonic names

  std::unordered_map<uint32_t, GCNInstructionType> m_opcodeToType; ///< Map from raw opcode to categorized GCNInstructionType
  std::unordered_map<GCNInstructionType, uint32_t> m_instructionCycles; ///< Map from GCNInstructionType to estimated CPU cycles

  mutable std::unordered_map<uint64_t, ShaderCacheEntry> m_shaderCache; ///< Cache for translated shaders (key: bytecode hash)

  mutable std::shared_mutex m_translatorMutex; ///< Mutex for thread safety, protecting internal state
  mutable ShaderTranslatorStats m_stats;       ///< Translator statistics, updated by all operations

  ShaderTranslationCallback_SPIRV m_spirvCallback; ///< Callback for SPIR-V translation completion
  ShaderTranslationCallback_GLSL m_glslCallback;   ///< Callback for GLSL translation completion
};

// Assuming GCNInstruction and GCNShaderType are defined in gcn_types.h
// Example minimal gcn_types.h content for compilation:
/*
#ifndef GCN_TYPES_H
#define GCN_TYPES_H

#include <cstdint>
#include <vector>

namespace ps4 {

enum class GCNShaderType {
    VERTEX,
    PIXEL,
    GEOMETRY,
    COMPUTE,
    HULL,
    DOMAIN_SHADER,
    UNKNOWN
};

struct GCNInstruction {
    uint32_t opcode;
    uint32_t dst;
    uint32_t src0;
    uint32_t src1;
    uint32_t src2;
    uint32_t imm;
    bool predicated;
    uint32_t predicate;
};

} // namespace ps4

#endif // GCN_TYPES_H
*/

} // namespace ps4

