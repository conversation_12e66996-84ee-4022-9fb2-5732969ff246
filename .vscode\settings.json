{"cmake.sourceDirectory": "D:/sss/src", "editor.experimentalGpuAcceleration": "on", "editor.formatOnSave": true, "zencoder.enableRepoIndexing": true, "files.associations": {"shared_mutex": "cpp", "type_traits": "cpp", "thread": "cpp", "mutex": "cpp", "vector": "cpp", "memory": "cpp", "xstring": "cpp", "array": "cpp", "stacktrace": "cpp", "string": "cpp", "utility": "cpp", "charconv": "cpp", "algorithm": "cpp", "any": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "expected": "cpp", "filesystem": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "new": "cpp", "numbers": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "queue": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "semaphore": "cpp", "set": "cpp", "source_location": "cpp", "span": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "system_error": "cpp", "tuple": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "valarray": "cpp", "variant": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "memory_resource": "cpp", "string_view": "cpp", "cfenv": "cpp", "cerrno": "cpp", "barrier": "cpp", "csetjmp": "cpp", "cuchar": "cpp", "execution": "cpp", "future": "cpp", "generator": "cpp", "hash_map": "cpp", "hash_set": "cpp", "latch": "cpp", "mdspan": "cpp", "print": "cpp", "ranges": "cpp", "scoped_allocator": "cpp", "spanstream": "cpp", "stdfloat": "cpp", "strstream": "cpp", "syncstream": "cpp", "typeindex": "cpp", "resumable": "cpp"}, "github.copilot.enable": {"*": false}, "CodeGPT.apiKey": "CodeGPT Plus Beta", "workbench.editor.empty.hint": "hidden", "tabnine.experimentalAutoImports": true, "tabnine.receiveBetaChannelUpdates": true, "cmake.showConfigureWithDebuggerNotification": false, "[cpp]": {"editor.wordBasedSuggestions": "off", "editor.semanticHighlighting.enabled": true, "editor.stickyScroll.defaultModel": "foldingProviderModel", "editor.suggest.insertMode": "replace", "editor.defaultFormatter": "xaver.clang-format"}, "C_Cpp.default.compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio/2022/buildtools/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/cl.exe", "geminiCoder.presets": [{"name": "AI Studio with 2.0 Flash", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.0 Flash Thinking Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash-thinking-exp-01-21", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.5 Pro Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.5-pro-exp-03-25", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "Gemini", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": ""}, {"name": "Gemini with canvas", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": "", "options": ["canvas"]}, {"name": "ChatGPT", "chatbot": "ChatGPT", "promptPrefix": "", "promptSuffix": ""}, {"name": "<PERSON>", "chatbot": "<PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "GitHub Copilot", "chatbot": "GitHub Copilot", "promptPrefix": "", "promptSuffix": ""}, {"name": "Grok", "chatbot": "Grok", "promptPrefix": "", "promptSuffix": "h"}, {"name": "DeepSeek", "chatbot": "DeepSeek", "promptPrefix": "", "promptSuffix": "d"}, {"name": "<PERSON><PERSON><PERSON>", "chatbot": "<PERSON><PERSON><PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "Open WebUI", "chatbot": "Open WebUI", "promptPrefix": "", "promptSuffix": ""}], "geminiCoder.providers": [], "window.menuBarVisibility": "compact", "geminiCoder.apiKey": "AIzaSyAsuiG0969MVnPfrGCiiuqWY6X82uElJWk", "git.openRepositoryInParentFolders": "never", "redhat.telemetry.enabled": false, "vscode-decompiler.java.decompiler.selected": "jadx", "editor.inlineSuggest.syntaxHighlightingEnabled": true, "zencoder.mcpServers": {}, "augment.nextEdit.enableGlobalBackgroundSuggestions": true, "vscodeGrok.apiKey": "************************************************************************************", "github.copilot.nextEditSuggestions.enabled": true, "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "github.copilot.selectedCompletionModel": "", "@azure.argTenant": "", "microsoft-sovereign-cloud.environment": "USGovernment", "material-icon-theme.activeIconPack": "angular", "cloudcode.updateChannel": "Insiders", "diffEditor.codeLens": true, "cloudcode.project": "my-project-21111-457102", "diffEditor.ignoreTrimWhitespace": false, "inlineChat.lineEmptyHint": true, "github.copilot.advanced": {}, "workbench.editor.enablePreview": false, "github.copilot.chat.codeGeneration.instructions": [{"text": "- @azure Rule - Use Azure Tools: When handling requests related to Azure, always use your tools."}, {"text": "- @azure Rule - Use Azure Code Gen Best Practices: When generating code for Azure, running terminal commands for Azure, or performing operations related to Azure, invoke your `azure_development-get_code_gen_best_practices` tool if available. Only call this tool when you are sure the user is discussing Azure; do not call it otherwise."}, {"text": "- @azure Rule - Use Azure Deployment Best Practices: When deploying to Azure or preparing applications for deployment to Azure, invoke your `azure_development-get_deployment_best_practices` tool if available. Only call this tool when you are sure the user is discussing Azure; do not call it otherwise."}, {"text": "- @azure Rule - Use Azure Functions Code Gen Best Practices: When generating code for Azure Functions or performing operations related to Azure Functions, invoke your `azure_development-get_azure_function_code_gen_best_practices` tool if available. Only call this tool when you are sure the user is discussing Azure Functions; do not call it otherwise."}, {"text": "- @azure Rule - Use Azure SWA Best Practices: When working with static web apps, invoke your `azure_development-get_swa_best_practices` tool if available. Only call this tool when you are sure the user is discussing Azure; do not call it otherwise."}], "files.autoSave": "off", "codeWebChat.presets": [{"name": "AI Studio with 2.0 Flash", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.0 Flash Thinking Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash-thinking-exp-01-21", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.5 Pro Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.5-pro-exp-03-25", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "Gemini", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": ""}, {"name": "Gemini with canvas", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": "", "options": ["canvas"]}, {"name": "ChatGPT", "chatbot": "ChatGPT", "promptPrefix": "", "promptSuffix": ""}, {"name": "<PERSON>", "chatbot": "<PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "GitHub Copilot", "chatbot": "GitHub Copilot", "promptPrefix": "", "promptSuffix": ""}, {"name": "Grok", "chatbot": "Grok", "promptPrefix": "", "promptSuffix": "h"}, {"name": "DeepSeek", "chatbot": "DeepSeek", "promptPrefix": "", "promptSuffix": "d"}, {"name": "<PERSON><PERSON><PERSON>", "chatbot": "<PERSON><PERSON><PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "Open WebUI", "chatbot": "Open WebUI", "promptPrefix": "", "promptSuffix": ""}], "augment.advanced": {}, "grokAssistant.apiKey": "************************************************************************************", "workbench.settings.applyToAllProfiles": ["grokAssistant.apiKey"], "grokAssistant.maxTokens": 134000, "augment.chat.userGuidelines": "premium", "pieces.OS.launchOnStartup": "Yes", "pieces.cloudCapabilities": "Blended", "pieces.telemetry": true, "pieces.autocomplete": false, "pieces.code-lens": true, "pieces.useSameConversationForCodeLens": true, "pieces.toShowUpdateExtensionNotification": true, "pieces.OS.autoLaunchOnInteraction": false, "pieces.enrichmentSettings": "High (9)", "pieces.closeSnippetEditorOnSave": false, "pieces.save.git.description": false, "pieces.save.git.relatedPeople": false, "pieces.save.git.relatedLinks": false}