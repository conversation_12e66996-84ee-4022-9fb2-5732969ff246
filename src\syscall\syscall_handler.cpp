#include "syscall_handler.h"
#include "../cpu/register.h"
#include "../cpu/x86_64_cpu.h"
#include "../memory/memory.h"
#include "../memory/ps4_mmu.h" // For ALLOC_FAILED and PhysicalMemoryAllocator
#include "../ps4/fiber_manager.h"
#include "../ps4/orbis_os.h"
#include "../ps4/ps4_emulator.h"
#include "../ps4/ps4_gpu.h"
#include "../video_core/command_processor.h"
#include "../video_core/tile_manager.h"
#include <cerrno>
#include <chrono>
#include <csignal>
#include <cstdint>
#include <ctime>
#include <fcntl.h>
#include <signal.h> // for SIG_BLOCK, SIG_UNBLOCK, SIG_SETMASK
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <sys/stat.h> // for struct timeval, gettimeofday, struct timezone// struct timezone, gettimeofday
#include <time.h>     // clockid_t, struct timespec, clock_gettime

#ifndef _WIN32
#include <sys/resource.h>
#include <sys/time.h>

#else
// Provide minimal stubs so we can still compile on Windows:
using clockid_t = int;
using useconds_t = unsigned long;
// Provide stub for rusage so sizeof(struct rusage) works on Windows:
struct rusage {
  struct timeval ru_utime;
  struct timeval ru_stime;
};
#ifndef RUSAGE_SELF
#define RUSAGE_SELF 0
#endif
#ifndef RUSAGE_CHILDREN
#define RUSAGE_CHILDREN 1
#endif
// Windows has its own functions, so we skip the POSIX headers here.
#endif

#ifdef _WIN32
#include <csignal>
#include <direct.h>
#include <io.h>
#include <process.h>
#include <sched.h>
#include <windows.h>
#ifndef SIGKILL
#define SIGKILL 9
#endif
#include <corecrt_io.h>
#define open _open
#define read _read
#define write _write
#define close _close
#define dup _dup
#define chdir _chdir
#define unlink _unlink
#define getpid _getpid
#define lseek _lseek
#define stat _stat
#define fstat _fstat
#ifndef S_ISREG
#define S_ISREG(m) (((m) & S_IFMT) == S_IFREG)
#endif
#ifndef S_ISDIR
#define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR)
#endif
#endif

#ifdef _WIN32
#undef open
#undef read
#undef write
#undef close
#undef dup
#undef chdir
#undef unlink
#undef getpid
#undef lseek
#undef stat
#undef fstat
#endif

#ifndef SIG_BLOCK
#define SIG_BLOCK 0
#define SIG_UNBLOCK 1
#define SIG_SETMASK 2
#endif

#ifndef MAP_ANONYMOUS
#define MAP_ANONYMOUS 0x20
#endif

#ifndef MAP_FIXED
#define MAP_FIXED 0x10
#endif

#ifndef O_ACCMODE
#define O_ACCMODE 0x3
#endif

namespace ps4 {

SyscallHandler::SyscallHandler(PS4Emulator &emu)
    : m_emulator(emu), m_maxFds(1024), m_stats() {
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_fdTable.resize(m_maxFds);
    if (m_fdTable.size() > 0)
      m_fdTable[0] = {true, 0, "stdin", 0, 0, 0};
    if (m_fdTable.size() > 1)
      m_fdTable[1] = {true, 1, "stdout", 0, 0, 0};
    if (m_fdTable.size() > 2)
      m_fdTable[2] = {true, 2, "stderr", 0, 0, 0};
    spdlog::info("SyscallHandler constructed");
  } catch (const std::exception &e) {
    m_stats.errorCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    spdlog::error("SyscallHandler construction failed: {}", e.what());
    throw SyscallException("SyscallHandler construction failed: " +
                           std::string(e.what()));
  }
}

SyscallHandler::~SyscallHandler() {
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    for (auto &fd : m_fdTable) {
      if (fd.isOpen && fd.hostFd >= 3) {
        ::close(fd.hostFd);
      }
    }
    spdlog::info("SyscallHandler destroyed");
  } catch (const std::exception &e) {
    m_stats.errorCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    spdlog::error("SyscallHandler destruction failed: {}", e.what());
  }
}

bool SyscallHandler::Initialize() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    spdlog::info("Initializing syscall handler");
    RegisterAllSyscalls();
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    spdlog::info("Registered {} syscall handlers, latency={}us",
                 m_syscallTable.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    spdlog::error("SyscallHandler initialization failed: {}", e.what());
    throw SyscallException("SyscallHandler initialization failed: " +
                           std::string(e.what()));
  }
}

void SyscallHandler::HandleSyscall() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t syscallNum = GetSyscallNumber();
    auto it = m_syscallTable.find(syscallNum);
    if (it != m_syscallTable.end()) {
      it->second();
      m_stats.callCount.fetch_add(1, std::memory_order_relaxed);
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::trace("HandleSyscall: Handled syscall 0x{:x}, latency={}us",
                    syscallNum, latency);
    } else {
      m_stats.errorCount.fetch_add(1, std::memory_order_relaxed);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      SetErrorWithLog(PS4_ENOSYS,
                      "Unhandled syscall: 0x" + std::to_string(syscallNum));
    }
  } catch (const std::exception &e) {
    m_stats.errorCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    SetErrorWithLog(PS4_EINVAL,
                    "HandleSyscall failed: " + std::string(e.what()));
  }
}

void SyscallHandler::RegisterSyscall(uint64_t number, SyscallFunc handler) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_syscallTable[number] = std::move(handler);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "RegisterSyscall: Registered handler for 0x{:x}, latency={}us", number,
        latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("RegisterSyscall failed: {}", e.what());
  }
}

void SyscallHandler::RegisterAllSyscalls() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    RegisterSyscall(1, [this]() { SysExit(); });
    RegisterSyscall(2, [this]() { SysFork(); });
    RegisterSyscall(3, [this]() { SysRead(); });
    RegisterSyscall(4, [this]() { SysWrite(); });
    RegisterSyscall(5, [this]() { SysOpen(); });
    RegisterSyscall(6, [this]() { SysClose(); });
    RegisterSyscall(7, [this]() { SysStat(); });
    RegisterSyscall(8, [this]() { SysFstat(); });
    RegisterSyscall(9, [this]() { SysLseek(); });
    RegisterSyscall(10, [this]() { SysUnlink(); });
    RegisterSyscall(12, [this]() { SysChdir(); });
    RegisterSyscall(14, [this]() { SysMknod(); });
    RegisterSyscall(17, [this]() { SysBrk(); });
    RegisterSyscall(20, [this]() { SysGetpid(); });
    RegisterSyscall(24, [this]() { SysGetuid(); });
    RegisterSyscall(25, [this]() { SysGeteuid(); });
    RegisterSyscall(37, [this]() { SysKill(); });
    RegisterSyscall(39, [this]() { SysGetppid(); });
    RegisterSyscall(41, [this]() { SysDup(); });
    RegisterSyscall(43, [this]() { SysGetegid(); });
    RegisterSyscall(47, [this]() { SysGetgid(); });
    RegisterSyscall(54, [this]() { SysIoctl(); });
    RegisterSyscall(55, [this]() { SysReboot(); });
    RegisterSyscall(73, [this]() { SysMunmap(); });
    RegisterSyscall(83, [this]() { SysGetdents(); });
    RegisterSyscall(116, [this]() { SysGettimeofday(); });
    RegisterSyscall(117, [this]() { SysGetrusage(); });
    RegisterSyscall(232, [this]() { SysClockGettime(); });
    RegisterSyscall(240, [this]() { SysSceKernelUsleep(); });
    RegisterSyscall(263, [this]() { SysNanosleep(); });
    RegisterSyscall(477, [this]() { SysMmap(); });
    RegisterSyscall(587, [this]() { SysSceKernelDlsym(); });
    RegisterSyscall(589, [this]() { SysSceKernelGetProcessId(); });
    RegisterSyscall(590, [this]() { SysSceKernelGetCpuFreq(); });
    RegisterSyscall(591, [this]() { SysSceKernelUuidCreate(); });
    RegisterSyscall(592, [this]() { SysSceKernelCreateFiber(); });
    RegisterSyscall(593, [this]() { SysSceKernelDeleteFiber(); });
    RegisterSyscall(594, [this]() { SysSceKernelSwitchToFiber(); });
    RegisterSyscall(600, [this]() { SysSceNpTrophyInit(); });
    RegisterSyscall(601, [this]() { SysSceNpTrophyCreateContext(); });
    RegisterSyscall(603, [this]() { SysSceNpTrophyUnlockTrophy(); });
    RegisterSyscall(610, [this]() { SysSceZlibDecompress(); });
    RegisterSyscall(611, [this]() { SysSceZlibCompress(); });
    RegisterSyscall(612, [this]() { SysSceKernelReadTsc(); });
    RegisterSyscall(613, [this]() { SysSceKernelGetTscFrequency(); });
    RegisterSyscall(620, [this]() { SysSceGnmSubmitCommandBuffers(); });
    RegisterSyscall(621, [this]() { SysSceGnmCreateTiledSurface(); });
    RegisterSyscall(622, [this]() { SysSceGnmLoadShader(); });
    RegisterSyscall(623, [this]() { SysSceGnmMapComputeQueue(); });
    RegisterSyscall(64, [this]() { SysRtSigaction(); });
    RegisterSyscall(65, [this]() { SysRtSigprocmask(); });

    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("RegisterAllSyscalls: Registered {} syscalls, latency={}us",
                  m_syscallTable.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("RegisterAllSyscalls failed: {}", e.what());
    throw SyscallException("RegisterAllSyscalls failed: " +
                           std::string(e.what()));
  }
}

uint64_t SyscallHandler::GetArg(int index) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    static const x86_64::Register regs[] = {
        x86_64::Register::RDI, x86_64::Register::RSI, x86_64::Register::RDX,
        x86_64::Register::RCX, x86_64::Register::R8,  x86_64::Register::R9};
    if (index >= 0 && index < 6) {
      uint64_t value = m_emulator.GetCPU(0).GetRegister(regs[index]);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetArg: index={}, value=0x{:x}, latency={}us", index,
                    value, latency);
      return value;
    }
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::warn("GetArg: Invalid argument index {}", index);
    return 0;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetArg failed: {}", e.what());
    return 0;
  }
}

uint64_t SyscallHandler::GetSyscallNumber() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t value = m_emulator.GetCPU(0).GetRegister(x86_64::Register::RAX);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetSyscallNumber: value=0x{:x}, latency={}us", value,
                  latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetSyscallNumber failed: {}", e.what());
    return 0;
  }
}

void SyscallHandler::SetReturnValue(uint64_t value) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_emulator.GetCPU(0).SetRegister(x86_64::Register::RAX, value);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SetReturnValue: value=0x{:x}, latency={}us", value, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetReturnValue failed: {}", e.what());
  }
}

void SyscallHandler::SetError(int error) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_emulator.GetCPU(0).SetRegister(x86_64::Register::RAX,
                                     static_cast<uint64_t>(-error));
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SetError: error={}, latency={}us", error, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetError failed: {}", e.what());
  }
}

void SyscallHandler::SetErrorWithLog(int error, const std::string &message) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    spdlog::error(message);
    SetError(error);
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SetErrorWithLog: error={}, message='{}', latency={}us",
                  error, message, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetErrorWithLog failed: {}", e.what());
  }
}

uint64_t SyscallHandler::GetBrk() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t brk = 0;
    m_emulator.GetMMU().ReadVirtual(0x1000, &brk, sizeof(brk), 1);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetBrk: brk=0x{:x}, latency={}us", brk, latency);
    return brk;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetBrk failed: {}", e.what());
    return 0;
  }
}

bool SyscallHandler::SetBrk(uint64_t addr) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    if (addr <= 8ULL * 1024 * 1024 * 1024) {
      m_emulator.GetMMU().WriteVirtual(0x1000, &addr, sizeof(addr), 1);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SetBrk: addr=0x{:x}, latency={}us", addr, latency);
      return true;
    }
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetBrk: Invalid address 0x{:x}", addr);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetBrk failed: {}", e.what());
    return false;
  }
}

std::string SyscallHandler::ReadString(uint64_t addr) {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    std::string result;
    char buffer[256];
    uint64_t offset = 0;
    size_t bytesRead;

    do {
      bytesRead = m_emulator.GetMMU().ReadVirtual(addr + offset, buffer,
                                                  sizeof(buffer) - 1, 1);
      buffer[bytesRead] = '\0';
      for (size_t i = 0; i < bytesRead; ++i) {
        if (buffer[i] == '\0') {
          result.append(buffer, i);
          m_stats.cacheHits++;
          auto end = std::chrono::high_resolution_clock::now();
          auto latency =
              std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                  .count();
          m_stats.totalLatencyUs += latency;
          spdlog::trace("ReadString: addr=0x{:x}, result='{}', latency={}us",
                        addr, result, latency);
          return result;
        }
      }
      result.append(buffer, bytesRead);
      offset += bytesRead;
    } while (bytesRead > 0 && offset < 65536);

    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::warn("ReadString: String at 0x{:x} truncated or invalid", addr);
    return result;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ReadString failed: {}", e.what());
    return "";
  }
}

int SyscallHandler::AllocateFD() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    for (size_t i = 3; i < m_fdTable.size(); ++i) {
      if (!m_fdTable[i].isOpen) {
        m_fdTable[i].cacheHits++;
        m_stats.cacheHits++;
        auto end = std::chrono::high_resolution_clock::now();
        auto latency =
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        m_stats.totalLatencyUs += latency;
        spdlog::trace("AllocateFD: Allocated FD {}, latency={}us", i, latency);
        return static_cast<int>(i);
      }
    }
    if (m_fdTable.size() < m_maxFds * 2) {
      m_fdTable.resize(m_fdTable.size() + 1024);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "AllocateFD: Expanded FD table, allocated FD {}, latency={}us",
          m_fdTable.size() - 1024, latency);
      return static_cast<int>(m_fdTable.size() - 1024);
    }
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("AllocateFD: No available file descriptors");
    throw SyscallException("No available file descriptors");
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("AllocateFD failed: {}", e.what());
    throw SyscallException("AllocateFD failed: " + std::string(e.what()));
  }
}

void SyscallHandler::FreeFD(int fd) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    if (fd >= 0 && fd < static_cast<int>(m_fdTable.size())) {
      if (m_fdTable[fd].isOpen && m_fdTable[fd].hostFd >= 3) {
        ::close(m_fdTable[fd].hostFd);
      }
      m_fdTable[fd] = FDInfo();
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("FreeFD: Freed FD {}, latency={}us", fd, latency);
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn("FreeFD: Invalid FD {}", fd);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FreeFD failed: {}", e.what());
  }
}

void SyscallHandler::SysExit() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t code = GetArg(0);
    m_emulator.GetOrbisOS().ExitProcess(static_cast<int>(code));
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysExit: Called with code {}, latency={}us", code, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysExit failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysExit failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysFork() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    SetErrorWithLog(PS4_EAGAIN, "SysFork: Not supported");
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysFork: Not supported, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysFork failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysFork failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysRead() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    uint64_t bufAddr = GetArg(1);
    size_t count = static_cast<size_t>(GetArg(2));
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF,
                      "SysRead: Invalid file descriptor " + std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(bufAddr, count, true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysRead: Invalid buffer address 0x" +
                                      std::to_string(bufAddr));
      return;
    }

    std::vector<uint8_t> buffer(count);
    ssize_t bytesRead = ::read(hostFd, buffer.data(), count);
    if (bytesRead < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysRead: Read failed for FD {}, errno={}", fd, errno);
    } else {
      m_emulator.GetMMU().WriteVirtual(bufAddr, buffer.data(),
                                       static_cast<size_t>(bytesRead), 1);
      SetReturnValue(static_cast<uint64_t>(bytesRead));
      std::unique_lock<std::shared_mutex> writeLock(m_fdMutex);
      m_fdTable[fd].offset += bytesRead;
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysRead: Read {} bytes from FD {} to 0x{:x}, latency={}us",
                    bytesRead, fd, bufAddr, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysRead failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysRead failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysWrite() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    uint64_t bufAddr = GetArg(1);
    size_t count = static_cast<size_t>(GetArg(2));
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysWrite: Invalid file descriptor " +
                                     std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(bufAddr, count, false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysWrite: Invalid buffer address 0x" +
                                      std::to_string(bufAddr));
      return;
    }

    std::vector<uint8_t> buffer(count);
    m_emulator.GetMMU().ReadVirtual(bufAddr, buffer.data(), count, 1);
    ssize_t bytesWritten = ::write(hostFd, buffer.data(), count);
    if (bytesWritten < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysWrite: Write failed for FD {}, errno={}", fd, errno);
    } else {
      SetReturnValue(static_cast<uint64_t>(bytesWritten));
      std::unique_lock<std::shared_mutex> writeLock(m_fdMutex);
      m_fdTable[fd].offset += bytesWritten;
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "SysWrite: Wrote {} bytes to FD {} from 0x{:x}, latency={}us",
          bytesWritten, fd, bufAddr, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysWrite failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysWrite failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysOpen() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t pathAddr = GetArg(0);
    int flags = static_cast<int>(GetArg(1));
    mode_t mode = static_cast<mode_t>(GetArg(2));
    std::string path;
    int fd;

    lock.unlock();
    path = ReadString(pathAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(pathAddr, path.size() + 1,
                                               false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysOpen: Invalid path address 0x" +
                                      std::to_string(pathAddr));
      return;
    }
    lock.lock();

    fd = AllocateFD();
    if (fd < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EMFILE, "SysOpen: No available file descriptors");
      return;
    }

#ifdef _WIN32
    int hostFlags = 0;
    if ((flags & O_ACCMODE) == O_RDONLY)
      hostFlags |= _O_RDONLY;
    if ((flags & O_ACCMODE) == O_WRONLY)
      hostFlags |= _O_WRONLY;
    if ((flags & O_ACCMODE) == O_RDWR)
      hostFlags |= _O_RDWR;
    if (flags & O_APPEND)
      hostFlags |= _O_APPEND;
    if (flags & O_CREAT)
      hostFlags |= _O_CREAT;
    if (flags & O_TRUNC)
      hostFlags |= _O_TRUNC;
    if (flags & O_EXCL)
      hostFlags |= _O_EXCL;
    hostFlags |= _O_BINARY;
    int pmode = _S_IREAD;
    if (mode & _S_IWRITE)
      pmode |= _S_IWRITE;
    int hostFd = ::open(path.c_str(), hostFlags, pmode);
#else
    int hostFd = ::open(path.c_str(), flags, mode);
#endif

    if (hostFd < 0) {
      FreeFD(fd);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysOpen: Failed to open {}: errno={}", path, errno);
    } else {
      m_fdTable[fd] = {true, hostFd, path, flags, static_cast<int>(mode), 0};
      m_fdTable[fd].cacheHits++;
      SetReturnValue(fd);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysOpen: Opened {} as FD {} with flags=0x{:x}, "
                   "mode=0x{:x}, latency={}us",
                   path, fd, flags, mode, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysOpen failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysOpen failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysClose() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysClose: Invalid file descriptor " +
                                     std::to_string(fd));
      return;
    }
    FreeFD(fd);
    SetReturnValue(0);
    m_fdTable[fd].cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysClose: Closed FD {}, latency={}us", fd, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysClose failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysClose failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysStat() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t pathAddr = GetArg(0);
    uint64_t statBufAddr = GetArg(1);
    std::string path;

    lock.unlock();
    path = ReadString(pathAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(pathAddr, path.size() + 1,
                                               false) ||
        !m_emulator.GetMMU().VirtualToPhysical(statBufAddr, sizeof(struct stat),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysStat: Invalid path or stat buffer address");
      return;
    }
    lock.lock();

    struct stat hostStat;
    if (::stat(path.c_str(), &hostStat) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysStat: Failed for path {}: errno={}", path, errno);
      return;
    }

    struct {
      uint32_t st_mode;
      uint32_t st_uid;
      uint32_t st_gid;
      uint64_t st_size;
      uint64_t st_atime;
      uint64_t st_mtime;
      uint64_t st_ctime;
    } ps4Stat;
    ps4Stat.st_mode = hostStat.st_mode;
    ps4Stat.st_uid = 1000;
    ps4Stat.st_gid = 1000;
    ps4Stat.st_size = hostStat.st_size;
    ps4Stat.st_atime = hostStat.st_atime;
    ps4Stat.st_mtime = hostStat.st_mtime;
    ps4Stat.st_ctime = hostStat.st_ctime;

    m_emulator.GetMMU().WriteVirtual(statBufAddr, &ps4Stat, sizeof(ps4Stat), 1);
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysStat: Stat for {} succeeded, size={}, latency={}us", path,
                  ps4Stat.st_size, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysStat failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysStat failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysFstat() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    uint64_t statBufAddr = GetArg(1);
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysFstat: Invalid file descriptor " +
                                     std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(statBufAddr, sizeof(struct stat),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysFstat: Invalid stat buffer address 0x" +
                                      std::to_string(statBufAddr));
      return;
    }

    struct stat hostStat;
    if (::fstat(hostFd, &hostStat) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysFstat: Failed for FD {}: errno={}", fd, errno);
      return;
    }

    struct {
      uint32_t st_mode;
      uint32_t st_uid;
      uint32_t st_gid;
      uint64_t st_size;
      uint64_t st_atime;
      uint64_t st_mtime;
      uint64_t st_ctime;
    } ps4Stat;
    ps4Stat.st_mode = hostStat.st_mode;
    ps4Stat.st_uid = 1000;
    ps4Stat.st_gid = 1000;
    ps4Stat.st_size = hostStat.st_size;
    ps4Stat.st_atime = hostStat.st_atime;
    ps4Stat.st_mtime = hostStat.st_mtime;
    ps4Stat.st_ctime = hostStat.st_ctime;

    m_emulator.GetMMU().WriteVirtual(statBufAddr, &ps4Stat, sizeof(ps4Stat), 1);
    SetReturnValue(0);
    m_fdTable[fd].cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysFstat: Stat for FD {} succeeded, size={}, latency={}us",
                  fd, ps4Stat.st_size, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysFstat failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysFstat failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysLseek() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    off_t offset = static_cast<off_t>(GetArg(1));
    int whence = static_cast<int>(GetArg(2));
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysLseek: Invalid file descriptor " +
                                     std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;
    lock.unlock();

    off_t newOffset = ::lseek(hostFd, offset, whence);
    if (newOffset < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error(
          "SysLseek: Failed for FD {}, offset={}, whence={}: errno={}", fd,
          offset, whence, errno);
    } else {
      std::unique_lock<std::shared_mutex> writeLock(m_fdMutex);
      m_fdTable[fd].offset = newOffset;
      SetReturnValue(static_cast<uint64_t>(newOffset));
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "SysLseek: Set offset to {} for FD {}, whence={}, latency={}us",
          newOffset, fd, whence, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysLseek failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysLseek failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetdents() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    uint64_t dirpAddr = GetArg(1);
    size_t count = static_cast<size_t>(GetArg(2));
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysGetdents: Invalid file descriptor " +
                                     std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(dirpAddr, count, true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysGetdents: Invalid dirp address 0x" +
                                      std::to_string(dirpAddr));
      return;
    }

#ifdef _WIN32
    // Emulate getdents on Windows
    std::vector<uint8_t> buffer(count);
    // Simplified emulation: return empty directory
    ssize_t bytesRead = 0;
#else
    std::vector<uint8_t> buffer(count);
    ssize_t bytesRead = ::getdents(
        hostFd, reinterpret_cast<struct dirent *>(buffer.data()), count);
#endif

    if (bytesRead < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysGetdents: Failed for FD {}: errno={}", fd, errno);
    } else {
      m_emulator.GetMMU().WriteVirtual(dirpAddr, buffer.data(),
                                       static_cast<size_t>(bytesRead), 1);
      SetReturnValue(static_cast<uint64_t>(bytesRead));
      std::unique_lock<std::shared_mutex> writeLock(m_fdMutex);
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysGetdents: Read {} bytes for FD {}, latency={}us",
                    bytesRead, fd, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetdents failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetdents failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysUnlink() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t pathAddr = GetArg(0);
    std::string path;

    lock.unlock();
    path = ReadString(pathAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(pathAddr, path.size() + 1,
                                               false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysUnlink: Invalid path address 0x" +
                                      std::to_string(pathAddr));
      return;
    }
    lock.lock();

    if (::unlink(path.c_str()) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(errno, "SysUnlink: Failed to unlink path " + path +
                                 ": errno=" + std::to_string(errno));
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysUnlink: Unlinked {}, latency={}us", path, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysUnlink failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysUnlink failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysChdir() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t pathAddr = GetArg(0);
    std::string path;

    lock.unlock();
    path = ReadString(pathAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(pathAddr, path.size() + 1,
                                               false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysChdir: Invalid path address 0x" +
                                      std::to_string(pathAddr));
      return;
    }
    lock.lock();

    if (::chdir(path.c_str()) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysChdir: Failed to change directory to {}: errno={}",
                    path, errno);
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysChdir: Changed directory to {}, latency={}us", path,
                   latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysChdir failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysChdir failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysMknod() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t pathAddr = GetArg(0);
    mode_t mode = static_cast<mode_t>(GetArg(1));
    dev_t dev = static_cast<dev_t>(GetArg(2));
    std::string path;

    lock.unlock();
    path = ReadString(pathAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(pathAddr, path.size() + 1,
                                               false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysMknod: Invalid path address 0x" +
                                      std::to_string(pathAddr));
      return;
    }
    lock.lock();

#ifdef _WIN32
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    SetErrorWithLog(PS4_ENOTSUP, "SysMknod: Not supported on Windows");
#else
    if (::mknod(path.c_str(), mode, dev) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysMknod: Failed to create node {}: errno={}", path,
                    errno);
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SysMknod: Created node {} with mode=0x{:x}, dev={}, latency={}us",
          path, mode, dev, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysMknod failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysMknod failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetpid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    lock.unlock();
    uint64_t pid = m_emulator.GetOrbisOS().SceKernelGetProcessId();
    lock.lock();
    SetReturnValue(pid);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGetpid: Returned PID {}, latency={}us", pid, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetpid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetpid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysKill() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    pid_t pid = static_cast<pid_t>(GetArg(0));
    int sig = static_cast<int>(GetArg(1));
    uint64_t currentPid;

    lock.unlock();
    currentPid = m_emulator.GetOrbisOS().SceKernelGetProcessId();
    lock.lock();

    if (pid != currentPid && pid != getpid()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ESRCH, "SysKill: Invalid PID " + std::to_string(pid));
      return;
    }

#ifndef _WIN32
    if (::kill(pid, sig) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysKill: Failed to send signal {} to PID {}: errno={}",
                    sig, pid, errno);
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      spdlog::info("SysKill: Sent signal {} to PID {}", sig, pid);
      if (sig == SIGKILL || sig == SIGTERM) {
        lock.unlock();
        m_emulator.GetOrbisOS().ExitProcess(0);
        lock.lock();
      }
    }
#else
    if (sig == SIGTERM || sig == SIGKILL) {
      lock.unlock();
      m_emulator.GetOrbisOS().ExitProcess(0);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("SysKill: Terminated process with signal {}, latency={}us",
                   sig, latency);
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysKill: Invalid signal " +
                                      std::to_string(sig) + " on Windows");
    }
#endif

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysKill: signal={}, pid={}, latency={}us", sig, pid,
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysKill failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysKill failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysDup() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int oldfd = static_cast<int>(GetArg(0));
    int newfd;
    int hostOldFd;

    if (oldfd < 0 || oldfd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[oldfd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF,
                      "SysDup: Invalid old FD " + std::to_string(oldfd));
      return;
    }
    hostOldFd = m_fdTable[oldfd].hostFd;
    newfd = AllocateFD();
    if (newfd < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EMFILE, "SysDup: No available file descriptors");
      return;
    }

    int hostNewFd = ::dup(hostOldFd);
    if (hostNewFd < 0) {
      FreeFD(newfd);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysDup: Failed to duplicate FD {}: errno={}", oldfd,
                    errno);
    } else {
      m_fdTable[newfd] = m_fdTable[oldfd];
      m_fdTable[newfd].hostFd = hostNewFd;
      m_fdTable[newfd].cacheHits++;
      SetReturnValue(newfd);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysDup: Duplicated FD {} to {}, latency={}us", oldfd, newfd,
                   latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysDup failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysDup failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetppid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGetppid: Returned PPID 0, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetppid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetppid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetuid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    SetReturnValue(1000);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGetuid: Returned UID 1000, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetuid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetuid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGeteuid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    SetReturnValue(1000);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGeteuid: Returned EUID 1000, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGeteuid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGeteuid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetgid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    SetReturnValue(1000);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGetgid: Returned GID 1000, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetgid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetgid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetegid() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    SetReturnValue(1000);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysGetegid: Returned EGID 1000, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetegid failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysGetegid failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysIoctl() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int fd = static_cast<int>(GetArg(0));
    uint64_t request = GetArg(1);
    uint64_t argp = GetArg(2);
    int hostFd;

    if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
        !m_fdTable[fd].isOpen) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EBADF, "SysIoctl: Invalid FD " + std::to_string(fd));
      return;
    }
    hostFd = m_fdTable[fd].hostFd;

#ifdef _WIN32
    // Emulate basic ioctl on Windows
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    SetErrorWithLog(PS4_ENOSYS, "SysIoctl: Not supported on Windows");
#else
    if (::ioctl(hostFd, request, reinterpret_cast<void *>(argp)) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysIoctl: Failed for FD {}, request=0x{:x}: errno={}", fd,
                    request, errno);
    } else {
      SetReturnValue(0);
      m_fdTable[fd].cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "SysIoctl: Executed for FD {}, request=0x{:x}, latency={}us", fd,
          request, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysIoctl failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysIoctl failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysReboot() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    SetErrorWithLog(PS4_EPERM, "SysReboot: Not permitted");
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysReboot: Not permitted, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysReboot failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysReboot failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysMmap() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t addr = GetArg(0);
    size_t len = static_cast<size_t>(GetArg(1));
    int prot = static_cast<int>(GetArg(2));
    int flags = static_cast<int>(GetArg(3));
    int fd = static_cast<int>(GetArg(4));
    off_t offset = static_cast<off_t>(GetArg(5));

    if (len == 0 || (addr % 4096 != 0 && addr != 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysMmap: Invalid length or address: len=" +
                                      std::to_string(len) + ", addr=0x" +
                                      std::to_string(addr));
      return;
    }

    int hostFd = -1;
    if (fd != -1) {
      if (fd < 0 || fd >= static_cast<int>(m_fdTable.size()) ||
          !m_fdTable[fd].isOpen) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EBADF, "SysMmap: Invalid FD " + std::to_string(fd));
        return;
      }
      hostFd = m_fdTable[fd].hostFd;
    }

    bool shared = (flags & MAP_SHARED) != 0;
    uint64_t mappedAddr =
        m_emulator.GetOrbisOS().AllocateVirtualMemory(len, 4096, shared);
    if (mappedAddr == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ENOMEM,
                      "SysMmap: Memory allocation failed for size " +
                          std::to_string(len));
    } else {
      if (prot != 0) {
        int ps4Prot = 0;
        if (prot & 0x1)
          ps4Prot |= ps4::MemoryProtection::PROT_READ;
        if (prot & 0x2)
          ps4Prot |= ps4::MemoryProtection::PROT_WRITE;
        if (prot & 0x4)
          ps4Prot |= ps4::MemoryProtection::PROT_EXEC;
        m_emulator.GetOrbisOS().ProtectMemory(mappedAddr, len, ps4Prot);
      }
      if (fd != -1 && !(flags & MAP_ANONYMOUS)) {
        std::vector<uint8_t> data(len);
        off_t currentOffset = ::lseek(hostFd, offset, SEEK_SET);
        if (currentOffset != offset) {
          m_emulator.GetOrbisOS().FreeVirtualMemory(mappedAddr);
          m_stats.errorCount++;
          m_stats.cacheMisses++;
          SetErrorWithLog(errno, "SysMmap: Failed to seek to offset " +
                                     std::to_string(offset));
          return;
        }
        ssize_t bytesRead = ::read(hostFd, data.data(), len);
        if (bytesRead < 0) {
          m_emulator.GetOrbisOS().FreeVirtualMemory(mappedAddr);
          m_stats.errorCount++;
          m_stats.cacheMisses++;
          SetErrorWithLog(errno, "SysMmap: Failed to read file data");
          return;
        }
        m_emulator.GetMMU().WriteVirtual(mappedAddr, data.data(),
                                         static_cast<size_t>(bytesRead), 1);
      }
      SetReturnValue(mappedAddr);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysMmap: Mapped {} bytes at 0x{:x}, prot=0x{:x}, "
                   "flags=0x{:x}, fd={}, offset={}, latency={}us",
                   len, mappedAddr, prot, flags, fd, offset, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysMmap failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysMmap failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysMunmap() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t addr = GetArg(0);
    size_t len = static_cast<size_t>(GetArg(1));
    if (len == 0 || addr % 4096 != 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysMunmap: Invalid length or address: len=" +
                                      std::to_string(len) + ", addr=0x" +
                                      std::to_string(addr));
      return;
    }

    m_emulator.GetOrbisOS().FreeVirtualMemory(addr);
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysMunmap: Unmapped {} bytes at 0x{:x}, latency={}us", len,
                 addr, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysMunmap failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysMunmap failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysBrk() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t addr = GetArg(0);
    uint64_t currentBrk = GetBrk();
    if (addr == 0) {
      SetReturnValue(currentBrk);
      m_stats.cacheHits++;
    } else if (SetBrk(addr)) {
      SetReturnValue(addr);
      m_stats.cacheHits++;
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ENOMEM, "SysBrk: Failed to set break to 0x" +
                                      std::to_string(addr));
      return;
    }
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysBrk: Set break to 0x{:x}, current=0x{:x}, latency={}us",
                  addr, currentBrk, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysBrk failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysBrk failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysRtSigaction() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int signum = static_cast<int>(GetArg(0));
    uint64_t actAddr = GetArg(1);
    uint64_t oldactAddr = GetArg(2);

    if (signum < 1 || signum > 64) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysRtSigaction: Invalid signal number " +
                                      std::to_string(signum));
      return;
    }

    struct sigaction {
      uint64_t sa_handler;
      uint64_t sa_flags;
      uint64_t sa_restorer;
      uint64_t sa_mask[2];
    } act, oldact;

    lock.unlock();
    if (actAddr != 0) {
      if (!m_emulator.GetMMU().VirtualToPhysical(actAddr, sizeof(act), false)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EFAULT, "SysRtSigaction: Invalid act address 0x" +
                                        std::to_string(actAddr));
        return;
      }
      m_emulator.GetMMU().ReadVirtual(actAddr, &act, sizeof(act), 1);
    }
    if (oldactAddr != 0) {
      if (!m_emulator.GetMMU().VirtualToPhysical(oldactAddr, sizeof(oldact),
                                                 true)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EFAULT,
                        "SysRtSigaction: Invalid oldact address 0x" +
                            std::to_string(oldactAddr));
        return;
      }
    }
    lock.lock();

    // Enhanced signal handling with proper storage
    if (actAddr != 0) {
      // Store signal handler in our enhanced structure
      SignalHandler &handler = m_signalHandlers[signum];
      handler.handler = act.sa_handler;
      handler.flags = act.sa_flags;
      handler.mask[0] = act.sa_mask[0];
      handler.mask[1] = act.sa_mask[1];
      handler.installed = true;

      spdlog::info("SysRtSigaction: Installed signal handler for signal {}, "
                   "handler=0x{:x}, flags=0x{:x}",
                   signum, act.sa_handler, act.sa_flags);
    }

    if (oldactAddr != 0) {
      // Return previous handler if it exists
      auto handlerIt = m_signalHandlers.find(signum);
      if (handlerIt != m_signalHandlers.end() && handlerIt->second.installed) {
        oldact.sa_handler = handlerIt->second.handler;
        oldact.sa_flags = handlerIt->second.flags;
        oldact.sa_restorer = 0; // Not used in PS4
        oldact.sa_mask[0] = handlerIt->second.mask[0];
        oldact.sa_mask[1] = handlerIt->second.mask[1];
      } else {
        // Return default handler
        oldact.sa_handler = 0; // SIG_DFL
        oldact.sa_flags = 0;
        oldact.sa_restorer = 0;
        oldact.sa_mask[0] = 0;
        oldact.sa_mask[1] = 0;
      }

      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(oldactAddr, &oldact, sizeof(oldact), 1);
      lock.lock();
    }

    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysRtSigaction: Handled signal {}, latency={}us", signum,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysRtSigaction failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysRtSigaction failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysRtSigprocmask() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int how = static_cast<int>(GetArg(0));
    uint64_t setAddr = GetArg(1);
    uint64_t oldsetAddr = GetArg(2);

    if (how != SIG_BLOCK && how != SIG_UNBLOCK && how != SIG_SETMASK) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysRtSigprocmask: Invalid how value " +
                                      std::to_string(how));
      return;
    }

    uint64_t set[2], oldset[2] = {0, 0};
    lock.unlock();
    if (setAddr != 0) {
      if (!m_emulator.GetMMU().VirtualToPhysical(setAddr, sizeof(set), false)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EFAULT, "SysRtSigprocmask: Invalid set address 0x" +
                                        std::to_string(setAddr));
        return;
      }
      m_emulator.GetMMU().ReadVirtual(setAddr, set, sizeof(set), 1);
    }
    if (oldsetAddr != 0) {
      if (!m_emulator.GetMMU().VirtualToPhysical(oldsetAddr, sizeof(oldset),
                                                 true)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EFAULT,
                        "SysRtSigprocmask: Invalid oldset address 0x" +
                            std::to_string(oldsetAddr));
        return;
      }
    }
    lock.lock();

    // Enhanced signal mask handling with proper storage
    if (oldsetAddr != 0) {
      // Return current signal mask
      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(oldsetAddr, m_signalMask,
                                       sizeof(m_signalMask), 1);
      lock.lock();
    }

    if (setAddr != 0) {
      uint64_t newMask[2];
      lock.unlock();
      m_emulator.GetMMU().ReadVirtual(setAddr, newMask, sizeof(newMask), 1);
      lock.lock();

      switch (how) {
      case SIG_BLOCK:
        // Add signals to mask
        m_signalMask[0] |= newMask[0];
        m_signalMask[1] |= newMask[1];
        spdlog::debug("SysRtSigprocmask: Blocked signals 0x{:x}:{:x}",
                      newMask[1], newMask[0]);
        break;

      case SIG_UNBLOCK:
        // Remove signals from mask
        m_signalMask[0] &= ~newMask[0];
        m_signalMask[1] &= ~newMask[1];
        spdlog::debug("SysRtSigprocmask: Unblocked signals 0x{:x}:{:x}",
                      newMask[1], newMask[0]);
        break;

      case SIG_SETMASK:
        // Set mask to new value
        m_signalMask[0] = newMask[0];
        m_signalMask[1] = newMask[1];
        spdlog::debug("SysRtSigprocmask: Set signal mask to 0x{:x}:{:x}",
                      newMask[1], newMask[0]);
        break;

      default:
        SetErrorWithLog(PS4_EINVAL, "SysRtSigprocmask: Invalid how parameter");
        return;
      }
    }

    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysRtSigprocmask: Handled how={}, latency={}us", how,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysRtSigprocmask failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysRtSigprocmask failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGettimeofday() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t tvAddr = GetArg(0);
    uint64_t tzAddr = GetArg(1);

    lock.unlock();
    if (tvAddr != 0 && !m_emulator.GetMMU().VirtualToPhysical(
                           tvAddr, sizeof(struct timeval), true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysGettimeofday: Invalid timeval address 0x" +
                          std::to_string(tvAddr));
      return;
    }
    if (tzAddr != 0) {
      // Define timezone struct size
      struct timezone {
        int tz_minuteswest;
        int tz_dsttime;
      };

      if (!m_emulator.GetMMU().VirtualToPhysical(
              tzAddr, sizeof(struct timezone), true)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        SetErrorWithLog(PS4_EFAULT,
                        "SysGettimeofday: Invalid timezone address 0x" +
                            std::to_string(tzAddr));
        return;
      }
    }
    lock.lock();

#ifdef _WIN32
    // not supported on Windows; return zero
    SetReturnValue(0);
    m_stats.cacheHits++;
    return;
#else
    struct timeval tv;
    // Define timezone struct since it's not standard across all systems
    struct timezone {
      int tz_minuteswest; // minutes west of Greenwich
      int tz_dsttime;     // type of DST correction
    };
    struct timezone tz;
    if (::gettimeofday(&tv, &tz) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysGettimeofday: Failed: errno={}", errno);
    } else {
      if (tvAddr != 0) {
        lock.unlock();
        m_emulator.GetMMU().WriteVirtual(tvAddr, &tv, sizeof(tv), 1);
        lock.lock();
      }
      if (tzAddr != 0) {
        lock.unlock();
        m_emulator.GetMMU().WriteVirtual(tzAddr, &tz, sizeof(tz), 1);
        lock.lock();
      }
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysGettimeofday: tv_sec={}, tv_usec={}, latency={}us",
                    tv.tv_sec, tv.tv_usec, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGettimeofday failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysGettimeofday failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysGetrusage() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    int who = static_cast<int>(GetArg(0));
    uint64_t usageAddr = GetArg(1);

    if (who != RUSAGE_SELF && who != RUSAGE_CHILDREN) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL,
                      std::string("SysGetrusage: Invalid who value ") +
                          std::to_string(who));
      return;
    }

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(usageAddr, sizeof(struct rusage),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysGetrusage: Invalid usage address 0x" +
                                      std::to_string(usageAddr));
      return;
    }
    lock.lock();

#ifdef _WIN32
    SetReturnValue(0);
    m_stats.cacheHits++;
    return;
#else
    struct rusage usage;
    if (::getrusage(who, &usage) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysGetrusage: Failed for who={}: errno={}", who, errno);
    } else {
      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(usageAddr, &usage, sizeof(usage), 1);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysGetrusage: who={}, utime={}s, latency={}us", who,
                    usage.ru_utime.tv_sec, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysGetrusage failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysGetrusage failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysClockGettime() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    clockid_t clk_id = static_cast<clockid_t>(GetArg(0));
    uint64_t tpAddr = GetArg(1);

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(tpAddr, sizeof(struct timespec),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysClockGettime: Invalid timespec address 0x" +
                          std::to_string(tpAddr));
      return;
    }
    lock.lock();

#ifdef _WIN32
    SetReturnValue(0);
    m_stats.cacheHits++;
    return;
#else
    struct timespec ts;
    if (::clock_gettime(clk_id, &ts) < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysClockGettime: Failed for clk_id={}: errno={}", clk_id,
                    errno);
    } else {
      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(tpAddr, &ts, sizeof(ts), 1);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysClockGettime: clk_id={}, sec={}, latency={}us", clk_id,
                    ts.tv_sec, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysClockGettime failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysClockGettime failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysNanosleep() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t reqAddr = GetArg(0);
    uint64_t remAddr = GetArg(1);

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(reqAddr, sizeof(struct timespec),
                                               false) ||
        (remAddr != 0 && !m_emulator.GetMMU().VirtualToPhysical(
                             remAddr, sizeof(struct timespec), true))) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysNanosleep: Invalid timespec address");
      return;
    }

    struct timespec req, rem;
    m_emulator.GetMMU().ReadVirtual(reqAddr, &req, sizeof(req), 1);
    lock.lock();

#ifdef _WIN32
    Sleep(static_cast<DWORD>(req.tv_sec * 1000 + req.tv_nsec / 1000000));
    SetReturnValue(0);
    m_stats.cacheHits++;
    return;
#else
    if (::nanosleep(&req, &rem) < 0) {
      if (remAddr != 0) {
        lock.unlock();
        m_emulator.GetMMU().WriteVirtual(remAddr, &rem, sizeof(rem), 1);
        lock.lock();
      }
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetError(errno);
      spdlog::error("SysNanosleep: Failed: errno={}", errno);
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("SysNanosleep: Slept for {}s, {}ns, latency={}us",
                    req.tv_sec, req.tv_nsec, latency);
    }
#endif
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysNanosleep failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysNanosleep failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelDlsym() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t handle = GetArg(0);
    uint64_t symbolAddr = GetArg(1);
    uint64_t resultAddr = GetArg(2);

    lock.unlock();
    std::string symbol = ReadString(symbolAddr);
    if (!m_emulator.GetMMU().VirtualToPhysical(symbolAddr, symbol.size() + 1,
                                               false) ||
        !m_emulator.GetMMU().VirtualToPhysical(resultAddr, sizeof(uint64_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT, "SysSceKernelDlsym: Invalid address");
      return;
    }
    lock.lock();

    // Implement basic symbol resolution for common PS4 system symbols
    uint64_t symbolAddress = 0;

    // Common PS4 system symbols that games might look for
    if (symbol == "sceKernelAllocateDirectMemory") {
      symbolAddress = 0x1000000; // Dummy address for memory allocation
    } else if (symbol == "sceKernelMapDirectMemory") {
      symbolAddress = 0x1000100; // Dummy address for memory mapping
    } else if (symbol == "sceKernelReleaseDirectMemory") {
      symbolAddress = 0x1000200; // Dummy address for memory release
    } else if (symbol == "sceGnmSubmitCommandBuffers") {
      symbolAddress = 0x2000000; // Dummy address for GPU command submission
    } else if (symbol == "sceGnmFlushGarlic") {
      symbolAddress = 0x2000100; // Dummy address for GPU flush
    } else if (symbol == "sceVideoOutOpen") {
      symbolAddress = 0x3000000; // Dummy address for video output
    } else if (symbol == "sceVideoOutSetFlipRate") {
      symbolAddress = 0x3000100; // Dummy address for flip rate
    } else if (symbol == "scePadInit") {
      symbolAddress = 0x4000000; // Dummy address for pad initialization
    } else if (symbol == "scePadOpen") {
      symbolAddress = 0x4000100; // Dummy address for pad open
    } else if (symbol == "sceAudioOutInit") {
      symbolAddress = 0x5000000; // Dummy address for audio initialization
    } else if (symbol == "sceAudioOutOpen") {
      symbolAddress = 0x5000100; // Dummy address for audio open
    } else {
      // Symbol not found
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ENOENT,
                      "SysSceKernelDlsym: Symbol not found: " + symbol);
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::warn("SysSceKernelDlsym: Symbol {} not found, latency={}us",
                   symbol, latency);
      return;
    }

    // Write the symbol address to the result
    lock.unlock();
    m_emulator.GetMMU().WriteVirtual(resultAddr, &symbolAddress,
                                     sizeof(symbolAddress), 1);
    lock.lock();

    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysSceKernelDlsym: Found symbol {} at 0x{:x}, latency={}us",
                 symbol, symbolAddress, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelDlsym failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelDlsym failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelUsleep() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    useconds_t usecs = static_cast<useconds_t>(GetArg(0));
    lock.unlock();

#ifdef _WIN32
    Sleep(usecs / 1000);
#else
    ::usleep(usecs);
#endif
    SetReturnValue(0);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysSceKernelUsleep: Slept for {}us, latency={}us", usecs,
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelUsleep failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelUsleep failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelGetCpuFreq() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t freqAddr = GetArg(0);
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(freqAddr, sizeof(uint32_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceKernelGetCpuFreq: Invalid frequency address 0x" +
                          std::to_string(freqAddr));
      return;
    }

    uint32_t freq = 1593000000; // Emulated PS4 CPU frequency (1.593 GHz)
    m_emulator.GetMMU().WriteVirtual(freqAddr, &freq, sizeof(freq), 1);
    SetReturnValue(0);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "SysSceKernelGetCpuFreq: Returned frequency {}Hz, latency={}us", freq,
        latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelGetCpuFreq failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelGetCpuFreq failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelGetProcessId() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    lock.unlock();
    uint64_t pid = m_emulator.GetOrbisOS().SceKernelGetProcessId();
    lock.lock();
    SetReturnValue(pid);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysSceKernelGetProcessId: Returned PID {}, latency={}us",
                  pid, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelGetProcessId failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceKernelGetProcessId failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelUuidCreate() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t uuidAddr = GetArg(0);
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(uuidAddr, sizeof(uint32_t) * 4,
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceKernelUuidCreate: Invalid UUID address 0x" +
                          std::to_string(uuidAddr));
      return;
    }

    uint32_t uuid[4] = {0x12345678, 0x90abcdef, 0x12345678,
                        0x90abcdef}; // Dummy UUID
    m_emulator.GetMMU().WriteVirtual(uuidAddr, uuid, sizeof(uuid), 1);
    SetReturnValue(0);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysSceKernelUuidCreate: Created UUID, latency={}us",
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelUuidCreate failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelUuidCreate failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelCreateFiber() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t fiberAddr = GetArg(0);
    uint64_t stackAddr = GetArg(1);
    size_t stackSize = static_cast<size_t>(GetArg(2));
    uint64_t entry = GetArg(3);
    uint64_t arg = GetArg(4);

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(fiberAddr, sizeof(uint64_t),
                                               true) ||
        !m_emulator.GetMMU().VirtualToPhysical(stackAddr, stackSize, true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_EFAULT,
          "SysSceKernelCreateFiber: Invalid fiber or stack address");
      return;
    }

    // Create fiber using the fiber manager
    FiberFunction function = [entry](uint64_t fiberArg) {
      reinterpret_cast<void (*)(uint64_t)>(entry)(fiberArg);
    };
    uint64_t fiberId = m_emulator.GetFiberManager().CreateFiber(
        "syscall_fiber", function, arg, 0, stackSize);

    lock.lock();
    if (fiberId == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ENOMEM,
                      "SysSceKernelCreateFiber: Failed to create fiber");
    } else {
      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(fiberAddr, &fiberId, sizeof(fiberId), 1);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceKernelCreateFiber: Created fiber {}, stack=0x{:x}, "
                   "size={}, latency={}us",
                   fiberId, stackAddr, stackSize, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelCreateFiber failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelCreateFiber failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelDeleteFiber() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t fiberId = GetArg(0);
    lock.unlock();

    bool result = m_emulator.GetFiberManager().DeleteFiber(fiberId);
    lock.lock();
    if (!result) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysSceKernelDeleteFiber: Invalid fiber ID " +
                                      std::to_string(fiberId));
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceKernelDeleteFiber: Deleted fiber {}, latency={}us",
                   fiberId, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelDeleteFiber failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelDeleteFiber failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelSwitchToFiber() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t fiberId = GetArg(0);
    lock.unlock();

    bool result = m_emulator.GetFiberManager().SwitchToFiber(fiberId);
    lock.lock();
    if (!result) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL,
                      "SysSceKernelSwitchToFiber: Invalid fiber ID " +
                          std::to_string(fiberId));
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SysSceKernelSwitchToFiber: Switched to fiber {}, latency={}us",
          fiberId, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelSwitchToFiber failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceKernelSwitchToFiber failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceNpTrophyInit() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    lock.unlock();
    bool result;
    try {
      m_emulator.GetTrophyManager().Initialize("");
      result = true;
    } catch (const std::exception &) {
      result = false;
    }
    lock.lock();
    if (!result) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL,
                      "SysSceNpTrophyInit: Failed to initialize trophy system");
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SysSceNpTrophyInit: Initialized trophy system, latency={}us",
          latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceNpTrophyInit failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceNpTrophyInit failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceNpTrophyCreateContext() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t contextAddr = GetArg(0);
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(contextAddr, sizeof(uint32_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_EFAULT,
          "SysSceNpTrophyCreateContext: Invalid context address 0x" +
              std::to_string(contextAddr));
      return;
    }

    // Create a trophy context (simplified implementation)
    uint32_t contextId = 1; // Simple context ID assignment

    if (contextId == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_ENOMEM,
                      "SysSceNpTrophyCreateContext: Failed to create context");
    } else {
      m_emulator.GetMMU().WriteVirtual(contextAddr, &contextId,
                                       sizeof(contextId), 1);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SysSceNpTrophyCreateContext: Created context {}, latency={}us",
          contextId, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceNpTrophyCreateContext failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceNpTrophyCreateContext failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceNpTrophyUnlockTrophy() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint32_t context = static_cast<uint32_t>(GetArg(0));
    uint32_t trophyId = static_cast<uint32_t>(GetArg(1));
    lock.unlock();

    bool result = m_emulator.GetTrophyManager().UnlockTrophy(
        std::to_string(context), trophyId);
    lock.lock();
    if (!result) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL,
                      "SysSceNpTrophyUnlockTrophy: Failed to unlock trophy " +
                          std::to_string(trophyId) + " in context " +
                          std::to_string(context));
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceNpTrophyUnlockTrophy: Unlocked trophy {} in context "
                   "{}, latency={}us",
                   trophyId, context, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceNpTrophyUnlockTrophy failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceNpTrophyUnlockTrophy failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceZlibDecompress() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t dstAddr = GetArg(0);
    uint64_t dstSizeAddr = GetArg(1);
    uint64_t srcAddr = GetArg(2);
    size_t srcSize = static_cast<size_t>(GetArg(3));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(dstAddr, 1, true) ||
        !m_emulator.GetMMU().VirtualToPhysical(dstSizeAddr, sizeof(size_t),
                                               true) ||
        !m_emulator.GetMMU().VirtualToPhysical(srcAddr, srcSize, false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceZlibDecompress: Invalid memory addresses");
      return;
    }

    size_t dstSize;
    m_emulator.GetMMU().ReadVirtual(dstSizeAddr, &dstSize, sizeof(dstSize), 1);
    std::vector<uint8_t> src(srcSize);
    std::vector<uint8_t> dst(dstSize);
    m_emulator.GetMMU().ReadVirtual(srcAddr, src.data(), srcSize, 1);

    int result = sceZlibDecompress(dst.data(), &dstSize, src.data(), srcSize);
    if (result < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysSceZlibDecompress: Decompression failed");
    } else {
      lock.lock();
      m_emulator.GetMMU().WriteVirtual(dstAddr, dst.data(), dstSize, 1);
      m_emulator.GetMMU().WriteVirtual(dstSizeAddr, &dstSize, sizeof(dstSize),
                                       1);
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SysSceZlibDecompress: Decompressed {} bytes to 0x{:x}, latency={}us",
          dstSize, dstAddr, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceZlibDecompress failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceZlibDecompress failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceZlibCompress() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t dstAddr = GetArg(0);
    uint64_t dstSizeAddr = GetArg(1);
    uint64_t srcAddr = GetArg(2);
    size_t srcSize = static_cast<size_t>(GetArg(3));
    int level = static_cast<int>(GetArg(4));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(dstAddr, 1, true) ||
        !m_emulator.GetMMU().VirtualToPhysical(dstSizeAddr, sizeof(size_t),
                                               true) ||
        !m_emulator.GetMMU().VirtualToPhysical(srcAddr, srcSize, false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceZlibCompress: Invalid memory addresses");
      return;
    }

    size_t dstSize;
    m_emulator.GetMMU().ReadVirtual(dstSizeAddr, &dstSize, sizeof(dstSize), 1);
    std::vector<uint8_t> src(srcSize);
    std::vector<uint8_t> dst(dstSize);
    m_emulator.GetMMU().ReadVirtual(srcAddr, src.data(), srcSize, 1);

    int result =
        sceZlibCompress(dst.data(), &dstSize, src.data(), srcSize, level);
    if (result < 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL, "SysSceZlibCompress: Compression failed");
    } else {
      lock.lock();
      m_emulator.GetMMU().WriteVirtual(dstAddr, dst.data(), dstSize, 1);
      m_emulator.GetMMU().WriteVirtual(dstSizeAddr, &dstSize, sizeof(dstSize),
                                       1);
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceZlibCompress: Compressed {} bytes to 0x{:x}, "
                   "level={}, latency={}us",
                   dstSize, dstAddr, level, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceZlibCompress failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceZlibCompress failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelReadTsc() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t tscAddr = GetArg(0);
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(tscAddr, sizeof(uint64_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceKernelReadTsc: Invalid TSC address 0x" +
                          std::to_string(tscAddr));
      return;
    }

    uint64_t tsc;
#if defined(__x86_64__) || defined(__i386__)
    unsigned int lo, hi;
    asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
    tsc = (static_cast<uint64_t>(hi) << 32) | lo;
#else
    tsc = 0;
#endif
    m_emulator.GetMMU().WriteVirtual(tscAddr, &tsc, sizeof(tsc), 1);
    SetReturnValue(0);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("SysSceKernelReadTsc: TSC=0x{:x}, latency={}us", tsc,
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelReadTsc failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceKernelReadTsc failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceKernelGetTscFrequency() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t freqAddr = GetArg(0);
    lock.unlock();

    if (!m_emulator.GetMMU().VirtualToPhysical(freqAddr, sizeof(uint64_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_EFAULT,
          "SysSceKernelGetTscFrequency: Invalid frequency address 0x" +
              std::to_string(freqAddr));
      return;
    }

    uint64_t freq = 1593000000; // Emulated PS4 TSC frequency (1.593 GHz)
    m_emulator.GetMMU().WriteVirtual(freqAddr, &freq, sizeof(freq), 1);
    SetReturnValue(0);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "SysSceKernelGetTscFrequency: Returned frequency {}Hz, latency={}us",
        freq, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceKernelGetTscFrequency failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceKernelGetTscFrequency failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceGnmSubmitCommandBuffers() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t bufferAddr = GetArg(0);
    size_t bufferSize = static_cast<size_t>(GetArg(1));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(bufferAddr, bufferSize, false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_EFAULT,
          "SysSceGnmSubmitCommandBuffers: Invalid buffer address 0x" +
              std::to_string(bufferAddr));
      return;
    }
    lock.lock();

    // Use CommandProcessor cache
    CommandProcessor &cmdProcessor = m_emulator.GetCommandProcessor();
    cmdProcessor.ProcessCommandBuffer(bufferAddr, bufferSize);
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysSceGnmSubmitCommandBuffers: Submitted buffer at 0x{:x}, "
                 "size={}, latency={}us",
                 bufferAddr, bufferSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceGnmSubmitCommandBuffers failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceGnmSubmitCommandBuffers failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceGnmCreateTiledSurface() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t surfaceAddr = GetArg(0);
    uint32_t width = static_cast<uint32_t>(GetArg(1));
    uint32_t height = static_cast<uint32_t>(GetArg(2));
    uint32_t format = static_cast<uint32_t>(GetArg(3));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(surfaceAddr, sizeof(uint64_t),
                                               true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_EFAULT,
          "SysSceGnmCreateTiledSurface: Invalid surface address 0x" +
              std::to_string(surfaceAddr));
      return;
    }
    lock.lock();

    uint64_t surfaceId = m_emulator.GetTileManager().CreateTiledSurface(
        width, height,
        /*depth*/ 1,
        /*mode*/ TileMode::TILED_2D,
        /*format*/ static_cast<TileFormat>(format),
        /*isRenderTarget*/ false,
        /*isDepthStencil*/ false);
    if (surfaceId == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(
          PS4_ENOMEM,
          "SysSceGnmCreateTiledSurface: Failed to create tiled surface");
    } else {
      lock.unlock();
      m_emulator.GetMMU().WriteVirtual(surfaceAddr, &surfaceId,
                                       sizeof(surfaceId), 1);
      lock.lock();
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceGnmCreateTiledSurface: Created surface {}, width={}, "
                   "height={}, format={}, latency={}us",
                   surfaceId, width, height, format, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceGnmCreateTiledSurface failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceGnmCreateTiledSurface failed: " +
                                    std::string(e.what()));
  }
}

void SyscallHandler::SysSceGnmLoadShader() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t shaderAddr = GetArg(0);
    size_t shaderSize = static_cast<size_t>(GetArg(1));
    GCNShaderType shaderType = static_cast<GCNShaderType>(GetArg(2));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(shaderAddr, shaderSize, false)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceGnmLoadShader: Invalid shader address 0x" +
                          std::to_string(shaderAddr));
      return;
    }

    std::vector<uint32_t> shaderCode(shaderSize / sizeof(uint32_t));
    m_emulator.GetMMU().ReadVirtual(shaderAddr, shaderCode.data(), shaderSize,
                                    1);

    // Use GNMShaderTranslator cache
    static GNMShaderTranslator translator;
    std::vector<uint32_t> spirvCode;
    std::string glslCode;
    uint64_t bytecodeHash = 0;
    for (const auto &word : shaderCode) {
      bytecodeHash ^= std::hash<uint32_t>{}(word);
    }
    if (!translator.GetCachedShader(bytecodeHash, shaderType, spirvCode,
                                    glslCode)) {
      spirvCode = translator.TranslateToSPIRV(shaderCode, shaderType);
    }

    // use the PS4GPU instance you’ve passed into your CommandProcessor:
    PS4GPU &gpu = m_emulator.GetGPU();
    bool result = gpu.CompileShader(
        spirvCode.data(), spirvCode.size() * sizeof(uint32_t), shaderType);
    lock.lock();
    if (!result) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EINVAL,
                      "SysSceGnmLoadShader: Failed to load shader, type=" +
                          std::to_string(static_cast<int>(shaderType)));
    } else {
      SetReturnValue(0);
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SysSceGnmLoadShader: Loaded shader at 0x{:x}, size={}, "
                   "type={}, latency={}us",
                   shaderAddr, shaderSize, static_cast<int>(shaderType),
                   latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceGnmLoadShader failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysSceGnmLoadShader failed: " + std::string(e.what()));
  }
}

void SyscallHandler::SysSceGnmMapComputeQueue() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint64_t queueAddr = GetArg(0);
    uint32_t queueSize = static_cast<uint32_t>(GetArg(1));

    lock.unlock();
    if (!m_emulator.GetMMU().VirtualToPhysical(queueAddr, queueSize, true)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      SetErrorWithLog(PS4_EFAULT,
                      "SysSceGnmMapComputeQueue: Invalid queue address 0x" +
                          std::to_string(queueAddr));
      return;
    }
    lock.lock();

    // Use CommandProcessor cache
    CommandProcessor &cmdProcessor = m_emulator.GetCommandProcessor();
    cmdProcessor.ProcessCommandBuffer(queueAddr, queueSize);
    SetReturnValue(0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SysSceGnmMapComputeQueue: Mapped queue at 0x{:x}, size={}, "
                 "latency={}us",
                 queueAddr, queueSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysSceGnmMapComputeQueue failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL, "SysSceGnmMapComputeQueue failed: " +
                                    std::string(e.what()));
  }
}

SyscallHandler::Stats SyscallHandler::GetStats() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

void SyscallHandler::SaveState(std::ostream &out) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    uint64_t fdTableSize = m_fdTable.size();
    out.write(reinterpret_cast<const char *>(&fdTableSize),
              sizeof(fdTableSize));
    for (const auto &fd : m_fdTable) {
      out.write(reinterpret_cast<const char *>(&fd.isOpen), sizeof(fd.isOpen));
      out.write(reinterpret_cast<const char *>(&fd.hostFd), sizeof(fd.hostFd));
      uint32_t pathLen = static_cast<uint32_t>(fd.path.size());
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(fd.path.data(), pathLen);
      out.write(reinterpret_cast<const char *>(&fd.flags), sizeof(fd.flags));
      out.write(reinterpret_cast<const char *>(&fd.mode), sizeof(fd.mode));
      out.write(reinterpret_cast<const char *>(&fd.offset), sizeof(fd.offset));
      out.write(reinterpret_cast<const char *>(&fd.cacheHits),
                sizeof(fd.cacheHits));
      out.write(reinterpret_cast<const char *>(&fd.cacheMisses),
                sizeof(fd.cacheMisses));
    }

    uint64_t syscallTableSize = m_syscallTable.size();
    out.write(reinterpret_cast<const char *>(&syscallTableSize),
              sizeof(syscallTableSize));
    // Note: SyscallFunc cannot be serialized, only count is saved
    out.write(reinterpret_cast<const char *>(&m_maxFds), sizeof(m_maxFds));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw SyscallException("Failed to write syscall handler state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved syscall handler state, fd_count={}, "
                 "syscall_count={}, latency={}us",
                 fdTableSize, syscallTableSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw SyscallException("SaveState failed: " + std::string(e.what()));
  }
}

void SyscallHandler::LoadState(std::istream &in) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw SyscallException("Unsupported syscall handler state version: " +
                             std::to_string(version));
    }

    uint64_t fdTableSize;
    in.read(reinterpret_cast<char *>(&fdTableSize), sizeof(fdTableSize));
    m_fdTable.resize(fdTableSize);
    for (auto &fd : m_fdTable) {
      in.read(reinterpret_cast<char *>(&fd.isOpen), sizeof(fd.isOpen));
      in.read(reinterpret_cast<char *>(&fd.hostFd), sizeof(fd.hostFd));
      uint32_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      fd.path.resize(pathLen);
      in.read(fd.path.data(), pathLen);
      in.read(reinterpret_cast<char *>(&fd.flags), sizeof(fd.flags));
      in.read(reinterpret_cast<char *>(&fd.mode), sizeof(fd.mode));
      in.read(reinterpret_cast<char *>(&fd.offset), sizeof(fd.offset));
      in.read(reinterpret_cast<char *>(&fd.cacheHits), sizeof(fd.cacheHits));
      in.read(reinterpret_cast<char *>(&fd.cacheMisses),
              sizeof(fd.cacheMisses));
    }

    uint64_t syscallTableSize;
    in.read(reinterpret_cast<char *>(&syscallTableSize),
            sizeof(syscallTableSize));
    // Note: SyscallFunc cannot be deserialized, re-register syscalls
    RegisterAllSyscalls();
    in.read(reinterpret_cast<char *>(&m_maxFds), sizeof(m_maxFds));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw SyscallException("Failed to read syscall handler state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded syscall handler state, fd_count={}, "
                 "syscall_count={}, latency={}us",
                 fdTableSize, syscallTableSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw SyscallException("LoadState failed: " + std::string(e.what()));
  }
}

// Enhanced syscall validation and implementation methods

/**
 * @brief Validates syscall arguments according to specification.
 * @param argInfo Vector of argument information.
 * @return True if all arguments are valid, false otherwise.
 */
bool SyscallHandler::ValidateArguments(
    const std::vector<ArgumentInfo> &argInfo) {
  try {
    for (size_t i = 0; i < argInfo.size(); ++i) {
      uint64_t arg = GetArg(i);
      const ArgumentInfo &info = argInfo[i];

      switch (info.type) {
      case ArgumentType::Pointer:
        if (!info.nullable && arg == 0) {
          spdlog::warn("ValidateArguments: Null pointer at argument {}", i);
          return false;
        }
        if (arg != 0 && !ValidatePointer(arg, info.bufferSize)) {
          spdlog::warn(
              "ValidateArguments: Invalid pointer 0x{:x} at argument {}", arg,
              i);
          return false;
        }
        break;

      case ArgumentType::Integer:
        if (arg < info.minValue || arg > info.maxValue) {
          spdlog::warn("ValidateArguments: Integer {} out of range [{}, {}] at "
                       "argument {}",
                       arg, info.minValue, info.maxValue, i);
          return false;
        }
        break;

      case ArgumentType::String:
        if (!info.nullable && arg == 0) {
          spdlog::warn("ValidateArguments: Null string at argument {}", i);
          return false;
        }
        if (arg != 0 && !ValidateString(arg, info.bufferSize)) {
          spdlog::warn("ValidateArguments: Invalid string at argument {}", i);
          return false;
        }
        break;

      case ArgumentType::Buffer:
        if (!info.nullable && arg == 0) {
          spdlog::warn("ValidateArguments: Null buffer at argument {}", i);
          return false;
        }
        if (arg != 0 && !ValidateBuffer(arg, info.bufferSize)) {
          spdlog::warn("ValidateArguments: Invalid buffer at argument {}", i);
          return false;
        }
        break;

      case ArgumentType::FileDescriptor:
        if (!ValidateFileDescriptor(static_cast<int>(arg))) {
          spdlog::warn(
              "ValidateArguments: Invalid file descriptor {} at argument {}",
              arg, i);
          return false;
        }
        break;
      }
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateArguments failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates a pointer for memory access.
 * @param ptr Pointer address.
 * @param size Size of memory region.
 * @param write True if write access is required.
 * @return True if pointer is valid, false otherwise.
 */
bool SyscallHandler::ValidatePointer(uint64_t ptr, size_t size, bool write) {
  try {
    if (ptr == 0) {
      return false;
    }

    // Check if pointer is in valid memory range
    if (ptr < 0x1000 || ptr > 0x7FFFFFFFFFFF) {
      spdlog::debug("ValidatePointer: Pointer 0x{:x} out of valid range", ptr);
      return false;
    }

    // Check if memory region is mapped and accessible
    uint64_t physAddr = m_emulator.GetMMU().VirtualToPhysical(ptr, 1, write);
    if (physAddr == 0 || physAddr == ALLOC_FAILED) {
      spdlog::debug("ValidatePointer: Pointer 0x{:x} not mapped", ptr);
      return false;
    }

    // For larger regions, check multiple pages
    if (size > 4096) {
      for (size_t offset = 4096; offset < size; offset += 4096) {
        uint64_t checkAddr = ptr + offset;
        uint64_t checkPhys =
            m_emulator.GetMMU().VirtualToPhysical(checkAddr, 1, write);
        if (checkPhys == 0 || checkPhys == ALLOC_FAILED) {
          spdlog::debug(
              "ValidatePointer: Pointer region 0x{:x}+{} not fully mapped", ptr,
              offset);
          return false;
        }
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidatePointer failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates a file descriptor.
 * @param fd File descriptor.
 * @return True if valid, false otherwise.
 */
bool SyscallHandler::ValidateFileDescriptor(int fd) {
  std::shared_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    if (fd < 0 || static_cast<size_t>(fd) >= m_fdTable.size()) {
      return false;
    }
    return m_fdTable[fd].isOpen;
  } catch (const std::exception &e) {
    spdlog::error("ValidateFileDescriptor failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates a string pointer.
 * @param strPtr String pointer.
 * @param maxLen Maximum string length.
 * @return True if valid, false otherwise.
 */
bool SyscallHandler::ValidateString(uint64_t strPtr, size_t maxLen) {
  try {
    if (!ValidatePointer(strPtr, 1, false)) {
      return false;
    }

    // Check string is null-terminated within maxLen
    for (size_t i = 0; i < maxLen; ++i) {
      char c;
      if (!m_emulator.GetMMU().ReadVirtual(strPtr + i, &c, 1, 1)) {
        return false;
      }
      if (c == '\0') {
        return true;
      }
    }

    spdlog::debug(
        "ValidateString: String at 0x{:x} not null-terminated within {} bytes",
        strPtr, maxLen);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("ValidateString failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates a buffer pointer.
 * @param bufPtr Buffer pointer.
 * @param size Buffer size.
 * @param write True if write access is required.
 * @return True if valid, false otherwise.
 */
bool SyscallHandler::ValidateBuffer(uint64_t bufPtr, size_t size, bool write) {
  try {
    if (size == 0) {
      return true; // Zero-size buffer is always valid
    }
    return ValidatePointer(bufPtr, size, write);
  } catch (const std::exception &e) {
    spdlog::error("ValidateBuffer failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Enhanced mmap implementation with proper validation.
 */
void SyscallHandler::SysAdvancedMmap() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fdMutex);
  try {
    std::vector<ArgumentInfo> argInfo;
    argInfo.push_back(
        {ArgumentType::Pointer, 0, UINT64_MAX, true, 0}); // addr (nullable)
    argInfo.push_back({ArgumentType::Integer, 1, SIZE_MAX, false, 0}); // length
    argInfo.push_back({ArgumentType::Integer, 0, 7, false, 0}); // prot (PROT_*)
    argInfo.push_back({ArgumentType::Integer, 0, 0xFFFF, false, 0}); // flags
    argInfo.push_back({ArgumentType::FileDescriptor, static_cast<size_t>(-1),
                       INT_MAX, false, 0}); // fd (-1 for anonymous)
    argInfo.push_back(
        {ArgumentType::Integer, 0, UINT64_MAX, false, 0}); // offset

    if (!ValidateArguments(argInfo)) {
      SetErrorWithLog(PS4_EINVAL, "SysAdvancedMmap: Invalid arguments");
      return;
    }

    uint64_t addr = GetArg(0);
    size_t length = static_cast<size_t>(GetArg(1));
    int prot = static_cast<int>(GetArg(2));
    int flags = static_cast<int>(GetArg(3));
    int fd = static_cast<int>(GetArg(4));
    off_t offset = static_cast<off_t>(GetArg(5));

    // Validate protection flags
    if (prot & ~(PROT_READ | PROT_WRITE | PROT_EXEC)) {
      SetErrorWithLog(PS4_EINVAL, "SysAdvancedMmap: Invalid protection flags");
      return;
    }

    // Validate flags
    const int validFlags = MAP_SHARED | MAP_PRIVATE | MAP_FIXED | MAP_ANONYMOUS;
    if (flags & ~validFlags) {
      SetErrorWithLog(PS4_EINVAL, "SysAdvancedMmap: Invalid flags");
      return;
    }

    // Check for conflicting flags
    if ((flags & MAP_SHARED) && (flags & MAP_PRIVATE)) {
      SetErrorWithLog(
          PS4_EINVAL,
          "SysAdvancedMmap: MAP_SHARED and MAP_PRIVATE are mutually exclusive");
      return;
    }

    // Validate file descriptor for non-anonymous mappings
    if (!(flags & MAP_ANONYMOUS)) {
      if (fd == -1 || !ValidateFileDescriptor(fd)) {
        SetErrorWithLog(PS4_EBADF, "SysAdvancedMmap: Invalid file descriptor "
                                   "for non-anonymous mapping");
        return;
      }
    }

    lock.unlock();
    uint64_t result = m_emulator.GetOrbisOS().AllocateVirtualMemory(
        length, 4096, flags & MAP_SHARED);
    lock.lock();

    if (result == 0) {
      SetErrorWithLog(PS4_ENOMEM, "SysAdvancedMmap: Failed to allocate memory");
      return;
    }

    // Store mapping for validation
    m_memoryMappings[result] = length;

    SetReturnValue(result);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::info(
        "SysAdvancedMmap: Mapped {} bytes at 0x{:x}, prot={}, flags={}", length,
        result, prot, flags);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SysAdvancedMmap failed: {}", e.what());
    SetErrorWithLog(PS4_EINVAL,
                    "SysAdvancedMmap failed: " + std::string(e.what()));
  }
}

} // namespace ps4