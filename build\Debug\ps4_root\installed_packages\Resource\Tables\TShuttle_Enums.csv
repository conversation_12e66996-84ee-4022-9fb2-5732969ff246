RSID_TSHUTTLE_START, 3575,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], <PERSON><PERSON>hu<PERSON>\PBTSpaceShuttle, 1,
 ,[Offset], SpaceShuttle\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON>huttle\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON>hu<PERSON>\InstructionsITAL, 4,
 ,[Offset], SpaceShuttle\InstructionsGERM, 5,
 ,[Offset], SpaceShuttle\InstructionsSPAN, 6,
 ,[Offset], SpaceShuttle\InstructionsPORT, 7,
 ,[Offset], SpaceShuttle\InstructionsDUTCH, 8,
 ,[Offset], SpaceShuttle\InstructionsDUTCH, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
 ,[Offset], <PERSON><PERSON>huttle, 17,
RSID_TSHUTTLE_LIGHTS, 3576,,,
RSID_TSHUTTLE_CAMERAS, 3577,,,
RSID_TSHUTTLE_LAMP_TEXTURES, 3578,,,
 ,[Offset], SpaceShuttleCameras, 0,
 ,[Offset], SpaceShuttleCameras, 1,
 ,[Offset], SpaceShuttleCameras, 2,
 ,[Offset], SpaceShuttleCameras, 3,
 ,[Offset], SpaceShuttleCameras, 4,
 ,[Offset], SpaceShuttleCameras, 5,
 ,[Offset], SpaceShuttleCameras, 6,
 ,[Offset], SpaceShuttleCameras, 7,
 ,[Offset], SpaceShuttleCameras, 8,
 ,[Offset], SpaceShuttleCameras, 9,
 ,[Offset], SpaceShuttleCameras, 10,
 ,[Offset], SpaceShuttleCameras, 11,
 ,[Offset], lamp007_off, 12,
 ,[Offset], lamp007_on, 13,
 ,[Offset], lamp008_off, 14,
 ,[Offset], lamp008_on, 15,
 ,[Offset], lamp009_off, 16,
 ,[Offset], lamp009_on, 17,
 ,[Offset], lamp010_off, 18,
 ,[Offset], lamp010_on, 19,
 ,[Offset], lamp011_off, 20,
 ,[Offset], lamp011_on, 21,
 ,[Offset], lamp012_off, 22,
 ,[Offset], lamp012_on, 23,
 ,[Offset], lamp013_off, 24,
 ,[Offset], lamp013_on, 25,
 ,[Offset], lamp014_off, 26,
 ,[Offset], lamp014_on, 27,
 ,[Offset], lamp015_off, 28,
 ,[Offset], lamp015_on, 29,
 ,[Offset], lamp016_off, 30,
 ,[Offset], lamp016_on, 31,
 ,[Offset], lamp017_off, 32,
 ,[Offset], lamp017_on, 33,
 ,[Offset], lamp018_off, 34,
 ,[Offset], lamp018_on, 35,
 ,[Offset], lamp019_off, 36,
 ,[Offset], lamp019_on, 37,
 ,[Offset], lamp020_off, 38,
 ,[Offset], lamp020_on, 39,
 ,[Offset], lamp021_off, 40,
 ,[Offset], lamp021_on, 41,
 ,[Offset], lamp022_off, 42,
 ,[Offset], lamp022_on, 43,
 ,[Offset], lamp023_off, 44,
 ,[Offset], lamp023_on, 45,
 ,[Offset], lamp024_off, 46,
 ,[Offset], lamp024_on, 47,
 ,[Offset], lamp025_bumper_off, 48,
 ,[Offset], lamp025_bumper_on, 49,
 ,[Offset], lamp026_bumper_off, 50,
 ,[Offset], lamp026_bumper_on, 51,
 ,[Offset], lamp027_bumper_off, 52,
 ,[Offset], lamp027_bumper_on, 53,
 ,[Offset], lamp028_off, 54,
 ,[Offset], lamp028_on, 55,
 ,[Offset], lamp029_off, 56,
 ,[Offset], lamp029_on, 57,
 ,[Offset], lamp030_off, 58,
 ,[Offset], lamp030_on, 59,
 ,[Offset], lamp031_off, 60,
 ,[Offset], lamp031_on, 61,
 ,[Offset], lamp032_off, 62,
 ,[Offset], lamp032_on, 63,
 ,[Offset], lamp033_off, 64,
 ,[Offset], lamp033_on, 65,
 ,[Offset], lamp034_off, 66,
 ,[Offset], lamp034_on, 67,
 ,[Offset], lamp035_off, 68,
 ,[Offset], lamp035_on, 69,
 ,[Offset], lamp036_off, 70,
 ,[Offset], lamp036_on, 71,
 ,[Offset], lamp037_off, 72,
 ,[Offset], lamp037_on, 73,
 ,[Offset], lamp037_on, 74,
 ,[Offset], lamp037_on, 75,
 ,[Offset], lamp039_off, 76,
 ,[Offset], lamp039_on, 77,
 ,[Offset], lamp040_off, 78,
 ,[Offset], lamp040_on, 79,
 ,[Offset], lamp041_off, 80,
 ,[Offset], lamp041_on, 81,
 ,[Offset], lamp042_off, 82,
 ,[Offset], lamp042_on, 83,
 ,[Offset], lamp043_off, 84,
 ,[Offset], lamp043_on, 85,
 ,[Offset], lamp044_off, 86,
 ,[Offset], lamp044_on, 87,
 ,[Offset], lamp045_off, 88,
 ,[Offset], lamp045_on, 89,
 ,[Offset], lamp046_off, 90,
 ,[Offset], lamp046_on, 91,
 ,[Offset], lamp047_off, 92,
 ,[Offset], lamp047_on, 93,
 ,[Offset], lamp048_off, 94,
 ,[Offset], lamp048_on, 95,
 ,[Offset], lamp049_off, 96,
 ,[Offset], lamp049_on, 97,
 ,[Offset], lamp050_off, 98,
 ,[Offset], lamp050_on, 99,
 ,[Offset], lamp051_off, 100,
 ,[Offset], lamp051_on, 101,
 ,[Offset], lamp052_off, 102,
 ,[Offset], lamp052_on, 103,
 ,[Offset], lamp053_off, 104,
 ,[Offset], lamp053_on, 105,
 ,[Offset], lamp054_off, 106,
 ,[Offset], lamp054_on, 107,
 ,[Offset], lamp055_off, 108,
 ,[Offset], lamp055_on, 109,
 ,[Offset], lamp056_off, 110,
 ,[Offset], lamp056_on, 111,
 ,[Offset], lamp057_off, 112,
 ,[Offset], lamp057_on, 113,
 ,[Offset], lamp058_off, 114,
 ,[Offset], lamp058_on, 115,
 ,[Offset], lamp059_off, 116,
 ,[Offset], lamp059_on, 117,
 ,[Offset], lamp060_off, 118,
 ,[Offset], lamp060_on, 119,
 ,[Offset], lamp061_off, 120,
 ,[Offset], lamp061_on, 121,
 ,[Offset], lamp062_off, 122,
 ,[Offset], lamp062_on, 123,
 ,[Offset], lamp063_off, 124,
 ,[Offset], lamp063_on, 125,
 ,[Offset], lamp064_off, 126,
 ,[Offset], lamp064_on, 127,
RSID_TSHUTTLE_TEXTURES, 3579,,,
 ,[Offset], SpaceShuttle_LED_Border, 0,
 ,[Offset], Backglass, 1,
 ,[Offset], Backglass_all, 2,
 ,[Offset], beige, 3,
 ,[Offset], bolt-tp01 copy, 4,
 ,[Offset], bumper_A_0, 5,
 ,[Offset], bumper_B_0, 6,
 ,[Offset], bumper_C_0, 7,
 ,[Offset], bumper_DE_0, 8,
 ,[Offset], Buttons_Parts, 9,
 ,[Offset], CoinSlots, 10,
 ,[Offset], flag, 11,
 ,[Offset], flipper, 12,
 ,[Offset], frontparts, 13,
 ,[Offset], GorGar_Table_down_B, 14,
 ,[Offset], inst_A1_0, 15,
 ,[Offset], inst_A2_0, 16,
 ,[Offset], inst_A3_0, 17,
 ,[Offset], inst_A4_0, 18,
 ,[Offset], inst_B1_0, 19,
 ,[Offset], inst_B2_0, 20,
 ,[Offset], inst_B3_0, 21,
 ,[Offset], inst_B4_0, 22,
 ,[Offset], Metal front, 23,
 ,[Offset], metal_legs, 24,
 ,[Offset], metal_parts, 25,
 ,[Offset], metal_parts01, 26,
 ,[Offset], metal_parts02 copy, 27,
 ,[Offset], metal_parts02, 28,
 ,[Offset], metal_trim, 29,
 ,[Offset], metal_walls, 30,
 ,[Offset], metal-parts01 copy, 31,
 ,[Offset], nut01, 32,
 ,[Offset], playfield_bottom, 33,
 ,[Offset], playfield_full, 34,
 ,[Offset], playfield_top, 35,
 ,[Offset], plunger, 36,
 ,[Offset], plunger_Metal, 37,
 ,[Offset], Plunger_Plate_Baked, 38,
 ,[Offset], pop_bumpers_off, 39,
 ,[Offset], pop_bumpers_on, 40,
 ,[Offset], post, 41,
 ,[Offset], post-band-tp01 copy, 42,
 ,[Offset], Rails, 43,
 ,[Offset], red plastic gates, 44,
 ,[Offset], rubber color, 45,
 ,[Offset], rubber, 46,
 ,[Offset], screw white, 47,
 ,[Offset], screw, 48,
 ,[Offset], shuttle_body, 49,
 ,[Offset], shuttle_wing, 50,
 ,[Offset], Silver_screws, 51,
 ,[Offset], spinner, 52,
 ,[Offset], SS_sign, 53,
 ,[Offset], SS_TableFront_Side, 54,
 ,[Offset], target, 55,
 ,[Offset], tile, 56,
 ,[Offset], tile2, 57,
 ,[Offset], tmp_gray, 58,
 ,[Offset], tmp_orange, 59,
 ,[Offset], tube, 60,
 ,[Offset], white_nut, 61,
 ,[Offset], wooden_rails, 62,
 ,[Offset], Plunger01, 63,
 ,[Offset], floor, 64,
 ,[Offset], arcs, 65,
 ,[Offset], clear, 66,
 ,[Offset], Glass_Hammered_1, 67,
 ,[Offset], jelly, 68,
 ,[Offset], light-strand-tp01 copy, 69,
 ,[Offset], post red, 70,
 ,[Offset], slingshot_plastics, 71,
 ,[Offset], ST1_0, 72,
 ,[Offset], ST2_0, 73,
 ,[Offset], ST3_0, 74,
 ,[Offset], ST4_0, 75,
 ,[Offset], transparent_gates, 76,
 ,[Offset], x_light, 77,
 ,[Offset], inside_walls, 78,
RSID_TSHUTTLE_MODELS, 3580,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass_moble, 1,
 ,[Offset], black_posts, 2,
 ,[Offset], bulbs, 3,
 ,[Offset], buttons, 4,
 ,[Offset], cabinet, 5,
 ,[Offset], cabinet_metal, 6,
 ,[Offset], lights, 7,
 ,[Offset], metal_parts, 8,
 ,[Offset], metal_walls, 9,
 ,[Offset], plastic_gates, 10,
 ,[Offset], plastic_nuts, 11,
 ,[Offset], plastics, 12,
 ,[Offset], playfield, 13,
 ,[Offset], pop_bumpers, 14,
 ,[Offset], rails, 15,
 ,[Offset], ramp, 16,
 ,[Offset], red_posts, 17,
 ,[Offset], screws, 18,
 ,[Offset], space_shuttle, 19,
 ,[Offset], transparent_plastics, 20,
 ,[Offset], white_rubber, 21,
 ,[Offset], wooden_rails, 22,
 ,[Offset], yellow_rubber, 23,
 ,[Offset], bumper, 24,
 ,[Offset], flipper right, 25,
 ,[Offset], gate, 26,
 ,[Offset], one_way_gate_A, 27,
 ,[Offset], one_way_gate_B, 28,
 ,[Offset], plunger, 29,
 ,[Offset], pop_up_flasher, 30,
 ,[Offset], pop_up_tile, 31,
 ,[Offset], right_slingshot, 32,
 ,[Offset], spinner, 33,
 ,[Offset], target, 34,
 ,[Offset], tile, 35,
 ,[Offset], trap, 36,
 ,[Offset], wire, 37,
 ,[Offset], wire_gate, 38,
 ,[Offset], flipper left, 39,
 ,[Offset], left_slingshot, 40,
 ,[Offset], pop_bumper_metal, 41,
RSID_TSHUTTLE_MODELS_LODS, 3581,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass_moble, 1,
 ,[Offset], black_posts, 2,
 ,[Offset], bulbs, 3,
 ,[Offset], buttons, 4,
 ,[Offset], cabinet, 5,
 ,[Offset], cabinet_metal, 6,
 ,[Offset], lights, 7,
 ,[Offset], metal_parts, 8,
 ,[Offset], metal_walls, 9,
 ,[Offset], plastic_gates, 10,
 ,[Offset], plastic_nuts, 11,
 ,[Offset], plastics, 12,
 ,[Offset], playfield, 13,
 ,[Offset], pop_bumpers, 14,
 ,[Offset], rails, 15,
 ,[Offset], ramp, 16,
 ,[Offset], red_posts, 17,
 ,[Offset], screws, 18,
 ,[Offset], space_shuttle, 19,
 ,[Offset], transparent_plastics, 20,
 ,[Offset], white_rubber, 21,
 ,[Offset], wooden_rails, 22,
 ,[Offset], yellow_rubber, 23,
 ,[Offset], bumper, 24,
 ,[Offset], flipper right, 25,
 ,[Offset], gate, 26,
 ,[Offset], one_way_gate_A, 27,
 ,[Offset], one_way_gate_B, 28,
 ,[Offset], plunger, 29,
 ,[Offset], pop_up_flasher, 30,
 ,[Offset], pop_up_tile, 31,
 ,[Offset], right_slingshot, 32,
 ,[Offset], spinner, 33,
 ,[Offset], target, 34,
 ,[Offset], tile, 35,
 ,[Offset], trap, 36,
 ,[Offset], wire, 37,
 ,[Offset], wire_gate, 38,
 ,[Offset], flipper left, 39,
 ,[Offset], left_slingshot, 40,
 ,[Offset], pop_bumper_metal, 41,
RSID_TSHUTTLE_COLLISION, 3582,,,
 ,[Offset], ball_drain, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper C, 3,
 ,[Offset], bumper D, 4,
 ,[Offset], bumper E, 5,
 ,[Offset], bumper F, 6,
 ,[Offset], flipper left back, 7,
 ,[Offset], flipper left front, 8,
 ,[Offset], floor, 9,
 ,[Offset], gate_col, 10,
 ,[Offset], left slingshot, 11,
 ,[Offset], left_trap, 12,
 ,[Offset], left_trap_lane, 13,
 ,[Offset], left_wall, 14,
 ,[Offset], one_way_gate_A_back, 15,
 ,[Offset], one_way_gate_A_front, 16,
 ,[Offset], one_way_gate_B_back, 17,
 ,[Offset], one_way_gate_B_front, 18,
 ,[Offset], plastic_walls, 19,
 ,[Offset], plunger_col, 20,
 ,[Offset], plunger_lane, 21,
 ,[Offset], pop_bumper_col, 22,
 ,[Offset], pop_up_flasher_col, 23,
 ,[Offset], pop_up_tile_col, 24,
 ,[Offset], ramp, 25,
 ,[Offset], right flipper back, 26,
 ,[Offset], right flipper front, 27,
 ,[Offset], right slingshot, 28,
 ,[Offset], right_trap, 29,
 ,[Offset], right_trap_lane, 30,
 ,[Offset], rubber_stopper, 31,
 ,[Offset], rubber1, 32,
 ,[Offset], rubber2, 33,
 ,[Offset], rubber3, 34,
 ,[Offset], rubber4, 35,
 ,[Offset], rubber5, 36,
 ,[Offset], rubber6, 37,
 ,[Offset], spinner_col, 38,
 ,[Offset], target_col, 39,
 ,[Offset], tile_col, 40,
 ,[Offset], top_arc, 41,
 ,[Offset], wall1, 42,
 ,[Offset], wall2, 43,
 ,[Offset], wall3, 44,
 ,[Offset], wall4, 45,
 ,[Offset], wire_gate, 46,
 ,[Offset], trapA_wall, 47,
 ,[Offset], trapB_wall, 48,
RSID_TSHUTTLE_PLACEMENT, 3583,,,
RSID_TSHUTTLE_EMUROM, 3584,,,
 ,[Offset], sshtl_l7, 0,
 ,[Offset], sshtl_l7, 1,
 ,[Offset], sshtl_l7, 2,
 ,[Offset], sshtl_l7, 3,
 ,[Offset], sshtl_l7, 4,
RSID_TSHUTTLE_SOUNDS_START, 3585,,,
RSID_TSHUTTLE_EMU_SOUNDS, 3586,,,
 ,[Offset], S0001_C1, 0,
 ,[Offset], S0002_C1, 1,
 ,[Offset], S0003_C1, 2,
 ,[Offset], S0004_C1, 3,
 ,[Offset], S0005_C1, 4,
 ,[Offset], S0006_C1, 5,
 ,[Offset], S0007_C1, 6,
 ,[Offset], S0008_C1, 7,
 ,[Offset], S0009_C1, 8,
 ,[Offset], S000A_C1, 9,
 ,[Offset], S000C-LP, 10,
 ,[Offset], S000D-LP, 11,
 ,[Offset], S000F_C1, 12,
 ,[Offset], S0010_C1, 13,
 ,[Offset], S0011_C1, 14,
 ,[Offset], S0012_C1, 15,
 ,[Offset], S0013_C1, 16,
 ,[Offset], S0014_C1, 17,
 ,[Offset], S0015_C1, 18,
 ,[Offset], S0016_C1, 19,
 ,[Offset], S0017_C1, 20,
 ,[Offset], S0018_C1, 21,
 ,[Offset], S0019_C1, 22,
 ,[Offset], S001A_C1, 23,
 ,[Offset], S001B_C1, 24,
 ,[Offset], S001C_C1, 25,
 ,[Offset], S001D_C1, 26,
 ,[Offset], S001E_C1, 27,
 ,[Offset], S001F_C1, 28,
 ,[Offset], S0020_C1, 29,
 ,[Offset], S0021_C1, 30,
 ,[Offset], S0022_C1, 31,
 ,[Offset], S0023_C1, 32,
 ,[Offset], S0024_C1, 33,
 ,[Offset], S0025_C1, 34,
 ,[Offset], S0026_C1, 35,
 ,[Offset], S0027_C1, 36,
 ,[Offset], S0028_C1, 37,
 ,[Offset], S0029_C1, 38,
 ,[Offset], S002A_C1, 39,
 ,[Offset], S002B_C1, 40,
 ,[Offset], S002C_C1, 41,
 ,[Offset], S002D_C1, 42,
 ,[Offset], S002E_C1, 43,
 ,[Offset], S002F_C1, 44,
 ,[Offset], S0030_C1, 45,
 ,[Offset], S0031_C1, 46,
 ,[Offset], S0032_C1, 47,
 ,[Offset], S0033_C1, 48,
 ,[Offset], S0034_C1, 49,
 ,[Offset], S0035_C1, 50,
 ,[Offset], S0036_C1, 51,
 ,[Offset], S0037_C1, 52,
 ,[Offset], S0038_C1, 53,
 ,[Offset], S0039_C1, 54,
 ,[Offset], S003A_C1, 55,
 ,[Offset], S003B_C1, 56,
 ,[Offset], S003C_C1, 57,
 ,[Offset], S003D_C1, 58,
 ,[Offset], S003E_C1, 59,
 ,[Offset], S003F_C1, 60,
 ,[Offset], S0040_C1, 61,
 ,[Offset], S0041_C1, 62,
 ,[Offset], S0042_C1, 63,
 ,[Offset], S0070_C1, 64,
 ,[Offset], S0071_C1, 65,
 ,[Offset], S0072_C1, 66,
 ,[Offset], S0073_C1, 67,
 ,[Offset], S0074_C1, 68,
 ,[Offset], S0075_C1, 69,
 ,[Offset], S0076_C1, 70,
 ,[Offset], S0077_C1, 71,
 ,[Offset], S0078_C1, 72,
 ,[Offset], S0079_C1, 73,
 ,[Offset], S007A_C1, 74,
 ,[Offset], S007B_C1, 75,
 ,[Offset], S007C_C1, 76,
 ,[Offset], S007D_C1, 77,
 ,[Offset], S007E_C1, 78,
 ,[Offset], S007F_C1, 79,
 ,[Offset], S0080_C1, 80,
 ,[Offset], S0081_C1, 81,
 ,[Offset], S0082_C1, 82,
 ,[Offset], S0083_C1, 83,
 ,[Offset], S0084_C1, 84,
 ,[Offset], S0085_C1, 85,
 ,[Offset], S0086_C1, 86,
 ,[Offset], S0087_C1, 87,
 ,[Offset], S0088_C1, 88,
 ,[Offset], S0089_C1, 89,
 ,[Offset], S008A_C1, 90,
 ,[Offset], S008B_C1, 91,
 ,[Offset], S008C_C1, 92,
 ,[Offset], S008D_C1, 93,
 ,[Offset], S008E_C1, 94,
 ,[Offset], S008F_C1, 95,
 ,[Offset], S0090_C1, 96,
 ,[Offset], S0091_C1, 97,
 ,[Offset], S0092_C1, 98,
 ,[Offset], S0093_C1, 99,
 ,[Offset], S0094_C1, 100,
 ,[Offset], S0095_C1, 101,
 ,[Offset], S0096_C1, 102,
 ,[Offset], S0097_C1, 103,
 ,[Offset], S0098_C1, 104,
 ,[Offset], S0099_C1, 105,
 ,[Offset], S009A_C1, 106,
 ,[Offset], S009B_C1, 107,
 ,[Offset], S009C_C1, 108,
 ,[Offset], S009D_C1, 109,
 ,[Offset], S009E_C1, 110,
 ,[Offset], S009F_C1, 111,
 ,[Offset], S00A0_C1, 112,
 ,[Offset], S00A1_C1, 113,
 ,[Offset], S00A2_C1, 114,
 ,[Offset], S00A3_C1, 115,
 ,[Offset], S00A4_C1, 116,
 ,[Offset], S00A5_C1, 117,
 ,[Offset], S00A6_C1, 118,
 ,[Offset], S00A7_C1, 119,
 ,[Offset], S00A8_C1, 120,
 ,[Offset], S00A9_C1, 121,
 ,[Offset], S00AA_C1, 122,
 ,[Offset], S00AB_C1, 123,
 ,[Offset], S00AC_C1, 124,
 ,[Offset], S00AD_C1, 125,
 ,[Offset], S00AE_C1, 126,
 ,[Offset], S00AF_C1, 127,
 ,[Offset], S00B0_C1, 128,
 ,[Offset], S00B1_C1, 129,
 ,[Offset], S00B2-LP, 130,
 ,[Offset], S00B3_C1, 131,
 ,[Offset], S00B4_C1, 132,
 ,[Offset], S00B5_C1, 133,
 ,[Offset], S00B6_C1, 134,
 ,[Offset], S00B7_C1, 135,
 ,[Offset], S00B8_C1, 136,
 ,[Offset], S00B9_C1, 137,
 ,[Offset], S00BA_C1, 138,
 ,[Offset], S00BB_C1, 139,
 ,[Offset], S00BC_C1, 140,
 ,[Offset], S00BD_C1, 141,
 ,[Offset], S00C0_C1, 142,
 ,[Offset], S00C1_C1, 143,
 ,[Offset], S00C2_C1, 144,
 ,[Offset], S00C3_C1, 145,
 ,[Offset], S00C4_C1, 146,
 ,[Offset], S00C5_C1, 147,
 ,[Offset], S00C6_C1, 148,
 ,[Offset], S00C7_C1, 149,
 ,[Offset], S00C8_C1, 150,
 ,[Offset], S00C9_C1, 151,
 ,[Offset], S00CA_C1, 152,
 ,[Offset], S00CB_C1, 153,
 ,[Offset], S00CC_C1, 154,
 ,[Offset], S00CD_C1, 155,
 ,[Offset], S00CE_C1, 156,
 ,[Offset], S00CF_C1, 157,
 ,[Offset], S00D0_C1, 158,
 ,[Offset], S00D1_C1, 159,
 ,[Offset], S00D2_C1, 160,
 ,[Offset], S00D3_C1, 161,
 ,[Offset], S00D4_C1, 162,
 ,[Offset], S00D5_C1, 163,
 ,[Offset], S00D6_C1, 164,
 ,[Offset], S00D7_C1, 165,
 ,[Offset], S00D8-LP, 166,
 ,[Offset], S00D9_C1, 167,
 ,[Offset], S00DA_C1, 168,
 ,[Offset], S00DB_C1, 169,
 ,[Offset], S00DC_C1, 170,
 ,[Offset], S00DD_C1, 171,
 ,[Offset], S00DE_C1, 172,
 ,[Offset], S00DF_C1, 173,
 ,[Offset], S00E0_C1, 174,
 ,[Offset], S00E1_C1, 175,
 ,[Offset], S00E2_C1, 176,
 ,[Offset], S00E3_C1, 177,
 ,[Offset], S00E4_C1, 178,
 ,[Offset], S00E5_C1, 179,
 ,[Offset], S00E6_C1, 180,
 ,[Offset], S00E7_C1, 181,
 ,[Offset], S00E8_C1, 182,
 ,[Offset], S00E9_C1, 183,
 ,[Offset], S00EA_C1, 184,
 ,[Offset], S00EB_C1, 185,
 ,[Offset], S00EC_C1, 186,
 ,[Offset], S00ED_C1, 187,
 ,[Offset], S00EE_C1, 188,
 ,[Offset], S00EF_C1, 189,
 ,[Offset], S00F0_C1, 190,
 ,[Offset], S00F1_C1, 191,
 ,[Offset], S00F2_C1, 192,
 ,[Offset], S00F3_C1, 193,
 ,[Offset], S00F4_C1, 194,
 ,[Offset], S00F5_C1, 195,
 ,[Offset], S00F6_C1, 196,
 ,[Offset], S00F7_C1, 197,
 ,[Offset], S00F8_C1, 198,
 ,[Offset], S00F8_C1, 199,
 ,[Offset], S00FA_C1, 200,
 ,[Offset], S00FB_C1, 201,
 ,[Offset], S00FC_C1, 202,
 ,[Offset], S00FD_C1, 203,
 ,[Offset], S00FE_C1, 204,
 ,[Offset], S00FF_C1, 205,
RSID_TSHUTTLE_MECH_SOUNDS, 3587,,,
 ,[Offset], bell, 0,
RSID_TSHUTTLE_SOUNDS_END, 3588,,,
RSID_TSHUTTLE_SAMPLES, 3589,,,
RSID_TSHUTTLE_VERSION, 3590,,,
RSID_TSHUTTLE_LCDHUD, 3591,,,
RSID_TSHUTTLE_END, 3592,,,
