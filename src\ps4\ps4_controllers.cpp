// Copyright 2025 <Copyright Owner>

#include "ps4_controllers.h"
#include "../memory/memory_diagnostics.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <spdlog/spdlog.h>
#include <stdexcept>


namespace ps4 {

/**
 * @brief Constructs the controller manager.
 * @param memory Reference to the PS4 MMU.
 */
PS4ControllerManager::PS4ControllerManager(PS4MMU &memory) : m_memory(memory) {
  auto start = std::chrono::steady_clock::now();
  std::fill(m_sdlControllers.begin(), m_sdlControllers.end(), nullptr);
  m_stats = ControllerStats();
  spdlog::info("PS4ControllerManager constructed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4ControllerManager::~PS4ControllerManager() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("PS4ControllerManager destroyed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Initializes the controller manager.
 * @return True on success, false otherwise.
 */
bool PS4ControllerManager::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (m_initialized) {
      spdlog::warn("PS4ControllerManager already initialized");
      return true;
    }

    if (SDL_Init(SDL_INIT_GAMECONTROLLER) < 0) {
      spdlog::error("SDL_Init failed: {}", SDL_GetError());
      throw ControllerException("SDL initialization failed");
    }
    for (int i = 0; i < SDL_NumJoysticks() && i < MAX_CONTROLLERS; ++i) {
      if (SDL_IsGameController(i)) {
        MapSDLControllerUnsafe(i);
      }
    }
    // DO NOT START THE THREAD HERE
    // m_running = true;
    // m_inputThread = std::thread(&PS4ControllerManager::InputLoop, this);

    m_initialized = true; // Mark as initialized but not yet running
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4ControllerManager initialized");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("PS4ControllerManager initialization failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Starts the input processing thread.
 */
void PS4ControllerManager::StartInputThread() {
    std::unique_lock<std::shared_mutex> lock(m_stateMutex);
    if (!m_initialized) {
        spdlog::error("Cannot start input thread: PS4ControllerManager not initialized");
        return;
    }
    if (m_running) {
        spdlog::warn("Input thread is already running.");
        return;
    }
    m_running = true;
    m_inputThread = std::thread(&PS4ControllerManager::InputLoop, this);
    spdlog::info("Controller input thread started.");
}

/**
 * @brief Shuts down the controller manager.
 */
void PS4ControllerManager::Shutdown() {
  auto start = std::chrono::steady_clock::now();

  // Set running to false first
  {
      std::unique_lock<std::shared_mutex> lock(m_stateMutex);
      if (!m_initialized) return; // Already shutdown
      m_running = false;
      m_inputCondition.notify_all();
  }

  // Join the thread without holding the lock
  try {
    if (m_inputThread.joinable()) {
      m_inputThread.join();
    }
  } catch (const std::system_error& e) {
      spdlog::error("Failed to join input thread: {}", e.what());
  }

  // Re-acquire lock to clean up resources
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    for (int i = 0; i < MAX_CONTROLLERS; ++i) {
      UnmapSDLControllerUnsafe(i);
    }
    SDL_QuitSubSystem(SDL_INIT_GAMECONTROLLER);
    m_initialized = false; // Reset initialization state
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4ControllerManager shutdown");
  } catch (const std::exception &e) {
    spdlog::error("PS4ControllerManager shutdown failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Processes SDL input events.
 * @note SDL event polling now handled by main thread. This method is kept for
 *       compatibility but no longer polls SDL events to avoid race conditions.
 */
void PS4ControllerManager::ProcessEvents() {
  // SDL event polling is now centralized to the main thread to prevent race
  // conditions. Controller device add/remove events are handled via
  // HandleControllerDeviceAdded/Removed methods called from the main thread's
  // event loop. This method is kept for compatibility but is now a no-op.
}

/**
 * @brief Connects a controller.
 * @param index Controller index.
 * @return True on success, false if invalid or already connected.
 */
bool PS4ControllerManager::ConnectController(int index) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (index < 0 || index >= MAX_CONTROLLERS) {
      spdlog::warn("Invalid controller index: {}", index);
      m_stats.cacheMisses++;
      return false;
    }
    if (!m_connected[index] && !m_sdlControllers[index] &&
        SDL_IsGameController(index)) {
      MapSDLControllerUnsafe(index);
    }
    m_connected[index] = true;
    UpdateMemoryRegisters(index);
    m_states[index].eventCount++;
    m_states[index].cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Connected controller {}", index);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Connect controller {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Disconnects a controller.
 * @param index Controller index.
 * @return True on success, false if invalid or not connected.
 */
bool PS4ControllerManager::DisconnectController(int index) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (index < 0 || index >= MAX_CONTROLLERS) {
      spdlog::warn("Invalid controller index: {}", index);
      m_stats.cacheMisses++;
      return false;
    }
    if (!m_connected[index]) {
      spdlog::warn("Controller {} not connected", index);
      m_stats.cacheMisses++;
      return false;
    }
    m_connected[index] = false;
    UnmapSDLControllerUnsafe(index);
    UpdateMemoryRegisters(index);
    m_states[index].eventCount++;
    m_states[index].cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Disconnected controller {}", index);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Disconnect controller {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Checks if a controller is connected.
 * @param index Controller index.
 * @return True if connected, false otherwise.
 */
bool PS4ControllerManager::IsControllerConnected(int index) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (index < 0 || index >= MAX_CONTROLLERS) {
      m_stats.cacheMisses++;
      return false;
    }
    return m_connected[index];
  } catch (const std::exception &e) {
    spdlog::error("IsControllerConnected {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Retrieves the state of a controller.
 * @param index Controller index.
 * @return Controller state.
 */
ControllerState PS4ControllerManager::GetControllerState(int index) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (index < 0 || index >= MAX_CONTROLLERS) {
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return {};
    }
    return m_states[index];
  } catch (const std::exception &e) {
    spdlog::error("Get controller state {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return {};
  }
}

/**
 * @brief Sets the rumble intensities for a controller.
 * @param index Controller index.
 * @param weak Weak rumble intensity.
 * @param strong Strong rumble intensity.
 */
void PS4ControllerManager::SetRumble(int index, uint8_t weak, uint8_t strong) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    SetRumbleUnsafe(index, weak, strong);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Set rumble {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Sets the rumble intensities for a controller without acquiring mutex.
 * @param index Controller index.
 * @param weak Weak rumble intensity.
 * @param strong Strong rumble intensity.
 * @note Caller must hold m_stateMutex lock.
 */
void PS4ControllerManager::SetRumbleUnsafe(int index, uint8_t weak,
                                           uint8_t strong) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_sdlControllers[index]) {
      spdlog::warn("Invalid or disconnected controller for rumble: {}", index);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_states[index].rumbleWeak = weak;
    m_states[index].rumbleStrong = strong;
    SDL_GameControllerRumble(m_sdlControllers[index], weak * 257, strong * 257,
                             1000);
    m_states[index].eventCount++;
    m_states[index].cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::trace("Set rumble for controller {}: weak={}, strong={}", index,
                  weak, strong);
  } catch (const std::exception &e) {
    spdlog::error("Set rumble {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Sets the LED color for a controller.
 * @param index Controller index.
 * @param r Red component.
 * @param g Green component.
 * @param b Blue component.
 */
void PS4ControllerManager::SetLED(int index, uint8_t r, uint8_t g, uint8_t b) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    SetLEDUnsafe(index, r, g, b);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Set LED {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Sets the LED color for a controller without acquiring mutex.
 * @param index Controller index.
 * @param r Red component.
 * @param g Green component.
 * @param b Blue component.
 * @note Caller must hold m_stateMutex lock.
 */
void PS4ControllerManager::SetLEDUnsafe(int index, uint8_t r, uint8_t g,
                                        uint8_t b) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_sdlControllers[index]) {
      spdlog::warn("Invalid or disconnected controller for LED: {}", index);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_states[index].ledR = r;
    m_states[index].ledG = g;
    m_states[index].ledB = b;
    SDL_GameControllerSetLED(m_sdlControllers[index], r, g, b);
    m_states[index].eventCount++;
    m_states[index].cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::trace("Set LED for controller {}: R={}, G={}, B={}", index, r, g,
                  b);
  } catch (const std::exception &e) {
    spdlog::error("Set LED {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Reads a controller register from the MMU.
 * @param address Register address.
 * @return Register value, or 0 if invalid.
 */
uint32_t PS4ControllerManager::ReadRegister(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (address < ControllerRegisters::BASE_ADDRESS ||
        address >= ControllerRegisters::BASE_ADDRESS +
                       MAX_CONTROLLERS * ControllerRegisters::SIZE) {
      spdlog::warn("Invalid controller register read: 0x{:x}", address);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return 0;
    }
    int index = static_cast<int>((address - ControllerRegisters::BASE_ADDRESS) /
                                 ControllerRegisters::SIZE);
    uint64_t offset = address - (ControllerRegisters::BASE_ADDRESS +
                                 index * ControllerRegisters::SIZE);
    std::shared_lock<std::shared_mutex> lock(m_stateMutex);
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_states[index].cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    switch (offset) {
    case 0x00:
      return m_states[index].buttons;
    case 0x04:
      return m_states[index].leftStickX;
    case 0x05:
      return m_states[index].leftStickY;
    case 0x06:
      return m_states[index].rightStickX;
    case 0x07:
      return m_states[index].rightStickY;
    case 0x08:
      return m_states[index].l2Trigger;
    case 0x09:
      return m_states[index].r2Trigger;
    case 0x0A:
      return m_states[index].rumbleWeak;
    case 0x0B:
      return m_states[index].rumbleStrong;
    case 0x0C:
      return m_states[index].ledR;
    case 0x0D:
      return m_states[index].ledG;
    case 0x0E:
      return m_states[index].ledB;
    case 0x10:
      return static_cast<uint16_t>(m_states[index].gyroX);
    case 0x12:
      return static_cast<uint16_t>(m_states[index].gyroY);
    case 0x14:
      return static_cast<uint16_t>(m_states[index].gyroZ);
    case 0x16:
      return static_cast<uint16_t>(m_states[index].accelX);
    case 0x18:
      return static_cast<uint16_t>(m_states[index].accelY);
    case 0x1A:
      return static_cast<uint16_t>(m_states[index].accelZ);
    case 0x1C:
      return m_states[index].batteryLevel;
    case 0x1D:
      return m_states[index].touchpadPressed ? 1 : 0;
    case 0x1E:
      return static_cast<uint16_t>(m_states[index].touchX);
    case 0x20:
      return static_cast<uint16_t>(m_states[index].touchY);
    default:
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return 0;
    }
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Read register 0x{:x} failed: {}", address, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Writes to a controller register in the MMU.
 * @param address Register address.
 * @param value Value to write.
 */
void PS4ControllerManager::WriteRegister(uint64_t address, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (address < ControllerRegisters::BASE_ADDRESS ||
        address >= ControllerRegisters::BASE_ADDRESS +
                       MAX_CONTROLLERS * ControllerRegisters::SIZE) {
      spdlog::warn("Invalid controller register write: 0x{:x}", address);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    int index = static_cast<int>((address - ControllerRegisters::BASE_ADDRESS) /
                                 ControllerRegisters::SIZE);
    uint64_t offset = address - (ControllerRegisters::BASE_ADDRESS +
                                 index * ControllerRegisters::SIZE);
    std::unique_lock<std::shared_mutex> lock(m_stateMutex);
    m_states[index].cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    switch (offset) {
    case 0x0A:
      SetRumbleUnsafe(index, static_cast<uint8_t>(value),
                      m_states[index].rumbleStrong);
      break;
    case 0x0B:
      SetRumbleUnsafe(index, m_states[index].rumbleWeak,
                      static_cast<uint8_t>(value));
      break;
    case 0x0C:
      SetLEDUnsafe(index, static_cast<uint8_t>(value), m_states[index].ledG,
                   m_states[index].ledB);
      break;
    case 0x0D:
      SetLEDUnsafe(index, m_states[index].ledR, static_cast<uint8_t>(value),
                   m_states[index].ledB);
      break;
    case 0x0E:
      SetLEDUnsafe(index, m_states[index].ledR, m_states[index].ledG,
                   static_cast<uint8_t>(value));
      break;
    default:
      break;
    }
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Write register 0x{:x} failed: {}", address, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Updates MMU registers for a controller.
 * @param index Controller index.
 */
void PS4ControllerManager::UpdateMemoryRegisters(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS) {
      spdlog::warn("Invalid controller index for register update: {}", index);
      m_stats.cacheMisses++;
      return;
    }
    uint64_t base =
        ControllerRegisters::BASE_ADDRESS + index * ControllerRegisters::SIZE;
    uint64_t processId = 1; // fixed process ID
    std::unique_lock<std::shared_mutex> lock(m_stateMutex);
    m_memory.WriteVirtual(base + 0x00, &m_states[index].buttons,
                          sizeof(m_states[index].buttons), processId);
    m_memory.WriteVirtual(base + 0x04, &m_states[index].leftStickX,
                          sizeof(m_states[index].leftStickX), processId);
    m_memory.WriteVirtual(base + 0x05, &m_states[index].leftStickY,
                          sizeof(m_states[index].leftStickY), processId);
    m_memory.WriteVirtual(base + 0x06, &m_states[index].rightStickX,
                          sizeof(m_states[index].rightStickX), processId);
    m_memory.WriteVirtual(base + 0x07, &m_states[index].rightStickY,
                          sizeof(m_states[index].rightStickY), processId);
    m_memory.WriteVirtual(base + 0x08, &m_states[index].l2Trigger,
                          sizeof(m_states[index].l2Trigger), processId);
    m_memory.WriteVirtual(base + 0x09, &m_states[index].r2Trigger,
                          sizeof(m_states[index].r2Trigger), processId);
    m_memory.WriteVirtual(base + 0x0A, &m_states[index].rumbleWeak,
                          sizeof(m_states[index].rumbleWeak), processId);
    m_memory.WriteVirtual(base + 0x0B, &m_states[index].rumbleStrong,
                          sizeof(m_states[index].rumbleStrong), processId);
    m_memory.WriteVirtual(base + 0x0C, &m_states[index].ledR,
                          sizeof(m_states[index].ledR), processId);
    m_memory.WriteVirtual(base + 0x0D, &m_states[index].ledG,
                          sizeof(m_states[index].ledG), processId);
    m_memory.WriteVirtual(base + 0x0E, &m_states[index].ledB,
                          sizeof(m_states[index].ledB), processId);
    m_memory.WriteVirtual(base + 0x10, &m_states[index].gyroX,
                          sizeof(m_states[index].gyroX), processId);
    m_memory.WriteVirtual(base + 0x12, &m_states[index].gyroY,
                          sizeof(m_states[index].gyroY), processId);
    m_memory.WriteVirtual(base + 0x14, &m_states[index].gyroZ,
                          sizeof(m_states[index].gyroZ), processId);
    m_memory.WriteVirtual(base + 0x16, &m_states[index].accelX,
                          sizeof(m_states[index].accelX), processId);
    m_memory.WriteVirtual(base + 0x18, &m_states[index].accelY,
                          sizeof(m_states[index].accelY), processId);
    m_memory.WriteVirtual(base + 0x1A, &m_states[index].accelZ,
                          sizeof(m_states[index].accelZ), processId);
    m_memory.WriteVirtual(base + 0x1C, &m_states[index].batteryLevel,
                          sizeof(m_states[index].batteryLevel), processId);
    uint8_t touchpadState = m_states[index].touchpadPressed ? 1 : 0;
    m_memory.WriteVirtual(base + 0x1D, &touchpadState, sizeof(touchpadState),
                          processId);
    m_memory.WriteVirtual(base + 0x1E, &m_states[index].touchX,
                          sizeof(m_states[index].touchX), processId);
    m_memory.WriteVirtual(base + 0x20, &m_states[index].touchY,
                          sizeof(m_states[index].touchY), processId);
    m_states[index].cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Update registers for controller {} failed: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Input processing loop for the input thread.
 */
void PS4ControllerManager::InputLoop() {
  while (m_running) {
    auto start = std::chrono::steady_clock::now();
    try {
      // Update all game controller states - this is thread-safe
      SDL_GameControllerUpdate();

      std::unique_lock<std::shared_mutex> lock(m_stateMutex);
      for (int i = 0; i < MAX_CONTROLLERS; ++i) {
        if (!m_sdlControllers[i] || !m_connected[i]) {
          if (m_connected[i] && ReconnectControllerUnsafe(i)) {
            spdlog::info("Reconnected controller {}", i);
          }
          continue;
        }
        ControllerState &state = m_states[i];
        state.buttons = 0;
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_A))
          state.buttons |= static_cast<uint32_t>(ControllerButton::CROSS);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_B))
          state.buttons |= static_cast<uint32_t>(ControllerButton::CIRCLE);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_X))
          state.buttons |= static_cast<uint32_t>(ControllerButton::SQUARE);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_Y))
          state.buttons |= static_cast<uint32_t>(ControllerButton::TRIANGLE);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_LEFTSHOULDER))
          state.buttons |= static_cast<uint32_t>(ControllerButton::L1);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_RIGHTSHOULDER))
          state.buttons |= static_cast<uint32_t>(ControllerButton::R1);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_BACK))
          state.buttons |= static_cast<uint32_t>(ControllerButton::SHARE);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_START))
          state.buttons |= static_cast<uint32_t>(ControllerButton::OPTIONS);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_LEFTSTICK))
          state.buttons |= static_cast<uint32_t>(ControllerButton::L3);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_RIGHTSTICK))
          state.buttons |= static_cast<uint32_t>(ControllerButton::R3);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_GUIDE))
          state.buttons |= static_cast<uint32_t>(ControllerButton::PS);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_DPAD_UP))
          state.buttons |= static_cast<uint32_t>(ControllerButton::DPAD_UP);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_DPAD_DOWN))
          state.buttons |= static_cast<uint32_t>(ControllerButton::DPAD_DOWN);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_DPAD_LEFT))
          state.buttons |= static_cast<uint32_t>(ControllerButton::DPAD_LEFT);
        if (SDL_GameControllerGetButton(m_sdlControllers[i],
                                        SDL_CONTROLLER_BUTTON_DPAD_RIGHT))
          state.buttons |= static_cast<uint32_t>(ControllerButton::DPAD_RIGHT);
        state.leftStickX = static_cast<uint8_t>(
            (SDL_GameControllerGetAxis(m_sdlControllers[i],
                                       SDL_CONTROLLER_AXIS_LEFTX) +
             32768) >>
            8);
        state.leftStickY = static_cast<uint8_t>(
            (SDL_GameControllerGetAxis(m_sdlControllers[i],
                                       SDL_CONTROLLER_AXIS_LEFTY) +
             32768) >>
            8);
        state.rightStickX = static_cast<uint8_t>(
            (SDL_GameControllerGetAxis(m_sdlControllers[i],
                                       SDL_CONTROLLER_AXIS_RIGHTX) +
             32768) >>
            8);
        state.rightStickY = static_cast<uint8_t>(
            (SDL_GameControllerGetAxis(m_sdlControllers[i],
                                       SDL_CONTROLLER_AXIS_RIGHTY) +
             32768) >>
            8);
        state.l2Trigger = static_cast<uint8_t>(
            SDL_GameControllerGetAxis(m_sdlControllers[i],
                                      SDL_CONTROLLER_AXIS_TRIGGERLEFT) >>
            7);
        state.r2Trigger = static_cast<uint8_t>(
            SDL_GameControllerGetAxis(m_sdlControllers[i],
                                      SDL_CONTROLLER_AXIS_TRIGGERRIGHT) >>
            7);
        // Enhanced motion sensor processing
        ProcessMotionSensorData(i);

        // Enhanced touchpad processing
        ProcessTouchpadData(i);

        // Enhanced battery processing
        ProcessBatteryData(i);

        // Enhanced haptics processing
        ProcessAdvancedHaptics(i);

        // Update legacy fields for compatibility
        UpdateLegacyFields(i);

        state.touchpadPressed = SDL_GameControllerGetButton(
            m_sdlControllers[i], SDL_CONTROLLER_BUTTON_TOUCHPAD);
        UpdateMemoryRegisters(i);
        state.eventCount++;
        state.cacheHits++;
      }
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      lock.unlock();
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      // Adaptive polling: sleep longer if no events, shorter if active
      std::this_thread::sleep_for(
          std::chrono::milliseconds(m_stats.eventCount > 0 ? 10 : 16));
    } catch (const std::exception &e) {
      spdlog::error("Input loop failed: {}", e.what());
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    }
  }
}

/**
 * @brief Maps an SDL controller to an index.
 * @param index Controller index.
 */
void PS4ControllerManager::MapSDLController(int index) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    MapSDLControllerUnsafe(index);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Map SDL controller {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Maps an SDL controller to an index without acquiring mutex.
 * @param index Controller index.
 * @note Caller must hold m_stateMutex lock.
 */
void PS4ControllerManager::MapSDLControllerUnsafe(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || m_sdlControllers[index]) {
      spdlog::warn("Invalid or already mapped controller index: {}", index);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_sdlControllers[index] = SDL_GameControllerOpen(index);
    if (m_sdlControllers[index]) {
      m_connected[index] = true;
      m_states[index].ledB = 255; // Default blue LED
      SetLEDUnsafe(index, m_states[index].ledR, m_states[index].ledG,
                   m_states[index].ledB);
      m_states[index].eventCount++;
      m_states[index].cacheHits++;
      m_stats.cacheHits++;
      spdlog::info("Mapped SDL controller {}", index);
    } else {
      spdlog::warn("Failed to open SDL controller {}: {}", index,
                   SDL_GetError());
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("Map SDL controller {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Unmaps an SDL controller from an index.
 * @param index Controller index.
 */
void PS4ControllerManager::UnmapSDLController(int index) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    UnmapSDLControllerUnsafe(index);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("Unmap SDL controller {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Unmaps an SDL controller from an index without acquiring mutex.
 * @param index Controller index.
 * @note Caller must hold m_stateMutex lock.
 */
void PS4ControllerManager::UnmapSDLControllerUnsafe(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_sdlControllers[index]) {
      spdlog::warn("Invalid or not mapped controller index: {}", index);
      m_stats.cacheMisses++;
      return;
    }
    SDL_GameControllerClose(m_sdlControllers[index]);
    m_sdlControllers[index] = nullptr;
    m_connected[index] = false;
    m_states[index] = {};
    UpdateMemoryRegisters(index);
    m_states[index].eventCount++;
    m_states[index].cacheHits++;
    m_stats.cacheHits++;
    spdlog::info("Unmapped SDL controller {}", index);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("Unmap SDL controller {} failed: {}", index, e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Attempts to reconnect a dropped controller.
 * @param index Controller index.
 * @return True if reconnected, false otherwise.
 */
bool PS4ControllerManager::ReconnectController(int index) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    bool result = ReconnectControllerUnsafe(index);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("Reconnect controller {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Attempts to reconnect a dropped controller without acquiring mutex.
 * @param index Controller index.
 * @return True if reconnected, false otherwise.
 * @note Caller must hold m_stateMutex lock.
 */
bool PS4ControllerManager::ReconnectControllerUnsafe(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || m_sdlControllers[index]) {
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    if (SDL_IsGameController(index)) {
      MapSDLControllerUnsafe(index);
      m_stats.reconnectAttempts.fetch_add(1, std::memory_order_relaxed);
      m_states[index].eventCount++;
      m_states[index].cacheHits++;
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

      return true;
    }
    m_stats.reconnectAttempts.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Reconnect controller {} failed: {}", index, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Saves the controller manager state.
 * @param out Output stream.
 */
void PS4ControllerManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(m_connected.data()),
              MAX_CONTROLLERS * sizeof(bool));
    for (const auto &state : m_states) {
      out.write(reinterpret_cast<const char *>(&state), sizeof(state));
    }
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw std::runtime_error("Failed to write controller state");
    }
    // no state mods in const method
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4ControllerManager state saved");
  } catch (const std::exception &e) {
    spdlog::error("PS4ControllerManager SaveState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Loads the controller manager state.
 * @param in Input stream.
 */
void PS4ControllerManager::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported controller state version: {}", version);
      throw std::runtime_error("Invalid controller state version");
    }
    in.read(reinterpret_cast<char *>(m_connected.data()),
            MAX_CONTROLLERS * sizeof(bool));
    for (auto &state : m_states) {
      in.read(reinterpret_cast<char *>(&state), sizeof(state));
    }
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw std::runtime_error("Failed to read controller state");
    }
    for (int i = 0; i < MAX_CONTROLLERS; ++i) {
      if (m_connected[i] && !m_sdlControllers[i]) {
        MapSDLControllerUnsafe(i);
      } else if (!m_connected[i] && m_sdlControllers[i]) {
        UnmapSDLControllerUnsafe(i);
      }
      UpdateMemoryRegisters(i);
      m_states[i].cacheHits++;
    }
    // stats update omitted
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4ControllerManager state loaded");
  } catch (const std::exception &e) {
    spdlog::error("PS4ControllerManager LoadState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Polls for controller events.
 */
void PS4ControllerManager::PollEvents() { ProcessEvents(); }

/**
 * @brief Handles SDL controller device added events from main thread.
 * @param deviceIndex The device index from SDL_CONTROLLERDEVICEADDED event.
 */
void PS4ControllerManager::HandleControllerDeviceAdded(int deviceIndex) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    if (deviceIndex < MAX_CONTROLLERS && SDL_IsGameController(deviceIndex)) {
      MapSDLControllerUnsafe(deviceIndex);
      m_states[deviceIndex].eventCount++;
      m_states[deviceIndex].cacheHits++;
    }
    m_stats.eventCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Handle controller device added failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Handles SDL controller device removed events from main thread.
 * @param instanceId The instance ID from SDL_CONTROLLERDEVICEREMOVED event.
 */
void PS4ControllerManager::HandleControllerDeviceRemoved(int instanceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_stateMutex);
  try {
    for (int i = 0; i < MAX_CONTROLLERS; ++i) {
      if (m_sdlControllers[i] &&
          SDL_JoystickInstanceID(SDL_GameControllerGetJoystick(
              m_sdlControllers[i])) == instanceId) {
        UnmapSDLControllerUnsafe(i);
        m_states[i].eventCount++;
        m_states[i].cacheHits++;
        break;
      }
    }
    m_stats.eventCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Handle controller device removed failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Checks the state of a specific button.
 * @param index Controller index (0 to MAX_CONTROLLERS-1).
 * @param button Button to check.
 * @return True if pressed, false otherwise.
 */
bool PS4ControllerManager::GetButtonState(int index, int button) const {
  if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index] ||
      !m_sdlControllers[index]) {
    return false;
  }

  std::shared_lock<std::shared_mutex> lock(m_stateMutex);
  return SDL_GameControllerGetButton(
             m_sdlControllers[index],
             static_cast<SDL_GameControllerButton>(button)) != 0;
}

/**
 * @brief Gets the state of a specific axis.
 * @param index Controller index (0 to MAX_CONTROLLERS-1).
 * @param axis Axis to check.
 * @return Axis value.
 */
int16_t PS4ControllerManager::GetAxisState(int index, int axis) const {
  if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index] ||
      !m_sdlControllers[index]) {
    return 0;
  }

  std::shared_lock<std::shared_mutex> lock(m_stateMutex);
  return SDL_GameControllerGetAxis(m_sdlControllers[index],
                                   static_cast<SDL_GameControllerAxis>(axis));
}

/**
 * @brief Enhanced motion sensor data processing.
 */
void PS4ControllerManager::ProcessMotionSensorData(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index]) {
      return;
    }

    ControllerState &state = m_states[index];

    // Simulate realistic motion sensor data (in a real implementation, this
    // would come from the controller)
    static float time = 0.0f;
    time += 0.016f; // 60 FPS simulation

    // Simulate gyroscope data (rad/s) with some noise
    state.motion.gyroX = 0.1f * sin(time * 2.0f) + (rand() % 100 - 50) * 0.001f;
    state.motion.gyroY =
        0.05f * cos(time * 1.5f) + (rand() % 100 - 50) * 0.001f;
    state.motion.gyroZ =
        0.02f * sin(time * 3.0f) + (rand() % 100 - 50) * 0.001f;

    // Simulate accelerometer data (m/s²) with gravity
    state.motion.accelX = 0.2f * sin(time) + (rand() % 100 - 50) * 0.01f;
    state.motion.accelY = 0.2f * cos(time) + (rand() % 100 - 50) * 0.01f;
    state.motion.accelZ = 9.81f + 0.1f * sin(time * 0.5f) +
                          (rand() % 100 - 50) * 0.01f; // Gravity + noise

    // Simulate magnetometer data (µT)
    state.motion.magnetX =
        25.0f + 5.0f * sin(time * 0.1f) + (rand() % 100 - 50) * 0.1f;
    state.motion.magnetY =
        30.0f + 3.0f * cos(time * 0.15f) + (rand() % 100 - 50) * 0.1f;
    state.motion.magnetZ =
        45.0f + 2.0f * sin(time * 0.05f) + (rand() % 100 - 50) * 0.1f;

    m_stats.motionEvents.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("ProcessMotionSensorData failed for controller {}: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Enhanced touchpad data processing.
 */
void PS4ControllerManager::ProcessTouchpadData(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index]) {
      return;
    }

    ControllerState &state = m_states[index];

    // Simulate dual touchpad support (PS4 controller has one touchpad but can
    // detect multiple touches)
    bool touchpadPressed = SDL_GameControllerGetButton(
        m_sdlControllers[index], SDL_CONTROLLER_BUTTON_TOUCHPAD);

    if (touchpadPressed) {
      // Simulate primary touch
      state.touchpad[0].active = true;
      state.touchpad[0].x =
          static_cast<uint16_t>(rand() % 1920); // Full touchpad width
      state.touchpad[0].y =
          static_cast<uint16_t>(rand() % 942); // Full touchpad height
      state.touchpad[0].touchId = 1;

      // Randomly simulate secondary touch
      if (rand() % 10 == 0) {
        state.touchpad[1].active = true;
        state.touchpad[1].x = static_cast<uint16_t>(rand() % 1920);
        state.touchpad[1].y = static_cast<uint16_t>(rand() % 942);
        state.touchpad[1].touchId = 2;
      } else {
        state.touchpad[1].active = false;
      }
    } else {
      state.touchpad[0].active = false;
      state.touchpad[1].active = false;
    }

    m_stats.touchEvents.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("ProcessTouchpadData failed for controller {}: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Enhanced battery data processing.
 */
void PS4ControllerManager::ProcessBatteryData(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index]) {
      return;
    }

    ControllerState &state = m_states[index];

    // Simulate battery status (in real implementation, this would come from
    // controller)
    static int batteryLevel = 100;
    static auto lastUpdate = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();

    // Decrease battery level slowly over time
    if (std::chrono::duration_cast<std::chrono::seconds>(now - lastUpdate)
            .count() > 60) {
      batteryLevel = std::max(0, batteryLevel - 1);
      lastUpdate = now;
    }

    state.battery.level = static_cast<uint8_t>(batteryLevel);
    state.battery.charging = batteryLevel < 20; // Simulate charging when low

    m_stats.batteryUpdates.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("ProcessBatteryData failed for controller {}: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Enhanced haptics processing.
 */
void PS4ControllerManager::ProcessAdvancedHaptics(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index]) {
      return;
    }

    ControllerState &state = m_states[index];

    // Process haptic patterns if active
    if (m_hapticPatterns[index].active) {
      uint32_t &currentStep = m_hapticPatterns[index].currentStep;
      const auto &leftPattern = m_hapticPatterns[index].leftMotorPattern;
      const auto &rightPattern = m_hapticPatterns[index].rightMotorPattern;

      if (currentStep < leftPattern.size() &&
          currentStep < rightPattern.size()) {
        state.haptics.leftMotor = leftPattern[currentStep];
        state.haptics.rightMotor = rightPattern[currentStep];
        currentStep++;
      } else {
        // Pattern finished
        m_hapticPatterns[index].active = false;
        state.haptics.leftMotor = 0;
        state.haptics.rightMotor = 0;
      }

      // Convert advanced haptics to SDL rumble
      uint16_t lowFreq = static_cast<uint16_t>(state.haptics.leftMotor *
                                               257); // 0-255 to 0-65535
      uint16_t highFreq = static_cast<uint16_t>(state.haptics.rightMotor * 257);

      if (m_sdlControllers[index]) {
        SDL_GameControllerRumble(m_sdlControllers[index], lowFreq, highFreq,
                                 state.haptics.duration);
      }
    }

    m_stats.hapticEvents.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("ProcessAdvancedHaptics failed for controller {}: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Update legacy fields for compatibility.
 */
void PS4ControllerManager::UpdateLegacyFields(int index) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index < 0 || index >= MAX_CONTROLLERS || !m_connected[index]) {
      return;
    }

    ControllerState &state = m_states[index];

    // Ensure connection state is consistent
    state.connected = m_connected[index];

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("UpdateLegacyFields failed for controller {}: {}", index,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

} // namespace ps4