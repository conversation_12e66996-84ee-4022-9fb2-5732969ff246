#include "memory_compressor.h"
#include <algorithm>
#include <array>
#include <chrono>
#include <cmath>
#include <cstring>
#include <functional>
#include <iostream>
#include <spdlog/spdlog.h>
#include <thread>

// Third-party compression libraries
#include "zlib.h"

// If LZ4 is available
#ifdef HAVE_LZ4
#include "lz4.h"
#endif

// If ZSTD is available
#ifdef HAVE_ZSTD
#include "zstd.h"
#endif

namespace ps4 {

// A tunable constant for the exponential decay. A smaller value means heat decays slower.
constexpr double HEAT_DECAY_RATE = 0.0001;
// Entropy threshold. Data with entropy higher than this is considered incompressible.
constexpr double SHANNON_ENTROPY_THRESHOLD = 7.5;


MemoryCompressor::MemoryCompressor(CompressionAlgorithm algorithm,
                                   CompressionPolicy policy)
    : m_algorithm(algorithm), m_policy(policy) {
  ResetStats();
}

MemoryCompressor::~MemoryCompressor() noexcept {
  // Clean up compressed pages
  m_compressedPages.clear();
}

bool MemoryCompressor::Initialize() {
  // Clear existing compressed pages
  ClearAllCompressedPages();

  // Reset stats
  ResetStats();
  spdlog::info("Memory compressor initialized with algorithm {} and policy {}",
               static_cast<int>(m_algorithm), static_cast<int>(m_policy));
  return true;
}

bool MemoryCompressor::CompressPage(const uint8_t *pageData, size_t pageSize,
                                    uint64_t &outCompressedId) {
  if (!pageData || pageSize == 0) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Invalid input for compression: null data or zero size");
#endif
    return false;
  }

  // Refined check for data entropy to avoid compressing uncompressible data
  if (!IsCompressible(pageData, pageSize)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::info("Skipping compression for page with high entropy");
#endif
    return false;
  }

  std::vector<uint8_t> compressed;
  if (!CompressData(pageData, pageSize, compressed)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Compression failed for page data");
#endif
    return false;
  }

  // Calculate compression ratio
  double ratio =
      static_cast<double>(compressed.size()) / static_cast<double>(pageSize);

  // Only store the compressed page if it actually saves space
  if (ratio >= 0.95) { // If compression doesn't save at least 5%, don't bother
#ifdef DEBUG_COMPRESSOR
    spdlog::info("Compression ratio {} not beneficial, skipping", ratio);
#endif
    return false;
  }

  // Generate a new ID for the compressed page
  uint64_t compressedId = GenerateCompressedId();

  // Store the size before moving
  size_t compressedSize = compressed.size();

  // Store the compressed page
  {
    std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
    m_compressedPages[compressedId] = std::move(compressed);
  }

  // Update stats
  {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats.pagesCompressed++;
    m_stats.totalBytesOriginal += pageSize;
    m_stats.totalBytesCompressed += compressedSize;
    m_stats.totalBytesSaved += (pageSize - compressedSize);
    m_stats.compressionRatio =
        static_cast<double>(m_stats.totalBytesCompressed) /
        static_cast<double>(m_stats.totalBytesOriginal);
    m_stats.compressionEfficiency =
        static_cast<double>(m_stats.totalBytesSaved) /
        static_cast<double>(m_stats.totalBytesOriginal);
  }

  outCompressedId = compressedId;
  return true;
}

bool MemoryCompressor::DecompressPage(uint64_t compressedId,
                                      uint8_t *outPageData, size_t pageSize) {
  if (!outPageData || pageSize == 0) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Invalid output buffer for decompression: null or zero size");
#endif
    return false;
  }

  std::vector<uint8_t> compressed;

  // Retrieve the compressed page
  {
    std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
    auto it = m_compressedPages.find(compressedId);
    if (it == m_compressedPages.end()) {
#ifdef DEBUG_COMPRESSOR
      spdlog::error("Compressed page ID {} not found", compressedId);
#endif
      return false;
    }
    compressed = it->second;
  }

  // Decompress the page
  if (!DecompressData(compressed.data(), compressed.size(), outPageData,
                      pageSize)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Decompression failed for page ID {}", compressedId);
#endif
    return false;
  }

  // Update stats
  {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats.pagesDecompressed++;
  }

  return true;
}

bool MemoryCompressor::FreeCompressedPage(uint64_t compressedId) {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  return m_compressedPages.erase(compressedId) > 0;
}

void MemoryCompressor::SetCompressionAlgorithm(CompressionAlgorithm algorithm) {
  m_algorithm = algorithm;
}

void MemoryCompressor::SetCompressionPolicy(CompressionPolicy policy) {
  m_policy = policy;
}

/**
 * @brief REFINED: Checks if a page should be compressed based on a time-decaying "heat" model.
 * A page's heat is its access count, decayed by how long ago it was last accessed.
 * @param accessCount Number of times the page has been accessed.
 * @param lastAccessTime Last time the page was accessed.
 * @return True if the page is "cold" enough to be compressed, false otherwise.
 */
bool MemoryCompressor::ShouldCompressPage(
    uint64_t accessCount,
    const std::chrono::steady_clock::time_point &lastAccessTime) const {

  double heatThreshold;
  uint64_t idleTimeMsThreshold;
  GetPolicyThresholds(heatThreshold, idleTimeMsThreshold);

  auto now = std::chrono::steady_clock::now();
  auto idleTime = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastAccessTime);

  // First, check if the page has been idle long enough. This is a simple, fast check.
  if (idleTime.count() < idleTimeMsThreshold) {
      return false;
  }

  // Calculate page "heat" using an exponential decay model.
  // Heat = access_count * e^(-decay_rate * time_idle_seconds)
  // This makes recent accesses count much more towards heat than ancient accesses.
  double idleTimeSeconds = idleTime.count() / 1000.0;
  double heat = static_cast<double>(accessCount) * std::exp(-HEAT_DECAY_RATE * idleTimeSeconds);

  // If the calculated heat is below the policy's threshold, it's a candidate for compression.
  return heat < heatThreshold;
}


CompressionStats MemoryCompressor::GetStats() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats;
}

void MemoryCompressor::ResetStats() {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  m_stats = {};
  m_stats.compressionRatio = 1.0; // Default to 1:1 ratio
}

void MemoryCompressor::SetCustomThresholds(uint64_t accessThreshold,
                                           uint64_t idleTimeMs) {
  m_customHeatThreshold = static_cast<double>(accessThreshold);
  m_customIdleTimeMs = idleTimeMs;
}

void MemoryCompressor::GetPolicyThresholds(double &outHeatThreshold,
                                           uint64_t &outIdleTimeMs) const {
  switch (m_policy) {
  case POLICY_AGGRESSIVE:
    outHeatThreshold = 3.0;   // Low heat threshold, easier to compress
    outIdleTimeMs = 1000;     // 1 second
    break;

  case POLICY_BALANCED:
    outHeatThreshold = 10.0;  // A moderate heat threshold
    outIdleTimeMs = 5000;     // 5 seconds
    break;

  case POLICY_CONSERVATIVE:
    outHeatThreshold = 25.0;  // High heat threshold, harder to compress
    outIdleTimeMs = 15000;    // 15 seconds
    break;

  case POLICY_CUSTOM:
    outHeatThreshold = m_customHeatThreshold;
    outIdleTimeMs = m_customIdleTimeMs;
    break;

  default:
    outHeatThreshold = 10.0;
    outIdleTimeMs = 5000;
  }
}

uint64_t MemoryCompressor::GenerateCompressedId() {
  return m_nextCompressedId.fetch_add(1, std::memory_order_relaxed);
}

bool MemoryCompressor::CompressData(const uint8_t *data, size_t size,
                                    std::vector<uint8_t> &outCompressed) {
  switch (m_algorithm) {
  case ALGO_LZ4:
#ifdef HAVE_LZ4
  {
    // Calculate max compressed size
    int maxCompressedSize = LZ4_compressBound(static_cast<int>(size));
    outCompressed.resize(maxCompressedSize);

    // Compress data
    int compressedSize =
        LZ4_compress_default(reinterpret_cast<const char *>(data),
                             reinterpret_cast<char *>(outCompressed.data()),
                             static_cast<int>(size), maxCompressedSize);

    if (compressedSize <= 0) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }
#else
    // Fall back to ZLIB if LZ4 is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZSTD:
#ifdef HAVE_ZSTD
  {
    // Calculate max compressed size
    size_t maxCompressedSize = ZSTD_compressBound(size);
    outCompressed.resize(maxCompressedSize);

    // Compress data
    size_t compressedSize = ZSTD_compress(
        outCompressed.data(), maxCompressedSize, data, size,
        3 // Compression level (1-19, higher = better compression but slower)
    );

    if (ZSTD_isError(compressedSize)) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }
#else
    // Fall back to ZLIB if ZSTD is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZLIB: {
    // Calculate max compressed size (worst case)
    uLong maxCompressedSize = compressBound(static_cast<uLong>(size));
    outCompressed.resize(maxCompressedSize);

    // Compress data
    uLong compressedSize = static_cast<uLong>(outCompressed.size());
    int result = compress(outCompressed.data(), &compressedSize, data,
                          static_cast<uLong>(size));

    if (result != Z_OK) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }

  case ALGO_NONE: {
    // No compression, just copy the data
    outCompressed.resize(size);
    std::memcpy(outCompressed.data(), data, size);
    return true;
  }

  default:
    return false;
  }
}

bool MemoryCompressor::DecompressData(const uint8_t *compressedData,
                                      size_t compressedSize, uint8_t *outData,
                                      size_t outSize) {
  switch (m_algorithm) {
  case ALGO_LZ4:
#ifdef HAVE_LZ4
  {
    // Decompress data
    int decompressedSize = LZ4_decompress_safe(
        reinterpret_cast<const char *>(compressedData),
        reinterpret_cast<char *>(outData), static_cast<int>(compressedSize),
        static_cast<int>(outSize));

    return decompressedSize > 0 &&
           static_cast<size_t>(decompressedSize) == outSize;
  }
#else
    // Fall back to ZLIB if LZ4 is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZSTD:
#ifdef HAVE_ZSTD
  {
    // Decompress data
    size_t decompressedSize =
        ZSTD_decompress(outData, outSize, compressedData, compressedSize);

    return !ZSTD_isError(decompressedSize) && decompressedSize == outSize;
  }
#else
    // Fall back to ZLIB if ZSTD is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZLIB: {
    // Decompress data
    uLong destLen = static_cast<uLong>(outSize);
    int result = uncompress(outData, &destLen, compressedData,
                            static_cast<uLong>(compressedSize));

    return result == Z_OK && destLen == outSize;
  }

  case ALGO_NONE: {
    // No compression, just copy the data
    if (compressedSize != outSize) {
      return false;
    }
    std::memcpy(outData, compressedData, outSize);
    return true;
  }

  default:
    return false;
  }
}

void MemoryCompressor::ClearAllCompressedPages() {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  m_compressedPages.clear();
  spdlog::debug("All compressed pages cleared");
}

size_t MemoryCompressor::GetCompressedPageCount() const {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  return m_compressedPages.size();
}

double MemoryCompressor::GetCompressionRatio() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats.compressionRatio;
}

double MemoryCompressor::GetCompressionEfficiency() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats.compressionEfficiency;
}

void MemoryCompressor::LogStats() const {
  auto stats = GetStats();
  spdlog::info(
      "Compression Stats - PagesCompressed: {}, PagesDecompressed: {}, "
      "TotalOrig: {}, TotalComp: {}, Saved: {}, Ratio: {:.2f}, Efficiency: "
      "{:.2f}",
      stats.pagesCompressed, stats.pagesDecompressed, stats.totalBytesOriginal,
      stats.totalBytesCompressed, stats.totalBytesSaved, stats.compressionRatio,
      stats.compressionEfficiency);
}

void MemoryCompressor::RegisterPageAccess(uint64_t pageAddress,
                                          const uint8_t *pageData,
                                          size_t pageSize) {
  using Clock = std::chrono::steady_clock;
  auto now = Clock::now();

  // Update access info
  {
    std::lock_guard<std::mutex> lock(m_pageAccessMutex);
    auto &info = m_pageAccessInfo[pageAddress];
    info.accessCount++;
    info.lastAccessTime = now;
  }

  // Check if page is already compressed
  uint64_t compressedId = 0;
  bool isCompressed = false;
  {
    std::lock_guard<std::mutex> lock(m_pageToCompressedMutex);
    auto it = m_pageToCompressedId.find(pageAddress);
    if (it != m_pageToCompressedId.end()) {
      compressedId = it->second;
      isCompressed = true;
    }
  }

  // Decide to compress or decompress
  {
    std::lock_guard<std::mutex> lock(m_pageAccessMutex);
    auto &info = m_pageAccessInfo[pageAddress];
    if (!isCompressed) {
      if (ShouldCompressPage(info.accessCount, info.lastAccessTime)) {
        uint64_t newId;
        if (CompressPage(pageData, pageSize, newId)) {
          std::lock_guard<std::mutex> lock2(m_pageToCompressedMutex);
          m_pageToCompressedId[pageAddress] = newId;
          spdlog::debug("Compressed page 0x{:x} -> id {}", pageAddress, newId);
        }
      }
    } else {
      // Page is compressed - if now hot, decompress
      if (!ShouldCompressPage(info.accessCount, info.lastAccessTime)) {
        std::vector<uint8_t> buffer(pageSize);
        if (DecompressPage(compressedId, buffer.data(), pageSize)) {
          std::lock_guard<std::mutex> lock2(m_pageToCompressedMutex);
          m_pageToCompressedId.erase(pageAddress);
          FreeCompressedPage(compressedId);
          spdlog::debug("Decompressed page 0x{:x} from id {}", pageAddress,
                        compressedId);
        }
      }
    }
  }
}

/**
 * @brief REFINED: Checks if data is compressible by calculating its Shannon entropy.
 * Data with high entropy (close to random) is unlikely to compress well.
 * @param data Data to analyze.
 * @param size Size of data.
 * @return True if entropy is below a threshold, indicating it's likely compressible.
 */
bool MemoryCompressor::IsCompressible(const uint8_t *data, size_t size) {
    if (size == 0) {
        return false;
    }

    // Calculate frequency of each byte value.
    std::array<uint64_t, 256> counts{}; // Initialize to all zeros
    for (size_t i = 0; i < size; ++i) {
        counts[data[i]]++;
    }

    // Calculate Shannon entropy.
    double entropy = 0.0;
    for (uint64_t count : counts) {
        if (count > 0) {
            double probability = static_cast<double>(count) / static_cast<double>(size);
            entropy -= probability * std::log2(probability);
        }
    }

    // A quick check for a page of all zeros (or any single byte), which has entropy 0
    // and is highly compressible but might not be caught by CompressPage's ratio check
    // if the compressed format has overhead.
    if (entropy < 0.1) {
        return true;
    }

    // If entropy is high, the data is random-like and won't compress well.
    // The theoretical maximum entropy is 8.0 for 256 byte values.
    // Encrypted or already compressed data will have very high entropy.
    return entropy < SHANNON_ENTROPY_THRESHOLD;
}

} // namespace ps4