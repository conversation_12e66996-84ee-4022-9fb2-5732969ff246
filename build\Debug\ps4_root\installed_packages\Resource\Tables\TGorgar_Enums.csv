RSID_TGORGAR_START, 3450,,,
 ,[Offset], GORGAR_FLYER_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON>\PBTGorgar, 1,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsITAL, 4,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsGERM, 5,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsSPAN, 6,
 ,[Offset], <PERSON><PERSON><PERSON>\InstructionsPORT, 7,
 ,[Offset], Go<PERSON>r\InstructionsDUTCH, 8,
 ,[Offset], tables\Gorgar_BG_scroll, 9,
RSID_TGORGAR_SCRIPT, 3451,,,
RSID_TGORGAR_LIGHTS, 3452,,,
RSID_TGORGAR_CAMERAS, 3453,,,
RSID_TGORGAR_LAMP_TEXTURES, 3454,,,
 ,[Offset], lamp_01_off, 0,
 ,[Offset], lamp_01_on, 1,
 ,[Offset], lamp_02_off, 2,
 ,[Offset], lamp_02_on, 3,
 ,[Offset], lamp_03_off, 4,
 ,[Offset], lamp_03_on, 5,
 ,[Offset], lamp_04_off, 6,
 ,[Offset], lamp_04_on, 7,
 ,[Offset], lamp_05_off, 8,
 ,[Offset], lamp_05_on, 9,
 ,[Offset], lamp_06_off, 10,
 ,[Offset], lamp_06_on, 11,
 ,[Offset], lamp_07_off, 12,
 ,[Offset], lamp_07_on, 13,
 ,[Offset], lamp_08_off, 14,
 ,[Offset], lamp_08_on, 15,
 ,[Offset], lamp_09_off, 16,
 ,[Offset], lamp_09_on, 17,
 ,[Offset], lamp_10_off, 18,
 ,[Offset], lamp_10_on, 19,
 ,[Offset], lamp_11_off, 20,
 ,[Offset], lamp_11_on, 21,
 ,[Offset], lamp_12_off, 22,
 ,[Offset], lamp_12_on, 23,
 ,[Offset], lamp_13_off, 24,
 ,[Offset], lamp_13_on, 25,
 ,[Offset], lamp_14_off, 26,
 ,[Offset], lamp_14_on, 27,
 ,[Offset], lamp_15_off, 28,
 ,[Offset], lamp_15_on, 29,
 ,[Offset], lamp_16_off, 30,
 ,[Offset], lamp_16_on, 31,
 ,[Offset], lamp_17_off, 32,
 ,[Offset], lamp_17_on, 33,
 ,[Offset], lamp_18_off, 34,
 ,[Offset], lamp_18_on, 35,
 ,[Offset], lamp_19_off, 36,
 ,[Offset], lamp_19_on, 37,
 ,[Offset], lamp_20_off, 38,
 ,[Offset], lamp_20_on, 39,
 ,[Offset], lamp_21_off, 40,
 ,[Offset], lamp_21_on, 41,
 ,[Offset], lamp_22_off, 42,
 ,[Offset], lamp_22_on, 43,
 ,[Offset], lamp_23_off, 44,
 ,[Offset], lamp_23_on, 45,
 ,[Offset], lamp_24_off, 46,
 ,[Offset], lamp_24_on, 47,
 ,[Offset], lamp_25_off, 48,
 ,[Offset], lamp_25_on, 49,
 ,[Offset], lamp_26_off, 50,
 ,[Offset], lamp_26_on, 51,
 ,[Offset], lamp_27_off, 52,
 ,[Offset], lamp_27_on, 53,
 ,[Offset], lamp_28_off, 54,
 ,[Offset], lamp_28_on, 55,
 ,[Offset], lamp_29_off, 56,
 ,[Offset], lamp_29_on, 57,
 ,[Offset], lamp_30_off, 58,
 ,[Offset], lamp_30_on, 59,
 ,[Offset], lamp_31_off, 60,
 ,[Offset], lamp_31_on, 61,
 ,[Offset], lamp_32_off, 62,
 ,[Offset], lamp_32_on, 63,
 ,[Offset], lamp_33_off, 64,
 ,[Offset], lamp_33_on, 65,
 ,[Offset], lamp_34_off, 66,
 ,[Offset], lamp_34_on, 67,
 ,[Offset], lamp_35_off, 68,
 ,[Offset], lamp_35_on, 69,
 ,[Offset], lamp_36_off, 70,
 ,[Offset], lamp_36_on, 71,
 ,[Offset], lamp_37_off, 72,
 ,[Offset], lamp_37_on, 73,
 ,[Offset], lamp_38_off, 74,
 ,[Offset], lamp_38_on, 75,
 ,[Offset], lamp_39_off, 76,
 ,[Offset], lamp_39_on, 77,
 ,[Offset], lamp_40_off, 78,
 ,[Offset], lamp_40_on, 79,
 ,[Offset], lamp_41_off, 80,
 ,[Offset], lamp_41_on, 81,
 ,[Offset], lamp_42_off, 82,
 ,[Offset], lamp_42_on, 83,
 ,[Offset], lamp_43_off, 84,
 ,[Offset], lamp_43_on, 85,
 ,[Offset], lamp_44_bumper_off, 86,
 ,[Offset], lamp_44_bumper_on, 87,
 ,[Offset], lamp_45_bumper_off, 88,
 ,[Offset], lamp_45_bumper_on, 89,
 ,[Offset], lamp_46_bumper_off, 90,
 ,[Offset], lamp_46_bumper_on, 91,
 ,[Offset], lamp_47_bumper_off, 92,
 ,[Offset], lamp_47_bumper_on, 93,
RSID_TGORGAR_TEXTURES, 3455,,,
 ,[Offset], G_A1_0, 0,
 ,[Offset], G_A2_0, 1,
 ,[Offset], G_B1_0, 2,
 ,[Offset], G_B2_0, 3,
 ,[Offset], G_A3_0, 4,
 ,[Offset], G_A4_0, 5,
 ,[Offset], G_B3_0, 6,
 ,[Offset], G_B4_0, 7,
 ,[Offset], G_A5_0, 8,
 ,[Offset], G_A6_0, 9,
 ,[Offset], G_B5_0, 10,
 ,[Offset], G_B6_0, 11,
 ,[Offset], G_A7_0, 12,
 ,[Offset], G_A8_0, 13,
 ,[Offset], G_B7_0, 14,
 ,[Offset], G_B8_0, 15,
 ,[Offset], G_C1_0, 16,
 ,[Offset], G_C2_0, 17,
 ,[Offset], G_D1_0, 18,
 ,[Offset], G_D2_0, 19,
 ,[Offset], G_C3_0, 20,
 ,[Offset], G_C4_0, 21,
 ,[Offset], G_D3_0, 22,
 ,[Offset], G_D4_0, 23,
 ,[Offset], G_C5_0, 24,
 ,[Offset], G_C6_0, 25,
 ,[Offset], G_D5_0, 26,
 ,[Offset], G_D6_0, 27,
 ,[Offset], G_C7_0, 28,
 ,[Offset], G_C8_0, 29,
 ,[Offset], G_D7_0, 30,
 ,[Offset], G_D8_0, 31,
 ,[Offset], G_E1_0, 32,
 ,[Offset], G_E2_0, 33,
 ,[Offset], G_F1_0, 34,
 ,[Offset], G_F2_0, 35,
 ,[Offset], G_E3_0, 36,
 ,[Offset], G_E4_0, 37,
 ,[Offset], G_F3_0, 38,
 ,[Offset], G_F4_0, 39,
 ,[Offset], G_E5_0, 40,
 ,[Offset], G_E6_0, 41,
 ,[Offset], G_F5_0, 42,
 ,[Offset], G_F6_0, 43,
 ,[Offset], G_E7_0, 44,
 ,[Offset], G_E8_0, 45,
 ,[Offset], G_F7_0, 46,
 ,[Offset], G_F8_0, 47,
 ,[Offset], G_G1_0, 48,
 ,[Offset], G_G2_0, 49,
 ,[Offset], G_H1_0, 50,
 ,[Offset], G_H2_0, 51,
 ,[Offset], G_G3_0, 52,
 ,[Offset], G_G4_0, 53,
 ,[Offset], G_H3_0, 54,
 ,[Offset], G_H4_0, 55,
 ,[Offset], G_G5_0, 56,
 ,[Offset], G_G6_0, 57,
 ,[Offset], G_H5_0, 58,
 ,[Offset], G_H6_0, 59,
 ,[Offset], G_G7_0, 60,
 ,[Offset], G_G8_0, 61,
 ,[Offset], G_H7_0, 62,
 ,[Offset], G_H8_0, 63,
 ,[Offset], color wheel, 64,
 ,[Offset], Buttons_Parts, 65,
 ,[Offset], GorGar_Table_down_B, 66,
 ,[Offset], GorGar_Table_up_A, 67,
 ,[Offset], GorGar_Table_up_C, 68,
 ,[Offset], GG_TableFront_Side, 69,
 ,[Offset], backGlass, 70,
 ,[Offset], metal_trim, 71,
 ,[Offset], metal_legs, 72,
 ,[Offset], metal_parts01, 73,
 ,[Offset], metal_parts02, 74,
 ,[Offset], TableRules_R1, 75,
 ,[Offset], TableRules_R2, 76,
 ,[Offset], TableRules_R3, 77,
 ,[Offset], TableRules_R4, 78,
 ,[Offset], TableRules_L1, 79,
 ,[Offset], TableRules_L2, 80,
 ,[Offset], TableRules_L3, 81,
 ,[Offset], TableRules_L4, 82,
 ,[Offset], plunger_plate_baked, 83,
 ,[Offset], plunger_metal, 84,
 ,[Offset], backglassA_0, 85,
 ,[Offset], backglassB_0, 86,
 ,[Offset], backglassC_0, 87,
 ,[Offset], backglassD_0, 88,
 ,[Offset], backglassE_0, 89,
 ,[Offset], backglassF_0, 90,
 ,[Offset], backglassG_0, 91,
 ,[Offset], backglassH_0, 92,
 ,[Offset], metal_tex, 93,
 ,[Offset], metal_side, 94,
 ,[Offset], metal-parts01 copy, 95,
 ,[Offset], red_base, 96,
 ,[Offset], metal front, 97,
 ,[Offset], CoinSlots, 98,
 ,[Offset], glass, 99,
 ,[Offset], bumpers_A, 100,
 ,[Offset], bumpers_B, 101,
 ,[Offset], bumpers_C, 102,
 ,[Offset], spinner, 103,
 ,[Offset], tile, 104,
 ,[Offset], tmp_orange, 105,
 ,[Offset], tmp_gray, 106,
 ,[Offset], tmp_yellow, 107,
 ,[Offset], target, 108,
 ,[Offset], target alt, 109,
 ,[Offset], post, 110,
 ,[Offset], flipper, 111,
 ,[Offset], rubber, 112,
 ,[Offset], screw, 113,
 ,[Offset], screw alt, 114,
 ,[Offset], screw white, 115,
 ,[Offset], blackscrew, 116,
 ,[Offset], speaker, 117,
 ,[Offset], jelly, 118,
 ,[Offset], post red, 119,
 ,[Offset], lightbulb, 120,
 ,[Offset], Lower_Playfield, 121,
 ,[Offset], Upper_Playfield, 122,
 ,[Offset], PopBumperBody, 123,
 ,[Offset], backGlass, 124,
 ,[Offset], Black_Grain, 125,
 ,[Offset], chrome_trim_walls, 126,
 ,[Offset], Coin_Slot, 127,
 ,[Offset], Extra_Metal_Parts, 128,
 ,[Offset], Extra_Metal_Parts_1, 129,
 ,[Offset], Extra_Metal_Parts_2, 130,
 ,[Offset], GG_Apron, 131,
 ,[Offset], GG_TableFront_Sideb, 132,
 ,[Offset], Gorgar_BackGlass, 133,
 ,[Offset], Gorgar_Bumper_Body, 134,
 ,[Offset], Gorgar_Bumper_Body_LIT, 135,
 ,[Offset], Gorgar_Coinslots, 136,
 ,[Offset], Gorgar_PlasticA, 137,
 ,[Offset], Gorgar_PlasticB, 138,
 ,[Offset], Gorgar_Roundbumper, 139,
 ,[Offset], Gorgar_roundbumper_bump, 140,
 ,[Offset], Gorgar_RoundBumper_Lit, 141,
 ,[Offset], Gorgar_RoundBumperBase, 142,
 ,[Offset], HarleyBumperBody, 143,
 ,[Offset], SpeakerHD, 144,
 ,[Offset], metal-parts01, 145,
 ,[Offset], metal-parts02, 146,
 ,[Offset], metal_parts, 147,
 ,[Offset], Silver Metal Screws_Temp, 148,
RSID_TGORGAR_MODELS, 3456,,,
 ,[Offset], Flipper_L, 0,
 ,[Offset], Flipper_R, 1,
 ,[Offset], floor, 2,
 ,[Offset], clip, 3,
 ,[Offset], backglass, 4,
 ,[Offset], glass, 5,
 ,[Offset], metal, 6,
 ,[Offset], parts, 7,
 ,[Offset], plunger, 8,
 ,[Offset], bumper A, 9,
 ,[Offset], bumper B, 10,
 ,[Offset], bumper C, 11,
 ,[Offset], bumper D, 12,
 ,[Offset], bumper E, 13,
 ,[Offset], bumper F, 14,
 ,[Offset], bumper G, 15,
 ,[Offset], tile, 16,
 ,[Offset], target, 17,
 ,[Offset], target alt, 18,
 ,[Offset], onewaygate, 19,
 ,[Offset], wire long, 20,
 ,[Offset], wire short, 21,
 ,[Offset], stopper, 22,
 ,[Offset], slingshot A, 23,
 ,[Offset], slingshot A ext, 24,
 ,[Offset], slingshot B, 25,
 ,[Offset], slingshot B ext, 26,
 ,[Offset], pop bumper A, 27,
 ,[Offset], trap A, 28,
 ,[Offset], spinner, 29,
 ,[Offset], magnet, 30,
 ,[Offset], Apron, 31,
 ,[Offset], Bulbs, 32,
 ,[Offset], Cabinet_Backglass, 33,
 ,[Offset], Cabinet_Body, 34,
 ,[Offset], Cabinet_Buttons, 35,
 ,[Offset], Cabinet_Metals, 36,
 ,[Offset], Light_Cutouts, 37,
 ,[Offset], Metal_Pieces, 38,
 ,[Offset], Plastic_Pieces, 39,
 ,[Offset], Plastic_Posts, 40,
 ,[Offset], Pop_Bumpers, 41,
 ,[Offset], Rubber_Pieces, 42,
 ,[Offset], Rubber_Pieces_2, 43,
RSID_TGORGAR_MODELS_LODS, 3457,,,
 ,[Offset], Flipper_L, 0,
 ,[Offset], Flipper_R, 1,
 ,[Offset], floor, 2,
 ,[Offset], clip, 3,
 ,[Offset], backglass, 4,
 ,[Offset], glass, 5,
 ,[Offset], metal, 6,
 ,[Offset], parts, 7,
 ,[Offset], plunger, 8,
 ,[Offset], bumper A, 9,
 ,[Offset], bumper B, 10,
 ,[Offset], bumper C, 11,
 ,[Offset], bumper D, 12,
 ,[Offset], bumper E, 13,
 ,[Offset], bumper F, 14,
 ,[Offset], bumper G, 15,
 ,[Offset], tile, 16,
 ,[Offset], target, 17,
 ,[Offset], target alt, 18,
 ,[Offset], onewaygate, 19,
 ,[Offset], wire long, 20,
 ,[Offset], wire short, 21,
 ,[Offset], stopper, 22,
 ,[Offset], slingshot A, 23,
 ,[Offset], slingshot A ext, 24,
 ,[Offset], slingshot B, 25,
 ,[Offset], slingshot B ext, 26,
 ,[Offset], pop bumper A, 27,
 ,[Offset], trap A, 28,
 ,[Offset], spinner, 29,
 ,[Offset], magnet, 30,
 ,[Offset], Apron, 31,
 ,[Offset], Bulbs, 32,
 ,[Offset], Cabinet_Backglass, 33,
 ,[Offset], Cabinet_Body, 34,
 ,[Offset], Cabinet_Buttons, 35,
 ,[Offset], Cabinet_Metals, 36,
 ,[Offset], Light_Cutouts, 37,
 ,[Offset], Metal_Pieces, 38,
 ,[Offset], Plastic_Pieces, 39,
 ,[Offset], Plastic_Posts, 40,
 ,[Offset], Pop_Bumpers, 41,
 ,[Offset], Rubber_Pieces, 42,
 ,[Offset], Rubber_Pieces_2, 43,
RSID_TGORGAR_COLLISIONS, 3458,,,
 ,[Offset], Flipper_Left_Front, 0,
 ,[Offset], Flipper_Left_Back, 1,
 ,[Offset], Flipper_Right_Front, 2,
 ,[Offset], Flipper_Right_Back, 3,
 ,[Offset], col wall 01, 4,
 ,[Offset], col wall 02, 5,
 ,[Offset], col wall 03, 6,
 ,[Offset], col wall 04, 7,
 ,[Offset], col wall 05, 8,
 ,[Offset], col floor 01, 9,
 ,[Offset], col floor 02, 10,
 ,[Offset], col alt wall 01, 11,
 ,[Offset], col alt wall 02, 12,
 ,[Offset], col alt wall 03, 13,
 ,[Offset], col alt wall 04, 14,
 ,[Offset], col alt wall 05, 15,
 ,[Offset], col alt wall 06, 16,
 ,[Offset], col alt wall 07, 17,
 ,[Offset], col alt wall 08, 18,
 ,[Offset], col alt wall 09, 19,
 ,[Offset], col arc 01, 20,
 ,[Offset], col arc 02, 21,
 ,[Offset], col arc 03, 22,
 ,[Offset], col arc 04, 23,
 ,[Offset], col arc 05, 24,
 ,[Offset], platform 1, 25,
 ,[Offset], plunger col, 26,
 ,[Offset], plunger area col, 27,
 ,[Offset], slingshot A col, 28,
 ,[Offset], slingshot B col, 29,
 ,[Offset], tile col, 30,
 ,[Offset], onewaygate col, 31,
 ,[Offset], stopper col, 32,
 ,[Offset], target col, 33,
 ,[Offset], pop bumper A col, 34,
 ,[Offset], spinner, 35,
 ,[Offset], bumper A col, 36,
 ,[Offset], bumper B col, 37,
 ,[Offset], bumper C col, 38,
 ,[Offset], bumper D col, 39,
 ,[Offset], bumper E col, 40,
 ,[Offset], bumper F col, 41,
 ,[Offset], trap A, 42,
 ,[Offset], target alt col, 43,
 ,[Offset], magnet col, 44,
 ,[Offset], bumper G col, 45,
RSID_TGORGAR_PLACEMENT, 3459,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_TGORGAR_SOUNDS, 3460,,,
 ,[Offset], Lights, 0,
 ,[Offset], Lights, 1,
 ,[Offset], Lights, 2,
 ,[Offset], Lights, 3,
 ,[Offset], Lights, 4,
 ,[Offset], Lights, 5,
 ,[Offset], Lights, 6,
 ,[Offset], Lights, 7,
 ,[Offset], Lights, 8,
 ,[Offset], Lights, 9,
 ,[Offset], Lights, 10,
 ,[Offset], Lights, 11,
 ,[Offset], Lights, 12,
 ,[Offset], Lights, 13,
 ,[Offset], Lights, 14,
 ,[Offset], Lights, 15,
 ,[Offset], Lights, 16,
 ,[Offset], Lights, 17,
 ,[Offset], Lights, 18,
 ,[Offset], Lights, 19,
 ,[Offset], Lights, 20,
 ,[Offset], Lights, 21,
 ,[Offset], Lights, 22,
 ,[Offset], Lights, 23,
 ,[Offset], Lights, 24,
 ,[Offset], Lights, 25,
 ,[Offset], Lights, 26,
 ,[Offset], Lights, 27,
 ,[Offset], Lights, 28,
 ,[Offset], Lights, 29,
 ,[Offset], Lights, 30,
 ,[Offset], Lights, 31,
 ,[Offset], Lights, 32,
 ,[Offset], Lights, 33,
 ,[Offset], GG_Gorgar_Speaks, 34,
 ,[Offset], GG_Me_Hurt, 35,
 ,[Offset], GG_Me_Got_You, 36,
 ,[Offset], GG_Gorgar, 37,
 ,[Offset], GG_You_Hurt_Gorgar, 38,
 ,[Offset], GG_You_Beat_Me, 39,
 ,[Offset], GG_You_Beat_Gorgar, 40,
 ,[Offset], GG_Me_Gorgar, 41,
 ,[Offset], GG_Beat_Me, 42,
 ,[Offset], GG_Bumpers_&_Spinner, 43,
 ,[Offset], GG_Drop_Targets, 44,
 ,[Offset], GG_Drop_Targets_2, 45,
 ,[Offset], GG_Drop_Targets_Complete, 46,
 ,[Offset], GG_Heartbeat_loop1, 47,
 ,[Offset], GG_Heartbeat_loop2, 48,
 ,[Offset], GG_Heartbeat_loop3, 49,
 ,[Offset], GG_Heartbeat_loop4, 50,
 ,[Offset], GG_Heartbeat_loop5, 51,
 ,[Offset], GG_Heartbeat_loop6, 52,
 ,[Offset], GG_Heartbeat_loop7, 53,
 ,[Offset], GG_Heartbeat_loop8, 54,
 ,[Offset], GG_Heartbeat_loop9, 55,
 ,[Offset], GG_Heartbeat_loop10, 56,
 ,[Offset], GG_Heartbeat_loop11, 57,
 ,[Offset], GG_Heartbeat_loop12, 58,
 ,[Offset], GG_Heartbeat_Single, 59,
 ,[Offset], GG_Left_Trap, 60,
 ,[Offset], GG_LostBall_A_Loop, 61,
 ,[Offset], GG_LostBall_B, 62,
 ,[Offset], GG_Slingshots, 63,
 ,[Offset], GG_Targets, 64,
 ,[Offset], GG_Targets_2, 65,
 ,[Offset], GG_TILT, 66,
 ,[Offset], GG_Top_Gates, 67,
 ,[Offset], GG_Top_Gates_Complete, 68,
 ,[Offset], GG_Top_left_trap, 69,
 ,[Offset], GG_wire_Rollovers, 70,
RSID_GORGAR_HUD, 3461,,,
RSID_GORGAR_HUDHD, 3462,,,
RSID_GORGAR_HUDHDD, 3463,,,
RSID_FONTTABLE_GORGAR_FONT, 3464,,,
RSID_FONT_GORGAR_HUD, 3465,,,
RSID_FONTTABLE_GORGAR_FONTHD, 3466,,,
RSID_FONT_GORGAR_HUDHD, 3467,,,
RSID_FONTTABLE_GORGAR_FONTHDD, 3468,,,
RSID_FONT_GORGAR_HUDHDD, 3469,,,
RSID_TGORGAR_VERSION, 3470,,,
RSID_TGORGAR_END, 3471,,,
