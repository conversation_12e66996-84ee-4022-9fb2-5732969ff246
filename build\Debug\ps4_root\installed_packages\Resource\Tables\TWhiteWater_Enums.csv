RSID_TWHITEWATER_START, 2750,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], White<PERSON>ater\PBTWhiteWater, 1,
 ,[Offset], WhiteWater\InstructionsENG, 2,
 ,[Offset], WhiteWater\InstructionsFR, 3,
 ,[Offset], WhiteWater\InstructionsITAL, 4,
 ,[Offset], WhiteWater\InstructionsGERM, 5,
 ,[Offset], WhiteWater\InstructionsSPAN, 6,
 ,[Offset], WhiteWater\InstructionsPORT, 7,
 ,[Offset], WhiteWater\InstructionsDUTCH, 8,
 ,[Offset], WhiteWater\InstructionsDUTCH, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TWHITEWATER_LIGHTS, 2751,,,
RSID_TWHITEWATER_CAMERAS, 2752,,,
RSID_TWHITEWATER_LAMP_TEXTURES, 2753,,,
 ,[Offset], L_11_off, 0,
 ,[Offset], L_11_on, 1,
 ,[Offset], L_12_off, 2,
 ,[Offset], L_12_on, 3,
 ,[Offset], L_13_off, 4,
 ,[Offset], L_13_on, 5,
 ,[Offset], L_14_off, 6,
 ,[Offset], L_14_on, 7,
 ,[Offset], L_15_off, 8,
 ,[Offset], L_15_on, 9,
 ,[Offset], L_16_off, 10,
 ,[Offset], L_16_on, 11,
 ,[Offset], L_17_off, 12,
 ,[Offset], L_17_on, 13,
 ,[Offset], L_18_off, 14,
 ,[Offset], L_18_on, 15,
 ,[Offset], L_21_off, 16,
 ,[Offset], L_21_on, 17,
 ,[Offset], L_22_off, 18,
 ,[Offset], L_22_on, 19,
 ,[Offset], L_23_off, 20,
 ,[Offset], L_23_on, 21,
 ,[Offset], L_24_off, 22,
 ,[Offset], L_24_on, 23,
 ,[Offset], L_25_off, 24,
 ,[Offset], L_25_on, 25,
 ,[Offset], L_26_off, 26,
 ,[Offset], L_26_on, 27,
 ,[Offset], L_27_off, 28,
 ,[Offset], L_27_on, 29,
 ,[Offset], L_28_off, 30,
 ,[Offset], L_28_on, 31,
 ,[Offset], L_31_off, 32,
 ,[Offset], L_31_on, 33,
 ,[Offset], L_32_off, 34,
 ,[Offset], L_32_on, 35,
 ,[Offset], L_33_off, 36,
 ,[Offset], L_33_on, 37,
 ,[Offset], L_34_off, 38,
 ,[Offset], L_34_on, 39,
 ,[Offset], L_35_off, 40,
 ,[Offset], L_35_on, 41,
 ,[Offset], L_36_off, 42,
 ,[Offset], L_36_on, 43,
 ,[Offset], L_37_off, 44,
 ,[Offset], L_37_on, 45,
 ,[Offset], L_38_off, 46,
 ,[Offset], L_38_on, 47,
 ,[Offset], L_41_off, 48,
 ,[Offset], L_41_on, 49,
 ,[Offset], L_42_off, 50,
 ,[Offset], L_42_on, 51,
 ,[Offset], L_43_off, 52,
 ,[Offset], L_43_on, 53,
 ,[Offset], L_44_off, 54,
 ,[Offset], L_44_on, 55,
 ,[Offset], L_45_off, 56,
 ,[Offset], L_45_on, 57,
 ,[Offset], L_46_off, 58,
 ,[Offset], L_46_on, 59,
 ,[Offset], L_47_off, 60,
 ,[Offset], L_47_on, 61,
 ,[Offset], L_48_off, 62,
 ,[Offset], L_48_on, 63,
 ,[Offset], L_51_off, 64,
 ,[Offset], L_51_on, 65,
 ,[Offset], L_52_off, 66,
 ,[Offset], L_52_on, 67,
 ,[Offset], L_53_off, 68,
 ,[Offset], L_53_on, 69,
 ,[Offset], L_54_off, 70,
 ,[Offset], L_54_on, 71,
 ,[Offset], L_55_off, 72,
 ,[Offset], L_55_on, 73,
 ,[Offset], L_56_off, 74,
 ,[Offset], L_56_on, 75,
 ,[Offset], L_57_off, 76,
 ,[Offset], L_57_on, 77,
 ,[Offset], L_58_off, 78,
 ,[Offset], L_58_on, 79,
 ,[Offset], L_61_off, 80,
 ,[Offset], L_61_on, 81,
 ,[Offset], L_62_off, 82,
 ,[Offset], L_62_on, 83,
 ,[Offset], L_63_off, 84,
 ,[Offset], L_63_on, 85,
 ,[Offset], L_64_off, 86,
 ,[Offset], L_64_on, 87,
 ,[Offset], L_65_off, 88,
 ,[Offset], L_65_on, 89,
 ,[Offset], L_66_off, 90,
 ,[Offset], L_66_on, 91,
 ,[Offset], L_67_off, 92,
 ,[Offset], L_67_on, 93,
 ,[Offset], L_68_off, 94,
 ,[Offset], L_68_on, 95,
 ,[Offset], L_71_off, 96,
 ,[Offset], L_71_on, 97,
 ,[Offset], L_72_off, 98,
 ,[Offset], L_72_on, 99,
 ,[Offset], L_73_off, 100,
 ,[Offset], L_73_on, 101,
 ,[Offset], L_74_off, 102,
 ,[Offset], L_74_on, 103,
 ,[Offset], L_75_off, 104,
 ,[Offset], L_75_on, 105,
 ,[Offset], L_76_off, 106,
 ,[Offset], L_76_on, 107,
 ,[Offset], L_77_off, 108,
 ,[Offset], L_77_on, 109,
 ,[Offset], L_78_off, 110,
 ,[Offset], L_78_on, 111,
 ,[Offset], L_81_off, 112,
 ,[Offset], L_81_on, 113,
 ,[Offset], L_82_off, 114,
 ,[Offset], L_82_on, 115,
 ,[Offset], L_83_off, 116,
 ,[Offset], L_83_on, 117,
 ,[Offset], L_84_off, 118,
 ,[Offset], L_84_on, 119,
 ,[Offset], L_84_on, 120,
 ,[Offset], L_84_on, 121,
 ,[Offset], L_84_on, 122,
 ,[Offset], L_84_on, 123,
 ,[Offset], L_84_on, 124,
 ,[Offset], L_84_on, 125,
 ,[Offset], L_84_on, 126,
 ,[Offset], L_84_on, 127,
 ,[Offset], F_17_Off, 128,
 ,[Offset], F_17_On, 129,
 ,[Offset], F_17B_Off, 130,
 ,[Offset], F_17B_On, 131,
 ,[Offset], F_18_Off, 132,
 ,[Offset], F_18_On, 133,
 ,[Offset], F_18B_Off, 134,
 ,[Offset], F_18B_On, 135,
 ,[Offset], F_19_Off, 136,
 ,[Offset], F_19_On, 137,
 ,[Offset], F_19B_Off, 138,
 ,[Offset], F_19B_On, 139,
 ,[Offset], F_20_Off, 140,
 ,[Offset], F_20_On, 141,
 ,[Offset], F_20B_Off, 142,
 ,[Offset], F_20B_On, 143,
 ,[Offset], F_21_Off, 144,
 ,[Offset], F_21_On, 145,
 ,[Offset], F_22_Off, 146,
 ,[Offset], F_22_On, 147,
 ,[Offset], F_22B_Off, 148,
 ,[Offset], F_22B_On, 149,
 ,[Offset], F_23_Off, 150,
 ,[Offset], F_23_On, 151,
 ,[Offset], F_23B_Off, 152,
 ,[Offset], F_23B_On, 153,
 ,[Offset], F_24_Off, 154,
 ,[Offset], F_24_On, 155,
RSID_TWHITEWATER_TEXTURES, 2754,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], Apron_t_c, 1,
 ,[Offset], backglass, 2,
 ,[Offset], BigFootFur_t_c, 3,
 ,[Offset], BigFootBody_t_c, 4,
 ,[Offset], black_post, 5,
 ,[Offset], blue_posts, 6,
 ,[Offset], BodyExtend_t_c, 7,
 ,[Offset], cabinet, 8,
 ,[Offset], cabinet_front, 9,
 ,[Offset], clear_post, 10,
 ,[Offset], Habit2, 11,
 ,[Offset], metal_parts, 12,
 ,[Offset], plastics, 13,
 ,[Offset], PlungerRamp, 14,
 ,[Offset], Ramp, 15,
 ,[Offset], ramp01 stickers, 16,
 ,[Offset], ramp3 stickers, 17,
 ,[Offset], Ramp3, 18,
 ,[Offset], Ramp3_Metals, 19,
 ,[Offset], Ramp3Stickers, 20,
 ,[Offset], Ramp4, 21,
 ,[Offset], Ramp4Missing, 22,
 ,[Offset], Ramp4MissingStickers, 23,
 ,[Offset], Ramp5, 24,
 ,[Offset], Ramp5_StickersMetal, 25,
 ,[Offset], Ramp6, 26,
 ,[Offset], Ramp6_Missing, 27,
 ,[Offset], rampbasemetal, 28,
 ,[Offset], Revised_plungerramp, 29,
 ,[Offset], Rocks_t_c, 30,
 ,[Offset], rocks02_t_c, 31,
 ,[Offset], rocks03_t_c, 32,
 ,[Offset], rocks04_t_c, 33,
 ,[Offset], rocks05_t_c, 34,
 ,[Offset], rocks06_t_c, 35,
 ,[Offset], rubberband, 36,
 ,[Offset], TopBox_t_c, 37,
 ,[Offset], upper_playfield, 38,
 ,[Offset], vertical_plastics, 39,
 ,[Offset], white_bulb, 40,
 ,[Offset], white_waterroundhabit, 41,
 ,[Offset], whitewaterramp4, 42,
 ,[Offset], whitewaterramp4_stickers, 43,
 ,[Offset], popbumperbody, 44,
 ,[Offset], silver metal screws_temp, 45,
 ,[Offset], metal_walls, 46,
 ,[Offset], flipper, 47,
 ,[Offset], generic_metal, 48,
 ,[Offset], bumper_hamer, 49,
 ,[Offset], bumper_sensors, 50,
 ,[Offset], cabinet_Metals, 51,
 ,[Offset], ClearPlasticPost_01, 52,
 ,[Offset], WW_Bottom, 53,
 ,[Offset], WW_Top, 54,
 ,[Offset], rubber_band_black, 55,
 ,[Offset], red_target, 56,
 ,[Offset], black_wood, 57,
 ,[Offset], Harley_Gate, 58,
 ,[Offset], DarkBlue_Target, 59,
 ,[Offset], Green_Target, 60,
 ,[Offset], White_Target, 61,
 ,[Offset], Yellow_Target, 62,
 ,[Offset], Extra_Metal_Parts, 63,
 ,[Offset], Metal front, 64,
 ,[Offset], Black_Metal, 65,
 ,[Offset], Blue_Bulb, 66,
 ,[Offset], Plunger, 67,
 ,[Offset], Rails, 68,
RSID_TWHITEWATER_MODELS, 2755,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Flipper, 2,
 ,[Offset], Metal_Pieces, 3,
 ,[Offset], Metal_Walls, 4,
 ,[Offset], Plastics, 5,
 ,[Offset], Plastics_Vertical, 6,
 ,[Offset], Playfield, 7,
 ,[Offset], Playfield_Upper, 8,
 ,[Offset], Posts_Black, 9,
 ,[Offset], Posts_Blue, 10,
 ,[Offset], Posts_Plastic, 11,
 ,[Offset], Ramp_Back_Left, 12,
 ,[Offset], Ramp_Back_Mid, 13,
 ,[Offset], Ramp_Bowl, 14,
 ,[Offset], Ramp_Left_Return, 15,
 ,[Offset], Ramp_Right_Return, 16,
 ,[Offset], Ramp_Stickers, 17,
 ,[Offset], Ramp_Yeti, 18,
 ,[Offset], Rock_Back, 19,
 ,[Offset], Rock_Bigfoot_Cave, 20,
 ,[Offset], Rock_Boulder_Garden, 21,
 ,[Offset], Rock_Left, 22,
 ,[Offset], Rock_Right, 23,
 ,[Offset], Rock_Right_B, 24,
 ,[Offset], Rubber, 25,
 ,[Offset], Slingshot_Left, 26,
 ,[Offset], Slingshot_Right, 27,
 ,[Offset], Target_Blue, 28,
 ,[Offset], Target_Green, 29,
 ,[Offset], Target_White, 30,
 ,[Offset], Target_Orange, 31,
 ,[Offset], Wire, 32,
 ,[Offset], Wooden_Rails, 33,
 ,[Offset], Yeti, 34,
 ,[Offset], Plunger, 35,
 ,[Offset], Gate_A, 36,
 ,[Offset], Gate_B, 37,
 ,[Offset], Gate_C, 38,
 ,[Offset], Habit_Curl, 39,
 ,[Offset], Plunger_Ramp, 40,
 ,[Offset], Ramp_Switch, 41,
 ,[Offset], Target_Yellow, 42,
 ,[Offset], Backglass, 43,
 ,[Offset], Cabinet, 44,
 ,[Offset], Cabinet_Interior, 45,
 ,[Offset], Cabinet_Metals, 46,
 ,[Offset], Top_Box, 47,
 ,[Offset], Flashers, 48,
 ,[Offset], Light_Cutouts, 49,
 ,[Offset], Light_Cutouts_Upper, 50,
 ,[Offset], Bulbs_Blue, 51,
 ,[Offset], Bulbs_Sign, 52,
 ,[Offset], Yeti_Body, 53,
 ,[Offset], Yeti_Head, 54,
 ,[Offset], Arm_Right_Hair, 55,
 ,[Offset], Arm_Hair_A, 56,
 ,[Offset], Arm_Hair_B, 57,
 ,[Offset], Arm_Hair_C, 58,
 ,[Offset], Arm_Hair_D, 59,
 ,[Offset], Arm_Hair_E, 60,
 ,[Offset], Arm_Hair_F, 61,
 ,[Offset], Arm_Hair_G, 62,
 ,[Offset], Arm_Hair_H, 63,
 ,[Offset], Arm_Hair_I, 64,
 ,[Offset], Body_Hair_A, 65,
 ,[Offset], Body_Hair_B, 66,
 ,[Offset], Body_Hair_C, 67,
 ,[Offset], Body_Hair_D, 68,
 ,[Offset], Body_Hair_E, 69,
 ,[Offset], Body_Hair_F, 70,
 ,[Offset], Head_Hair_A, 71,
 ,[Offset], Head_Hair_B, 72,
 ,[Offset], Head_Hair_C, 73,
 ,[Offset], Head_Hair_D, 74,
 ,[Offset], Head_Hair_E, 75,
 ,[Offset], Head_Hair_F, 76,
 ,[Offset], Head_Hair_G, 77,
 ,[Offset], Head_Hair_H, 78,
 ,[Offset], Head_Hair_I, 79,
 ,[Offset], Head_Hair_J, 80,
 ,[Offset], Head_Hair_K, 81,
 ,[Offset], Yeti_Position_B/Arm_Hair_A, 82,
 ,[Offset], Yeti_Position_B/Arm_Hair_B, 83,
 ,[Offset], Yeti_Position_B/Arm_Hair_C, 84,
 ,[Offset], Yeti_Position_B/Arm_Hair_D, 85,
 ,[Offset], Yeti_Position_B/Arm_Hair_E, 86,
 ,[Offset], Yeti_Position_B/Arm_Hair_F, 87,
 ,[Offset], Yeti_Position_B/Arm_Hair_G, 88,
 ,[Offset], Yeti_Position_B/Arm_Hair_H, 89,
 ,[Offset], Yeti_Position_B/Arm_Hair_I, 90,
 ,[Offset], Yeti_Position_B/Yeti_Hands, 91,
 ,[Offset], Yeti_Position_C/Arm_Hair_A, 92,
 ,[Offset], Yeti_Position_C/Arm_Hair_B, 93,
 ,[Offset], Yeti_Position_C/Arm_Hair_C, 94,
 ,[Offset], Yeti_Position_C/Arm_Hair_D, 95,
 ,[Offset], Yeti_Position_C/Arm_Hair_E, 96,
 ,[Offset], Yeti_Position_C/Arm_Hair_F, 97,
 ,[Offset], Yeti_Position_C/Arm_Hair_G, 98,
 ,[Offset], Yeti_Position_C/Arm_Hair_H, 99,
 ,[Offset], Yeti_Position_C/Arm_Hair_I, 100,
 ,[Offset], Yeti_Position_C/Yeti_Hands, 101,
 ,[Offset], Arm_Hair_J, 102,
 ,[Offset], Yeti_Position_B/Arm_Hair_J, 103,
 ,[Offset], Yeti_Position_C/Arm_Hair_J, 104,
 ,[Offset], Bolts, 105,
RSID_TWHITEWATER_MODELS_LODS, 2756,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Flipper, 2,
 ,[Offset], Metal_Pieces, 3,
 ,[Offset], Metal_Walls, 4,
 ,[Offset], Plastics, 5,
 ,[Offset], Plastics_Vertical, 6,
 ,[Offset], Playfield, 7,
 ,[Offset], Playfield_Upper, 8,
 ,[Offset], Posts_Black, 9,
 ,[Offset], Posts_Blue, 10,
 ,[Offset], Posts_Plastic, 11,
 ,[Offset], Ramp_Back_Left, 12,
 ,[Offset], Ramp_Back_Mid, 13,
 ,[Offset], Ramp_Bowl, 14,
 ,[Offset], Ramp_Left_Return, 15,
 ,[Offset], Ramp_Right_Return, 16,
 ,[Offset], Ramp_Stickers, 17,
 ,[Offset], Ramp_Yeti, 18,
 ,[Offset], Rock_Back, 19,
 ,[Offset], Rock_Bigfoot_Cave, 20,
 ,[Offset], Rock_Boulder_Garden, 21,
 ,[Offset], Rock_Left, 22,
 ,[Offset], Rock_Right, 23,
 ,[Offset], Rock_Right_B, 24,
 ,[Offset], Rubber, 25,
 ,[Offset], Slingshot_Left, 26,
 ,[Offset], Slingshot_Right, 27,
 ,[Offset], Target_Blue, 28,
 ,[Offset], Target_Green, 29,
 ,[Offset], Target_White, 30,
 ,[Offset], Target_Orange, 31,
 ,[Offset], Wire, 32,
 ,[Offset], Wooden_Rails, 33,
 ,[Offset], Yeti, 34,
 ,[Offset], Plunger, 35,
 ,[Offset], Gate_A, 36,
 ,[Offset], Gate_B, 37,
 ,[Offset], Gate_C, 38,
 ,[Offset], Habit_Curl, 39,
 ,[Offset], Plunger_Ramp, 40,
 ,[Offset], Ramp_Switch, 41,
 ,[Offset], Target_Yellow, 42,
 ,[Offset], Backglass, 43,
 ,[Offset], Cabinet, 44,
 ,[Offset], Cabinet_Interior, 45,
 ,[Offset], Cabinet_Metals, 46,
 ,[Offset], Top_Box, 47,
 ,[Offset], Flashers, 48,
 ,[Offset], Light_Cutouts, 49,
 ,[Offset], Light_Cutouts_Upper, 50,
 ,[Offset], Bulbs_Blue, 51,
 ,[Offset], Bulbs_Sign, 52,
 ,[Offset], Yeti_Body, 53,
 ,[Offset], Yeti_Head, 54,
 ,[Offset], Arm_Right_Hair, 55,
 ,[Offset], Arm_Hair_A, 56,
 ,[Offset], Arm_Hair_B, 57,
 ,[Offset], Arm_Hair_C, 58,
 ,[Offset], Arm_Hair_D, 59,
 ,[Offset], Arm_Hair_E, 60,
 ,[Offset], Arm_Hair_F, 61,
 ,[Offset], Arm_Hair_G, 62,
 ,[Offset], Arm_Hair_H, 63,
 ,[Offset], Arm_Hair_I, 64,
 ,[Offset], Body_Hair_A, 65,
 ,[Offset], Body_Hair_B, 66,
 ,[Offset], Body_Hair_C, 67,
 ,[Offset], Body_Hair_D, 68,
 ,[Offset], Body_Hair_E, 69,
 ,[Offset], Body_Hair_F, 70,
 ,[Offset], Head_Hair_A, 71,
 ,[Offset], Head_Hair_B, 72,
 ,[Offset], Head_Hair_C, 73,
 ,[Offset], Head_Hair_D, 74,
 ,[Offset], Head_Hair_E, 75,
 ,[Offset], Head_Hair_F, 76,
 ,[Offset], Head_Hair_G, 77,
 ,[Offset], Head_Hair_H, 78,
 ,[Offset], Head_Hair_I, 79,
 ,[Offset], Head_Hair_J, 80,
 ,[Offset], Head_Hair_K, 81,
 ,[Offset], Arm_Hair_J, 102,
 ,[Offset], Bolts, 105,
RSID_TWHITEWATER_COLLISION, 2757,,,
 ,[Offset], Apron, 0,
 ,[Offset], Flipper_Lane_Left, 1,
 ,[Offset], Flipper_Lane_Right, 2,
 ,[Offset], Inner_Wall_A, 3,
 ,[Offset], Inner_Wall_B, 4,
 ,[Offset], Inner_Wall_C, 5,
 ,[Offset], Left_Wall, 6,
 ,[Offset], Outer_Wall, 7,
 ,[Offset], Playfield_Upper, 8,
 ,[Offset], Plunger_Lane, 9,
 ,[Offset], Plunger_Moving, 10,
 ,[Offset], Plunger_Rest, 11,
 ,[Offset], Ramp_Back_Left, 12,
 ,[Offset], Ramp_Back_Mid, 13,
 ,[Offset], Ramp_Bowl, 14,
 ,[Offset], Ramp_Left_Return, 15,
 ,[Offset], Ramp_Plunger, 16,
 ,[Offset], Ramp_Right_Return, 17,
 ,[Offset], Ramp_Yeti, 18,
 ,[Offset], Ramp_Back_Left, 19,
 ,[Offset], Rubber_Left, 20,
 ,[Offset], Rubber_Mid, 21,
 ,[Offset], Rubber_Right, 22,
 ,[Offset], Rubber_Upper, 23,
 ,[Offset], Slingshot_Left, 24,
 ,[Offset], Slingshot_Right, 25,
 ,[Offset], Upper_Wall, 26,
 ,[Offset], Playfield, 27,
 ,[Offset], Bumper, 28,
 ,[Offset], Flipper_Left_Back, 29,
 ,[Offset], Flipper_Left_Front, 30,
 ,[Offset], Flipper_Right_Back, 31,
 ,[Offset], Flipper_Right_Front, 32,
 ,[Offset], Flipper_Upper_Back, 33,
 ,[Offset], Flipper_Upper_Front, 34,
 ,[Offset], Target, 35,
 ,[Offset], Target_Small, 36,
 ,[Offset], Slingshot_Left_Front, 37,
 ,[Offset], Slingshot_Right_Front, 38,
 ,[Offset], Ball_Drain, 39,
 ,[Offset], Gate, 40,
 ,[Offset], Opto, 41,
 ,[Offset], Bowl, 42,
 ,[Offset], Bowl_Shoot, 43,
 ,[Offset], Bowl_Walls, 44,
 ,[Offset], Trough, 45,
 ,[Offset], Trough_Trap, 46,
 ,[Offset], Curl_Habit_Trap, 47,
 ,[Offset], Habit_Curl, 48,
 ,[Offset], Rubber_Switch_A, 49,
 ,[Offset], Rubber_Switch_B, 50,
 ,[Offset], Diverter, 51,
 ,[Offset], Ramp_Left_Return_Upper, 52,
 ,[Offset], Ramp_Left_Return_Mid, 53,
RSID_TWHITEWATER_PLACEMENT, 2758,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TWHITEWATER_EMUROM, 2759,,,
 ,[Offset], ww_l5, 0,
 ,[Offset], ww_l5, 1,
 ,[Offset], ww_l5, 2,
 ,[Offset], ww_l5, 3,
 ,[Offset], ww_l5, 4,
 ,[Offset], ww_l5, 5,
 ,[Offset], ww_default_nvram, 6,
 ,[Offset], ww_default_nvram, 7,
RSID_TWHITEWATER_SOUNDS_START, 2760,,,
RSID_TWHITEWATER_EMU_SOUNDS, 2761,,,
 ,[Offset], S0002-LP, 0,
 ,[Offset], S0003-LP1, 1,
 ,[Offset], S0003-LP2, 2,
 ,[Offset], S0004-LP1, 3,
 ,[Offset], S0004-LP2, 4,
 ,[Offset], S0005-LP, 5,
 ,[Offset], S0006-LP1, 6,
 ,[Offset], S0006-LP2, 7,
 ,[Offset], S0007-LP1, 8,
 ,[Offset], S0007-LP2, 9,
 ,[Offset], S0008-LP1, 10,
 ,[Offset], S0008-LP2, 11,
 ,[Offset], S0009-LP1, 12,
 ,[Offset], S0009-LP2, 13,
 ,[Offset], S000A-LP1, 14,
 ,[Offset], S000A-LP2, 15,
 ,[Offset], S000B-LP1, 16,
 ,[Offset], S000B-LP2, 17,
 ,[Offset], S000C-LP1, 18,
 ,[Offset], S000C-LP2, 19,
 ,[Offset], S000D-LP, 20,
 ,[Offset], S000E-LP1, 21,
 ,[Offset], S000E-LP2, 22,
 ,[Offset], S000F-LP, 23,
 ,[Offset], S0010-LP1, 24,
 ,[Offset], S0010-LP2, 25,
 ,[Offset], S0011-LP, 26,
 ,[Offset], S0012-LP1, 27,
 ,[Offset], S0012-LP2, 28,
 ,[Offset], S0013-LP1, 29,
 ,[Offset], S0013-LP2, 30,
 ,[Offset], S0014-LP1, 31,
 ,[Offset], S0014-LP2, 32,
 ,[Offset], S0016-LP, 33,
 ,[Offset], S0017-LP1, 34,
 ,[Offset], S0017-LP2, 35,
 ,[Offset], S0018_C2, 36,
 ,[Offset], S0019_C2, 37,
 ,[Offset], S001A_C2, 38,
 ,[Offset], S001B_C2, 39,
 ,[Offset], S001C_C2, 40,
 ,[Offset], S001D_C2, 41,
 ,[Offset], S001E_C2, 42,
 ,[Offset], S001F_C2, 43,
 ,[Offset], S0030_C3, 44,
 ,[Offset], S003A-LP1, 45,
 ,[Offset], S003D-LP1, 46,
 ,[Offset], S003D-LP2, 47,
 ,[Offset], S003E-LP1, 48,
 ,[Offset], S003E-LP2, 49,
 ,[Offset], S003F-LP1, 50,
 ,[Offset], S003F-LP2, 51,
 ,[Offset], S0040-LP1, 52,
 ,[Offset], S0040-LP2, 53,
 ,[Offset], S0041-LP1, 54,
 ,[Offset], S0041-LP2, 55,
 ,[Offset], S0042-LP1, 56,
 ,[Offset], S0042-LP2, 57,
 ,[Offset], S0043-LP1, 58,
 ,[Offset], S0043-LP2, 59,
 ,[Offset], S0044-LP1, 60,
 ,[Offset], S0044-LP2, 61,
 ,[Offset], S0046-LP1, 62,
 ,[Offset], S0046-LP2, 63,
 ,[Offset], S0047-LP, 64,
 ,[Offset], S0048-LP, 65,
 ,[Offset], S0049-LP, 66,
 ,[Offset], S004A-LP, 67,
 ,[Offset], S0050_C4, 68,
 ,[Offset], S0051_C4, 69,
 ,[Offset], S0052_C4, 70,
 ,[Offset], S0053_C4, 71,
 ,[Offset], S0054_C4, 72,
 ,[Offset], S0055_C4, 73,
 ,[Offset], S0056_C4, 74,
 ,[Offset], S0057_C4, 75,
 ,[Offset], S0058_C4, 76,
 ,[Offset], S0059_C4, 77,
 ,[Offset], S0080_C4-LP, 78,
 ,[Offset], S0083_C4, 79,
 ,[Offset], S0084_C4, 80,
 ,[Offset], S0085_C4, 81,
 ,[Offset], S0086_C4-LP, 82,
 ,[Offset], S0087_C4, 83,
 ,[Offset], S0088_C4, 84,
 ,[Offset], S0089_C4, 85,
 ,[Offset], S008A_C4, 86,
 ,[Offset], S008B_C4, 87,
 ,[Offset], S008C_C4, 88,
 ,[Offset], S008D_C4, 89,
 ,[Offset], S008E_C4, 90,
 ,[Offset], S008F_C4, 91,
 ,[Offset], S0090_C4, 92,
 ,[Offset], S0091_C4, 93,
 ,[Offset], S0092_C4, 94,
 ,[Offset], S0093_C4, 95,
 ,[Offset], S0094_C4, 96,
 ,[Offset], S0095_C4, 97,
 ,[Offset], S0096_C4, 98,
 ,[Offset], S0097_C4, 99,
 ,[Offset], S0098_C4, 100,
 ,[Offset], S0099_C4, 101,
 ,[Offset], S009A_C4, 102,
 ,[Offset], S009B_C4, 103,
 ,[Offset], S009C_C4, 104,
 ,[Offset], S009D_C4, 105,
 ,[Offset], S009E_C4, 106,
 ,[Offset], S009F_C4, 107,
 ,[Offset], S00A0_C4, 108,
 ,[Offset], S00A1_C4, 109,
 ,[Offset], S00A2_C4, 110,
 ,[Offset], S00A3_C4, 111,
 ,[Offset], S00A4_C4, 112,
 ,[Offset], S00A5_C4, 113,
 ,[Offset], S00A6_C4, 114,
 ,[Offset], S00A7_C4, 115,
 ,[Offset], S00A8_C4, 116,
 ,[Offset], S00A9_C4, 117,
 ,[Offset], S00AA_C4, 118,
 ,[Offset], S00AB_C4, 119,
 ,[Offset], S00AC_C4, 120,
 ,[Offset], S00AD_C4, 121,
 ,[Offset], S00AE_C4, 122,
 ,[Offset], S00AF_C4, 123,
 ,[Offset], S00B0_C4, 124,
 ,[Offset], S00B1_C4, 125,
 ,[Offset], S00B2_C4, 126,
 ,[Offset], S00B3_C4, 127,
 ,[Offset], S00B4_C4, 128,
 ,[Offset], S00B5_C4, 129,
 ,[Offset], S00B6_C4, 130,
 ,[Offset], S00B7_C4, 131,
 ,[Offset], S00B8_C4, 132,
 ,[Offset], S00B9_C4, 133,
 ,[Offset], S00BA_C4, 134,
 ,[Offset], S00BB_C4, 135,
 ,[Offset], S00BC_C4, 136,
 ,[Offset], S00BD_C4, 137,
 ,[Offset], S00BE_C4, 138,
 ,[Offset], S00BF_C4, 139,
 ,[Offset], S00C0_C4, 140,
 ,[Offset], S00C1_C4, 141,
 ,[Offset], S00C2_C4, 142,
 ,[Offset], S00C3_C4, 143,
 ,[Offset], S00C4_C4, 144,
 ,[Offset], S00C5_C4, 145,
 ,[Offset], S00C6_C4, 146,
 ,[Offset], S00C7_C4, 147,
 ,[Offset], S00C8_C4, 148,
 ,[Offset], S00C9_C4, 149,
 ,[Offset], S00CA_C4, 150,
 ,[Offset], S00CB_C4, 151,
 ,[Offset], S00CC_C4, 152,
 ,[Offset], S00CD_C4, 153,
 ,[Offset], S00CE_C4, 154,
 ,[Offset], S00CF_C4, 155,
 ,[Offset], S00D0_C4, 156,
 ,[Offset], S00D1_C4, 157,
 ,[Offset], S00D2_C4, 158,
 ,[Offset], S00D3_C4, 159,
 ,[Offset], S00D4_C4, 160,
 ,[Offset], S00D5_C4, 161,
 ,[Offset], S00D6_C4, 162,
 ,[Offset], S00D7_C4, 163,
 ,[Offset], S00D8_C4, 164,
 ,[Offset], S00D9_C4, 165,
 ,[Offset], S00DA_C4, 166,
 ,[Offset], S00DB_C4, 167,
 ,[Offset], S00DC_C4, 168,
 ,[Offset], S00DD_C4, 169,
 ,[Offset], S00DE_C4, 170,
 ,[Offset], S00DF_C4, 171,
 ,[Offset], S00E0_C4, 172,
 ,[Offset], S00E1_C4, 173,
 ,[Offset], S00E2_C4, 174,
 ,[Offset], S00E3_C4, 175,
 ,[Offset], S00E4_C4, 176,
 ,[Offset], S00E5_C4, 177,
 ,[Offset], S00E6_C4, 178,
 ,[Offset], S00E7_C4, 179,
 ,[Offset], S00E8_C4, 180,
 ,[Offset], S00E9_C4, 181,
 ,[Offset], S00EA_C4, 182,
 ,[Offset], S00EB_C4, 183,
 ,[Offset], S00EC_C4, 184,
 ,[Offset], S00ED_C4, 185,
 ,[Offset], S0101_C2, 186,
 ,[Offset], S0102_C2, 187,
 ,[Offset], S0103_C2, 188,
 ,[Offset], S0104_C2, 189,
 ,[Offset], S0105_C2, 190,
 ,[Offset], S0106_C2, 191,
 ,[Offset], S0107_C2, 192,
 ,[Offset], S0108_C2, 193,
 ,[Offset], S0109_C2, 194,
 ,[Offset], S010A_C2, 195,
 ,[Offset], S010B_C2, 196,
 ,[Offset], S010C_C2, 197,
 ,[Offset], S010D_C2, 198,
 ,[Offset], S010E_C2, 199,
 ,[Offset], S010F_C2, 200,
 ,[Offset], S0110_C2, 201,
 ,[Offset], S0111_C2, 202,
 ,[Offset], S0112_C2, 203,
 ,[Offset], S0113_C2, 204,
 ,[Offset], S0114_C2, 205,
 ,[Offset], S0115_C2, 206,
 ,[Offset], S0116_C2, 207,
 ,[Offset], S0117_C2, 208,
 ,[Offset], S0118_C2, 209,
 ,[Offset], S0119_C2, 210,
 ,[Offset], S011A_C2, 211,
 ,[Offset], S011B_C2, 212,
 ,[Offset], S011C_C2, 213,
 ,[Offset], S011D_C2, 214,
 ,[Offset], S011E_C2, 215,
 ,[Offset], S011F_C2, 216,
 ,[Offset], S0120_C2, 217,
 ,[Offset], S0121_C2, 218,
 ,[Offset], S0122_C2, 219,
 ,[Offset], S0123_C2, 220,
 ,[Offset], S0124_C2, 221,
 ,[Offset], S0125_C2, 222,
 ,[Offset], S0126_C2, 223,
 ,[Offset], S0127_C2, 224,
 ,[Offset], S0128_C2, 225,
 ,[Offset], S0129_C2, 226,
 ,[Offset], S012A_C2, 227,
 ,[Offset], S012B_C2, 228,
 ,[Offset], S012C_C2, 229,
 ,[Offset], S012D_C2, 230,
 ,[Offset], S012E_C2, 231,
 ,[Offset], S012F_C2, 232,
 ,[Offset], S0130_C2, 233,
 ,[Offset], S0131_C2, 234,
 ,[Offset], S0132_C2, 235,
 ,[Offset], S0133_C2, 236,
 ,[Offset], S0134_C2, 237,
 ,[Offset], S0135_C2, 238,
 ,[Offset], S0136_C2, 239,
 ,[Offset], S0137_C2, 240,
 ,[Offset], S0138_C2, 241,
 ,[Offset], S0139_C2, 242,
 ,[Offset], S013A_C2, 243,
 ,[Offset], S013B_C2, 244,
 ,[Offset], S013C_C2, 245,
 ,[Offset], S013D_C2, 246,
 ,[Offset], S013E_C2, 247,
 ,[Offset], S013F_C2, 248,
 ,[Offset], S0140_C2, 249,
 ,[Offset], S0141_C2, 250,
 ,[Offset], S0142_C2, 251,
 ,[Offset], S0143_C2, 252,
 ,[Offset], S0144_C2, 253,
 ,[Offset], S0145_C2, 254,
 ,[Offset], S0146_C2, 255,
 ,[Offset], S0147_C2, 256,
 ,[Offset], S0148_C2, 257,
 ,[Offset], S0149_C2, 258,
 ,[Offset], S014A_C2, 259,
 ,[Offset], S014B_C2, 260,
 ,[Offset], S014C_C2, 261,
 ,[Offset], S014D_C2, 262,
 ,[Offset], S014E_C2, 263,
 ,[Offset], S014F_C2, 264,
 ,[Offset], S0150_C2, 265,
 ,[Offset], S0152_C2, 266,
 ,[Offset], S0153_C2, 267,
 ,[Offset], S0154_C2, 268,
 ,[Offset], S0155_C2, 269,
 ,[Offset], S0156_C2, 270,
 ,[Offset], S0158_C2, 271,
 ,[Offset], S0159_C2, 272,
 ,[Offset], S015A_C2, 273,
 ,[Offset], S015B_C2, 274,
 ,[Offset], S015C_C2, 275,
 ,[Offset], S015D_C2, 276,
 ,[Offset], S015E_C2, 277,
 ,[Offset], S015F_C2, 278,
 ,[Offset], S0160_C2, 279,
 ,[Offset], S0161_C2, 280,
 ,[Offset], S0162_C2, 281,
 ,[Offset], S0163_C2, 282,
 ,[Offset], S0164_C2, 283,
 ,[Offset], S0165_C2, 284,
 ,[Offset], S0166_C2, 285,
 ,[Offset], S0167_C2, 286,
 ,[Offset], S0168_C2, 287,
 ,[Offset], S0169_C2, 288,
 ,[Offset], S016A_C2, 289,
 ,[Offset], S016B_C2, 290,
 ,[Offset], S016C_C2, 291,
 ,[Offset], S016D_C2, 292,
 ,[Offset], S016E_C2, 293,
 ,[Offset], S016F_C2, 294,
 ,[Offset], S0170_C2, 295,
 ,[Offset], S0171_C2, 296,
 ,[Offset], S0172_C2, 297,
 ,[Offset], S0173_C2, 298,
 ,[Offset], S0174_C2, 299,
 ,[Offset], S0175_C2, 300,
 ,[Offset], S0176_C2, 301,
 ,[Offset], S0177_C2, 302,
 ,[Offset], S0178_C2, 303,
 ,[Offset], S0179_C2, 304,
 ,[Offset], S017A_C2, 305,
 ,[Offset], S017B_C2, 306,
 ,[Offset], S017C_C2, 307,
 ,[Offset], S017D_C2, 308,
 ,[Offset], S017E_C2, 309,
 ,[Offset], S017F_C2, 310,
RSID_TWHITEWATER_MECH_SOUNDS, 2762,,,
RSID_TWHITEWATER_SOUNDS_END, 2763,,,
RSID_TWHITEWATER_SAMPLES, 2764,,,
RSID_TWHITEWATER_END, 2765,,,
