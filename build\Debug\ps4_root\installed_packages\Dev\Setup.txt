//---------------------------------------------
// Setup file for Pinball.
//---------------------------------------------

//---------------------------------------------
// Debug
//---------------------------------------------
 
gShowDebugInfo = FALSE
SkipMenus = FALSE
TableOnlyMode = FALSE
RunTestCode = FALSE
LanguageTesting = FALSE
BallControl = TRUE
LightingAllOn = FALSE

//TableNum = 1		// TABLE_GORGAR
//TableNum = 2		// TABLE_PINBOT
//TableNum = 3		// TABLE_KNIGHT
//TableNum = 4		// TABLE_FUNHOUSE
//TableNum = 5		// TABLE_SHUTTLE        
//TableNum = 6		// TABLE_TAXI
//TableNum = 7		// TABLE_WHIRLWIND
//TableNum = 8		// TABLE_FIREPOWER
//TableNum = 9		// TABLE_SORCERER
//TableNum = 10		// TABLE_JIVETIME       
//TableNum = 11		// TABLE_TALES
//TableNum = 12		// TABLE_MEDIEVAL
//TableNum = 13		// TABLE_GOFERS
//TableNum = 14		// TABLE_BRIDE
//TableNum = 15		// TABLE_HARLEY         
//TableNum = 16		// TABLE_RIPLEYS
//TableNum = 17		// TABLE_BLACK_HOLE
//TableNum = 18		// TABLE_THEATER
TableNum = 19		// TABLE_CIRQUS
//TableNum = 20		// TABLE_CREATURE       
//TableNum = 21		// TABLE_MONSTERBASH
//TableNum = 22		// TABLE_TWILIGHTZONE
//TableNum = 23		// TABLE_ATTACKFROMMARS
//TableNum = 24		// TABLE_BIGSHOT
//TableNum = 25		// TABLE_CACTUSCANYON   
//TableNum = 26		// TABLE_CENTRALPARK
//TableNum = 27		// TABLE_CHAMPIONPUB
//TableNum = 28		// TABLE_DRDUDE
//TableNum = 29		// TABLE_ELVIRAPARTYMONSTERS
//TableNum = 30		// TABLE_GENIE       
//TableNum = 31		// TABLE_SCAREDSTIFF
//TableNum = 32		// TABLE_STARTREKTNG
//TableNum = 33		// TABLE_VICTORY
//TableNum = 34		// TABLE_WHITEWATER
//TableNum = 35		// TABLE_ACEHIGH      
//TableNum = 36		// TABLE_CENTAUR
//TableNum = 37		// TABLE_ELDORADO
//TableNum = 38		// TABLE_GOINNUTS
//TableNum = 39		// TABLE_HAUNTEDHOUSE
//TableNum = 40		// TABLE_HIGHSPEED2	
//TableNum = 41		// TABLE_PHANTOMOPERA
//TableNum = 42		// TABLE_SAFECRACKER
//TableNum = 43		// TABLE_STRIKESSPARES
//TableNum = 44		// TABLE_TEEDOFF
//TableNum = 45		// TABLE_TERMINATOR2
//TableNum = 46		// TABLE_FLIGHT2000


// Test Tables
//TableFiles = "Dev/PBTable.csv", "TTales_Enums.csv"
//TableFiles = "Dev/PBTableSimple.csv", "TTales_Enums.csv"
//TableFiles = "Dev/PBPhysicsTest.csv", "TestTable_Enums.csv"

// Actual Tables
//TableFiles = "Dev/Harley/PBTHarley.csv", "THarleyDavidson_Enums.csv"
//TableFiles = "Dev/Bride/PBTBride.csv", "TBride_Enums.csv"
//TableFiles = "Dev/Tales/PBTTotan.csv", "TTotan_Enums.csv"
//TableFiles = "Dev/Ripleys/PBTRipleys.csv", "TRipleys_Enums.csv"
//TableFiles = "Dev/Taxi/PBTTaxi.csv", "TNewTaxi_Enums.csv"
//TableFiles = "Dev/ElviraPartyMonsters/PBTElviraPartyMonsters.csv", "TElvira_Enums.csv"
//TableFiles = "Dev/BlackHole/PBTBlackHole.csv", "TBlackHole_Enums.csv"

//---------------------------------------------
// Music and sound
//---------------------------------------------

Volume_SoundFx			= 100
Volume_Ambient			= 100
Volume_Music			= 100

//---------------------------------------------

