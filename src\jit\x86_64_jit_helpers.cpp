// Copyright 2025 <Copyright Owner>

#include "x86_64_jit_helpers.h"
#include "../cpu/x86_64_cpu.h"
#include <atomic>
#include <chrono>
#include <emmintrin.h>
#include <immintrin.h>
#include <iostream>
#include <mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>


// Forward declaration to resolve namespace issues
namespace x86_64 {
class X86_64CPU;
class MemoryHelper;
} // namespace x86_64

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/mman.h>
#endif

#pragma warning(push)
#pragma warning(disable : 4297) // extern "C" functions may throw

#ifndef EXC_PF
#define EXC_PF 14 // Define EXC_PF as the page fault exception code
#endif

namespace x86_64 {

// Forward declarations for validation functions
bool ValidateMemoryAccess(uint64_t addr, size_t size, bool isWrite = false);
bool ValidateCPU(X86_64CPU *cpu);
bool ValidateSimdAccess(uint64_t addr, uint32_t size,
                        const void *buffer = nullptr);
bool ValidateStackAccess(X86_64CPU *cpu, uint64_t value = 0);

/**
 * @brief Validates memory access parameters for JIT operations.
 * @param addr Memory address to validate
 * @param size Size of memory operation
 * @param isWrite Whether this is a write operation
 * @return True if parameters are valid, false otherwise
 */
bool ValidateMemoryAccess(uint64_t addr, size_t size, bool isWrite) {
  // Check for null pointer access
  if (addr == 0) {
    spdlog::error("JIT memory access: null pointer access");
    return false;
  }

  // Check for overflow in address calculation
  if (addr + size < addr) {
    spdlog::error("JIT memory access: address overflow at 0x{:x} + {}", addr,
                  size);
    return false;
  }

  // Check for reasonable size limits
  constexpr size_t MAX_SINGLE_ACCESS = 1024; // 1KB max per operation
  if (size > MAX_SINGLE_ACCESS) {
    spdlog::error("JIT memory access: size {} exceeds maximum {}", size,
                  MAX_SINGLE_ACCESS);
    return false;
  }

  // Check for minimum alignment for larger operations
  if (size >= 16 && (addr % 16) != 0) {
    spdlog::warn("JIT memory access: unaligned access at 0x{:x}, size={}", addr,
                 size);
  }

  // Check if address is in user space (PS4 specific - user space typically <
  // 0x800000000000)
  if (addr >= 0x800000000000ULL) {
    spdlog::error("JIT memory access: kernel space access at 0x{:x}", addr);
    return false;
  }

  return true;
}

/**
 * @brief Validates CPU pointer and basic state.
 * @param cpu CPU pointer to validate
 * @return True if CPU is valid, false otherwise
 */
bool ValidateCPU(X86_64CPU *cpu) {
  if (!cpu) {
    spdlog::error("JIT operation: null CPU pointer");
    return false;
  }

  // Additional CPU state validation could be added here
  return true;
}

/**
 * @brief Validates XMM/SIMD parameters.
 * @param addr Address or register index
 * @param size Size of SIMD operation
 * @param buffer Buffer pointer (can be null for validation check)
 * @return True if parameters are valid, false otherwise
 */
bool ValidateSimdAccess(uint64_t addr, uint32_t size, const void *buffer) {
  // Validate SIMD size
  if (size != 16 && size != 32 && size != 64) {
    spdlog::error("JIT SIMD access: invalid size {}", size);
    return false;
  }

  // Validate buffer alignment for SIMD operations
  if (buffer && ((uintptr_t)buffer % 16) != 0) {
    spdlog::warn("JIT SIMD access: unaligned buffer at 0x{:x}",
                 (uintptr_t)buffer);
  }

  // For register access, validate register index
  if (addr > 15) { // x86-64 has 16 XMM registers (0-15)
    spdlog::error("JIT SIMD access: invalid register index {}", addr);
    return false;
  }

  return true;
}

/**
 * @brief Validates stack operation parameters.
 * @param cpu CPU pointer
 * @param value Value being pushed/popped
 * @return True if parameters are valid, false otherwise
 */
bool ValidateStackAccess(X86_64CPU *cpu, uint64_t value) {
  if (!ValidateCPU(cpu)) {
    return false;
  }

  // Get current stack pointer
  uint64_t rsp = cpu->GetRegister(Register::RSP);

  // Check for stack overflow (approaching guard page)
  constexpr uint64_t STACK_GUARD_SIZE = 4096; // 4KB guard
  if (rsp < STACK_GUARD_SIZE) {
    spdlog::error("JIT stack access: stack overflow detected, RSP=0x{:x}", rsp);
    return false;
  }

  // Check for reasonable stack bounds (PS4 stack typically 2MB)
  constexpr uint64_t MAX_STACK_SIZE = 2 * 1024 * 1024; // 2MB
  if (rsp > MAX_STACK_SIZE) {
    spdlog::warn("JIT stack access: stack pointer unusually high, RSP=0x{:x}",
                 rsp);
  }

  return true;
}

void MemoryHelper::SaveState(std::ostream &out) const {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // CRITICAL FIX: Add stream validation before writing
    if (!out.good()) {
      spdlog::error(
          "MemoryHelper SaveState: output stream is not in good state");
      return;
    }

    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    if (!out.good()) {
      spdlog::error("MemoryHelper SaveState: failed to write version");
      return;
    }

    // CRITICAL FIX: Save atomic values properly by loading them first
    Stats currentStats =
        m_stats; // This uses the copy constructor which loads atomic values
    out.write(reinterpret_cast<const char *>(&currentStats),
              sizeof(currentStats));
    if (!out.good()) {
      spdlog::error("MemoryHelper SaveState: failed to write stats");
      return;
    }

    spdlog::info("MemoryHelper state saved");
  } catch (const std::exception &e) {
    spdlog::error("MemoryHelper SaveState failed: {}", e.what());
  }
}

void MemoryHelper::LoadState(std::istream &in) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // CRITICAL FIX: Add stream validation before reading
    if (!in.good()) {
      spdlog::error(
          "MemoryHelper LoadState: input stream is not in good state");
      return;
    }

    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (!in.good()) {
      spdlog::error("MemoryHelper LoadState: failed to read version");
      return;
    }
    if (version != 1) {
      spdlog::error("Unsupported MemoryHelper state version: {}", version);
      throw JITException("Invalid MemoryHelper state version");
    }

    // CRITICAL FIX: Load atomic values properly by reading into temporary
    // struct
    Stats tempStats;
    in.read(reinterpret_cast<char *>(&tempStats), sizeof(tempStats));
    if (!in.good()) {
      spdlog::error("MemoryHelper LoadState: failed to read stats");
      return;
    }

    // Copy the loaded values to atomic members
    m_stats = tempStats;

    spdlog::info("MemoryHelper state loaded");
  } catch (const std::exception &e) {
    spdlog::error("MemoryHelper LoadState failed: {}", e.what());
    ResetStats(); // Reset to safe state on error
  }
}
/**
 * @brief Reads an 8-bit value from virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to read from.
 * @return The 8-bit value read.
 * @throws JITException on memory access failure or null CPU pointer.
 */
uint64_t jitReadMem8(X86_64CPU *cpu, uint64_t addr) {
  auto start = std::chrono::steady_clock::now();
  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw x86_64::JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint8_t), false)) {
    throw x86_64::JITException("Invalid memory access parameters");
  }
  try {
    uint8_t value;
    bool cacheHit = cpu->GetMMU().ReadVirtual(addr, &value, sizeof(value),
                                              cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = x86_64::MemoryHelper::GetInstance();
    helper.IncrementMemoryReads();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT read8 failed at 0x{:x}: memory not accessible", addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw x86_64::JITException("Memory read fault");
    }
    spdlog::trace("JIT read8 at 0x{:x}, value=0x{:x}", addr, value);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("JIT read8 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw x86_64::JITException("Memory read fault");
  }
}

/**
 * @brief Reads a 16-bit value from virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to read from.
 * @return The 16-bit value read.
 * @throws JITException on memory access failure or null CPU pointer.
 */
uint64_t jitReadMem16(X86_64CPU *cpu, uint64_t addr) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint16_t), false)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint16_t value;
    bool cacheHit = cpu->GetMMU().ReadVirtual(addr, &value, sizeof(value),
                                              cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryReads();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT read16 failed at 0x{:x}: memory not accessible", addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory read fault");
    }
    spdlog::trace("JIT read16 at 0x{:x}, value=0x{:x}", addr, value);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("JIT read16 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory read fault");
  }
}

/**
 * @brief Reads a 32-bit value from virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to read from.
 * @return The 32-bit value read.
 * @throws JITException on memory access failure or null CPU pointer.
 */
uint64_t jitReadMem32(X86_64CPU *cpu, uint64_t addr) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint32_t), false)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint32_t value;
    bool cacheHit = cpu->GetMMU().ReadVirtual(addr, &value, sizeof(value),
                                              cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryReads();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT read32 failed at 0x{:x}: memory not accessible", addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory read fault");
    }
    spdlog::trace("JIT read32 at 0x{:x}, value=0x{:x}", addr, value);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("JIT read32 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory read fault");
  }
}

/**
 * @brief Reads a 64-bit value from virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to read from.
 * @return The 64-bit value read.
 * @throws JITException on memory access failure or null CPU pointer.
 */
uint64_t jitReadMem64(X86_64CPU *cpu, uint64_t addr) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint64_t), false)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint64_t value;
    bool cacheHit = cpu->GetMMU().ReadVirtual(addr, &value, sizeof(value),
                                              cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryReads();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT read64 failed at 0x{:x}: memory not accessible", addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory read fault");
    }
    spdlog::trace("JIT read64 at 0x{:x}, value=0x{:x}", addr, value);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("JIT read64 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory read fault");
  }
}

/**
 * @brief Writes an 8-bit value to virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to write to.
 * @param value The 8-bit value to write.
 * @throws JITException on memory access failure or null CPU pointer.
 */
void jitWriteMem8(X86_64CPU *cpu, uint64_t addr, uint64_t value) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint8_t), true)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint8_t v = static_cast<uint8_t>(value);
    bool cacheHit =
        cpu->GetMMU().WriteVirtual(addr, &v, sizeof(v), cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryWrites();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT write8 failed at 0x{:x}: memory not accessible", addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory write fault");
    }
    spdlog::trace("JIT write8 at 0x{:x}, value=0x{:x}", addr, v);
  } catch (const std::exception &e) {
    spdlog::error("JIT write8 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory write fault");
  }
}

/**
 * @brief Writes a 16-bit value to virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to write to.
 * @param value The 16-bit value to write.
 * @throws JITException on memory access failure or null CPU pointer.
 */
void jitWriteMem16(X86_64CPU *cpu, uint64_t addr, uint64_t value) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint16_t), true)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint16_t v = static_cast<uint16_t>(value);
    bool cacheHit =
        cpu->GetMMU().WriteVirtual(addr, &v, sizeof(v), cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryWrites();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT write16 failed at 0x{:x}: memory not accessible",
                    addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory write fault");
    }
    spdlog::trace("JIT write16 at 0x{:x}, value=0x{:x}", addr, v);
  } catch (const std::exception &e) {
    spdlog::error("JIT write16 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory write fault");
  }
}

/**
 * @brief Writes a 32-bit value to virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to write to.
 * @param value The 32-bit value to write.
 * @throws JITException on memory access failure or null CPU pointer.
 */
void jitWriteMem32(X86_64CPU *cpu, uint64_t addr, uint64_t value) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint32_t), true)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    uint32_t v = static_cast<uint32_t>(value);
    bool cacheHit =
        cpu->GetMMU().WriteVirtual(addr, &v, sizeof(v), cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryWrites();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT write32 failed at 0x{:x}: memory not accessible",
                    addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory write fault");
    }
    spdlog::trace("JIT write32 at 0x{:x}, value=0x{:x}", addr, v);
  } catch (const std::exception &e) {
    spdlog::error("JIT write32 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory write fault");
  }
}

/**
 * @brief Writes a 64-bit value to virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to write to.
 * @param value The 64-bit value to write.
 * @throws JITException on memory access failure or null CPU pointer.
 */
void jitWriteMem64(X86_64CPU *cpu, uint64_t addr, uint64_t value) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!x86_64::ValidateMemoryAccess(addr, sizeof(uint64_t), true)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    bool cacheHit = cpu->GetMMU().WriteVirtual(addr, &value, sizeof(value),
                                               cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryWrites();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT write64 failed at 0x{:x}: memory not accessible",
                    addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("Memory write fault");
    }
    spdlog::trace("JIT write64 at 0x{:x}, value=0x{:x}", addr, value);
  } catch (const std::exception &e) {
    spdlog::error("JIT write64 fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Memory write fault");
  }
}

/**
 * @brief Reads SIMD data from virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to read from.
 * @param dest Destination buffer for SIMD data.
 * @param size Size of data to read (128 or 256 bits).
 * @throws JITException on memory access failure, null CPU pointer, or invalid
 * size.
 */
void jitReadXMM(X86_64CPU *cpu, uint64_t addr, uint8_t *dest, uint32_t size) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!dest) {
    spdlog::error("JIT XMM read: null destination buffer");
    throw JITException("Null destination buffer");
  }
  if (!x86_64::ValidateSimdAccess(addr, size, dest)) {
    throw JITException("Invalid SIMD access parameters");
  }
  if (!x86_64::ValidateMemoryAccess(addr, size, false)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    if (size != 16 && size != 32) {
      spdlog::error("Invalid SIMD read size {} at 0x{:x}", size, addr);
      throw JITException("Invalid SIMD read size");
    }
    bool cacheHit =
        cpu->GetMMU().ReadVirtual(addr, dest, size, cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryReads();
    helper.IncrementSimdAccesses();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT XMM read failed at 0x{:x}: memory not accessible",
                    addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("XMM memory read fault");
    }
    spdlog::trace("JIT XMM read at 0x{:x}, size={}", addr, size);
  } catch (const std::exception &e) {
    spdlog::error("JIT XMM read fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("XMM memory read fault");
  }
}

/**
 * @brief Writes SIMD data to virtual memory using PS4MMU.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr Virtual address to write to.
 * @param src Source buffer for SIMD data.
 * @param size Size of data to write (128 or 256 bits).
 * @throws JITException on memory access failure, null CPU pointer, or invalid
 * size.
 */
void jitWriteXMM(X86_64CPU *cpu, uint64_t addr, const uint8_t *src,
                 uint32_t size) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Enhanced validation with bounds checking
  if (!x86_64::ValidateCPU(cpu)) {
    throw JITException("Invalid CPU pointer");
  }
  if (!src) {
    spdlog::error("JIT XMM write: null source buffer");
    throw JITException("Null source buffer");
  }
  if (!x86_64::ValidateSimdAccess(addr, size, src)) {
    throw JITException("Invalid SIMD access parameters");
  }
  if (!x86_64::ValidateMemoryAccess(addr, size, true)) {
    throw JITException("Invalid memory access parameters");
  }

  try {
    if (size != 16 && size != 32) {
      spdlog::error("Invalid SIMD write size {} at 0x{:x}", size, addr);
      throw JITException("Invalid SIMD write size");
    }
    bool cacheHit =
        cpu->GetMMU().WriteVirtual(addr, src, size, cpu->GetProcessId());
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementMemoryWrites();
    helper.IncrementSimdAccesses();
    if (cacheHit) {
      helper.IncrementCacheHits();
    } else {
      helper.IncrementCacheMisses();
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    if (!cacheHit) {
      spdlog::error("JIT XMM write failed at 0x{:x}: memory not accessible",
                    addr);
      cpu->TriggerInterrupt(EXC_PF, 0, false);
      throw JITException("XMM memory write fault");
    }
    spdlog::trace("JIT XMM write at 0x{:x}, size={}", addr, size);
  } catch (const std::exception &e) {
    spdlog::error("JIT XMM write fault at 0x{:x}: {}", addr, e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("XMM memory write fault");
  }
}

/**
 * @brief Pushes a 64-bit value onto the stack.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param value The 64-bit value to push.
 * @return The new stack pointer (RSP).
 * @throws JITException on stack access failure or null CPU pointer.
 */
uint64_t jitPush64(X86_64CPU *cpu, uint64_t value) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT push64: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  if (!x86_64::ValidateStackAccess(cpu, value)) {
    throw JITException("Invalid stack access parameters for push");
  }
  try {
    cpu->Push(value);
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementPushes();
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    uint64_t rsp = cpu->GetRegister(Register::RSP);
    spdlog::trace("JIT push64, value=0x{:x}, RSP=0x{:x}", value, rsp);
    return rsp;
  } catch (const std::exception &e) {
    spdlog::error("JIT push fault: {}", e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Stack push fault");
  }
}

/**
 * @brief Pops a 64-bit value from the stack.
 * @param cpu Pointer to the X86_64CPU instance.
 * @return The 64-bit value popped.
 * @throws JITException on stack access failure or null CPU pointer.
 */
uint64_t jitPop64(X86_64CPU *cpu) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT pop64: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  if (!x86_64::ValidateStackAccess(cpu)) { // No value to validate for pop
    throw JITException("Invalid stack access parameters for pop");
  }
  try {
    uint64_t value = cpu->Pop();
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementPops();
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    spdlog::trace("JIT pop64, value=0x{:x}", value);
    return value;
  } catch (const std::exception &e) {
    spdlog::error("JIT pop fault: {}", e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("Stack pop fault");
  }
}

/**
 * @brief Performs a direct branch to the specified address.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param addr The target address to branch to.
 * @throws JITException on null CPU pointer.
 */
void jitDirectBranch(X86_64CPU *cpu, uint64_t addr) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT direct branch: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  cpu->SetRegister(Register::RIP, addr);
  // RACE CONDITION FIX: Use atomic methods instead of direct access
  auto &helper = MemoryHelper::GetInstance();
  helper.IncrementBranches();
  auto end = std::chrono::steady_clock::now();
  auto duration = end - start;
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
  helper.AddLatency(latency);
  spdlog::trace("JIT direct branch to 0x{:x}", addr);
}

/**
 * @brief Performs a 64-bit addition and updates CPU flags.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param op1 First operand.
 * @param op2 Second operand.
 * @param sizeInBits Size of the operands in bits.
 * @throws JITException on null CPU pointer.
 */
void jitAdd64(X86_64CPU *cpu, uint64_t op1, uint64_t op2, uint8_t sizeInBits) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT add64: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  uint64_t result = op1 + op2;
  cpu->UpdateArithmeticFlags(op1, op2, result, sizeInBits, false);
  // RACE CONDITION FIX: Use atomic methods instead of direct access
  auto &helper = MemoryHelper::GetInstance();
  auto end = std::chrono::steady_clock::now();
  auto duration = end - start;
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
  helper.AddLatency(latency);
  spdlog::trace("JIT add64, op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                result);
}

/**
 * @brief Performs a 64-bit subtraction and updates CPU flags.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param op1 First operand.
 * @param op2 Second operand.
 * @param sizeInBits Size of the operands in bits.
 * @throws JITException on null CPU pointer.
 */
void jitSub64(X86_64CPU *cpu, uint64_t op1, uint64_t op2, uint8_t sizeInBits) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT sub64: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  uint64_t result = op1 - op2;
  cpu->UpdateArithmeticFlags(op1, op2, result, sizeInBits, true);
  // RACE CONDITION FIX: Use atomic methods instead of direct access
  auto &helper = MemoryHelper::GetInstance();
  auto end = std::chrono::steady_clock::now();
  auto duration = end - start;
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
  helper.AddLatency(latency);
  spdlog::trace("JIT sub64, op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                result);
}

/**
 * @brief Checks a Jcc condition code.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param conditionCode The condition code to check.
 * @return True if the condition is met, false otherwise.
 * @throws JITException on null CPU pointer.
 */
bool jitCheckJccCondition(X86_64CPU *cpu, uint8_t conditionCode) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT check Jcc: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  bool result = cpu->CheckCondition(conditionCode);
  // RACE CONDITION FIX: Use atomic methods instead of direct access
  auto &helper = MemoryHelper::GetInstance();
  auto end = std::chrono::steady_clock::now();
  auto duration = end - start;
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
  helper.AddLatency(latency);
  spdlog::trace("JIT check Jcc condition code 0x{:x}, result={}", conditionCode,
                result);
  return result;
}

/**
 * @brief Checks a general condition code (alias for jitCheckJccCondition).
 * @param cpu Pointer to the X86_64CPU instance.
 * @param conditionCode The condition code to check.
 * @return True if the condition is met, false otherwise.
 * @throws JITException on null CPU pointer.
 */
bool jitCheckCondition(X86_64CPU *cpu, uint8_t conditionCode) {
  return jitCheckJccCondition(cpu, conditionCode);
}

/**
 * @brief Performs SIMD addition (ADDPS) on XMM registers.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 * @throws JITException on null CPU pointer or invalid register index.
 */
void jitAddps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT addps: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  // Validate XMM register indices
  if (!ValidateSimdAccess(dest, 0) || !ValidateSimdAccess(src1, 0) || !ValidateSimdAccess(src2, 0)) {
    spdlog::error(
        "JIT addps: invalid XMM register index (dest={}, src1={}, src2={})",
        dest, src1, src2);
    throw JITException("Invalid XMM register index");
  }
  try {
    // Assuming GetXMMRegister and SetXMMRegister handle __m256i for AVX
    // If only SSE is supported, these would be __m128i and _mm_add_ps
    __m256i xmmSrc1 = cpu->GetXMMRegister(src1);
    __m256i xmmSrc2 = cpu->GetXMMRegister(src2);
    __m256 result = _mm256_add_ps(_mm256_castsi256_ps(xmmSrc1),
                                  _mm256_castsi256_ps(xmmSrc2));
    cpu->SetXMMRegister(dest, _mm256_castps_si256(result));
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementSimdAccesses();
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    spdlog::trace("JIT addps: XMM{}, XMM{}, XMM{}", dest, src1, src2);
  } catch (const std::exception &e) {
    spdlog::error("JIT addps fault: {}", e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("SIMD operation fault");
  }
}

/**
 * @brief Performs SIMD subtraction (SUBPS) on XMM registers.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 * @throws JITException on null CPU pointer or invalid register index.
 */
void jitSubps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT subps: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  // Validate XMM register indices
  if (!ValidateSimdAccess(dest, 0) || !ValidateSimdAccess(src1, 0) || !ValidateSimdAccess(src2, 0)) {
    spdlog::error(
        "JIT subps: invalid XMM register index (dest={}, src1={}, src2={})",
        dest, src1, src2);
    throw JITException("Invalid XMM register index");
  }
  try {
    __m256i xmmSrc1 = cpu->GetXMMRegister(src1);
    __m256i xmmSrc2 = cpu->GetXMMRegister(src2);
    __m256 result = _mm256_sub_ps(_mm256_castsi256_ps(xmmSrc1),
                                  _mm256_castsi256_ps(xmmSrc2));
    cpu->SetXMMRegister(dest, _mm256_castps_si256(result));
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementSimdAccesses();
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    spdlog::trace("JIT subps: XMM{}, XMM{}, XMM{}", dest, src1, src2);
  } catch (const std::exception &e) {
    spdlog::error("JIT subps fault: {}", e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("SIMD operation fault");
  }
}

/**
 * @brief Performs SIMD multiplication (MULPS) on XMM registers.
 * @param cpu Pointer to the X86_64CPU instance.
 * @param dest Destination XMM register index.
 * @param src1 First source XMM register index.
 * @param src2 Second source XMM register index.
 * @throws JITException on null CPU pointer or invalid register index.
 */
void jitMulps(X86_64CPU *cpu, uint64_t dest, uint64_t src1, uint64_t src2) {
  auto start = std::chrono::steady_clock::now();
  if (!cpu) {
    spdlog::error("JIT mulps: null CPU pointer");
    throw JITException("Null CPU pointer");
  }
  // Validate XMM register indices
  if (!ValidateSimdAccess(dest, 0) || !ValidateSimdAccess(src1, 0) || !ValidateSimdAccess(src2, 0)) {
    spdlog::error(
        "JIT mulps: invalid XMM register index (dest={}, src1={}, src2={})",
        dest, src1, src2);
    throw JITException("Invalid XMM register index");
  }
  try {
    __m256i xmmSrc1 = cpu->GetXMMRegister(src1);
    __m256i xmmSrc2 = cpu->GetXMMRegister(src2);
    __m256 result = _mm256_mul_ps(_mm256_castsi256_ps(xmmSrc1),
                                  _mm256_castsi256_ps(xmmSrc2));
    cpu->SetXMMRegister(dest, _mm256_castps_si256(result));
    // RACE CONDITION FIX: Use atomic methods instead of direct access
    auto &helper = MemoryHelper::GetInstance();
    helper.IncrementSimdAccesses();
    auto end = std::chrono::steady_clock::now();
    auto duration = end - start;
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    helper.AddLatency(latency);
    spdlog::trace("JIT mulps: XMM{}, XMM{}, XMM{}", dest, src1, src2);
  } catch (const std::exception &e) {
    spdlog::error("JIT mulps fault: {}", e.what());
    cpu->TriggerInterrupt(EXC_PF, 0, false);
    throw JITException("SIMD operation fault");
  }
}

} // namespace x86_64

#pragma warning(pop)
