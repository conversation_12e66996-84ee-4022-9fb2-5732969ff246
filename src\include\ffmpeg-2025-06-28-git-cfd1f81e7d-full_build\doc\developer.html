<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 7.2, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      Developer Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Developer Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="region-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Introduction" href="#Introduction">1 Introduction</a></li>
  <li><a id="toc-Coding-Rules" href="#Coding-Rules-1">2 Coding Rules</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Language" href="#Language">2.1 Language</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-SIMD_002fDSP" href="#SIMD_002fDSP-1">2.1.1 SIMD/DSP</a></li>
      <li><a id="toc-Other-languages" href="#Other-languages">2.1.2 Other languages</a></li>
    </ul></li>
    <li><a id="toc-Code-formatting-conventions" href="#Code-formatting-conventions">2.2 Code formatting conventions</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples" href="#Examples">2.2.1 Examples</a></li>
      <li><a id="toc-Vim-configuration" href="#Vim-configuration">2.2.2 Vim configuration</a></li>
      <li><a id="toc-Emacs-configuration" href="#Emacs-configuration">2.2.3 Emacs configuration</a></li>
    </ul></li>
    <li><a id="toc-Comments" href="#Comments">2.3 Comments</a></li>
    <li><a id="toc-Naming-conventions" href="#Naming-conventions-1">2.4 Naming conventions</a></li>
    <li><a id="toc-Miscellaneous-conventions" href="#Miscellaneous-conventions">2.5 Miscellaneous conventions</a></li>
  </ul></li>
  <li><a id="toc-Development-Policy" href="#Development-Policy-1">3 Development Policy</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Code-behaviour" href="#Code-behaviour">3.1 Code behaviour</a></li>
    <li><a id="toc-Patches_002fCommitting" href="#Patches_002fCommitting">3.2 Patches/Committing</a></li>
    <li><a id="toc-Code" href="#Code">3.3 Code</a></li>
    <li><a id="toc-Library-public-interfaces" href="#Library-public-interfaces">3.4 Library public interfaces</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Adding-new-interfaces" href="#Adding-new-interfaces">3.4.1 Adding new interfaces</a></li>
      <li><a id="toc-Removing-interfaces" href="#Removing-interfaces-1">3.4.2 Removing interfaces</a></li>
      <li><a id="toc-Major-version-bumps" href="#Major-version-bumps-1">3.4.3 Major version bumps</a></li>
    </ul></li>
    <li><a id="toc-Documentation_002fOther" href="#Documentation_002fOther">3.5 Documentation/Other</a></li>
  </ul></li>
  <li><a id="toc-Submitting-patches" href="#Submitting-patches-1">4 Submitting patches</a></li>
  <li><a id="toc-New-codecs-or-formats-checklist" href="#New-codecs-or-formats-checklist">5 New codecs or formats checklist</a></li>
  <li><a id="toc-Patch-submission-checklist" href="#Patch-submission-checklist">6 Patch submission checklist</a></li>
  <li><a id="toc-Patch-review-process" href="#Patch-review-process">7 Patch review process</a></li>
  <li><a id="toc-Regression-tests" href="#Regression-tests-1">8 Regression tests</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Adding-files-to-the-fate_002dsuite-dataset" href="#Adding-files-to-the-fate_002dsuite-dataset">8.1 Adding files to the fate-suite dataset</a></li>
    <li><a id="toc-Visualizing-Test-Coverage" href="#Visualizing-Test-Coverage">8.2 Visualizing Test Coverage</a></li>
    <li><a id="toc-Using-Valgrind" href="#Using-Valgrind">8.3 Using Valgrind</a></li>
  </ul></li>
  <li><a id="toc-Maintenance-process" href="#Maintenance-process">9 Maintenance process</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-MAINTAINERS" href="#MAINTAINERS-1">9.1 MAINTAINERS</a></li>
    <li><a id="toc-Becoming-a-maintainer" href="#Becoming-a-maintainer-1">9.2 Becoming a maintainer</a></li>
  </ul></li>
  <li><a id="toc-Release-process" href="#Release-process-1">10 Release process</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Criteria-for-Point-Releases" href="#Criteria-for-Point-Releases-1">10.1 Criteria for Point Releases</a></li>
    <li><a id="toc-Release-Checklist" href="#Release-Checklist">10.2 Release Checklist</a></li>
  </ul></li>
</ul>
</div>
</div>

<a name="Introduction"></a>
<h2 class="chapter">1 Introduction<span class="pull-right"><a class="anchor hidden-xs" href="#Introduction" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Introduction" aria-hidden="true">TOC</a></span></h2>

<p>This text is concerned with the development <em class="emph">of</em> FFmpeg itself. Information
on using the FFmpeg libraries in other programs can be found elsewhere, e.g. in:
</p><ul class="itemize mark-bullet">
<li>the installed header files
</li><li><a class="url" href="http://ffmpeg.org/doxygen/trunk/index.html">the Doxygen documentation</a>
generated from the headers
</li><li>the examples under <samp class="file">doc/examples</samp>
</li></ul>

<p>For more detailed legal information about the use of FFmpeg in
external programs read the <samp class="file">LICENSE</samp> file in the source tree and
consult <a class="url" href="https://ffmpeg.org/legal.html">https://ffmpeg.org/legal.html</a>.
</p>
<p>If you modify FFmpeg code for your own use case, you are highly encouraged to
<em class="emph">submit your changes back to us</em>, using this document as a guide. There are
both pragmatic and ideological reasons to do so:
</p><ul class="itemize mark-bullet">
<li>Maintaining external changes to keep up with upstream development is
time-consuming and error-prone. With your code in the main tree, it will be
maintained by FFmpeg developers.
</li><li>FFmpeg developers include leading experts in the field who can find bugs or
design flaws in your code.
</li><li>By supporting the project you find useful you ensure it continues to be
maintained and developed.
</li></ul>

<p>All proposed code changes should be submitted for review to
<a class="url" href="mailto:<EMAIL>">the development mailing list</a>, as
described in more detail in the <a class="ref" href="#Submitting-patches">Submitting patches</a> chapter. The code
should comply with the <a class="ref" href="#Development-Policy">Development Policy</a> and follow the <a class="ref" href="#Coding-Rules">Coding Rules</a>.
The developer making the commit and the author are responsible for their changes
and should try to fix issues their commit causes.
</p>
<a class="anchor" id="Coding-Rules"></a><a name="Coding-Rules-1"></a>
<h2 class="chapter">2 Coding Rules<span class="pull-right"><a class="anchor hidden-xs" href="#Coding-Rules" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Coding-Rules" aria-hidden="true">TOC</a></span></h2>

<a name="Language"></a>
<h3 class="section">2.1 Language<span class="pull-right"><a class="anchor hidden-xs" href="#Language" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Language" aria-hidden="true">TOC</a></span></h3>

<p>FFmpeg is mainly programmed in the ISO C11 language, except for the public
headers which must stay C99 compatible.
</p>
<p>Compiler-specific extensions may be used with good reason, but must not be
depended on, i.e. the code must still compile and work with compilers lacking
the extension.
</p>
<p>The following C99 features must not be used anywhere in the codebase:
</p><ul class="itemize mark-bullet">
<li>variable-length arrays;

</li><li>complex numbers;
</li></ul>

<a name="SIMD_002fDSP-1"></a>
<h4 class="subsection">2.1.1 SIMD/DSP<span class="pull-right"><a class="anchor hidden-xs" href="#SIMD_002fDSP" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-SIMD_002fDSP" aria-hidden="true">TOC</a></span></h4>
<a class="anchor" id="SIMD_002fDSP"></a>
<p>As modern compilers are unable to generate efficient SIMD or other
performance-critical DSP code from plain C, handwritten assembly is used.
Usually such code is isolated in a separate function. Then the standard approach
is writing multiple versions of this function – a plain C one that works
everywhere and may also be useful for debugging, and potentially multiple
architecture-specific optimized implementations. Initialization code then
chooses the best available version at runtime and loads it into a function
pointer; the function in question is then always called through this pointer.
</p>
<p>The specific syntax used for writing assembly is:
</p><ul class="itemize mark-bullet">
<li>NASM on x86;

</li><li>GAS on ARM and RISC-V.
</li></ul>

<p>A unit testing framework for assembly called <code class="code">checkasm</code> lives under
<samp class="file">tests/checkasm</samp>. All new assembly should come with <code class="code">checkasm</code> tests;
adding tests for existing assembly that lacks them is also strongly encouraged.
</p>
<a name="Other-languages"></a>
<h4 class="subsection">2.1.2 Other languages<span class="pull-right"><a class="anchor hidden-xs" href="#Other-languages" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Other-languages" aria-hidden="true">TOC</a></span></h4>

<p>Other languages than C may be used in special cases:
</p><ul class="itemize mark-bullet">
<li>Compiler intrinsics or inline assembly when the code in question cannot be
written in the standard way described in the <a class="ref" href="#SIMD_002fDSP">SIMD/DSP</a> section. This
typically applies to code that needs to be inlined.

</li><li>Objective-C where required for interacting with macOS-specific interfaces.
</li></ul>

<a name="Code-formatting-conventions"></a>
<h3 class="section">2.2 Code formatting conventions<span class="pull-right"><a class="anchor hidden-xs" href="#Code-formatting-conventions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Code-formatting-conventions" aria-hidden="true">TOC</a></span></h3>

<p>There are the following guidelines regarding the code style in files:
</p>
<ul class="itemize mark-bullet">
<li>Indent size is 4.

</li><li>The TAB character is forbidden outside of Makefiles as is any
form of trailing whitespace. Commits containing either will be
rejected by the git repository.

</li><li>You should try to limit your code lines to 80 characters; however, do so if
and only if this improves readability.

</li><li>K&amp;R coding style is used.
</li></ul>
<p>The presentation is one inspired by &rsquo;indent -i4 -kr -nut&rsquo;.
</p>
<a name="Examples"></a>
<h4 class="subsection">2.2.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>
<p>Some notable examples to illustrate common code style in FFmpeg:
</p>
<ul class="itemize mark-bullet">
<li>Space around assignments and after
<code class="code">if</code>/<code class="code">do</code>/<code class="code">while</code>/<code class="code">for</code> keywords:

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
if (condition)
    av_foo();
</pre></div>

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
for (size_t i = 0; i &lt; len; i++)
    av_bar(i);
</pre></div>

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
size_t size = 0;
</pre></div>

<p>However no spaces between the parentheses and condition, unless it helps
readability of complex conditions, so the following should not be done:
</p>
<div class="example user-c user-bad">
<pre class="example-preformatted">// Bad style
if ( condition )
    av_foo();
</pre></div>

</li><li>No unnecessary parentheses, unless it helps readability:

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
int fields = ilace ? 2 : 1;
</pre></div>

</li><li>Don&rsquo;t wrap single-line blocks in braces. Use braces only if there is an accompanying else statement. This keeps future code changes easier to keep track of.

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
if (bits_pixel == 24) {
    avctx-&gt;pix_fmt = AV_PIX_FMT_BGR24;
} else if (bits_pixel == 8) {
    avctx-&gt;pix_fmt = AV_PIX_FMT_GRAY8;
} else
    return AVERROR_INVALIDDATA;

</pre></div>

</li><li>Avoid assignments in conditions where it makes sense:

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
video_enc-&gt;chroma_intra_matrix = av_mallocz(sizeof(*video_enc-&gt;chroma_intra_matrix) * 64)
if (!video_enc-&gt;chroma_intra_matrix)
    return AVERROR(ENOMEM);
</pre></div>

<div class="example user-c user-bad">
<pre class="example-preformatted">// Bad style
if (!(video_enc-&gt;chroma_intra_matrix = av_mallocz(sizeof(*video_enc-&gt;chroma_intra_matrix) * 64)))
    return AVERROR(ENOMEM);
</pre></div>

<div class="example user-c user-good">
<pre class="example-preformatted">// Ok
while ((entry = av_dict_iterate(options, entry)))
    av_log(ctx, AV_LOG_INFO, &quot;Item '%s': '%s'\n&quot;, entry-&gt;key, entry-&gt;value);
</pre></div>

</li><li>When declaring a pointer variable, the <code class="code">*</code> goes with the variable not the type:

<div class="example user-c user-good">
<pre class="example-preformatted">// Good
AVStream *stream;
</pre></div>

<div class="example user-c user-bad">
<pre class="example-preformatted">// Bad style
AVStream* stream;
</pre></div>

</li></ul>

<p>If you work on a file that does not follow these guidelines consistently,
change the parts that you are editing to follow these guidelines but do
not make unrelated changes in the file to make it conform to these.
</p>
<a name="Vim-configuration"></a>
<h4 class="subsection">2.2.2 Vim configuration<span class="pull-right"><a class="anchor hidden-xs" href="#Vim-configuration" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Vim-configuration" aria-hidden="true">TOC</a></span></h4>
<p>In order to configure Vim to follow FFmpeg formatting conventions, paste
the following snippet into your <samp class="file">.vimrc</samp>:
</p><div class="example">
<pre class="example-preformatted">&quot; indentation rules for FFmpeg: 4 spaces, no tabs
set expandtab
set shiftwidth=4
set softtabstop=4
set cindent
set cinoptions=(0
&quot; Allow tabs in Makefiles.
autocmd FileType make,automake set noexpandtab shiftwidth=8 softtabstop=8
&quot; Trailing whitespace and tabs are forbidden, so highlight them.
highlight ForbiddenWhitespace ctermbg=red guibg=red
match ForbiddenWhitespace /\s\+$\|\t/
&quot; Do not highlight spaces at the end of line while typing on that line.
autocmd InsertEnter * match ForbiddenWhitespace /\t\|\s\+\%#\@&lt;!$/
</pre></div>

<a name="Emacs-configuration"></a>
<h4 class="subsection">2.2.3 Emacs configuration<span class="pull-right"><a class="anchor hidden-xs" href="#Emacs-configuration" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Emacs-configuration" aria-hidden="true">TOC</a></span></h4>
<p>For Emacs, add these roughly equivalent lines to your <samp class="file">.emacs.d/init.el</samp>:
</p><div class="example lisp">
<pre class="lisp-preformatted">(c-add-style &quot;ffmpeg&quot;
             '(&quot;k&amp;r&quot;
               (c-basic-offset . 4)
               (indent-tabs-mode . nil)
               (show-trailing-whitespace . t)
               (c-offsets-alist
                (statement-cont . (c-lineup-assignments +)))
               )
             )
(setq c-default-style &quot;ffmpeg&quot;)
</pre></div>

<a name="Comments"></a>
<h3 class="section">2.3 Comments<span class="pull-right"><a class="anchor hidden-xs" href="#Comments" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Comments" aria-hidden="true">TOC</a></span></h3>
<p>Use the JavaDoc/Doxygen  format (see examples below) so that code documentation
can be generated automatically. All nontrivial functions should have a comment
above them explaining what the function does, even if it is just one sentence.
All structures and their member variables should be documented, too.
</p>
<p>Avoid Qt-style and similar Doxygen syntax with <code class="code">!</code> in it, i.e. replace
<code class="code">//!</code> with <code class="code">///</code> and similar.  Also @ syntax should be employed
for markup commands, i.e. use <code class="code">@param</code> and not <code class="code">\param</code>.
</p>
<div class="example">
<pre class="example-preformatted">/**
 * @file
 * MPEG codec.
 * <AUTHOR>
 */

/**
 * Summary sentence.
 * more text ...
 * ...
 */
typedef struct Foobar {
    int var1; /**&lt; var1 description */
    int var2; ///&lt; var2 description
    /** var3 description */
    int var3;
} Foobar;

/**
 * Summary sentence.
 * more text ...
 * ...
 * @param my_parameter description of my_parameter
 * @return return value description
 */
int myfunc(int my_parameter)
...
</pre></div>

<a class="anchor" id="Naming-conventions"></a><a name="Naming-conventions-1"></a>
<h3 class="section">2.4 Naming conventions<span class="pull-right"><a class="anchor hidden-xs" href="#Naming-conventions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Naming-conventions" aria-hidden="true">TOC</a></span></h3>

<p>Names of functions, variables, and struct members must be lowercase, using
underscores (_) to separate words. For example, &lsquo;<samp class="samp">avfilter_get_video_buffer</samp>&rsquo;
is an acceptable function name and &lsquo;<samp class="samp">AVFilterGetVideo</samp>&rsquo; is not.
</p>
<p>Struct, union, enum, and typedeffed type names must use CamelCase. All structs
and unions should be typedeffed to the same name as the struct/union tag, e.g.
<code class="code">typedef struct AVFoo { ... } AVFoo;</code>. Enums are typically not
typedeffed.
</p>
<p>Enumeration constants and macros must be UPPERCASE, except for macros
masquerading as functions, which should use the function naming convention.
</p>
<p>All identifiers in the libraries should be namespaced as follows:
</p><ul class="itemize mark-bullet">
<li>No namespacing for identifiers with file and lower scope (e.g. local variables,
static functions), and struct and union members,

</li><li>The <code class="code">ff_</code> prefix must be used for variables and functions visible outside
of file scope, but only used internally within a single library, e.g.
&lsquo;<samp class="samp">ff_w64_demuxer</samp>&rsquo;. This prevents name collisions when FFmpeg is statically
linked.

</li><li>For variables and functions visible outside of file scope, used internally
across multiple libraries, use <code class="code">avpriv_</code> as prefix, for example,
&lsquo;<samp class="samp">avpriv_report_missing_feature</samp>&rsquo;.

</li><li>All other internal identifiers, like private type or macro names, should be
namespaced only to avoid possible internal conflicts. E.g. <code class="code">H264_NAL_SPS</code>
vs. <code class="code">HEVC_NAL_SPS</code>.

</li><li>Each library has its own prefix for public symbols, in addition to the
commonly used <code class="code">av_</code> (<code class="code">avformat_</code> for libavformat,
<code class="code">avcodec_</code> for libavcodec, <code class="code">swr_</code> for libswresample, etc).
Check the existing code and choose names accordingly.

</li><li>Other public identifiers (struct, union, enum, macro, type names) must use their
library&rsquo;s public prefix (<code class="code">AV</code>, <code class="code">Sws</code>, or <code class="code">Swr</code>).
</li></ul>

<p>Furthermore, name space reserved for the system should not be invaded.
Identifiers ending in <code class="code">_t</code> are reserved by
<a class="url" href="http://pubs.opengroup.org/onlinepubs/007904975/functions/xsh_chap02_02.html#tag_02_02_02">POSIX</a>.
Also avoid names starting with <code class="code">__</code> or <code class="code">_</code> followed by an uppercase
letter as they are reserved by the C standard. Names starting with <code class="code">_</code>
are reserved at the file level and may not be used for externally visible
symbols. If in doubt, just avoid names starting with <code class="code">_</code> altogether.
</p>
<a name="Miscellaneous-conventions"></a>
<h3 class="section">2.5 Miscellaneous conventions<span class="pull-right"><a class="anchor hidden-xs" href="#Miscellaneous-conventions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Miscellaneous-conventions" aria-hidden="true">TOC</a></span></h3>

<ul class="itemize mark-bullet">
<li>Casts should be used only when necessary. Unneeded parentheses
should also be avoided if they don&rsquo;t make the code easier to understand.
</li></ul>

<a class="anchor" id="Development-Policy"></a><a name="Development-Policy-1"></a>
<h2 class="chapter">3 Development Policy<span class="pull-right"><a class="anchor hidden-xs" href="#Development-Policy" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Development-Policy" aria-hidden="true">TOC</a></span></h2>

<a name="Code-behaviour"></a>
<h3 class="section">3.1 Code behaviour<span class="pull-right"><a class="anchor hidden-xs" href="#Code-behaviour" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Code-behaviour" aria-hidden="true">TOC</a></span></h3>

<a name="Correctness"></a>
<p>The code must be valid. It must not crash, abort, access invalid pointers, leak
memory, cause data races or signed integer overflow, or otherwise cause
undefined behaviour. Error codes should be checked and, when applicable,
forwarded to the caller.
</p>
<a name="Thread_002d-and-library_002dsafety"></a>
<p>Our libraries may be called by multiple independent callers in the same process.
These calls may happen from any number of threads and the different call sites
may not be aware of each other - e.g. a user program may be calling our
libraries directly, and use one or more libraries that also call our libraries.
The code must behave correctly under such conditions.
</p>
<a name="Robustness"></a>
<p>The code must treat as untrusted any bytestream received from a caller or read
from a file, network, etc. It must not misbehave when arbitrary data is sent to
it - typically it should print an error message and return
<code class="code">AVERROR_INVALIDDATA</code> on encountering invalid input data.
</p>
<a name="Memory-allocation"></a>
<p>The code must use the <code class="code">av_malloc()</code> family of functions from
<samp class="file">libavutil/mem.h</samp> to perform all memory allocation, except in special cases
(e.g. when interacting with an external library that requires a specific
allocator to be used).
</p>
<p>All allocations should be checked and <code class="code">AVERROR(ENOMEM)</code> returned on
failure. A common mistake is that error paths leak memory - make sure that does
not happen.
</p>
<a name="stdio"></a>
<p>Our libraries must not access the stdio streams stdin/stdout/stderr directly
(e.g. via <code class="code">printf()</code> family of functions), as that is not library-safe. For
logging, use <code class="code">av_log()</code>.
</p>
<a name="Patches_002fCommitting"></a>
<h3 class="section">3.2 Patches/Committing<span class="pull-right"><a class="anchor hidden-xs" href="#Patches_002fCommitting" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Patches_002fCommitting" aria-hidden="true">TOC</a></span></h3>
<a name="Licenses-for-patches-must-be-compatible-with-FFmpeg_002e"></a>
<p>Contributions should be licensed under the
<a class="uref" href="http://www.gnu.org/licenses/lgpl-2.1.html">LGPL 2.1</a>,
including an &quot;or any later version&quot; clause, or, if you prefer
a gift-style license, the
<a class="uref" href="http://opensource.org/licenses/isc-license.txt">ISC</a> or
<a class="uref" href="http://mit-license.org/">MIT</a> license.
<a class="uref" href="http://www.gnu.org/licenses/gpl-2.0.html">GPL 2</a> including
an &quot;or any later version&quot; clause is also acceptable, but LGPL is
preferred.
If you add a new file, give it a proper license header. Do not copy and
paste it from a random place, use an existing file as template.
</p>
<a name="You-must-not-commit-code-which-breaks-FFmpeg_0021"></a>
<p>This means unfinished code which is enabled and breaks compilation,
or compiles but does not work/breaks the regression tests. Code which
is unfinished but disabled may be permitted under-circumstances, like
missing samples or an implementation with a small subset of features.
Always check the mailing list for any reviewers with issues and test
FATE before you push.
</p>
<a name="Commit-messages"></a>
<p>Commit messages are highly important tools for informing other developers on
what a given change does and why. Every commit must always have a properly
filled out commit message with the following format:
</p><div class="example">
<pre class="example-preformatted">area changed: short 1 line description

details describing what and why and giving references.
</pre></div>

<p>If the commit addresses a known bug on our bug tracker or other external issue
(e.g. CVE), the commit message should include the relevant bug ID(s) or other
external identifiers. Note that this should be done in addition to a proper
explanation and not instead of it. Comments such as &quot;fixed!&quot; or &quot;Changed it.&quot;
are not acceptable.
</p>
<p>When applying patches that have been discussed at length on the mailing list,
reference the thread in the commit message.
</p>
<a name="Testing-must-be-adequate-but-not-excessive_002e"></a>
<p>If it works for you, others, and passes FATE then it should be OK to commit
it, provided it fits the other committing criteria. You should not worry about
over-testing things. If your code has problems (portability, triggers
compiler bugs, unusual environment etc) they will be reported and eventually
fixed.
</p>
<a name="Do-not-commit-unrelated-changes-together_002e"></a>
<p>They should be split them into self-contained pieces. Also do not forget
that if part B depends on part A, but A does not depend on B, then A can
and should be committed first and separate from B. Keeping changes well
split into self-contained parts makes reviewing and understanding them on
the commit log mailing list easier. This also helps in case of debugging
later on.
Also if you have doubts about splitting or not splitting, do not hesitate to
ask/discuss it on the developer mailing list.
</p>
<a name="Cosmetic-changes-should-be-kept-in-separate-patches_002e"></a>
<p>We refuse source indentation and other cosmetic changes if they are mixed
with functional changes, such commits will be rejected and removed. Every
developer has his own indentation style, you should not change it. Of course
if you (re)write something, you can use your own style, even though we would
prefer if the indentation throughout FFmpeg was consistent (Many projects
force a given indentation style - we do not.). If you really need to make
indentation changes (try to avoid this), separate them strictly from real
changes.
</p>
<p>NOTE: If you had to put if(){ .. } over a large (&gt; 5 lines) chunk of code,
then either do NOT change the indentation of the inner part within (do not
move it to the right)! or do so in a separate commit
</p>
<a name="Credit-the-author-of-the-patch_002e"></a>
<p>Make sure the author of the commit is set correctly. (see git commit &ndash;author)
If you apply a patch, send an
answer to ffmpeg-devel (or wherever you got the patch from) saying that
you applied the patch.
</p>
<a name="Credit-any-researchers"></a>
<p>If a commit/patch fixes an issues found by some researcher, always credit the
researcher in the commit message for finding/reporting the issue.
</p>
<a name="Always-wait-long-enough-before-pushing-changes"></a>
<p>Do NOT commit to code actively maintained by others without permission.
Send a patch to ffmpeg-devel. If no one answers within a reasonable
time-frame (12h for build failures and security fixes, 3 days small changes,
1 week for big patches) then commit your patch if you think it is OK.
Also note, the maintainer can simply ask for more time to review!
</p>
<a name="Code"></a>
<h3 class="section">3.3 Code<span class="pull-right"><a class="anchor hidden-xs" href="#Code" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Code" aria-hidden="true">TOC</a></span></h3>
<a name="Warnings-for-correct-code-may-be-disabled-if-there-is-no-other-option_002e"></a>
<p>Compiler warnings indicate potential bugs or code with bad style. If a type of
warning always points to correct and clean code, that warning should
be disabled, not the code changed.
Thus the remaining warnings can either be bugs or correct code.
If it is a bug, the bug has to be fixed. If it is not, the code should
be changed to not generate a warning unless that causes a slowdown
or obfuscates the code.
</p>
<a name="Library-public-interfaces"></a>
<h3 class="section">3.4 Library public interfaces<span class="pull-right"><a class="anchor hidden-xs" href="#Library-public-interfaces" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Library-public-interfaces" aria-hidden="true">TOC</a></span></h3>
<p>Every library in FFmpeg provides a set of public APIs in its installed headers,
which are those listed in the variable <code class="code">HEADERS</code> in that library&rsquo;s
<samp class="file">Makefile</samp>. All identifiers defined in those headers (except for those
explicitly documented otherwise), and corresponding symbols exported from
compiled shared or static libraries are considered public interfaces and must
comply with the API and ABI compatibility rules described in this section.
</p>
<p>Public APIs must be backward compatible within a given major version. I.e. any
valid user code that compiles and works with a given library version must still
compile and work with any later version, as long as the major version number is
unchanged. &quot;Valid user code&quot; here means code that is calling our APIs in a
documented and/or intended manner and is not relying on any undefined behavior.
Incrementing the major version may break backward compatibility, but only to the
extent described in <a class="ref" href="#Major-version-bumps">Major version bumps</a>.
</p>
<p>We also guarantee backward ABI compatibility for shared and static libraries.
I.e. it should be possible to replace a shared or static build of our library
with a build of any later version (re-linking the user binary in the static
case) without breaking any valid user binaries, as long as the major version
number remains unchanged.
</p>
<a name="Adding-new-interfaces"></a>
<h4 class="subsection">3.4.1 Adding new interfaces<span class="pull-right"><a class="anchor hidden-xs" href="#Adding-new-interfaces" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Adding-new-interfaces" aria-hidden="true">TOC</a></span></h4>
<p>Any new public identifiers in installed headers are considered new API - this
includes new functions, structs, macros, enum values, typedefs, new fields in
existing structs, new installed headers, etc. Consider the following
guidelines when adding new APIs.
</p>
<a name="Motivation"></a>
<p>While new APIs can be added relatively easily, changing or removing them is much
harder due to abovementioned compatibility requirements. You should then
consider carefully whether the functionality you are adding really needs to be
exposed to our callers as new public API.
</p>
<p>Your new API should have at least one well-established use case outside of the
library that cannot be easily achieved with existing APIs. Every library in
FFmpeg also has a defined scope - your new API must fit within it.
</p>
<a name="Replacing-existing-APIs"></a>
<p>If your new API is replacing an existing one, it should be strictly superior to
it, so that the advantages of using the new API outweight the cost to the
callers of changing their code. After adding the new API you should then
deprecate the old one and schedule it for removal, as described in
<a class="ref" href="#Removing-interfaces">Removing interfaces</a>.
</p>
<p>If you deem an existing API deficient and want to fix it, the preferred approach
in most cases is to add a differently-named replacement and deprecate the
existing API rather than modify it. It is important to make the changes visible
to our callers (e.g. through compile- or run-time deprecation warnings) and make
it clear how to transition to the new API (e.g. in the Doxygen documentation or
on the wiki).
</p>
<a name="API-design"></a>
<p>The FFmpeg libraries are used by a variety of callers to perform a wide range of
multimedia-related processing tasks. You should therefore - within reason - try
to design your new API for the broadest feasible set of use cases and avoid
unnecessarily limiting it to a specific type of callers (e.g. just media
playback or just transcoding).
</p>
<a name="Consistency"></a>
<p>Check whether similar APIs already exist in FFmpeg. If they do, try to model
your new addition on them to achieve better overall consistency.
</p>
<p>The naming of your new identifiers should follow the <a class="ref" href="#Naming-conventions">Naming conventions</a>
and be aligned with other similar APIs, if applicable.
</p>
<a name="Extensibility"></a>
<p>You should also consider how your API might be extended in the future in a
backward-compatible way. If you are adding a new struct <code class="code">AVFoo</code>, the
standard approach is requiring the caller to always allocate it through a
constructor function, typically named <code class="code">av_foo_alloc()</code>. This way new fields
may be added to the end of the struct without breaking ABI compatibility.
Typically you will also want a destructor - <code class="code">av_foo_free(AVFoo**)</code> that
frees the indirectly supplied object (and its contents, if applicable) and
writes <code class="code">NULL</code> to the supplied pointer, thus eliminating the potential
dangling pointer in the caller&rsquo;s memory.
</p>
<p>If you are adding new functions, consider whether it might be desirable to tweak
their behavior in the future - you may want to add a flags argument, even though
it would be unused initially.
</p>
<a name="Documentation"></a>
<p>All new APIs must be documented as Doxygen-formatted comments above the
identifiers you add to the public headers. You should also briefly mention the
change in <samp class="file">doc/APIchanges</samp>.
</p>
<a name="Bump-the-version"></a>
<p>Backward-incompatible API or ABI changes require incrementing (bumping) the
major version number, as described in <a class="ref" href="#Major-version-bumps">Major version bumps</a>. Major
bumps are significant events that happen on a schedule - so if your change
strictly requires one you should add it under <code class="code">#if</code> preprocesor guards that
disable it until the next major bump happens.
</p>
<p>New APIs that can be added without breaking API or ABI compatibility require
bumping the minor version number.
</p>
<p>Incrementing the third (micro) version component means a noteworthy binary
compatible change (e.g. encoder bug fix that matters for the decoder). The third
component always starts at 100 to distinguish FFmpeg from Libav.
</p>
<a class="anchor" id="Removing-interfaces"></a><a name="Removing-interfaces-1"></a>
<h4 class="subsection">3.4.2 Removing interfaces<span class="pull-right"><a class="anchor hidden-xs" href="#Removing-interfaces" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Removing-interfaces" aria-hidden="true">TOC</a></span></h4>
<p>Due to abovementioned compatibility guarantees, removing APIs is an involved
process that should only be undertaken with good reason. Typically a deficient,
restrictive, or otherwise inadequate API is replaced by a superior one, though
it does at times happen that we remove an API without any replacement (e.g. when
the feature it provides is deemed not worth the maintenance effort, out of scope
of the project, fundamentally flawed, etc.).
</p>
<p>The removal has two steps - first the API is deprecated and scheduled for
removal, but remains present and functional. The second step is actually
removing the API - this is described in <a class="ref" href="#Major-version-bumps">Major version bumps</a>.
</p>
<p>To deprecate an API you should signal to our users that they should stop using
it. E.g. if you intend to remove struct members or functions, you should mark
them with <code class="code">attribute_deprecated</code>. When this cannot be done, it may be
possible to detect the use of the deprecated API at runtime and print a warning
(though take care not to print it too often). You should also document the
deprecation (and the replacement, if applicable) in the relevant Doxygen
documentation block.
</p>
<p>Finally, you should define a deprecation guard along the lines of
<code class="code">#define FF_API_&lt;FOO&gt; (LIBAVBAR_VERSION_MAJOR &lt; XX)</code> (where XX is the major
version in which the API will be removed) in <samp class="file">libavbar/version_major.h</samp>
(<samp class="file">version.h</samp> in case of <code class="code">libavutil</code>). Then wrap all uses of the
deprecated API in <code class="code">#if FF_API_&lt;FOO&gt; .... #endif</code>, so that the code will
automatically get disabled once the major version reaches XX. You can also use
<code class="code">FF_DISABLE_DEPRECATION_WARNINGS</code> and <code class="code">FF_ENABLE_DEPRECATION_WARNINGS</code>
to suppress compiler deprecation warnings inside these guards. You should test
that the code compiles and works with the guard macro evaluating to both true
and false.
</p>
<a class="anchor" id="Major-version-bumps"></a><a name="Major-version-bumps-1"></a>
<h4 class="subsection">3.4.3 Major version bumps<span class="pull-right"><a class="anchor hidden-xs" href="#Major-version-bumps" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Major-version-bumps" aria-hidden="true">TOC</a></span></h4>
<p>A major version bump signifies an API and/or ABI compatibility break. To reduce
the negative effects on our callers, who are required to adapt their code,
backward-incompatible changes during a major bump should be limited to:
</p><ul class="itemize mark-bullet">
<li>Removing previously deprecated APIs.

</li><li>Performing ABI- but not API-breaking changes, like reordering struct contents.
</li></ul>

<a name="Documentation_002fOther"></a>
<h3 class="section">3.5 Documentation/Other<span class="pull-right"><a class="anchor hidden-xs" href="#Documentation_002fOther" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Documentation_002fOther" aria-hidden="true">TOC</a></span></h3>
<a name="Subscribe-to-the-ffmpeg_002ddevel-mailing-list_002e"></a>
<p>It is important to be subscribed to the
<a class="uref" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-devel">ffmpeg-devel</a>
mailing list. Almost any non-trivial patch is to be sent there for review.
Other developers may have comments about your contribution. We expect you see
those comments, and to improve it if requested. (N.B. Experienced committers
have other channels, and may sometimes skip review for trivial fixes.) Also,
discussion here about bug fixes and FFmpeg improvements by other developers may
be helpful information for you. Finally, by being a list subscriber, your
contribution will be posted immediately to the list, without the moderation
hold which messages from non-subscribers experience.
</p>
<p>However, it is more important to the project that we receive your patch than
that you be subscribed to the ffmpeg-devel list. If you have a patch, and don&rsquo;t
want to subscribe and discuss the patch, then please do send it to the list
anyway.
</p>
<a name="Subscribe-to-the-ffmpeg_002dcvslog-mailing-list_002e"></a>
<p>Diffs of all commits are sent to the
<a class="uref" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-cvslog">ffmpeg-cvslog</a>
mailing list. Some developers read this list to review all code base changes
from all sources. Subscribing to this list is not mandatory.
</p>
<a name="Keep-the-documentation-up-to-date_002e"></a>
<p>Update the documentation if you change behavior or add features. If you are
unsure how best to do this, send a patch to ffmpeg-devel, the documentation
maintainer(s) will review and commit your stuff.
</p>
<a name="Important-discussions-should-be-accessible-to-all_002e"></a>
<p>Try to keep important discussions and requests (also) on the public
developer mailing list, so that all developers can benefit from them.
</p>
<a name="Check-your-entries-in-MAINTAINERS_002e"></a>
<p>Make sure that no parts of the codebase that you maintain are missing from the
<samp class="file">MAINTAINERS</samp> file. If something that you want to maintain is missing add it with
your name after it.
If at some point you no longer want to maintain some code, then please help in
finding a new maintainer and also don&rsquo;t forget to update the <samp class="file">MAINTAINERS</samp> file.
</p>
<p>We think our rules are not too hard. If you have comments, contact us.
</p>
<a class="anchor" id="Submitting-patches"></a><a name="Submitting-patches-1"></a>
<h2 class="chapter">4 Submitting patches<span class="pull-right"><a class="anchor hidden-xs" href="#Submitting-patches" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Submitting-patches" aria-hidden="true">TOC</a></span></h2>

<p>First, read the <a class="ref" href="#Coding-Rules">Coding Rules</a> above if you did not yet, in particular
the rules regarding patch submission.
</p>
<p>When you submit your patch, please use <code class="code">git format-patch</code> or
<code class="code">git send-email</code>. We cannot read other diffs :-).
</p>
<p>Also please do not submit a patch which contains several unrelated changes.
Split it into separate, self-contained pieces. This does not mean splitting
file by file. Instead, make the patch as small as possible while still
keeping it as a logical unit that contains an individual change, even
if it spans multiple files. This makes reviewing your patches much easier
for us and greatly increases your chances of getting your patch applied.
</p>
<p>Use the patcheck tool of FFmpeg to check your patch.
The tool is located in the tools directory.
</p>
<p>Run the <a class="ref" href="#Regression-tests">Regression tests</a> before submitting a patch in order to verify
it does not cause unexpected problems.
</p>
<p>It also helps quite a bit if you tell us what the patch does (for example
&rsquo;replaces lrint by lrintf&rsquo;), and why (for example &rsquo;*BSD isn&rsquo;t C99 compliant
and has no lrint()&rsquo;)
</p>
<p>Also please if you send several patches, send each patch as a separate mail,
do not attach several unrelated patches to the same mail.
</p>
<p>Patches should be posted to the
<a class="uref" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-devel">ffmpeg-devel</a>
mailing list. Use <code class="code">git send-email</code> when possible since it will properly
send patches without requiring extra care. If you cannot, then send patches
as base64-encoded attachments, so your patch is not trashed during
transmission. Also ensure the correct mime type is used
(text/x-diff or text/x-patch or at least text/plain) and that only one
patch is inline or attached per mail.
You can check <a class="url" href="https://patchwork.ffmpeg.org">https://patchwork.ffmpeg.org</a>, if your patch does not show up, its mime type
likely was wrong.
</p>
<a name="How-to-setup-git-send_002demail_003f"></a>

<p>Please see <a class="url" href="https://git-send-email.io/">https://git-send-email.io/</a>.
For gmail additionally see <a class="url" href="https://shallowsky.com/blog/tech/email/gmail-app-passwds.html">https://shallowsky.com/blog/tech/email/gmail-app-passwds.html</a>.
</p>
<a name="Sending-patches-from-email-clients"></a>
<p>Using <code class="code">git send-email</code> might not be desirable for everyone. The
following trick allows to send patches via email clients in a safe
way. It has been tested with Outlook and Thunderbird (with X-Unsent
extension) and might work with other applications.
</p>
<p>Create your patch like this:
</p>
<pre class="verbatim">git format-patch -s -o &quot;outputfolder&quot; --add-header &quot;X-Unsent: 1&quot; --suffix .eml --to <EMAIL> -1 1a2b3c4d
</pre>
<p>Now you&rsquo;ll just need to open the eml file with the email application
and execute &rsquo;Send&rsquo;.
</p>
<a name="Reviews"></a>
<p>Your patch will be reviewed on the mailing list. You will likely be asked
to make some changes and are expected to send in an improved version that
incorporates the requests from the review. This process may go through
several iterations. Once your patch is deemed good enough, some developer
will pick it up and commit it to the official FFmpeg tree.
</p>
<p>Give us a few days to react. But if some time passes without reaction,
send a reminder by email. Your patch should eventually be dealt with.
</p>

<a name="New-codecs-or-formats-checklist"></a>
<h2 class="chapter">5 New codecs or formats checklist<span class="pull-right"><a class="anchor hidden-xs" href="#New-codecs-or-formats-checklist" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-New-codecs-or-formats-checklist" aria-hidden="true">TOC</a></span></h2>

<ol class="enumerate">
<li> Did you use av_cold for codec initialization and close functions?

</li><li> Did you add a long_name under NULL_IF_CONFIG_SMALL to the AVCodec or
AVInputFormat/AVOutputFormat struct?

</li><li> Did you bump the minor version number (and reset the micro version
number) in <samp class="file">libavcodec/version.h</samp> or <samp class="file">libavformat/version.h</samp>?

</li><li> Did you register it in <samp class="file">allcodecs.c</samp> or <samp class="file">allformats.c</samp>?

</li><li> Did you add the AVCodecID to <samp class="file">codec_id.h</samp>?
When adding new codec IDs, also add an entry to the codec descriptor
list in <samp class="file">libavcodec/codec_desc.c</samp>.

</li><li> If it has a FourCC, did you add it to <samp class="file">libavformat/riff.c</samp>,
even if it is only a decoder?

</li><li> Did you add a rule to compile the appropriate files in the Makefile?
Remember to do this even if you&rsquo;re just adding a format to a file that is
already being compiled by some other rule, like a raw demuxer.

</li><li> Did you add an entry to the table of supported formats or codecs in
<samp class="file">doc/general_contents.texi</samp>?

</li><li> Did you add an entry in the Changelog?

</li><li> If it depends on a parser or a library, did you add that dependency in
configure?

</li><li> Did you <code class="code">git add</code> the appropriate files before committing?

</li><li> Did you make sure it compiles standalone, i.e. with
<code class="code">configure --disable-everything --enable-decoder=foo</code>
(or <code class="code">--enable-demuxer</code> or whatever your component is)?
</li></ol>


<a name="Patch-submission-checklist"></a>
<h2 class="chapter">6 Patch submission checklist<span class="pull-right"><a class="anchor hidden-xs" href="#Patch-submission-checklist" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Patch-submission-checklist" aria-hidden="true">TOC</a></span></h2>

<ol class="enumerate">
<li> Does <code class="code">make fate</code> pass with the patch applied?

</li><li> Was the patch generated with git format-patch or send-email?

</li><li> Did you sign-off your patch? (<code class="code">git commit -s</code>)
See <a class="uref" href="https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/plain/Documentation/process/submitting-patches.rst">Sign your work</a> for the meaning
of <em class="dfn">sign-off</em>.

</li><li> Did you provide a clear git commit log message?

</li><li> Is the patch against latest FFmpeg git master branch?

</li><li> Are you subscribed to ffmpeg-devel?
(the list is subscribers only due to spam)

</li><li> Have you checked that the changes are minimal, so that the same cannot be
achieved with a smaller patch and/or simpler final code?

</li><li> If the change is to speed critical code, did you benchmark it?

</li><li> If you did any benchmarks, did you provide them in the mail?

</li><li> Have you checked that the patch does not introduce buffer overflows or
other security issues?

</li><li> Did you test your decoder or demuxer against damaged data? If no, see
tools/trasher, the noise bitstream filter, and
<a class="uref" href="http://caca.zoy.org/wiki/zzuf">zzuf</a>. Your decoder or demuxer
should not crash, end in a (near) infinite loop, or allocate ridiculous
amounts of memory when fed damaged data.

</li><li> Did you test your decoder or demuxer against sample files?
Samples may be obtained at <a class="url" href="https://samples.ffmpeg.org">https://samples.ffmpeg.org</a>.

</li><li> Does the patch not mix functional and cosmetic changes?

</li><li> Did you add tabs or trailing whitespace to the code? Both are forbidden.

</li><li> Is the patch attached to the email you send?

</li><li> Is the mime type of the patch correct? It should be text/x-diff or
text/x-patch or at least text/plain and not application/octet-stream.

</li><li> If the patch fixes a bug, did you provide a verbose analysis of the bug?

</li><li> If the patch fixes a bug, did you provide enough information, including
a sample, so the bug can be reproduced and the fix can be verified?
Note please do not attach samples &gt;100k to mails but rather provide a
URL, you can upload to <a class="url" href="https://streams.videolan.org/upload/">https://streams.videolan.org/upload/</a>.

</li><li> Did you provide a verbose summary about what the patch does change?

</li><li> Did you provide a verbose explanation why it changes things like it does?

</li><li> Did you provide a verbose summary of the user visible advantages and
disadvantages if the patch is applied?

</li><li> Did you provide an example so we can verify the new feature added by the
patch easily?

</li><li> If you added a new file, did you insert a license header? It should be
taken from FFmpeg, not randomly copied and pasted from somewhere else.

</li><li> You should maintain alphabetical order in alphabetically ordered lists as
long as doing so does not break API/ABI compatibility.

</li><li> Lines with similar content should be aligned vertically when doing so
improves readability.

</li><li> Consider adding a regression test for your code. All new modules
should be covered by tests. That includes demuxers, muxers, decoders, encoders
filters, bitstream filters, parsers. If its not possible to do that, add
an explanation why to your patchset, its ok to not test if theres a reason.

</li><li> If you added NASM code please check that things still work with &ndash;disable-x86asm.

</li><li> Test your code with valgrind and or Address Sanitizer to ensure it&rsquo;s free
of leaks, out of array accesses, etc.
</li></ol>

<a name="Patch-review-process"></a>
<h2 class="chapter">7 Patch review process<span class="pull-right"><a class="anchor hidden-xs" href="#Patch-review-process" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Patch-review-process" aria-hidden="true">TOC</a></span></h2>

<p>All patches posted to ffmpeg-devel will be reviewed, unless they contain a
clear note that the patch is not for the git master branch.
Reviews and comments will be posted as replies to the patch on the
mailing list. The patch submitter then has to take care of every comment,
that can be by resubmitting a changed patch or by discussion. Resubmitted
patches will themselves be reviewed like any other patch. If at some point
a patch passes review with no comments then it is approved, that can for
simple and small patches happen immediately while large patches will generally
have to be changed and reviewed many times before they are approved.
After a patch is approved it will be committed to the repository.
</p>
<p>We will review all submitted patches, but sometimes we are quite busy so
especially for large patches this can take several weeks.
</p>
<p>If you feel that the review process is too slow and you are willing to try to
take over maintainership of the area of code you change then just clone
git master and maintain the area of code there. We will merge each area from
where its best maintained.
</p>
<p>When resubmitting patches, please do not make any significant changes
not related to the comments received during review. Such patches will
be rejected. Instead, submit significant changes or new features as
separate patches.
</p>
<p>Everyone is welcome to review patches. Also if you are waiting for your patch
to be reviewed, please consider helping to review other patches, that is a great
way to get everyone&rsquo;s patches reviewed sooner.
</p>
<a class="anchor" id="Regression-tests"></a><a name="Regression-tests-1"></a>
<h2 class="chapter">8 Regression tests<span class="pull-right"><a class="anchor hidden-xs" href="#Regression-tests" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Regression-tests" aria-hidden="true">TOC</a></span></h2>

<p>Before submitting a patch (or committing to the repository), you should at least
test that you did not break anything.
</p>
<p>Running &rsquo;make fate&rsquo; accomplishes this, please see <a class="url" href="fate.html">fate.html</a> for details.
</p>
<p>[Of course, some patches may change the results of the regression tests. In
this case, the reference results of the regression tests shall be modified
accordingly].
</p>
<a name="Adding-files-to-the-fate_002dsuite-dataset"></a>
<h3 class="section">8.1 Adding files to the fate-suite dataset<span class="pull-right"><a class="anchor hidden-xs" href="#Adding-files-to-the-fate_002dsuite-dataset" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Adding-files-to-the-fate_002dsuite-dataset" aria-hidden="true">TOC</a></span></h3>

<p>If you need a sample uploaded send a mail to samples-request.
</p>
<p>When there is no muxer or encoder available to generate test media for a
specific test then the media has to be included in the fate-suite.
First please make sure that the sample file is as small as possible to test the
respective decoder or demuxer sufficiently. Large files increase network
bandwidth and disk space requirements.
Once you have a working fate test and fate sample, provide in the commit
message or introductory message for the patch series that you post to
the ffmpeg-devel mailing list, a direct link to download the sample media.
</p>
<a name="Visualizing-Test-Coverage"></a>
<h3 class="section">8.2 Visualizing Test Coverage<span class="pull-right"><a class="anchor hidden-xs" href="#Visualizing-Test-Coverage" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Visualizing-Test-Coverage" aria-hidden="true">TOC</a></span></h3>

<p>The FFmpeg build system allows visualizing the test coverage in an easy
manner with the coverage tools <code class="code">gcov</code>/<code class="code">lcov</code>.  This involves
the following steps:
</p>
<ol class="enumerate">
<li> Configure to compile with instrumentation enabled:
    <code class="code">configure --toolchain=gcov</code>.

</li><li> Run your test case, either manually or via FATE. This can be either
    the full FATE regression suite, or any arbitrary invocation of any
    front-end tool provided by FFmpeg, in any combination.

</li><li> Run <code class="code">make lcov</code> to generate coverage data in HTML format.

</li><li> View <code class="code">lcov/index.html</code> in your preferred HTML viewer.
</li></ol>

<p>You can use the command <code class="code">make lcov-reset</code> to reset the coverage
measurements. You will need to rerun <code class="code">make lcov</code> after running a
new test.
</p>
<a name="Using-Valgrind"></a>
<h3 class="section">8.3 Using Valgrind<span class="pull-right"><a class="anchor hidden-xs" href="#Using-Valgrind" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Using-Valgrind" aria-hidden="true">TOC</a></span></h3>

<p>The configure script provides a shortcut for using valgrind to spot bugs
related to memory handling. Just add the option
<code class="code">--toolchain=valgrind-memcheck</code> or <code class="code">--toolchain=valgrind-massif</code>
to your configure line, and reasonable defaults will be set for running
FATE under the supervision of either the <strong class="strong">memcheck</strong> or the
<strong class="strong">massif</strong> tool of the valgrind suite.
</p>
<p>In case you need finer control over how valgrind is invoked, use the
<code class="code">--target-exec='valgrind &lt;your_custom_valgrind_options&gt;</code> option in
your configure line instead.
</p>
<a class="anchor" id="Maintenance"></a><a name="Maintenance-process"></a>
<h2 class="chapter">9 Maintenance process<span class="pull-right"><a class="anchor hidden-xs" href="#Maintenance-process" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Maintenance-process" aria-hidden="true">TOC</a></span></h2>

<a class="anchor" id="MAINTAINERS"></a><a name="MAINTAINERS-1"></a>
<h3 class="section">9.1 MAINTAINERS<span class="pull-right"><a class="anchor hidden-xs" href="#MAINTAINERS" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-MAINTAINERS" aria-hidden="true">TOC</a></span></h3>

<p>The developers maintaining each part of the codebase are listed in <samp class="file">MAINTAINERS</samp>.
Being listed in <samp class="file">MAINTAINERS</samp>, gives one the right to have git write access to
the specific repository.
</p>
<a class="anchor" id="Becoming-a-maintainer"></a><a name="Becoming-a-maintainer-1"></a>
<h3 class="section">9.2 Becoming a maintainer<span class="pull-right"><a class="anchor hidden-xs" href="#Becoming-a-maintainer" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Becoming-a-maintainer" aria-hidden="true">TOC</a></span></h3>

<p>People add themselves to <samp class="file">MAINTAINERS</samp> by sending a patch like any other code
change. These get reviewed by the community like any other patch. It is expected
that, if someone has an objection to a new maintainer, she is willing to object
in public with her full name and is willing to take over maintainership for the area.
</p>

<a class="anchor" id="Release-process"></a><a name="Release-process-1"></a>
<h2 class="chapter">10 Release process<span class="pull-right"><a class="anchor hidden-xs" href="#Release-process" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Release-process" aria-hidden="true">TOC</a></span></h2>

<p>FFmpeg maintains a set of <strong class="strong">release branches</strong>, which are the
recommended deliverable for system integrators and distributors (such as
Linux distributions, etc.). At regular times, a <strong class="strong">release
manager</strong> prepares, tests and publishes tarballs on the
<a class="url" href="https://ffmpeg.org">https://ffmpeg.org</a> website.
</p>
<p>There are two kinds of releases:
</p>
<ol class="enumerate">
<li> <strong class="strong">Major releases</strong> always include the latest and greatest
features and functionality.

</li><li> <strong class="strong">Point releases</strong> are cut from <strong class="strong">release</strong> branches,
which are named <code class="code">release/X</code>, with <code class="code">X</code> being the release
version number.
</li></ol>

<p>Note that we promise to our users that shared libraries from any FFmpeg
release never break programs that have been <strong class="strong">compiled</strong> against
previous versions of <strong class="strong">the same release series</strong> in any case!
</p>
<p>However, from time to time, we do make API changes that require adaptations
in applications. Such changes are only allowed in (new) major releases and
require further steps such as bumping library version numbers and/or
adjustments to the symbol versioning file. Please discuss such changes
on the <strong class="strong">ffmpeg-devel</strong> mailing list in time to allow forward planning.
</p>
<a class="anchor" id="Criteria-for-Point-Releases"></a><a name="Criteria-for-Point-Releases-1"></a>
<h3 class="section">10.1 Criteria for Point Releases<span class="pull-right"><a class="anchor hidden-xs" href="#Criteria-for-Point-Releases" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Criteria-for-Point-Releases" aria-hidden="true">TOC</a></span></h3>

<p>Changes that match the following criteria are valid candidates for
inclusion into a point release:
</p>
<ol class="enumerate">
<li> Fixes a security issue, preferably identified by a <strong class="strong">CVE
number</strong> issued by <a class="url" href="http://cve.mitre.org/">http://cve.mitre.org/</a>.

</li><li> Fixes a documented bug in <a class="url" href="https://trac.ffmpeg.org">https://trac.ffmpeg.org</a>.

</li><li> Improves the included documentation.

</li><li> Retains both source code and binary compatibility with previous
point releases of the same release branch.
</li></ol>

<p>The order for checking the rules is (1 OR 2 OR 3) AND 4.
</p>

<a name="Release-Checklist"></a>
<h3 class="section">10.2 Release Checklist<span class="pull-right"><a class="anchor hidden-xs" href="#Release-Checklist" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Release-Checklist" aria-hidden="true">TOC</a></span></h3>

<p>The release process involves the following steps:
</p>
<ol class="enumerate">
<li> Ensure that the <samp class="file">RELEASE</samp> file contains the version number for
the upcoming release.

</li><li> Add the release at <a class="url" href="https://trac.ffmpeg.org/admin/ticket/versions">https://trac.ffmpeg.org/admin/ticket/versions</a>.

</li><li> Announce the intent to do a release to the mailing list.

</li><li> Make sure all relevant security fixes have been backported. See
<a class="url" href="https://ffmpeg.org/security.html">https://ffmpeg.org/security.html</a>.

</li><li> Ensure that the FATE regression suite still passes in the release
branch on at least <strong class="strong">i386</strong> and <strong class="strong">amd64</strong>
(cf. <a class="ref" href="#Regression-tests">Regression tests</a>).

</li><li> Prepare the release tarballs in <code class="code">bz2</code> and <code class="code">gz</code> formats, and
supplementing files that contain <code class="code">gpg</code> signatures

</li><li> Publish the tarballs at <a class="url" href="https://ffmpeg.org/releases">https://ffmpeg.org/releases</a>. Create and
push an annotated tag in the form <code class="code">nX</code>, with <code class="code">X</code>
containing the version number.

</li><li> Propose and send a patch to the <strong class="strong">ffmpeg-devel</strong> mailing list
with a news entry for the website.

</li><li> Publish the news entry.

</li><li> Send an announcement to the mailing list.
</li></ol>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
