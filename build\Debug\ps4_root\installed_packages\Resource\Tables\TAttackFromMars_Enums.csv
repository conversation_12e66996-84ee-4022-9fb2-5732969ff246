RSID_TATTACKFROMMARS_START, 2975,,,
 ,[Offset], ATTACK_FLYER_1, 0,
 ,[Offset], AttackFromMars\PBTAttackFromMars, 1,
 ,[Offset], AttackFromMars\InstructionsENG, 2,
 ,[Offset], Attack<PERSON>romMars\InstructionsFR, 3,
 ,[Offset], Attack<PERSON>romMars\InstructionsITAL, 4,
 ,[Offset], AttackFromMars\InstructionsGERM, 5,
 ,[Offset], AttackFromMars\InstructionsSPAN, 6,
 ,[Offset], AttackFromMars\InstructionsENG, 7,
 ,[Offset], AttackFromMars\InstructionsENG, 8,
 ,[Offset], tables\Tales_BG_scroll, 9,
 ,[Offset], AttackFromMars\Pro_TipsENG, 10,
 ,[Offset], AttackFromMars\Pro_TipsFR, 11,
 ,[Offset], AttackFromMars\Pro_TipsITAL, 12,
 ,[Offset], Attack<PERSON>romMars\Pro_TipsGERM, 13,
 ,[Offset], Attack<PERSON>romMars\Pro_TipsSPAN, 14,
 ,[Offset], Attack<PERSON>romMars\Pro_TipsENG, 15,
 ,[Offset], Attack<PERSON>romMars\Pro_TipsENG, 16,
RSID_TATTACKFROMMARS_LIGHTS, 2976,,,
RSID_TATTACKFROMMARS_CAMERAS, 2977,,,
RSID_TATTACKFROMMARS_LAMP_TEXTURES, 2978,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_86_On, 124,
 ,[Offset], L_86_On, 125,
 ,[Offset], L_88_Off, 126,
 ,[Offset], L_88_On, 127,
 ,[Offset], F_21_Off, 128,
 ,[Offset], F_21_On, 129,
 ,[Offset], F_22_Off, 130,
 ,[Offset], F_22_On, 131,
 ,[Offset], F_17_Off, 132,
 ,[Offset], F_17_On, 133,
 ,[Offset], F_18_Off, 134,
 ,[Offset], F_18_On, 135,
 ,[Offset], F_19_Off, 136,
 ,[Offset], F_19_On, 137,
 ,[Offset], F_20_Off, 138,
 ,[Offset], F_20_On, 139,
 ,[Offset], F_23_Off, 140,
 ,[Offset], F_23_On, 141,
 ,[Offset], F_25_Off, 142,
 ,[Offset], F_25_On, 143,
 ,[Offset], F_26_Off, 144,
 ,[Offset], F_26_On, 145,
 ,[Offset], F_27_Off, 146,
 ,[Offset], F_27_On, 147,
 ,[Offset], F_28_Off, 148,
 ,[Offset], F_28_On, 149,
 ,[Offset], LED_01_Off, 150,
 ,[Offset], LED_01_On, 151,
 ,[Offset], LED_02_Off, 152,
 ,[Offset], LED_02_On, 153,
 ,[Offset], LED_03_Off, 154,
 ,[Offset], LED_03_On, 155,
 ,[Offset], LED_04_Off, 156,
 ,[Offset], LED_04_On, 157,
 ,[Offset], LED_05_Off, 158,
 ,[Offset], LED_05_On, 159,
 ,[Offset], LED_06_Off, 160,
 ,[Offset], LED_06_On, 161,
 ,[Offset], LED_07_Off, 162,
 ,[Offset], LED_07_On, 163,
 ,[Offset], LED_08_Off, 164,
 ,[Offset], LED_08_On, 165,
 ,[Offset], LED_09_Off, 166,
 ,[Offset], LED_09_On, 167,
 ,[Offset], LED_10_Off, 168,
 ,[Offset], LED_10_On, 169,
 ,[Offset], LED_11_Off, 170,
 ,[Offset], LED_11_On, 171,
 ,[Offset], LED_12_Off, 172,
 ,[Offset], LED_12_On, 173,
 ,[Offset], LED_13_Off, 174,
 ,[Offset], LED_13_On, 175,
 ,[Offset], LED_14_Off, 176,
 ,[Offset], LED_14_On, 177,
 ,[Offset], LED_15_Off, 178,
 ,[Offset], LED_15_On, 179,
 ,[Offset], LED_16_Off, 180,
 ,[Offset], LED_16_On, 181,
RSID_TATTACKFROMMARS_TEXTURES, 2979,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], AlienGuy_t_c, 1,
 ,[Offset], AM_Lower_Playfield, 2,
 ,[Offset], AM_Upper_Playfield, 3,
 ,[Offset], Apron, 4,
 ,[Offset], Black_posts, 5,
 ,[Offset], bulb1, 6,
 ,[Offset], Bumper_Hamer, 7,
 ,[Offset], Bumper_Sensors, 8,
 ,[Offset], clear_plasticpiece, 9,
 ,[Offset], clear_ramp, 10,
 ,[Offset], clearplastic_t_c, 11,
 ,[Offset], dark_Metals, 12,
 ,[Offset], flipper, 13,
 ,[Offset], Generic_Metal, 14,
 ,[Offset], HarleyBumperBody, 15,
 ,[Offset], large_ramp, 16,
 ,[Offset], mainship_t_c, 17,
 ,[Offset], Mars_Habit1, 18,
 ,[Offset], Mars_Habit2, 19,
 ,[Offset], metal, 20,
 ,[Offset], metal_temp, 21,
 ,[Offset], metal_walls, 22,
 ,[Offset], metalpiece_t_c, 23,
 ,[Offset], plastics02_t_c, 24,
 ,[Offset], plastics_t_c, 25,
 ,[Offset], ramp_metal, 26,
 ,[Offset], red_light_off, 27,
 ,[Offset], red_plastics, 28,
 ,[Offset], Red_target, 29,
 ,[Offset], round_metal, 30,
 ,[Offset], Rubber Post_Temp, 31,
 ,[Offset], rubberband_temp, 32,
 ,[Offset], silver metal screws_temp, 33,
 ,[Offset], slingshotcover_t_c, 34,
 ,[Offset], small_ramp, 35,
 ,[Offset], small_ufo, 36,
 ,[Offset], stickers, 37,
 ,[Offset], target_white, 38,
 ,[Offset], Rails, 39,
 ,[Offset], Red_Plastic_Post, 40,
 ,[Offset], ClearPlasticPost_01, 41,
 ,[Offset], Plastic_Ramp_02, 42,
 ,[Offset], backglass, 43,
 ,[Offset], black_metal, 44,
 ,[Offset], black_wood, 45,
 ,[Offset], cabinet_front, 46,
 ,[Offset], cabinet_sides, 47,
 ,[Offset], CoinSlots, 48,
 ,[Offset], Extra_metal_Parts, 49,
 ,[Offset], LaunchBall_Button, 50,
 ,[Offset], metal front, 51,
 ,[Offset], Metal_Parts, 52,
 ,[Offset], Speaker, 53,
 ,[Offset], Red_Bumper, 54,
 ,[Offset], PopBumperBody, 55,
 ,[Offset], Harley_Spinner, 56,
 ,[Offset], Harley_Gate, 57,
 ,[Offset], Interior_Walls, 58,
RSID_TATTACKFROMMARS_MODELS, 2980,,,
 ,[Offset], Apron, 0,
 ,[Offset], Alien, 1,
 ,[Offset], Drop_Target, 2,
 ,[Offset], Flipper_Left, 3,
 ,[Offset], Flipper_Plastics, 4,
 ,[Offset], Habit_Left, 5,
 ,[Offset], Habit_Right, 6,
 ,[Offset], Main_UFO, 7,
 ,[Offset], Metal_Pieces, 8,
 ,[Offset], Metal_Posts, 9,
 ,[Offset], Metal_Walls, 10,
 ,[Offset], Plastic_Pieces, 11,
 ,[Offset], Plastic_Posts, 12,
 ,[Offset], Playfield, 13,
 ,[Offset], Ramp_BAck, 14,
 ,[Offset], Ramp_Left, 15,
 ,[Offset], Ramp_Right, 16,
 ,[Offset], Ramp_Stickers, 17,
 ,[Offset], Red_Plastic_Posts, 18,
 ,[Offset], Rubber_Posts, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Right, 21,
 ,[Offset], Target, 22,
 ,[Offset], Target_Bracket, 23,
 ,[Offset], Target_Red, 24,
 ,[Offset], UFO_Left, 25,
 ,[Offset], UFO_Right, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Wooden_Rails, 28,
 ,[Offset], Backglass, 29,
 ,[Offset], Cabinet, 30,
 ,[Offset], Cabinet_Interior, 31,
 ,[Offset], Cabinet_Metals, 32,
 ,[Offset], Cabinet_Buttons, 33,
 ,[Offset], Diverter, 34,
 ,[Offset], Bumper, 35,
 ,[Offset], Pop_Bumpers, 36,
 ,[Offset], Light_Cutouts, 37,
 ,[Offset], Slingshot_Plastics, 38,
 ,[Offset], Gate, 39,
 ,[Offset], Ramp_Wire, 40,
 ,[Offset], Alien_01, 41,
 ,[Offset], Alien_02, 42,
 ,[Offset], Alien_03, 43,
 ,[Offset], Alien_04, 44,
 ,[Offset], Alien_05, 45,
 ,[Offset], Alien_06, 46,
 ,[Offset], Alien_07, 47,
 ,[Offset], Alien_3B, 48,
 ,[Offset], Alien_4B, 49,
 ,[Offset], Bulbs, 50,
 ,[Offset], Bulbs, 51,
 ,[Offset], Bulbs, 52,
 ,[Offset], Led_Bulbs, 53,
RSID_TATTACKFROMMARS_MODELS_LODS, 2981,,,
 ,[Offset], Apron, 0,
 ,[Offset], Alien, 1,
 ,[Offset], Drop_Target, 2,
 ,[Offset], Flipper_Left, 3,
 ,[Offset], Flipper_Plastics, 4,
 ,[Offset], Habit_Left, 5,
 ,[Offset], Habit_Right, 6,
 ,[Offset], Main_UFO, 7,
 ,[Offset], Metal_Pieces, 8,
 ,[Offset], Metal_Posts, 9,
 ,[Offset], Metal_Walls, 10,
 ,[Offset], Plastic_Pieces, 11,
 ,[Offset], Plastic_Posts, 12,
 ,[Offset], Playfield, 13,
 ,[Offset], Ramp_BAck, 14,
 ,[Offset], Ramp_Left, 15,
 ,[Offset], Ramp_Right, 16,
 ,[Offset], Ramp_Stickers, 17,
 ,[Offset], Red_Plastic_Posts, 18,
 ,[Offset], Rubber_Posts, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Right, 21,
 ,[Offset], Target, 22,
 ,[Offset], Target_Bracket, 23,
 ,[Offset], Target_Red, 24,
 ,[Offset], UFO_Left, 25,
 ,[Offset], UFO_Right, 26,
 ,[Offset], Wire, 27,
 ,[Offset], Wooden_Rails, 28,
 ,[Offset], Backglass, 29,
 ,[Offset], Cabinet, 30,
 ,[Offset], Cabinet_Interior, 31,
 ,[Offset], Cabinet_Metals, 32,
 ,[Offset], Cabinet_Buttons, 33,
 ,[Offset], Diverter, 34,
 ,[Offset], Bumper, 35,
 ,[Offset], Pop_Bumpers, 36,
 ,[Offset], Light_Cutouts, 37,
 ,[Offset], Slingshot_Plastics, 38,
 ,[Offset], Gate, 39,
 ,[Offset], Ramp_Wire, 40,
 ,[Offset], Alien_01, 41,
 ,[Offset], Alien_02, 42,
 ,[Offset], Alien_03, 43,
 ,[Offset], Alien_04, 44,
 ,[Offset], Alien_05, 45,
 ,[Offset], Alien_06, 46,
 ,[Offset], Alien_07, 47,
 ,[Offset], Alien_3B, 48,
 ,[Offset], Alien_4B, 49,
 ,[Offset], Bulbs, 50,
 ,[Offset], Flasher_Planes_A, 51,
 ,[Offset], Flasher_Planes_B, 52,
 ,[Offset], Led_Bulbs, 53,
RSID_TATTACKFROMMARS_COLLISION, 2982,,,
 ,[Offset], Apron, 0,
 ,[Offset], Ball_Drain, 1,
 ,[Offset], Flipper_Lane_Left, 2,
 ,[Offset], Flipper_Lane_Right, 3,
 ,[Offset], Habit_Left, 4,
 ,[Offset], Habit_Right, 5,
 ,[Offset], Left_Wall, 6,
 ,[Offset], Mid_Wall, 7,
 ,[Offset], Outer_Wall, 8,
 ,[Offset], Playfield, 9,
 ,[Offset], Plunger_Lane, 10,
 ,[Offset], Plunger_Moving, 11,
 ,[Offset], Plunger_Ramp, 12,
 ,[Offset], Plunger_Rest, 13,
 ,[Offset], Ramp_Back, 14,
 ,[Offset], Ramp_Left, 15,
 ,[Offset], Ramp_Right, 16,
 ,[Offset], Right_Wall, 17,
 ,[Offset], Rubber_A, 18,
 ,[Offset], Rubber_B, 19,
 ,[Offset], Rubber_C, 20,
 ,[Offset], Rubber_D, 21,
 ,[Offset], Rubber_E, 22,
 ,[Offset], Rubber_F, 23,
 ,[Offset], Slingshot_Left, 24,
 ,[Offset], Slingshot_Right, 25,
 ,[Offset], Diverter, 26,
 ,[Offset], Flipper_Left_Back, 27,
 ,[Offset], Flipper_Left_Front, 28,
 ,[Offset], Flipper_Right_Back, 29,
 ,[Offset], Flipper_Right_Front, 30,
 ,[Offset], Scoop_Trap, 31,
 ,[Offset], Slingshot_Left_Front, 32,
 ,[Offset], Slingshot_Right_Front, 33,
 ,[Offset], Target, 34,
 ,[Offset], Target_Red, 35,
 ,[Offset], Trough, 36,
 ,[Offset], Tunnel_Trap, 37,
 ,[Offset], Bumper, 38,
 ,[Offset], Scoop, 39,
 ,[Offset], Gate, 40,
 ,[Offset], OneWay_Gate_Back, 41,
 ,[Offset], OneWay_Gate_Front, 42,
 ,[Offset], dirty_pool, 43,
RSID_TATTACKFROMMARS_PLACEMENT, 2983,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TATTACKFROMMARS_EMUROM, 2984,,,
 ,[Offset], afm_10, 0,
 ,[Offset], afm_10, 1,
 ,[Offset], afm_10, 2,
 ,[Offset], afm_11, 3,
 ,[Offset], afm_11, 4,
 ,[Offset], afm_11, 5,
 ,[Offset], afm_default, 6,
 ,[Offset], afm_default, 7,
RSID_TATTACKFROMMARS_SOUNDS_START, 2985,,,
RSID_TATTACKFROMMARS_EMU_SOUNDS, 2986,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP1, 1,
 ,[Offset], S0003-LP, 2,
 ,[Offset], S0004-LP1, 3,
 ,[Offset], S0005-LP1, 4,
 ,[Offset], S0005-LP2, 5,
 ,[Offset], S0006-LP1, 6,
 ,[Offset], S0006-LP2, 7,
 ,[Offset], S0007-LP, 8,
 ,[Offset], S0008-LP, 9,
 ,[Offset], S0009-LP1, 10,
 ,[Offset], S000A-LP, 11,
 ,[Offset], S000B-LP1, 12,
 ,[Offset], S000B-LP2, 13,
 ,[Offset], S000C-LP1, 14,
 ,[Offset], S000D-LP1, 15,
 ,[Offset], S000D-LP2, 16,
 ,[Offset], S000E-LP1, 17,
 ,[Offset], S000E-LP2, 18,
 ,[Offset], S000F-LP, 19,
 ,[Offset], S0010-LP, 20,
 ,[Offset], S0011-LP, 21,
 ,[Offset], S0012-LP, 22,
 ,[Offset], S0014-LP, 23,
 ,[Offset], S0064_C4, 24,
 ,[Offset], S0068_C4, 25,
 ,[Offset], S006A_C4, 26,
 ,[Offset], S006C_C4, 27,
 ,[Offset], S006E_C4, 28,
 ,[Offset], S0070_C4, 29,
 ,[Offset], S0072_C4, 30,
 ,[Offset], S0074_C4, 31,
 ,[Offset], S0076_C4, 32,
 ,[Offset], S0078_C4, 33,
 ,[Offset], S007A_C4, 34,
 ,[Offset], S007C_C4, 35,
 ,[Offset], S007E_C4, 36,
 ,[Offset], S0080_C4, 37,
 ,[Offset], S0082_C4, 38,
 ,[Offset], S0084_C4, 39,
 ,[Offset], S0088_C4, 40,
 ,[Offset], S008A_C4, 41,
 ,[Offset], S008C_C4, 42,
 ,[Offset], S008E_C4, 43,
 ,[Offset], S0090_C4, 44,
 ,[Offset], S0092_C4, 45,
 ,[Offset], S0094_C4, 46,
 ,[Offset], S0098_C4, 47,
 ,[Offset], S009A_C4, 48,
 ,[Offset], S009C_C4, 49,
 ,[Offset], S009E_C4, 50,
 ,[Offset], S00A0_C4, 51,
 ,[Offset], S00A2_C4, 52,
 ,[Offset], S00A4_C4, 53,
 ,[Offset], S00AE_C4, 54,
 ,[Offset], S00B2_C4, 55,
 ,[Offset], S00B4_C4, 56,
 ,[Offset], S00B8_C4, 57,
 ,[Offset], S00BA_C4, 58,
 ,[Offset], S00C0_C4, 59,
 ,[Offset], S00C4_C4, 60,
 ,[Offset], S00C8_C4, 61,
 ,[Offset], S00CC_C4, 62,
 ,[Offset], S00D0_C4, 63,
 ,[Offset], S00D2_C4, 64,
 ,[Offset], S00D6_C4, 65,
 ,[Offset], S00E4_C4, 66,
 ,[Offset], S00E6_C4, 67,
 ,[Offset], S00E8_C4, 68,
 ,[Offset], S00EA_C4, 69,
 ,[Offset], S00EC_C4, 70,
 ,[Offset], S00F0_C4, 71,
 ,[Offset], S00F2_C4, 72,
 ,[Offset], S00F4_C4, 73,
 ,[Offset], S00F6_C4, 74,
 ,[Offset], S00FC_C4, 75,
 ,[Offset], S00FE_C4, 76,
 ,[Offset], S0100_C4, 77,
 ,[Offset], S0102_C4, 78,
 ,[Offset], S0104_C4, 79,
 ,[Offset], S010C_C4, 80,
 ,[Offset], S010E_C4, 81,
 ,[Offset], S0110_C4, 82,
 ,[Offset], S0112_C4, 83,
 ,[Offset], S0114_C4, 84,
 ,[Offset], S0116_C4, 85,
 ,[Offset], S0118_C4, 86,
 ,[Offset], S011C_C4, 87,
 ,[Offset], S011E_C4, 88,
 ,[Offset], S0120_C4, 89,
 ,[Offset], S0122_C4, 90,
 ,[Offset], S0124_C4, 91,
 ,[Offset], S0126_C4, 92,
 ,[Offset], S0128_C4, 93,
 ,[Offset], S012A_C4, 94,
 ,[Offset], S012C_C4, 95,
 ,[Offset], S012E_C4, 96,
 ,[Offset], S0130_C4, 97,
 ,[Offset], S0132_C4, 98,
 ,[Offset], S0134_C4, 99,
 ,[Offset], S0136_C4, 100,
 ,[Offset], S0138_C4, 101,
 ,[Offset], S013A_C4, 102,
 ,[Offset], S013C_C4, 103,
 ,[Offset], S013E_C4, 104,
 ,[Offset], S0142_C4, 105,
 ,[Offset], S0146_C4, 106,
 ,[Offset], S0148_C4, 107,
 ,[Offset], S014A_C4, 108,
 ,[Offset], S014E_C4, 109,
 ,[Offset], S0150_C4, 110,
 ,[Offset], S0152_C4, 111,
 ,[Offset], S0154_C4, 112,
 ,[Offset], S0156_C4, 113,
 ,[Offset], S0158_C4, 114,
 ,[Offset], S015A_C4, 115,
 ,[Offset], S015C_C4, 116,
 ,[Offset], S015E_C4, 117,
 ,[Offset], S0160_C4, 118,
 ,[Offset], S0162_C4, 119,
 ,[Offset], S0164_C4, 120,
 ,[Offset], S0166_C4, 121,
 ,[Offset], S0168_C4, 122,
 ,[Offset], S0176_C4, 123,
 ,[Offset], S0178_C4, 124,
 ,[Offset], S017A_C4, 125,
 ,[Offset], S017C_C4, 126,
 ,[Offset], S017E_C4, 127,
 ,[Offset], S0180_C4, 128,
 ,[Offset], S0186_C4, 129,
 ,[Offset], S0192_C6, 130,
 ,[Offset], S0193_C6, 131,
 ,[Offset], S0194_C6, 132,
 ,[Offset], S0196_C6, 133,
 ,[Offset], S0197_C6, 134,
 ,[Offset], S019B_C6, 135,
 ,[Offset], S019C_C6, 136,
 ,[Offset], S019D_C6, 137,
 ,[Offset], S019E_C6, 138,
 ,[Offset], S019F_C6, 139,
 ,[Offset], S01A0_C6, 140,
 ,[Offset], S01A1_C6, 141,
 ,[Offset], S01A5_C6, 142,
 ,[Offset], S01A6_C6, 143,
 ,[Offset], S01A7_C6, 144,
 ,[Offset], S01A8_C6, 145,
 ,[Offset], S01A9_C6, 146,
 ,[Offset], S01AB_C6, 147,
 ,[Offset], S01AE_C6, 148,
 ,[Offset], S01B1_C6, 149,
 ,[Offset], S01B2_C6, 150,
 ,[Offset], S01B3_C6, 151,
 ,[Offset], S01B4_C6, 152,
 ,[Offset], S01B5_C6, 153,
 ,[Offset], S01B6_C6, 154,
 ,[Offset], S01B8_C6, 155,
 ,[Offset], S01BA_C6, 156,
 ,[Offset], S01BC_C6, 157,
 ,[Offset], S01BD_C6, 158,
 ,[Offset], S01BE_C6, 159,
 ,[Offset], S01BF_C6, 160,
 ,[Offset], S01C0_C6, 161,
 ,[Offset], S01F9_C6, 162,
 ,[Offset], S01FB_C6, 163,
 ,[Offset], S0214_C6, 164,
 ,[Offset], S0215_C6, 165,
 ,[Offset], S0216_C6, 166,
 ,[Offset], S0217_C6, 167,
 ,[Offset], S0219_C6, 168,
 ,[Offset], S021A_C6, 169,
 ,[Offset], S021C_C6, 170,
 ,[Offset], S021D_C6, 171,
 ,[Offset], S021E_C6, 172,
 ,[Offset], S021F_C6, 173,
 ,[Offset], S0220_C6, 174,
 ,[Offset], S0222_C6, 175,
 ,[Offset], S0223_C6, 176,
 ,[Offset], S0224_C6, 177,
 ,[Offset], S0225_C6, 178,
 ,[Offset], S0226_C6, 179,
 ,[Offset], S0227_C6, 180,
 ,[Offset], S0228_C6, 181,
 ,[Offset], S0229_C6, 182,
 ,[Offset], S022A_C6, 183,
 ,[Offset], S022B_C6, 184,
 ,[Offset], S022D_C6, 185,
 ,[Offset], S022F_C6, 186,
 ,[Offset], S0230_C6, 187,
 ,[Offset], S0231_C6, 188,
 ,[Offset], S0232_C6, 189,
 ,[Offset], S0233_C6, 190,
 ,[Offset], S0234_C6, 191,
 ,[Offset], S0237_C6, 192,
 ,[Offset], S0238_C6, 193,
 ,[Offset], S0239_C6, 194,
 ,[Offset], S023A_C6, 195,
 ,[Offset], S023B_C6, 196,
 ,[Offset], S023C_C6, 197,
 ,[Offset], S0242_C6, 198,
 ,[Offset], S0243_C6, 199,
 ,[Offset], S0244_C6, 200,
 ,[Offset], S0245_C6, 201,
 ,[Offset], S0246_C6, 202,
 ,[Offset], S0247_C6, 203,
 ,[Offset], S0248_C6, 204,
 ,[Offset], S0249_C6, 205,
 ,[Offset], S024A_C6, 206,
 ,[Offset], S024B_C6, 207,
 ,[Offset], S024C_C6, 208,
 ,[Offset], S024D_C6, 209,
 ,[Offset], S024E_C6, 210,
 ,[Offset], S024F_C6, 211,
 ,[Offset], S0250_C6, 212,
 ,[Offset], S0251_C6, 213,
 ,[Offset], S0258_C6, 214,
 ,[Offset], S0259_C6, 215,
 ,[Offset], S025A_C6, 216,
 ,[Offset], S025B_C6, 217,
 ,[Offset], S025C_C6, 218,
 ,[Offset], S025D_C6, 219,
 ,[Offset], S025E_C6, 220,
 ,[Offset], S025F_C6, 221,
 ,[Offset], S0260_C6, 222,
 ,[Offset], S0261_C6, 223,
 ,[Offset], S0262_C6, 224,
 ,[Offset], S0263_C6, 225,
 ,[Offset], S0269_C6, 226,
 ,[Offset], S026A_C6, 227,
 ,[Offset], S026B_C6, 228,
 ,[Offset], S026D_C6, 229,
 ,[Offset], S0271_C6, 230,
 ,[Offset], S0272_C6, 231,
 ,[Offset], S0273_C6, 232,
 ,[Offset], S0274_C6, 233,
 ,[Offset], S0276_C6, 234,
 ,[Offset], S0277_C6, 235,
 ,[Offset], S0278_C6, 236,
 ,[Offset], S0279_C6, 237,
 ,[Offset], S027A_C6, 238,
 ,[Offset], S02BC_C6, 239,
 ,[Offset], S02BD_C6, 240,
 ,[Offset], S02BE_C6, 241,
 ,[Offset], S02BF_C6, 242,
 ,[Offset], S02C0_C6, 243,
 ,[Offset], S02C1_C6, 244,
 ,[Offset], S02C2_C6, 245,
 ,[Offset], S02C3_C6, 246,
 ,[Offset], S02C6_C6, 247,
 ,[Offset], S02C7_C6, 248,
 ,[Offset], S02CA_C6, 249,
 ,[Offset], S02CB_C6, 250,
 ,[Offset], S02CC_C6, 251,
 ,[Offset], S02CD_C6, 252,
 ,[Offset], S02CE_C6, 253,
 ,[Offset], S02CF_C6, 254,
 ,[Offset], S02D0_C6, 255,
 ,[Offset], S02D1_C6, 256,
 ,[Offset], S02D2_C6, 257,
 ,[Offset], S02D3_C6, 258,
 ,[Offset], S02D4_C6, 259,
 ,[Offset], S02D5_C6, 260,
 ,[Offset], S02D6_C6, 261,
 ,[Offset], S02D7_C6, 262,
 ,[Offset], S02D8_C6, 263,
 ,[Offset], S02D9_C6, 264,
 ,[Offset], S02DA_C6, 265,
 ,[Offset], S02DB_C6, 266,
 ,[Offset], S02DC_C6, 267,
 ,[Offset], S02DD_C6, 268,
 ,[Offset], S02DE_C6, 269,
 ,[Offset], S02DF_C6, 270,
 ,[Offset], S02E5_C6, 271,
 ,[Offset], S02E6_C6, 272,
 ,[Offset], S02EA_C6, 273,
 ,[Offset], S02EB_C6, 274,
 ,[Offset], S02ED_C6, 275,
 ,[Offset], S02F9_C6, 276,
 ,[Offset], S02FA_C6, 277,
 ,[Offset], S02FB_C6, 278,
 ,[Offset], S02FC_C6, 279,
 ,[Offset], S02FF_C6, 280,
 ,[Offset], S0304_C6, 281,
 ,[Offset], S0307_C6, 282,
 ,[Offset], S0308_C6, 283,
 ,[Offset], S0309_C6, 284,
 ,[Offset], S030D_C6, 285,
 ,[Offset], S030F_C6, 286,
 ,[Offset], S0311_C6, 287,
 ,[Offset], S0312_C6, 288,
 ,[Offset], S0313_C6, 289,
 ,[Offset], S0314_C6, 290,
 ,[Offset], S0315_C6, 291,
 ,[Offset], S0316_C6, 292,
 ,[Offset], S0317_C6, 293,
 ,[Offset], S0318_C6, 294,
 ,[Offset], S0319_C6, 295,
 ,[Offset], S031A_C6, 296,
 ,[Offset], S031B_C6, 297,
 ,[Offset], S03BE_C1, 298,
 ,[Offset], S03C0_C1, 299,
 ,[Offset], S03D4_C-1, 300,
 ,[Offset], S03D5_C-1, 301,
 ,[Offset], S03D6_C5, 302,
 ,[Offset], S03D7_C5, 303,
 ,[Offset], S03D8_C5, 304,
 ,[Offset], S03D9_C-1, 305,
 ,[Offset], S03DA_C-1, 306,
 ,[Offset], S03DB_C5, 307,
 ,[Offset], S03DC_C5, 308,
 ,[Offset], S03DD_C5, 309,
RSID_TATTACKFROMMARS_MECH_SOUNDS, 2987,,,
 ,[Offset], destroy_saucer, 0,
 ,[Offset], fall_into_scoop, 1,
 ,[Offset], left_upkick, 2,
 ,[Offset], plunge, 3,
 ,[Offset], saucer_gate_up_down, 4,
 ,[Offset], scoop_eject, 5,
 ,[Offset], martian_attack_1, 6,
 ,[Offset], martian_attack_2, 7,
 ,[Offset], martian_attack_3, 8,
 ,[Offset], martian_attack, 9,
RSID_TATTACKFROMMARS_SOUNDS_END, 2988,,,
RSID_TATTACKFROMMARS_SAMPLES, 2989,,,
RSID_TATTACKFROMMARS_END, 2990,,,
