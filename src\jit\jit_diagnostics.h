// Copyright 2025 <Copyright Owner>

#pragma once

#include <istream> // for LoadState
#include <mutex>
#include <ostream> // for SaveState
#include <string>
#include <unordered_map>

#include "jit/x86_64_jit_compiler.h"

namespace x86_64 {
class JITDiagnostics {
public:
  /**
   * @brief Retrieves the singleton instance of JITDiagnostics.
   * @return Reference to the singleton instance.
   */
  static JITDiagnostics &GetInstance();

  /**
   * @brief Updates JIT-related diagnostic metrics.
   * @details Collects metrics from the JIT compiler, including cache usage and
   * hot blocks.
   */
  void UpdateMetrics();

  /**
   * @brief Updates JIT-related diagnostic metrics with provided data.
   * @details Collects metrics from provided JIT compilers to break circular dependency.
   * @param jitCompilers Vector of JIT compiler pointers to collect metrics from
   */
  void UpdateMetricsWithData(const std::vector<class X86_64JITCompiler*>& jitCompilers);

  /**
   * @brief Resets all diagnostic metrics.
   */
  void ResetMetrics();

  /**
   * @brief Retrieves the current diagnostic metrics.
   * @return Map of metric names to values.
   */
  const std::unordered_map<std::string, uint64_t> &GetMetrics() const;

  /**
   * @brief Logs detailed diagnostic information.
   */
  void LogDiagnostics();

  /**
   * @brief Saves the current state of diagnostics to the output stream.
   * @param out The output stream to save the state to.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the diagnostics state from the input stream.
   * @param in The input stream to load the state from.
   */
  void LoadState(std::istream &in);

private:
  JITDiagnostics();
  mutable std::mutex m_mutex;
  std::unordered_map<std::string, uint64_t> m_metrics;
};
} // namespace x86_64