<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_BARRIER_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_barrier_destroy, pthread_barrier_init - destroy and
initialize a barrier object (<B>ADVANCED REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_barrier_destroy(pthread_barrier_t *</B><I>barrier</I><B>);
<BR>int pthread_barrier_init(pthread_barrier_t *restrict</B> <I>barrier</I><B>,
const pthread_barrierattr_t *restrict</B> <I>attr</I><B>, unsigned</B>
<I>count</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_barrier_destroy</B> function shall destroy the
barrier referenced by <I>barrier</I> and release any resources used
by the barrier. The effect of subsequent use of the barrier is
undefined until the barrier is reinitialized by another call to
<B>pthread_barrier_init</B> . An implementation may use this function
to set <I>barrier</I> to an invalid value. An error code is returned if <B>pthread_barrier_destroy</B> is called when any thread is
blocked on the barrier, or if this function is called with an
uninitialized barrier. 
</P>
<P>The <B>pthread_barrier_init</B> function shall allocate any
resources required to use the barrier referenced by <I>barrier</I>
and shall initialize the barrier with attributes referenced by <I>attr</I>.
If <I>attr</I> is NULL, the default barrier attributes shall be used;
the effect is the same as passing the address of a default barrier
attributes object. The results are undefined if <B>pthread_barrier_init</B>
is called when any thread is blocked on the barrier (that is, has not
returned from the <A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
call). The results are undefined if a barrier is used without first
being initialized. The results are undefined if <B>pthread_barrier_init</B>
is called specifying an already initialized barrier. 
</P>
<P>The <I>count</I> argument specifies the number of threads that
must call <A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
before any of them successfully return from the call. The value
specified by <I>count</I> must be greater than zero. 
</P>
<P>If the <B>pthread_barrier_init</B> function fails, the barrier
shall not be initialized and the contents of <I>barrier</I> are
undefined. 
</P>
<P>Only the object referenced by <I>barrier</I> may be used for
performing synchronization. The result of referring to copies of that
object in calls to <B>pthread_barrier_destroy</B> <B>or</B>
<A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
is undefined.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, these functions shall return zero;
otherwise, an error number shall be returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_barrier_destroy</B> function may fail if: 
</P>
<DL>
	<DT><B>EBUSY</B> 
	</DT><DD>
	The implementation has detected an attempt to destroy a barrier
	while it is in use (for example, while being used in a
	<A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
	call) by another thread. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>barrier</I> is invalid. 
	</DD></DL>
<P>
The <B>pthread_barrier_init</B> function shall fail if: 
</P>
<DL>
	<DT><B>EAGAIN</B> 
	</DT><DD>
	The system lacks the necessary resources to initialize another
	barrier. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD>
	The value specified by <I>count</I> is equal to zero. 
	</DD><DT>
	<B>ENOMEM</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	Insufficient memory exists to initialize the barrier. 
	</DD></DL>
<P>
The <B>pthread_barrier_init</B> function may fail if: 
</P>
<DL>
	<DT><B>EBUSY</B> 
	</DT><DD>
	The implementation has detected an attempt to reinitialize a barrier
	while it is in use (for example, while being used in a
	<A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
	call) by another thread. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>attr</I> is invalid. 
	</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>The <B>pthread_barrier_destroy</B> and <B>pthread_barrier_init</B>
functions are part of the Barriers option and need not be provided on
all implementations. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_BARRIERS</B> to indicate
that these routines are implemented and may be used.</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc11" NAME="sect11">Known Bugs</A></H2>
<DL>
	<DD STYLE="margin-left: 0cm; margin-bottom: 0.5cm">In
	<B><SPAN LANG="en-GB"><SPAN LANG="en-GB">PThreads4W</SPAN></SPAN></B>,
	the behaviour of threads which enter <A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
	while the barrier is being destroyed is undefined. 
	</DD></DL>
<H2>
<A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_barrier_wait.html"><B>pthread_barrier_wait</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect11" NAME="toc11">Known
	Bugs</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>