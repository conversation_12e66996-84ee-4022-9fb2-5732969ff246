// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <fstream>
#include <istream>
#include <ostream>
#include <stdexcept>
#include <string>
#include <vector>

namespace ps4 {

/**
 * @brief GCN shader types supported by the emulator.
 * @details Defines shader types for GCN shader processing, used by
 *          GNMShaderTranslator for translation to SPIR-V or GLSL.
 */
enum class GCNShaderType {
  VERTEX,       ///< Vertex shader
  PIXEL,        ///< Pixel (fragment) shader
  GEOMETRY,     ///< Geometry shader
  COMPUTE,      ///< Compute shader
  HULL,         ///< Hull (tessellation control) shader
  DOMAIN_SHADER ///< Domain (tessellation evaluation) shader
};

/**
 * @brief GCN instruction structure for shader processing.
 * @details Represents a single GCN instruction with opcode, source/destination
 *          registers, immediate value, and predication. Includes metrics for
 *          tracking processing efficiency and supports serialization for state
 *          persistence in GNMShaderTranslator.
 */
struct GCNInstruction {
  uint32_t opcode = 0;      ///< Instruction opcode
  uint32_t dst = 0;         ///< Destination register
  uint32_t src0 = 0;        ///< Source register 0
  uint32_t src1 = 0;        ///< Source register 1
  uint32_t src2 = 0;        ///< Source register 2
  uint32_t imm = 0;         ///< Immediate value
  bool predicated = false;  ///< Is instruction predicated?
  uint8_t predicate = 0;    ///< Predicate register
  uint64_t cacheHits = 0;   ///< Cache hits for this instruction
  uint64_t cacheMisses = 0; ///< Cache misses for this instruction
  uint64_t usageCount = 0;  ///< Number of times processed

  /**
   * @brief Checks if the instruction is valid.
   * @return True if opcode and registers are within valid ranges, false
   * otherwise.
   * @details Validates opcode (0x00-0xFF) and register indices (0-255).
   */
  bool IsValid() const {
    return opcode <= 0xFF && dst <= 0xFF && src0 <= 0xFF && src1 <= 0xFF &&
           src2 <= 0xFF && predicate <= 0xFF;
  }

  /**
   * @brief Validates the instruction and throws an exception if invalid.
   * @throws std::runtime_error if the instruction is invalid.
   * @details Checks opcode and register ranges, throwing detailed error
   * messages.
   */
  void Validate() const {
    if (opcode > 0xFF) {
      throw std::runtime_error("Invalid GCN instruction opcode: " +
                               std::to_string(opcode));
    }
    if (dst > 0xFF) {
      throw std::runtime_error(
          "Invalid GCN instruction destination register: " +
          std::to_string(dst));
    }
    if (src0 > 0xFF) {
      throw std::runtime_error("Invalid GCN instruction source register 0: " +
                               std::to_string(src0));
    }
    if (src1 > 0xFF) {
      throw std::runtime_error("Invalid GCN instruction source register 1: " +
                               std::to_string(src1));
    }
    if (src2 > 0xFF) {
      throw std::runtime_error("Invalid GCN instruction source register 2: " +
                               std::to_string(src2));
    }
    if (predicate > 0xFF) {
      throw std::runtime_error("Invalid GCN instruction predicate register: " +
                               std::to_string(predicate));
    }
  }

  /**
   * @brief Serializes the instruction to a stream.
   * @param out Output stream.
   * @details Writes opcode, registers, immediate, predication, and metrics.
   */
  void Serialize(std::ostream &out) const {
    out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
    out.write(reinterpret_cast<const char *>(&dst), sizeof(dst));
    out.write(reinterpret_cast<const char *>(&src0), sizeof(src0));
    out.write(reinterpret_cast<const char *>(&src1), sizeof(src1));
    out.write(reinterpret_cast<const char *>(&src2), sizeof(src2));
    out.write(reinterpret_cast<const char *>(&imm), sizeof(imm));
    out.write(reinterpret_cast<const char *>(&predicated), sizeof(predicated));
    out.write(reinterpret_cast<const char *>(&predicate), sizeof(predicate));
    out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
    out.write(reinterpret_cast<const char *>(&cacheMisses),
              sizeof(cacheMisses));
    out.write(reinterpret_cast<const char *>(&usageCount), sizeof(usageCount));
  }

  /**
   * @brief Deserializes the instruction from a stream.
   * @param in Input stream.
   * @details Reads opcode, registers, immediate, predication, and metrics.
   * @throws std::runtime_error if reading fails or instruction is invalid.
   */
  void Deserialize(std::istream &in) {
    in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
    in.read(reinterpret_cast<char *>(&dst), sizeof(dst));
    in.read(reinterpret_cast<char *>(&src0), sizeof(src0));
    in.read(reinterpret_cast<char *>(&src1), sizeof(src1));
    in.read(reinterpret_cast<char *>(&src2), sizeof(src2));
    in.read(reinterpret_cast<char *>(&imm), sizeof(imm));
    in.read(reinterpret_cast<char *>(&predicated), sizeof(predicated));
    in.read(reinterpret_cast<char *>(&predicate), sizeof(predicate));
    in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
    in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
    in.read(reinterpret_cast<char *>(&usageCount), sizeof(usageCount));
    if (!in.good()) {
      throw std::runtime_error("Failed to deserialize GCN instruction");
    }
    Validate();
  }
};

} // namespace ps4