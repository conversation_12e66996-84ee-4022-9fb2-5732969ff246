// MultipleFiles/gnm_state.h
#pragma once

#include <array>
#include <cstdint>
#include <functional>
#include <istream>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

// Forward declarations
class PS4GPU; // Assuming PS4GPU might interact with this state manager

/**
 * @brief GNM register types.
 */
enum class GNMRegisterType {
  SHADER_REG,  ///< Shader registers (per stage, e.g., SGPRs, VGPRs)
  CONTEXT_REG, ///< Context registers (global rendering state, e.g., viewport, render targets)
  CONFIG_REG,  ///< Configuration registers (hardware configuration, e.g., cache settings)
  USER_REG     ///< User-defined registers (for custom purposes, often used by drivers)
};

/**
 * @brief Callback function type for notifying about register changes.
 * @param type The type of register that changed.
 * @param stage The shader stage (only relevant for SHADER_REG).
 * @param offset The offset of the register within its array.
 * @param value The new value of the register.
 */
using RegisterChangeCallback =
    std::function<void(GNMRegisterType type, uint32_t stage, uint32_t offset, uint32_t value)>;

/**
 * @brief Exception for GNM register state errors.
 */
struct GNMException : std::runtime_error {
  explicit GNMException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Cache entry for register accesses.
 */
struct RegisterCacheEntry {
  GNMRegisterType type;     ///< Register type
  uint32_t stage;           ///< Shader stage (for SHADER_REG, 0 otherwise)
  uint32_t offset;          ///< Register offset within its array
  uint32_t value;           ///< Cached value of the register
  mutable uint64_t cacheHits = 0;   ///< Cache hits for this specific entry
  mutable uint64_t cacheMisses = 0; ///< Cache misses for this specific entry
};

/**
 * @brief Statistics for register state operations.
 */
struct GNMRegisterStateStats {
  uint64_t accessCount = 0;    ///< Total number of register read/write accesses
  uint64_t totalLatencyUs = 0; ///< Total accumulated latency in microseconds across all operations
  uint64_t cacheHits = 0;      ///< Total cache hits for register accesses
  uint64_t cacheMisses = 0;    ///< Total cache misses for register accesses
  uint64_t errorCount = 0;     ///< Total errors encountered during any operation
};

/**
 * @brief Vertex buffer binding description.
 */
struct VertexBindingDesc {
  uint32_t binding;      ///< Binding index (e.g., 0, 1, 2)
  uint32_t stride;       ///< Stride in bytes between consecutive vertices in the buffer
  bool perInstance;      ///< True if data is fetched per instance, false if per vertex
  uint32_t instanceStep; ///< Instance step rate (how many instances to advance per step, if perInstance is true)

  // Default copy/assignment operators are sufficient
  VertexBindingDesc& operator=(const VertexBindingDesc&) = default;
  VertexBindingDesc(const VertexBindingDesc&) = default;
  VertexBindingDesc() = default;
};

/**
 * @brief Vertex attribute description.
 */
struct VertexAttributeDesc {
  uint32_t location;   ///< Attribute location (e.g., layout(location=0) in vec3 position;)
  uint32_t binding;    ///< Binding index to which this attribute is bound (links to VertexBindingDesc)
  uint32_t format;     ///< Format enum (e.g., R32G32B32_FLOAT, R8G8B8A8_UNORM). Needs a proper enum/mapping.
  uint32_t offset;     ///< Offset in bytes within the bound vertex buffer
  uint32_t components; ///< Number of components (e.g., 1 for float, 3 for vec3, 4 for vec4)

  // Default copy/assignment operators are sufficient
  VertexAttributeDesc& operator=(const VertexAttributeDesc&) = default;
  VertexAttributeDesc(const VertexAttributeDesc&) = default;
  VertexAttributeDesc() = default;
};

/**
 * @brief Manages GNM register state for shaders and rendering.
 * @details Provides thread-safe access to shader, context, config, and user
 *          registers, with caching to reduce access overhead. Integrates with
 *          PS4GPU for rendering and GNMShaderTranslator for shader translation.
 *          Supports serialization with versioning and multi-core diagnostics.
 */
class GNMRegisterState {
public:
  /**
   * @brief Constructs the register state manager.
   * @details Initializes all internal register arrays to zero and resets statistics. Thread-safe.
   */
  GNMRegisterState();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Calls Shutdown() to ensure proper cleanup. Thread-safe.
   */
  ~GNMRegisterState();

  /**
   * @brief Initializes the register state manager.
   * @return True on success, false on failure.
   * @throws GNMException on initialization errors.
   * @details Populates register name mappings and resets all internal state and statistics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the register state manager.
   * @details Clears all internal register name mappings, the register cache, and resets statistics. Thread-safe.
   */
  void Shutdown();

  // Constants defining the maximum sizes of different register arrays
  static constexpr size_t MAX_SHADER_REGS = 0x1000; ///< Max shader registers per stage (4096)
  static constexpr size_t SHADER_STAGES = 4; ///< Number of shader stages (e.g., VS, HS, DS, PS, CS)
  static constexpr size_t MAX_CONTEXT_REGS = 0x400; ///< Max context registers (1024)
  static constexpr size_t MAX_CONFIG_REGS = 0x200;  ///< Max config registers (512)
  static constexpr size_t MAX_USER_REGS = 0x100;    ///< Max user registers (256)

  /**
   * @brief Gets a shader register value.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @param offset Register offset within the shader stage's register array.
   * @return The 32-bit value of the specified shader register.
   * @throws GNMException on invalid stage or offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetShaderRegister(uint32_t stage, uint32_t offset) const;

  /**
   * @brief Sets a shader register value.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @param offset Register offset within the shader stage's register array.
   * @param value The 32-bit value to set.
   * @throws GNMException on invalid stage, offset, or if validation fails.
   * @details Thread-safe. Updates the internal register array and the cache. Notifies via callback if set.
   */
  void SetShaderRegister(uint32_t stage, uint32_t offset, uint32_t value);

  /**
   * @brief Gets a context register value.
   * @param offset Register offset within the context register array.
   * @return The 32-bit value of the specified context register.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetContextRegister(uint32_t offset) const;

  /**
   * @brief Sets a context register value.
   * @param offset Register offset within the context register array.
   * @param value The 32-bit value to set.
   * @throws GNMException on invalid offset or if validation fails.
   * @details Thread-safe. Updates the internal register array and the cache. Notifies via callback if set.
   */
  void SetContextRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets a config register value.
   * @param offset Register offset within the config register array.
   * @return The 32-bit value of the specified config register.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetConfigRegister(uint32_t offset) const;

  /**
   * @brief Sets a config register value.
   * @param offset Register offset within the config register array.
   * @param value The 32-bit value to set.
   * @throws GNMException on invalid offset or if validation fails.
   * @details Thread-safe. Updates the internal register array and the cache.
   */
  void SetConfigRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets a user register value.
   * @param offset Register offset within the user register array.
   * @return The 32-bit value of the specified user register.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetUserRegister(uint32_t offset) const;

  /**
   * @brief Sets a user register value.
   * @param offset Register offset within the user register array.
   * @param value The 32-bit value to set.
   * @throws GNMException on invalid offset or if validation fails.
   * @details Thread-safe. Updates the internal register array and the cache.
   */
  void SetUserRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets the shader base address for a specific shader stage.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @return The 32-bit base address of the shader code in memory.
   * @throws GNMException on invalid stage.
   * @details Thread-safe (read-only). Directly accesses the internal array for performance, avoiding recursive locking.
   */
  uint32_t GetShaderBase(uint32_t stage) const;

  /**
   * @brief Gets the address of a render target.
   * @param index Render target index (0 to 7, typically).
   * @return The 32-bit memory address of the specified render target.
   * @throws GNMException on invalid index.
   * @details Thread-safe (read-only). Directly accesses the internal array for performance, avoiding recursive locking.
   */
  uint32_t GetRenderTarget(uint32_t index) const;

  /**
   * @brief Gets the viewport parameters.
   * @param x Output: The X coordinate of the viewport origin.
   * @param y Output: The Y coordinate of the viewport origin.
   * @param width Output: The width of the viewport.
   * @param height Output: The height of the viewport.
   * @param minDepth Output: The minimum depth value for the viewport.
   * @param maxDepth Output: The maximum depth value for the viewport.
   * @details Thread-safe (read-only). Reads multiple context registers and interprets them as floats. Updates metrics.
   */
  void GetViewport(float &x, float &y, float &width, float &height,
                   float &minDepth, float &maxDepth) const;

  /**
   * @brief Retrieves a cached register value.
   * @param type Register type.
   * @param stage Shader stage (only relevant for SHADER_REG, pass 0 otherwise).
   * @param offset Register offset.
   * @param value Output: The cached value if found.
   * @return True if the value was found in the cache, false otherwise.
   * @details Thread-safe (read-only). Updates cache hit/miss statistics for the specific entry and overall.
   */
  bool GetCachedRegister(GNMRegisterType type, uint32_t stage, uint32_t offset,
                         uint32_t &value) const;

  /**
   * @brief Clears the entire register cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearRegisterCache();

  /**
   * @brief Dumps the current register state to a human-readable string.
   * @param nonZeroOnly If true, only registers with non-zero values are included in the dump.
   * @return A string containing the formatted register state.
   * @throws GNMException on internal errors during dump generation.
   * @details Thread-safe (read-only). Iterates through all register arrays and formats their values and names. Updates metrics.
   */
  std::string DumpState(bool nonZeroOnly = true) const;

  /**
   * @brief Retrieves current register state statistics.
   * @return A copy of the current `GNMRegisterStateStats`.
   * @details Thread-safe (read-only). Updates metrics for this access.
   */
  GNMRegisterStateStats GetStats() const;

  /**
   * @brief Saves the entire register state to an output stream.
   * @param out The output stream to write to.
   * @throws GNMException on write errors.
   * @details Thread-safe (read-only). Serializes all register arrays, name mappings, and the register cache with a version header (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the register state from an input stream.
   * @param in The input stream to read from.
   * @throws GNMException on invalid state data, read errors, or unsupported version.
   * @details Thread-safe. Expects version 1 serialization format. Clears existing state before loading.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the callback function to be invoked when a register's value changes.
   * @param callback The `RegisterChangeCallback` function. Pass an empty `std::function` to disable.
   * @details Thread-safe. This callback is used to notify external systems (like the PS4GPU emulator core)
   *          about changes in critical register values, allowing them to react (e.g., update rendering state).
   */
  void SetRegisterChangeCallback(const RegisterChangeCallback &callback);

  /**
   * @brief Serializes the entire register state to a byte vector.
   * @param includeCache If true, includes cache entries in the serialized data.
   * @return A `std::vector<uint8_t>` containing the serialized register state.
   * @throws GNMException on serialization errors.
   * @details Thread-safe (read-only). Uses `SaveState` internally.
   */
  std::vector<uint8_t> Serialize(bool includeCache = false) const;

  /**
   * @brief Deserializes the register state from a byte vector.
   * @param data A `std::vector<uint8_t>` containing the serialized register state.
   * @throws GNMException on deserialization errors.
   * @details Thread-safe. Uses `LoadState` internally.
   */
  void Deserialize(const std::vector<uint8_t> &data);

  /**
   * @brief Deserializes the register state from an input stream.
   * @param in The input stream containing serialized register state.
   * @throws GNMException on deserialization errors.
   * @details Thread-safe. This is an alias for `LoadState`. Use this method when you have an input stream instead of a byte vector.
   */
  void DeserializeFromStream(std::istream &in);

  /**
   * @brief Gets the tessellation state parameters from context registers.
   * @param tessMode Output: The tessellation mode (e.g., 0=disabled, 1=isolines, 2=triangles, 3=quads).
   * @param tessFactorScale Output: The tessellation factor scale.
   * @param tessEdgeFactorScale Output: The tessellation edge factor scale.
   * @param tessControlPointCount Output: The number of control points (typically 1-32).
   * @param tessDistribution Output: The tessellation distribution mode (e.g., 0=equal, 1=fractional_even, 2=fractional_odd).
   * @param tessWinding Output: The tessellation winding order (e.g., 0=CCW, 1=CW).
   * @throws GNMException on errors during register access.
   * @details Thread-safe (read-only). Reads and interprets multiple context registers. Updates metrics.
   */
  void GetTessellationState(uint32_t &tessMode, float &tessFactorScale,
                            float &tessEdgeFactorScale,
                            uint32_t &tessControlPointCount,
                            uint32_t &tessDistribution,
                            uint32_t &tessWinding) const;

  /**
   * @brief Gets vertex input binding descriptions from context registers.
   * @param maxBindings Maximum number of bindings to retrieve (e.g., 16).
   * @return A `std::vector` of `VertexBindingDesc` objects.
   * @throws GNMException on errors during register access.
   * @details Thread-safe (read-only). Parses relevant context registers to reconstruct binding information. Updates metrics.
   */
  std::vector<VertexBindingDesc>
  GetVertexInputBindings(uint32_t maxBindings = 16) const;

  /**
   * @brief Gets vertex input attribute descriptions from context registers.
   * @param maxAttributes Maximum number of attributes to retrieve (e.g., 16).
   * @return A `std::vector` of `VertexAttributeDesc` objects.
   * @throws GNMException on errors during register access.
   * @details Thread-safe (read-only). Parses relevant context registers to reconstruct attribute information. Updates metrics.
   */
  std::vector<VertexAttributeDesc>
  GetVertexInputAttributes(uint32_t maxAttributes = 16) const;

  /**
   * @brief Calculates a hash value for the current resource state.
   * @param includeShaderState If true, includes key shader registers in the hash.
   * @param includeRenderTargets If true, includes render target and viewport registers in the hash.
   * @param includeInputState If true, includes vertex input (bindings, attributes) and tessellation state in the hash.
   * @return A `uint64_t` hash value representing the current resource state.
   * @throws GNMException on errors during register access.
   * @details Thread-safe (read-only). Uses a simple hash combine function. This hash can be used to detect state changes or for state caching (e.g., Graphics Pipeline State Objects).
   */
  uint64_t GetResourceStateHash(bool includeShaderState = true,
                                bool includeRenderTargets = true,
                                bool includeInputState = true) const;

private:
  /**
   * @brief Initializes internal register name mappings.
   * @details Populates `m_shaderRegNames`, `m_contextRegNames`, `m_configRegNames`, and `m_userRegNames` with human-readable names for known register offsets.
   *          Called during `Initialize()`.
   */
  void InitRegisterNames();

  /**
   * @brief Validates a register access (read or write).
   * @param type The type of register being accessed.
   * @param stage The shader stage (for `SHADER_REG`), 0 otherwise.
   * @param offset The offset of the register.
   * @param value The value being written (ignored for reads).
   * @return True if the access is within valid bounds for the given register type, false otherwise.
   * @details This is an inline helper function for performance. It does not throw exceptions, but returns false on invalid access.
   */
  inline bool ValidateRegister(GNMRegisterType type, uint32_t stage,
                               uint32_t offset, uint32_t value) const {
    // Value parameter is unused for validation, but kept for consistency if future validation needs it.
    (void)value;
    switch (type) {
    case GNMRegisterType::SHADER_REG:
      return stage < SHADER_STAGES && offset < MAX_SHADER_REGS;
    case GNMRegisterType::CONTEXT_REG:
      return offset < MAX_CONTEXT_REGS;
    case GNMRegisterType::CONFIG_REG:
      return offset < MAX_CONFIG_REGS;
    case GNMRegisterType::USER_REG:
      return offset < MAX_USER_REGS;
    default:
      return false;
    }
  }

  // Internal storage for different types of registers
  std::array<std::array<uint32_t, MAX_SHADER_REGS>, SHADER_STAGES>
      m_shaderRegisters; ///< 2D array for shader registers (stage x offset)
  std::array<uint32_t, MAX_CONTEXT_REGS>
      m_contextRegisters; ///< Array for context registers
  std::array<uint32_t, MAX_CONFIG_REGS> m_configRegisters; ///< Array for config registers
  std::array<uint32_t, MAX_USER_REGS> m_userRegisters;     ///< Array for user registers

  // Maps for human-readable register names (offset to name string)
  std::unordered_map<uint32_t, std::string> m_shaderRegNames;
  std::unordered_map<uint32_t, std::string> m_contextRegNames;
  std::unordered_map<uint32_t, std::string> m_configRegNames;
  std::unordered_map<uint32_t, std::string> m_userRegNames;

  // Cache for recently accessed register values (key: combined type/stage/offset hash)
  mutable std::unordered_map<uint64_t, RegisterCacheEntry> m_registerCache;

  // Callback function to notify external systems of register changes
  RegisterChangeCallback m_registerChangeCallback;

  // Mutex for thread safety, protecting all internal data members
  mutable std::shared_mutex m_regMutex;
  // Statistics for register state operations, also protected by the mutex
  mutable GNMRegisterStateStats m_stats;
};

} // namespace ps4

