#pragma once
#include <cstdint>
#include <string>

namespace x86_64 {

// General Purpose Registers (64-bit)
enum class Register : uint8_t {
  // 64-bit General Purpose Registers
  RAX = 0,
  RCX = 1,
  RDX = 2,
  RBX = 3,
  RSP = 4,
  RBP = 5,
  RSI = 6,
  RDI = 7,
  R8 = 8,
  R9 = 9,
  R10 = 10,
  R11 = 11,
  R12 = 12,
  R13 = 13,
  R14 = 14,
  R15 = 15,

  // Instruction Pointer
  RIP = 16,

  // Segment Registers
  ES = 17,
  CS = 18,
  SS = 19,
  DS = 20,
  FS = 21,
  GS = 22,

  // Control Registers
  CR0 = 23,
  CR1 = 24, // Reserved
  CR2 = 25,
  CR3 = 26,
  CR4 = 27,
  CR8 = 28, // Task Priority Register (TPR)

  // Debug Registers
  DR0 = 29,
  DR1 = 30,
  DR2 = 31,
  DR3 = 32,
  DR4 = 33, // Reserved
  DR5 = 34, // Reserved
  DR6 = 35,
  DR7 = 36,

  // XMM/YMM/ZMM Registers
  XMM0 = 37,
  XMM1 = 38,
  XMM2 = 39,
  XMM3 = 40,
  XMM4 = 41,
  XMM5 = 42,
  XMM6 = 43,
  XMM7 = 44,
  XMM8 = 45,
  XMM9 = 46,
  XMM10 = 47,
  XMM11 = 48,
  XMM12 = 49,
  XMM13 = 50,
  XMM14 = 51,
  XMM15 = 52,

  // x87 FPU Stack Registers
  ST0 = 53,
  ST1 = 54,
  ST2 = 55,
  ST3 = 56,
  ST4 = 57,
  ST5 = 58,
  ST6 = 59,
  ST7 = 60,

  // MMX Registers (aliased to ST0-ST7)
  MM0 = 53,
  MM1 = 54,
  MM2 = 55,
  MM3 = 56,
  MM4 = 57,
  MM5 = 58,
  MM6 = 59,
  MM7 = 60,

  // Mask Registers (AVX-512)
  K0 = 61,
  K1 = 62,
  K2 = 63,
  K3 = 64,
  K4 = 65,
  K5 = 66,
  K6 = 67,
  K7 = 68,

  // Special Purpose Registers
  RFLAGS = 69,

  // Test Registers (mostly obsolete, for completeness)
  TR0 = 70,
  TR1 = 71,
  TR2 = 72,
  TR3 = 73,
  TR4 = 74,
  TR5 = 75,
  TR6 = 76,
  TR7 = 77,

  NONE = 255 // Invalid/No register
};

constexpr size_t REGISTER_COUNT = 78;
constexpr size_t GPR_COUNT = 16;
constexpr size_t XMM_COUNT = 16;
constexpr size_t SEGMENT_COUNT = 6;
constexpr size_t CONTROL_COUNT = 6;
constexpr size_t DEBUG_COUNT = 8;
constexpr size_t FPU_COUNT = 8;
constexpr size_t MASK_COUNT = 8;

// MSR (Model Specific Register) definitions
enum class MSR : uint32_t {
  // Time Stamp Counter
  TSC = 0x10,

  // Platform Information
  PLATFORM_INFO = 0xCE,

  // Performance Monitoring
  PMC0 = 0xC1,
  PMC1 = 0xC2,
  PMC2 = 0xC3,
  PMC3 = 0xC4,

  // MTRR (Memory Type Range Registers)
  MTRR_CAP = 0xFE,
  MTRR_DEF_TYPE = 0x2FF,
  MTRR_PHYS_BASE0 = 0x200,
  MTRR_PHYS_MASK0 = 0x201,
  MTRR_PHYS_BASE1 = 0x202,
  MTRR_PHYS_MASK1 = 0x203,

  // APIC MSRs
  APIC_BASE = 0x1B,

  // EFER (Extended Feature Enable Register)
  EFER = 0xC0000080,

  // STAR/LSTAR/CSTAR (System Call Target Address)
  STAR = 0xC0000081,
  LSTAR = 0xC0000082,
  CSTAR = 0xC0000083,
  SFMASK = 0xC0000084,

  // FS/GS Base
  FS_BASE = 0xC0000100,
  GS_BASE = 0xC0000101,
  KERNEL_GS_BASE = 0xC0000102,

  // Performance Counters
  PERF_GLOBAL_STATUS = 0x38E,
  PERF_GLOBAL_CTRL = 0x38F,
  PERF_GLOBAL_OVF_CTRL = 0x390,

  // Hardware Configuration
  MISC_ENABLE = 0x1A0,

  // Temperature
  THERM_STATUS = 0x19C,
  THERM_INTERRUPT = 0x19B,

  // Power Management
  PKG_POWER_LIMIT = 0x610,
  PKG_ENERGY_STATUS = 0x611,

  // Invalid MSR
  INVALID = 0xFFFFFFFF
};

// Register size enumeration
enum class RegisterSize : uint16_t {
  BYTE = 8,      // 8-bit (AL, AH, etc.)
  WORD = 16,     // 16-bit (AX, etc.)
  DWORD = 32,    // 32-bit (EAX, etc.)
  QWORD = 64,    // 64-bit (RAX, etc.)
  XMMWORD = 128, // 128-bit XMM
  YMMWORD = 256, // 256-bit YMM
  ZMMWORD = 512  // 512-bit ZMM
};

// Utility functions
inline bool IsValidRegister(Register reg) {
  return static_cast<uint8_t>(reg) < REGISTER_COUNT;
}

inline bool IsGPR(Register reg) {
  return reg >= Register::RAX && reg <= Register::R15;
}

inline bool IsSegmentRegister(Register reg) {
  return reg >= Register::ES && reg <= Register::GS;
}

inline bool IsControlRegister(Register reg) {
  return (reg >= Register::CR0 && reg <= Register::CR4) || reg == Register::CR8;
}

inline bool IsDebugRegister(Register reg) {
  return reg >= Register::DR0 && reg <= Register::DR7;
}

inline bool IsXMMRegister(Register reg) {
  return reg >= Register::XMM0 && reg <= Register::XMM15;
}

inline bool IsFPURegister(Register reg) {
  return reg >= Register::ST0 && reg <= Register::ST7;
}

inline bool IsMaskRegister(Register reg) {
  return reg >= Register::K0 && reg <= Register::K7;
}

inline std::string RegisterToString(Register reg) {
  switch (reg) {
  // GPRs
  case Register::RAX:
    return "RAX";
  case Register::RCX:
    return "RCX";
  case Register::RDX:
    return "RDX";
  case Register::RBX:
    return "RBX";
  case Register::RSP:
    return "RSP";
  case Register::RBP:
    return "RBP";
  case Register::RSI:
    return "RSI";
  case Register::RDI:
    return "RDI";
  case Register::R8:
    return "R8";
  case Register::R9:
    return "R9";
  case Register::R10:
    return "R10";
  case Register::R11:
    return "R11";
  case Register::R12:
    return "R12";
  case Register::R13:
    return "R13";
  case Register::R14:
    return "R14";
  case Register::R15:
    return "R15";
  case Register::RIP:
    return "RIP";

  // Segment registers
  case Register::ES:
    return "ES";
  case Register::CS:
    return "CS";
  case Register::SS:
    return "SS";
  case Register::DS:
    return "DS";
  case Register::FS:
    return "FS";
  case Register::GS:
    return "GS";

  // Control registers
  case Register::CR0:
    return "CR0";
  case Register::CR1:
    return "CR1";
  case Register::CR2:
    return "CR2";
  case Register::CR3:
    return "CR3";
  case Register::CR4:
    return "CR4";
  case Register::CR8:
    return "CR8";

  // Debug registers
  case Register::DR0:
    return "DR0";
  case Register::DR1:
    return "DR1";
  case Register::DR2:
    return "DR2";
  case Register::DR3:
    return "DR3";
  case Register::DR4:
    return "DR4";
  case Register::DR5:
    return "DR5";
  case Register::DR6:
    return "DR6";
  case Register::DR7:
    return "DR7";

  // XMM registers
  case Register::XMM0:
    return "XMM0";
  case Register::XMM1:
    return "XMM1";
  case Register::XMM2:
    return "XMM2";
  case Register::XMM3:
    return "XMM3";
  case Register::XMM4:
    return "XMM4";
  case Register::XMM5:
    return "XMM5";
  case Register::XMM6:
    return "XMM6";
  case Register::XMM7:
    return "XMM7";
  case Register::XMM8:
    return "XMM8";
  case Register::XMM9:
    return "XMM9";
  case Register::XMM10:
    return "XMM10";
  case Register::XMM11:
    return "XMM11";
  case Register::XMM12:
    return "XMM12";
  case Register::XMM13:
    return "XMM13";
  case Register::XMM14:
    return "XMM14";
  case Register::XMM15:
    return "XMM15";

  case Register::RFLAGS:
    return "RFLAGS";
  case Register::NONE:
    return "NONE";
  default:
    return "UNKNOWN";
  }
}

// Sub-register access helpers
inline Register GetSubRegister(Register base, RegisterSize size) {
  if (!IsGPR(base))
    return Register::NONE;

  // For now, return the base register as we handle sizing in the CPU
  // In a more complete implementation, we'd have separate enums for
  // EAX, AX, AL, AH, etc.
  return base;
}

}
