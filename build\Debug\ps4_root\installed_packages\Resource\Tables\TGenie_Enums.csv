RSID_TGENIE_START, 3625,,,
 ,[Offset], GENIE_FLYER_1, 0,
 ,[Offset], <PERSON><PERSON>\PBTGenie, 1,
 ,[Offset], <PERSON>ie\InstructionsENG, 2,
 ,[Offset], <PERSON>ie\InstructionsFR, 3,
 ,[Offset], <PERSON>ie\InstructionsITAL, 4,
 ,[Offset], <PERSON>ie\InstructionsGERM, 5,
 ,[Offset], <PERSON>ie\InstructionsSPAN, 6,
 ,[Offset], <PERSON>ie\InstructionsENG, 7,
 ,[Offset], Genie\InstructionsENG, 8,
 ,[Offset], tables\Tales_BG_scroll, 9,
 ,[Offset], StarTrekTNG\Pro_TipsENG, 10,
 ,[Offset], StarTrekTNG\Pro_TipsFR, 11,
 ,[Offset], StarTrekTNG\Pro_TipsITAL, 12,
 ,[Offset], StarTrekTNG\Pro_TipsGERM, 13,
 ,[Offset], <PERSON>T<PERSON>TNG\Pro_TipsSPAN, 14,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON><PERSON>\Pro_TipsENG, 15,
 ,[Offset], <PERSON><PERSON><PERSON>TNG\Pro_TipsENG, 16,
RSID_TGENIE_LIGHTS, 3626,,,
RSID_TGENIE_CAMERAS, 3627,,,
RSID_TGENIE_LAMP_TEXTURES, 3628,,,
 ,[Offset], l4 off, 0,
 ,[Offset], l4 on, 1,
 ,[Offset], l5 off, 2,
 ,[Offset], l5 on, 3,
 ,[Offset], l6 off, 4,
 ,[Offset], l6 on, 5,
 ,[Offset], l7 off, 6,
 ,[Offset], l7 on, 7,
 ,[Offset], l8 off, 8,
 ,[Offset], l8 on, 9,
 ,[Offset], l9 off, 10,
 ,[Offset], l9 on, 11,
 ,[Offset], l10 off, 12,
 ,[Offset], l10 on, 13,
 ,[Offset], l11 off, 14,
 ,[Offset], l11 on, 15,
 ,[Offset], l12 off, 16,
 ,[Offset], l12 on, 17,
 ,[Offset], l13 off, 18,
 ,[Offset], l13 on, 19,
 ,[Offset], l14 off, 20,
 ,[Offset], l14 on, 21,
 ,[Offset], l15 off, 22,
 ,[Offset], l15 on, 23,
 ,[Offset], l16 off, 24,
 ,[Offset], l16 on, 25,
 ,[Offset], l17 off, 26,
 ,[Offset], l17 on, 27,
 ,[Offset], l18 off, 28,
 ,[Offset], l18 on, 29,
 ,[Offset], l19 off, 30,
 ,[Offset], l19 on, 31,
 ,[Offset], l20 off, 32,
 ,[Offset], l20 on, 33,
 ,[Offset], l21 off, 34,
 ,[Offset], l21 on, 35,
 ,[Offset], l22 off, 36,
 ,[Offset], l22 on, 37,
 ,[Offset], l23 off, 38,
 ,[Offset], l23 on, 39,
 ,[Offset], l24 off, 40,
 ,[Offset], l24 on, 41,
 ,[Offset], l25 off, 42,
 ,[Offset], l25 on, 43,
 ,[Offset], l26 off, 44,
 ,[Offset], l26 on, 45,
 ,[Offset], l27 off, 46,
 ,[Offset], l27 on, 47,
 ,[Offset], l28 off, 48,
 ,[Offset], l28 on, 49,
 ,[Offset], l29 off, 50,
 ,[Offset], l29 on, 51,
 ,[Offset], l30 off, 52,
 ,[Offset], l30 on, 53,
 ,[Offset], l31 off, 54,
 ,[Offset], l31 on, 55,
 ,[Offset], l32 off, 56,
 ,[Offset], l32 on, 57,
 ,[Offset], l33 off, 58,
 ,[Offset], l33 on, 59,
 ,[Offset], l36 off, 60,
 ,[Offset], l36 on, 61,
 ,[Offset], sw53a off, 62,
 ,[Offset], sw53a on, 63,
 ,[Offset], sw53b off, 64,
 ,[Offset], sw53b on, 65,
 ,[Offset], sw53c off, 66,
 ,[Offset], sw53c on, 67,
 ,[Offset], sw64a off, 68,
 ,[Offset], sw64a on, 69,
 ,[Offset], sw64b off, 70,
 ,[Offset], sw64b on, 71,
RSID_TGENIE_TEXTURES, 3629,,,
 ,[Offset], blank, 0,
 ,[Offset], bolt-tp01 copy, 1,
 ,[Offset], Box_Logo, 2,
 ,[Offset], DropA copy, 3,
 ,[Offset], DropB copy, 4,
 ,[Offset], DropC copy, 5,
 ,[Offset], flippers-tp01 copy, 6,
 ,[Offset], glass, 7,
 ,[Offset], LampBrick, 8,
 ,[Offset], metal01, 9,
 ,[Offset], rubberband01, 10,
 ,[Offset], tableSide_front, 11,
 ,[Offset], targetR01 copy, 12,
 ,[Offset], targetW01 copy, 13,
 ,[Offset], TopCorner, 14,
 ,[Offset], wood01, 15,
 ,[Offset], light-strand-tp01 copy, 16,
 ,[Offset], Buttons_Parts, 17,
 ,[Offset], Coinback, 18,
 ,[Offset], CoinSlots, 19,
 ,[Offset], metalplate, 20,
 ,[Offset], partD01 copy, 21,
 ,[Offset], partB01 copy, 22,
 ,[Offset], partA01 copy, 23,
 ,[Offset], partC01 copy, 24,
 ,[Offset], postB01 copy, 25,
 ,[Offset], postA01 copy, 26,
 ,[Offset], BackGlass, 27,
 ,[Offset], Bumpers_down, 28,
 ,[Offset], Bumpers_Up, 29,
 ,[Offset], rules, 30,
 ,[Offset], lowerTable_L, 31,
 ,[Offset], lowerTable_R, 32,
 ,[Offset], floor01, 33,
 ,[Offset], floor02, 34,
 ,[Offset], floor03, 35,
 ,[Offset], floor04, 36,
 ,[Offset], floor05, 37,
 ,[Offset], floor06, 38,
 ,[Offset], floor07, 39,
 ,[Offset], floor08, 40,
 ,[Offset], floor09, 41,
 ,[Offset], floor10, 42,
 ,[Offset], floor11, 43,
 ,[Offset], floor12, 44,
 ,[Offset], floor13, 45,
 ,[Offset], floor14, 46,
 ,[Offset], floor15, 47,
 ,[Offset], RbumpA01 copy, 48,
 ,[Offset], RbumpA02 copy, 49,
 ,[Offset], RbumpB01 copy, 50,
 ,[Offset], RbumpB02 copy, 51,
 ,[Offset], playfield_bottom, 52,
 ,[Offset], playfield_top, 53,
 ,[Offset], cabinet_metal, 54,
 ,[Offset], Metal_Parts, 55,
 ,[Offset], Plunger, 56,
 ,[Offset], Plunger_Plate, 57,
 ,[Offset], rubber, 58,
 ,[Offset], spring, 59,
 ,[Offset], metal1, 60,
 ,[Offset], plastic_piece, 61,
 ,[Offset], yellowplastic_gates, 62,
 ,[Offset], redplastic_gates, 63,
 ,[Offset], Flipper, 64,
 ,[Offset], nut, 65,
 ,[Offset], Silver_screws, 66,
 ,[Offset], metal_texture, 67,
 ,[Offset], rails, 68,
 ,[Offset], clear, 69,
 ,[Offset], metal, 70,
 ,[Offset], small_Flipper, 71,
RSID_TGENIE_MODELS, 3630,,,
 ,[Offset], floor, 0,
 ,[Offset], backglass, 1,
 ,[Offset], blank, 2,
 ,[Offset], bumper A01, 3,
 ,[Offset], bumper B01, 4,
 ,[Offset], bumper C01, 5,
 ,[Offset], flipper L, 6,
 ,[Offset], flipper R, 7,
 ,[Offset], flipper R2, 8,
 ,[Offset], gate oneway01, 9,
 ,[Offset], generic B01, 10,
 ,[Offset], generic C01, 11,
 ,[Offset], generic D01, 12,
 ,[Offset], glass, 13,
 ,[Offset], plastic, 14,
 ,[Offset], plastic, 15,
 ,[Offset], plunger, 16,
 ,[Offset], slingshot A01, 17,
 ,[Offset], slingshot A02, 18,
 ,[Offset], slingshot B01, 19,
 ,[Offset], slingshot B02, 20,
 ,[Offset], slingshot C01, 21,
 ,[Offset], slingshot C02, 22,
 ,[Offset], slingshot D01, 23,
 ,[Offset], slingshot D02, 24,
 ,[Offset], spinner A, 25,
 ,[Offset], target A, 26,
 ,[Offset], target B, 27,
 ,[Offset], target C, 28,
 ,[Offset], target D, 29,
 ,[Offset], tile, 30,
 ,[Offset], trap A, 31,
 ,[Offset], upper flipper L, 32,
 ,[Offset], upper flipper R, 33,
 ,[Offset], wall, 34,
 ,[Offset], wire A, 35,
 ,[Offset], wire F, 36,
 ,[Offset], lamps, 37,
 ,[Offset], cabinet, 38,
 ,[Offset], cabinet_metals, 39,
 ,[Offset], pop_bumpers, 40,
 ,[Offset], plastics, 41,
 ,[Offset], metal_pieces, 42,
 ,[Offset], wooden_rails, 43,
 ,[Offset], yellow_gates, 44,
 ,[Offset], red_gates, 45,
 ,[Offset], metal_posts, 46,
 ,[Offset], nutsandbolts, 47,
 ,[Offset], rubbers, 48,
 ,[Offset], metal_rails, 49,
 ,[Offset], Flipper, 50,
 ,[Offset], Flipper_Small, 51,
RSID_TGENIE_MODELS_LODS, 3631,,,
 ,[Offset], floor, 0,
 ,[Offset], backglass, 1,
 ,[Offset], blank, 2,
 ,[Offset], bumper A01, 3,
 ,[Offset], bumper B01, 4,
 ,[Offset], bumper C01, 5,
 ,[Offset], flipper L, 6,
 ,[Offset], flipper R, 7,
 ,[Offset], flipper R2, 8,
 ,[Offset], gate oneway01, 9,
 ,[Offset], generic B01, 10,
 ,[Offset], generic C01, 11,
 ,[Offset], generic D01, 12,
 ,[Offset], glass, 13,
 ,[Offset], plastic, 14,
 ,[Offset], plastic, 15,
 ,[Offset], plunger, 16,
 ,[Offset], slingshot A01, 17,
 ,[Offset], slingshot A02, 18,
 ,[Offset], slingshot B01, 19,
 ,[Offset], slingshot B02, 20,
 ,[Offset], slingshot C01, 21,
 ,[Offset], slingshot C02, 22,
 ,[Offset], slingshot D01, 23,
 ,[Offset], slingshot D02, 24,
 ,[Offset], spinner A, 25,
 ,[Offset], target A, 26,
 ,[Offset], target B, 27,
 ,[Offset], target C, 28,
 ,[Offset], target D, 29,
 ,[Offset], tile, 30,
 ,[Offset], trap A, 31,
 ,[Offset], upper flipper L, 32,
 ,[Offset], upper flipper R, 33,
 ,[Offset], wall, 34,
 ,[Offset], wire A, 35,
 ,[Offset], wire F, 36,
 ,[Offset], lamps, 37,
 ,[Offset], cabinet, 38,
 ,[Offset], cabinet_metals, 39,
 ,[Offset], pop_bumpers, 40,
 ,[Offset], plastics, 41,
 ,[Offset], metal_pieces, 42,
 ,[Offset], wooden_rails, 43,
 ,[Offset], yellow_gates, 44,
 ,[Offset], red_gates, 45,
 ,[Offset], metal_posts, 46,
 ,[Offset], nutsandbolts, 47,
 ,[Offset], rubbers, 48,
 ,[Offset], metal_rails, 49,
 ,[Offset], Flipper, 50,
 ,[Offset], Flipper_Small, 51,
RSID_TGENIE_COLLISIONS, 3632,,,
 ,[Offset], floor col, 0,
 ,[Offset], arc col, 1,
 ,[Offset], bumper A01 col, 2,
 ,[Offset], bumper B01 col, 3,
 ,[Offset], bumper C01 col, 4,
 ,[Offset], flipper L B col, 5,
 ,[Offset], flipper L col, 6,
 ,[Offset], flipper L F col, 7,
 ,[Offset], flipper R B col, 8,
 ,[Offset], flipper R col, 9,
 ,[Offset], flipper R F col, 10,
 ,[Offset], flipper R2 col, 11,
 ,[Offset], gate oneway01 col, 12,
 ,[Offset], generic B01 col, 13,
 ,[Offset], generic C01 col, 14,
 ,[Offset], generic D01 col, 15,
 ,[Offset], plunger col, 16,
 ,[Offset], plungercam col, 17,
 ,[Offset], rubber col 01, 18,
 ,[Offset], rubber col 02, 19,
 ,[Offset], rubber col 03, 20,
 ,[Offset], rubber col, 21,
 ,[Offset], slingshot A01 col, 22,
 ,[Offset], slingshot B01 col, 23,
 ,[Offset], slingshot C01 col, 24,
 ,[Offset], slingshot D01 col, 25,
 ,[Offset], spinner A col, 26,
 ,[Offset], target A col, 27,
 ,[Offset], target B col, 28,
 ,[Offset], target C col, 29,
 ,[Offset], target D col, 30,
 ,[Offset], tile col, 31,
 ,[Offset], trap A col, 32,
 ,[Offset], upper flipper L B col, 33,
 ,[Offset], upper flipper L col, 34,
 ,[Offset], upper flipper L F col, 35,
 ,[Offset], upper flipper R B col, 36,
 ,[Offset], upper flipper R col, 37,
 ,[Offset], upper flipper R F col, 38,
 ,[Offset], wall col, 39,
 ,[Offset], wire A col, 40,
 ,[Offset], wire F col, 41,
 ,[Offset], wire H col, 42,
 ,[Offset], wire I col, 43,
 ,[Offset], platform1, 44,
 ,[Offset], rubber col 04, 45,
 ,[Offset], rubber col 05, 46,
 ,[Offset], rubber col 06, 47,
 ,[Offset], rubber col 07, 48,
 ,[Offset], rubber col 08, 49,
 ,[Offset], rubber col 09, 50,
 ,[Offset], rubber col 10, 51,
 ,[Offset], rubber col 11, 52,
 ,[Offset], rubber col 12, 53,
 ,[Offset], lower_rails, 54,
 ,[Offset], left_walls, 55,
 ,[Offset], ballcatch back, 56,
 ,[Offset], drain col, 57,
 ,[Offset], leftback target, 58,
 ,[Offset], lowleftplasticwall, 59,
 ,[Offset], midback target, 60,
 ,[Offset], rightback target, 61,
 ,[Offset], lowrightplasticwall, 62,
 ,[Offset], midplasticwall, 63,
 ,[Offset], backplasticwall, 64,
 ,[Offset], switch_1, 65,
 ,[Offset], switch_2, 66,
 ,[Offset], switch_3, 67,
 ,[Offset], switch_4, 68,
 ,[Offset], switch_5, 69,
 ,[Offset], FLipper_Left, 70,
 ,[Offset], FLipper_Left_Back, 71,
 ,[Offset], FLipper_Right, 72,
 ,[Offset], FLipper_Right_Back, 73,
 ,[Offset], FLipper_Right_Upper, 74,
 ,[Offset], FLipper_Right_Upper_Back, 75,
 ,[Offset], Flipper_Left_Small, 76,
 ,[Offset], Flipper_Left_Small_Back, 77,
 ,[Offset], Flipper_Right_Small, 78,
 ,[Offset], Flipper_Right_Small_Back, 79,
RSID_TGENIE_PLACEMENT, 3633,,,
 ,[Offset], placement, 0,
 ,[Offset], Flipper_Placement, 1,
 ,[Offset], Lights, 2,
RSID_TGENIE_SOUNDS, 3634,,,
 ,[Offset], 10k_bottom01, 0,
 ,[Offset], 1k01, 1,
 ,[Offset], 20k_addbonus01, 2,
 ,[Offset], 500_andbonus01, 3,
 ,[Offset], 500_andbonus02, 4,
 ,[Offset], 500_score_bonus01, 5,
 ,[Offset], droptarget01, 6,
 ,[Offset], droptarget-bonus01, 7,
 ,[Offset], insertcoin-music01, 8,
 ,[Offset], lamp-spinner01, 9,
 ,[Offset], lettersound01, 10,
 ,[Offset], lettersound02, 11,
 ,[Offset], losegame01, 12,
 ,[Offset], startup01, 13,
 ,[Offset], target-point01, 14,
 ,[Offset], scoring, 15,
 ,[Offset], scoring_2x, 16,
 ,[Offset], scoring_3x, 17,
 ,[Offset], scoring_4x, 18,
 ,[Offset], scoring_5x, 19,
 ,[Offset], main_menu, 20,
RSID_TGENIE_HUD, 3635,,,
RSID_TGENIE_HUDHD, 3636,,,
RSID_TGENIE_HUDHDD, 3637,,,
RSID_FONTTABLE_TGENIE_FONT, 3638,,,
RSID_FONT_TGENIE_HUD, 3639,,,
RSID_FONTTABLE_TGENIE_FONTHD, 3640,,,
RSID_FONT_TGENIE_HUDHD, 3641,,,
RSID_FONTTABLE_TGENIE_FONTHDD, 3642,,,
RSID_FONT_TGENIE_HUDHDD, 3643,,,
RSID_TGENIE_VERSION, 3644,,,
RSID_TGENIE_END, 3645,,,
