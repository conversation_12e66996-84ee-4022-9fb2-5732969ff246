RSID_TMONSTERBASH_START, 3275,,,
 ,[Offset], <PERSON>Bash_FLYER_1, 0,
 ,[Offset], <PERSON>B<PERSON>\PBTMonsterBash, 1,
 ,[Offset], MonsterBash\InstructionsENG, 2,
 ,[Offset], MonsterBash\InstructionsFR, 3,
 ,[Offset], <PERSON>B<PERSON>\InstructionsITAL, 4,
 ,[Offset], MonsterB<PERSON>\InstructionsGERM, 5,
 ,[Offset], MonsterBash\InstructionsSPAN, 6,
 ,[Offset], MonsterBash\InstructionsPORT, 7,
 ,[Offset], MonsterBash\InstructionsDUTCH, 8,
 ,[Offset], tables\Monster_BG_scroll, 9,
RSID_TMONSTERBASH_LIGHTS, 3276,,,
RSID_TMONSTERBASH_CAMERAS, 3277,,,
RSID_TMONSTERBASH_LAMP_TEXTURES, 3278,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], F_15_Off, 124,
 ,[Offset], F_15_On, 125,
 ,[Offset], F_17_Off_B, 126,
 ,[Offset], F_17_On_B, 127,
 ,[Offset], F_17_Off, 128,
 ,[Offset], F_17_On, 129,
 ,[Offset], F_18_Off, 130,
 ,[Offset], F_18_On, 131,
 ,[Offset], F_19_Off, 132,
 ,[Offset], F_19_On, 133,
 ,[Offset], F_23_Off, 134,
 ,[Offset], F_23_On, 135,
 ,[Offset], F_21_Off, 136,
 ,[Offset], F_21_On, 137,
 ,[Offset], F_26_Off, 138,
 ,[Offset], F_26_On, 139,
 ,[Offset], LaunchBall_Button, 140,
 ,[Offset], LaunchBall_Button_On, 141,
 ,[Offset], F_22_Off, 142,
 ,[Offset], F_22_On, 143,
 ,[Offset], L_22_on_F, 144,
 ,[Offset], L_13_on_F, 145,
 ,[Offset], MummyB, 146,
 ,[Offset], MummyB_on, 147,
 ,[Offset], MummyH, 148,
 ,[Offset], MummyH_on, 149,
 ,[Offset], coffin_plastic, 150,
 ,[Offset], coffin_plastic_on, 151,
 ,[Offset], coffin, 152,
 ,[Offset], coffin_on, 153,
 ,[Offset], Tree_Plastics, 154,
 ,[Offset], Tree_Plastics_on, 155,
RSID_TMONSTERBASH_TEXTURES, 3279,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], Big_Orange_Plastic, 1,
 ,[Offset], BottomRing, 2,
 ,[Offset], Bride_IslandPlastic, 3,
 ,[Offset], Dracula_Body, 4,
 ,[Offset], Dracula_Head, 5,
 ,[Offset], Frank_Body, 6,
 ,[Offset], Frank_Head, 7,
 ,[Offset], Frank_Stand, 8,
 ,[Offset], Habit_1, 9,
 ,[Offset], Last_Plastics, 10,
 ,[Offset], Left_CornerPlastic_SmallPlastic, 11,
 ,[Offset], LeftSideCreaturePlastic, 12,
 ,[Offset], Metal_Ramp1, 13,
 ,[Offset], Metal_Ramp2, 14,
 ,[Offset], Middle_Ring, 15,
 ,[Offset], Playfield, 16,
 ,[Offset], Playfield_Lower, 17,
 ,[Offset], Playfield_Upper, 18,
 ,[Offset], Tesla_Stand, 19,
 ,[Offset], TOP_Ring, 20,
 ,[Offset], Big_Orange_Plastic, 21,
 ,[Offset], Big_Orange_Plastic, 22,
 ,[Offset], Big_Orange_Plastic, 23,
 ,[Offset], Big_Orange_Plastic, 24,
 ,[Offset], Apron, 25,
 ,[Offset], Bulb1, 26,
 ,[Offset], Flipper, 27,
 ,[Offset], Silver Metal Screws_Temp, 28,
 ,[Offset], rubberband_Temp, 29,
 ,[Offset], Generic_Metal, 30,
 ,[Offset], Eject_Scoop, 31,
 ,[Offset], Metal_Parts, 32,
 ,[Offset], Rails, 33,
 ,[Offset], ClearPlasticPost_01, 34,
 ,[Offset], Plastic_Ramp_02, 35,
 ,[Offset], PopBumperBody, 36,
 ,[Offset], Clear_Bumper, 37,
 ,[Offset], Red_Target, 38,
 ,[Offset], Rubber Post_Temp, 39,
 ,[Offset], Red_Plastic_Post, 40,
 ,[Offset], Bumper_Sensors, 41,
 ,[Offset], Bumper_Hamer, 42,
 ,[Offset], Target_White, 43,
 ,[Offset], Metal_Walls, 44,
 ,[Offset], Target_Yellow, 45,
 ,[Offset], Dracula_Coffin, 46,
 ,[Offset], Dracula_CoffinPlastics, 47,
 ,[Offset], Dracula_PlasticPlane, 48,
 ,[Offset], Long_PurplePlastic, 49,
 ,[Offset], Part1_Plastics, 50,
 ,[Offset], Part2_Plastics, 51,
 ,[Offset], Spider_Plastic, 52,
 ,[Offset], ArrowtoKnee, 53,
 ,[Offset], Orange_Target, 54,
 ,[Offset], Spinner, 55,
 ,[Offset], Creature_Feature_Plastic, 56,
 ,[Offset], Sign_Plastic, 57,
 ,[Offset], Chain_Plastic, 58,
 ,[Offset], Metal_Scoop_Bracket, 59,
 ,[Offset], Harley_Spinner, 60,
 ,[Offset], Backglass, 61,
 ,[Offset], Black_Grain, 62,
 ,[Offset], Box_Textures, 63,
 ,[Offset], Cab_Front_Face, 64,
 ,[Offset], Metals, 65,
 ,[Offset], Cabinet_Metals, 66,
 ,[Offset], Cabinet01, 67,
 ,[Offset], Coin_Slot, 68,
 ,[Offset], Flipper_Button, 69,
 ,[Offset], Harley_Gate, 70,
 ,[Offset], backplasticA, 71,
 ,[Offset], backplasticB, 72,
 ,[Offset], Metal_Temp, 73,
 ,[Offset], Blue_Bulb, 74,
 ,[Offset], Habit_2, 75,
 ,[Offset], MM_Table_All, 76,
 ,[Offset], speaker, 77,
 ,[Offset], Buttons_Parts, 78,
 ,[Offset], metal-parts01 copy, 79,
 ,[Offset], Inner_Cabinet, 80,
 ,[Offset], metal front, 81,
 ,[Offset], Extra_Metal_Parts, 82,
 ,[Offset], Metal_Lamp, 83,
 ,[Offset], Generic_Screw, 84,
 ,[Offset], brdface1, 85,
 ,[Offset], bride_body, 86,
 ,[Offset], prof1, 87,
 ,[Offset], prof2, 88,
 ,[Offset], Doctor_Plastic, 89,
 ,[Offset], Igor_BodyA, 90,
 ,[Offset], Igor_HeadA, 91,
 ,[Offset], Final_DracHead, 92,
 ,[Offset], FrankHeadB, 93,
 ,[Offset], FrankB, 94,
 ,[Offset], Final_FrankStand, 95,
 ,[Offset], Side_Walls, 96,
RSID_TMONSTERBASH_MODELS, 3280,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Cabinet, 2,
 ,[Offset], Flipper, 3,
 ,[Offset], Habit_Left, 4,
 ,[Offset], Habit_Right, 5,
 ,[Offset], Lightning_Towers, 6,
 ,[Offset], Metal_Pieces, 7,
 ,[Offset], Metal_Ramps, 8,
 ,[Offset], Metal_Walls, 9,
 ,[Offset], Plastic_Pieces, 10,
 ,[Offset], Plastic_Posts, 11,
 ,[Offset], Playfield, 12,
 ,[Offset], Pop_Bumpers, 13,
 ,[Offset], Red_Target, 14,
 ,[Offset], Rubber_Posts, 15,
 ,[Offset], Slingshot_Left, 16,
 ,[Offset], Slingshot_Right, 17,
 ,[Offset], White_Target, 18,
 ,[Offset], Wire, 19,
 ,[Offset], Wooden_Rails, 20,
 ,[Offset], Yellow_Target, 21,
 ,[Offset], Dracula, 22,
 ,[Offset], Orange_Target, 23,
 ,[Offset], Red_Target_Case, 24,
 ,[Offset], Spinner_A, 25,
 ,[Offset], Pop_Bumper_Metal, 26,
 ,[Offset], Creature_Feature_Lights, 27,
 ,[Offset], Cabinet_Backglass, 28,
 ,[Offset], Cabinet_Buttons, 29,
 ,[Offset], Cabinet_Body, 30,
 ,[Offset], Cabinet_Interior, 31,
 ,[Offset], Cabinet_Metals, 32,
 ,[Offset], Gate_A, 33,
 ,[Offset], Gate_B, 34,
 ,[Offset], Gate_C, 35,
 ,[Offset], Ramp_Switch, 36,
 ,[Offset], OneWayGate, 37,
 ,[Offset], Stopper_Post, 38,
 ,[Offset], Flashers, 39,
 ,[Offset], Wolf_Plastic, 40,
 ,[Offset], Wolf_Plastic_2, 41,
 ,[Offset], Creature_Lagoon, 42,
 ,[Offset], Frank, 43,
 ,[Offset], Frank_Arms, 44,
 ,[Offset], Frank_Bracket, 45,
 ,[Offset], Light_Cutouts, 46,
 ,[Offset], Bride_Head, 47,
 ,[Offset], Coffin, 48,
 ,[Offset], Bride, 49,
 ,[Offset], Mummy, 50,
 ,[Offset], Scientist, 51,
 ,[Offset], Igor, 52,
 ,[Offset], Light_Cutouts_2, 53,
 ,[Offset], Plastic_Pieces_2, 54,
 ,[Offset], Plastic_Posts_2, 55,
RSID_TMONSTERBASH_MODELS_LODS, 3281,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Cabinet, 2,
 ,[Offset], Flipper, 3,
 ,[Offset], Habit_Left, 4,
 ,[Offset], Habit_Right, 5,
 ,[Offset], Lightning_Towers, 6,
 ,[Offset], Metal_Pieces, 7,
 ,[Offset], Metal_Ramps, 8,
 ,[Offset], Metal_Walls, 9,
 ,[Offset], Plastic_Pieces, 10,
 ,[Offset], Plastic_Posts, 11,
 ,[Offset], Playfield, 12,
 ,[Offset], Pop_Bumpers, 13,
 ,[Offset], Red_Target, 14,
 ,[Offset], Rubber_Posts, 15,
 ,[Offset], Slingshot_Left, 16,
 ,[Offset], Slingshot_Right, 17,
 ,[Offset], White_Target, 18,
 ,[Offset], Wire, 19,
 ,[Offset], Wooden_Rails, 20,
 ,[Offset], Yellow_Target, 21,
 ,[Offset], Dracula, 22,
 ,[Offset], Orange_Target, 23,
 ,[Offset], Red_Target_Case, 24,
 ,[Offset], Spinner_A, 25,
 ,[Offset], Pop_Bumper_Metal, 26,
 ,[Offset], Creature_Feature_Lights, 27,
 ,[Offset], Cabinet_Backglass, 28,
 ,[Offset], Cabinet_Buttons, 29,
 ,[Offset], Cabinet_Body, 30,
 ,[Offset], Cabinet_Interior, 31,
 ,[Offset], Cabinet_Metals, 32,
 ,[Offset], Gate_A, 33,
 ,[Offset], Gate_B, 34,
 ,[Offset], Gate_C, 35,
 ,[Offset], Ramp_Switch, 36,
 ,[Offset], OneWayGate, 37,
 ,[Offset], Stopper_Post, 38,
 ,[Offset], Flashers, 39,
 ,[Offset], Wolf_Plastic, 40,
 ,[Offset], Wolf_Plastic_2, 41,
 ,[Offset], Creature_Lagoon, 42,
 ,[Offset], Frank, 43,
 ,[Offset], Frank_Arms, 44,
 ,[Offset], Frank_Bracket, 45,
 ,[Offset], Light_Cutouts, 46,
 ,[Offset], Bride_Head, 47,
 ,[Offset], Coffin, 48,
 ,[Offset], Bride, 49,
 ,[Offset], Mummy, 50,
 ,[Offset], Scientist, 51,
 ,[Offset], Igor, 52,
 ,[Offset], Light_Cutouts_2, 53,
 ,[Offset], Plastic_Pieces_2, 54,
 ,[Offset], Plastic_Posts_2, 55,
RSID_TMONSTERBASH_COLLISION, 3282,,,
 ,[Offset], Apron, 0,
 ,[Offset], Ball_Drain, 1,
 ,[Offset], Flipper_Lane_Left, 2,
 ,[Offset], Flipper_Lane_Right, 3,
 ,[Offset], Flipper_Left_Back, 4,
 ,[Offset], Flipper_Left_Front, 5,
 ,[Offset], Flipper_Right_Back, 6,
 ,[Offset], Flipper_Right_Front, 7,
 ,[Offset], Floor, 8,
 ,[Offset], Frank_Ramp, 9,
 ,[Offset], Habit_Left, 10,
 ,[Offset], Habit_Ramp, 11,
 ,[Offset], Habit_Right, 12,
 ,[Offset], Metal_Wall_1, 13,
 ,[Offset], Metal_Wall_2, 14,
 ,[Offset], Outer_Wall, 15,
 ,[Offset], Plunger_Ramp, 16,
 ,[Offset], Rubber_1, 17,
 ,[Offset], Rubber_2, 18,
 ,[Offset], Rubber_3, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Left_Front, 21,
 ,[Offset], Slingshot_Right, 22,
 ,[Offset], Slingshot_Right_Front, 23,
 ,[Offset], Red_Target, 24,
 ,[Offset], White_Target, 25,
 ,[Offset], Yellow_Target, 26,
 ,[Offset], Plunger, 27,
 ,[Offset], Plunger_Button, 28,
 ,[Offset], Dracula, 29,
 ,[Offset], creature_Trap, 30,
 ,[Offset], Scoop_Trap, 31,
 ,[Offset], Pop_Bumper, 32,
 ,[Offset], Gate_Back, 33,
 ,[Offset], Gate_Front, 34,
 ,[Offset], Scoop, 35,
 ,[Offset], Spinner_Back, 36,
 ,[Offset], Spinner_Front, 37,
 ,[Offset], Left_Opto, 38,
 ,[Offset], Right_Opto, 39,
 ,[Offset], Stopper_Post, 40,
 ,[Offset], Frank, 41,
 ,[Offset], Gate, 42,
 ,[Offset], Blue_Rubber, 43,
RSID_TMONSTERBASH_PLACEMENT, 3283,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TMONSTERBASH_EMUROM, 3284,,,
 ,[Offset], mb_10, 0,
 ,[Offset], mb_10, 1,
 ,[Offset], mb_10, 2,
RSID_TMONSTERBASH_SOUNDS_START, 3285,,,
RSID_TMONSTERBASH_EMU_SOUNDS, 3286,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP, 1,
 ,[Offset], S0003-LP1, 2,
 ,[Offset], S0003-LP2, 3,
 ,[Offset], S0004-LP1, 4,
 ,[Offset], S0005-LP, 5,
 ,[Offset], S0006-LP1, 6,
 ,[Offset], S0007-LP, 7,
 ,[Offset], S0008_C3, 8,
 ,[Offset], S0009-LP1, 9,
 ,[Offset], S000A-LP, 10,
 ,[Offset], S000B-LP, 11,
 ,[Offset], S000C_C3, 12,
 ,[Offset], S000D-LP1, 13,
 ,[Offset], S000E-LP, 14,
 ,[Offset], S000F-LP1, 15,
 ,[Offset], S0010-LP, 16,
 ,[Offset], S0011_C3, 17,
 ,[Offset], S0012_C3, 18,
 ,[Offset], S0013-LP1, 19,
 ,[Offset], S0014-LP, 20,
 ,[Offset], S0016-LP1, 21,
 ,[Offset], S0017-LP, 22,
 ,[Offset], S0018-LP, 23,
 ,[Offset], S0019-LP1, 24,
 ,[Offset], S001A-LP, 25,
 ,[Offset], S001B-LP1, 26,
 ,[Offset], S001B-LP2, 27,
 ,[Offset], S001C-LP, 28,
 ,[Offset], S0064_C4, 29,
 ,[Offset], S0074_C4, 30,
 ,[Offset], S0078_C4, 31,
 ,[Offset], S007C_C4, 32,
 ,[Offset], S0080_C4, 33,
 ,[Offset], S0088_C4, 34,
 ,[Offset], S008C_C4, 35,
 ,[Offset], S0090_C4, 36,
 ,[Offset], S0094_C4, 37,
 ,[Offset], S0098_C4, 38,
 ,[Offset], S00A0_C4, 39,
 ,[Offset], S00A4_C4, 40,
 ,[Offset], S00A8_C4, 41,
 ,[Offset], S00B0_C4, 42,
 ,[Offset], S00B4_C4, 43,
 ,[Offset], S00B8_C4, 44,
 ,[Offset], S00BC_C4, 45,
 ,[Offset], S00C0_C4, 46,
 ,[Offset], S00C4_C4, 47,
 ,[Offset], S00C8_C4, 48,
 ,[Offset], S00CC_C4, 49,
 ,[Offset], S00D4_C4, 50,
 ,[Offset], S00D8_C4, 51,
 ,[Offset], S00DC_C4, 52,
 ,[Offset], S00E0_C4, 53,
 ,[Offset], S00E4_C4, 54,
 ,[Offset], S00E8_C4, 55,
 ,[Offset], S00EC_C4, 56,
 ,[Offset], S00F0_C4, 57,
 ,[Offset], S00F4_C4, 58,
 ,[Offset], S00FC_C4, 59,
 ,[Offset], S0100_C4, 60,
 ,[Offset], S0104_C4, 61,
 ,[Offset], S0108_C4, 62,
 ,[Offset], S010C_C4, 63,
 ,[Offset], S0110_C4, 64,
 ,[Offset], S0114_C4, 65,
 ,[Offset], S0118_C4, 66,
 ,[Offset], S011C_C4, 67,
 ,[Offset], S0120_C4, 68,
 ,[Offset], S0124_C4, 69,
 ,[Offset], S0128_C4, 70,
 ,[Offset], S0130_C4, 71,
 ,[Offset], S0134_C4, 72,
 ,[Offset], S0138_C4, 73,
 ,[Offset], S0140_C4, 74,
 ,[Offset], S0144_C4, 75,
 ,[Offset], S0148_C4, 76,
 ,[Offset], S014C_C4, 77,
 ,[Offset], S0150_C4, 78,
 ,[Offset], S0154_C4, 79,
 ,[Offset], S0164_C4, 80,
 ,[Offset], S0168_C4, 81,
 ,[Offset], S016C_C4, 82,
 ,[Offset], S0170_C4, 83,
 ,[Offset], S0174_C4, 84,
 ,[Offset], S0178_C4, 85,
 ,[Offset], S017C_C4, 86,
 ,[Offset], S0184_C4, 87,
 ,[Offset], S0188_C4, 88,
 ,[Offset], S018C_C4, 89,
 ,[Offset], S0194_C4, 90,
 ,[Offset], S0198_C4, 91,
 ,[Offset], S019C_C4, 92,
 ,[Offset], S01A0_C4, 93,
 ,[Offset], S01A4_C4, 94,
 ,[Offset], S01A8_C4, 95,
 ,[Offset], S01AC_C4, 96,
 ,[Offset], S01B0_C4, 97,
 ,[Offset], S01B4_C4, 98,
 ,[Offset], S01BC_C4, 99,
 ,[Offset], S01C0_C4, 100,
 ,[Offset], S01C4_C4, 101,
 ,[Offset], S01C8_C4, 102,
 ,[Offset], S01CC_C4, 103,
 ,[Offset], S01D0_C4, 104,
 ,[Offset], S01D4_C4, 105,
 ,[Offset], S01D8_C4, 106,
 ,[Offset], S01E0_C4, 107,
 ,[Offset], S01E4_C4, 108,
 ,[Offset], S01E8_C4, 109,
 ,[Offset], S01EC_C4, 110,
 ,[Offset], S01F0_C4, 111,
 ,[Offset], S01F4_C4, 112,
 ,[Offset], S01F8_C4, 113,
 ,[Offset], S01FC_C4, 114,
 ,[Offset], S0200_C4, 115,
 ,[Offset], S0204_C4, 116,
 ,[Offset], S0208_C4, 117,
 ,[Offset], S020C_C4, 118,
 ,[Offset], S0210_C4, 119,
 ,[Offset], S0214_C4, 120,
 ,[Offset], S0258_C6, 121,
 ,[Offset], S0259_C6, 122,
 ,[Offset], S025A_C6, 123,
 ,[Offset], S025B_C6, 124,
 ,[Offset], S025C_C6, 125,
 ,[Offset], S025D_C6, 126,
 ,[Offset], S025F_C6, 127,
 ,[Offset], S0260_C6, 128,
 ,[Offset], S0261_C6, 129,
 ,[Offset], S0262_C6, 130,
 ,[Offset], S0263_C6, 131,
 ,[Offset], S0264_C6, 132,
 ,[Offset], S0265_C6, 133,
 ,[Offset], S0266_C6, 134,
 ,[Offset], S0267_C6, 135,
 ,[Offset], S0268_C6, 136,
 ,[Offset], S026A_C6, 137,
 ,[Offset], S026B_C6, 138,
 ,[Offset], S026C_C6, 139,
 ,[Offset], S026D_C6, 140,
 ,[Offset], S026E_C6, 141,
 ,[Offset], S026F_C6, 142,
 ,[Offset], S0270_C6, 143,
 ,[Offset], S0271_C6, 144,
 ,[Offset], S0272_C6, 145,
 ,[Offset], S0273_C6, 146,
 ,[Offset], S0274_C6, 147,
 ,[Offset], S0275_C6, 148,
 ,[Offset], S0279_C6, 149,
 ,[Offset], S027A_C6, 150,
 ,[Offset], S027B_C6, 151,
 ,[Offset], S027C_C6, 152,
 ,[Offset], S027D_C6, 153,
 ,[Offset], S027E_C6, 154,
 ,[Offset], S027F_C6, 155,
 ,[Offset], S0280_C6, 156,
 ,[Offset], S0281_C6, 157,
 ,[Offset], S0282_C6, 158,
 ,[Offset], S0283_C6, 159,
 ,[Offset], S0284_C6, 160,
 ,[Offset], S0285_C6, 161,
 ,[Offset], S0286_C6, 162,
 ,[Offset], S0287_C6, 163,
 ,[Offset], S0288_C6, 164,
 ,[Offset], S0289_C6, 165,
 ,[Offset], S028A_C6, 166,
 ,[Offset], S028B_C6, 167,
 ,[Offset], S028E_C6, 168,
 ,[Offset], S028F_C6, 169,
 ,[Offset], S0290_C6, 170,
 ,[Offset], S0291_C6, 171,
 ,[Offset], S0292_C6, 172,
 ,[Offset], S0293_C6, 173,
 ,[Offset], S0294_C6, 174,
 ,[Offset], S0295_C6, 175,
 ,[Offset], S0296_C6, 176,
 ,[Offset], S0297_C6, 177,
 ,[Offset], S0298_C6, 178,
 ,[Offset], S0299_C6, 179,
 ,[Offset], S029A_C6, 180,
 ,[Offset], S029E_C6, 181,
 ,[Offset], S02A2_C6, 182,
 ,[Offset], S02A4_C6, 183,
 ,[Offset], S02A5_C6, 184,
 ,[Offset], S02A6_C6, 185,
 ,[Offset], S02A7_C6, 186,
 ,[Offset], S02A8_C6, 187,
 ,[Offset], S02A9_C6, 188,
 ,[Offset], S02AA_C6, 189,
 ,[Offset], S02AB_C6, 190,
 ,[Offset], S0337_C6, 191,
 ,[Offset], S033E_C6, 192,
 ,[Offset], S033F_C6, 193,
 ,[Offset], S0340_C6, 194,
 ,[Offset], S03D4_C-1, 195,
 ,[Offset], S03D5_C-1, 196,
 ,[Offset], S03D6_C5, 197,
 ,[Offset], S03D7_C5, 198,
 ,[Offset], S03D8_C5, 199,
 ,[Offset], S03D9_C-1, 200,
 ,[Offset], S03DA_C-1, 201,
 ,[Offset], S03DB_C5, 202,
 ,[Offset], S03DC_C5, 203,
 ,[Offset], S03DD_C5, 204,
 ,[Offset], S03DE_C-1, 205,
 ,[Offset], S07D0_C6, 206,
 ,[Offset], S07D1_C6, 207,
 ,[Offset], S07D2_C6, 208,
 ,[Offset], S07D3_C6, 209,
 ,[Offset], S07D4_C6, 210,
 ,[Offset], S07D5_C6, 211,
 ,[Offset], S07D6_C6, 212,
 ,[Offset], S07D7_C6, 213,
 ,[Offset], S07D8_C6, 214,
 ,[Offset], S07DA_C6, 215,
 ,[Offset], S07DB_C6, 216,
 ,[Offset], S07DD_C6, 217,
 ,[Offset], S07DE_C6, 218,
 ,[Offset], S07DF_C6, 219,
 ,[Offset], S07E0_C6, 220,
 ,[Offset], S07E1_C6, 221,
 ,[Offset], S07E2_C6, 222,
 ,[Offset], S07E3_C6, 223,
 ,[Offset], S07E4_C6, 224,
 ,[Offset], S07E6_C6, 225,
 ,[Offset], S07E7_C6, 226,
 ,[Offset], S07E8_C6, 227,
 ,[Offset], S07E9_C6, 228,
 ,[Offset], S07EC_C6, 229,
 ,[Offset], S07ED_C6, 230,
 ,[Offset], S07EE_C6, 231,
 ,[Offset], S07EF_C6, 232,
 ,[Offset], S07F0_C6, 233,
 ,[Offset], S07F1_C6, 234,
 ,[Offset], S07F2_C6, 235,
 ,[Offset], S07F3_C6, 236,
 ,[Offset], S07F5_C6, 237,
 ,[Offset], S07F6_C6, 238,
 ,[Offset], S07F7_C6, 239,
 ,[Offset], S07F9_C6, 240,
 ,[Offset], S07FA_C6, 241,
 ,[Offset], S07FB_C6, 242,
 ,[Offset], S07FC_C6, 243,
 ,[Offset], S07FF_C6, 244,
 ,[Offset], S0800_C6, 245,
 ,[Offset], S0801_C6, 246,
 ,[Offset], S0802_C6, 247,
 ,[Offset], S0803_C6, 248,
 ,[Offset], S0804_C6, 249,
 ,[Offset], S09C4_C6, 250,
 ,[Offset], S09C5_C6, 251,
 ,[Offset], S09C6_C6, 252,
 ,[Offset], S09C7_C6, 253,
 ,[Offset], S09C9_C6, 254,
 ,[Offset], S09CA_C6, 255,
 ,[Offset], S09CB_C6, 256,
 ,[Offset], S09CE_C6, 257,
 ,[Offset], S09CF_C6, 258,
 ,[Offset], S09D1_C6, 259,
 ,[Offset], S09D2_C6, 260,
 ,[Offset], S09D3_C6, 261,
 ,[Offset], S09D5_C6, 262,
 ,[Offset], S09D7_C6, 263,
 ,[Offset], S09D8_C6, 264,
 ,[Offset], S09D9_C6, 265,
 ,[Offset], S09DC_C6, 266,
 ,[Offset], S09E1_C6, 267,
 ,[Offset], S09E2_C6, 268,
 ,[Offset], S09E3_C6, 269,
 ,[Offset], S09E4_C6, 270,
 ,[Offset], S09E5_C6, 271,
 ,[Offset], S09E6_C6, 272,
 ,[Offset], S09F7_C6, 273,
 ,[Offset], S09F8_C6, 274,
 ,[Offset], S09FA_C6, 275,
 ,[Offset], S09FB_C6, 276,
 ,[Offset], S09FD_C6, 277,
 ,[Offset], S09FE_C6, 278,
 ,[Offset], S09FF_C6, 279,
 ,[Offset], S0A00_C6, 280,
 ,[Offset], S0A01_C6, 281,
 ,[Offset], S0A02_C6, 282,
 ,[Offset], S0A03_C6, 283,
 ,[Offset], S0AF0_C6, 284,
 ,[Offset], S0AF1_C6, 285,
 ,[Offset], S0AF3_C6, 286,
 ,[Offset], S0AF4_C6, 287,
 ,[Offset], S0AF5_C6, 288,
 ,[Offset], S0AFC_C6, 289,
 ,[Offset], S0AFD_C6, 290,
 ,[Offset], S0AFE_C6, 291,
 ,[Offset], S0AFF_C6, 292,
 ,[Offset], S0B00_C6, 293,
 ,[Offset], S0B01_C6, 294,
 ,[Offset], S0B02_C6, 295,
 ,[Offset], S0B04_C6, 296,
 ,[Offset], S0B05_C6, 297,
 ,[Offset], S0B09_C6, 298,
 ,[Offset], S0B0A_C6, 299,
 ,[Offset], S0B0B_C6, 300,
 ,[Offset], S0B0C_C6, 301,
 ,[Offset], S0B0D_C6, 302,
 ,[Offset], S0B0E_C6, 303,
 ,[Offset], S0BB8_C6, 304,
 ,[Offset], S0BB9_C6, 305,
 ,[Offset], S0BBA_C6, 306,
 ,[Offset], S0BBB_C6, 307,
 ,[Offset], S0BBC_C6, 308,
 ,[Offset], S0BBD_C6, 309,
 ,[Offset], S0BBE_C6, 310,
 ,[Offset], S0BBF_C6, 311,
 ,[Offset], S0BC0_C6, 312,
 ,[Offset], S0BC2_C6, 313,
 ,[Offset], S0BC3_C6, 314,
 ,[Offset], S0BC4_C6, 315,
 ,[Offset], S0BC8_C6, 316,
 ,[Offset], S0BC9_C6, 317,
 ,[Offset], S0BCA_C6, 318,
 ,[Offset], S0BCB_C6, 319,
 ,[Offset], S0BCC_C6, 320,
 ,[Offset], S0BCD_C6, 321,
 ,[Offset], S0BCE_C6, 322,
 ,[Offset], S0BCF_C6, 323,
 ,[Offset], S0BD0_C6, 324,
 ,[Offset], S0BD2_C6, 325,
 ,[Offset], S0BD3_C6, 326,
 ,[Offset], S0DAD_C6, 327,
 ,[Offset], S0DAE_C6, 328,
 ,[Offset], S0DAF_C6, 329,
 ,[Offset], S0DB0_C6, 330,
 ,[Offset], S0DB1_C6, 331,
 ,[Offset], S0DB3_C6, 332,
 ,[Offset], S0DB4_C6, 333,
 ,[Offset], S0DB6_C6, 334,
 ,[Offset], S0DB7_C6, 335,
 ,[Offset], S0DB8_C6, 336,
 ,[Offset], S0DB9_C6, 337,
 ,[Offset], S0DBA_C6, 338,
 ,[Offset], S0DBB_C6, 339,
 ,[Offset], S0DBC_C6, 340,
 ,[Offset], S0DBD_C6, 341,
 ,[Offset], S0DBE_C6, 342,
 ,[Offset], S0DBF_C6, 343,
 ,[Offset], S0DC0_C6, 344,
 ,[Offset], S0DC1_C6, 345,
 ,[Offset], S0DC2_C6, 346,
 ,[Offset], S0DC3_C6, 347,
 ,[Offset], S0DC4_C6, 348,
 ,[Offset], S0DC5_C6, 349,
 ,[Offset], S0DC6_C6, 350,
 ,[Offset], S0DC7_C6, 351,
 ,[Offset], S0DC8_C6, 352,
 ,[Offset], S0DC9_C6, 353,
 ,[Offset], S0DCA_C6, 354,
 ,[Offset], S0DCB_C6, 355,
 ,[Offset], S0DCC_C6, 356,
 ,[Offset], S0DCD_C6, 357,
 ,[Offset], S0DCE_C6, 358,
 ,[Offset], S0DCF_C6, 359,
 ,[Offset], S0DD0_C6, 360,
 ,[Offset], S0DD1_C6, 361,
 ,[Offset], S0DD2_C6, 362,
 ,[Offset], S0DD3_C6, 363,
 ,[Offset], S0DD4_C6, 364,
 ,[Offset], S0DD5_C6, 365,
 ,[Offset], S0DD6_C6, 366,
 ,[Offset], S0DD7_C6, 367,
 ,[Offset], S0DD8_C6, 368,
 ,[Offset], S0DD9_C6, 369,
 ,[Offset], S0DDA_C6, 370,
 ,[Offset], S0DDB_C6, 371,
 ,[Offset], S0DDC_C6, 372,
 ,[Offset], S0ED9_C6, 373,
 ,[Offset], S0EDD_C6, 374,
 ,[Offset], S0EDE_C6, 375,
 ,[Offset], S0FA0_C6, 376,
 ,[Offset], S0FA1_C6, 377,
 ,[Offset], S1194, 378,
 ,[Offset], S1195, 379,
 ,[Offset], S1196, 380,
 ,[Offset], S1197, 381,
 ,[Offset], S1198, 382,
 ,[Offset], S1199, 383,
 ,[Offset], S119A, 384,
 ,[Offset], S119B, 385,
 ,[Offset], S119C, 386,
 ,[Offset], S12C0, 387,
 ,[Offset], S12C1, 388,
 ,[Offset], S1324, 389,
 ,[Offset], S1388, 390,
 ,[Offset], S1389, 391,
 ,[Offset], S138A, 392,
 ,[Offset], S138B, 393,
 ,[Offset], S138C, 394,
 ,[Offset], S138D, 395,
 ,[Offset], S138E, 396,
 ,[Offset], S138F, 397,
 ,[Offset], S1390, 398,
 ,[Offset], S1391, 399,
 ,[Offset], S1392, 400,
 ,[Offset], S1393, 401,
 ,[Offset], S1394, 402,
 ,[Offset], S1395, 403,
 ,[Offset], S1396, 404,
 ,[Offset], S1397, 405,
 ,[Offset], S1398, 406,
 ,[Offset], S1399, 407,
 ,[Offset], S139A, 408,
 ,[Offset], S139B, 409,
 ,[Offset], S139C, 410,
 ,[Offset], S139D, 411,
 ,[Offset], S139E, 412,
 ,[Offset], S139F, 413,
 ,[Offset], S13A0, 414,
 ,[Offset], S13A1, 415,
 ,[Offset], S13A2, 416,
 ,[Offset], S13A3, 417,
 ,[Offset], S13A4, 418,
 ,[Offset], S13A5, 419,
 ,[Offset], S13A6, 420,
 ,[Offset], S13A7, 421,
 ,[Offset], S13A8, 422,
 ,[Offset], S13A9, 423,
 ,[Offset], S13AA, 424,
 ,[Offset], S13AB, 425,
 ,[Offset], S13AC, 426,
RSID_TMONSTERBASH_SOUNDS_END, 3287,,,
RSID_TMONSTERBASH_SAMPLES, 3288,,,
RSID_TMONSTERBASH_VERSION, 3289,,,
RSID_TMONSTERBASH_END, 3290,,,
