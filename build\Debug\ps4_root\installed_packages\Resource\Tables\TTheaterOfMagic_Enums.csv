RSID_TABLE_THEATEROFMAGIC_START, 2725,,,
 ,[Offset], PS3_360\Theatre_flyer_1, 0,
 ,[Offset], TheaterOfMagic\PBTTheaterOfMagic, 1,
 ,[Offset], TheaterOfMagic\InstructionsENG, 2,
 ,[Offset], TheaterOfMagic\InstructionsFR, 3,
 ,[Offset], TheaterOfMagic\InstructionsITAL, 4,
 ,[Offset], TheaterOfMagic\InstructionsGERM, 5,
 ,[Offset], TheaterOfMagic\InstructionsSPAN, 6,
RSID_TABLE_THEATEROFMAGIC_LIGHTS, 2726,,,
RSID_TABLE_THEATEROFMAGIC_CAMERAS, 2727,,,
RSID_TABLE_THEATEROFMAGIC_LAMP_TEXTURES, 2728,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_85_Off, 114,
 ,[Offset], L_85_On, 115,
 ,[Offset], L_86_Off, 116,
 ,[Offset], L_86_On, 117,
 ,[Offset], L_87_Off, 118,
 ,[Offset], L_87_On, 119,
 ,[Offset], L_88_Off, 120,
 ,[Offset], L_88_On, 121,
 ,[Offset], S_11_Off, 122,
 ,[Offset], S_11_On, 123,
 ,[Offset], S_12_Off, 124,
 ,[Offset], S_12_On, 125,
 ,[Offset], S_13_Off, 126,
 ,[Offset], S_13_On, 127,
 ,[Offset], S_20_Off, 128,
 ,[Offset], S_20_On, 129,
 ,[Offset], S_23_Off, 130,
 ,[Offset], S_23_On, 131,
 ,[Offset], S_25_Off, 132,
 ,[Offset], S_25_On, 133,
 ,[Offset], S_26_Off, 134,
 ,[Offset], S_26_On, 135,
 ,[Offset], S_27_Off, 136,
 ,[Offset], S_27_On, 137,
 ,[Offset], L_54B_Off, 138,
 ,[Offset], L_54B_On, 139,
 ,[Offset], S_24_Off, 140,
 ,[Offset], S_24_On, 141,
 ,[Offset], S_28_Off, 142,
 ,[Offset], S_28_On, 143,
 ,[Offset], F_11_Off, 144,
 ,[Offset], F_11_On, 145,
 ,[Offset], F_12_Off, 146,
 ,[Offset], F_12_On, 147,
 ,[Offset], F_13_Off, 148,
 ,[Offset], F_13_On, 149,
 ,[Offset], S_13_Off_s, 150,
 ,[Offset], S_12_Off_s, 151,
 ,[Offset], S_11_Off_s, 152,
 ,[Offset], S_27B_Off, 153,
 ,[Offset], S_27B_On, 154,
 ,[Offset], Lower_Ramp_Off, 155,
 ,[Offset], Lower_Ramp_On, 156,
 ,[Offset], Upper_Ramp_Off, 157,
 ,[Offset], Upper_Ramp_On, 158,
 ,[Offset], S_28B_Off, 159,
 ,[Offset], S_28B_On, 160,
 ,[Offset], L_26B_Off, 161,
 ,[Offset], L_26B_On, 162,
RSID_TABLE_THEATEROFMAGIC_TEXTURES, 2729,,,
 ,[Offset], Extra_Ball_button, 0,
 ,[Offset], Plunger, 1,
 ,[Offset], Flipper_Button, 2,
 ,[Offset], Black_Grain, 3,
 ,[Offset], Extra_Metal_Parts, 4,
 ,[Offset], Metal_Parts, 5,
 ,[Offset], Cabinet, 6,
 ,[Offset], Cabinet_Front, 7,
 ,[Offset], Cabinet_Head, 8,
 ,[Offset], Coin_Slot, 9,
 ,[Offset], Screen_Speakers, 10,
 ,[Offset], Blue_Plastic, 11,
 ,[Offset], mirror, 12,
 ,[Offset], mirror_s, 13,
 ,[Offset], Apron, 14,
 ,[Offset], BoxFrame, 15,
 ,[Offset], BoxWalls, 16,
 ,[Offset], ChestMetalFront, 17,
 ,[Offset], EntranceRampUVS, 18,
 ,[Offset], Flasher_Blue, 19,
 ,[Offset], Flasher_Green, 20,
 ,[Offset], Flipper, 21,
 ,[Offset], Generic_Metal, 22,
 ,[Offset], Generic_Metal_s, 23,
 ,[Offset], Habit_Trail_Straight, 24,
 ,[Offset], Habit_Trail_Wavy, 25,
 ,[Offset], Metal_Guide, 26,
 ,[Offset], Metal_Guide_s, 27,
 ,[Offset], Plastic_Post_Red, 28,
 ,[Offset], PlasticBumper, 29,
 ,[Offset], PlasticBumper_s, 30,
 ,[Offset], Playfield_Bottom, 31,
 ,[Offset], Playfield_Top, 32,
 ,[Offset], Red_Plastic_Post, 33,
 ,[Offset], Rubber_Band_Black, 34,
 ,[Offset], Rubber_Band_Black_s, 35,
 ,[Offset], Rubberband_temp, 36,
 ,[Offset], Rules, 37,
 ,[Offset], Spinner, 38,
 ,[Offset], SpiritRing, 39,
 ,[Offset], Target_Orange, 40,
 ,[Offset], Target_White, 41,
 ,[Offset], Target_Yellow, 42,
 ,[Offset], TrapDoor, 43,
 ,[Offset], Rubber Post_Temp, 44,
 ,[Offset], Silver Metal Screws_Temp, 45,
 ,[Offset], Silver Metal Screws_Temp_s, 46,
 ,[Offset], Harley_Spinner, 47,
 ,[Offset], Harley_Spinner_s, 48,
 ,[Offset], Generic_Screw, 49,
 ,[Offset], Generic_Screw_s, 50,
 ,[Offset], HarleyBumperBody, 51,
 ,[Offset], HarleyBumperBody_s, 52,
 ,[Offset], Bumper_Sensors, 53,
 ,[Offset], Bumper_Hamer, 54,
 ,[Offset], Metal_Temp, 55,
 ,[Offset], BlackMatte_Temp, 56,
 ,[Offset], Harley_Gate, 57,
 ,[Offset], FlipperLane_Plastic, 58,
 ,[Offset], Plastics_01, 59,
 ,[Offset], Plastics_02, 60,
 ,[Offset], Plastics_03, 61,
 ,[Offset], Plastics_04, 62,
 ,[Offset], Plastics_05, 63,
 ,[Offset], Plastics_06, 64,
 ,[Offset], bulb1, 65,
 ,[Offset], Metal_Walls, 66,
 ,[Offset], Metal_Walls_s, 67,
 ,[Offset], Plastic_Ramp_01, 68,
 ,[Offset], Plastic_Ramp_02, 69,
 ,[Offset], Orange_Plastic_Post, 70,
 ,[Offset], Plastic_Ramp_01Stickers, 71,
 ,[Offset], Plastic_Ramp_02Stickers, 72,
 ,[Offset], Tiger_Metal, 73,
 ,[Offset], Chainsaw_Wheel, 74,
 ,[Offset], Back_Wall, 75,
 ,[Offset], Flasher_Orange, 76,
 ,[Offset], RampLip, 77,
 ,[Offset], Chest_Edges, 78,
 ,[Offset], NewFrame, 79,
 ,[Offset], Mirror_Details, 80,
 ,[Offset], Rails, 81,
 ,[Offset], generic_screw_gold, 82,
RSID_TABLE_THEATEROFMAGIC_MODELS, 2730,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet, 1,
 ,[Offset], Flashers, 2,
 ,[Offset], Flipper_Lane_Plastics, 3,
 ,[Offset], Flipper_Left, 4,
 ,[Offset], Flipper_Right, 5,
 ,[Offset], Habbi_Trails, 6,
 ,[Offset], Light_Bulbs, 7,
 ,[Offset], Metal_Gate, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Walls, 10,
 ,[Offset], Mirror, 11,
 ,[Offset], Plastic_Posts, 12,
 ,[Offset], Plastics, 13,
 ,[Offset], Playfield, 14,
 ,[Offset], Plunger, 15,
 ,[Offset], Plunger_Shot, 16,
 ,[Offset], Pop_Bumper_Metal, 17,
 ,[Offset], Pop_Bumpers, 18,
 ,[Offset], Rubbers, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Right, 21,
 ,[Offset], Spinner, 22,
 ,[Offset], Spirit_Ring_Magnet, 23,
 ,[Offset], Target_Orange, 24,
 ,[Offset], Target_Round, 25,
 ,[Offset], Target_White, 26,
 ,[Offset], Tiger_Saw, 27,
 ,[Offset], Trap_Door, 28,
 ,[Offset], Trapped_Ball, 29,
 ,[Offset], Trunk, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Wooden_Rails, 32,
 ,[Offset], Crossing_Habbi_Trail, 33,
 ,[Offset], Left_Ramp, 34,
 ,[Offset], Right_Habbi_Trail, 35,
 ,[Offset], Upper_Ramp, 36,
 ,[Offset], Ball_Lock_Diverter, 37,
 ,[Offset], One_Way_Gate_A, 38,
 ,[Offset], One_Way_Gate_B, 39,
 ,[Offset], One_Way_Gate_C, 40,
 ,[Offset], One_Way_Gate_D, 41,
 ,[Offset], One_Way_Gate_E, 42,
 ,[Offset], Gate_A, 43,
 ,[Offset], Ramp_Wire_A, 44,
 ,[Offset], Ramp_Wire_B, 45,
 ,[Offset], Ramp_Wire_C, 46,
 ,[Offset], Ramp_Wire_D, 47,
 ,[Offset], Light_Cutouts, 48,
 ,[Offset], Safe_Escape_Light, 49,
 ,[Offset], Cabinet_Backglass, 50,
 ,[Offset], Cabinet_Interior, 51,
 ,[Offset], Cabinet_Metals, 52,
 ,[Offset], Back_Wall, 53,
 ,[Offset], Jet_Flashers, 54,
 ,[Offset], ReturnLane_Flashers, 55,
 ,[Offset], Ramp_Stickers, 56,
 ,[Offset], Mirror_LightCutouts, 57,
 ,[Offset], Mirror_MetalPieces, 58,
 ,[Offset], Mirror_MetalWall, 59,
 ,[Offset], Mirror_PlasticPieces, 60,
 ,[Offset], Mirror_PlasticPosts, 61,
 ,[Offset], Mirror_Playfield, 62,
 ,[Offset], Mirror_PopBumper, 63,
 ,[Offset], Mirror_Rubbers, 64,
 ,[Offset], Mirror_Left_Ramp, 65,
 ,[Offset], Mirror_Flashers, 66,
 ,[Offset], Flashers_Green, 67,
 ,[Offset], Slingshot_Left_Extended, 68,
 ,[Offset], Slingshot_Right_Extended, 69,
RSID_TABLE_THEATEROFMAGIC_MODELS_LODS, 2731,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet, 1,
 ,[Offset], Flashers, 2,
 ,[Offset], Flipper_Lane_Plastics, 3,
 ,[Offset], Flipper_Left, 4,
 ,[Offset], Flipper_Right, 5,
 ,[Offset], Habbi_Trails, 6,
 ,[Offset], Light_Bulbs, 7,
 ,[Offset], Metal_Gate, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Walls, 10,
 ,[Offset], Mirror, 11,
 ,[Offset], Plastic_Posts, 12,
 ,[Offset], Plastics, 13,
 ,[Offset], Playfield, 14,
 ,[Offset], Plunger, 15,
 ,[Offset], Plunger_Shot, 16,
 ,[Offset], Pop_Bumper_Metal, 17,
 ,[Offset], Pop_Bumpers, 18,
 ,[Offset], Rubbers, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Right, 21,
 ,[Offset], Spinner, 22,
 ,[Offset], Spirit_Ring_Magnet, 23,
 ,[Offset], Target_Orange, 24,
 ,[Offset], Target_Round, 25,
 ,[Offset], Target_White, 26,
 ,[Offset], Tiger_Saw, 27,
 ,[Offset], Trap_Door, 28,
 ,[Offset], Trapped_Ball, 29,
 ,[Offset], Trunk, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Wooden_Rails, 32,
 ,[Offset], Crossing_Habbi_Trail, 33,
 ,[Offset], Left_Ramp, 34,
 ,[Offset], Right_Habbi_Trail, 35,
 ,[Offset], Upper_Ramp, 36,
 ,[Offset], Ball_Lock_Diverter, 37,
 ,[Offset], One_Way_Gate_A, 38,
 ,[Offset], One_Way_Gate_B, 39,
 ,[Offset], One_Way_Gate_C, 40,
 ,[Offset], One_Way_Gate_D, 41,
 ,[Offset], One_Way_Gate_E, 42,
 ,[Offset], Gate_A, 43,
 ,[Offset], Ramp_Wire_A, 44,
 ,[Offset], Ramp_Wire_B, 45,
 ,[Offset], Ramp_Wire_C, 46,
 ,[Offset], Ramp_Wire_D, 47,
 ,[Offset], Light_Cutouts, 48,
 ,[Offset], Safe_Escape_Light, 49,
 ,[Offset], Cabinet_Backglass, 50,
 ,[Offset], Cabinet_Interior, 51,
 ,[Offset], Cabinet_Metals, 52,
 ,[Offset], Back_Wall, 53,
 ,[Offset], Jet_Flashers, 54,
 ,[Offset], ReturnLane_Flashers, 55,
 ,[Offset], Ramp_Stickers, 56,
 ,[Offset], Mirror_LightCutouts, 57,
 ,[Offset], Mirror_MetalPieces, 58,
 ,[Offset], Mirror_MetalWall, 59,
 ,[Offset], Mirror_PlasticPieces, 60,
 ,[Offset], Mirror_PlasticPosts, 61,
 ,[Offset], Mirror_Playfield, 62,
 ,[Offset], Mirror_PopBumper, 63,
 ,[Offset], Mirror_Rubbers, 64,
 ,[Offset], Mirror_Left_Ramp, 65,
 ,[Offset], Mirror_Flashers, 66,
 ,[Offset], Flashers_Green, 67,
 ,[Offset], Slingshot_Left_Extended, 68,
 ,[Offset], Slingshot_Right_Extended, 69,
RSID_TABLE_THEATEROFMAGIC_COLLISIONS, 2732,,,
 ,[Offset], Apron_COL, 0,
 ,[Offset], Ball_Drain_COL, 1,
 ,[Offset], Flipper_Lane_COL, 2,
 ,[Offset], Flipper_Left_Back_COL, 3,
 ,[Offset], Flipper_Left_Front_COL, 4,
 ,[Offset], Flipper_Right_Back_COL, 5,
 ,[Offset], Flipper_Right_Front_COL, 6,
 ,[Offset], Flipper_Right_Front_COL, 7,
 ,[Offset], Outer_Wall_COL, 8,
 ,[Offset], Playfield_COL, 9,
 ,[Offset], Plunger_Shot_COL, 10,
 ,[Offset], Slingshot_Left_COL, 11,
 ,[Offset], Slingshot_Left_Front_COL, 12,
 ,[Offset], Slingshot_Right_COL, 13,
 ,[Offset], Slingshot_Right_Front_COL, 14,
 ,[Offset], Trunk_COL, 15,
 ,[Offset], Orange_Target_COL, 16,
 ,[Offset], Plunger_COL, 17,
 ,[Offset], Plunger_Lane_COL, 18,
 ,[Offset], Pop_Bumper_COL, 19,
 ,[Offset], Round_Target_COL, 20,
 ,[Offset], Spinner_COL, 21,
 ,[Offset], White_Target_COL, 22,
 ,[Offset], Wooden_Rail_COL, 23,
 ,[Offset], Crossing_Habbi_Trail_COL, 24,
 ,[Offset], Crossing_Habbi_Trail_COL, 25,
 ,[Offset], Right_Habbi_Trail_COL, 26,
 ,[Offset], Right_Habbi_Trail_COL, 27,
 ,[Offset], Trap_Door_COL, 28,
 ,[Offset], Trap_Door_Moving_COL, 29,
 ,[Offset], Trap_Trunk_COL, 30,
 ,[Offset], Spirit_Magnet_COL, 31,
 ,[Offset], Trunk_Hole_COL, 32,
 ,[Offset], Trunk_Magnet_COL, 33,
 ,[Offset], Ball_Lock_Diverter_COL, 34,
 ,[Offset], Left_Lane_Magnet_COL, 35,
 ,[Offset], Right_Lane_Magnet_COL, 36,
 ,[Offset], One_Way_Gate_A_COL, 37,
 ,[Offset], One_Way_Gate_A_Back_COL, 38,
 ,[Offset], One_Way_Gate_B_COL, 39,
 ,[Offset], One_Way_Gate_B_Back_COL, 40,
 ,[Offset], One_Way_Gate_C_COL, 41,
 ,[Offset], One_Way_Gate_C_Back_COL, 42,
 ,[Offset], One_Way_Gate_D_COL, 43,
 ,[Offset], One_Way_Gate_D_Back_COL, 44,
 ,[Offset], One_Way_Gate_E_COL, 45,
 ,[Offset], One_Way_Gate_E_Back_COL, 46,
 ,[Offset], Gate_A_COL, 47,
 ,[Offset], Vanishing_Ball_COL, 48,
 ,[Offset], Trapped_Ball_COL, 49,
 ,[Offset], Optical_Trunk_COL, 50,
 ,[Offset], Inner_Walls_A_COL, 51,
 ,[Offset], Inner_Walls_B_COL, 52,
 ,[Offset], Inner_Walls_C_COL, 53,
 ,[Offset], Inner_Walls_D_COL, 54,
 ,[Offset], Inner_Walls_E_COL, 55,
 ,[Offset], Inner_Walls_F_COL, 56,
 ,[Offset], Lower_Ramp_A_COL, 57,
 ,[Offset], Lower_Ramp_B_COL, 58,
 ,[Offset], Lower_Ramp_C_COL, 59,
 ,[Offset], Upper_Ramp_A_COL, 60,
 ,[Offset], Upper_Ramp_B_COL, 61,
 ,[Offset], Upper_Ramp_C_COL, 62,
 ,[Offset], Rubber_Post_A_COL, 63,
 ,[Offset], Rubber_Post_B_COL, 64,
 ,[Offset], Rubber_Post_C_COL, 65,
 ,[Offset], Rubber_Post_D_COL, 66,
RSID_TABLE_THEATEROFMAGIC_PLACEMENT, 2733,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_TABLE_THEATEROFMAGIC_ROM, 2734,,,
 ,[Offset], TOM1_3X, 0,
 ,[Offset], tom_13_Attract, 1,
 ,[Offset], tom_13_1P, 2,
 ,[Offset], tom_13_2P, 3,
 ,[Offset], tom_13_3P, 4,
 ,[Offset], tom_13_4P, 5,
 ,[Offset], tom_13, 6,
 ,[Offset], tom_13, 7,
 ,[Offset], tom_13, 8,
RSID_TABLE_THEATEROFMAGIC_SOUNDS_START, 2735,,,
RSID_TABLE_THEATEROFMAGIC_EMU_SOUNDS, 2736,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP, 1,
 ,[Offset], S0005-LP, 2,
 ,[Offset], S0006-LP, 3,
 ,[Offset], S0007_C5, 4,
 ,[Offset], S0008-LP, 5,
 ,[Offset], S0009-LP, 6,
 ,[Offset], S000B-LP, 7,
 ,[Offset], S000C-LP, 8,
 ,[Offset], S000D-LP, 9,
 ,[Offset], S000E_C3, 10,
 ,[Offset], S000F-LP1, 11,
 ,[Offset], S000F-LP2, 12,
 ,[Offset], S0010_C3, 13,
 ,[Offset], S0029_C6, 14,
 ,[Offset], S0032_C6, 15,
 ,[Offset], S0033_C6, 16,
 ,[Offset], S0034_C6, 17,
 ,[Offset], S0035_C4, 18,
 ,[Offset], S0036_C4, 19,
 ,[Offset], S0037_C4, 20,
 ,[Offset], S0038_C5, 21,
 ,[Offset], S0039_C5, 22,
 ,[Offset], S003A_C4, 23,
 ,[Offset], S003B_C4, 24,
 ,[Offset], S003C_C4, 25,
 ,[Offset], S003D_C4, 26,
 ,[Offset], S003E_C4, 27,
 ,[Offset], S003F_C6, 28,
 ,[Offset], S0046_C6, 29,
 ,[Offset], S0047_C6, 30,
 ,[Offset], S0064_C4, 31,
 ,[Offset], S0065_C6, 32,
 ,[Offset], S0066_C6, 33,
 ,[Offset], S0067_C6, 34,
 ,[Offset], S0068_C4, 35,
 ,[Offset], S0069_C4, 36,
 ,[Offset], S006B_C6, 37,
 ,[Offset], S006C_C6, 38,
 ,[Offset], S006D_C4, 39,
 ,[Offset], S006E_C4, 40,
 ,[Offset], S006F_C4, 41,
 ,[Offset], S0070_C4, 42,
 ,[Offset], S0071_C4, 43,
 ,[Offset], S0072_C4, 44,
 ,[Offset], S0073_C4, 45,
 ,[Offset], S0074_C5, 46,
 ,[Offset], S0075_C6, 47,
 ,[Offset], S0076_C4, 48,
 ,[Offset], S0077_C6, 49,
 ,[Offset], S0078_C4, 50,
 ,[Offset], S0079_C4, 51,
 ,[Offset], S007A_C6, 52,
 ,[Offset], S007B_C4, 53,
 ,[Offset], S007C_C4, 54,
 ,[Offset], S007D_C6, 55,
 ,[Offset], S007E_C4, 56,
 ,[Offset], S007F_C4, 57,
 ,[Offset], S0080_C4, 58,
 ,[Offset], S0081_C4, 59,
 ,[Offset], S0082_C4, 60,
 ,[Offset], S0085_C4, 61,
 ,[Offset], S0086_C4, 62,
 ,[Offset], S0087_C4, 63,
 ,[Offset], S0088_C4, 64,
 ,[Offset], S008A_C4, 65,
 ,[Offset], S008E_C4, 66,
 ,[Offset], S008F_C4, 67,
 ,[Offset], S0090_C4, 68,
 ,[Offset], S0091_C4, 69,
 ,[Offset], S0092_C4, 70,
 ,[Offset], S0093_C4, 71,
 ,[Offset], S0094_C4, 72,
 ,[Offset], S0095_C4, 73,
 ,[Offset], S0097_C5, 74,
 ,[Offset], S0098_C4, 75,
 ,[Offset], S0099_C4, 76,
 ,[Offset], S009B_C4, 77,
 ,[Offset], S009C_C4, 78,
 ,[Offset], S009D_C4, 79,
 ,[Offset], S009E_C4, 80,
 ,[Offset], S009F_C4, 81,
 ,[Offset], S00C7_C4, 82,
 ,[Offset], S00C8_C4, 83,
 ,[Offset], S00C9_C6, 84,
 ,[Offset], S00CA_C5, 85,
 ,[Offset], S00CB_C5, 86,
 ,[Offset], S00CC_C5, 87,
 ,[Offset], S00CD_C5, 88,
 ,[Offset], S00CE_C5, 89,
 ,[Offset], S00CF_C6, 90,
 ,[Offset], S012C_C3, 91,
 ,[Offset], S012D_C3, 92,
 ,[Offset], S012E_C3, 93,
 ,[Offset], S0190_C4, 94,
 ,[Offset], S01F4_C5, 95,
 ,[Offset], S01F6_C5, 96,
 ,[Offset], S01F7_C5, 97,
 ,[Offset], S01F8_C5, 98,
 ,[Offset], S01F9_C5, 99,
 ,[Offset], S01FA_C5, 100,
 ,[Offset], S01FB_C5, 101,
 ,[Offset], S01FE_C5, 102,
 ,[Offset], S01FF_C5, 103,
 ,[Offset], S0200_C5, 104,
 ,[Offset], S0201_C5, 105,
 ,[Offset], S0202_C5, 106,
 ,[Offset], S0210_C5, 107,
 ,[Offset], S0211_C5, 108,
 ,[Offset], S0212_C5, 109,
 ,[Offset], S0213_C5, 110,
 ,[Offset], S0214_C5, 111,
 ,[Offset], S0215_C5, 112,
 ,[Offset], S0216_C5, 113,
 ,[Offset], S0217_C5, 114,
 ,[Offset], S0218_C5, 115,
 ,[Offset], S0219_C5, 116,
 ,[Offset], S021A_C5, 117,
 ,[Offset], S021B_C5, 118,
 ,[Offset], S021C_C5, 119,
 ,[Offset], S021D_C5, 120,
 ,[Offset], S021E_C5, 121,
 ,[Offset], S021F_C5, 122,
 ,[Offset], S0220_C5, 123,
 ,[Offset], S0222_C5, 124,
 ,[Offset], S0223_C5, 125,
 ,[Offset], S0224_C5, 126,
 ,[Offset], S0225_C5, 127,
 ,[Offset], S0226_C5, 128,
 ,[Offset], S0227_C5, 129,
 ,[Offset], S0228_C5, 130,
 ,[Offset], S0229_C5, 131,
 ,[Offset], S022A_C5, 132,
 ,[Offset], S022C_C5, 133,
 ,[Offset], S022D_C5, 134,
 ,[Offset], S022E_C5, 135,
 ,[Offset], S022F_C5, 136,
 ,[Offset], S0230_C5, 137,
 ,[Offset], S0234_C5, 138,
 ,[Offset], S0237_C5, 139,
 ,[Offset], S023A_C5, 140,
 ,[Offset], S023B_C5, 141,
 ,[Offset], S023D_C5, 142,
 ,[Offset], S023E_C5, 143,
 ,[Offset], S0241_C5, 144,
 ,[Offset], S0242_C5, 145,
 ,[Offset], S0244_C5, 146,
 ,[Offset], S0247_C5, 147,
 ,[Offset], S0249_C5, 148,
 ,[Offset], S024A_C5, 149,
 ,[Offset], S024B_C5, 150,
 ,[Offset], S024C_C5, 151,
 ,[Offset], S024D_C5, 152,
 ,[Offset], S024E_C5, 153,
 ,[Offset], S024F_C5, 154,
 ,[Offset], S0250_C5, 155,
 ,[Offset], S0252_C5, 156,
 ,[Offset], S0255_C5, 157,
 ,[Offset], S025A_C5, 158,
 ,[Offset], S025C_C5, 159,
 ,[Offset], S025D_C5, 160,
 ,[Offset], S025E_C5, 161,
 ,[Offset], S025F_C5, 162,
 ,[Offset], S0260_C5, 163,
 ,[Offset], S0261_C5, 164,
 ,[Offset], S0262_C5, 165,
 ,[Offset], S0263_C5, 166,
 ,[Offset], S0264_C5, 167,
 ,[Offset], S0265_C5, 168,
 ,[Offset], S02BC_C5, 169,
 ,[Offset], S02BD_C5, 170,
 ,[Offset], S02BE_C5, 171,
 ,[Offset], S02BF_C5, 172,
 ,[Offset], S02C0_C5, 173,
 ,[Offset], S02C1_C5, 174,
 ,[Offset], S02C2_C5, 175,
 ,[Offset], S02C4_C5, 176,
 ,[Offset], S02C7_C5, 177,
 ,[Offset], S02D3_C5, 178,
 ,[Offset], S02D4_C5, 179,
 ,[Offset], S02D5_C5, 180,
 ,[Offset], S02D6_C5, 181,
 ,[Offset], S02D7_C5, 182,
 ,[Offset], S02D8_C5, 183,
 ,[Offset], S02D9_C5, 184,
 ,[Offset], S02DA_C5, 185,
 ,[Offset], S02DB_C5, 186,
 ,[Offset], S02DC_C5, 187,
 ,[Offset], S02DD_C5, 188,
 ,[Offset], S02DF_C5, 189,
 ,[Offset], S02E0_C5, 190,
 ,[Offset], S02E1_C5, 191,
 ,[Offset], S02E2_C5, 192,
 ,[Offset], S02E3_C5, 193,
 ,[Offset], S02E4_C5, 194,
 ,[Offset], S02E5_C5, 195,
 ,[Offset], S02E6_C5, 196,
 ,[Offset], S02E7_C5, 197,
 ,[Offset], S02E8_C5, 198,
 ,[Offset], S02EA_C5, 199,
 ,[Offset], S02EB_C5, 200,
 ,[Offset], S02ED_C5, 201,
 ,[Offset], S02EE_C5, 202,
 ,[Offset], S02EF_C5, 203,
 ,[Offset], S02F0_C5, 204,
 ,[Offset], S02F1_C5, 205,
 ,[Offset], S02F2_C5, 206,
 ,[Offset], S02F3_C5, 207,
 ,[Offset], S02F4_C5, 208,
 ,[Offset], S02F7_C5, 209,
 ,[Offset], S02F9_C5, 210,
 ,[Offset], S02FA_C5, 211,
 ,[Offset], S02FC_C5, 212,
 ,[Offset], S02FD_C5, 213,
 ,[Offset], S02FE_C5, 214,
 ,[Offset], S0300_C5, 215,
 ,[Offset], S0301_C5, 216,
 ,[Offset], S0304_C5, 217,
 ,[Offset], S0307_C5, 218,
 ,[Offset], S0308_C5, 219,
 ,[Offset], S0309_C5, 220,
 ,[Offset], S030A_C5, 221,
 ,[Offset], S030B_C5, 222,
 ,[Offset], S030C_C5, 223,
 ,[Offset], S0321_C5, 224,
 ,[Offset], S0323_C5, 225,
 ,[Offset], S0325_C5, 226,
 ,[Offset], S0327_C5, 227,
 ,[Offset], S0328_C5, 228,
 ,[Offset], S0329_C5, 229,
 ,[Offset], S032A_C5, 230,
 ,[Offset], S032B_C5, 231,
 ,[Offset], S032C_C5, 232,
 ,[Offset], S032D_C5, 233,
 ,[Offset], S032E_C5, 234,
 ,[Offset], S032F_C5, 235,
 ,[Offset], S0332_C5, 236,
 ,[Offset], S03D4_C-1, 237,
 ,[Offset], S03D5_C-1, 238,
 ,[Offset], S03D6_C5, 239,
 ,[Offset], S03D7_C5, 240,
 ,[Offset], S03D8_C5, 241,
 ,[Offset], S03D9_C-1, 242,
 ,[Offset], S03DA_C-1, 243,
 ,[Offset], S03DB_C3, 244,
 ,[Offset], S03DC_C5, 245,
 ,[Offset], S03DD_C5, 246,
RSID_TABLE_THEATEROFMAGIC_MECH_SOUNDS, 2737,,,
 ,[Offset], basement_close, 0,
 ,[Offset], basement_eject, 1,
 ,[Offset], basement_open, 2,
 ,[Offset], inlane_magnet, 3,
 ,[Offset], ring_magnet, 4,
 ,[Offset], trunk_motor_start, 5,
 ,[Offset], trunk_motor, 6,
 ,[Offset], plunge_ramp, 7,
 ,[Offset], plunge_ramp_drop, 8,
 ,[Offset], plunge_ramp_rollback, 9,
 ,[Offset], ball_lock, 10,
 ,[Offset], secret_ball_lock, 11,
RSID_TABLE_THEATEROFMAGIC_SOUNDS_END, 2738,,,
RSID_TABLE_THEATEROFMAGIC_SAMPLES, 2739,,,
RSID_TABLE_THEATEROFMAGIC_VERSION, 2740,,,
RSID_TABLE_THEATEROFMAGIC_END, 2741,,,
