// Copyright 2025 < Copyright Owner >

#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <fstream>
#include <immintrin.h>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <random>
#include <stdexcept>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#ifdef _WIN32
#include <intrin.h>
#else
#include <cpuid.h>
#endif

#include <spdlog/spdlog.h>

// AVX-512 helper functions for platforms that may not support them
#ifndef _mm512_castps512_ps256
#define _mm512_castps512_ps256(a)                                              \
  _mm256_castpd_ps(_mm256_loadu_pd((const double *)&(a)))
#endif

#ifndef _mm512_castsi256_si512
#define _mm512_castsi256_si512(a)                                              \
  _mm512_castsi128_si512(_mm_loadu_si128((const __m128i *)&(a)))
#endif

#include "cache/cache.h"
#include "common/lock_ordering.h"
#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "cpu/x86_64_cpu.h"
#include "cpu/x86_64_pipeline.h"
#include "emulator/apic.h"
#include "emulator/interrupt_handler.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/tlb.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {

// CPU Feature Detection
namespace {
struct CPUFeatures {
  bool avx512f = false;
  bool avx2 = false;
  bool avx = false;
  bool sse42 = false;
  bool sse41 = false;
  bool ssse3 = false;
  bool sse3 = false;
  bool sse2 = false;
  bool sse = false;
};

CPUFeatures DetectCPUFeatures() {
  CPUFeatures features;

#ifdef _WIN32
  int cpuInfo[4];

  // Check for basic CPUID support
  __cpuid(cpuInfo, 0);
  int maxLeaf = cpuInfo[0];

  if (maxLeaf >= 1) {
    __cpuid(cpuInfo, 1);
    features.sse = (cpuInfo[3] & (1 << 25)) != 0;
    features.sse2 = (cpuInfo[3] & (1 << 26)) != 0;
    features.sse3 = (cpuInfo[2] & (1 << 0)) != 0;
    features.ssse3 = (cpuInfo[2] & (1 << 9)) != 0;
    features.sse41 = (cpuInfo[2] & (1 << 19)) != 0;
    features.sse42 = (cpuInfo[2] & (1 << 20)) != 0;
    features.avx = (cpuInfo[2] & (1 << 28)) != 0;
  }

  if (maxLeaf >= 7) {
    __cpuidex(cpuInfo, 7, 0);
    features.avx2 = (cpuInfo[1] & (1 << 5)) != 0;
    features.avx512f = (cpuInfo[1] & (1 << 16)) != 0;
  }
#else
  // GCC/Clang intrinsics
  unsigned int eax, ebx, ecx, edx;

  if (__get_cpuid(0, &eax, &ebx, &ecx, &edx)) {
    unsigned int maxLeaf = eax;

    if (maxLeaf >= 1 && __get_cpuid(1, &eax, &ebx, &ecx, &edx)) {
      features.sse = (edx & (1 << 25)) != 0;
      features.sse2 = (edx & (1 << 26)) != 0;
      features.sse3 = (ecx & (1 << 0)) != 0;
      features.ssse3 = (ecx & (1 << 9)) != 0;
      features.sse41 = (ecx & (1 << 19)) != 0;
      features.sse42 = (ecx & (1 << 20)) != 0;
      features.avx = (ecx & (1 << 28)) != 0;
    }

    if (maxLeaf >= 7 && __get_cpuid_count(7, 0, &eax, &ebx, &ecx, &edx)) {
      features.avx2 = (ebx & (1 << 5)) != 0;
      features.avx512f = (ebx & (1 << 16)) != 0;
    }
  }
#endif

  spdlog::info("CPU Features detected: SSE={}, SSE2={}, SSE3={}, SSSE3={}, "
               "SSE4.1={}, SSE4.2={}, AVX={}, AVX2={}, AVX-512F={}",
               features.sse, features.sse2, features.sse3, features.ssse3,
               features.sse41, features.sse42, features.avx, features.avx2,
               features.avx512f);

  return features;
}

static const CPUFeatures g_cpuFeatures = DetectCPUFeatures();
} // namespace

// PIC Implementation
PIC::PIC()
    : icw1(0), icw4(0), imr(0xFF), isr(0), irr(0), baseVector(0x08),
      currentCommand(0), initSequence(0) {
  spdlog::info("PIC constructed");
}

void PIC::Reset() {
  icw1 = 0;
  icw4 = 0;
  imr = 0xFF; // Mask all interrupts
  isr = 0;
  irr = 0;
  baseVector = 0x08;
  currentCommand = 0;
  initSequence = 0;
  spdlog::info("PIC reset");
}

void PIC::Write(uint64_t address, uint64_t value, uint8_t size) {
  if (size != 1)
    return;

  uint8_t data = static_cast<uint8_t>(value);
  switch (address & 1) {
  case 0: // Command/ICW1
    if (data & 0x10) {
      // ICW1
      icw1 = data;
      initSequence = 1;
      imr = 0xFF; // Mask all during init
    } else {
      // OCW2/OCW3
      currentCommand = data;
    }
    break;
  case 1: // Data/ICW2-4/IMR
    if (initSequence == 1) {
      baseVector = data;
      initSequence = 2;
    } else if (initSequence == 2 && (icw1 & 0x02) == 0) {
      // ICW3 (cascade mode)
      initSequence = 3;
    } else if (initSequence >= 2) {
      // ICW4 or IMR
      if (icw1 & 0x01) {
        icw4 = data;
        initSequence = 0;
      } else {
        imr = data;
      }
    } else {
      imr = data;
    }
    break;
  }
  spdlog::trace("PIC write: addr=0x{:x}, value=0x{:x}", address, data);
}

uint64_t PIC::Read(uint64_t address, uint8_t size) {
  if (size != 1)
    return 0;

  switch (address & 1) {
  case 0: // Status
    return (currentCommand & 0x04) ? isr : irr;
  case 1: // IMR
    return imr;
  }
  return 0;
}

std::string PIC::GetName() const { return "PIC"; }

void PIC::SaveState(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(&icw1), sizeof(icw1));
  out.write(reinterpret_cast<const char *>(&icw4), sizeof(icw4));
  out.write(reinterpret_cast<const char *>(&imr), sizeof(imr));
  out.write(reinterpret_cast<const char *>(&isr), sizeof(isr));
  out.write(reinterpret_cast<const char *>(&irr), sizeof(irr));
  out.write(reinterpret_cast<const char *>(&baseVector), sizeof(baseVector));
}

void PIC::LoadState(std::istream &in) {
  in.read(reinterpret_cast<char *>(&icw1), sizeof(icw1));
  in.read(reinterpret_cast<char *>(&icw4), sizeof(icw4));
  in.read(reinterpret_cast<char *>(&imr), sizeof(imr));
  in.read(reinterpret_cast<char *>(&isr), sizeof(isr));
  in.read(reinterpret_cast<char *>(&irr), sizeof(irr));
  in.read(reinterpret_cast<char *>(&baseVector), sizeof(baseVector));
}

void PIC::SignalInterrupt(uint8_t irq) {
  if (irq < 8) {
    irr |= (1 << irq);
    UpdateInterrupts();
  }
}

void PIC::UpdateInterrupts() {
  uint8_t pending = irr & ~imr;
  if (pending && !cpu.expired()) {
    if (auto cpuPtr = cpu.lock()) {
      for (int i = 0; i < 8; ++i) {
        if (pending & (1 << i)) {
          cpuPtr->QueueInterrupt(baseVector + i, 0);
          isr |= (1 << i);
          irr &= ~(1 << i);
          break;
        }
      }
    }
  }
}

// PIT Implementation
PIT::PIT() : controlWord(0), currentChannel(0), lowByte(false) {
  try {
    counters[0] = 0;
    counters[1] = 0;
    counters[2] = 0;
    spdlog::info("PIT constructed");
  } catch (const std::exception &e) {
    spdlog::error("PIT constructor failed: {}", e.what());
    throw;
  } catch (...) {
    spdlog::error("PIT constructor failed with unknown exception");
    throw;
  }
}

void PIT::Reset() {
  controlWord = 0;
  currentChannel = 0;
  lowByte = false;
  counters[0] = 0;
  counters[1] = 0;
  counters[2] = 0;
  spdlog::info("PIT reset");
}

void PIT::Write(uint64_t address, uint64_t value, uint8_t size) {
  if (size != 1)
    return;

  uint8_t data = static_cast<uint8_t>(value);
  switch (address & 3) {
  case 0:
  case 1:
  case 2: {
    uint8_t channel = address & 3;
    if (lowByte) {
      counters[channel] = (counters[channel] & 0xFF00) | data;
      lowByte = false;
    } else {
      counters[channel] = (counters[channel] & 0x00FF) | (data << 8);
      lowByte = true;
    }
    break;
  }
  case 3: // Control word
    controlWord = data;
    currentChannel = (data >> 6) & 3;
    if (currentChannel < 3) {
      lowByte = true; // Start with low byte
    }
    break;
  }
  spdlog::trace("PIT write: addr=0x{:x}, value=0x{:x}", address, data);
}

uint64_t PIT::Read(uint64_t address, uint8_t size) {
  if (size != 1)
    return 0;

  switch (address & 3) {
  case 0:
  case 1:
  case 2: {
    uint8_t channel = address & 3;
    if (lowByte) {
      lowByte = false;
      return counters[channel] & 0xFF;
    } else {
      lowByte = true;
      return (counters[channel] >> 8) & 0xFF;
    }
  }
  case 3:
    return 0; // Control word is write-only
  }
  return 0;
}

std::string PIT::GetName() const { return "PIT"; }

void PIT::SaveState(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(counters), sizeof(counters));
  out.write(reinterpret_cast<const char *>(&controlWord), sizeof(controlWord));
  out.write(reinterpret_cast<const char *>(&currentChannel),
            sizeof(currentChannel));
  out.write(reinterpret_cast<const char *>(&lowByte), sizeof(lowByte));
}

void PIT::LoadState(std::istream &in) {
  in.read(reinterpret_cast<char *>(counters), sizeof(counters));
  in.read(reinterpret_cast<char *>(&controlWord), sizeof(controlWord));
  in.read(reinterpret_cast<char *>(&currentChannel), sizeof(currentChannel));
  in.read(reinterpret_cast<char *>(&lowByte), sizeof(lowByte));
}

void PIT::Tick() {
  // Decrement active counters
  for (int i = 0; i < 3; ++i) {
    if (counters[i] > 0) {
      counters[i]--;
      if (counters[i] == 0 && i == 0) {
        // Channel 0 generates timer interrupt
        if (!cpu.expired()) {
          if (auto cpuPtr = cpu.lock()) {
            cpuPtr->QueueInterrupt(0x20, 0); // Timer interrupt
          }
        }
      }
    }
  }
}

// DeviceManager Implementation
void DeviceManager::RegisterDevice(uint64_t base, uint64_t size,
                                   std::weak_ptr<x86_64::Device> device) {
  try {
    // Validate parameters
    if (size == 0) {
      spdlog::error(
          "DeviceManager: Cannot register device with zero size at 0x{:x}",
          base);
      throw std::invalid_argument("Device size cannot be zero");
    }

    if (base + size < base) {
      spdlog::error("DeviceManager: Address overflow for device at 0x{:x} with "
                    "size 0x{:x}",
                    base, size);
      throw std::invalid_argument("Device address range overflow");
    }

    // Check if device pointer is valid
    if (device.expired()) {
      spdlog::error("DeviceManager: Cannot register expired device at 0x{:x}",
                    base);
      throw std::invalid_argument("Device pointer is expired");
    }

    // Check for overlapping devices
    for (const auto &existing : devices) {
      uint64_t existing_end = existing.base + existing.size;
      uint64_t new_end = base + size;

      if ((base < existing_end && new_end > existing.base)) {
        spdlog::error("DeviceManager: Device overlap detected - new device "
                      "0x{:x}-0x{:x} overlaps with existing 0x{:x}-0x{:x}",
                      base, new_end - 1, existing.base, existing_end - 1);
        throw std::invalid_argument(
            "Device address range overlaps with existing device");
      }
    }

    DeviceMapping mapping;
    mapping.base = base;
    mapping.size = size;
    mapping.device = device;
    devices.push_back(mapping);

    spdlog::info("DeviceManager: Successfully registered device at "
                 "0x{:x}-0x{:x} (size: 0x{:x})",
                 base, base + size - 1, size);
  } catch (const std::exception &e) {
    spdlog::error("DeviceManager: Failed to register device at 0x{:x}: {}",
                  base, e.what());
    throw;
  }
}

std::weak_ptr<x86_64::Device> DeviceManager::FindDevice(uint64_t address) {
  try {
    for (const auto &mapping : devices) {
      // Validate mapping integrity
      if (mapping.size == 0) {
        spdlog::warn(
            "DeviceManager: Found device with zero size at 0x{:x}, skipping",
            mapping.base);
        continue;
      }

      if (address >= mapping.base && address < mapping.base + mapping.size) {
        if (mapping.device.expired()) {
          spdlog::warn("DeviceManager: Found expired device at 0x{:x} for "
                       "address 0x{:x}",
                       mapping.base, address);
          continue;
        }
        return mapping.device;
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("DeviceManager: Error finding device for address 0x{:x}: {}",
                  address, e.what());
  }
  return std::weak_ptr<x86_64::Device>();
}

X86_64CPU::X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu,
                     uint32_t cpuId)
    : m_emulator(emulator), mmu(mmu), m_cpuId(cpuId), registers{},
      xmmRegisters{}, rflags(DEFAULT_RFLAGS), running(false), halted(false),
      jit(std::make_unique<X86_64JITCompiler>(this)),
      decoder(std::make_unique<InstructionDecoder>()),
      pipeline(std::make_unique<Pipeline>(*this)), pic(nullptr), apic(nullptr),
      pit(nullptr), utilization(0.0f), tlb() { // Initialize TLB

  try {
    spdlog::info("X86_64CPU[{}]: Starting register initialization", m_cpuId);
    registers.fill(0);

#ifdef __AVX__
    xmmRegisters.fill(_mm256_setzero_si256());
#else
    for (auto &reg : xmmRegisters) {
      reg = _mm256_castsi128_si256(_mm_setzero_si128());
    }
#endif

    // AVX-512 registers (only if supported)
    spdlog::debug("X86_64CPU[{}]: Initializing SIMD registers", m_cpuId);
    if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
      zmmRegisters.fill(_mm512_setzero_si512());
#else
      // Fallback: zero-initialize manually if AVX-512 intrinsics not available
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
#endif
      spdlog::debug("X86_64CPU[{}]: AVX-512 registers initialized", m_cpuId);
    } else {
      // Zero-initialize without using AVX-512 intrinsics
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
    }

    // Initialize mask registers (K0-K7)
    kRegisters.fill(0);
    kRegisters[0] = 0xFFFF; // K0 is always all 1s
    spdlog::debug("X86_64CPU[{}]: Mask registers initialized", m_cpuId);
    spdlog::debug(
        "X86_64CPU[{}]: AVX-512 not supported, registers zero-initialized",
        m_cpuId);
  }
  kRegisters.fill(0);

  // Create devices safely with individual error handling
  spdlog::info("X86_64CPU[{}]: Creating devices", m_cpuId);

  try {
    spdlog::debug("X86_64CPU[{}]: Creating PIC", m_cpuId);
    pic = std::make_shared<PIC>();
    if (!pic) {
      throw std::runtime_error("Failed to create PIC");
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: PIC creation failed: {}", m_cpuId, e.what());
    throw;
  }

  try {
    spdlog::debug("X86_64CPU[{}]: Creating PIT", m_cpuId);
    pit = std::make_shared<PIT>();
    if (!pit) {
      throw std::runtime_error("Failed to create PIT");
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: PIT creation failed: {}", m_cpuId, e.what());
    throw;
  }

  try {
    spdlog::debug("X86_64CPU[{}]: Creating APIC", m_cpuId);
    apic = std::make_shared<x86_64::APIC>(cpuId);
    if (!apic) {
      throw std::runtime_error("Failed to create APIC");
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: APIC creation failed: {}", m_cpuId, e.what());
    throw;
  }

  // Register devices with error handling
  spdlog::info("X86_64CPU[{}]: Registering devices", m_cpuId);

  spdlog::debug("X86_64CPU[{}]: Registering PIC at 0x20", m_cpuId);
  deviceManager.RegisterDevice(0x20, 2, pic); // PIC

  spdlog::debug("X86_64CPU[{}]: Registering PIT at 0x40", m_cpuId);
  deviceManager.RegisterDevice(0x40, 4, pit); // PIT

  spdlog::debug("X86_64CPU[{}]: Registering APIC at 0xFEE00000", m_cpuId);
  deviceManager.RegisterDevice(0xFEE00000, 0x1000, apic); // APIC

  spdlog::info("X86_64CPU[{}] created with {} XMM registers", m_cpuId,
               XMM_REGISTER_COUNT);
}
catch (const std::exception &e) {
  spdlog::error("X86_64CPU[{}] constructor failed: {}", m_cpuId, e.what());
  throw;
}
catch (...) {
  spdlog::error("X86_64CPU[{}] constructor failed with unknown exception",
                m_cpuId);
  throw;
}
}

X86_64CPU::~X86_64CPU() {
  Shutdown();
  spdlog::info("X86_64CPU[{}] destroyed", m_cpuId);
}

bool X86_64CPU::Initialize() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  try {
    running = true;
    halted = false;
    tlb.Clear();
    // Clear and re-initialize interrupt queue
    while (!interruptQueue.empty()) {
      interruptQueue.pop();
    }

    // Reset all registers
    registers.fill(0);
    xmmRegisters.fill(_mm256_setzero_si256());

    // Reset AVX-512 registers conditionally
    if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
      zmmRegisters.fill(_mm512_setzero_si512());
#else
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
#endif
    } else {
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
    }
    kRegisters.fill(0);
    segmentRegisters.fill(0);
    controlRegisters.fill(0);
    debugRegisters.fill(0);
    msrRegisters.clear();

    // Initialize default register values
    registers[static_cast<size_t>(Register::RSP)] = DEFAULT_STACK_POINTER;
    registers[static_cast<size_t>(Register::RIP)] = DEFAULT_ENTRY_POINT;
    rflags = DEFAULT_RFLAGS;

    // Initialize segment registers with default values
    segmentRegisters[static_cast<size_t>(Register::CS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_CS; // Code segment
    segmentRegisters[static_cast<size_t>(Register::DS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_DS; // Data segment
    segmentRegisters[static_cast<size_t>(Register::ES) -
                     static_cast<size_t>(Register::ES)] = KERNEL_DS;
    segmentRegisters[static_cast<size_t>(Register::SS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_DS; // Stack segment
    segmentRegisters[static_cast<size_t>(Register::FS) -
                     static_cast<size_t>(Register::ES)] =
        0; // FS and GS usually 0 initially
    segmentRegisters[static_cast<size_t>(Register::GS) -
                     static_cast<size_t>(Register::ES)] = 0;

    // Initialize control registers
    controlRegisters[0] = 0x80000011; // CR0: PE=1, PG=1, WP=1 (Protected
                                      // Mode, Paging, Write Protect)
    controlRegisters[3] = 0x0; // CR3: Page directory base (usually set by OS)
    controlRegisters[4] =
        0x000006F0; // CR4: Various extensions enabled (PAE, PGE, OSFXSR,
                    // OSXMMEXCPT, VMXE, SMXE, PCIDE, OSXSAVE, SMEP, SMAP)

    // Initialize essential MSRs
    msrRegisters[static_cast<uint32_t>(MSR::EFER)] =
        0x501; // LME=1, LMA=1, SCE=1 (Long Mode Enable, Long Mode Active,
               // SYSCALL Enable)
    msrRegisters[static_cast<uint32_t>(MSR::STAR)] =
        0x230008ULL << 32; // SYSCALL/SYSRET target CS/SS
    msrRegisters[static_cast<uint32_t>(MSR::LSTAR)] = 0; // SYSCALL target RIP
    msrRegisters[static_cast<uint32_t>(MSR::CSTAR)] =
        0; // Compat-mode SYSCALL target RIP
    msrRegisters[static_cast<uint32_t>(MSR::SFMASK)] = 0; // SYSCALL RFLAGS mask
    msrRegisters[static_cast<uint32_t>(MSR::FS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::GS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::KERNEL_GS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::TSC)] = 0; // Time Stamp Counter
    msrRegisters[static_cast<uint32_t>(MSR::APIC_BASE)] =
        0xFEE00000 | (1ULL << 11); // APIC Base Address, APIC Global Enable

    // Initialize FPU state (FXSAVE/FXRSTOR format)
    fpuState = {};
    fpuState.controlWord = 0x037F; // Default FPU control word
    fpuState.statusWord = 0x0000;
    fpuState.tagWord = 0xFFFF; // All FPU registers empty
    fpuState.lastInstructionPointer = 0;
    fpuState.lastDataPointer = 0;
    fpuState.opcode = 0;
    fpuState.top = 0;      // Stack top pointer
    fpuState.tags.fill(3); // All registers empty (tag = 3)
    fpuState.st.fill(0.0); // Clear all stack registers

    // Initialize MMX state (part of FPU state)
    mmxState = {};
    mmxState.mmxMode = false; // Not strictly needed, but good for clarity

    // Initialize GDTR, IDTR, LDTR, TR
    gdtrBase = 0;
    gdtrLimit = 0;
    idtrBase = 0;
    idtrLimit = 0;
    ldtr = 0;
    tr = 0;
    tssBase = 0;
    tssLimit = 0;

    // Initialize process ID
    processId = 0;

    pipeline->ResetStats();
    utilization = 0.0f;

    spdlog::info(
        "X86_64CPU[{}] initialized with full register support. RIP=0x{:x}",
        m_cpuId, DEFAULT_ENTRY_POINT);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}] initialization failed: {}", m_cpuId, e.what());
    running = false;
    return false;
  }
}

void X86_64CPU::Shutdown() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  running = false;
  halted = false;
  tlb.Clear();
  while (!interruptQueue.empty()) {
    interruptQueue.pop();
  }
  utilization = 0.0f;

  pipeline->Flush();
  spdlog::info("X86_64CPU[{}] shut down", m_cpuId);
}

void X86_64CPU::ResetState() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  utilization = 0.0f;
  while (!interruptQueue.empty()) {
    interruptQueue.pop();
  }
  halted = false;

  pipeline->Flush();

  // Reset all registers to zero
  registers.fill(0);
  xmmRegisters.fill(_mm256_setzero_si256());

  // Reset AVX-512 registers conditionally
  if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
    zmmRegisters.fill(_mm512_setzero_si512());
#else
    for (auto &reg : zmmRegisters) {
      memset(&reg, 0, sizeof(reg));
    }
#endif
  } else {
    for (auto &reg : zmmRegisters) {
      memset(&reg, 0, sizeof(reg));
    }
  }
  kRegisters.fill(0);
  segmentRegisters.fill(0);
  controlRegisters.fill(0);
  debugRegisters.fill(0);
  msrRegisters.clear();

  // Set default values using constants
  registers[static_cast<size_t>(Register::RSP)] = DEFAULT_STACK_POINTER;
  registers[static_cast<size_t>(Register::RIP)] = DEFAULT_ENTRY_POINT;
  rflags = DEFAULT_RFLAGS;

  // Reset segment registers
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_CS;
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;

  // Reset control registers
  controlRegisters[0] = 0x80000011;
  controlRegisters[3] = 0x0;
  controlRegisters[4] = 0x000006F0;

  // Reset MSRs
  msrRegisters[static_cast<uint32_t>(MSR::EFER)] = 0x501;
  msrRegisters[static_cast<uint32_t>(MSR::STAR)] = 0x230008ULL << 32;
  msrRegisters[static_cast<uint32_t>(MSR::LSTAR)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::CSTAR)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::SFMASK)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::FS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::GS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::KERNEL_GS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::TSC)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::APIC_BASE)] =
      0xFEE00000 | (1ULL << 11);

  // Reset FPU state
  fpuState = {};
  fpuState.controlWord = 0x037F;
  fpuState.tagWord = 0xFFFF;
  fpuState.top = 0;
  fpuState.tags.fill(3);
  fpuState.st.fill(0.0);

  // Reset GDTR, IDTR, LDTR, TR
  gdtrBase = 0;
  gdtrLimit = 0;
  idtrBase = 0;
  idtrLimit = 0;
  ldtr = 0;
  tr = 0;
  tssBase = 0;
  tssLimit = 0;

  processId = 0;

  spdlog::info("X86_64CPU[{}] state reset to defaults: RIP=0x{:x}", m_cpuId,
               DEFAULT_ENTRY_POINT);
}

void X86_64CPU::QueueInterrupt(uint8_t vector, uint8_t priority) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  interruptQueue.push({vector, priority});
  spdlog::debug("X86_64CPU[{}]: Queued interrupt vector=0x{:x}, priority={}",
                m_cpuId, vector, priority);
  halted = false; // Wake up from HLT
}

void X86_64CPU::TriggerInterrupt(uint8_t vector, uint64_t errorCode,
                                 bool isSoftwareInterrupt) {
  // Release CPU mutex before calling InterruptHandler to prevent deadlock
  // The InterruptHandler will call back into CPU methods, so we must not hold
  // our mutex
  std::unique_lock<std::recursive_timed_mutex> lock(mutex, std::defer_lock);
  if (lock.owns_lock()) { // Only unlock if we already own it
    lock.unlock();
  }

  InterruptHandler &handler = m_emulator.GetInterruptHandler();
  try {
    handler.HandleInterrupt(vector, errorCode, isSoftwareInterrupt);
    spdlog::debug("X86_64CPU[{}]: Triggered interrupt vector=0x{:x}", m_cpuId,
                  vector);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to handle interrupt vector=0x{:x}: {}",
                  m_cpuId, vector, e.what());
    // If handling fails, re-queue the interrupt or halt the CPU
    // Re-acquire lock to modify internal state
    lock.lock();
    interruptQueue.push({vector, 0}); // Re-queue with low priority
    halted = true; // Or halt the CPU if it's a critical error
  }
}

void X86_64CPU::DeviceTick() {
  pit->Tick();
  apic->UpdateTimer(1); // Update timer with 1 cycle
}

void X86_64CPU::ExecuteCycle() {
  // Aggressive timeout and intelligent backoff for mutex acquisition
  auto lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
      mutex, ps4::LockLevel::CPU, "CPUMutex", std::try_to_lock);
  if (!lock->owns_lock()) {
    auto timeout = std::chrono::microseconds(500);
    if (!mutex.try_lock_for(timeout)) {
      spdlog::trace(
          "X86_64CPU[{}]: Mutex contention - core contention detected",
          m_cpuId);

      static thread_local std::random_device rd;
      static thread_local std::mt19937 gen(rd());
      std::uniform_int_distribution<> dis(1, 100);

      auto backoff_us = dis(gen) * m_cpuId;
      std::this_thread::sleep_for(std::chrono::microseconds(backoff_us));
      return;
    }
    lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
        mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
  }

  if (!running) {
    spdlog::debug("X86_64CPU[{}]: Not running, skipping cycle", m_cpuId);
    return;
  }

  DeviceTick();
  CheckMWAITWakeup();

  if (halted) {
    // If halted, only process interrupts
    if (!interruptQueue.empty() && GetFlag(FLAG_IF)) {
      auto irq = interruptQueue.top();
      interruptQueue.pop();
      spdlog::debug("X86_64CPU[{}]: Waking from HLT to handle interrupt 0x{:x}",
                    m_cpuId, irq.vector);
      halted = false; // Exit HLT state
      // Release lock before triggering interrupt to prevent deadlock
      lock->unlock();
      TriggerInterrupt(irq.vector, 0, false);
      return; // Cycle ends after handling interrupt
    } else {
      spdlog::trace("X86_64CPU[{}]: Halted, waiting for interrupt", m_cpuId);
      std::this_thread::sleep_for(
          std::chrono::microseconds(100)); // Sleep to avoid busy-waiting
      return;
    }
  }

  auto start = std::chrono::steady_clock::now();
  try {
    // Handle pending interrupts if enabled
    if (GetFlag(FLAG_IF) && !interruptQueue.empty()) {
      auto irq = interruptQueue.top();
      interruptQueue.pop();

      uint64_t currentRip = _getRegister(Register::RIP);

      // Release our mutex before calling TriggerInterrupt to prevent deadlock
      lock->unlock();

      if (currentRip == 0 || currentRip < 0x1000) {
        spdlog::critical("X86_64CPU[{}]: Invalid RIP 0x{:x} before interrupt - "
                         "preventing execution",
                         m_cpuId, currentRip);
        // Re-acquire lock to set running to false
        if (mutex.try_lock()) {
          lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
              mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
          running = false;
        }
        return;
      }

      TriggerInterrupt(irq.vector, 0, false);

      // Reacquire lock for UpdateUtilization
      if (!mutex.try_lock()) {
        spdlog::warn("X86_64CPU[{}]: Could not reacquire mutex after interrupt",
                     m_cpuId);
        return;
      }
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      UpdateUtilization(start);
      return;
    }

    uint64_t rip = _getRegister(Register::RIP);

    // Enhanced RIP initialization with per-core distribution
    if (rip == 0 || rip == DEFAULT_ENTRY_POINT) {
      uint64_t baseEntry = DEFAULT_ENTRY_POINT;
      uint64_t coreOffset = m_cpuId * 0x1000; // 4KB per core
      uint64_t newRip = baseEntry + coreOffset;

      spdlog::info("X86_64CPU[{}]: Setting distributed entry point: 0x{:x} "
                   "(base + 0x{:x})",
                   m_cpuId, newRip, coreOffset);
      _setRegister(Register::RIP, newRip);
      rip = newRip;

      // Initialize some basic code (e.g., NOP) at this location
      try {
        lock->unlock();                // Release lock for MMU access
        uint8_t nopInstruction = 0x90; // NOP instruction
        mmu.WriteVirtual(rip, &nopInstruction, 1, processId);
        if (!mutex.try_lock_for(std::chrono::milliseconds(1))) {
          spdlog::warn(
              "X86_64CPU[{}]: Could not reacquire lock after RIP setup",
              m_cpuId);
          return;
        }
        lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
            mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: Failed to initialize entry point: {}",
                      m_cpuId, e.what());
        running = false;
        return;
      }
    }

    // CRITICAL FIX: Validate RIP before any execution
    if (rip < 0x1000 || rip >= 0x800000000000ULL) { // Basic sanity check for
                                                    // user-space addresses
      spdlog::critical("X86_64CPU[{}]: CRITICAL - Invalid RIP 0x{:x} in "
                       "ExecuteCycle, attempting recovery",
                       m_cpuId, rip);

      // Try to set a safe default RIP
      _setRegister(Register::RIP, DEFAULT_ENTRY_POINT);
      rip = DEFAULT_ENTRY_POINT;

      if (rip < 0x1000) { // If still invalid, stop the CPU
        running = false;
        spdlog::error(
            "X86_64CPU[{}]: Cannot recover from invalid RIP, stopping CPU",
            m_cpuId);
        return;
      }
    }

    // Release lock before JIT/pipeline execution to prevent deadlocks
    lock->unlock();

    bool jitSuccess = false;
    try {
      jitSuccess = jit->ExecuteCompiledCode(rip);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: JIT execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
      jitSuccess = false;
    }

    if (jitSuccess) {
      if (mutex.try_lock()) {
        lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
            mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
        UpdateUtilization(start);
      }
      return;
    }

    spdlog::debug("X86_64CPU[{}]: JIT failed at 0x{:x}, using pipeline",
                  m_cpuId, rip);

    try {
      pipeline->Step(); // This will call FetchDecodeExecute internally
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Pipeline execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
      // Trigger a general protection fault or similar exception
      TriggerInterrupt(EXC_GP, 0, false);
    }

    if (mutex.try_lock()) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      UpdateUtilization(start);
    }
  } catch (const CPUException &e) {
    // Ensure we have the lock for accessing GetRegister and running
    if (!lock->owns_lock() && mutex.try_lock()) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
    }

    if (lock->owns_lock()) {
      uint64_t errorRip = _getRegister(Register::RIP);
      spdlog::critical("X86_64CPU[{}]: Exception at RIP=0x{:x}: {}", m_cpuId,
                       errorRip, e.what());
      running = false;
    } else {
      spdlog::critical("X86_64CPU[{}]: Exception (could not acquire lock): {}",
                       m_cpuId, e.what());
    }
    throw;
  } catch (const std::exception &e) {
    // Catch any other unexpected exceptions
    if (!lock->owns_lock() && mutex.try_lock()) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
    }
    if (lock->owns_lock()) {
      uint64_t errorRip = _getRegister(Register::RIP);
      spdlog::critical("X86_64CPU[{}]: Unhandled exception at RIP=0x{:x}: {}",
                       m_cpuId, errorRip, e.what());
      running = false;
    } else {
      spdlog::critical(
          "X86_64CPU[{}]: Unhandled exception (could not acquire lock): {}",
          m_cpuId, e.what());
    }
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
  }
}

void X86_64CPU::UpdateUtilization(
    const std::chrono::steady_clock::time_point &start) {
  auto end = std::chrono::steady_clock::now();
  auto duration =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  // Accumulate utilization over time, capping at 1.0 (100%)
  utilization = (std::min)(1.0f, utilization + (float)duration / 1000000.0f);
  spdlog::trace("X86_64CPU[{}] utilization updated: {:.2f}%", m_cpuId,
                utilization * 100.0f);
}

bool X86_64CPU::CalculateParity(uint64_t value) {
  uint8_t byte = value & 0xFF;
  int count = 0;
  for (int i = 0; i < 8; ++i) {
    if ((byte >> i) & 1)
      count++;
  }
  return (count % 2 == 0);
}

void X86_64CPU::UpdateArithmeticFlags(uint64_t op1, uint64_t op2,
                                      uint64_t result, uint8_t sizeInBits,
                                      bool isSubtract) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error("X86_64CPU[{}]: Invalid size for flag calculation: {} bits",
                  m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;
  op1 &= mask;
  op2 &= mask;

  // Clear only arithmetic flags, preserve other flags
  rflags &= ~ARITHMETIC_FLAGS_MASK;

  // Zero flag
  if (result == 0)
    rflags |= FLAG_ZF;

  // Sign flag
  if (result & signBit)
    rflags |= FLAG_SF;

  // Parity flag
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // Carry and Auxiliary Carry flags
  if (isSubtract) {
    // CF is set if a borrow is generated (op1 < op2)
    if (op1 < op2)
      rflags |= FLAG_CF;
    // AF is set if a borrow is generated from bit 4
    if ((op1 & 0xF) < (op2 & 0xF))
      rflags |= FLAG_AF;
  } else { // Addition
    // CF is set if a carry is generated (result overflows mask)
    uint64_t fullResult = op1 + op2;
    if (fullResult > mask)
      rflags |= FLAG_CF;
    // AF is set if a carry is generated from bit 3 to bit 4
    if (((op1 & 0xF) + (op2 & 0xF)) > 0xF)
      rflags |= FLAG_AF;
  }

  // Overflow flag calculation
  bool s1 = (op1 & signBit) != 0;
  bool s2 = (op2 & signBit) != 0;
  bool sr = (result & signBit) != 0;

  if (isSubtract) {
    // Overflow occurs if (positive - negative = negative) or (negative -
    // positive = positive)
    if ((s1 && !s2 && !sr) || (!s1 && s2 && sr))
      rflags |= FLAG_OF;
  } else { // Addition
    // Overflow occurs if (positive + positive = negative) or (negative +
    // negative = positive)
    if (s1 == s2 && s1 != sr)
      rflags |= FLAG_OF;
  }

  spdlog::trace("X86_64CPU[{}] flags updated: RFLAGS=0x{:x}", m_cpuId, rflags);
}

uint64_t
X86_64CPU::ReadOperandValue(const DecodedInstruction::Operand &operand) {
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    return GetRegister(operand.reg) & ((1ULL << sizeInBits) - 1);
  case DecodedInstruction::Operand::Type::IMMEDIATE:
    // Sign-extend immediate values based on their size
    switch (sizeInBits) {
    case 8:
      return static_cast<uint64_t>(static_cast<int8_t>(operand.immediate));
    case 16:
      return static_cast<uint64_t>(static_cast<int16_t>(operand.immediate));
    case 32:
      return static_cast<uint64_t>(static_cast<int32_t>(operand.immediate));
    case 64:
      return operand.immediate;
    default:
      spdlog::error("X86_64CPU[{}]: Invalid immediate size: {} bits", m_cpuId,
                    sizeInBits);
      throw CPUException("Invalid immediate size");
    }
  case DecodedInstruction::Operand::Type::MEMORY: {
    uint64_t addr = CalculateMemoryAddress(operand);
    size_t bytesToRead = sizeInBits / 8;
    uint64_t value = 0;
    try {
      mmu.ReadVirtual(addr, &value, bytesToRead, GetProcessId());
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Memory read failed at 0x{:x}: {}", m_cpuId,
                    addr, e.what());
      TriggerInterrupt(EXC_PF, addr, false); // Page Fault
      throw CPUException("Memory read error");
    }
    spdlog::trace("Read memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                  bytesToRead);
    return value & ((1ULL << sizeInBits) - 1);
  }
  case DecodedInstruction::Operand::Type::XMM: {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                               static_cast<size_t>(Register::XMM0)];
    alignas(32) uint64_t tmp[4];
    _mm256_store_si256((__m256i *)tmp, reg);

    uint64_t result = 0;
    switch (sizeInBits) {
    case 32: // Single 32-bit value (e.g., scalar float)
      result = tmp[0] & 0xFFFFFFFF;
      break;
    case 64: // Single 64-bit value (e.g., scalar double)
      result = tmp[0];
      break;
    case 128: // Full XMM register (128-bit) - return lower 64 bits with
              // warning
      spdlog::warn(
          "ReadOperandValue called for 128-bit XMM operation at XMM{} - "
          "returning lower 64 bits. Use ReadXmmOperandValue() for full SIMD "
          "data.",
          static_cast<int>(operand.reg));
      result = tmp[0];
      break;
    case 256: // Full YMM register (256-bit) - return lower 64 bits with
              // warning
      spdlog::warn(
          "ReadOperandValue called for 256-bit YMM operation at XMM{} - "
          "returning lower 64 bits. Use ReadXmmOperandValue() for full SIMD "
          "data.",
          static_cast<int>(operand.reg));
      result = tmp[0];
      break;
    default:
      result = tmp[0] & ((1ULL << sizeInBits) - 1);
      break;
    }

    spdlog::trace("Read XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), result, sizeInBits);
    return result;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Invalid operand type");
  }
}

void X86_64CPU::WriteOperandValue(const DecodedInstruction::Operand &operand,
                                  uint64_t value) {
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    SetRegister(operand.reg, value & ((1ULL << sizeInBits) - 1));
    spdlog::trace("Write register {}: value=0x{:x}",
                  static_cast<int>(operand.reg), value);
    break;
  case DecodedInstruction::Operand::Type::MEMORY:
    WriteMemoryOperand(operand, value);
    break;
  case DecodedInstruction::Operand::Type::XMM: {
    __m256i newValue;
    switch (sizeInBits) {
    case 32:
      newValue =
          _mm256_set_epi32(0, 0, 0, 0, 0, 0, 0, static_cast<int32_t>(value));
      break;
    case 64:
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 128:
      spdlog::warn("WriteOperandValue called for 128-bit XMM operation - use "
                   "WriteXmmOperandValue instead. Setting lower 64 bits.");
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 256:
      spdlog::warn("WriteOperandValue called for 256-bit YMM operation - use "
                   "WriteXmmOperandValue instead. Broadcasting value.");
      newValue = _mm256_set1_epi64x(static_cast<int64_t>(value));
      break;
    default:
      newValue = _mm256_set_epi64x(
          0, 0, 0, static_cast<int64_t>(value & ((1ULL << sizeInBits) - 1)));
      break;
    }
    WriteXmmOperand(operand, newValue);
    spdlog::trace("Write XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), value, sizeInBits);
    break;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type for write: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Write to invalid operand type");
  }
}

uint64_t X86_64CPU::CalculateMemoryAddress(
    const DecodedInstruction::Operand &operand) const {
  if (operand.type != DecodedInstruction::Operand::Type::MEMORY) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for memory address "
                  "calculation: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for memory address");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(
      mutex); // Lock for register access
  uint64_t addr = 0;
  if (operand.memory.base == Register::RIP) {
    // RIP-relative addressing: RIP + displacement + instruction_length
    addr = _getRegister(Register::RIP) + operand.memory.displacement +
           operand.instructionLength;
  } else if (operand.memory.base != Register::NONE) {
    addr = _getRegister(operand.memory.base);
  }
  if (operand.memory.index != Register::NONE) {
    addr += _getRegister(operand.memory.index) * operand.memory.scale;
  }
  addr += static_cast<int64_t>(
      operand.memory.displacement); // Displacement is signed

  spdlog::trace("Calculated memory address: 0x{:x}", addr);
  return addr;
}

void X86_64CPU::WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                                   uint64_t value) {
  uint8_t sizeInBits = operand.size;
  uint64_t addr = CalculateMemoryAddress(operand);
  size_t bytesToWrite = sizeInBits / 8;
  try {
    mmu.WriteVirtual(addr, &value, bytesToWrite, GetProcessId());
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Memory write failed at 0x{:x}: {}", m_cpuId,
                  addr, e.what());
    TriggerInterrupt(EXC_PF, addr, false); // Page Fault
    throw CPUException("Memory write error");
  }
  spdlog::trace("Write memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                bytesToWrite);
}

void X86_64CPU::WriteXmmOperand(const DecodedInstruction::Operand &operand,
                                const __m256i &value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  xmmRegisters[static_cast<size_t>(operand.reg) -
               static_cast<size_t>(Register::XMM0)] = value;
  spdlog::trace("Write XMM{}: value set", static_cast<int>(operand.reg));
}

__m256i
X86_64CPU::ReadXmmOperandValue(const DecodedInstruction::Operand &operand) {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM read: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM read");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                             static_cast<size_t>(Register::XMM0)];

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Zero upper 128 bits, keep lower 128 bits
    return _mm256_inserti128_si256(_mm256_setzero_si256(),
                                   _mm256_extracti128_si256(reg, 0), 0);
  case 256: // YMM register (256-bit)
    return reg;
  default:
    // For smaller sizes, return the full register and let caller handle
    // extraction
    spdlog::warn("ReadXmmOperandValue called for size {} bits, returning full "
                 "256-bit register. Caller should mask.",
                 sizeInBits);
    return reg;
  }
}

void X86_64CPU::WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                                     const __m256i &value) {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM write: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM write");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t regIndex =
      static_cast<size_t>(operand.reg) - static_cast<size_t>(Register::XMM0);

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Preserve upper 128 bits, set lower 128 bits
    xmmRegisters[regIndex] = _mm256_inserti128_si256(
        xmmRegisters[regIndex],                 // Keep the full register
        _mm256_extracti128_si256(value, 0), 0); // Set lower 128 bits
    break;
  case 256: // YMM register (256-bit)
    xmmRegisters[regIndex] = value;
    break;
  default:
    // For other sizes, preserve upper bits and modify only the relevant
    // portion This is a simplification; proper handling would involve
    // _mm256_insert_epiXX
    spdlog::warn("WriteXmmOperandValue called for size {} bits, writing full "
                 "256-bit register. This might be incorrect.",
                 sizeInBits);
    xmmRegisters[regIndex] = value;
    break;
  }

  spdlog::trace("Write XMM{}: full register updated, size={} bits",
                static_cast<int>(operand.reg), sizeInBits);
}

void X86_64CPU::FetchDecodeExecute() {
  uint64_t rip = GetRegister(Register::RIP);
  DecodedInstruction instr;

  try {
    // Comprehensive RIP validation
    if (rip == 0 || rip == 0xDEADBEEF || rip == 0xCCCCCCCC ||
        rip == 0xFEEEFEEE || rip < 0x1000 || rip >= 0x800000000000ULL) {
      spdlog::critical("X86_64CPU[{}]: CRITICAL - Invalid RIP detected: 0x{:x}",
                       m_cpuId, rip);
      spdlog::critical("X86_64CPU[{}]: This indicates severe memory corruption "
                       "or deadlock. Attempting recovery.",
                       m_cpuId);

      // Emergency RIP recovery - try to set to a safe default or previous
      // known good value
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      if (registers[static_cast<size_t>(Register::RSP)] != 0) {
        try {
          uint64_t stackTop = registers[static_cast<size_t>(Register::RSP)];
          if (stackTop > 0x1000 && stackTop < 0x800000000000ULL) {
            lock.unlock(); // Release lock for MMU access
            uint64_t returnAddr = 0;
            if (mmu.ReadVirtual(stackTop, &returnAddr, 8, processId) &&
                returnAddr > 0x1000 && returnAddr < 0x800000000000ULL) {
              lock.lock(); // Re-acquire lock
              _setRegister(Register::RIP, returnAddr);
              _setRegister(Register::RSP, stackTop + 8);
              spdlog::warn("X86_64CPU[{}]: Emergency RIP recovery to 0x{:x} "
                           "from stack",
                           m_cpuId, returnAddr);
              return; // Successfully recovered, try again next cycle
            }
          }
        } catch (...) {
          // Recovery failed, continue with error handling
        }
      }

      // If recovery fails, stop execution to prevent cascade
      running = false;
      lock.unlock();
      TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
      return;
    }

    std::vector<uint8_t> instBuffer(INSTRUCTION_BUFFER_SIZE);
    try {
      if (!mmu.ReadVirtual(rip, instBuffer.data(), instBuffer.size(),
                           GetProcessId())) {
        spdlog::error("X86_64CPU[{}]: Memory read failed at RIP=0x{:x}",
                      m_cpuId, rip);
        TriggerInterrupt(EXC_PF, rip, false); // Page Fault
        return;
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Exception reading memory at RIP=0x{:x}: {}",
                    m_cpuId, rip, e.what());
      TriggerInterrupt(EXC_PF, rip, false); // Page Fault
      return;
    }

    // Decode the instruction
    auto decodeInfo =
        decoder->Decode(rip, instBuffer.data(), instBuffer.size(), instr);

    if (decodeInfo.error != DecoderError::Success ||
        instr.instType == InstructionType::Unknown || instr.length == 0 ||
        instr.length > MAX_INSTRUCTION_LENGTH) {
      spdlog::warn("X86_64CPU[{}]: Decode failed at 0x{:x}: error={}, type={}, "
                   "length={}",
                   m_cpuId, rip, static_cast<int>(decodeInfo.error),
                   static_cast<int>(instr.instType), instr.length);
      TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
      return;
    }

    if (!instr.validate()) {
      spdlog::error("X86_64CPU[{}]: Invalid instruction structure at 0x{:x}",
                    m_cpuId, rip);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    uint64_t nextRip = rip + instr.length;
    spdlog::trace("X86_64CPU[{}]: Interpreting at 0x{:x}: type={}, length={}",
                  m_cpuId, rip, static_cast<int>(instr.instType), instr.length);

    // Handle REP prefixes for string operations
    bool repActive = (instr.instType >= InstructionType::Movsb &&
                      instr.instType <= InstructionType::Cmpsq) &&
                     (instr.repPrefix || instr.repePrefix || instr.repnePrefix);

    if (repActive) {
      ExecuteStringOperation(instr, nextRip);
    } else {
      // Execute non-string instruction
      switch (instr.instType) {
      case InstructionType::Nop:
        break;
      case InstructionType::Mov: {
        uint64_t value = ReadOperandValue(instr.operands[1]);
        WriteOperandValue(instr.operands[0], value);
        spdlog::trace("MOV: dst=0x{:x}, src=0x{:x}",
                      static_cast<int>(instr.operands[0].reg), value);
        break;
      }
      case InstructionType::Add: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 + op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, false);
        spdlog::trace("ADD: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Sub: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 - op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, true);
        spdlog::trace("SUB: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Inc: {
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint64_t result = op + 1;
        WriteOperandValue(instr.operands[0], result);
        // INC does not affect CF
        UpdateArithmeticFlags(op, 1, result, instr.operands[0].size, false);
        SetFlag(FLAG_CF, GetFlag(FLAG_CF)); // Preserve CF
        spdlog::trace("INC: op=0x{:x}, result=0x{:x}", op, result);
        break;
      }
      case InstructionType::Dec: {
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint64_t result = op - 1;
        WriteOperandValue(instr.operands[0], result);
        // DEC does not affect CF
        UpdateArithmeticFlags(op, 1, result, instr.operands[0].size, true);
        SetFlag(FLAG_CF, GetFlag(FLAG_CF)); // Preserve CF
        spdlog::trace("DEC: op=0x{:x}, result=0x{:x}", op, result);
        break;
      }
      case InstructionType::Neg: {
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint64_t result = (~op + 1) & ((1ULL << instr.operands[0].size) - 1);
        WriteOperandValue(instr.operands[0], result);
        // CF is set if operand is not 0
        SetFlag(FLAG_CF, op != 0);
        // OF is set if operand is 0x80...0 (most negative number)
        SetFlag(FLAG_OF, op == (1ULL << (instr.operands[0].size - 1)));
        UpdateLogicalFlags(result, instr.operands[0].size); // ZF, SF, PF
        spdlog::trace("NEG: op=0x{:x}, result=0x{:x}", op, result);
        break;
      }
      case InstructionType::Not: {
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint64_t result = ~op;
        WriteOperandValue(instr.operands[0], result);
        // NOT does not affect any flags
        spdlog::trace("NOT: op=0x{:x}, result=0x{:x}", op, result);
        break;
      }
      case InstructionType::And: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 & op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateLogicalFlags(result, instr.operands[0].size);
        spdlog::trace("AND: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Or: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 | op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateLogicalFlags(result, instr.operands[0].size);
        spdlog::trace("OR: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Xor: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 ^ op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateLogicalFlags(result, instr.operands[0].size);
        spdlog::trace("XOR: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Cmp: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result =
            op1 - op2; // CMP performs subtraction but discards result
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, true);
        spdlog::trace("CMP: op1=0x{:x}, op2=0x{:x}", op1, op2);
        break;
      }
      case InstructionType::Test: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 & op2; // TEST performs AND but discards result
        UpdateLogicalFlags(result, instr.operands[0].size);
        spdlog::trace("TEST: op1=0x{:x}, op2=0x{:x}", op1, op2);
        break;
      }
      case InstructionType::Shl:
      case InstructionType::Sal: { // Shift Left / Arithmetic Shift Left
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint8_t count =
            static_cast<uint8_t>(ReadOperandValue(instr.operands[1]));
        uint8_t size = instr.operands[0].size;
        uint64_t mask = (1ULL << size) - 1;

        if (count == 0)
          break;
        if (count >= size) { // Shift by more than operand size
          WriteOperandValue(instr.operands[0], 0);
          SetFlag(FLAG_CF, false);     // CF is undefined for count >= size
          SetFlag(FLAG_OF, false);     // OF is undefined
          UpdateLogicalFlags(0, size); // ZF, SF, PF
          break;
        }

        uint64_t result = (op << count) & mask;
        WriteOperandValue(instr.operands[0], result);

        SetFlag(FLAG_CF, (op >> (size - count)) & 1); // Last bit shifted out
        if (count == 1) {
          SetFlag(FLAG_OF,
                  ((result >> (size - 1)) & 1) != ((op >> (size - 1)) & 1));
        } else {
          SetFlag(FLAG_OF, false); // OF undefined for count > 1
        }
        UpdateLogicalFlags(result, size); // ZF, SF, PF
        spdlog::trace("SHL/SAL: op=0x{:x}, count={}, result=0x{:x}", op, count,
                      result);
        break;
      }
      case InstructionType::Shr: { // Shift Right Logical
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint8_t count =
            static_cast<uint8_t>(ReadOperandValue(instr.operands[1]));
        uint8_t size = instr.operands[0].size;
        uint64_t mask = (1ULL << size) - 1;

        if (count == 0)
          break;
        if (count >= size) {
          WriteOperandValue(instr.operands[0], 0);
          SetFlag(FLAG_CF, false);
          SetFlag(FLAG_OF, false);
          UpdateLogicalFlags(0, size);
          break;
        }

        uint64_t result = (op >> count) & mask;
        WriteOperandValue(instr.operands[0], result);

        SetFlag(FLAG_CF, (op >> (count - 1)) & 1); // Last bit shifted out
        if (count == 1) {
          SetFlag(FLAG_OF,
                  ((op >> (size - 1)) & 1)); // OF is SF of original operand
        } else {
          SetFlag(FLAG_OF, false); // OF undefined for count > 1
        }
        UpdateLogicalFlags(result, size); // ZF, SF, PF
        spdlog::trace("SHR: op=0x{:x}, count={}, result=0x{:x}", op, count,
                      result);
        break;
      }
      case InstructionType::Sar: { // Shift Right Arithmetic
        int64_t op = static_cast<int64_t>(ReadOperandValue(instr.operands[0]));
        uint8_t count =
            static_cast<uint8_t>(ReadOperandValue(instr.operands[1]));
        uint8_t size = instr.operands[0].size;
        uint64_t mask = (1ULL << size) - 1;

        if (count == 0)
          break;
        if (count >= size) {
          // For SAR, shifting by >= size copies sign bit
          uint64_t result = (op < 0) ? mask : 0;
          WriteOperandValue(instr.operands[0], result);
          SetFlag(FLAG_CF, (op >> (count - 1)) & 1); // Last bit shifted out
          SetFlag(FLAG_OF, false);                   // OF is 0
          UpdateLogicalFlags(result, size);
          break;
        }

        int64_t result = op >> count;
        WriteOperandValue(instr.operands[0],
                          static_cast<uint64_t>(result) & mask);

        SetFlag(FLAG_CF, (op >> (count - 1)) & 1); // Last bit shifted out
        SetFlag(FLAG_OF, false);                   // OF is always 0 for SAR
        UpdateLogicalFlags(static_cast<uint64_t>(result) & mask,
                           size); // ZF, SF, PF
        spdlog::trace("SAR: op=0x{:x}, count={}, result=0x{:x}", op, count,
                      result);
        break;
      }
      case InstructionType::Rol: { // Rotate Left
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint8_t count =
            static_cast<uint8_t>(ReadOperandValue(instr.operands[1])) %
            instr.operands[0].size;
        uint8_t size = instr.operands[0].size;
        uint64_t mask = (1ULL << size) - 1;

        if (count == 0)
          break;

        uint64_t result = ((op << count) | (op >> (size - count))) & mask;
        WriteOperandValue(instr.operands[0], result);

        SetFlag(FLAG_CF, (result & 1)); // Last bit rotated into CF
        if (count == 1) {
          SetFlag(FLAG_OF, ((result >> (size - 1)) & 1) != (GetFlag(FLAG_CF)));
        } else {
          SetFlag(FLAG_OF, false); // OF undefined for count > 1
        }
        // ROL does not affect ZF, SF, PF, AF
        spdlog::trace("ROL: op=0x{:x}, count={}, result=0x{:x}", op, count,
                      result);
        break;
      }
      case InstructionType::Ror: { // Rotate Right
        uint64_t op = ReadOperandValue(instr.operands[0]);
        uint8_t count =
            static_cast<uint8_t>(ReadOperandValue(instr.operands[1])) %
            instr.operands[0].size;
        uint8_t size = instr.operands[0].size;
        uint64_t mask = (1ULL << size) - 1;

        if (count == 0)
          break;

        uint64_t result = ((op >> count) | (op << (size - count))) & mask;
        WriteOperandValue(instr.operands[0], result);

        SetFlag(FLAG_CF,
                ((result >> (size - 1)) & 1)); // Last bit rotated into CF
        if (count == 1) {
          SetFlag(FLAG_OF,
                  ((result >> (size - 1)) & 1) != ((result >> (size - 2)) & 1));
        } else {
          SetFlag(FLAG_OF, false); // OF undefined for count > 1
        }
        // ROR does not affect ZF, SF, PF, AF
        spdlog::trace("ROR: op=0x{:x}, count={}, result=0x{:x}", op, count,
                      result);
        break;
      }
      case InstructionType::Push: {
        uint64_t value = ReadOperandValue(instr.operands[0]);
        Push(value, instr.operands[0].size / 8);
        spdlog::trace("PUSH: value=0x{:x}", value);
        break;
      }
      case InstructionType::Pop: {
        uint64_t value = Pop(instr.operands[0].size / 8);
        WriteOperandValue(instr.operands[0], value);
        spdlog::trace("POP: value=0x{:x}", value);
        break;
      }
      case InstructionType::Call: {
        uint64_t currentRip = GetRegister(Register::RIP);
        uint64_t currentRsp = GetRegister(Register::RSP);

        if (currentRsp < 16 ||
            currentRsp - 8 < 0x1000) { // Basic stack overflow check
          spdlog::error(
              "X86_64CPU[{}]: Stack overflow in CALL at 0x{:x}, RSP=0x{:x}",
              m_cpuId, currentRip, currentRsp);
          TriggerInterrupt(EXC_SS, 0, false); // Stack Segment Fault
          return;
        }

        uint64_t target;
        if (instr.operands[0].type ==
            DecodedInstruction::Operand::Type::IMMEDIATE) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          target = nextRip + displacement;
        } else {
          target = ReadOperandValue(instr.operands[0]);
        }

        if (target < 0x1000 || target >= 0x800000000000ULL) {
          spdlog::error(
              "X86_64CPU[{}]: Invalid call target: 0x{:x} from 0x{:x}", m_cpuId,
              target, currentRip);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        Push(nextRip, 8); // Push return address (RIP of next instruction)
        nextRip = target;
        spdlog::trace(
            "CALL: from 0x{:x} to 0x{:x}, return address 0x{:x} pushed",
            currentRip, target, nextRip);
        break;
      }
      case InstructionType::Ret: {
        uint64_t currentRip = GetRegister(Register::RIP);
        uint64_t currentRsp = GetRegister(Register::RSP);

        if (currentRsp + 8 > DEFAULT_STACK_POINTER ||
            currentRsp >= 0x800000000000ULL) {
          spdlog::error(
              "X86_64CPU[{}]: Stack underflow in RET at 0x{:x}, RSP=0x{:x}",
              m_cpuId, currentRip, currentRsp);
          TriggerInterrupt(EXC_SS, 0, false);
          return;
        }

        uint64_t returnAddress = Pop(8);

        if (instr.operandCount == 1) { // RET with immediate (stack cleanup)
          uint16_t stackCleanup =
              static_cast<uint16_t>(instr.operands[0].immediate);
          SetRegister(Register::RSP, GetRegister(Register::RSP) + stackCleanup);
          spdlog::trace("RET {}: cleaned up {} bytes from stack", stackCleanup,
                        stackCleanup);
        }

        if (returnAddress < 0x1000 || returnAddress >= 0x800000000000ULL) {
          spdlog::error(
              "X86_64CPU[{}]: Invalid return address: 0x{:x} from 0x{:x}",
              m_cpuId, returnAddress, currentRip);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        nextRip = returnAddress;
        spdlog::trace("RET: from 0x{:x} to 0x{:x}", currentRip, returnAddress);
        break;
      }
      case InstructionType::Call_far: {
        uint64_t currentRip = GetRegister(Register::RIP);
        uint16_t currentCS = GetCS();

        if (instr.operandCount >= 2) {
          uint16_t newCS =
              static_cast<uint16_t>(ReadOperandValue(instr.operands[1]));
          uint64_t newRIP = ReadOperandValue(instr.operands[0]);

          if (newRIP < 0x1000 || newRIP >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid far call target: CS=0x{:x} "
                          "RIP=0x{:x}",
                          m_cpuId, newCS, newRIP);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }

          Push(currentCS, 8); // Push current CS (padded to 8 bytes)
          Push(nextRip, 8);   // Push current RIP (padded to 8 bytes)

          SetCS(newCS);
          nextRip = newRIP;

          spdlog::trace(
              "CALL FAR: from CS=0x{:x} RIP=0x{:x} to CS=0x{:x} RIP=0x{:x}",
              currentCS, currentRip, newCS, newRIP);
        } else {
          TriggerInterrupt(EXC_UD, 0, false);
          return;
        }
        break;
      }
      case InstructionType::Ret_far: {
        uint64_t currentRip = GetRegister(Register::RIP);

        uint64_t returnRIP = Pop(8);
        uint64_t returnCS = Pop(8);

        if (instr.operandCount == 1) { // RETF with immediate (stack cleanup)
          uint16_t stackCleanup =
              static_cast<uint16_t>(instr.operands[0].immediate);
          SetRegister(Register::RSP, GetRegister(Register::RSP) + stackCleanup);
          spdlog::trace("RETF {}: cleaned up {} bytes from stack", stackCleanup,
                        stackCleanup);
        }

        if (returnRIP < 0x1000 || returnRIP >= 0x800000000000ULL) {
          spdlog::error("X86_64CPU[{}]: Invalid far return address: "
                        "CS=0x{:x} RIP=0x{:x}",
                        m_cpuId, static_cast<uint16_t>(returnCS), returnRIP);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        SetCS(static_cast<uint16_t>(returnCS));
        nextRip = returnRIP;

        spdlog::debug("X86_64CPU[{}]: RET FAR: to CS=0x{:x} RIP=0x{:x}",
                      m_cpuId, static_cast<uint16_t>(returnCS), returnRIP);
        break;
      }
      case InstructionType::Jmp: {
        uint64_t target;
        if (instr.operands[0].type ==
            DecodedInstruction::Operand::Type::IMMEDIATE) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          target = nextRip + displacement;
        } else {
          target = ReadOperandValue(instr.operands[0]);
        }

        if (target < 0x1000 || target >= 0x800000000000ULL) {
          spdlog::error("X86_64CPU[{}]: Invalid JMP target: 0x{:x} from 0x{:x}",
                        m_cpuId, target, rip);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        nextRip = target;
        spdlog::trace("JMP: to 0x{:x}", target);
        break;
      }
      case InstructionType::Jcc: {
        uint8_t conditionCode = instr.conditionCode;
        bool conditionMet = CheckCondition(conditionCode);

        if (conditionMet) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;

          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid Jcc target: 0x{:x}", m_cpuId,
                          target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("JCC (0x{:x}): condition met, jumping to 0x{:x}",
                        conditionCode, target);
        } else {
          spdlog::trace("JCC (0x{:x}): condition not met, continuing",
                        conditionCode);
        }
        break;
      }
      case InstructionType::Loop: {
        uint64_t rcx = GetRegister(Register::RCX);
        rcx--;
        SetRegister(Register::RCX, rcx);

        if (rcx != 0) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;

          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid loop target: 0x{:x}", m_cpuId,
                          target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("LOOP: RCX=0x{:x}, jumping to 0x{:x}", rcx, target);
        } else {
          spdlog::trace("LOOP: RCX=0, continuing");
        }
        break;
      }
      case InstructionType::Loope: {
        uint64_t rcx = GetRegister(Register::RCX);
        rcx--;
        SetRegister(Register::RCX, rcx);

        if (rcx != 0 && GetFlag(FLAG_ZF)) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;

          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid loope target: 0x{:x}",
                          m_cpuId, target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("LOOPE: RCX=0x{:x}, ZF=1, jumping to 0x{:x}", rcx,
                        target);
        } else {
          spdlog::trace("LOOPE: RCX=0x{:x}, ZF={}, continuing", rcx,
                        GetFlag(FLAG_ZF) ? 1 : 0);
        }
        break;
      }
      case InstructionType::Loopne: {
        uint64_t rcx = GetRegister(Register::RCX);
        rcx--;
        SetRegister(Register::RCX, rcx);

        if (rcx != 0 && !GetFlag(FLAG_ZF)) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;

          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid loopne target: 0x{:x}",
                          m_cpuId, target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("LOOPNE: RCX=0x{:x}, ZF=0, jumping to 0x{:x}", rcx,
                        target);
        } else {
          spdlog::trace("LOOPNE: RCX=0x{:x}, ZF={}, continuing", rcx,
                        GetFlag(FLAG_ZF) ? 1 : 0);
        }
        break;
      }
      case InstructionType::Jecxz: {
        uint32_t ecx = static_cast<uint32_t>(GetRegister(Register::RCX));
        if (ecx == 0) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;
          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid jecxz target: 0x{:x}",
                          m_cpuId, target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("JECXZ: ECX=0, jumping to 0x{:x}", target);
        } else {
          spdlog::trace("JECXZ: ECX=0x{:x}, continuing", ecx);
        }
        break;
      }
      case InstructionType::Jrcxz: {
        uint64_t rcx = GetRegister(Register::RCX);
        if (rcx == 0) {
          int64_t displacement =
              static_cast<int64_t>(instr.operands[0].immediate);
          uint64_t target = nextRip + displacement;
          if (target < 0x1000 || target >= 0x800000000000ULL) {
            spdlog::error("X86_64CPU[{}]: Invalid jrcxz target: 0x{:x}",
                          m_cpuId, target);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          nextRip = target;
          spdlog::trace("JRCXZ: RCX=0, jumping to 0x{:x}", target);
        } else {
          spdlog::trace("JRCXZ: RCX=0x{:x}, continuing", rcx);
        }
        break;
      }
      case InstructionType::Iret:    // IRET (16-bit)
      case InstructionType::Iretd:   // IRETD (32-bit)
      case InstructionType::Iretq: { // IRETQ (64-bit)
        uint64_t currentRip = GetRegister(Register::RIP);
        uint64_t currentRsp = GetRegister(Register::RSP);

        if (currentRsp < 0x1000) { // Basic stack sanity check
          spdlog::error("X86_64CPU[{}]: Stack underflow in IRET/D/Q at 0x{:x}, "
                        "RSP=0x{:x}",
                        m_cpuId, currentRip, currentRsp);
          TriggerInterrupt(EXC_SS, 0, false);
          return;
        }

        uint64_t newRIP, newCS, newRFLAGS, newRSP = 0, newSS = 0;

        if (instr.instType == InstructionType::Iretq) { // 64-bit IRETQ
          newRIP = Pop(8);
          newCS = Pop(8);
          newRFLAGS = Pop(8);
          newRSP = Pop(8);
          newSS = Pop(8);
        } else if (instr.instType == InstructionType::Iretd) { // 32-bit IRETD
          newRIP = Pop(4) & 0xFFFFFFFF;
          newCS = Pop(4) & 0xFFFFFFFF;
          newRFLAGS = Pop(4) & 0xFFFFFFFF;
          // In 32-bit mode, RSP and SS are not popped by IRETD
        } else { // 16-bit IRET
          newRIP = Pop(2) & 0xFFFF;
          newCS = Pop(2) & 0xFFFF;
          newRFLAGS = Pop(2) & 0xFFFF;
          // In 16-bit mode, RSP and SS are not popped by IRET
        }

        if (newRIP < 0x1000 || newRIP >= 0x800000000000ULL) {
          spdlog::error("X86_64CPU[{}]: Invalid IRET/D/Q return RIP: 0x{:x}",
                        m_cpuId, newRIP);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        SetRflags(newRFLAGS);
        SetCS(static_cast<uint16_t>(newCS));
        if (instr.instType == InstructionType::Iretq) {
          SetRegister(Register::RSP, newRSP);
          SetSS(static_cast<uint16_t>(newSS));
        }
        nextRip = newRIP;

        spdlog::debug(
            "X86_64CPU[{}]: IRET/D/Q to CS=0x{:x} RIP=0x{:x} RFLAGS=0x{:x}",
            m_cpuId, static_cast<uint16_t>(newCS), newRIP, newRFLAGS);
        break;
      }
      case InstructionType::Cmovcc: {
        uint8_t conditionCode = instr.conditionCode;
        bool conditionMet = CheckCondition(conditionCode);

        if (conditionMet) {
          uint64_t value = ReadOperandValue(instr.operands[1]);
          WriteOperandValue(instr.operands[0], value);
          spdlog::trace("CMOV{:02X}: condition met, moved 0x{:x}",
                        conditionCode, value);
        } else {
          spdlog::trace("CMOV{:02X}: condition not met", conditionCode);
        }
        break;
      }
      case InstructionType::Setcc: {
        uint8_t conditionCode = instr.conditionCode;
        bool conditionMet = CheckCondition(conditionCode);

        uint8_t result = conditionMet ? 1 : 0;
        WriteOperandValue(instr.operands[0], result);
        spdlog::trace("SET{:02X}: condition {}, set to {}", conditionCode,
                      conditionMet ? "met" : "not met", result);
        break;
      }
      case InstructionType::Cpuid: {
        // Basic CPUID implementation
        uint64_t eax = GetRegister(Register::RAX);
        uint64_t ecx = GetRegister(Register::RCX);
        uint64_t ebx_val = 0, ecx_val = 0, edx_val = 0;

        switch (eax) {
        case 0x0:                           // Vendor ID and Max Function
          ebx_val = 0x756E6547;             // "Genu"
          edx_val = 0x4E6C6574;             // "ntel"
          ecx_val = 0x696E6549;             // "ineI"
          SetRegister(Register::RAX, 0x10); // Max function supported
          break;
        case 0x1: // Processor Info and Feature Bits
          SetRegister(Register::RAX,
                      0x000706A0); // Family 6, Model 7, Stepping 10 (Example)
          ebx_val = 0;             // Brand ID, CLFLUSH size, etc.

          // Build ECX feature flags based on actual CPU capabilities
          ecx_val = 0;
          if (g_cpuFeatures.sse3)
            ecx_val |= (1 << 0); // SSE3
          if (g_cpuFeatures.ssse3)
            ecx_val |= (1 << 9); // SSSE3
          if (g_cpuFeatures.sse41)
            ecx_val |= (1 << 19); // SSE4.1
          if (g_cpuFeatures.sse42)
            ecx_val |= (1 << 20); // SSE4.2
          if (g_cpuFeatures.avx)
            ecx_val |= (1 << 28); // AVX

          // Build EDX feature flags based on actual CPU capabilities
          edx_val = (1 << 17); // PSE (always supported in emulator)
          if (g_cpuFeatures.sse)
            edx_val |= (1 << 25); // SSE
          if (g_cpuFeatures.sse2)
            edx_val |= (1 << 26);                       // SSE2
          edx_val |= (1 << 23) | (1 << 24) | (1 << 28); // MMX, FXSR, HTT
          break;
        case 0x7: // Extended Features (Leaf 7, Sub-leaf 0)
          if (ecx == 0) {
            SetRegister(Register::RAX, 0); // Max sub-leaf

            // Build EBX feature flags based on actual CPU capabilities
            ebx_val = (1 << 3) | (1 << 8) | (1 << 18) |
                      (1 << 20); // BMI1, BMI2, RDSEED, SMAP (always supported)
            if (g_cpuFeatures.avx2)
              ebx_val |= (1 << 5); // AVX2
            if (g_cpuFeatures.avx512f)
              ebx_val |= (1 << 16); // AVX-512F

            ecx_val = 0;
            edx_val = 0;
          }
          break;
        case 0x80000000: // Max Extended Function
          SetRegister(Register::RAX,
                      0x80000008); // Max extended function supported
          break;
        case 0x80000001: // Extended Processor Info and Feature Bits
          ecx_val = (1 << 5) | (1 << 8); // LZCNT, PREFETCHW
          edx_val = (1 << 20) | (1 << 26) | (1 << 27) |
                    (1 << 29); // NX, Page1GB, RDTSCP, LM
          break;
        case 0x80000008: // Virtual and Physical Address Sizes
          SetRegister(Register::RAX,
                      (48ULL << 16) | 48ULL); // 48-bit virtual, 48-bit physical
          break;
        default:
          spdlog::warn("X86_64CPU[{}]: Unhandled CPUID leaf: 0x{:x}", m_cpuId,
                       eax);
          SetRegister(Register::RAX, 0);
          ebx_val = 0;
          ecx_val = 0;
          edx_val = 0;
          break;
        }
        SetRegister(Register::RBX, ebx_val);
        SetRegister(Register::RCX, ecx_val);
        SetRegister(Register::RDX, edx_val);
        spdlog::trace("CPUID: EAX=0x{:x}, EBX=0x{:x}, ECX=0x{:x}, EDX=0x{:x}",
                      GetRegister(Register::RAX), GetRegister(Register::RBX),
                      GetRegister(Register::RCX), GetRegister(Register::RDX));
        break;
      }
      case InstructionType::Rdtsc: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        uint64_t tsc_value = msrRegisters[static_cast<uint32_t>(MSR::TSC)];
        // Increment TSC for next read (simple approximation)
        msrRegisters[static_cast<uint32_t>(MSR::TSC)] +=
            100; // Arbitrary increment
        lock.unlock();
        SetRegister(Register::RAX, tsc_value & 0xFFFFFFFF);
        SetRegister(Register::RDX, tsc_value >> 32);
        spdlog::trace("RDTSC: RAX=0x{:x}, RDX=0x{:x}",
                      GetRegister(Register::RAX), GetRegister(Register::RDX));
        break;
      }
      case InstructionType::Syscall: {
        // Check CPL (Current Privilege Level)
        if (GetCPL() != 3) { // SYSCALL is typically from CPL3 to CPL0
          spdlog::error("X86_64CPU[{}]: SYSCALL from non-CPL3 (CPL={})",
                        m_cpuId, GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        uint64_t currentRip = GetRegister(Register::RIP);
        uint64_t currentRsp = GetRegister(Register::RSP);
        uint64_t currentRflags = GetRflags();

        // Save current RIP to RCX (as per convention)
        SetRegister(Register::RCX, nextRip);

        // Save RFLAGS to R11 (as per convention)
        SetRegister(Register::R11, currentRflags);

        // Load new CS, SS from STAR MSR
        uint64_t star_msr = GetMSR(MSR::STAR);
        uint16_t newCS =
            static_cast<uint16_t>((star_msr >> 32) & 0xFFFC); // KERNEL_CS
        uint16_t newSS = static_cast<uint16_t>((star_msr >> 32) & 0xFFFC) +
                         8; // KERNEL_DS (assuming flat model)

        // Load new RIP from LSTAR MSR
        uint64_t lstar_msr = GetMSR(MSR::LSTAR);
        if (lstar_msr == 0) {
          spdlog::error(
              "X86_64CPU[{}]: SYSCALL LSTAR MSR is 0, cannot handle syscall",
              m_cpuId);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        // Mask RFLAGS based on SFMASK MSR
        uint64_t sfmask_msr = GetMSR(MSR::SFMASK);
        SetRflags(currentRflags & ~sfmask_msr);

        SetCS(newCS);
        SetSS(newSS);
        nextRip = lstar_msr; // Jump to SYSCALL handler

        spdlog::trace("SYSCALL: from 0x{:x} to 0x{:x}, CS=0x{:x}, SS=0x{:x}",
                      currentRip, nextRip, newCS, newSS);
        break;
      }
      case InstructionType::Sysret: {
        // Check CPL
        if (GetCPL() != 0) { // SYSRET is typically from CPL0 to CPL3
          spdlog::error("X86_64CPU[{}]: SYSRET from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }

        uint64_t currentRip = GetRegister(Register::RIP);
        uint64_t currentRsp = GetRegister(Register::RSP);

        // Restore RFLAGS from R11
        SetRflags(GetRegister(Register::R11));

        // Restore RIP from RCX
        nextRip = GetRegister(Register::RCX);

        // Load new CS, SS from STAR MSR (user segments)
        uint64_t star_msr = GetMSR(MSR::STAR);
        uint16_t newCS = static_cast<uint16_t>((star_msr >> 48) & 0xFFFC) +
                         16; // USER_CS (assuming flat model)
        uint16_t newSS = static_cast<uint16_t>((star_msr >> 48) & 0xFFFC) +
                         8; // USER_DS (assuming flat model)

        SetCS(newCS);
        SetSS(newSS);

        spdlog::trace("SYSRET: from 0x{:x} to 0x{:x}, CS=0x{:x}, SS=0x{:x}",
                      currentRip, nextRip, newCS, newSS);
        break;
      }
      case InstructionType::Hlt: {
        // Check CPL
        if (GetCPL() != 0) { // HLT is a privileged instruction
          spdlog::error("X86_64CPU[{}]: HLT from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        spdlog::info("X86_64CPU[{}]: HLT instruction executed. CPU halted.",
                     m_cpuId);
        halted = true;
        break;
      }
      case InstructionType::Cli: {
        // Check CPL and IOPL
        if (GetCPL() > GetIOPL()) { // CLI is a privileged instruction
          spdlog::error("X86_64CPU[{}]: CLI from insufficient privilege "
                        "(CPL={}, IOPL={})",
                        m_cpuId, GetCPL(), GetIOPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        SetFlag(FLAG_IF, false);
        spdlog::trace("CLI: Interrupts disabled.");
        break;
      }
      case InstructionType::Sti: {
        // Check CPL and IOPL
        if (GetCPL() > GetIOPL()) { // STI is a privileged instruction
          spdlog::error("X86_64CPU[{}]: STI from insufficient privilege "
                        "(CPL={}, IOPL={})",
                        m_cpuId, GetCPL(), GetIOPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        SetFlag(FLAG_IF, true);
        spdlog::trace("STI: Interrupts enabled.");
        break;
      }
      case InstructionType::Invd: {
        // Check CPL
        if (GetCPL() != 0) { // INVD is a privileged instruction
          spdlog::error("X86_64CPU[{}]: INVD from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        spdlog::warn("X86_64CPU[{}]: INVD instruction executed. Cache "
                     "invalidation not fully emulated.",
                     m_cpuId);
        // Invalidate internal caches (TLB, instruction cache, data cache)
        tlb.Clear();
        jit->ClearCache(); // Clear JIT cache
        break;
      }
      case InstructionType::Wbinvd: {
        // Check CPL
        if (GetCPL() != 0) { // WBINVD is a privileged instruction
          spdlog::error("X86_64CPU[{}]: WBINVD from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        spdlog::warn("X86_64CPU[{}]: WBINVD instruction executed. Cache "
                     "write-back and invalidation not fully emulated.",
                     m_cpuId);
        // Invalidate internal caches (TLB, instruction cache, data cache)
        tlb.Clear();
        jit->ClearCache(); // Clear JIT cache
        // For a real emulator, this would involve flushing dirty cache lines
        // to main memory
        break;
      }
      case InstructionType::Xgetbv: {
        // Check CPL
        if (GetCPL() != 0) { // XGETBV is a privileged instruction
          spdlog::error("X86_64CPU[{}]: XGETBV from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint64_t ecx_val = GetRegister(Register::RCX) & 0xFFFFFFFF;
        uint64_t xcr0_value =
            0; // XCR0 is the only XCR register currently defined

        if (ecx_val == 0) { // XCR0
          // XCR0 bits: X87, SSE, AVX, MPX, AVX512, etc.
          // For now, assume X87 and SSE are enabled
          xcr0_value = (1ULL << 0) | (1ULL << 1); // X87 and SSE state
#ifdef __AVX__
          xcr0_value |= (1ULL << 2); // AVX state
#endif
        } else {
          spdlog::warn(
              "X86_64CPU[{}]: XGETBV with unhandled XCR register: 0x{:x}",
              m_cpuId, ecx_val);
          TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
          return;
        }
        SetRegister(Register::RAX, xcr0_value & 0xFFFFFFFF);
        SetRegister(Register::RDX, xcr0_value >> 32);
        spdlog::trace("XGETBV: ECX=0x{:x}, RAX=0x{:x}, RDX=0x{:x}", ecx_val,
                      GetRegister(Register::RAX), GetRegister(Register::RDX));
        break;
      }
      case InstructionType::Xsetbv: {
        // Check CPL
        if (GetCPL() != 0) { // XSETBV is a privileged instruction
          spdlog::error("X86_64CPU[{}]: XSETBV from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint64_t ecx_val = GetRegister(Register::RCX) & 0xFFFFFFFF;
        uint64_t new_xcr_value = (GetRegister(Register::RDX) << 32) |
                                 (GetRegister(Register::RAX) & 0xFFFFFFFF);

        if (ecx_val == 0) { // XCR0
          // Only allow setting bits that are supported by the emulator
          uint64_t supported_xcr0_mask = (1ULL << 0) | (1ULL << 1); // X87, SSE
#ifdef __AVX__
          supported_xcr0_mask |= (1ULL << 2); // AVX
#endif
          if ((new_xcr_value & ~supported_xcr0_mask) != 0) {
            spdlog::error("X86_64CPU[{}]: XSETBV: Attempted to set unsupported "
                          "XCR0 bits: 0x{:x}",
                          m_cpuId, new_xcr_value);
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
          // In a real emulator, this would enable/disable FPU/SSE/AVX
          // features
          spdlog::trace("XSETBV: XCR0 set to 0x{:x}", new_xcr_value);
        } else {
          spdlog::warn(
              "X86_64CPU[{}]: XSETBV with unhandled XCR register: 0x{:x}",
              m_cpuId, ecx_val);
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        break;
      }
      case InstructionType::Lgdt: {
        // Check CPL
        if (GetCPL() != 0) { // LGDT is a privileged instruction
          spdlog::error("X86_64CPU[{}]: LGDT from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint64_t operand_addr = CalculateMemoryAddress(instr.operands[0]);
        uint16_t limit = 0;
        uint64_t base = 0;
        try {
          mmu.ReadVirtual(operand_addr, &limit, 2, processId);
          mmu.ReadVirtual(operand_addr + 2, &base, 8,
                          processId); // 64-bit base
        } catch (const std::exception &e) {
          spdlog::error("X86_64CPU[{}]: LGDT memory read failed at 0x{:x}: {}",
                        m_cpuId, operand_addr, e.what());
          TriggerInterrupt(EXC_PF, operand_addr, false);
          return;
        }
        SetGDTR(base, limit);
        spdlog::info("X86_64CPU[{}]: LGDT: base=0x{:x}, limit=0x{:x}", m_cpuId,
                     base, limit);
        break;
      }
      case InstructionType::Lidt: {
        // Check CPL
        if (GetCPL() != 0) { // LIDT is a privileged instruction
          spdlog::error("X86_64CPU[{}]: LIDT from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint64_t operand_addr = CalculateMemoryAddress(instr.operands[0]);
        uint16_t limit = 0;
        uint64_t base = 0;
        try {
          mmu.ReadVirtual(operand_addr, &limit, 2, processId);
          mmu.ReadVirtual(operand_addr + 2, &base, 8,
                          processId); // 64-bit base
        } catch (const std::exception &e) {
          spdlog::error("X86_64CPU[{}]: LIDT memory read failed at 0x{:x}: {}",
                        m_cpuId, operand_addr, e.what());
          TriggerInterrupt(EXC_PF, operand_addr, false);
          return;
        }
        SetIDTR(base, limit);
        spdlog::info("X86_64CPU[{}]: LIDT: base=0x{:x}, limit=0x{:x}", m_cpuId,
                     base, limit);
        break;
      }
      case InstructionType::Ltr: {
        // Check CPL
        if (GetCPL() != 0) { // LTR is a privileged instruction
          spdlog::error("X86_64CPU[{}]: LTR from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint16_t selector =
            static_cast<uint16_t>(ReadOperandValue(instr.operands[0]));
        SetTR(selector);
        spdlog::info("X86_64CPU[{}]: LTR: selector=0x{:x}", m_cpuId, selector);
        break;
      }
      case InstructionType::Lmsw: {
        // Check CPL
        if (GetCPL() != 0) { // LMSW is a privileged instruction
          spdlog::error("X86_64CPU[{}]: LMSW from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint16_t msw =
            static_cast<uint16_t>(ReadOperandValue(instr.operands[0]));
        // Only lower 4 bits of MSW are relevant for CR0
        SetCR0((GetCR0() & ~0xFFFFULL) | msw);
        spdlog::info("X86_64CPU[{}]: LMSW: MSW=0x{:x}, CR0=0x{:x}", m_cpuId,
                     msw, GetCR0());
        break;
      }
      case InstructionType::Mov_CR: {
        // Check CPL
        if (GetCPL() != 0) { // MOV CRx is a privileged instruction
          spdlog::error("X86_64CPU[{}]: MOV CRx from non-CPL0 (CPL={})",
                        m_cpuId, GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        // MOV CRx, Reg or MOV Reg, CRx
        if (instr.operands[0].type ==
                DecodedInstruction::Operand::Type::REGISTER &&
            instr.operands[1].type ==
                DecodedInstruction::Operand::Type::CONTROL_REGISTER) {
          // MOV Reg, CRx
          uint64_t cr_value = GetControlRegister(instr.operands[1].reg);
          WriteOperandValue(instr.operands[0], cr_value);
          spdlog::trace("MOV {}: CR{} = 0x{:x}",
                        static_cast<int>(instr.operands[0].reg),
                        static_cast<int>(instr.operands[1].reg) -
                            static_cast<int>(Register::CR0),
                        cr_value);
        } else if (instr.operands[0].type ==
                       DecodedInstruction::Operand::Type::CONTROL_REGISTER &&
                   instr.operands[1].type ==
                       DecodedInstruction::Operand::Type::REGISTER) {
          // MOV CRx, Reg
          uint64_t reg_value = ReadOperandValue(instr.operands[1]);
          SetControlRegister(instr.operands[0].reg, reg_value);
          spdlog::trace("MOV CR{}: {} = 0x{:x}",
                        static_cast<int>(instr.operands[0].reg) -
                            static_cast<int>(Register::CR0),
                        static_cast<int>(instr.operands[1].reg), reg_value);
        } else {
          spdlog::error("X86_64CPU[{}]: Invalid operands for MOV_CR", m_cpuId);
          TriggerInterrupt(EXC_UD, 0, false);
        }
        break;
      }
      case InstructionType::Mov_DR: {
        // Check CPL
        if (GetCPL() != 0) { // MOV DRx is a privileged instruction
          spdlog::error("X86_64CPU[{}]: MOV DRx from non-CPL0 (CPL={})",
                        m_cpuId, GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        // MOV DRx, Reg or MOV Reg, DRx
        if (instr.operands[0].type ==
                DecodedInstruction::Operand::Type::REGISTER &&
            instr.operands[1].type ==
                DecodedInstruction::Operand::Type::DEBUG_REGISTER) {
          // MOV Reg, DRx
          uint64_t dr_value = GetDebugRegister(instr.operands[1].reg);
          WriteOperandValue(instr.operands[0], dr_value);
          spdlog::trace("MOV {}: DR{} = 0x{:x}",
                        static_cast<int>(instr.operands[0].reg),
                        static_cast<int>(instr.operands[1].reg) -
                            static_cast<int>(Register::DR0),
                        dr_value);
        } else if (instr.operands[0].type ==
                       DecodedInstruction::Operand::Type::DEBUG_REGISTER &&
                   instr.operands[1].type ==
                       DecodedInstruction::Operand::Type::REGISTER) {
          // MOV DRx, Reg
          uint64_t reg_value = ReadOperandValue(instr.operands[1]);
          SetDebugRegister(instr.operands[0].reg, reg_value);
          spdlog::trace("MOV DR{}: {} = 0x{:x}",
                        static_cast<int>(instr.operands[0].reg) -
                            static_cast<int>(Register::DR0),
                        static_cast<int>(instr.operands[1].reg), reg_value);
        } else {
          spdlog::error("X86_64CPU[{}]: Invalid operands for MOV_DR", m_cpuId);
          TriggerInterrupt(EXC_UD, 0, false);
        }
        break;
      }
      case InstructionType::Rdmsr: {
        // Check CPL
        if (GetCPL() != 0) { // RDMSR is a privileged instruction
          spdlog::error("X86_64CPU[{}]: RDMSR from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint32_t msr_index = static_cast<uint32_t>(GetRegister(Register::RCX));
        uint64_t msr_value = GetMSR(static_cast<MSR>(msr_index));
        SetRegister(Register::RAX, msr_value & 0xFFFFFFFF);
        SetRegister(Register::RDX, msr_value >> 32);
        spdlog::trace("RDMSR: RCX=0x{:x}, RAX=0x{:x}, RDX=0x{:x}", msr_index,
                      GetRegister(Register::RAX), GetRegister(Register::RDX));
        break;
      }
      case InstructionType::Wrmsr: {
        // Check CPL
        if (GetCPL() != 0) { // WRMSR is a privileged instruction
          spdlog::error("X86_64CPU[{}]: WRMSR from non-CPL0 (CPL={})", m_cpuId,
                        GetCPL());
          TriggerInterrupt(EXC_GP, 0, false);
          return;
        }
        uint32_t msr_index = static_cast<uint32_t>(GetRegister(Register::RCX));
        uint64_t new_msr_value = (GetRegister(Register::RDX) << 32) |
                                 (GetRegister(Register::RAX) & 0xFFFFFFFF);
        SetMSR(static_cast<MSR>(msr_index), new_msr_value);
        spdlog::trace("WRMSR: RCX=0x{:x}, RAX=0x{:x}, RDX=0x{:x}", msr_index,
                      GetRegister(Register::RAX), GetRegister(Register::RDX));
        break;
      }
      // Basic SIMD operations (integer and floating point)
      case InstructionType::Movaps:   // Move Aligned Packed Single-precision
                                      // Floating-point
      case InstructionType::Movdqa: { // Move Double Quadword Aligned
        __m256i value;
        if (instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {
          value = ReadXmmOperandValue(instr.operands[1]);
        } else if (instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::MEMORY) {
          // Read 16 or 32 bytes from memory
          uint64_t addr = CalculateMemoryAddress(instr.operands[1]);
          alignas(32) uint8_t buffer[32];
          size_t bytesToRead = instr.operands[1].size / 8;
          if (bytesToRead != 16 && bytesToRead != 32) {
            spdlog::error("X86_64CPU[{}]: MOVAPS/MOVDQA invalid size: {}",
                          m_cpuId, bytesToRead);
            TriggerInterrupt(EXC_UD, 0, false);
            return;
          }
          try {
            mmu.ReadVirtual(addr, buffer, bytesToRead, GetProcessId());
          } catch (const std::exception &e) {
            spdlog::error("X86_64CPU[{}]: MOVAPS/MOVDQA memory read failed "
                          "at 0x{:x}: {}",
                          m_cpuId, addr, e.what());
            TriggerInterrupt(EXC_PF, addr, false);
            return;
          }
          if (bytesToRead == 16)
            value = _mm256_castsi128_si256(
                _mm_load_si128(reinterpret_cast<__m128i *>(buffer)));
          else if (bytesToRead == 32)
            value = _mm256_load_si256(reinterpret_cast<__m256i *>(buffer));
        } else {
          spdlog::error(
              "X86_64CPU[{}]: MOVAPS/MOVDQA invalid source operand type",
              m_cpuId);
          TriggerInterrupt(EXC_UD, 0, false);
          return;
        }
        WriteXmmOperandValue(instr.operands[0], value);
        spdlog::trace("MOVAPS/MOVDQA: moved SIMD value");
        break;
      }
      case InstructionType::Addps: { // Add Packed Single-precision
                                     // Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256 result_ps = _mm256_add_ps(_mm256_castsi256_ps(op1_xmm),
                                         _mm256_castsi256_ps(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result_ps));
        spdlog::trace("ADDPS: performed packed single-precision add");
        break;
      }
      case InstructionType::Mulps: { // Multiply Packed Single-precision
                                     // Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256 result_ps = _mm256_mul_ps(_mm256_castsi256_ps(op1_xmm),
                                         _mm256_castsi256_ps(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result_ps));
        spdlog::trace("MULPS: performed packed single-precision multiply");
        break;
      }
      case InstructionType::Andps: { // Bitwise Logical AND Packed
                                     // Single-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256 result_ps = _mm256_and_ps(_mm256_castsi256_ps(op1_xmm),
                                         _mm256_castsi256_ps(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result_ps));
        spdlog::trace("ANDPS: performed packed single-precision bitwise AND");
        break;
      }
      case InstructionType::Orps: { // Bitwise Logical OR Packed
                                    // Single-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256 result_ps = _mm256_or_ps(_mm256_castsi256_ps(op1_xmm),
                                        _mm256_castsi256_ps(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result_ps));
        spdlog::trace("ORPS: performed packed single-precision bitwise OR");
        break;
      }
      case InstructionType::Xorps: { // Bitwise Logical XOR Packed
                                     // Single-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256 result_ps = _mm256_xor_ps(_mm256_castsi256_ps(op1_xmm),
                                         _mm256_castsi256_ps(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result_ps));
        spdlog::trace("XORPS: performed packed single-precision bitwise XOR");
        break;
      }
      case InstructionType::Addpd: { // Add Packed Double-precision
                                     // Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256d result_pd = _mm256_add_pd(_mm256_castsi256_pd(op1_xmm),
                                          _mm256_castsi256_pd(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result_pd));
        spdlog::trace("ADDPD: performed packed double-precision add");
        break;
      }
      case InstructionType::Mulpd: { // Multiply Packed Double-precision
                                     // Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256d result_pd = _mm256_mul_pd(_mm256_castsi256_pd(op1_xmm),
                                          _mm256_castsi256_pd(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result_pd));
        spdlog::trace("MULPD: performed packed double-precision multiply");
        break;
      }
      case InstructionType::Andpd: { // Bitwise Logical AND Packed
                                     // Double-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256d result_pd = _mm256_and_pd(_mm256_castsi256_pd(op1_xmm),
                                          _mm256_castsi256_pd(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result_pd));
        spdlog::trace("ANDPD: performed packed double-precision bitwise AND");
        break;
      }
      case InstructionType::Orpd: { // Bitwise Logical OR Packed
                                    // Double-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256d result_pd = _mm256_or_pd(_mm256_castsi256_pd(op1_xmm),
                                         _mm256_castsi256_pd(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result_pd));
        spdlog::trace("ORPD: performed packed double-precision bitwise OR");
        break;
      }
      case InstructionType::Xorpd: { // Bitwise Logical XOR Packed
                                     // Double-precision Floating-point
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256d result_pd = _mm256_xor_pd(_mm256_castsi256_pd(op1_xmm),
                                          _mm256_castsi256_pd(op2_xmm));
        WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result_pd));
        spdlog::trace("XORPD: performed packed double-precision bitwise XOR");
        break;
      }
      case InstructionType::Paddb:   // Packed Add Byte
      case InstructionType::Paddw:   // Packed Add Word
      case InstructionType::Paddd:   // Packed Add Dword
      case InstructionType::Paddq: { // Packed Add Qword
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256i result_xmm;
        switch (instr.instType) {
        case InstructionType::Paddb:
          result_xmm = _mm256_add_epi8(op1_xmm, op2_xmm);
          break;
        case InstructionType::Paddw:
          result_xmm = _mm256_add_epi16(op1_xmm, op2_xmm);
          break;
        case InstructionType::Paddd:
          result_xmm = _mm256_add_epi32(op1_xmm, op2_xmm);
          break;
        case InstructionType::Paddq:
          result_xmm = _mm256_add_epi64(op1_xmm, op2_xmm);
          break;
        default:
          break; // Should not happen
        }
        WriteXmmOperandValue(instr.operands[0], result_xmm);
        spdlog::trace("PADD: performed packed integer add");
        break;
      }
      case InstructionType::Psubb:
      case InstructionType::Psubw:
      case InstructionType::Psubd:
      case InstructionType::Psubq: {
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256i result_xmm;
        switch (instr.instType) {
        case InstructionType::Psubb:
          result_xmm = _mm256_sub_epi8(op1_xmm, op2_xmm);
          break;
        case InstructionType::Psubw:
          result_xmm = _mm256_sub_epi16(op1_xmm, op2_xmm);
          break;
        case InstructionType::Psubd:
          result_xmm = _mm256_sub_epi32(op1_xmm, op2_xmm);
          break;
        case InstructionType::Psubq:
          result_xmm = _mm256_sub_epi64(op1_xmm, op2_xmm);
          break;
        default:
          break; // Should not happen
        }
        WriteXmmOperandValue(instr.operands[0], result_xmm);
        spdlog::trace("PSUB: performed packed integer subtract");
        break;
      }
      case InstructionType::Pand: { // Packed Bitwise Logical AND
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256i result_xmm = _mm256_and_si256(op1_xmm, op2_xmm);
        WriteXmmOperandValue(instr.operands[0], result_xmm);
        spdlog::trace("PAND: performed packed bitwise AND");
        break;
      }
      case InstructionType::Por: { // Packed Bitwise Logical OR
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256i result_xmm = _mm256_or_si256(op1_xmm, op2_xmm);
        WriteXmmOperandValue(instr.operands[0], result_xmm);
        spdlog::trace("POR: performed packed bitwise OR");
        break;
      }
      case InstructionType::Pxor: { // Packed Bitwise Logical XOR
        __m256i op1_xmm = ReadXmmOperandValue(instr.operands[0]);
        __m256i op2_xmm = ReadXmmOperandValue(instr.operands[1]);
        __m256i result_xmm = _mm256_xor_si256(op1_xmm, op2_xmm);
        WriteXmmOperandValue(instr.operands[0], result_xmm);
        spdlog::trace("PXOR: performed packed bitwise XOR");
        break;
      }
      // Floating-point instructions
      case InstructionType::Fadd:
      case InstructionType::Fmul:
      case InstructionType::Fcom:
      case InstructionType::Fsin:
      case InstructionType::Fcos:
      case InstructionType::Fsincos:
      case InstructionType::Fprem:
      case InstructionType::Fscale:
      case InstructionType::F2xm1:
      case InstructionType::Fyl2x:
      case InstructionType::Fptan:
      case InstructionType::Fpatan:
      case InstructionType::Fxtract:
        ExecuteFloatingPointInstruction(instr, nextRip);
        break;

      // AVX instructions
      case InstructionType::Vaddps:
      case InstructionType::Vaddpd:
      case InstructionType::Vfmadd132ps:
      case InstructionType::Vgatherdps:
      case InstructionType::Vperm2f128:
      case InstructionType::Vbroadcastss:
      case InstructionType::Vroundps:
      case InstructionType::Vtestps:
      case InstructionType::Vzeroupper:
        ExecuteAVXInstruction(instr, nextRip);
        break;

      // AVX-512 instructions
      case InstructionType::Vpaddd_zmm:
      case InstructionType::Vpmullq_zmm:
      case InstructionType::Vgatherdpd:
      case InstructionType::Vcompressps:
      case InstructionType::Vexpandps:
      case InstructionType::Vfpclassps:
      case InstructionType::Vgetexpps:
      case InstructionType::Vreduceps:
      case InstructionType::Vscatterqps:
      case InstructionType::Vpmovm2d:
      case InstructionType::Vpmovd2m:
        ExecuteAVX512Instruction(instr, nextRip);
        break;

      // VMX instructions
      case InstructionType::Vmxon:
        VMXON(ReadOperandValue(instr.operands[0]));
        break;
      case InstructionType::Vmptrld:
        VMPTRLD(ReadOperandValue(instr.operands[0]));
        break;
      case InstructionType::Vmlaunch:
        VMLAUNCH();
        break;
      case InstructionType::Vmresume:
        VMRESUME();
        break;
      case InstructionType::Vmxoff:
        VMXOFF();
        break;

      // SMM instruction
      case InstructionType::Rsm:
        RSM();
        break;

      // Cache control
      case InstructionType::Clflush:
        CLFLUSH(CalculateMemoryAddress(instr.operands[0]));
        break;

      case InstructionType::Prefetch:
        PREFETCH(instr.operands[0].type,
                 CalculateMemoryAddress(instr.operands[0]));
        break;
      default: {
        spdlog::error("X86_64CPU[{}]: Unsupported instruction: {}", m_cpuId,
                      static_cast<int>(instr.instType));
        TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
        return;
      }
      }
    }

    // Update RIP if it wasn't changed by a jump/call/ret/interrupt
    if (GetRegister(Register::RIP) == rip) {
      SetRegister(Register::RIP, nextRip);
    }
  } catch (const CPUException &e) {
    spdlog::error("X86_64CPU[{}]: CPU Exception during execution at 0x{:x}: {}",
                  m_cpuId, rip, e.what());
    // Exception already triggered by helper functions
    running = false; // Stop CPU on critical error
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Unhandled C++ exception during execution "
                  "at 0x{:x}: {}",
                  m_cpuId, rip, e.what());
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    running = false;
  }
}

uint64_t X86_64CPU::TranslateAddress(uint64_t virtualAddr) {
  // Check CPL for privilege checks on memory access
  uint8_t cpl = GetCPL();

  size_t hitFlag, useCache;
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    hitFlag = 0;
    useCache = 0;
    uint64_t physAddr = tlb.Lookup(virtualAddr, hitFlag, useCache);
    if (physAddr != 0) {
      spdlog::trace("TLB hit for virtual 0x{:x}, physical 0x{:x}", virtualAddr,
                    physAddr);
      return physAddr;
    }
  }

  // Paging is enabled if CR0.PG (bit 31) is set
  if (!(GetCR0() & (1ULL << 31))) {
    spdlog::trace("Paging disabled, virtual address 0x{:x} is physical",
                  virtualAddr);
    return virtualAddr; // Identity mapping if paging is off
  }

  uint64_t cr3_val = GetCR3(); // CR3 holds the base address of the PML4 table
  uint64_t pml4_base = cr3_val & ~0xFFF; // CR3 bits 11:0 are reserved/ignored

  // Extract page table indices
  uint64_t pml4_idx = (virtualAddr >> 39) & 0x1FF;
  uint64_t pdpt_idx = (virtualAddr >> 30) & 0x1FF;
  uint64_t pd_idx = (virtualAddr >> 21) & 0x1FF;
  uint64_t pt_idx = (virtualAddr >> 12) & 0x1FF;
  uint64_t offset = virtualAddr & 0xFFF;

  uint64_t pml4e_addr = pml4_base + pml4_idx * 8;
  uint64_t pml4e = 0;
  try {
    mmu.ReadPhysical(pml4e_addr, &pml4e, 8);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Page table walk failed: PML4E read error "
                  "at 0x{:x}: {}",
                  m_cpuId, pml4e_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pml4e & 1)) { // Present bit (bit 0)
    spdlog::error(
        "Page table fault: PML4E not present at 0x{:x} (virtual 0x{:x})",
        pml4e_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PML4E not present");
  }
  // Check R/W (bit 1) and U/S (bit 2) permissions
  if (!CheckPagePermissions(pml4e, cpl, true,
                            true)) { // Assume read/write for now
    spdlog::error("Page table fault: PML4E permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pml4e_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PML4E permission violation");
  }

  uint64_t pdpt_base = pml4e & ~0xFFF;
  uint64_t pdpte_addr = pdpt_base + pdpt_idx * 8;
  uint64_t pdpte = 0;
  try {
    mmu.ReadPhysical(pdpte_addr, &pdpte, 8);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Page table walk failed: PDPTE read error "
                  "at 0x{:x}: {}",
                  m_cpuId, pdpte_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pdpte & 1)) { // Present bit
    spdlog::error(
        "Page table fault: PDPTE not present at 0x{:x} (virtual 0x{:x})",
        pdpte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PDPTE not present");
  }
  if (!CheckPagePermissions(pdpte, cpl, true, true)) {
    spdlog::error("Page table fault: PDPTE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pdpte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PDPTE permission violation");
  }

  // Check for 1GB page (bit 7 of PDPTE)
  if (pdpte & (1ULL << 7)) { // PS bit (Page Size)
    uint64_t physAddr = (pdpte & ~0x3FFFFFULL) |
                        (virtualAddr & 0x3FFFFFFF); // Mask for 1GB page
    spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x} (1GB page)",
                  virtualAddr, physAddr);
    {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      tlb.Insert(virtualAddr, physAddr);
    }
    return physAddr;
  }

  uint64_t pd_base = pdpte & ~0xFFF;
  uint64_t pde_addr = pd_base + pd_idx * 8;
  uint64_t pde = 0;
  try {
    mmu.ReadPhysical(pde_addr, &pde, 8);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Page table walk failed: PDE read error at 0x{:x}: {}",
        m_cpuId, pde_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pde & 1)) { // Present bit
    spdlog::error(
        "Page table fault: PDE not present at 0x{:x} (virtual 0x{:x})",
        pde_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PDE not present");
  }
  if (!CheckPagePermissions(pde, cpl, true, true)) {
    spdlog::error("Page table fault: PDE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pde_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PDE permission violation");
  }

  // Check for 2MB page (bit 7 of PDE)
  if (pde & (1ULL << 7)) { // PS bit
    uint64_t physAddr =
        (pde & ~0x1FFFFFULL) | (virtualAddr & 0x1FFFFF); // Mask for 2MB page
    spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x} (2MB page)",
                  virtualAddr, physAddr);
    {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      tlb.Insert(virtualAddr, physAddr);
    }
    return physAddr;
  }

  uint64_t pt_base = pde & ~0xFFF;
  uint64_t pte_addr = pt_base + pt_idx * 8;
  uint64_t pte = 0;
  try {
    mmu.ReadPhysical(pte_addr, &pte, 8);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Page table walk failed: PTE read error at 0x{:x}: {}",
        m_cpuId, pte_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pte & 1)) { // Present bit
    spdlog::error(
        "Page table fault: PTE not present at 0x{:x} (virtual 0x{:x})",
        pte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PTE not present");
  }
  if (!CheckPagePermissions(pte, cpl, true, true)) {
    spdlog::error("Page table fault: PTE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PTE permission violation");
  }

  uint64_t physAddr = (pte & ~0xFFF) | offset;
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    tlb.Insert(virtualAddr, physAddr);
  }
  spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x}", virtualAddr,
                physAddr);

  return physAddr;
}

bool X86_64CPU::CheckPagePermissions(uint64_t pte_entry, uint8_t cpl,
                                     bool is_write, bool is_execute) const {
  bool present = (pte_entry & (1ULL << 0)) != 0;
  bool rw = (pte_entry & (1ULL << 1)) != 0;  // Read/Write
  bool us = (pte_entry & (1ULL << 2)) != 0;  // User/Supervisor
  bool nx = (pte_entry & (1ULL << 63)) != 0; // No-Execute (if EFER.NXE is set)

  if (!present)
    return false; // Not present, always a fault

  // User/Supervisor check
  if (cpl == 3 && !us) { // User mode trying to access supervisor page
    spdlog::debug("Permission denied: User mode access to supervisor page.");
    return false;
  }

  // Read/Write check
  if (is_write && !rw) { // Trying to write to a read-only page
    spdlog::debug("Permission denied: Write access to read-only page.");
    return false;
  }

  // Execute check (if NXE is enabled in EFER)
  if (GetMSR(MSR::EFER) & (1ULL << 11)) { // EFER.NXE (No-Execute Enable)
    if (is_execute && nx) { // Trying to execute a non-executable page
      spdlog::debug(
          "Permission denied: Execute access to non-executable page.");
      return false;
    }
  }

  return true;
}

void X86_64CPU::Push(uint64_t value, uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP);

  if (rsp < (uint64_t)sizeInBytes ||
      rsp - sizeInBytes < 0x1000) { // Basic stack overflow check
    lock.unlock();
    spdlog::critical(
        "X86_64CPU[{}]: Stack overflow during PUSH. RSP=0x{:x}, size={}",
        m_cpuId, rsp, sizeInBytes);
    TriggerInterrupt(EXC_SS, 0, false); // Stack Segment Fault
    throw CPUException("Stack overflow");
  }

  rsp -= sizeInBytes;
  _setRegister(Register::RSP, rsp);
  uint64_t processIdCopy = processId;
  lock.unlock(); // Release lock before MMU access

  try {
    mmu.WriteVirtual(rsp, reinterpret_cast<const void *>(&value), sizeInBytes,
                     processIdCopy);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Memory write failed during PUSH at 0x{:x}: {}", m_cpuId,
        rsp, e.what());
    TriggerInterrupt(EXC_PF, rsp, false); // Page Fault
    throw CPUException("Memory write error during PUSH");
  }
  spdlog::trace("Pushed value 0x{:x} to stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
}

uint64_t X86_64CPU::Pop(uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP);

  if (rsp + sizeInBytes > DEFAULT_STACK_POINTER ||
      rsp >= 0x800000000000ULL) { // Basic stack underflow check
    lock.unlock();
    spdlog::critical(
        "X86_64CPU[{}]: Stack underflow during POP. RSP=0x{:x}, size={}",
        m_cpuId, rsp, sizeInBytes);
    TriggerInterrupt(EXC_SS, 0, false); // Stack Segment Fault
    throw CPUException("Stack underflow");
  }

  uint64_t processIdCopy = processId;
  lock.unlock(); // Release lock before MMU access

  uint64_t value = 0;
  try {
    mmu.ReadVirtual(rsp, reinterpret_cast<void *>(&value), sizeInBytes,
                    processIdCopy);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Memory read failed during POP at 0x{:x}: {}",
                  m_cpuId, rsp, e.what());
    TriggerInterrupt(EXC_PF, rsp, false); // Page Fault
    throw CPUException("Memory read error during POP");
  }

  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    _setRegister(Register::RSP, rsp + sizeInBytes);
  }
  spdlog::trace("Popped value 0x{:x} from stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
  return value;
}

void X86_64CPU::UpdateFlags(uint64_t result, uint64_t op1, uint64_t op2,
                            uint8_t sizeInBits, bool isSubtract) {
  UpdateArithmeticFlags(op1, op2, result, sizeInBits, isSubtract);
}

bool X86_64CPU::CheckCondition(uint8_t conditionCode) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  switch (conditionCode) {
  // Overflow conditions
  case 0x0: // JO - Jump if overflow
    return GetFlag(FLAG_OF);
  case 0x1: // JNO - Jump if not overflow
    return !GetFlag(FLAG_OF);

  // Carry conditions
  case 0x2: // JB/JNAE/JC - Jump if below/carry
    return GetFlag(FLAG_CF);
  case 0x3: // JNB/JAE/JNC - Jump if not below/not carry
    return !GetFlag(FLAG_CF);

  // Zero conditions
  case 0x4: // JZ/JE - Jump if zero/equal
    return GetFlag(FLAG_ZF);
  case 0x5: // JNZ/JNE - Jump if not zero/not equal
    return !GetFlag(FLAG_ZF);

  // Below or equal / Above
  case 0x6: // JBE/JNA - Jump if below or equal
    return GetFlag(FLAG_CF) || GetFlag(FLAG_ZF);
  case 0x7: // JNBE/JA - Jump if not below or equal/above
    return !GetFlag(FLAG_CF) && !GetFlag(FLAG_ZF);

  // Sign conditions
  case 0x8: // JS - Jump if sign
    return GetFlag(FLAG_SF);
  case 0x9: // JNS - Jump if not sign
    return !GetFlag(FLAG_SF);

  // Parity conditions
  case 0xA: // JP/JPE - Jump if parity/parity even
    return GetFlag(FLAG_PF);
  case 0xB: // JNP/JPO - Jump if not parity/parity odd
    return !GetFlag(FLAG_PF);

  // Less than (signed)
  case 0xC: // JL/JNGE - Jump if less/not greater or equal
    return GetFlag(FLAG_SF) != GetFlag(FLAG_OF);
  case 0xD: // JNL/JGE - Jump if not less/greater or equal
    return GetFlag(FLAG_SF) == GetFlag(FLAG_OF);

  // Less than or equal / Greater than (signed)
  case 0xE: // JLE/JNG - Jump if less or equal/not greater
    return GetFlag(FLAG_ZF) || (GetFlag(FLAG_SF) != GetFlag(FLAG_OF));
  case 0xF: // JNLE/JG - Jump if not less or equal/greater
    return !GetFlag(FLAG_ZF) && (GetFlag(FLAG_SF) == GetFlag(FLAG_OF));

  default:
    spdlog::warn("X86_64CPU[{}]: Invalid condition code 0x{:x}", m_cpuId,
                 conditionCode);
    return false;
  }
}

void X86_64CPU::SetGDTR(uint64_t base, uint16_t limit) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  gdtrBase = base;
  gdtrLimit = limit;
  spdlog::info("X86_64CPU[{}]: Set GDTR: base=0x{:x}, limit=0x{:x}", m_cpuId,
               base, limit);
}

void X86_64CPU::SetIDTR(uint64_t base, uint16_t limit) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  idtrBase = base;
  idtrLimit = limit;
  spdlog::info("X86_64CPU[{}]: Set IDTR: base=0x{:x}, limit=0x{:x}", m_cpuId,
               base, limit);
}

void X86_64CPU::SetLDTR(uint16_t selector) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  ldtr = selector;
  spdlog::info("X86_64CPU[{}]: Set LDTR: selector=0x{:x}", m_cpuId, selector);
}

void X86_64CPU::SetTR(uint16_t selector) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // Save old TR value
  uint16_t oldTR = tr;

  // Update TR
  tr = selector;
  spdlog::info("X86_64CPU[{}]: Set TR: selector=0x{:x}", m_cpuId, selector);

  // Load TSS descriptor
  LoadTSSDescriptor();

  // Load I/O permission bitmap
  if (tssBase != 0) {
    LoadIOPermissionBitmap();

    // Log I/O permission bitmap status
    if (!ioPermissionBitmap.empty()) {
      spdlog::info("X86_64CPU[{}]: I/O permission bitmap loaded: {} bytes",
                   m_cpuId, ioPermissionBitmap.size());
    } else {
      spdlog::info("X86_64CPU[{}]: No I/O permission bitmap available", m_cpuId);
    }
  } else {
    spdlog::warn("X86_64CPU[{}]: Failed to load TSS descriptor for TR=0x{:x}",
                 m_cpuId, selector);

    // Restore old TR if TSS loading failed
    tr = oldTR;
  }
}

uint64_t X86_64CPU::GetGDTRBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return gdtrBase;
}

uint16_t X86_64CPU::GetGDTRLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return gdtrLimit;
}

uint64_t X86_64CPU::GetIDTRBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return idtrBase;
}

uint16_t X86_64CPU::GetIDTRLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return idtrLimit;
}

uint16_t X86_64CPU::GetLDTR() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return ldtr;
}

uint16_t X86_64CPU::GetTR() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tr;
}

void X86_64CPU::SaveState(std::ostream &out) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  out.write(reinterpret_cast<const char *>(registers.data()),
            registers.size() * sizeof(uint64_t));
  out.write(reinterpret_cast<const char *>(xmmRegisters.data()),
            xmmRegisters.size() * sizeof(__m256i));
  out.write(reinterpret_cast<const char *>(&rflags), sizeof(rflags));
  out.write(reinterpret_cast<const char *>(segmentRegisters.data()),
            segmentRegisters.size() * sizeof(uint16_t));
  out.write(reinterpret_cast<const char *>(controlRegisters.data()),
            controlRegisters.size() * sizeof(uint64_t));
  out.write(reinterpret_cast<const char *>(debugRegisters.data()),
            debugRegisters.size() * sizeof(uint64_t));
  // Save MSRs (more complex due to unordered_map)
  size_t msr_count = msrRegisters.size();
  out.write(reinterpret_cast<const char *>(&msr_count), sizeof(msr_count));
  for (const auto &pair : msrRegisters) {
    out.write(reinterpret_cast<const char *>(&pair.first), sizeof(pair.first));
    out.write(reinterpret_cast<const char *>(&pair.second),
              sizeof(pair.second));
  }
  out.write(reinterpret_cast<const char *>(&gdtrBase), sizeof(gdtrBase));
  out.write(reinterpret_cast<const char *>(&gdtrLimit), sizeof(gdtrLimit));
  out.write(reinterpret_cast<const char *>(&idtrBase), sizeof(idtrBase));
  out.write(reinterpret_cast<const char *>(&idtrLimit), sizeof(idtrLimit));
  out.write(reinterpret_cast<const char *>(&ldtr), sizeof(ldtr));
  out.write(reinterpret_cast<const char *>(&tr), sizeof(tr));
  out.write(reinterpret_cast<const char *>(&tssBase), sizeof(tssBase));
  out.write(reinterpret_cast<const char *>(&tssLimit), sizeof(tssLimit));
  out.write(reinterpret_cast<const char *>(&processId), sizeof(processId));
  out.write(reinterpret_cast<const char *>(&halted), sizeof(halted));
  // FPU state (assuming fpuState is a POD struct)
  out.write(reinterpret_cast<const char *>(&fpuState), sizeof(fpuState));

  spdlog::info("X86_64CPU[{}]: Saved state", m_cpuId);
}

void X86_64CPU::LoadState(std::istream &in) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  in.read(reinterpret_cast<char *>(registers.data()),
          registers.size() * sizeof(uint64_t));
  in.read(reinterpret_cast<char *>(xmmRegisters.data()),
          xmmRegisters.size() * sizeof(__m256i));
  in.read(reinterpret_cast<char *>(&rflags), sizeof(rflags));
  in.read(reinterpret_cast<char *>(segmentRegisters.data()),
          segmentRegisters.size() * sizeof(uint16_t));
  in.read(reinterpret_cast<char *>(controlRegisters.data()),
          controlRegisters.size() * sizeof(uint64_t));
  in.read(reinterpret_cast<char *>(debugRegisters.data()),
          debugRegisters.size() * sizeof(uint64_t));
  // Load MSRs
  size_t msr_count;
  in.read(reinterpret_cast<char *>(&msr_count), sizeof(msr_count));
  msrRegisters.clear();
  for (size_t i = 0; i < msr_count; ++i) {
    uint32_t key;
    uint64_t value;
    in.read(reinterpret_cast<char *>(&key), sizeof(key));
    in.read(reinterpret_cast<char *>(&value), sizeof(value));
    msrRegisters[key] = value;
  }
  in.read(reinterpret_cast<char *>(&gdtrBase), sizeof(gdtrBase));
  in.read(reinterpret_cast<char *>(&gdtrLimit), sizeof(gdtrLimit));
  in.read(reinterpret_cast<char *>(&idtrBase), sizeof(idtrBase));
  in.read(reinterpret_cast<char *>(&idtrLimit), sizeof(idtrLimit));
  in.read(reinterpret_cast<char *>(&ldtr), sizeof(ldtr));
  in.read(reinterpret_cast<char *>(&tr), sizeof(tr));
  in.read(reinterpret_cast<char *>(&tssBase), sizeof(tssBase));
  in.read(reinterpret_cast<char *>(&tssLimit), sizeof(tssLimit));
  in.read(reinterpret_cast<char *>(&processId), sizeof(processId));
  in.read(reinterpret_cast<char *>(&halted), sizeof(halted));
  // FPU state
  in.read(reinterpret_cast<char *>(&fpuState), sizeof(fpuState));

  spdlog::info("X86_64CPU[{}]: Loaded state", m_cpuId);
}

uint64_t X86_64CPU::GetRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return _getRegister(r);
}

bool X86_64CPU::SetRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  _setRegister(r, v);
  return true; // signal success
}

ps4::PS4MMU &X86_64CPU::GetMemory() { return m_emulator.GetMemory(); }

uint32_t X86_64CPU::GetCPUId() const { return m_cpuId; }

void X86_64CPU::InvalidateTLB(uint64_t virtAddr) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  tlb.Invalidate(virtAddr);
}

uint64_t X86_64CPU::_getRegister(Register r) const {
  return registers[static_cast<size_t>(r)];
}

void X86_64CPU::_setRegister(Register r, uint64_t v) {
  registers[static_cast<size_t>(r)] = v;
}

void X86_64CPU::SetRflags(uint64_t f) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  rflags = f;
  spdlog::trace("X86_64CPU[{}] RFLAGS set: 0x{:x}", m_cpuId, f);
}

uint64_t X86_64CPU::GetRflags() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return rflags;
}

bool X86_64CPU::GetFlag(uint64_t m) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return (rflags & m) != 0;
}

void X86_64CPU::SetFlag(uint64_t flag, bool value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (value) {
    rflags |= flag;
  } else {
    rflags &= ~flag;
  }
}

void X86_64CPU::UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error(
        "X86_64CPU[{}]: Invalid size for logical flag calculation: {} bits",
        m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;

  // Clear logical flags AND carry/overflow flags for TEST/AND/OR/XOR
  // CF and OF are always cleared for logical operations
  rflags &= ~(FLAG_ZF | FLAG_SF | FLAG_PF | FLAG_CF | FLAG_OF);

  // Set flags based on result
  if (result == 0)
    rflags |= FLAG_ZF;
  if (result & signBit)
    rflags |= FLAG_SF;
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // AF is undefined for logical operations, so we leave it unchanged

  spdlog::trace("X86_64CPU[{}]: Logical flags updated: RFLAGS=0x{:x}", m_cpuId,
                rflags);
}

uint64_t X86_64CPU::GetControlRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::CR0);
  if (index >= controlRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to read invalid control register: {}",
        m_cpuId, static_cast<int>(r));
    throw CPUException("Invalid control register access");
  }
  return controlRegisters[index];
}

void X86_64CPU::SetControlRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::CR0);
  if (index >= controlRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to write invalid control register: {}",
        m_cpuId, static_cast<int>(r));
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return;
  }
  controlRegisters[index] = v;
  // Special handling for CR3 (TLB flush)
  if (r == Register::CR3) {
    tlb.Clear(); // CR3 writes invalidate TLB
    spdlog::info("X86_64CPU[{}]: CR3 set to 0x{:x}, TLB flushed.", m_cpuId, v);
  }
  spdlog::trace("X86_64CPU[{}]: CR{} set to 0x{:x}", m_cpuId, index, v);
}

uint64_t X86_64CPU::GetDebugRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::DR0);
  if (index >= debugRegisters.size()) {
    spdlog::error("X86_64CPU[{}]: Attempted to read invalid debug register: {}",
                  m_cpuId, static_cast<int>(r));
    throw CPUException("Invalid debug register access");
  }
  return debugRegisters[index];
}

void X86_64CPU::SetDebugRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::DR0);
  if (index >= debugRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to write invalid debug register: {}", m_cpuId,
        static_cast<int>(r));
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return;
  }
  debugRegisters[index] = v;
  spdlog::trace("X86_64CPU[{}]: DR{} set to 0x{:x}", m_cpuId, index, v);
}

uint64_t X86_64CPU::GetMSR(MSR msr_index) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  auto it = msrRegisters.find(static_cast<uint32_t>(msr_index));
  if (it != msrRegisters.end()) {
    return it->second;
  }
  spdlog::warn("X86_64CPU[{}]: Attempted to read uninitialized MSR: 0x{:x}",
               m_cpuId, static_cast<uint32_t>(msr_index));
  return 0; // Return 0 for uninitialized MSRs
}

void X86_64CPU::SetMSR(MSR msr_index, uint64_t value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  msrRegisters[static_cast<uint32_t>(msr_index)] = value;
  spdlog::trace("X86_64CPU[{}]: MSR 0x{:x} set to 0x{:x}", m_cpuId,
                static_cast<uint32_t>(msr_index), value);
}

uint64_t X86_64CPU::GetCR0() const { return GetControlRegister(Register::CR0); }
uint64_t X86_64CPU::GetCR2() const { return GetControlRegister(Register::CR2); }
uint64_t X86_64CPU::GetCR3() const { return GetControlRegister(Register::CR3); }
uint64_t X86_64CPU::GetCR4() const { return GetControlRegister(Register::CR4); }

void X86_64CPU::SetCR0(uint64_t v) { SetControlRegister(Register::CR0, v); }
void X86_64CPU::SetCR2(uint64_t v) { SetControlRegister(Register::CR2, v); }
void X86_64CPU::SetCR3(uint64_t v) { SetControlRegister(Register::CR3, v); }
void X86_64CPU::SetCR4(uint64_t v) { SetControlRegister(Register::CR4, v); }

uint16_t X86_64CPU::GetCS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::CS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetCS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: CS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::SS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetSS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: SS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetDS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::DS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetDS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: DS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetES() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::ES) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetES(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: ES set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetFS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::FS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetFS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::FS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: FS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetGS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::GS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetGS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::GS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: GS set to 0x{:x}", m_cpuId, v);
}

uint64_t X86_64CPU::GetTSSBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssBase;
}

uint16_t X86_64CPU::GetTSSLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssLimit;
}

uint16_t X86_64CPU::GetKernelSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return kernelSS;
}

uint8_t X86_64CPU::GetCPL() const {
  // Current Privilege Level is bits 0 and 1 of CS selector
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return GetCS() & 0x3;
}

uint8_t X86_64CPU::GetIOPL() const {
  // I/O Privilege Level is bits 12 and 13 of RFLAGS
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return (rflags >> 12) & 0x3;
}

X86_64JITCompiler &X86_64CPU::GetJITCompiler() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *jit;
}

Pipeline &X86_64CPU::GetPipeline() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *pipeline;
}

uint64_t X86_64CPU::GetProcessId() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return processId;
}

void X86_64CPU::SetProcessId(uint64_t pid) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  processId = pid;
  spdlog::info("X86_64CPU[{}]: Process ID set to 0x{:x}", m_cpuId, pid);
}

void X86_64CPU::Execute() { FetchDecodeExecute(); }

void X86_64CPU::SetContext(const CPUContext &ctx) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  registers = ctx.registers;
  rflags = ctx.rflags;
  xmmRegisters = ctx.xmmRegisters;
  _setRegister(Register::RIP, ctx.rip);
  _setRegister(Register::RSP, ctx.rsp);
  // Restore segment registers
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = ctx.cs;
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = ctx.ds;
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = ctx.es;
  segmentRegisters[static_cast<size_t>(Register::FS) -
                   static_cast<size_t>(Register::ES)] = ctx.fs;
  segmentRegisters[static_cast<size_t>(Register::GS) -
                   static_cast<size_t>(Register::ES)] = ctx.gs;
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = ctx.ss;
  // Restore control registers
  controlRegisters[0] = ctx.cr0;
  controlRegisters[2] = ctx.cr2;
  controlRegisters[3] = ctx.cr3;
  controlRegisters[4] = ctx.cr4;
  // Restore FPU state (simplified, assumes direct copy)
  std::memcpy(&fpuState, ctx.fpu_state_bytes, sizeof(FPUState));

  spdlog::info("X86_64CPU[{}]: Context set: RIP=0x{:x}, RSP=0x{:x}", m_cpuId,
               ctx.rip, ctx.rsp);
}

std::unordered_map<std::string, uint64_t> X86_64CPU::GetDiagnostics() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  std::unordered_map<std::string, uint64_t> diagnostics;
  diagnostics["cycles"] = pipeline->GetStats().cycles;
  diagnostics["instructionsExecuted"] =
      pipeline->GetStats().instructionsExecuted;
  diagnostics["stalls"] = pipeline->GetStats().stalls;
  diagnostics["data_hazard_stalls"] = pipeline->GetStats().data_hazard_stalls;
  diagnostics["memory_stalls"] = pipeline->GetStats().memory_stalls;
  diagnostics["branch_hits"] = pipeline->GetStats().branch_hits;
  diagnostics["branch_mispredictions"] =
      pipeline->GetStats().branch_mispredictions;
  diagnostics["interruptQueueSize"] = interruptQueue.size();
  diagnostics["tlbHits"] = tlb.GetHits();
  diagnostics["tlbMisses"] = tlb.GetMisses();
  spdlog::trace("X86_64CPU[{}]: Diagnostics retrieved", m_cpuId);
  return diagnostics;
}

bool X86_64CPU::SwitchToFiber(uint64_t fiberId) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  try {
    // Save current CPU state
    CPUContext currentContext;
    currentContext.registers = registers;
    currentContext.rflags = rflags;
    currentContext.xmmRegisters = xmmRegisters;
    currentContext.rip = GetRegister(Register::RIP);
    currentContext.rsp = GetRegister(Register::RSP);
    // Save segment registers
    currentContext.cs = GetCS();
    currentContext.ds = GetDS();
    currentContext.es = GetES();
    currentContext.fs = GetFS();
    currentContext.gs = GetGS();
    currentContext.ss = GetSS();
    // Save control registers
    currentContext.cr0 = GetCR0();
    currentContext.cr2 = GetCR2();
    currentContext.cr3 = GetCR3();
    currentContext.cr4 = GetCR4();
    // Save FPU state
    std::memcpy(currentContext.fpu_state_bytes, &fpuState, sizeof(FPUState));

    // Get fiber manager from emulator
    auto &fiberManager = m_emulator.GetFiberManager();

    // Switch to the specified fiber
    bool success = fiberManager.SwitchToFiber(fiberId);
    if (!success) {
      spdlog::error("X86_64CPU[{}]: Failed to switch to fiber {}", m_cpuId,
                    fiberId);
      return false;
    }

    // The fiber manager should have updated the CPU context
    // In a full implementation, we would restore the fiber's saved context
    // here
    spdlog::info("X86_64CPU[{}]: Successfully switched to fiber {}", m_cpuId,
                 fiberId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Exception during fiber switch to {}: {}",
                  m_cpuId, fiberId, e.what());
    return false;
  }
}

bool X86_64CPU::IsRunning() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return running;
}

bool X86_64CPU::ValidateIOPortAccess(uint16_t port, uint8_t size) {
  // Check I/O privilege level (IOPL) in RFLAGS
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint8_t iopl = GetIOPL();
  uint8_t cpl = GetCPL();

  // Validate port range and size first
  if (port + size > 0x10000) {
    spdlog::error("X86_64CPU[{}]: I/O port access beyond valid range: "
                  "port=0x{:x}, size={}",
                  m_cpuId, port, size);
    TriggerInterrupt(EXC_GP, 0, false);
    return false;
  }

  // If CPL <= IOPL, access is allowed without checking I/O permission bitmap
  if (cpl <= iopl) {
    spdlog::trace("X86_64CPU[{}]: I/O port access allowed by IOPL: "
                  "port=0x{:x}, CPL={}, IOPL={}",
                  m_cpuId, port, cpl, iopl);
    return true;
  }

  // CPL > IOPL, need to check I/O permission bitmap
  spdlog::debug("X86_64CPU[{}]: Checking I/O permission bitmap for port "
                "0x{:x}, CPL={}, IOPL={}",
                m_cpuId, port, cpl, iopl);

  // Load TSS and I/O permission bitmap if not already loaded
  LoadIOPermissionBitmap();

  // Check I/O permission bitmap
  if (!CheckIOPermissionBitmap(port, size)) {
    spdlog::warn("X86_64CPU[{}]: I/O port access denied by permission bitmap: "
                 "port=0x{:x}, size={}",
                 m_cpuId, port, size);
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return false;
  }

  spdlog::trace("X86_64CPU[{}]: I/O port access allowed by permission bitmap: "
                "port=0x{:x}",
                m_cpuId, port);
  return true;
}

void X86_64CPU::LoadTSSDescriptor() {
  if (tr == 0) {
    spdlog::debug("X86_64CPU[{}]: TR is 0, no TSS loaded", m_cpuId);
    return;
  }

  // Calculate TSS descriptor address in GDT
  uint64_t descriptorAddr = gdtrBase + (tr & 0xFFF8); // Clear RPL and TI bits

  if (descriptorAddr + sizeof(TSSDescriptor) > gdtrBase + gdtrLimit) {
    spdlog::error("X86_64CPU[{}]: TSS descriptor beyond GDT limit", m_cpuId);
    TriggerInterrupt(EXC_GP, tr, false);
    return;
  }

  // Read TSS descriptor from memory
  TSSDescriptor tssDesc;
  mmu.ReadVirtual(descriptorAddr, &tssDesc, sizeof(TSSDescriptor),
                  GetProcessId());

  // Calculate TSS base address (64-bit)
  tssBase = static_cast<uint64_t>(tssDesc.base_low) |
            (static_cast<uint64_t>(tssDesc.base_mid) << 16) |
            (static_cast<uint64_t>(tssDesc.base_high) << 24) |
            (static_cast<uint64_t>(tssDesc.base_upper) << 32);

  // Calculate TSS limit
  tssLimit = tssDesc.limit_low;
  if (tssDesc.granularity & 0x80) {      // G bit set
    tssLimit = (tssLimit << 12) | 0xFFF; // Page granularity
  }

  spdlog::debug(
      "X86_64CPU[{}]: Loaded TSS descriptor: base=0x{:x}, limit=0x{:x}",
      m_cpuId, tssBase, tssLimit);
}

void X86_64CPU::LoadIOPermissionBitmap() {
  // Load TSS descriptor if not already loaded
  if (tssBase == 0) {
    LoadTSSDescriptor();
  }

  if (tssBase == 0) {
    spdlog::debug(
        "X86_64CPU[{}]: No valid TSS, I/O permission bitmap unavailable",
        m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  // Read TSS to get I/O permission bitmap offset
  TSS64 tss;
  if (tssLimit < sizeof(TSS64)) {
    spdlog::warn("X86_64CPU[{}]: TSS too small for 64-bit TSS structure",
                 m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  mmu.ReadVirtual(tssBase, &tss, sizeof(TSS64), GetProcessId());
  ioPermissionBitmapOffset = tss.io_map_base;

  // Check if I/O permission bitmap offset is valid
  if (ioPermissionBitmapOffset >= tssLimit) {
    spdlog::debug(
        "X86_64CPU[{}]: I/O permission bitmap offset beyond TSS limit",
        m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  // Calculate I/O permission bitmap size (from offset to end of TSS)
  uint32_t bitmapSize = tssLimit - ioPermissionBitmapOffset + 1;

  // Limit bitmap size to reasonable maximum (8KB covers all 65536 ports)
  if (bitmapSize > 8192) {
    bitmapSize = 8192;
  }

  // Read I/O permission bitmap from TSS
  ioPermissionBitmap.resize(bitmapSize);
  mmu.ReadVirtual(tssBase + ioPermissionBitmapOffset, ioPermissionBitmap.data(),
                  bitmapSize, GetProcessId());

  spdlog::debug("X86_64CPU[{}]: Loaded I/O permission bitmap: offset=0x{:x}, "
                "size={} bytes",
                m_cpuId, ioPermissionBitmapOffset, bitmapSize);
}

bool X86_64CPU::CheckIOPermissionBitmap(uint16_t port, uint8_t size) {
  if (ioPermissionBitmap.empty()) {
    // No I/O permission bitmap available, deny access
    return false;
  }

  // Check each port in the range
  for (uint8_t i = 0; i < size; i++) {
    uint16_t currentPort = port + i;
    uint32_t byteIndex = currentPort / 8;
    uint8_t bitIndex = currentPort % 8;

    // Check if byte index is within bitmap
    if (byteIndex >= ioPermissionBitmap.size()) {
      // Port beyond bitmap, deny access
      return false;
    }

    // Check if bit is set (1 = deny, 0 = allow)
    if (ioPermissionBitmap[byteIndex] & (1 << bitIndex)) {
      // Access denied for this port
      return false;
    }
  }

  // All ports in range are allowed
  return true;
}

uint64_t X86_64CPU::ReadIOPort(uint16_t port, uint8_t size) {
  auto weakDev = deviceManager.FindDevice(port);
  if (auto dev = weakDev.lock()) {
    return dev->Read(port, size);
  }
  if (!ValidateIOPortAccess(port, size)) {
    // ValidateIOPortAccess already triggers GP fault
    return 0;
  }

  // In a real emulator, this would interface with hardware devices
  // For now, return dummy values based on common ports
  uint64_t value = 0;

  switch (port) {
  case 0x20:      // PIC1 command
  case 0x21:      // PIC1 data
  case 0xA0:      // PIC2 command
  case 0xA1:      // PIC2 data
    value = 0x00; // PIC not active
    break;
  case 0x40:      // Timer channel 0
  case 0x41:      // Timer channel 1
  case 0x42:      // Timer channel 2
  case 0x43:      // Timer command
    value = 0x00; // Timer inactive
    break;
  case 0x60:      // Keyboard data
    value = 0x00; // No key pressed
    break;
  case 0x64:      // Keyboard status
    value = 0x14; // Ready for input (output buffer empty, input buffer empty,
                  // system flag clear)
    break;
  case 0x70:      // CMOS address
  case 0x71:      // CMOS data
    value = 0x00; // CMOS default
    break;
  case 0x80:      // NMI Status and Control Register (NMI_SC)
    value = 0x00; // Dummy value
    break;
  case 0x92:      // Fast A20 Gate
    value = 0x02; // A20 enabled (bit 1)
    break;
  case 0x3F8:     // COM1 data
  case 0x3F9:     // COM1 interrupt enable
  case 0x3FA:     // COM1 interrupt ID
  case 0x3FB:     // COM1 line control
  case 0x3FC:     // COM1 modem control
  case 0x3FD:     // COM1 line status
  case 0x3FE:     // COM1 modem status
  case 0x3FF:     // COM1 scratch
    value = 0x60; // Serial port ready (LSR: THRE, DR)
    break;
  case 0xCF8:     // PCI Configuration Address Register
    value = 0x00; // No PCI device selected
    break;
  case 0xCFC:           // PCI Configuration Data Register
    value = 0xFFFFFFFF; // Dummy value for unconfigured PCI
    break;
  default:
    spdlog::warn("X86_64CPU[{}]: Read from unhandled I/O port 0x{:x}", m_cpuId,
                 port);
    value = 0xFFFFFFFFFFFFFFFFULL; // Unconnected port
    break;
  }

  // Mask to requested size
  switch (size) {
  case 1:
    value &= 0xFF;
    break;
  case 2:
    value &= 0xFFFF;
    break;
  case 4:
    value &= 0xFFFFFFFF;
    break;
  // 8-byte I/O not supported in x86-64
  default:
    spdlog::error("X86_64CPU[{}]: Invalid I/O read size: {}", m_cpuId, size);
    TriggerInterrupt(EXC_GP, 0, false);
    return 0;
  }

  spdlog::trace("X86_64CPU[{}]: Read I/O port 0x{:x} size={} value=0x{:x}",
                m_cpuId, port, size, value);
  return value;
}

void X86_64CPU::WriteIOPort(uint16_t port, uint64_t value, uint8_t size) {
  auto weakDev = deviceManager.FindDevice(port);
  if (auto dev = weakDev.lock()) {
    dev->Write(port, value, size);
    return;
  }
  if (!ValidateIOPortAccess(port, size)) {
    // ValidateIOPortAccess already triggers GP fault
    return;
  }

  // In a real emulator, this would interface with hardware devices
  spdlog::trace("X86_64CPU[{}]: Write I/O port 0x{:x} size={} value=0x{:x}",
                m_cpuId, port, size, value);

  // Handle some common ports for logging and basic state changes
  switch (port) {
  case 0x20: // PIC1 command
  case 0xA0: // PIC2 command
    spdlog::debug("X86_64CPU[{}]: PIC command written: 0x{:x}", m_cpuId, value);
    break;
  case 0x21: // PIC1 data
  case 0xA1: // PIC2 data
    spdlog::debug("X86_64CPU[{}]: PIC mask written: 0x{:x}", m_cpuId, value);
    break;
  case 0x43: // Timer command
    spdlog::debug("X86_64CPU[{}]: Timer command written: 0x{:x}", m_cpuId,
                  value);
    break;
  case 0x64:             // Keyboard command
    if (value == 0xFE) { // Pulse CPU reset
      spdlog::warn("X86_64CPU[{}]: Keyboard controller requested CPU reset!",
                   m_cpuId);
      // This would typically trigger a full emulator reset
      // For now, just log
    }
    break;
  case 0x70: // CMOS address
    spdlog::debug("X86_64CPU[{}]: CMOS address written: 0x{:x}", m_cpuId,
                  value);
    break;
  case 0x71: // CMOS data
    spdlog::debug("X86_64CPU[{}]: CMOS data written: 0x{:x}", m_cpuId, value);
    break;
  case 0x92: // Fast A20 Gate
    spdlog::debug("X86_64CPU[{}]: Fast A20 Gate written: 0x{:x}", m_cpuId,
                  value);
    // Bit 1 controls A20 gate
    break;
  case 0x3F8: // COM1 data
    spdlog::debug("X86_64CPU[{}]: Serial output: 0x{:x} ('{}')", m_cpuId, value,
                  (value >= 32 && value <= 126) ? static_cast<char>(value)
                                                : '?');
    break;
  case 0xCF8: // PCI Configuration Address Register
    spdlog::debug("X86_64CPU[{}]: PCI Config Address written: 0x{:x}", m_cpuId,
                  value);
    // In a real emulator, this would select a PCI device/register
    break;
  case 0xCFC: // PCI Configuration Data Register
    spdlog::debug("X86_64CPU[{}]: PCI Config Data written: 0x{:x}", m_cpuId,
                  value);
    break;
  default:
    spdlog::warn(
        "X86_64CPU[{}]: Write to unhandled I/O port 0x{:x} with value 0x{:x}",
        m_cpuId, port, value);
    break;
  }
}

void X86_64CPU::SetIOPermissionBit(uint16_t port, bool allow) {
  if (ioPermissionBitmap.empty()) {
    // Initialize bitmap if not present (8KB for all 65536 ports)
    ioPermissionBitmap.resize(8192, 0xFF); // Default deny all
  }

  uint32_t byteIndex = port / 8;
  uint8_t bitIndex = port % 8;

  if (byteIndex >= ioPermissionBitmap.size()) {
    spdlog::warn("X86_64CPU[{}]: Port 0x{:x} beyond I/O permission bitmap",
                 m_cpuId, port);
    return;
  }

  if (allow) {
    // Clear bit to allow access (0 = allow)
    ioPermissionBitmap[byteIndex] &= ~(1 << bitIndex);
  } else {
    // Set bit to deny access (1 = deny)
    ioPermissionBitmap[byteIndex] |= (1 << bitIndex);
  }

  spdlog::debug("X86_64CPU[{}]: Set I/O permission for port 0x{:x}: {}",
                m_cpuId, port, allow ? "ALLOW" : "DENY");
}

void X86_64CPU::SetIOPermissionRange(uint16_t startPort, uint16_t endPort,
                                     bool allow) {
  for (uint16_t port = startPort; port <= endPort; port++) {
    SetIOPermissionBit(port, allow);
  }

  spdlog::info("X86_64CPU[{}]: Set I/O permission for range 0x{:x}-0x{:x}: {}",
               m_cpuId, startPort, endPort, allow ? "ALLOW" : "DENY");
}

void X86_64CPU::ClearIOPermissionBitmap() {
  ioPermissionBitmap.clear();
  ioPermissionBitmapOffset = 0;
  spdlog::debug("X86_64CPU[{}]: Cleared I/O permission bitmap", m_cpuId);
}

void X86_64CPU::UpdateIOPermissionBitmapOffset(uint16_t offset) {
  ioPermissionBitmapOffset = offset;
  // Force reload of bitmap on next access
  ioPermissionBitmap.clear();
  spdlog::debug("X86_64CPU[{}]: Updated I/O permission bitmap offset to 0x{:x}",
                m_cpuId, offset);
}

void X86_64CPU::UpdateStringIndexes(uint8_t size, bool forward) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  int64_t delta = forward ? size : -size;

  // Update RSI and RDI based on direction flag
  bool directionFlag = GetFlag(FLAG_DF);
  if (directionFlag) {
    delta = -delta;
  }

  uint64_t rsi = _getRegister(Register::RSI);
  uint64_t rdi = _getRegister(Register::RDI);
  _setRegister(Register::RSI, rsi + delta);
  _setRegister(Register::RDI, rdi + delta);

  spdlog::trace("X86_64CPU[{}]: Updated string indexes: RSI=0x{:x}, "
                "RDI=0x{:x}, delta={}",
                m_cpuId, rsi + delta, rdi + delta, delta);
}

void X86_64CPU::ExecuteStringOperation(const DecodedInstruction &instr,
                                       uint64_t &nextRip) {
  // Handle REP prefixes
  bool hasRepPrefix = instr.repPrefix || instr.repePrefix || instr.repnePrefix;
  uint64_t count = hasRepPrefix ? GetRegister(Register::RCX) : 1;

  if (hasRepPrefix && count == 0) {
    spdlog::trace(
        "X86_64CPU[{}]: String operation with REP but RCX=0, skipping",
        m_cpuId);
    return;
  }

  // Determine operation size
  uint8_t size = 1;
  switch (instr.instType) {
  case InstructionType::Movsb:
  case InstructionType::Stosb:
  case InstructionType::Lodsb:
  case InstructionType::Scasb:
  case InstructionType::Cmpsb:
    size = 1;
    break;
  case InstructionType::Movsw:
  case InstructionType::Stosw:
  case InstructionType::Lodsw:
  case InstructionType::Scasw:
  case InstructionType::Cmpsw:
    size = 2;
    break;
  case InstructionType::Movsd:
  case InstructionType::Stosd:
  case InstructionType::Lodsd:
  case InstructionType::Scasd:
  case InstructionType::Cmpsd:
    size = 4;
    break;
  case InstructionType::Movsq:
  case InstructionType::Stosq:
  case InstructionType::Lodsq:
  case InstructionType::Scasq:
  case InstructionType::Cmpsq:
    size = 8;
    break;
  default:
    spdlog::error("X86_64CPU[{}]: Invalid string operation type: {}", m_cpuId,
                  static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  bool continueLoop = true;
  uint64_t iterations = 0;

  while (continueLoop && count > 0) {
    switch (instr.instType) {
    case InstructionType::Movsb:
    case InstructionType::Movsw:
    case InstructionType::Movsd:
    case InstructionType::Movsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t rdi = GetRegister(Register::RDI);

      // Read from source
      std::vector<uint8_t> buffer(size);
      try {
        if (!mmu.ReadVirtual(rsi, buffer.data(), size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: MOVSx source read failed at 0x{:x}: {}",
                      m_cpuId, rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      // Write to destination
      try {
        if (!mmu.WriteVirtual(rdi, buffer.data(), size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error(
            "X86_64CPU[{}]: MOVSx destination write failed at 0x{:x}: {}",
            m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      UpdateStringIndexes(size);
      spdlog::trace(
          "X86_64CPU[{}]: MOVS{}: copied {} bytes from 0x{:x} to 0x{:x}",
          m_cpuId,
          size == 1   ? "B"
          : size == 2 ? "W"
          : size == 4 ? "D"
                      : "Q",
          size, rsi, rdi);
      break;
    }

    case InstructionType::Stosb:
    case InstructionType::Stosw:
    case InstructionType::Stosd:
    case InstructionType::Stosq: {
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t value = GetRegister(Register::RAX) & ((1ULL << (size * 8)) - 1);

      try {
        if (!mmu.WriteVirtual(rdi, &value, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: STOSx write failed at 0x{:x}: {}",
                      m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      UpdateStringIndexes(size);
      spdlog::trace("X86_64CPU[{}]: STOS{}: stored 0x{:x} to 0x{:x}", m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    value, rdi);
      break;
    }

    case InstructionType::Lodsb:
    case InstructionType::Lodsw:
    case InstructionType::Lodsd:
    case InstructionType::Lodsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t value = 0;

      try {
        if (!mmu.ReadVirtual(rsi, &value, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: LODSx read failed at 0x{:x}: {}", m_cpuId,
                      rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      // Store in AL/AX/EAX/RAX based on size
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      uint64_t rax = _getRegister(Register::RAX);
      switch (size) {
      case 1:
        rax = (rax & 0xFFFFFFFFFFFFFF00ULL) | (value & 0xFF);
        break;
      case 2:
        rax = (rax & 0xFFFFFFFFFFFF0000ULL) | (value & 0xFFFF);
        break;
      case 4:
        rax = (rax & 0xFFFFFFFF00000000ULL) | (value & 0xFFFFFFFF);
        break;
      case 8:
        rax = value;
        break;
      }
      _setRegister(Register::RAX, rax);
      lock.unlock();

      UpdateStringIndexes(size);
      spdlog::trace("X86_64CPU[{}]: LODS{}: loaded 0x{:x} from 0x{:x} into RAX",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    value, rsi);
      break;
    }

    case InstructionType::Scasb:
    case InstructionType::Scasw:
    case InstructionType::Scasd:
    case InstructionType::Scasq: {
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t raxValue =
          GetRegister(Register::RAX) & ((1ULL << (size * 8)) - 1);
      uint64_t memValue = 0;

      try {
        if (!mmu.ReadVirtual(rdi, &memValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: SCASx read failed at 0x{:x}: {}", m_cpuId,
                      rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      memValue &= ((1ULL << (size * 8)) - 1);
      uint64_t result = raxValue - memValue;
      UpdateArithmeticFlags(raxValue, memValue, result, size * 8, true);

      UpdateStringIndexes(size);

      // Check termination condition for REPE/REPNE
      if (instr.repePrefix) {
        continueLoop = GetFlag(FLAG_ZF); // Continue while equal
      } else if (instr.repnePrefix) {
        continueLoop = !GetFlag(FLAG_ZF); // Continue while not equal
      }

      spdlog::trace("X86_64CPU[{}]: SCAS{}: compared RAX=0x{:x} with "
                    "[0x{:x}]=0x{:x}, ZF={}",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    raxValue, rdi, memValue, GetFlag(FLAG_ZF) ? 1 : 0);
      break;
    }

    case InstructionType::Cmpsb:
    case InstructionType::Cmpsw:
    case InstructionType::Cmpsd:
    case InstructionType::Cmpsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t srcValue = 0, dstValue = 0;

      try {
        if (!mmu.ReadVirtual(rsi, &srcValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: CMPSx source read failed at 0x{:x}: {}",
                      m_cpuId, rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      try {
        if (!mmu.ReadVirtual(rdi, &dstValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error(
            "X86_64CPU[{}]: CMPSx destination read failed at 0x{:x}: {}",
            m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      srcValue &= ((1ULL << (size * 8)) - 1);
      dstValue &= ((1ULL << (size * 8)) - 1);
      uint64_t result = srcValue - dstValue;
      UpdateArithmeticFlags(srcValue, dstValue, result, size * 8, true);

      UpdateStringIndexes(size);

      // Check termination condition for REPE/REPNE
      if (instr.repePrefix) {
        continueLoop = GetFlag(FLAG_ZF); // Continue while equal
      } else if (instr.repnePrefix) {
        continueLoop = !GetFlag(FLAG_ZF); // Continue while not equal
      }

      spdlog::trace("X86_64CPU[{}]: CMPS{}: compared [0x{:x}]=0x{:x} with "
                    "[0x{:x}]=0x{:x}, ZF={}",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    rsi, srcValue, rdi, dstValue, GetFlag(FLAG_ZF) ? 1 : 0);
      break;
    }

    default:
      spdlog::error("X86_64CPU[{}]: Unhandled string operation: {}", m_cpuId,
                    static_cast<int>(instr.instType));
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    if (hasRepPrefix) {
      count--;
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      _setRegister(Register::RCX, count);
      lock.unlock();

      // Prevent infinite loops in emulation
      iterations++;
      if (iterations >
          MAX_REP_ITERATIONS) { // Define MAX_REP_ITERATIONS in header
        spdlog::warn("X86_64CPU[{}]: String operation interrupted after {} "
                     "iterations to prevent hang",
                     m_cpuId, iterations);
        break;
      }
    } else {
      continueLoop = false;
    }
  }

  spdlog::trace("X86_64CPU[{}]: String operation completed after {} "
                "iterations, RCX=0x{:x}",
                m_cpuId, iterations, GetRegister(Register::RCX));
}

// ========================
// Floating-Point Unit (FPU)
// ========================

void X86_64CPU::ExecuteFloatingPointInstruction(const DecodedInstruction &instr,
                                                uint64_t &nextRip) {
  // Check for stack overflow/underflow before executing
  if (fpuState.IsStackEmpty() && (instr.instType == InstructionType::Fadd ||
                                  instr.instType == InstructionType::Fsub ||
                                  instr.instType == InstructionType::Fmul ||
                                  instr.instType == InstructionType::Fdiv ||
                                  instr.instType == InstructionType::Fcom ||
                                  instr.instType == InstructionType::Fsin ||
                                  instr.instType == InstructionType::Fcos)) {
    fpuState.SetStackUnderflow();
    TriggerInterrupt(EXC_MF, 0, false); // Math Fault
    return;
  }

  switch (instr.instType) {
  case InstructionType::Fld: {
    // FLD - Load floating point value onto stack
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    double value = 0.0;
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::MEMORY) {
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      if (instr.operands[0].size == 32) {
        float fval;
        mmu.ReadVirtual(addr, &fval, 4, GetProcessId());
        value = static_cast<double>(fval);
      } else if (instr.operands[0].size == 64) {
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      } else if (instr.operands[0].size == 80) {
        // 80-bit extended precision (simplified)
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t stack_idx = fpuState.GetStackIndex(st_reg);
      value = fpuState.st[stack_idx];
    }

    fpuState.PushStack(value);
    break;
  }

  case InstructionType::Fst:
  case InstructionType::Fstp: {
    // FST/FSTP - Store floating point value from stack
    if (fpuState.IsStackEmpty()) {
      fpuState.SetStackUnderflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    double value = fpuState.st[fpuState.top];

    if (instr.operands[0].type == DecodedInstruction::Operand::Type::MEMORY) {
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      if (instr.operands[0].size == 32) {
        float fval = static_cast<float>(value);
        mmu.WriteVirtual(addr, &fval, 4, GetProcessId());
      } else if (instr.operands[0].size == 64) {
        mmu.WriteVirtual(addr, &value, 8, GetProcessId());
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t stack_idx = fpuState.GetStackIndex(st_reg);
      fpuState.st[stack_idx] = value;
    }

    // FSTP pops the stack, FST does not
    if (instr.instType == InstructionType::Fstp) {
      fpuState.PopStack();
    }
    break;
  }

  case InstructionType::Fadd: {
    // FADD/FADDP/FIADD
    if (instr.operandCount == 0) {
      // FADD ST(0), ST(1)
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      uint8_t st1_idx = fpuState.GetStackIndex(1);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + fpuState.st[st1_idx];
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      // FADD ST(0), ST(i)
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      uint8_t sti_idx = fpuState.GetStackIndex(st_reg);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + fpuState.st[sti_idx];
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::MEMORY) {
      // FADD m32fp/m64fp
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      double value;
      if (instr.operands[0].size == 32) {
        float fval;
        mmu.ReadVirtual(addr, &fval, 4, GetProcessId());
        value = static_cast<double>(fval);
      } else {
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      }
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + value;
    }
    break;
  }
  case InstructionType::Fmul: {
    // Similar implementation to FADD
    break;
  }
  case InstructionType::Fcom: {
    // Floating-point compare
    double st0 = fpuState.st[0];
    double operand = 0.0;

    if (instr.operandCount == 0) {
      operand = fpuState.st[1];
    } else {
      uint8_t st_index = static_cast<uint8_t>(instr.operands[0].reg) -
                         static_cast<uint8_t>(Register::ST0);
      operand = fpuState.st[st_index];
    }

    // Clear condition codes
    fpuState.statusWord &= ~0x4500;

    if (std::isnan(st0) || std::isnan(operand)) {
      fpuState.statusWord |= 0x4500; // Invalid operation
    } else if (st0 == operand) {
      fpuState.statusWord |= 0x4000; // C3 (equal)
    } else if (st0 < operand) {
      fpuState.statusWord |= 0x0100; // C0 (less than)
    } else {
      // C2 and C3 cleared for greater than
    }
    break;
  }
  case InstructionType::Fsin: {
    if (std::fabs(fpuState.st[0]) > 2.0 * M_PI) {
      fpuState.statusWord |= 0x02; // Stack error
    } else {
      fpuState.st[0] = std::sin(fpuState.st[0]);
    }
    break;
  }
  case InstructionType::Fcos: {
    if (std::fabs(fpuState.st[0]) > 2.0 * M_PI) {
      fpuState.statusWord |= 0x02; // Stack error
    } else {
      fpuState.st[0] = std::cos(fpuState.st[0]);
    }
    break;
  }
  case InstructionType::Fsincos: {
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double angle = fpuState.st[st0_idx];

    if (std::fabs(angle) > (1ULL << 63)) { // Large angle check
      fpuState.statusWord |= 0x0400;       // C2 set for incomplete reduction
    } else {
      double sin_val = std::sin(angle);
      double cos_val = std::cos(angle);

      // Replace ST(0) with sine
      fpuState.st[st0_idx] = sin_val;

      // Push cosine onto stack
      fpuState.PushStack(cos_val);

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fprem: {
    // Partial remainder (IEEE 754 compatible)
    if (fpuState.IsStackEmpty()) {
      fpuState.SetStackUnderflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    uint8_t st1_idx = fpuState.GetStackIndex(1);
    double dividend = fpuState.st[st0_idx];
    double divisor = fpuState.st[st1_idx];

    if (divisor == 0.0) {
      // Division by zero
      fpuState.statusWord |= 0x0004; // Set D flag (Divide by Zero)
      fpuState.st[st0_idx] = std::copysign(INFINITY, dividend); // Result is ±∞
    } else if (std::isnan(dividend) || std::isnan(divisor)) {
      // NaN operand
      fpuState.statusWord |= 0x0001; // Set IE flag (Invalid Operation)
      fpuState.st[st0_idx] = std::numeric_limits<double>::quiet_NaN();
    } else if (std::isinf(dividend)) {
      // Infinity / finite
      fpuState.statusWord |= 0x0001; // Set IE flag (Invalid Operation)
      fpuState.st[st0_idx] = std::numeric_limits<double>::quiet_NaN();
    } else {
      // Normal case: compute partial remainder
      double temp = dividend / divisor;
      int n = static_cast<int>(temp);

      // Calculate remainder
      double remainder = dividend - (n * divisor);
      fpuState.st[st0_idx] = remainder;

      // Set condition code bits C0, C1, C3 based on quotient bits
      fpuState.statusWord &= ~0x4500;         // Clear C0, C1, C3
      fpuState.statusWord |= ((n & 1) << 8);  // C0 = Q0
      fpuState.statusWord |= ((n & 2) << 13); // C3 = Q1
      fpuState.statusWord |= ((n & 4) << 12); // C1 = Q2

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fscale: {
    // Scale by power of two
    double exponent = std::floor(fpuState.st[1]);
    if (exponent > 1024 || exponent < -1024) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[0] = std::scalbn(fpuState.st[0], static_cast<int>(exponent));
    }
    break;
  }
  case InstructionType::F2xm1: {
    // 2^x - 1
    if (fpuState.st[0] < -1.0 || fpuState.st[0] > 1.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[0] = std::exp2(fpuState.st[0]) - 1.0;
    }
    break;
  }
  case InstructionType::Fyl2x: {
    // y * log2(x)
    double st0 = fpuState.st[0];
    double st1 = fpuState.st[1];

    if (st0 <= 0.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[1] = st1 * std::log2(st0);
      // Pop stack
      fpuState.st[0] = fpuState.st[1];
      fpuState.statusWord =
          (fpuState.statusWord & ~0x3800) | 0x2000; // C1=0, C2=0, C3=0, TOP=1
    }
    break;
  }
  case InstructionType::Fptan: {
    // Tangent of ST(0) and push 1.0
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double angle = fpuState.st[st0_idx];

    if (std::fabs(angle) > (1ULL << 63)) { // Large angle check
      fpuState.statusWord |= 0x0400;       // C2 set for incomplete reduction
    } else {
      double tan_val = std::tan(angle);

      // Replace ST(0) with tangent
      fpuState.st[st0_idx] = tan_val;

      // Push 1.0 onto stack (required by FPTAN)
      fpuState.PushStack(1.0);

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fpatan: {
    // Arctangent of ST(1)/ST(0)
    double y = fpuState.st[1];
    double x = fpuState.st[0];

    if (x == 0.0 && y == 0.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[1] = std::atan2(y, x);
      // Pop stack
      fpuState.st[0] = fpuState.st[1];
    }
    break;
  }
  case InstructionType::Fxtract: {
    // Extract exponent and significand
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double value = fpuState.st[st0_idx];

    if (value == 0.0) {
      // Special case for zero
      fpuState.st[st0_idx] = -INFINITY; // Exponent = -∞ for zero
      fpuState.PushStack(0.0);          // Significand = 0
      fpuState.statusWord |= 0x0001;    // Set IE (Invalid Operation)
    } else {
      int bin_exp;
      double significand = std::frexp(value, &bin_exp);

      // Adjust significand to be in [1.0, 2.0) range
      significand *= 2.0;
      bin_exp -= 1;

      // Replace ST(0) with exponent
      fpuState.st[st0_idx] = static_cast<double>(bin_exp);

      // Push significand onto stack
      fpuState.PushStack(significand);
    }
    break;
  }
  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled FPU instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }
}

// ========================
// Advanced SIMD (AVX/AVX2)
// ========================

void X86_64CPU::ExecuteAVXInstruction(const DecodedInstruction &instr,
                                      uint64_t &nextRip) {
  // ... [previous AVX code] ...

  // Add new AVX instructions
  switch (instr.instType) {
  case InstructionType::Vaddps: {
    __m256i op1_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i op2_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 op1 = _mm256_castsi256_ps(op1_int);
    __m256 op2 = _mm256_castsi256_ps(op2_int);
    __m256 result = _mm256_add_ps(op1, op2);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vaddpd: {
    __m256i op1_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i op2_int = ReadXmmOperandValue(instr.operands[2]);
    __m256d op1 = _mm256_castsi256_pd(op1_int);
    __m256d op2 = _mm256_castsi256_pd(op2_int);
    __m256d result = _mm256_add_pd(op1, op2);
    WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result));
    break;
  }
  case InstructionType::Vfmadd132ps: {
    // Fused multiply-add: a*b + c
    __m256i a_int = ReadXmmOperandValue(instr.operands[0]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i c_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 a = _mm256_castsi256_ps(a_int);
    __m256 b = _mm256_castsi256_ps(b_int);
    __m256 c = _mm256_castsi256_ps(c_int);
    __m256 result = _mm256_fmadd_ps(a, b, c);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vgatherdps: {
    // Gather packed single-precision values
    __m256i vindex_256 = ReadXmmOperandValue(instr.operands[2]);
    __m256i mask_256 = ReadXmmOperandValue(instr.operands[3]);
    __m256i base_256 = ReadXmmOperandValue(instr.operands[1]);

    // Extract lower 128 bits for gather operation
    __m128i vindex = _mm256_extracti128_si256(vindex_256, 0);
    __m128i mask = _mm256_extracti128_si256(mask_256, 0);
    __m128 base = _mm_castsi128_ps(_mm256_extracti128_si256(base_256, 0));

    // Use aligned arrays to access individual elements
    alignas(16) int32_t vindex_arr[4];
    alignas(16) int32_t mask_arr[4];
    alignas(16) float base_arr[4];
    alignas(16) float result_arr[4] = {0};

    _mm_store_si128((__m128i *)vindex_arr, vindex);
    _mm_store_si128((__m128i *)mask_arr, mask);
    _mm_store_ps(base_arr, base);

    for (int i = 0; i < 4; i++) {
      if (mask_arr[i] & 0x80000000) {
        uint64_t addr = static_cast<uint64_t>(base_arr[i]) + vindex_arr[i];
        float value;
        mmu.ReadVirtual(addr, &value, 4, GetProcessId());
        result_arr[i] = value;
      }
    }

    __m128 result = _mm_load_ps(result_arr);
    WriteXmmOperandValue(instr.operands[0],
                         _mm256_zextsi128_si256(_mm_castps_si128(result)));
    break;
  }
  case InstructionType::Vperm2f128: {
    // Permute floating-point values
    __m256i a_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 a = _mm256_castsi256_ps(a_int);
    __m256 b = _mm256_castsi256_ps(b_int);
    int imm8 = static_cast<int>(instr.operands[3].immediate);

    __m256 result;
    switch (imm8 & 0x0F) {
    case 0x00:
      result = _mm256_permute2f128_ps(a, b, 0x00);
      break;
    case 0x01:
      result = _mm256_permute2f128_ps(a, b, 0x01);
      break;
    case 0x02:
      result = _mm256_permute2f128_ps(a, b, 0x02);
      break;
    case 0x03:
      result = _mm256_permute2f128_ps(a, b, 0x03);
      break;
    default:
      spdlog::warn("X86_64CPU[{}]: Invalid permute mask: 0x{:x}", m_cpuId,
                   imm8);
      result = _mm256_setzero_ps();
    }
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vbroadcastss: {
    // Broadcast single-precision value
    float value;
    if (instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {
      __m256i xmm_int = ReadXmmOperandValue(instr.operands[1]);
      __m128 xmm_float = _mm_castsi128_ps(_mm256_extracti128_si256(xmm_int, 0));
      alignas(16) float temp[4];
      _mm_store_ps(temp, xmm_float);
      value = temp[0];
    } else {
      uint64_t addr = CalculateMemoryAddress(instr.operands[1]);
      mmu.ReadVirtual(addr, &value, 4, GetProcessId());
    }

    __m256 result = _mm256_set1_ps(value);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vroundps: {
    // Round packed single-precision values
    __m256i input_int = ReadXmmOperandValue(instr.operands[1]);
    __m128 input = _mm_castsi128_ps(_mm256_extracti128_si256(input_int, 0));
    int rounding = static_cast<int>(instr.operands[2].immediate) & 0x7;

    __m128 result;
    switch (rounding) {
    case 0:
      result = _mm_round_ps(input, _MM_FROUND_TO_NEAREST_INT);
      break;
    case 1:
      result = _mm_round_ps(input, _MM_FROUND_TO_NEG_INF);
      break;
    case 2:
      result = _mm_round_ps(input, _MM_FROUND_TO_POS_INF);
      break;
    case 3:
      result = _mm_round_ps(input, _MM_FROUND_TO_ZERO);
      break;
    default:
      spdlog::warn("X86_64CPU[{}]: Invalid rounding mode: {}", m_cpuId,
                   rounding);
      result = input;
    }
    WriteXmmOperandValue(instr.operands[0],
                         _mm256_zextsi128_si256(_mm_castps_si128(result)));
    break;
  }
  case InstructionType::Vtestps: {
    // Logical AND and set flags
    __m256i a_int = ReadXmmOperandValue(instr.operands[0]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[1]);
    __m128 a = _mm_castsi128_ps(_mm256_extracti128_si256(a_int, 0));
    __m128 b = _mm_castsi128_ps(_mm256_extracti128_si256(b_int, 0));

    __m128 and_result = _mm_and_ps(a, b);
    int mask = _mm_movemask_ps(and_result);

    // Set ZF if all bits are 0, CF if all bits are 1
    SetFlag(FLAG_ZF, mask == 0);
    SetFlag(FLAG_CF, mask == 0x0F);
    break;
  }
  case InstructionType::Vzeroupper: {
    // Zero upper bits of YMM registers
    for (auto &reg : xmmRegisters) {
      reg = _mm256_zextsi128_si256(_mm256_castsi256_si128(reg));
    }
    break;
  }
  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled AVX instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }
}

// ========================
// System Management Mode (SMM)
// ========================

void X86_64CPU::EnterSMM(SMMInterruptType type) {
  if (inSMM) {
    spdlog::warn("X86_64CPU[{}]: Already in SMM, ignoring SMM entry", m_cpuId);
    return;
  }

  smmEntryTime = std::chrono::high_resolution_clock::now();
  smmEntryCount++;
  smmInterruptType = static_cast<uint8_t>(type);

  // Save complete CPU state to SMM saved state structure
  SaveSMMState();

  // Write SMM state to SMRAM
  WriteSMMStateToMemory();

  // Set up SMM execution environment
  // Clear interrupt flag and other flags
  uint64_t newRflags =
      GetRflags() & ~(0x200 | 0x400 | 0x10000); // Clear IF, DF, RF
  SetRflags(newRflags);

  // Set up SMM segment registers (real mode like environment)
  SetSegmentRegister(SegmentRegister::CS, 0x3000); // CS = SMBASE >> 4
  SetSegmentRegister(SegmentRegister::DS, 0x0000);
  SetSegmentRegister(SegmentRegister::ES, 0x0000);
  SetSegmentRegister(SegmentRegister::FS, 0x0000);
  SetSegmentRegister(SegmentRegister::GS, 0x0000);
  SetSegmentRegister(SegmentRegister::SS, 0x0000);

  // Set SMM entry point
  SetRegister(Register::RIP, SMM_HANDLER_ENTRY_POINT);
  SetRegister(Register::RSP, smbase + 0x8000); // Set stack pointer

  // Set SMM flag
  inSMM = true;

  spdlog::info("X86_64CPU[{}]: Entered SMM, type=0x{:x}, SMBASE=0x{:x}",
               m_cpuId, static_cast<uint8_t>(type), smbase);
}

void X86_64CPU::ExitSMM() {
  if (!inSMM) {
    spdlog::warn("X86_64CPU[{}]: Not in SMM, ignoring SMM exit", m_cpuId);
    return;
  }

  // Record SMM exit time and update statistics
  smmExitTime = std::chrono::high_resolution_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                      smmExitTime - smmEntryTime)
                      .count();
  smmTotalTime += duration;

  // Update SMM saved state with exit time
  smmSavedState.last_smm_exit_time =
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          smmExitTime.time_since_epoch())
          .count();
  smmSavedState.smm_total_time = smmTotalTime;

  // Read potentially updated state from SMRAM
  ReadSMMStateFromMemory();

  // Check if SMBASE was updated during SMM
  uint64_t newSMBASE = smmSavedState.smbase;
  if (newSMBASE != smbase) {
    spdlog::info("X86_64CPU[{}]: SMBASE updated from 0x{:x} to 0x{:x}", m_cpuId,
                 smbase, newSMBASE);
    smbase = newSMBASE;
  }

  // Restore CPU state from SMM saved state
  RestoreSMMState();

  // Handle auto-halt restart if enabled
  if (smmAutoHaltRestart) {
    // If CPU was in HALT before SMM, re-enter HALT
    SetRegister(Register::RIP,
                GetRegister(Register::RIP) - 1); // Point to HLT instruction
    spdlog::debug("X86_64CPU[{}]: Auto-halt restart enabled, returning to HALT",
                  m_cpuId);
  }

  // Handle I/O instruction restart if enabled
  if (smmIoRestart) {
    // Restore RIP to I/O instruction
    SetRegister(Register::RIP, smmSavedState.io_restart_rip64);
    SetRegister(Register::RCX, smmSavedState.io_restart_rcx64);
    SetRegister(Register::RSI, smmSavedState.io_restart_rsi64);
    SetRegister(Register::RDI, smmSavedState.io_restart_rdi64);
    spdlog::debug("X86_64CPU[{}]: I/O instruction restart enabled, returning "
                  "to I/O instruction",
                  m_cpuId);
  }

  // Clear SMM flag
  inSMM = false;
  smmIoRestart = false;
  smmAutoHaltRestart = false;

  spdlog::info(
      "X86_64CPU[{}]: Exited SMM, duration={} μs, total SMM time={} μs",
      m_cpuId, duration, smmTotalTime);
}

// ========================
// Virtualization (VMX)
// ========================

void X86_64CPU::VMXON(uint64_t region) {
  if (vmxEnabled) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check CR4.VMXE
  if (!(GetCR4() & (1ULL << 13))) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Read VMXON region revision
  uint32_t revision;
  mmu.ReadVirtual(region, &revision, 4, GetProcessId());
  if (revision != VMX_REVISION_ID) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  vmxonRegion = region;
  vmxEnabled = true;
  spdlog::info("X86_64CPU[{}]: VMXON executed", m_cpuId);
}

void X86_64CPU::VMCLEAR(uint64_t region) {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check if region is valid
  if (region & 0xFFF) { // Not page-aligned
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if region is current VMCS
  if (region == currentVMCS) {
    // Save current VMCS to memory
    mmu.WriteVirtual(region, &vmcs, sizeof(VMCS), GetProcessId());
    // Mark VMCS as clear
    vmcs.abort_indicator = 0;
    currentVMCS = 0;
  }

  // Clear VMCS state
  vmcsState = {};
  spdlog::info("X86_64CPU[{}]: VMCLEAR executed, region=0x{:x}", m_cpuId,
               region);
}

void X86_64CPU::VMPTRLD(uint64_t region) {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check VMCS revision
  uint32_t revision;
  mmu.ReadVirtual(region, &revision, 4, GetProcessId());
  if (revision != VMX_REVISION_ID) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  currentVMCS = region;
  // Load VMCS data into memory
  mmu.ReadVirtual(region, &vmcs, sizeof(VMCS), GetProcessId());
  spdlog::info("X86_64CPU[{}]: VMPTRLD executed", m_cpuId);
}

void X86_64CPU::VMLAUNCH() {
  if (!vmxEnabled || currentVMCS == 0) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Save host state
  vmcs.host_cr0 = GetCR0();
  vmcs.host_cr3 = GetCR3();
  vmcs.host_cr4 = GetCR4();
  vmcs.host_rip = GetRegister(Register::RIP);

  // Load guest state
  SetCR0(vmcs.guest_cr0);
  SetCR3(vmcs.guest_cr3);
  SetCR4(vmcs.guest_cr4);
  SetRegister(Register::RIP, vmcs.guest_rip);
  SetRflags(vmcs.guest_rflags);

  inVMXNonRootOperation = true;
  spdlog::info(
      "X86_64CPU[{}]: VMLAUNCH executed, entering VMX non-root operation",
      m_cpuId);
}

void X86_64CPU::VMRESUME() {
  if (!vmxEnabled || currentVMCS == 0) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Similar to VMLAUNCH but without saving host state
  SetCR0(vmcs.guest_cr0);
  SetCR3(vmcs.guest_cr3);
  SetCR4(vmcs.guest_cr4);
  SetRegister(Register::RIP, vmcs.guest_rip);
  SetRflags(vmcs.guest_rflags);

  inVMXNonRootOperation = true;
  spdlog::info(
      "X86_64CPU[{}]: VMRESUME executed, entering VMX non-root operation",
      m_cpuId);
}

void X86_64CPU::VMREAD(uint64_t field, uint64_t &value) {
  if (!vmxEnabled || currentVMCS == 0) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Get field from VMCS
  VMCSField vmcsField = static_cast<VMCSField>(field);

  switch (vmcsField) {
  case VMCSField::GUEST_RIP:
    value = vmcs.guest_rip;
    break;
  case VMCSField::GUEST_RSP:
    value = vmcs.guest_rsp;
    break;
  case VMCSField::GUEST_RFLAGS:
    value = vmcs.guest_rflags;
    break;
  case VMCSField::GUEST_CR0:
    value = vmcs.guest_cr0;
    break;
  case VMCSField::GUEST_CR3:
    value = vmcs.guest_cr3;
    break;
  case VMCSField::GUEST_CR4:
    value = vmcs.guest_cr4;
    break;
  case VMCSField::HOST_CR0:
    value = vmcs.host_cr0;
    break;
  case VMCSField::HOST_CR3:
    value = vmcs.host_cr3;
    break;
  case VMCSField::HOST_CR4:
    value = vmcs.host_cr4;
    break;
  case VMCSField::HOST_RIP:
    value = vmcs.host_rip;
    break;
  case VMCSField::HOST_RSP:
    value = vmcs.host_rsp;
    break;
  case VMCSField::PIN_BASED_VM_EXEC_CONTROL:
    value = vmcs.pin_based_vm_exec_control;
    break;
  case VMCSField::CPU_BASED_VM_EXEC_CONTROL:
    value = vmcs.cpu_based_vm_exec_control;
    break;
  case VMCSField::EXCEPTION_BITMAP:
    value = vmcs.exception_bitmap;
    break;
  case VMCSField::VM_EXIT_CONTROLS:
    value = vmcs.vm_exit_controls;
    break;
  case VMCSField::VM_ENTRY_CONTROLS:
    value = vmcs.vm_entry_controls;
    break;
  case VMCSField::VM_EXIT_REASON:
    value = vmcs.vm_exit_reason;
    break;
  default:
    spdlog::warn("X86_64CPU[{}]: VMREAD with unsupported field 0x{:x}", m_cpuId,
                 static_cast<uint64_t>(vmcsField));
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::trace("X86_64CPU[{}]: VMREAD field=0x{:x}, value=0x{:x}", m_cpuId,
                static_cast<uint64_t>(vmcsField), value);
}

void X86_64CPU::VMWRITE(uint64_t field, uint64_t value) {
  if (!vmxEnabled || currentVMCS == 0) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Write field to VMCS
  VMCSField vmcsField = static_cast<VMCSField>(field);

  switch (vmcsField) {
  case VMCSField::GUEST_RIP:
    vmcs.guest_rip = value;
    break;
  case VMCSField::GUEST_RSP:
    vmcs.guest_rsp = value;
    break;
  case VMCSField::GUEST_RFLAGS:
    vmcs.guest_rflags = value;
    break;
  case VMCSField::GUEST_CR0:
    vmcs.guest_cr0 = value;
    break;
  case VMCSField::GUEST_CR3:
    vmcs.guest_cr3 = value;
    break;
  case VMCSField::GUEST_CR4:
    vmcs.guest_cr4 = value;
    break;
  case VMCSField::HOST_CR0:
    vmcs.host_cr0 = value;
    break;
  case VMCSField::HOST_CR3:
    vmcs.host_cr3 = value;
    break;
  case VMCSField::HOST_CR4:
    vmcs.host_cr4 = value;
    break;
  case VMCSField::HOST_RIP:
    vmcs.host_rip = value;
    break;
  case VMCSField::HOST_RSP:
    vmcs.host_rsp = value;
    break;
  case VMCSField::PIN_BASED_VM_EXEC_CONTROL:
    vmcs.pin_based_vm_exec_control = static_cast<uint32_t>(value);
    break;
  case VMCSField::CPU_BASED_VM_EXEC_CONTROL:
    vmcs.cpu_based_vm_exec_control = static_cast<uint32_t>(value);
    break;
  case VMCSField::EXCEPTION_BITMAP:
    vmcs.exception_bitmap = static_cast<uint32_t>(value);
    break;
  case VMCSField::VM_EXIT_CONTROLS:
    vmcs.vm_exit_controls = static_cast<uint32_t>(value);
    break;
  case VMCSField::VM_ENTRY_CONTROLS:
    vmcs.vm_entry_controls = static_cast<uint32_t>(value);
    break;
  default:
    spdlog::warn("X86_64CPU[{}]: VMWRITE with unsupported field 0x{:x}",
                 m_cpuId, static_cast<uint64_t>(vmcsField));
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::trace("X86_64CPU[{}]: VMWRITE field=0x{:x}, value=0x{:x}", m_cpuId,
                static_cast<uint64_t>(vmcsField), value);
}

void X86_64CPU::VMXOFF() {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  vmxEnabled = false;
  inVMXNonRootOperation = false;
  spdlog::info("X86_64CPU[{}]: VMXOFF executed", m_cpuId);
}

// VM Exit handling
void X86_64CPU::VMExit(uint32_t reason, uint64_t qualification) {
  if (!inVMXNonRootOperation) {
    spdlog::error(
        "X86_64CPU[{}]: VMExit called but not in VMX non-root operation",
        m_cpuId);
    return;
  }

  // Save guest state
  SaveGuestState();

  // Set VM exit information fields
  vmcs.vm_exit_reason = reason;
  vmcs.vm_exit_qualification = qualification;
  vmcs.guest_linear_address = 0;   // Set if needed
  vmcs.guest_physical_address = 0; // Set if needed

  // Load host state
  LoadHostState();

  inVMXNonRootOperation = false;
  spdlog::info("X86_64CPU[{}]: VM Exit, reason=0x{:x}, qualification=0x{:x}",
               m_cpuId, reason, qualification);
}

void X86_64CPU::SaveGuestState() {
  // Save general purpose registers
  vmcs.guest_rax = GetRegister(Register::RAX);
  vmcs.guest_rbx = GetRegister(Register::RBX);
  vmcs.guest_rcx = GetRegister(Register::RCX);
  vmcs.guest_rdx = GetRegister(Register::RDX);
  vmcs.guest_rsi = GetRegister(Register::RSI);
  vmcs.guest_rdi = GetRegister(Register::RDI);
  vmcs.guest_rbp = GetRegister(Register::RBP);
  vmcs.guest_rsp = GetRegister(Register::RSP);
  vmcs.guest_r8 = GetRegister(Register::R8);
  vmcs.guest_r9 = GetRegister(Register::R9);
  vmcs.guest_r10 = GetRegister(Register::R10);
  vmcs.guest_r11 = GetRegister(Register::R11);
  vmcs.guest_r12 = GetRegister(Register::R12);
  vmcs.guest_r13 = GetRegister(Register::R13);
  vmcs.guest_r14 = GetRegister(Register::R14);
  vmcs.guest_r15 = GetRegister(Register::R15);
  vmcs.guest_rip = GetRegister(Register::RIP);
  vmcs.guest_rflags = GetRflags();

  // Save control registers
  vmcs.guest_cr0 = GetCR0();
  vmcs.guest_cr3 = GetCR3();
  vmcs.guest_cr4 = GetCR4();
  vmcs.guest_dr7 = GetDR7();

  // Save segment registers (simplified)
  vmcs.guest_cs_selector = GetSegmentRegister(SegmentRegister::CS);
  vmcs.guest_ds_selector = GetSegmentRegister(SegmentRegister::DS);
  vmcs.guest_es_selector = GetSegmentRegister(SegmentRegister::ES);
  vmcs.guest_fs_selector = GetSegmentRegister(SegmentRegister::FS);
  vmcs.guest_gs_selector = GetSegmentRegister(SegmentRegister::GS);
  vmcs.guest_ss_selector = GetSegmentRegister(SegmentRegister::SS);
}

void X86_64CPU::LoadHostState() {
  // Load control registers
  SetCR0(vmcs.host_cr0);
  SetCR3(vmcs.host_cr3);
  SetCR4(vmcs.host_cr4);

  // Load segment registers (simplified)
  SetSegmentRegister(SegmentRegister::CS, vmcs.host_cs_selector);
  SetSegmentRegister(SegmentRegister::DS, vmcs.host_ds_selector);
  SetSegmentRegister(SegmentRegister::ES, vmcs.host_es_selector);
  SetSegmentRegister(SegmentRegister::FS, vmcs.host_fs_selector);
  SetSegmentRegister(SegmentRegister::GS, vmcs.host_gs_selector);
  SetSegmentRegister(SegmentRegister::SS, vmcs.host_ss_selector);

  // Load RIP and RSP
  SetRegister(Register::RIP, vmcs.host_rip);
  SetRegister(Register::RSP, vmcs.host_rsp);
}

bool X86_64CPU::CheckVMExitConditions(const DecodedInstruction &instr) {
  if (!inVMXNonRootOperation) {
    return false;
  }

  // Check if instruction causes VM exit based on VMCS configuration
  switch (instr.instType) {
  case InstructionType::Cpuid:
    // CPUID always causes VM exit in VMX non-root operation
    VMExit(static_cast<uint32_t>(VMExitReason::CPUID), 0);
    return true;

  case InstructionType::Invd:
    // INVD always causes VM exit
    VMExit(static_cast<uint32_t>(VMExitReason::INVD), 0);
    return true;

  case InstructionType::Vmcall:
    // VMCALL always causes VM exit
    VMExit(static_cast<uint32_t>(VMExitReason::VMCALL), 0);
    return true;

  case InstructionType::Hlt:
    // Check if HLT exiting is enabled
    if (vmcs.cpu_based_vm_exec_control & (1 << 7)) {
      VMExit(static_cast<uint32_t>(VMExitReason::HLT), 0);
      return true;
    }
    break;

  case InstructionType::In:
  case InstructionType::Out:
  case InstructionType::Ins:
  case InstructionType::Outs:
    // Check if I/O instruction exiting is enabled
    if (vmcs.cpu_based_vm_exec_control & (1 << 24)) {
      uint64_t qualification = 0;
      // Set qualification based on instruction details
      VMExit(static_cast<uint32_t>(VMExitReason::IO_INSTRUCTION),
             qualification);
      return true;
    }
    break;

  case InstructionType::Rdmsr:
  case InstructionType::Wrmsr:
    // Check if MSR access causes VM exit
    if (vmcs.cpu_based_vm_exec_control & (1 << 28)) {
      uint32_t reason = (instr.instType == InstructionType::Rdmsr)
                            ? static_cast<uint32_t>(VMExitReason::RDMSR)
                            : static_cast<uint32_t>(VMExitReason::WRMSR);
      VMExit(reason, 0);
      return true;
    }
    break;

  default:
    break;
  }

  return false;
}

// ========================
// Advanced Power Management
// ========================

void X86_64CPU::CheckMWAITWakeup() {
  if (!mwaitState)
    return;

  // Check if monitored address has changed
  uint64_t currentValue;
  mmu.ReadVirtual(mwaitMonitorAddr, &currentValue, 8, GetProcessId());

  if (currentValue != mwaitSavedValue) {
    mwaitState = false;
    halted = false;
    spdlog::info("X86_64CPU[{}]: MWAIT wakeup triggered", m_cpuId);
  } else if (mwaitWakeupCounter++ > MWAIT_MAX_CYCLES) {
    mwaitState = false;
    halted = false;
    spdlog::warn("X86_64CPU[{}]: MWAIT timeout", m_cpuId);
  }
}

// ========================
// Cache Control
// ========================

void X86_64CPU::CLFLUSH(uint64_t addr) {
  // In a real implementation, this would flush a specific cache line.
  // Here we can invalidate the JIT cache for that region as a substitute.
  jit->InvalidateRange(addr, 64); // Invalidate a 64-byte cache line
  spdlog::trace("X86_64CPU[{}]: CLFLUSH for address 0x{:x}", m_cpuId, addr);
}

void X86_64CPU::PREFETCH(DecodedInstruction::Operand::Type hint,
                         uint64_t addr) {
  // This is a hint to the CPU and may be a NOP in emulation.
  // In a sophisticated cache model, this would move data closer to the CPU.
  spdlog::trace("X86_64CPU[{}]: PREFETCH hint received for address 0x{:x}",
                m_cpuId, addr);
}

// XMM Register Access Methods
__m256i X86_64CPU::GetXMMRegister(uint8_t reg) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= XMM_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid XMM register index: {}", m_cpuId,
                  reg);
    return _mm256_setzero_si256();
  }
  return xmmRegisters[reg];
}

void X86_64CPU::SetXMMRegister(uint8_t reg, const __m256i &value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= XMM_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid XMM register index: {}", m_cpuId,
                  reg);
    return;
  }
  xmmRegisters[reg] = value;
  spdlog::trace("X86_64CPU[{}]: Set XMM{} register", m_cpuId, reg);
}

uint16_t X86_64CPU::GetMaskRegister(uint8_t reg) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= K_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid mask register index: {}", m_cpuId,
                  reg);
    return 0;
  }
  return kRegisters[reg];
}

void X86_64CPU::SetMaskRegister(uint8_t reg, uint16_t value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= K_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid mask register index: {}", m_cpuId,
                  reg);
    return;
  }
  // K0 is always 0xFFFF (all bits set)
  if (reg == 0) {
    kRegisters[0] = 0xFFFF;
    spdlog::trace("X86_64CPU[{}]: K0 register is read-only (always 0xFFFF)",
                  m_cpuId);
  } else {
    kRegisters[reg] = value;
    spdlog::trace("X86_64CPU[{}]: Set K{} register to 0x{:x}", m_cpuId, reg,
                  value);
  }
}

// AVX-512 Instruction Execution
void X86_64CPU::ExecuteAVX512Instruction(const DecodedInstruction &instr,
                                         uint64_t &nextRip) {
  if (!g_cpuFeatures.avx512f) {
    spdlog::warn("X86_64CPU[{}]: AVX-512 instruction attempted but not "
                 "supported by host CPU: type={}",
                 m_cpuId, static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
    return;
  }

  // Ensure we have valid operands
  if (instr.operandCount < 1) {
    spdlog::error(
        "X86_64CPU[{}]: AVX-512 instruction with invalid operand count: {}",
        m_cpuId, instr.operandCount);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Get mask register if used (k1-k7)
  uint8_t maskReg = 0;
  bool useMask = false;
  bool zeroMasking = false;

  if (instr.isEvex && instr.evexInfo.valid) {
    maskReg = instr.evexInfo.aaa();
    useMask = maskReg > 0;
    zeroMasking = instr.evexInfo.z();
  }

  // Handle different AVX-512 instruction types
  switch (instr.instType) {
  // Vector arithmetic operations
  case InstructionType::Vaddpd:
  case InstructionType::Vaddps:
  case InstructionType::Vaddsd:
  case InstructionType::Vaddss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VADD with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m512 src1 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[1])));
    __m512 src2 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[2])));
    __m512 result;

    if (instr.instType == InstructionType::Vaddps) {
      result = _mm512_add_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vaddpd) {
      result = _mm512_castpd_ps(
          _mm512_add_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vaddsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_add_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vaddss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_add_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m512 dest = _mm512_castsi512_ps(
          _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[0])));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256i result256 = _mm256_castps_si256(_mm512_castps512_ps256(result));
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector subtraction operations
  case InstructionType::Vsubpd:
  case InstructionType::Vsubps:
  case InstructionType::Vsubsd:
  case InstructionType::Vsubss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VSUB with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m512 src1 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[1])));
    __m512 src2 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[2])));
    __m512 result;

    if (instr.instType == InstructionType::Vsubps) {
      result = _mm512_sub_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vsubpd) {
      result = _mm512_castpd_ps(
          _mm512_sub_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vsubsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_sub_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vsubss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_sub_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m512 dest = _mm512_castsi512_ps(
          _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[0])));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256i result256 = _mm256_castps_si256(_mm512_castps512_ps256(result));
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector multiplication operations
  case InstructionType::Vmulpd:
  case InstructionType::Vmulps:
  case InstructionType::Vmulsd:
  case InstructionType::Vmulss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VMUL with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m512 src1 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[1])));
    __m512 src2 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[2])));
    __m512 result;

    if (instr.instType == InstructionType::Vmulps) {
      result = _mm512_mul_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vmulpd) {
      result = _mm512_castpd_ps(
          _mm512_mul_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vmulsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_mul_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vmulss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_mul_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m512 dest = _mm512_castsi512_ps(
          _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[0])));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256i result256 = _mm256_castps_si256(_mm512_castps512_ps256(result));
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector division operations
  case InstructionType::Vdivpd:
  case InstructionType::Vdivps:
  case InstructionType::Vdivsd:
  case InstructionType::Vdivss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VDIV with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m512 src1 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[1])));
    __m512 src2 = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[2])));
    __m512 result;

    if (instr.instType == InstructionType::Vdivps) {
      result = _mm512_div_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vdivpd) {
      result = _mm512_castpd_ps(
          _mm512_div_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vdivsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_div_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vdivss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_div_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m512 dest = _mm512_castsi512_ps(
          _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[0])));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256i result256 = _mm256_castps_si256(_mm512_castps512_ps256(result));
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector square root operations
  case InstructionType::Vsqrtpd:
  case InstructionType::Vsqrtps:
  case InstructionType::Vsqrtsd:
  case InstructionType::Vsqrtss: {
    if (instr.operandCount < 2) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VSQRT with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m512 src = _mm512_castsi512_ps(
        _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[1])));
    __m512 result;

    if (instr.instType == InstructionType::Vsqrtps) {
      result = _mm512_sqrt_ps(src);
    } else if (instr.instType == InstructionType::Vsqrtpd) {
      result = _mm512_castpd_ps(_mm512_sqrt_pd(_mm512_castps_pd(src)));
    } else if (instr.instType == InstructionType::Vsqrtsd) {
      // Scalar double operation
      __m128d scalar_src = _mm_castps_pd(_mm512_castps512_ps128(src));
      __m128d scalar_result = _mm_sqrt_sd(scalar_src, scalar_src);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vsqrtss
      // Scalar single operation
      __m128 scalar_src = _mm512_castps512_ps128(src);
      __m128 scalar_result = _mm_sqrt_ss(scalar_src);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m512 dest = _mm512_castsi512_ps(
          _mm512_castsi256_si512(ReadXmmOperandValue(instr.operands[0])));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256i result256 = _mm256_castps_si256(_mm512_castps512_ps256(result));
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled AVX-512 instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }

  // Update RIP
  nextRip = GetRegister(Register::RIP) + instr.length;
}

void X86_64CPU::SaveSMMState() {
  // Initialize SMM state save area
  smmSavedState = {};
  smmSavedState.smm_revision_id = SMM_REVISION_ID;
  smmSavedState.smbase = smbase;
  smmSavedState.smm_state_save_completion_flag = 0;

  // Save general purpose registers
  smmSavedState.rax = GetRegister(Register::RAX);
  smmSavedState.rcx = GetRegister(Register::RCX);
  smmSavedState.rdx = GetRegister(Register::RDX);
  smmSavedState.rbx = GetRegister(Register::RBX);
  smmSavedState.rsp = GetRegister(Register::RSP);
  smmSavedState.rbp = GetRegister(Register::RBP);
  smmSavedState.rsi = GetRegister(Register::RSI);
  smmSavedState.rdi = GetRegister(Register::RDI);
  smmSavedState.r8 = GetRegister(Register::R8);
  smmSavedState.r9 = GetRegister(Register::R9);
  smmSavedState.r10 = GetRegister(Register::R10);
  smmSavedState.r11 = GetRegister(Register::R11);
  smmSavedState.r12 = GetRegister(Register::R12);
  smmSavedState.r13 = GetRegister(Register::R13);
  smmSavedState.r14 = GetRegister(Register::R14);
  smmSavedState.r15 = GetRegister(Register::R15);

  // Save RIP and RFLAGS
  smmSavedState.rip = GetRegister(Register::RIP);
  smmSavedState.rflags = GetRflags();

  // Save control registers
  smmSavedState.cr0 = GetCR0();
  smmSavedState.cr3 = GetCR3();
  smmSavedState.cr4 = GetCR4();
  smmSavedState.dr6 = GetDR6();
  smmSavedState.dr7 = GetDR7();

  // Save segment registers (simplified - in real implementation would save
  // hidden parts too)
  smmSavedState.cs_selector = GetSegmentRegister(SegmentRegister::CS);
  smmSavedState.ds_selector = GetSegmentRegister(SegmentRegister::DS);
  smmSavedState.es_selector = GetSegmentRegister(SegmentRegister::ES);
  smmSavedState.fs_selector = GetSegmentRegister(SegmentRegister::FS);
  smmSavedState.gs_selector = GetSegmentRegister(SegmentRegister::GS);
  smmSavedState.ss_selector = GetSegmentRegister(SegmentRegister::SS);

  // Save FPU state (simplified)
  memcpy(smmSavedState.fpu_state, &fpuState, sizeof(fpuState));

  // Save SMM statistics
  smmSavedState.smm_entry_count = smmEntryCount;
  smmSavedState.last_smm_entry_time =
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          smmEntryTime.time_since_epoch())
          .count();

  // Set completion flag
  smmSavedState.smm_state_save_completion_flag = 1;

  spdlog::debug("X86_64CPU[{}]: Saved CPU state to SMM saved state area",
                m_cpuId);
}

void X86_64CPU::RestoreSMMState() {
  // Restore general purpose registers
  SetRegister(Register::RAX, smmSavedState.rax);
  SetRegister(Register::RCX, smmSavedState.rcx);
  SetRegister(Register::RDX, smmSavedState.rdx);
  SetRegister(Register::RBX, smmSavedState.rbx);
  SetRegister(Register::RSP, smmSavedState.rsp);
  SetRegister(Register::RBP, smmSavedState.rbp);
  SetRegister(Register::RSI, smmSavedState.rsi);
  SetRegister(Register::RDI, smmSavedState.rdi);
  SetRegister(Register::R8, smmSavedState.r8);
  SetRegister(Register::R9, smmSavedState.r9);
  SetRegister(Register::R10, smmSavedState.r10);
  SetRegister(Register::R11, smmSavedState.r11);
  SetRegister(Register::R12, smmSavedState.r12);
  SetRegister(Register::R13, smmSavedState.r13);
  SetRegister(Register::R14, smmSavedState.r14);
  SetRegister(Register::R15, smmSavedState.r15);

  // Restore RIP and RFLAGS
  SetRegister(Register::RIP, smmSavedState.rip);
  SetRflags(smmSavedState.rflags);

  // Restore control registers
  SetCR0(smmSavedState.cr0);
  SetCR3(smmSavedState.cr3);
  SetCR4(smmSavedState.cr4);
  SetDR6(smmSavedState.dr6);
  SetDR7(smmSavedState.dr7);

  // Restore segment registers
  SetSegmentRegister(SegmentRegister::CS, smmSavedState.cs_selector);
  SetSegmentRegister(SegmentRegister::DS, smmSavedState.ds_selector);
  SetSegmentRegister(SegmentRegister::ES, smmSavedState.es_selector);
  SetSegmentRegister(SegmentRegister::FS, smmSavedState.fs_selector);
  SetSegmentRegister(SegmentRegister::GS, smmSavedState.gs_selector);
  SetSegmentRegister(SegmentRegister::SS, smmSavedState.ss_selector);

  // Restore FPU state (simplified)
  memcpy(&fpuState, smmSavedState.fpu_state, sizeof(fpuState));

  spdlog::debug("X86_64CPU[{}]: Restored CPU state from SMM saved state area",
                m_cpuId);
}

void X86_64CPU::WriteSMMStateToMemory() {
  // Calculate SMRAM state save area address
  uint64_t stateSaveArea = smbase + SMM_STATE_SAVE_OFFSET;

  // Write SMM state to SMRAM
  mmu.WriteVirtual(stateSaveArea, &smmSavedState, sizeof(SMMSavedState),
                   GetProcessId());

  spdlog::debug("X86_64CPU[{}]: Wrote SMM state to SMRAM at 0x{:x}", m_cpuId,
                stateSaveArea);
}

void X86_64CPU::ReadSMMStateFromMemory() {
  // Calculate SMRAM state save area address
  uint64_t stateSaveArea = smbase + SMM_STATE_SAVE_OFFSET;

  // Read SMM state from SMRAM
  mmu.ReadVirtual(stateSaveArea, &smmSavedState, sizeof(SMMSavedState),
                  GetProcessId());

  // Update SMM control flags
  smmAutoHaltRestart = smmSavedState.auto_halt_restart != 0;
  smmIoRestart = smmSavedState.io_restart_flag != 0;

  spdlog::debug("X86_64CPU[{}]: Read SMM state from SMRAM at 0x{:x}", m_cpuId,
                stateSaveArea);
}

void X86_64CPU::SetSMBASE(uint64_t newBase) {
  if (inSMM) {
    // SMBASE can only be changed from within SMM
    smbase = newBase;
    smmSavedState.smbase = newBase;
    spdlog::info("X86_64CPU[{}]: SMBASE updated to 0x{:x}", m_cpuId, newBase);
  } else {
    spdlog::warn("X86_64CPU[{}]: Attempted to change SMBASE outside of SMM",
                 m_cpuId);
  }
}

uint64_t X86_64CPU::GetSMBASE() const { return smbase; }

bool X86_64CPU::IsSMMActive() const { return inSMM; }

void X86_64CPU::TriggerSMI(SMMInterruptType type) {
  spdlog::info("X86_64CPU[{}]: SMI triggered, type=0x{:x}", m_cpuId,
               static_cast<uint8_t>(type));
  EnterSMM(type);
}

// RSM (Resume from System Management Mode) instruction
void X86_64CPU::RSM() {
  if (!inSMM) {
    // RSM is only valid in SMM
    spdlog::warn("X86_64CPU[{}]: RSM instruction executed outside of SMM",
                 m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false); // Undefined opcode exception
    return;
  }

  spdlog::info("X86_64CPU[{}]: RSM instruction executed, exiting SMM", m_cpuId);
  ExitSMM();
}

} // namespace x86_64
