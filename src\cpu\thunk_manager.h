// Copyright 2025 <Copyright Owner>

#pragma once

#include <any>
#include <cstdint>
#include <functional>
#include <shared_mutex>
#include <string>
#include <tuple>
#include <type_traits>
#include <unordered_map>
#include <utility>
#include <variant>
#include <vector>

namespace ps4 {
enum class ThunkError { Success, InvalidId, Inactive, InvalidArgs, Exception, TypeMismatch };

// Type-safe argument storage using std::any
using ThunkArgument = std::any;
using ThunkArgumentList = std::vector<ThunkArgument>;

class ThunkManager {
public:
  using ThunkFunction = std::function<uint64_t(const ThunkArgumentList&)>;

  ThunkManager();
  ~ThunkManager();

  bool Initialize();
  void Shutdown();

  template <typename Ret, typename... Args>
  uint64_t RegisterThunk(const std::string &name,
                         std::function<Ret(Args...)> func) {
    auto thunkFunc = [this, func](const ThunkArgumentList& args) -> uint64_t {
      if (args.size() != sizeof...(Args)) {
        return static_cast<uint64_t>(-1);
      }

      try {
        std::tuple<Args...> tuple;
        if (!unpackArgsSafe(args, tuple, std::index_sequence_for<Args...>{})) {
          return static_cast<uint64_t>(-1);
        }

        if constexpr (std::is_void_v<Ret>) {
          std::apply(func, tuple);
          return 0;
        } else if constexpr (std::is_integral_v<Ret> || std::is_pointer_v<Ret>) {
          return static_cast<uint64_t>(std::apply(func, tuple));
        } else {
          // For non-integral return types, store in a static location and return pointer
          static thread_local Ret result;
          result = std::apply(func, tuple);
          return reinterpret_cast<uint64_t>(&result);
        }
      } catch (const std::bad_any_cast& e) {
        return static_cast<uint64_t>(-1);
      }
    };
    return RegisterThunkImpl(name, thunkFunc, sizeof...(Args));
  }

  bool UnregisterThunk(uint64_t thunkId);

  std::pair<uint64_t, ThunkError> CallThunk(uint64_t thunkId, const ThunkArgumentList& args);

  // Legacy interface for backward compatibility
  std::pair<uint64_t, ThunkError> CallThunk(uint64_t thunkId, uint64_t *args,
                                            uint32_t argCount);

  uint64_t GetThunkId(const std::string &name) const;
  std::string GetThunkName(uint64_t thunkId) const;
  std::vector<std::string> ListThunks() const;

  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);

private:
  struct ThunkEntry {
    std::string name;
    ThunkFunction function;
    bool active;
    uint32_t refCount;
    uint32_t expectedArgs;
  };

  uint64_t RegisterThunkImpl(const std::string &name, ThunkFunction function,
                             uint32_t expectedArgs);
  uint64_t GenerateThunkId();

  // Type-safe argument unpacking using std::any
  template <typename Tuple, size_t... Is>
  bool unpackArgsSafe(const ThunkArgumentList& args, Tuple &tuple, std::index_sequence<Is...>) {
    try {
      return (unpackSingleArg<Is>(args, tuple) && ...);
    } catch (const std::bad_any_cast&) {
      return false;
    }
  }

  template <size_t I, typename Tuple>
  bool unpackSingleArg(const ThunkArgumentList& args, Tuple& tuple) {
    using ArgType = std::tuple_element_t<I, Tuple>;

    if (I >= args.size()) {
      return false;
    }

    try {
      if constexpr (std::is_integral_v<ArgType>) {
        // Handle integral types - try multiple conversions
        if (args[I].type() == typeid(uint64_t)) {
          std::get<I>(tuple) = static_cast<ArgType>(std::any_cast<uint64_t>(args[I]));
        } else if (args[I].type() == typeid(int64_t)) {
          std::get<I>(tuple) = static_cast<ArgType>(std::any_cast<int64_t>(args[I]));
        } else if (args[I].type() == typeid(uint32_t)) {
          std::get<I>(tuple) = static_cast<ArgType>(std::any_cast<uint32_t>(args[I]));
        } else if (args[I].type() == typeid(int32_t)) {
          std::get<I>(tuple) = static_cast<ArgType>(std::any_cast<int32_t>(args[I]));
        } else {
          return false;
        }
      } else if constexpr (std::is_pointer_v<ArgType>) {
        // Handle pointer types
        if (args[I].type() == typeid(uint64_t)) {
          std::get<I>(tuple) = reinterpret_cast<ArgType>(std::any_cast<uint64_t>(args[I]));
        } else if (args[I].type() == typeid(void*)) {
          std::get<I>(tuple) = static_cast<ArgType>(std::any_cast<void*>(args[I]));
        } else {
          return false;
        }
      } else {
        // Direct type match required for non-integral, non-pointer types
        std::get<I>(tuple) = std::any_cast<ArgType>(args[I]);
      }
      return true;
    } catch (const std::bad_any_cast&) {
      return false;
    }
  }

  std::vector<ThunkEntry> m_thunks;
  std::unordered_map<std::string, uint64_t> m_thunkNameMap;
  mutable std::shared_mutex m_thunkMutex;
  uint64_t m_nextThunkId;

  std::unordered_map<uint64_t, std::pair<uint64_t, uint64_t>>
      m_stats; // Stores call count and total cycles
};
} // namespace ps4