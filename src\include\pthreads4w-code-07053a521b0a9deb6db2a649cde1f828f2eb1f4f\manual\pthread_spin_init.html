<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SPIN_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_spin_destroy, pthread_spin_init - destroy or initialize a
spin lock object (<B>ADVANCED REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>pthread_spinlock_t</B> <I>lock</I> <B>=
PTHREAD_SPINLOCK_INITIALIZER;</B></P>
<P><B>int pthread_spin_destroy(pthread_spinlock_t *</B><I>lock</I><B>);
<BR>int pthread_spin_init(pthread_spinlock_t *</B><I>lock</I><B>, int</B>
<I>pshared</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_spin_destroy</B> function shall destroy the spin
lock referenced by <I>lock</I> and release any resources used by the
lock. The effect of subsequent use of the lock is undefined until the
lock is reinitialized by another call to <B>pthread_spin_init</B> .
The results are undefined if <B>pthread_spin_destroy</B> is called
when a thread holds the lock, or if this function is called with an
uninitialized thread spin lock. 
</P>
<P>The <B>pthread_spin_init</B> function shall allocate any resources
required to use the spin lock referenced by <I>lock</I> and
initialize the lock to an unlocked state. 
</P>
<P><B>PThreads4W</B> supports single and multiple processor systems
as well as process CPU affinity masking by checking the mask when the
spin lock is initialized. If the process is using only a single
processor at the time <B>pthread_spin_init</B> is called then the
spin lock is initialized as a PTHREAD_MUTEX_NORMAL mutex object. A
thread that calls <A HREF="pthread_spin_lock.html"><B>pthread_spin_lock(3)</B></A>
will block rather than spin in this case. If the process CPU affinity
mask is altered after the spin lock has been initialised, the spin
lock is not modified, and may no longer be optimal for the number of
CPUs available.</P>
<P><B>PThreads4W</B> defines <B>_POSIX_THREAD_PROCESS_SHARED</B> in
pthread.h as -1 to indicate that these routines do not support the
<B>PTHREAD_PROCESS_SHARED</B> attribute. <B>pthread_spin_init</B>
will return the error <B>ENOTSUP</B> if the value of <I>pshared</I>
is not <B>PTHREAD_PROCESS_PRIVATE</B>.</P>
<P>The results are undefined if <B>pthread_spin_init</B> is called
specifying an already initialized spin lock. The results are
undefined if a spin lock is used without first being initialized. 
</P>
<P>If the <B>pthread_spin_init</B> function fails, the lock is not
initialized and the contents of <I>lock</I> are undefined. 
</P>
<P>Only the object referenced by <I>lock</I> may be used for
performing synchronization. 
</P>
<P>The result of referring to copies of that object in calls to
<B>pthread_spin_destroy</B> , <A HREF="pthread_spin_lock.html"><B>pthread_spin_lock</B>(3)</A>
, <A HREF="pthread_spin_lock.html"><B>pthread_spin_trylock</B>(3)</A>,
or <A HREF="pthread_spin_unlock.html"><B>pthread_spin_unlock</B>(3)</A>
is undefined. 
</P>
<P><B>PThreads4W</B> supports statically initialized spin locks
using <B>PTHREAD_SPINLOCK_INITIALIZER</B>. An application should
still call <B>pthread_spin_destroy</B> at some point to ensure that
any resources consumed by the spin lock are released.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, these functions shall return zero;
otherwise, an error number shall be returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>These functions may fail if: 
</P>
<DL>
	<DT><B>EBUSY</B> 
	</DT><DD>
	The implementation has detected an attempt to initialize or destroy
	a spin lock while it is in use (for example, while being used in a
	<A HREF="pthread_spin_lock.html"><B>pthread_spin_lock</B>(3)</A>
	call) by another thread. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>lock</I> is invalid. 
	</DD></DL>
<P>
The <B>pthread_spin_init</B> function shall fail if: 
</P>
<DL>
	<DT><B>ENOTSUP</B> 
	</DT><DD>
	The value of <I>pshared</I> is not <B>PTHREAD_PROCESS_PRIVATE</B>.</DD><DT>
	<B>ENOMEM</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	Insufficient memory exists to initialize the lock. 
	</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>The <B>pthread_spin_destroy</B> and <B>pthread_spin_init</B>
functions are part of the Spin Locks option and need not be provided
on all implementations. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_spin_lock.html"><B>pthread_spin_lock</B>(3)</A> <B>,</B>
<A HREF="pthread_spin_unlock.html"><B>pthread_spin_unlock</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
