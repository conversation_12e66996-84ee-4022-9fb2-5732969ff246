<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_DELAY_NP(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: medium">pthread_delay_np – suspend the
thread for a specified period</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_delay_np (const struct timespec *</B>interval<B>);</B></P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_delay_np</B> causes a thread to delay execution for a
specific period of time. This period ends at the current time plus
the specified interval. The routine will not return before the end of
the period is reached, but may return an arbitrary amount of time
after the period has gone by. This can be due to system load, thread
priorities, and system timer granularity.</P>
<P>Specifying an interval of zero (0) seconds and zero (0)
nanoseconds is allowed and can be used to force the thread to give up
the processor or to deliver a pending cancellation request.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P><B>pthread_delay_np </B>is a cancellation point.</P>
<H2><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P>If an error condition occurs, <B>pthread_delay_np</B> returns an
integer value indicating the type of error.</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>The <B>pthread_delay_np</B> function returns the following error
code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT></DL>
</DL>
<P STYLE="margin-left: 2cm">
The value specified by interval is invalid.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>
