#pragma once

#include "crypto_utils.h"
#include <cstdint>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

class KeyStore {
public:
  KeyStore();
  ~KeyStore();

  // Initialize the keystore with a master key for decrypting the key file
  bool Initialize(const std::string& master_key, const std::string& key_file_path = "keys.json");

  // Get a key by type and index
  std::vector<uint8_t> GetKey(uint32_t key_type, uint32_t key_index) const;
  std::vector<uint8_t> GetIV(uint32_t key_type, uint32_t iv_index) const;

  // Check if the keystore is initialized
  bool IsInitialized() const { return initialized_; }

  // Get the last error message
  const std::string& GetLastError() const { return last_error_; }

private:
  // Load and decrypt the key file
  bool LoadKeyFile(const std::string& key_file_path, const std::vector<uint8_t>& master_key);

  // Parse JSON key data
  bool ParseKeyData(const std::vector<uint8_t>& key_data);

  // Set error message
  void SetError(const std::string& error);

  bool initialized_;
  std::string last_error_;

  // Key storage: map of (key_type, index) -> key/IV
  std::unordered_map<uint32_t, std::unordered_map<uint32_t, std::vector<uint8_t>>> keys_;
  std::unordered_map<uint32_t, std::unordered_map<uint32_t, std::vector<uint8_t>>> ivs_;
};

} // namespace ps4