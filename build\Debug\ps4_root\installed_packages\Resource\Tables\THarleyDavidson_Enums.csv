RSID_THARLEY_START, 3400,,,
 ,[Offset], <PERSON><PERSON>_FLYER, 0,
 ,[Offset], <PERSON>\PBTHarley, 1,
 ,[Offset], <PERSON>\iOS and Android\InstructionsENG, 2,
 ,[Offset], <PERSON>\iOS and Android\InstructionsFR, 3,
 ,[Offset], <PERSON>\iOS and Android\InstructionsITAL, 4,
 ,[Offset], <PERSON>\iOS and Android\InstructionsGERM, 5,
 ,[Offset], Harley\iOS and Android\InstructionsSPAN, 6,
 ,[Offset], <PERSON>\iOS and Android\InstructionsENG, 7,
 ,[Offset], <PERSON>\iOS and Android\InstructionsENG, 8,
 ,[Offset], tables\Harley_BG_Scroll, 9,
RSID_THARLEY_LIGHTS, 3401,,,
RSID_THARLEY_CAMERAS, 3402,,,
RSID_THARLEY_LAMP_TEXTURES, 3403,,,
 ,[Offset], L_001_Off, 0,
 ,[Offset], L_001_On, 1,
 ,[Offset], L_002_Off, 2,
 ,[Offset], L_002_On, 3,
 ,[Offset], L_003_Off, 4,
 ,[Offset], L_003_On, 5,
 ,[Offset], L_004_Off, 6,
 ,[Offset], L_004_On, 7,
 ,[Offset], L_005_Off, 8,
 ,[Offset], L_005_On, 9,
 ,[Offset], L_006_Off, 10,
 ,[Offset], L_006_On, 11,
 ,[Offset], L_007_Off, 12,
 ,[Offset], L_007_On_Red, 13,
 ,[Offset], L_007_On_Red, 14,
 ,[Offset], L_007_On_Green, 15,
 ,[Offset], L_009_Off, 16,
 ,[Offset], L_009_On, 17,
 ,[Offset], L_010_Off, 18,
 ,[Offset], L_010_On, 19,
 ,[Offset], L_011_Off, 20,
 ,[Offset], L_011_On, 21,
 ,[Offset], L_012_Off, 22,
 ,[Offset], L_012_On, 23,
 ,[Offset], L_013_Off, 24,
 ,[Offset], L_013_On, 25,
 ,[Offset], L_014_Off, 26,
 ,[Offset], L_014_On, 27,
 ,[Offset], L_015_Off, 28,
 ,[Offset], L_015_On, 29,
 ,[Offset], L_016_Off, 30,
 ,[Offset], L_016_On, 31,
 ,[Offset], L_017_Off, 32,
 ,[Offset], L_017_On, 33,
 ,[Offset], L_018_Off, 34,
 ,[Offset], L_018_On, 35,
 ,[Offset], L_019_Off, 36,
 ,[Offset], L_019_On, 37,
 ,[Offset], L_020_Off, 38,
 ,[Offset], L_020_On, 39,
 ,[Offset], L_021_Off, 40,
 ,[Offset], L_021_On, 41,
 ,[Offset], L_022_Off, 42,
 ,[Offset], L_022_On, 43,
 ,[Offset], L_023_Off, 44,
 ,[Offset], L_023_On, 45,
 ,[Offset], L_024_Off, 46,
 ,[Offset], L_024_On, 47,
 ,[Offset], L_025_Off, 48,
 ,[Offset], L_025_On, 49,
 ,[Offset], L_026_Off, 50,
 ,[Offset], L_026_On, 51,
 ,[Offset], L_027_Off, 52,
 ,[Offset], L_027_On, 53,
 ,[Offset], L_028_Off, 54,
 ,[Offset], L_028_On, 55,
 ,[Offset], L_029_Off, 56,
 ,[Offset], L_029_On, 57,
 ,[Offset], L_030_Off, 58,
 ,[Offset], L_030_On, 59,
 ,[Offset], L_031_Off, 60,
 ,[Offset], L_031_On, 61,
 ,[Offset], L_032_Off, 62,
 ,[Offset], L_032_On, 63,
 ,[Offset], L_033_Off, 64,
 ,[Offset], L_033_On, 65,
 ,[Offset], L_034_Off, 66,
 ,[Offset], L_034_On, 67,
 ,[Offset], L_035_Off, 68,
 ,[Offset], L_035_On, 69,
 ,[Offset], L_036_Off, 70,
 ,[Offset], L_036_On, 71,
 ,[Offset], L_037_Off, 72,
 ,[Offset], L_037_On, 73,
 ,[Offset], L_038_Off, 74,
 ,[Offset], L_038_On, 75,
 ,[Offset], L_039_Off, 76,
 ,[Offset], L_039_On, 77,
 ,[Offset], L_040_Off, 78,
 ,[Offset], L_040_On, 79,
 ,[Offset], L_041_Off, 80,
 ,[Offset], L_041_On, 81,
 ,[Offset], L_042_Off, 82,
 ,[Offset], L_042_On, 83,
 ,[Offset], L_043_Off, 84,
 ,[Offset], L_043_On, 85,
 ,[Offset], L_044_Off, 86,
 ,[Offset], L_044_On, 87,
 ,[Offset], L_045_Off, 88,
 ,[Offset], L_045_On, 89,
 ,[Offset], L_046_Off, 90,
 ,[Offset], L_046_On, 91,
 ,[Offset], L_046_On, 92,
 ,[Offset], L_046_On, 93,
 ,[Offset], L_048_Off, 94,
 ,[Offset], L_048_On, 95,
 ,[Offset], L_049_Off, 96,
 ,[Offset], L_049_On, 97,
 ,[Offset], L_050_Off, 98,
 ,[Offset], L_050_On, 99,
 ,[Offset], L_051_Off, 100,
 ,[Offset], L_051_On, 101,
 ,[Offset], L_057_Off, 102,
 ,[Offset], L_057_On, 103,
 ,[Offset], L_058_Off, 104,
 ,[Offset], L_058_On, 105,
 ,[Offset], L_059_Off, 106,
 ,[Offset], L_059_On, 107,
 ,[Offset], L_065_Off, 108,
 ,[Offset], L_065_On, 109,
 ,[Offset], L_066_Off, 110,
 ,[Offset], L_066_On, 111,
 ,[Offset], L_067_Off, 112,
 ,[Offset], L_067_On, 113,
 ,[Offset], L_068_Off, 114,
 ,[Offset], L_068_On, 115,
 ,[Offset], L_069_Off, 116,
 ,[Offset], L_069_On, 117,
 ,[Offset], L_070_Off, 118,
 ,[Offset], L_070_On, 119,
 ,[Offset], L_071_Off, 120,
 ,[Offset], L_071_On, 121,
 ,[Offset], L_072_Off, 122,
 ,[Offset], L_072_On, 123,
 ,[Offset], L_070B_Off, 124,
 ,[Offset], L_070A_Off, 125,
 ,[Offset], F_F1_Off, 126,
 ,[Offset], F_F1_On, 127,
 ,[Offset], F_F2_Off, 128,
 ,[Offset], F_F2_On, 129,
 ,[Offset], F_F3A_Off, 130,
 ,[Offset], F_F3A_On, 131,
 ,[Offset], F_F3B_Off, 132,
 ,[Offset], F_F3B_On, 133,
 ,[Offset], F_F4_Off, 134,
 ,[Offset], F_F4_On, 135,
 ,[Offset], F_F5_Off, 136,
 ,[Offset], F_F5_On, 137,
 ,[Offset], F_F6_Off, 138,
 ,[Offset], F_F6_On, 139,
 ,[Offset], F_F7_Off, 140,
 ,[Offset], F_F7_On, 141,
 ,[Offset], F_F7_On, 142,
 ,[Offset], F_F8_On, 143,
 ,[Offset], F_20_Off, 144,
 ,[Offset], F_20_On, 145,
 ,[Offset], Stopper, 146,
 ,[Offset], Stopper_On, 147,
RSID_THARLEY_TEXTURES, 3404,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], Harleydroptarget, 1,
 ,[Offset], Metal_Temp, 2,
 ,[Offset], Rubber Post_Temp, 3,
 ,[Offset], rubberband_Temp, 4,
 ,[Offset], Left_Flipper, 5,
 ,[Offset], Stopper, 6,
 ,[Offset], Apron, 7,
 ,[Offset], Flipper_Button, 8,
 ,[Offset], Box_Textures, 9,
 ,[Offset], Black_Grain, 10,
 ,[Offset], cabfront, 11,
 ,[Offset], backglass, 12,
 ,[Offset], Cabinet_Metals, 13,
 ,[Offset], Plastic_Pieces_Map, 14,
 ,[Offset], Plastic_Pieces_Flag, 15,
 ,[Offset], Plastic_Pieces_Lightning, 16,
 ,[Offset], Plastic_Pieces_Slingshot, 17,
 ,[Offset], Harley_Habit_Trail, 18,
 ,[Offset], BBike_Chrome, 19,
 ,[Offset], BBike_Color, 20,
 ,[Offset], Green_Light, 21,
 ,[Offset], Yellow_Light, 22,
 ,[Offset], RedLight, 23,
 ,[Offset], RedLight_s, 24,
 ,[Offset], Red_Light, 25,
 ,[Offset], StopLight, 26,
 ,[Offset], Harley_Spinner, 27,
 ,[Offset], SBike_Chrome_Parts, 28,
 ,[Offset], SBike_Grey, 29,
 ,[Offset], Sbike_Red, 30,
 ,[Offset], GlassTile, 31,
 ,[Offset], BBike_Color, 32,
 ,[Offset], Live_To_Ride, 33,
 ,[Offset], Flipper_Left, 34,
 ,[Offset], Flipper_Right, 35,
 ,[Offset], Right_Flipper, 36,
 ,[Offset], Bumper_Hamer, 37,
 ,[Offset], Bumper_Head, 38,
 ,[Offset], Bumper_Sensors, 39,
 ,[Offset], HabbiTrail_Chrome, 40,
 ,[Offset], Light_Piece_Eagle, 41,
 ,[Offset], Eagle_Plastic, 42,
 ,[Offset], Second_Eagle, 43,
 ,[Offset], Clear_Plastic, 44,
 ,[Offset], PLAYFIELD_Bottom, 45,
 ,[Offset], PLAYFIELD_Top, 46,
 ,[Offset], PLAYFIELD_Bottom_s, 47,
 ,[Offset], PLAYFIELD_Top_s, 48,
 ,[Offset], Metals, 49,
 ,[Offset], Metals_s, 50,
 ,[Offset], DropTarget, 51,
 ,[Offset], Harley_Gate, 52,
 ,[Offset], BumperTop , 53,
 ,[Offset], HarleyBumperBody, 54,
 ,[Offset], Small_Bikes_Texture_03-2, 55,
 ,[Offset], Small_Bikes_Texture_03, 56,
 ,[Offset], Small_Bikes_Texture_02, 57,
 ,[Offset], Small_Bikes_Texture_01, 58,
 ,[Offset], Main_Bike_Texture_03-3, 59,
 ,[Offset], Main_Bike_Texture_03-2, 60,
 ,[Offset], Main_Bike_Texture_03, 61,
 ,[Offset], Main_Bike_Texture_02, 62,
 ,[Offset], Main_Bike_Texture_01, 63,
 ,[Offset], Light2, 64,
 ,[Offset], Light1, 65,
 ,[Offset], Plastic_Ramp, 66,
 ,[Offset], Plastic_Ramp_s, 67,
 ,[Offset], bulb1, 68,
 ,[Offset], Metal_Wall, 69,
 ,[Offset], metal, 70,
 ,[Offset], metalplane, 71,
 ,[Offset], BlackMatte_Temp, 72,
 ,[Offset], TransparentRedPlastic_Temp, 73,
 ,[Offset], Silver Metal Screws_Temp, 74,
 ,[Offset], RedSquare, 75,
 ,[Offset], Plastic_Outlane, 76,
 ,[Offset], Ramp_Sticker, 77,
 ,[Offset], Ball_Launch_Off, 78,
 ,[Offset], Ball_Launch_On, 79,
 ,[Offset], Hole_Details, 80,
RSID_THARLEY_MODELS, 3405,,,
 ,[Offset], Playfield, 0,
 ,[Offset], cabinet, 1,
 ,[Offset], PlasticRamp, 2,
 ,[Offset], Plastics, 3,
 ,[Offset], Apron, 4,
 ,[Offset], RubberPosts, 5,
 ,[Offset], HabbiTrail, 6,
 ,[Offset], Rubbers, 7,
 ,[Offset], Targets, 8,
 ,[Offset], TrafficLight, 9,
 ,[Offset], Flipper_Left, 10,
 ,[Offset], Flipper_Right, 11,
 ,[Offset], Small_Bikes, 12,
 ,[Offset], DropTarget, 13,
 ,[Offset], Flasher, 14,
 ,[Offset], Harley, 15,
 ,[Offset], HarleyBrace, 16,
 ,[Offset], PopBumper, 17,
 ,[Offset], Stopper, 18,
 ,[Offset], WireA, 19,
 ,[Offset], PopBumper_Lamps, 20,
 ,[Offset], SlingshotLeft, 21,
 ,[Offset], SlingshotLeft_Extended, 22,
 ,[Offset], SlingshotRight, 23,
 ,[Offset], Lights, 24,
 ,[Offset], Live_to_Ride, 25,
 ,[Offset], metalpieces, 26,
 ,[Offset], Wooden_Rails, 27,
 ,[Offset], SlingshotRight_Extended, 28,
 ,[Offset], Spinner, 29,
 ,[Offset], cabinet_metals, 30,
 ,[Offset], Light_Cutouts, 31,
 ,[Offset], Spinner_Brace, 32,
 ,[Offset], GateA, 33,
 ,[Offset], GateB, 34,
 ,[Offset], GateC, 35,
 ,[Offset], Flash_Lamp_Cutouts, 36,
 ,[Offset], PopBumperMetal, 37,
 ,[Offset], Red_Bulb, 38,
 ,[Offset], Ramp_Stickers, 39,
 ,[Offset], Cabinet_Backglass, 40,
RSID_THARLEY_MODELS_LODS, 3406,,,
 ,[Offset], Playfield, 0,
 ,[Offset], cabinet, 1,
 ,[Offset], PlasticRamp, 2,
 ,[Offset], Plastics, 3,
 ,[Offset], Apron, 4,
 ,[Offset], RubberPosts, 5,
 ,[Offset], HabbiTrail, 6,
 ,[Offset], Rubbers, 7,
 ,[Offset], Targets, 8,
 ,[Offset], TrafficLight, 9,
 ,[Offset], Flipper_Left, 10,
 ,[Offset], Flipper_Right, 11,
 ,[Offset], Small_Bikes, 12,
 ,[Offset], DropTarget, 13,
 ,[Offset], Flasher, 14,
 ,[Offset], Harley, 15,
 ,[Offset], HarleyBrace, 16,
 ,[Offset], PopBumper, 17,
 ,[Offset], Stopper, 18,
 ,[Offset], WireA, 19,
 ,[Offset], PopBumper_Lamps, 20,
 ,[Offset], SlingshotLeft, 21,
 ,[Offset], SlingshotLeft_Extended, 22,
 ,[Offset], SlingshotRight, 23,
 ,[Offset], Lights, 24,
 ,[Offset], Live_to_Ride, 25,
 ,[Offset], metalpieces, 26,
 ,[Offset], Wooden_Rails, 27,
 ,[Offset], SlingshotRight_Extended, 28,
 ,[Offset], Spinner, 29,
 ,[Offset], cabinet_metals, 30,
 ,[Offset], Light_Cutouts, 31,
 ,[Offset], Spinner_Brace, 32,
 ,[Offset], GateA, 33,
 ,[Offset], GateB, 34,
 ,[Offset], GateC, 35,
 ,[Offset], Flash_Lamp_Cutouts, 36,
 ,[Offset], PopBumperMetal, 37,
 ,[Offset], Red_Bulb, 38,
 ,[Offset], Ramp_Stickers, 39,
 ,[Offset], Cabinet_Backglass, 40,
RSID_THARLEY_COLLISION, 3407,,,
 ,[Offset], Playfield_Col, 0,
 ,[Offset], InnerWalls_Col, 1,
 ,[Offset], OuterWall_Col, 2,
 ,[Offset], PlasticRamp_Col, 3,
 ,[Offset], HabbiTrail_Col, 4,
 ,[Offset], Bumpers_Col, 5,
 ,[Offset], DropTarget_Col, 6,
 ,[Offset], SlingShot_Col, 7,
 ,[Offset], SmallBikes_Col, 8,
 ,[Offset], BigBike_Col, 9,
 ,[Offset], Target_Col, 10,
 ,[Offset], Left_Flipper_Front_Col, 11,
 ,[Offset], Left_Flipper_Back_Col, 12,
 ,[Offset], Right_Flipper_Front_Col, 13,
 ,[Offset], Right_Flipper_Back_Col, 14,
 ,[Offset], Left_Slingshot_Front_Col, 15,
 ,[Offset], Right_Slingshot_Front_Col, 16,
 ,[Offset], Post_Col, 17,
 ,[Offset], Spinner_Col, 18,
 ,[Offset], Stopper_Col, 19,
 ,[Offset], Tunnel_Col, 20,
 ,[Offset], Apron_Col, 21,
 ,[Offset], BallTrap_A_Col, 22,
 ,[Offset], BallTrap_B_Col, 23,
 ,[Offset], Left_Flipper_Lane_Col, 24,
 ,[Offset], Left_Slingshot_Col, 25,
 ,[Offset], Right_Flipper_Lane_Col, 26,
 ,[Offset], Right_Slingshot_Col, 27,
 ,[Offset], Plunger_Col, 28,
 ,[Offset], GateA_Col, 29,
 ,[Offset], GateB_Col, 30,
 ,[Offset], GateC_Col, 31,
 ,[Offset], Ball_Drain_Col, 32,
 ,[Offset], Magnet_Col, 33,
 ,[Offset], LightSensor_Col, 34,
 ,[Offset], Harley_Cave_Col, 35,
 ,[Offset], Harley_Sensor_Col, 36,
 ,[Offset], BigBike_Front_Col, 37,
 ,[Offset], Plunger_Moving, 38,
 ,[Offset], Harley_Cave_Walls_Col, 39,
 ,[Offset], Metal_Hole_Col, 40,
RSID_THARLEY_PLACEMENT, 3408,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_THARLEY_EMUROM, 3409,,,
 ,[Offset], harley_400, 0,
 ,[Offset], harley_400, 1,
 ,[Offset], harley_400, 2,
RSID_TABLE_HARLEY_SOUNDS_START, 3410,,,
RSID_TABLE_HARLEY_EMU_SOUNDS, 3411,,,
 ,[Offset], SFD02, 0,
 ,[Offset], SFD03-LP1, 1,
 ,[Offset], SFD03-LP2, 2,
 ,[Offset], SFD04-LP1, 3,
 ,[Offset], SFD04-LP2, 4,
 ,[Offset], SFD05, 5,
 ,[Offset], SFD06-LP1, 6,
 ,[Offset], SFD06-LP2, 7,
 ,[Offset], SFD07, 8,
 ,[Offset], SFD08, 9,
 ,[Offset], SFD09, 10,
 ,[Offset], SFD0A-LP1, 11,
 ,[Offset], SFD0A-LP2, 12,
 ,[Offset], SFD0B-LP1, 13,
 ,[Offset], SFD0B-LP2, 14,
 ,[Offset], SFD0C, 15,
 ,[Offset], SFD0D, 16,
 ,[Offset], SFD0E, 17,
 ,[Offset], SFD0F, 18,
 ,[Offset], SFD10, 19,
 ,[Offset], SFD11, 20,
 ,[Offset], SFD12, 21,
 ,[Offset], SFD13, 22,
 ,[Offset], SFD14, 23,
 ,[Offset], SFD15-LP1, 24,
 ,[Offset], SFD15-LP2, 25,
 ,[Offset], SFD16-LP1, 26,
 ,[Offset], SFD16-LP2, 27,
 ,[Offset], SFD17, 28,
 ,[Offset], SFD18, 29,
 ,[Offset], SFD19, 30,
 ,[Offset], SFD1A, 31,
 ,[Offset], SFD1B, 32,
 ,[Offset], SFD1D, 33,
 ,[Offset], SFD1E, 34,
 ,[Offset], SFD1F, 35,
 ,[Offset], SFD20-LP1, 36,
 ,[Offset], SFD20-LP2, 37,
 ,[Offset], SFD21, 38,
 ,[Offset], SFD22, 39,
 ,[Offset], SFD23, 40,
 ,[Offset], SFD24, 41,
 ,[Offset], SFD28, 42,
 ,[Offset], SFD2A-LP1, 43,
 ,[Offset], SFD2A-LP2, 44,
 ,[Offset], SFD2C, 45,
 ,[Offset], SFD2E, 46,
 ,[Offset], SFD30, 47,
 ,[Offset], SFD32, 48,
 ,[Offset], SFD34, 49,
 ,[Offset], SFD36, 50,
 ,[Offset], SFD38, 51,
 ,[Offset], SFD39, 52,
 ,[Offset], SFD3A, 53,
 ,[Offset], SFD3B, 54,
 ,[Offset], SFD3C, 55,
 ,[Offset], SFD3D, 56,
 ,[Offset], SFD3E, 57,
 ,[Offset], SFD3F, 58,
 ,[Offset], SFD42, 59,
 ,[Offset], SFD48, 60,
 ,[Offset], SFD4C, 61,
 ,[Offset], SFD4D, 62,
 ,[Offset], SFD4E, 63,
 ,[Offset], SFD4F, 64,
 ,[Offset], SFD50, 65,
 ,[Offset], SFD51, 66,
 ,[Offset], SFD52, 67,
 ,[Offset], SFD56, 68,
 ,[Offset], SFD57, 69,
 ,[Offset], SFD58, 70,
 ,[Offset], SFD59, 71,
 ,[Offset], SFD5A, 72,
 ,[Offset], SFD5B, 73,
 ,[Offset], SFD5C, 74,
 ,[Offset], SFD5E, 75,
 ,[Offset], SFD5F, 76,
 ,[Offset], SFD60, 77,
 ,[Offset], SFD61, 78,
 ,[Offset], SFD62, 79,
 ,[Offset], SFD63, 80,
 ,[Offset], SFD65, 81,
 ,[Offset], SFD67, 82,
 ,[Offset], SFD68, 83,
 ,[Offset], SFD6A, 84,
 ,[Offset], SFD6B, 85,
 ,[Offset], SFD6D, 86,
 ,[Offset], SFD6E, 87,
 ,[Offset], SFD70, 88,
 ,[Offset], SFD71, 89,
 ,[Offset], SFD72, 90,
 ,[Offset], SFD73, 91,
 ,[Offset], SFD74, 92,
 ,[Offset], SFD75, 93,
 ,[Offset], SFD76, 94,
 ,[Offset], SFD77, 95,
 ,[Offset], SFD78, 96,
 ,[Offset], SFD79, 97,
 ,[Offset], SFD7B, 98,
 ,[Offset], SFD7C, 99,
 ,[Offset], SFD7D, 100,
 ,[Offset], SFD7E, 101,
 ,[Offset], SFD7F, 102,
 ,[Offset], SFD80, 103,
 ,[Offset], SFD81, 104,
 ,[Offset], SFD82, 105,
 ,[Offset], SFD83, 106,
 ,[Offset], SFD85, 107,
 ,[Offset], SFD89, 108,
 ,[Offset], SFD8B, 109,
 ,[Offset], SFD90, 110,
 ,[Offset], SFD91, 111,
 ,[Offset], SFD92, 112,
 ,[Offset], SFD93, 113,
 ,[Offset], SFD94, 114,
 ,[Offset], SFD95, 115,
 ,[Offset], SFD96, 116,
 ,[Offset], SFD97, 117,
 ,[Offset], SFD9D, 118,
 ,[Offset], SFD9E, 119,
 ,[Offset], SFD9F, 120,
 ,[Offset], SFDA1, 121,
 ,[Offset], SFDA3, 122,
 ,[Offset], SFDA4, 123,
 ,[Offset], SFDA5, 124,
 ,[Offset], SFDA6, 125,
 ,[Offset], SFDA7, 126,
 ,[Offset], SFDAB, 127,
 ,[Offset], SFDAC, 128,
 ,[Offset], SFDAD, 129,
 ,[Offset], SFDAE, 130,
 ,[Offset], SFDAF, 131,
 ,[Offset], SFDB0, 132,
 ,[Offset], SFDB1, 133,
 ,[Offset], SFDB2, 134,
 ,[Offset], SFDB3, 135,
 ,[Offset], SFDB4, 136,
 ,[Offset], SFDB5, 137,
 ,[Offset], SFDB6, 138,
 ,[Offset], SFDB7, 139,
 ,[Offset], SFDB8, 140,
 ,[Offset], SFDB9, 141,
 ,[Offset], SFDBB, 142,
 ,[Offset], SFDBC, 143,
 ,[Offset], SFDBD, 144,
 ,[Offset], SFDBE, 145,
 ,[Offset], SFDBF, 146,
 ,[Offset], SFDC0, 147,
 ,[Offset], SFDC1, 148,
 ,[Offset], SFDC2, 149,
 ,[Offset], SFDC5, 150,
 ,[Offset], SFDC7, 151,
 ,[Offset], SFDC8, 152,
 ,[Offset], SFDC9, 153,
 ,[Offset], SFDCA, 154,
 ,[Offset], SFDCB, 155,
 ,[Offset], SFDCC, 156,
 ,[Offset], SFDCD, 157,
 ,[Offset], SFDCE, 158,
 ,[Offset], SFDCF, 159,
 ,[Offset], SFDD0, 160,
 ,[Offset], SFDD1, 161,
 ,[Offset], SFDD2, 162,
 ,[Offset], SFDD3, 163,
 ,[Offset], SFDD5, 164,
 ,[Offset], SFDD6, 165,
 ,[Offset], SFDD7, 166,
 ,[Offset], SFDD8, 167,
 ,[Offset], SFDD9, 168,
 ,[Offset], SFDDA, 169,
 ,[Offset], SFDDD, 170,
 ,[Offset], SFDDF, 171,
 ,[Offset], SFDE0, 172,
 ,[Offset], SFDE1, 173,
 ,[Offset], SFDE3, 174,
 ,[Offset], SFDE4, 175,
 ,[Offset], SFDE5, 176,
 ,[Offset], SFDE6, 177,
 ,[Offset], SFDE8, 178,
 ,[Offset], SFDE9, 179,
 ,[Offset], SFDEA, 180,
 ,[Offset], SFDEC, 181,
 ,[Offset], SFDED, 182,
 ,[Offset], SFDF0, 183,
 ,[Offset], SFDF1, 184,
 ,[Offset], SFDF2, 185,
 ,[Offset], SFDF4, 186,
 ,[Offset], SFDF5, 187,
 ,[Offset], SFDF6, 188,
 ,[Offset], SFDF7, 189,
 ,[Offset], SFDF8, 190,
 ,[Offset], SFDF9, 191,
 ,[Offset], SFDFA, 192,
 ,[Offset], SFDFB, 193,
 ,[Offset], SFDFC, 194,
 ,[Offset], SFE30, 195,
 ,[Offset], SFE31, 196,
 ,[Offset], SFE32, 197,
 ,[Offset], SFE33, 198,
 ,[Offset], SFE34, 199,
 ,[Offset], SFE35, 200,
 ,[Offset], SFE36, 201,
 ,[Offset], SFE37, 202,
 ,[Offset], SFE38, 203,
 ,[Offset], SFE39, 204,
 ,[Offset], SFE3A, 205,
 ,[Offset], SFE3B, 206,
 ,[Offset], SFE3C, 207,
 ,[Offset], SFE3D, 208,
 ,[Offset], SFE3E, 209,
 ,[Offset], SFE3F, 210,
 ,[Offset], SFE40, 211,
 ,[Offset], SFE41, 212,
 ,[Offset], SFE42, 213,
 ,[Offset], SFE45, 214,
 ,[Offset], SFE46, 215,
 ,[Offset], SFE47, 216,
 ,[Offset], SFE48, 217,
 ,[Offset], SFE49, 218,
 ,[Offset], SFE4A, 219,
 ,[Offset], SFE4B, 220,
 ,[Offset], SFE4C, 221,
 ,[Offset], SFE4D, 222,
 ,[Offset], SFE4E, 223,
 ,[Offset], SFE4F, 224,
 ,[Offset], SFE50, 225,
 ,[Offset], SFE51, 226,
 ,[Offset], SFE52, 227,
 ,[Offset], SFE53, 228,
 ,[Offset], SFE54, 229,
 ,[Offset], SFE55, 230,
 ,[Offset], SFE56, 231,
 ,[Offset], SFE57, 232,
 ,[Offset], SFE58, 233,
 ,[Offset], SFE59, 234,
 ,[Offset], SFE5A, 235,
 ,[Offset], SFE5B, 236,
 ,[Offset], SFE5C, 237,
 ,[Offset], SFE5D, 238,
 ,[Offset], SFE5E, 239,
 ,[Offset], SFE5F, 240,
 ,[Offset], SFE60, 241,
 ,[Offset], SFE62, 242,
 ,[Offset], SFE63, 243,
 ,[Offset], SFE64, 244,
 ,[Offset], SFE65, 245,
 ,[Offset], SFE66, 246,
 ,[Offset], SFE67, 247,
 ,[Offset], SFE68, 248,
 ,[Offset], SFE69, 249,
 ,[Offset], SFE6A, 250,
 ,[Offset], SFE6B, 251,
 ,[Offset], SFE6C, 252,
 ,[Offset], SFE6D, 253,
 ,[Offset], SFE6E, 254,
 ,[Offset], SFE6F, 255,
 ,[Offset], SFE70, 256,
 ,[Offset], SFE71, 257,
 ,[Offset], SFE72, 258,
 ,[Offset], SFE73, 259,
 ,[Offset], SFE74, 260,
 ,[Offset], SFE76, 261,
 ,[Offset], SFE77, 262,
 ,[Offset], SFE78, 263,
 ,[Offset], SFE79, 264,
 ,[Offset], SFE7A, 265,
 ,[Offset], SFE7B, 266,
 ,[Offset], SFE7C, 267,
 ,[Offset], SFE7D, 268,
 ,[Offset], SFE7E, 269,
 ,[Offset], SFE7F, 270,
 ,[Offset], SFE80, 271,
 ,[Offset], SFE81, 272,
 ,[Offset], SFE82, 273,
 ,[Offset], SFE85, 274,
 ,[Offset], SFE86, 275,
 ,[Offset], SFE87, 276,
 ,[Offset], SFE8B, 277,
 ,[Offset], SFE8C, 278,
 ,[Offset], SFE8D, 279,
 ,[Offset], SFE8E, 280,
 ,[Offset], SFE8F, 281,
 ,[Offset], SFE90, 282,
 ,[Offset], SFE91, 283,
 ,[Offset], SFE94, 284,
 ,[Offset], SFE95, 285,
 ,[Offset], SFE96, 286,
 ,[Offset], SFE97, 287,
 ,[Offset], SFE98, 288,
 ,[Offset], SFE99, 289,
 ,[Offset], SFE9B, 290,
 ,[Offset], SFE9C, 291,
 ,[Offset], SFE9D, 292,
 ,[Offset], SFEA0, 293,
 ,[Offset], SFEA1, 294,
 ,[Offset], SFEA2, 295,
 ,[Offset], SFEA3, 296,
 ,[Offset], SFEA4, 297,
 ,[Offset], SFEA5, 298,
 ,[Offset], SFEA6, 299,
 ,[Offset], SFEA7, 300,
 ,[Offset], SFEA8, 301,
 ,[Offset], SFEA9, 302,
 ,[Offset], SFEAA, 303,
 ,[Offset], SFEAB, 304,
 ,[Offset], SFEAC, 305,
 ,[Offset], SFEAD, 306,
 ,[Offset], SFEAE, 307,
 ,[Offset], SFEAF, 308,
 ,[Offset], SFEB0, 309,
 ,[Offset], SFEB1, 310,
 ,[Offset], SFEB2, 311,
 ,[Offset], SFEB3, 312,
 ,[Offset], SFEB4, 313,
 ,[Offset], SFEB5, 314,
 ,[Offset], SFEB6, 315,
 ,[Offset], SFEB7, 316,
 ,[Offset], SFEB8, 317,
 ,[Offset], SFEBB, 318,
 ,[Offset], SFEBC, 319,
 ,[Offset], SFEBE, 320,
 ,[Offset], SFEC3, 321,
 ,[Offset], SFEC5, 322,
 ,[Offset], SFEC6, 323,
 ,[Offset], SFEC9, 324,
 ,[Offset], SFECA, 325,
 ,[Offset], SFECB, 326,
 ,[Offset], SFECD, 327,
 ,[Offset], SFECE, 328,
 ,[Offset], SFECF, 329,
 ,[Offset], SFED0, 330,
 ,[Offset], SFED1, 331,
 ,[Offset], SFED2, 332,
 ,[Offset], SFED3, 333,
 ,[Offset], SFED4, 334,
 ,[Offset], SFED5, 335,
 ,[Offset], SFED6, 336,
 ,[Offset], SFED7, 337,
 ,[Offset], SFED8, 338,
 ,[Offset], SFED9, 339,
 ,[Offset], SFEDA, 340,
 ,[Offset], SFEDB, 341,
 ,[Offset], SFEDC, 342,
 ,[Offset], SFEDD, 343,
 ,[Offset], SFEDE, 344,
 ,[Offset], SFEDF, 345,
 ,[Offset], SFEE1, 346,
 ,[Offset], SFEE2, 347,
 ,[Offset], SFEE3, 348,
 ,[Offset], SFEE4, 349,
 ,[Offset], SFEE5, 350,
 ,[Offset], SFEE6, 351,
 ,[Offset], SFEE7, 352,
 ,[Offset], SFEE8, 353,
 ,[Offset], SFEE9, 354,
 ,[Offset], SFEEA, 355,
 ,[Offset], SFEEB, 356,
 ,[Offset], SFEED, 357,
 ,[Offset], SFEEE, 358,
 ,[Offset], SFEEF, 359,
 ,[Offset], SFEF0, 360,
 ,[Offset], SFEF3, 361,
 ,[Offset], SFEF4, 362,
 ,[Offset], SFEF5, 363,
 ,[Offset], SFEF6, 364,
 ,[Offset], SFEF7, 365,
 ,[Offset], SFEF8, 366,
 ,[Offset], SFEF9, 367,
 ,[Offset], SFEFA, 368,
 ,[Offset], SFEFB, 369,
 ,[Offset], menu_select, 370,
RSID_TABLE_HARLEY_MECH_SOUNDS, 3412,,,
 ,[Offset], harley_plunge, 0,
RSID_TABLE_HARLEY_SOUNDS_END, 3413,,,
RSID_TABLE_HARLEY_SAMPLES, 3414,,,
RSID_THARLEY_END, 3415,,,
