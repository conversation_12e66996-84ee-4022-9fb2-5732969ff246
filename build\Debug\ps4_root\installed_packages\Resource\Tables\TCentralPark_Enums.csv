RSID_TCENTRALPARK_START, 3025,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], CentralPark\PBTCentralPark, 1,
 ,[Offset], CentralPark\InstructionsENG, 2,
 ,[Offset], CentralPark\InstructionsFR, 3,
 ,[Offset], CentralPark\InstructionsITAL, 4,
 ,[Offset], CentralPark\InstructionsGERM, 5,
 ,[Offset], CentralPark\InstructionsSPAN, 6,
 ,[Offset], CentralPark\InstructionsPORT, 7,
 ,[Offset], CentralPark\InstructionsDUTCH, 8,
RSID_TCENTRALPARK_LIGHTS, 3026,,,
RSID_TCENTRALPARK_CAMERAS, 3027,,,
RSID_TCENTRALPARK_LAMP_TEXTURES, 3028,,,
 ,[Offset], L1 unlit, 0,
 ,[Offset], L1 lit, 1,
 ,[Offset], L2 unlit, 2,
 ,[Offset], L2 lit, 3,
 ,[Offset], L3 unlit, 4,
 ,[Offset], L3 lit, 5,
 ,[Offset], L4 unlit, 6,
 ,[Offset], L4 lit, 7,
 ,[Offset], L5 unlit, 8,
 ,[Offset], L5 lit, 9,
 ,[Offset], L6 unlit, 10,
 ,[Offset], L6 lit, 11,
 ,[Offset], L7 unlit, 12,
 ,[Offset], L7 lit, 13,
 ,[Offset], R1 unlit, 14,
 ,[Offset], R1 lit, 15,
 ,[Offset], R2 unlit, 16,
 ,[Offset], R2 lit, 17,
 ,[Offset], R3 unlit, 18,
 ,[Offset], R3 lit, 19,
 ,[Offset], R4 unlit, 20,
 ,[Offset], R4 lit, 21,
 ,[Offset], R5 unlit, 22,
 ,[Offset], R5 lit, 23,
 ,[Offset], R6 unlit, 24,
 ,[Offset], R6 lit, 25,
 ,[Offset], R7 unlit, 26,
 ,[Offset], R7 lit, 27,
 ,[Offset], L8 unlit, 28,
 ,[Offset], L8 lit, 29,
 ,[Offset], L9 unlit, 30,
 ,[Offset], L9 lit, 31,
 ,[Offset], L10 unlit, 32,
 ,[Offset], L10 lit, 33,
 ,[Offset], L11 unlit, 34,
 ,[Offset], L11 lit, 35,
 ,[Offset], L12 unlit, 36,
 ,[Offset], L12 lit, 37,
 ,[Offset], R8 unlit, 38,
 ,[Offset], R8 lit, 39,
 ,[Offset], R9 unlit, 40,
 ,[Offset], R9 lit, 41,
 ,[Offset], R10 unlit, 42,
 ,[Offset], R10 lit, 43,
 ,[Offset], R11 unlit, 44,
 ,[Offset], R11 lit, 45,
 ,[Offset], R12 unlit, 46,
 ,[Offset], R12 lit, 47,
 ,[Offset], bottom green unlit, 48,
 ,[Offset], bottom green lit, 49,
 ,[Offset], top green unlit, 50,
 ,[Offset], top green lit, 51,
 ,[Offset], bottom yellow unlit, 52,
 ,[Offset], bottom yellow lit, 53,
 ,[Offset], top yellow unlit, 54,
 ,[Offset], top yellow lit, 55,
RSID_TCENTRALPARK_TEXTURES, 3029,,,
 ,[Offset], BackGlass01, 0,
 ,[Offset], BackGlass, 1,
 ,[Offset], Ball Bumper Stop, 2,
 ,[Offset], blank, 3,
 ,[Offset], BrickWall_red, 4,
 ,[Offset], brushed metal, 5,
 ,[Offset], Bumper Glass, 6,
 ,[Offset], Bumper rubber2, 7,
 ,[Offset], Bumper rubber red, 8,
 ,[Offset], Bumper_lower, 9,
 ,[Offset], Bumper_SideUp_Center, 10,
 ,[Offset], columns, 11,
 ,[Offset], CP_BackGlass, 12,
 ,[Offset], CP_tableSide, 13,
 ,[Offset], CP_TableTop, 14,
 ,[Offset], curtains, 15,
 ,[Offset], dryWall, 16,
 ,[Offset], fancyTable, 17,
 ,[Offset], Flipper_Decor, 18,
 ,[Offset], floor 1A, 19,
 ,[Offset], floor 1B, 20,
 ,[Offset], floor 1C, 21,
 ,[Offset], floor 1D, 22,
 ,[Offset], floor 2A, 23,
 ,[Offset], floor 2B, 24,
 ,[Offset], floor 2C, 25,
 ,[Offset], floor 2D, 26,
 ,[Offset], frontmetal, 27,
 ,[Offset], G_BackGlass, 28,
 ,[Offset], G_tableSide_front, 29,
 ,[Offset], G_TableTop, 30,
 ,[Offset], glass bulb, 31,
 ,[Offset], glass, 32,
 ,[Offset], green lit rail, 33,
 ,[Offset], Green_Yellow Target, 34,
 ,[Offset], GreyMetal, 35,
 ,[Offset], gsign01, 36,
 ,[Offset], log, 37,
 ,[Offset], LowerTable_L, 38,
 ,[Offset], LowerTable_R, 39,
 ,[Offset], metal01, 40,
 ,[Offset], metal_trim, 41,
 ,[Offset], nut, 42,
 ,[Offset], orange tri bumper lit, 43,
 ,[Offset], Particle Board, 44,
 ,[Offset], PB_sideTable_front, 45,
 ,[Offset], PB_TableTop, 46,
 ,[Offset], Playboy_Sign, 47,
 ,[Offset], Red_Legs, 48,
 ,[Offset], RoundBumpers, 49,
 ,[Offset], Rubber Ball Bumper, 50,
 ,[Offset], space1, 51,
 ,[Offset], tableglass, 52,
 ,[Offset], tableSide, 53,
 ,[Offset], Target, 54,
 ,[Offset], teddy black, 55,
 ,[Offset], teddy purple, 56,
 ,[Offset], teddy white, 57,
 ,[Offset], top flipper lit, 58,
 ,[Offset], UpCurve_L, 59,
 ,[Offset], UpCurve_R, 60,
 ,[Offset], wood, 61,
 ,[Offset], woodbase_wall1, 62,
 ,[Offset], WoodEdge_Tile, 63,
 ,[Offset], woodFloor, 64,
 ,[Offset], WoodTrim_Decor, 65,
 ,[Offset], yellow lit rail, 66,
 ,[Offset], zoltar shirt, 67,
 ,[Offset], ZOLTAR WOOD2, 68,
 ,[Offset], ZOLTAR WOOD3, 69,
 ,[Offset], ZOLTAR WOOD4, 70,
 ,[Offset], Zoltor cabinet2, 71,
 ,[Offset], lower_playfield, 72,
 ,[Offset], upper_playfield, 73,
 ,[Offset], coin_box, 74,
 ,[Offset], Metal_Parts, 75,
 ,[Offset], Plunger_Plate, 76,
 ,[Offset], wooden_rails, 77,
 ,[Offset], upper_apron, 78,
 ,[Offset], nut01, 79,
 ,[Offset], Silver_screws, 80,
 ,[Offset], stopper, 81,
 ,[Offset], flipper_gate, 82,
 ,[Offset], chrome_post, 83,
 ,[Offset], metal_parts01, 84,
 ,[Offset], Plunger, 85,
 ,[Offset], L_chrome_post, 86,
 ,[Offset], R_chrome_post, 87,
RSID_TCENTRALPARK_MODELS, 3030,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper G, 3,
 ,[Offset], chrome, 4,
 ,[Offset], env, 5,
 ,[Offset], flipper A bottom, 6,
 ,[Offset], flipper A, 7,
 ,[Offset], flipper B bottom, 8,
 ,[Offset], flipper B, 9,
 ,[Offset], flipper B, 10,
 ,[Offset], gate oneway, 11,
 ,[Offset], glass, 12,
 ,[Offset], cabinet, 13,
 ,[Offset], plastics, 14,
 ,[Offset], metal, 15,
 ,[Offset], metal, 16,
 ,[Offset], pop bumper A, 17,
 ,[Offset], pop bumper B, 18,
 ,[Offset], rubber, 19,
 ,[Offset], slingshot A, 20,
 ,[Offset], slingshot B, 21,
 ,[Offset], slingshot C, 22,
 ,[Offset], slingshot D, 23,
 ,[Offset], stopper, 24,
 ,[Offset], target A, 25,
 ,[Offset], target f, 26,
 ,[Offset], target g, 27,
 ,[Offset], tile, 28,
 ,[Offset], transparent, 29,
 ,[Offset], wire A, 30,
 ,[Offset], wire B, 31,
 ,[Offset], wire C, 32,
 ,[Offset], wire D, 33,
 ,[Offset], wire E, 34,
 ,[Offset], wire F, 35,
 ,[Offset], wire G, 36,
 ,[Offset], wire H, 37,
 ,[Offset], wire I, 38,
 ,[Offset], wire J, 39,
 ,[Offset], playfield, 40,
 ,[Offset], lamps, 41,
 ,[Offset], cabinet_metal, 42,
 ,[Offset], aprons, 43,
 ,[Offset], green_pop bumpers, 44,
 ,[Offset], yellow_pop bumpers, 45,
 ,[Offset], nuts and screws, 46,
 ,[Offset], flipper_gates, 47,
 ,[Offset], green_plastic, 48,
 ,[Offset], yellow_plastic, 49,
 ,[Offset], rails, 50,
 ,[Offset], metal_parts, 51,
 ,[Offset], plunger01, 52,
 ,[Offset], lamps01, 53,
 ,[Offset], wooden_rails, 54,
RSID_TCENTRALPARK_MODELS_LODS, 3031,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper G, 3,
 ,[Offset], chrome, 4,
 ,[Offset], env, 5,
 ,[Offset], flipper A bottom, 6,
 ,[Offset], flipper A, 7,
 ,[Offset], flipper B bottom, 8,
 ,[Offset], flipper B, 9,
 ,[Offset], flipper B, 10,
 ,[Offset], gate oneway, 11,
 ,[Offset], glass, 12,
 ,[Offset], cabinet, 13,
 ,[Offset], plastics, 14,
 ,[Offset], metal, 15,
 ,[Offset], metal, 16,
 ,[Offset], pop bumper A, 17,
 ,[Offset], pop bumper B, 18,
 ,[Offset], rubber, 19,
 ,[Offset], slingshot A, 20,
 ,[Offset], slingshot B, 21,
 ,[Offset], slingshot C, 22,
 ,[Offset], slingshot D, 23,
 ,[Offset], stopper, 24,
 ,[Offset], target A, 25,
 ,[Offset], target f, 26,
 ,[Offset], target g, 27,
 ,[Offset], tile, 28,
 ,[Offset], transparent, 29,
 ,[Offset], wire A, 30,
 ,[Offset], wire B, 31,
 ,[Offset], wire C, 32,
 ,[Offset], wire D, 33,
 ,[Offset], wire E, 34,
 ,[Offset], wire F, 35,
 ,[Offset], wire G, 36,
 ,[Offset], wire H, 37,
 ,[Offset], wire I, 38,
 ,[Offset], wire J, 39,
 ,[Offset], playfield, 40,
 ,[Offset], lamps, 41,
 ,[Offset], cabinet_metal, 42,
 ,[Offset], aprons, 43,
 ,[Offset], green_pop bumpers, 44,
 ,[Offset], yellow_pop bumpers, 45,
 ,[Offset], nuts and screws, 46,
 ,[Offset], flipper_gates, 47,
 ,[Offset], green_plastic, 48,
 ,[Offset], yellow_plastic, 49,
 ,[Offset], rails, 50,
 ,[Offset], metal_parts, 51,
 ,[Offset], plunger01, 52,
 ,[Offset], lamps01, 53,
 ,[Offset], wooden_rails, 54,
RSID_TCENTRALPARK_COLLISIONS, 3032,,,
 ,[Offset], arc col, 0,
 ,[Offset], bumper A col, 1,
 ,[Offset], bumper B col, 2,
 ,[Offset], bumper B col, 3,
 ,[Offset], flipper A B col, 4,
 ,[Offset], flipper A col, 5,
 ,[Offset], flipper A F col, 6,
 ,[Offset], flipper B B col, 7,
 ,[Offset], flipper B col, 8,
 ,[Offset], flipper B F col, 9,
 ,[Offset], floor col, 10,
 ,[Offset], gate oneway col, 11,
 ,[Offset], platform 1, 12,
 ,[Offset], played col, 13,
 ,[Offset], plunger area col, 14,
 ,[Offset], plunger col, 15,
 ,[Offset], pop bumper A col, 16,
 ,[Offset], pop bumper b col, 17,
 ,[Offset], rubber col, 18,
 ,[Offset], slingshot A col, 19,
 ,[Offset], slingshot B col, 20,
 ,[Offset], slingshot C col, 21,
 ,[Offset], slingshot D col, 22,
 ,[Offset], stopper col, 23,
 ,[Offset], target A col, 24,
 ,[Offset], target f col, 25,
 ,[Offset], target g col, 26,
 ,[Offset], wall col, 27,
 ,[Offset], rubber col 01, 28,
 ,[Offset], rubber col 02, 29,
 ,[Offset], rubber col 03, 30,
 ,[Offset], rubber col 04, 31,
 ,[Offset], rubber col 05, 32,
 ,[Offset], rubber col 06, 33,
 ,[Offset], rubber col 07, 34,
 ,[Offset], rubber col 08, 35,
 ,[Offset], rail_col, 36,
 ,[Offset], wall col 01, 37,
 ,[Offset], wall col 02, 38,
 ,[Offset], left_plastic walls, 39,
 ,[Offset], right_plastic walls, 40,
 ,[Offset], ball drain walls, 41,
RSID_TCENTRALPARK_PLACEMENT, 3033,,,
 ,[Offset], bumpers, 0,
 ,[Offset], plunger, 1,
RSID_TCENTRALPARK_SOUNDS, 3034,,,
 ,[Offset], bigbell01, 0,
 ,[Offset], freegame01, 1,
 ,[Offset], target01, 2,
 ,[Offset], target02, 3,
 ,[Offset], wire01, 4,
 ,[Offset], side_center_wires, 5,
 ,[Offset], menu_music, 6,
RSID_TCENTRALPARK_HUD, 3035,,,
 ,[Offset], CP_Hud_Border, 0,
 ,[Offset], CP_Hud_Numbers, 1,
 ,[Offset], CP_Hud_Border_BallInPlay, 2,
 ,[Offset], monkey, 3,
 ,[Offset], monkey_bell, 4,
RSID_FONTTABLE_TCENTRALPARK_FONT, 3036,,,
RSID_FONT_TCENTRALPARK_HUD, 3037,,,
RSID_FONTTABLE_TCENTRALPARK_FONTHD, 3038,,,
RSID_FONT_TCENTRALPARK_HUDHD, 3039,,,
RSID_FONTTABLE_TCENTRALPARK_FONTHDD, 3040,,,
RSID_FONT_TCENTRALPARK_HUDHDD, 3041,,,
RSID_TCENTRALPARK_VERSION, 3042,,,
RSID_TCENTRALPARK_END, 3043,,,
