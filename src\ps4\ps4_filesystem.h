// Copyright 2025 <Copyright Owner>

#pragma once

#include <chrono>
#include <cstdint>
#include <filesystem>
#include <functional>
#include <istream>
#include <map>
#include <memory>
#include <mutex>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <unordered_map>
#include <utility>
#include <vector>

#if defined(_MSC_VER) || defined(_WIN32) || defined(_WIN64)
#include <BaseTsd.h>
typedef SSIZE_T ssize_t;
typedef unsigned int mode_t;
#endif

#ifdef CreateDirectoryA
#undef CreateDirectoryA
#endif
#ifdef CreateDirectoryW
#undef CreateDirectoryW
#endif
#ifdef CreateDirectory
#undef CreateDirectory
#endif
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif
#ifdef EncryptFileA
#undef EncryptFileA
#endif
#ifdef EncryptFileW
#undef EncryptFileW
#endif
#ifdef EncryptFile
#undef EncryptFile
#endif
#ifdef DecryptFileA
#undef DecryptFileA
#endif
#ifdef DecryptFileW
#undef DecryptFileW
#endif
#ifdef DecryptFile
#undef DecryptFile
#endif

namespace ps4 {

class PS4Emulator;

enum class PS4FileType {
    Regular,
    Directory,
    PFS,
    Device,
    System,
    Network,
    Pipe,
    Socket
};

struct FilesystemStats {
    uint64_t operationCount = 0;
    uint64_t fileAccessCount = 0;
    uint64_t cacheHits = 0;
    uint64_t cacheMisses = 0;
    uint64_t totalLatencyUs = 0;
};

struct EnhancedSettings {
    std::string saveDataPath = "/savedata";
    std::string trophyPath = "/trophy";
    std::string systemPath = "/system";
    std::string defaultMountPoint = "/app0";
    bool enablePFS = true;
    bool enableDeviceFiles = true;
    bool enableCaseSensitivity = false;
    uint32_t defaultFileMode = 0644;
    uint32_t defaultDirMode = 0755;
    uint64_t pfsBlockSize = 4096;
    uint64_t cacheSize = 64 * 1024 * 1024; // 64MB default cache
    std::vector<std::string> additionalMounts;
};

struct FileHandle {
    std::string path;
    int flags = 0;
    off_t offset = 0;
    int hostFd = -1;
    int fd = -1;
    uint64_t cacheHits = 0;
    uint64_t cacheMisses = 0;
};

struct FileEntry {
    std::string path;
    std::string hostPath;
    std::vector<uint8_t> data;
    uint64_t size = 0;
    uint32_t protection = 0;
    uint32_t mode = 0;
    time_t creationTime = 0;
    time_t modificationTime = 0;
    time_t accessTime = 0;
    bool isDir = false;
    PS4FileType fileType = PS4FileType::Regular;
    uint32_t ps4Permissions = 0;
    std::string mountPoint;
    bool isEncrypted = false;
    uint64_t blockSize = 0;
    std::vector<uint8_t> checksum;
    bool present = false;
    uint64_t cacheHits = 0;
    uint64_t cacheMisses = 0;
};

struct PFSBlock {
    uint64_t index = 0;
    uint64_t offset = 0;
    std::vector<uint8_t> data;
    std::vector<uint8_t> checksum;
    bool dirty = false;
    bool loaded = false;
    time_t lastAccessTime = 0;
};

struct PFSFile {
    std::string path;
    std::string hostPath;
    uint64_t size = 0;
    uint64_t blockSize = 4096;
    uint64_t blockCount = 0;
    bool isEncrypted = false;
    std::vector<uint8_t> key;
    std::vector<uint8_t> iv;
    std::unordered_map<uint64_t, PFSBlock> blocks;
    time_t accessTime = 0;
    time_t modificationTime = 0;
    uint64_t cacheHits = 0;
    uint64_t cacheMisses = 0;
};

// Declarations for MountPointManager
class MountPointManager {
public:
    bool Mount(const std::filesystem::path &hostPath, const std::string &guestPath,
               bool readOnly = false, bool isPFS = false);
    bool Unmount(const std::string &guestPath);
    void UnmountAll();
    std::filesystem::path GetHostPath(const std::string &guestPath, bool *isReadOnly = nullptr) const;

private:
    std::map<std::string, std::pair<std::filesystem::path, bool>> m_mountPoints;
    std::map<std::string, bool> m_pfsMountPoints;
    mutable std::shared_mutex m_mutex;
};

// Declarations for HandleTable
class HandleTable {
public:
    struct File {
        int fd = -1; // Add this line to store the file descriptor
        bool is_opened = false;
        std::string host_name;
        std::string guest_name;
        int host_fd = -1;
        PS4FileType type = PS4FileType::Regular;
    };

    int CreateHandle();
    void DeleteHandle(int fd);
    File *GetFile(int fd);
    void CreateStdHandles();

private:
    std::unordered_map<int, File> m_files;
    std::shared_mutex m_mutex;
    int m_nextFd = 3;
};

class PS4Filesystem {
public:
    PS4Filesystem(PS4Emulator &emu);
    PS4Filesystem();
    ~PS4Filesystem();

    bool Initialize();
    void Shutdown();

    int OpenFile(const std::string &path, int flags, mode_t mode);
    int CloseFile(int fd);
    ssize_t ReadFile(int fd, void *buf, size_t count);
    bool ReadFile(const std::string &path, std::vector<uint8_t> &data);
    // std::vector<std::string> ListFiles(const std::string &virtDir, bool recursive = false); // This function is declared but not defined in the .cpp
    ssize_t WriteFile(int fd, const void *buf, size_t count);
    bool WriteFile(const std::string &path, const void *data, uint64_t size);
    off_t SeekFile(int fd, off_t offset, int whence);

#ifdef _WIN32
    int StatFile(const std::string &path, struct _stat64i32 *buf);
#else
    int StatFile(const std::string &path, struct stat *buf);
#endif

    bool CreateDirectory(const std::string &path, mode_t mode);
    // bool CreateDirectoryW(const std::wstring &path); // This function is declared but not defined in the .cpp
    bool CreateVirtualDirectory(const std::string &path);
    bool RemoveDirectory(const std::string &path);
    bool MountDirectory(const std::wstring &path);

    uint64_t AllocateVirtualMemory(uint64_t size, uint64_t alignment, bool shared);
    bool FreeVirtualMemory(uint64_t address);
    bool ProtectMemory(uint64_t address, uint64_t size, int protection);
    uint64_t SceKernelGetProcessId();

    std::string DumpState() const;
    void SaveState(std::ostream &out) const;
    void LoadState(std::istream &in);

    FilesystemStats GetStats() const;
    void SetSettings(const EnhancedSettings &settings);
    const EnhancedSettings &GetSettings() const;
    bool SaveSettings(const std::string &filename) const;
    bool LoadSettings(const std::string &filename);

    // bool LoadGame(const std::string &gamePath); // This function is declared but not defined in the .cpp
    // bool StartGame(); // This function is declared but not defined in the .cpp
    // std::string GetLoadedGamePath() const; // This function is declared but not defined in the .cpp
    // bool IsGameLoaded() const; // This function is declared but not defined in the .cpp
    std::string GetGameDirectory() const;
    void SetGameDirectory(const std::string &path);

    bool InitializePFS();
    bool CreatePFSFile(const std::string &path, uint64_t size, bool encrypted = true);
    bool DeletePFSFile(const std::string &path);
    bool MountPFS(const std::string &pfspath, const std::string &mountpoint, const std::vector<uint8_t> &key);
    bool UnmountPFS(const std::string &mountpoint);
    ssize_t ReadPFSFile(const std::string &path, void *buf, uint64_t offset, size_t count);
    ssize_t WritePFSFile(const std::string &path, const void *buf, uint64_t offset, size_t count);
    bool ValidatePFSChecksum(const std::string &path);

    // bool CreateDeviceFile(const std::string &path, PS4FileType deviceType); // This function is declared but not defined in the .cpp
    bool HandleDeviceAccess(const std::string &path, void *buffer, size_t size, bool isWrite);
    bool InitializeAllDeviceFiles();
    bool RegisterDeviceHandler(const std::string &devicePath, std::function<bool(void *, size_t, bool)> handler);

    PS4FileType DetermineFileType(const std::string &path) const;
    bool ValidatePS4Permissions(const std::string &path, int mode);
    std::string MapToHostPath(const std::string &virtualPath);
    std::string MapToHostPathLocked(const std::string &virtualPath);
    std::string ResolvePath(const std::string &virtualPath) const;

private:
    bool InitializeAllDeviceFilesLocked(); // Internal version that assumes mutex is already held
    bool CreateDeviceFileInternal(const std::string &path, PS4FileType deviceType);
    bool CreateDeviceFileInternalLocked(const std::string &path, PS4FileType deviceType);
    bool RegisterDeviceHandlerLocked(const std::string &devicePath, std::function<bool(void *, size_t, bool)> handler);
    bool MountPFSLocked(const std::string &pfspath, const std::string &mountpoint, const std::vector<uint8_t> &key);
    bool InitializePFSInternal();
    PFSBlock *GetPFSBlock(PFSFile &file, uint64_t blockIndex, bool createIfNotExist = false);
    bool FlushPFSBlock(PFSFile &file, PFSBlock &block);
    void ManagePFSCache();
    bool EncryptPfsData(const std::vector<uint8_t> &plaintext, std::vector<uint8_t> &ciphertext,
                        const std::vector<uint8_t> &key, const std::vector<uint8_t> &iv);
    bool DecryptPfsData(const std::vector<uint8_t> &ciphertext, std::vector<uint8_t> &plaintext,
                        const std::vector<uint8_t> &key, const std::vector<uint8_t> &iv);
    bool GeneratePFSKey(std::vector<uint8_t> &key, const std::string &passphrase = "");
    std::vector<uint8_t> CalculateChecksum(const std::vector<uint8_t> &data) const;

    PS4Emulator &m_emulator;
    std::string m_rootPath;
    std::string m_loadedGamePath;
    std::string m_gameDirectory;
    bool m_gameLoaded = false;

    std::unordered_map<int, FileHandle> m_fileHandles;
    std::unordered_map<std::string, FileEntry> m_files;
    std::vector<std::string> m_directories;

    std::unordered_map<std::string, PS4FileType> m_deviceFiles;
    std::unordered_map<std::string, std::function<bool(void *, size_t, bool)>> m_deviceHandlers;

    std::unordered_map<std::string, PFSFile> m_pfsFiles;
    std::unordered_map<std::string, std::string> m_pfsMounts;
    std::vector<uint8_t> m_pfsKey;
    uint64_t m_pfsCacheSize = 64 * 1024 * 1024;

    mutable std::shared_mutex m_mutex;

    int m_nextFd = 3;
    mutable FilesystemStats m_stats;

    EnhancedSettings m_settings;

    MountPointManager m_mountPoints;

    HandleTable m_handleTable;
};

} // namespace ps4
