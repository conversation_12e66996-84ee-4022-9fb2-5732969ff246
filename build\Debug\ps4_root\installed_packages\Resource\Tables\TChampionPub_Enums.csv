RSID_TCHAMPIONPUB_START, 3050,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON>\PBTChampionPub, 1,
 ,[Offset], ChampionP<PERSON>\InstructionsENG, 2,
 ,[Offset], ChampionP<PERSON>\InstructionsFR, 3,
 ,[Offset], <PERSON>P<PERSON>\InstructionsITAL, 4,
 ,[Offset], ChampionP<PERSON>\InstructionsGERM, 5,
 ,[Offset], ChampionPub\InstructionsSPAN, 6,
 ,[Offset], ChampionP<PERSON>\InstructionsPORT, 7,
 ,[Offset], ChampionPub\InstructionsDUTCH, 8,
 ,[Offset], ChampionPub\InstructionsDUTCH, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TCHAMPIONPUB_LIGHTS, 3051,,,
RSID_TCHAMPIONPUB_CAMERAS, 3052,,,
RSID_TCHAMPIONPUB_LAMP_TEXTURES, 3053,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_37_On, 46,
 ,[Offset], L_37_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_86_On, 124,
 ,[Offset], L_86_On, 125,
 ,[Offset], L_86_On, 126,
 ,[Offset], L_86_On, 127,
 ,[Offset], GI_01, 128,
 ,[Offset], GI_01_On, 129,
 ,[Offset], GI_02, 130,
 ,[Offset], GI_02_On, 131,
 ,[Offset], GI_03, 132,
 ,[Offset], GI_03_On, 133,
 ,[Offset], GI_04, 134,
 ,[Offset], GI_04_On, 135,
 ,[Offset], GI_05, 136,
 ,[Offset], GI_05_On, 137,
 ,[Offset], GI_06, 138,
 ,[Offset], GI_06_On, 139,
 ,[Offset], GI_07, 140,
 ,[Offset], GI_07_On, 141,
 ,[Offset], GI_08, 142,
 ,[Offset], GI_08_On, 143,
 ,[Offset], GI_09, 144,
 ,[Offset], GI_09_On, 145,
 ,[Offset], GI_10, 146,
 ,[Offset], GI_10_On, 147,
 ,[Offset], GI_11, 148,
 ,[Offset], GI_11_On, 149,
 ,[Offset], GI_12, 150,
 ,[Offset], GI_12_On, 151,
 ,[Offset], GI_13, 152,
 ,[Offset], GI_13_On, 153,
 ,[Offset], GI_14, 154,
 ,[Offset], GI_14_On, 155,
 ,[Offset], GI_15, 156,
 ,[Offset], GI_15_On, 157,
 ,[Offset], GI_16, 158,
 ,[Offset], GI_16_On, 159,
 ,[Offset], GI_17, 160,
 ,[Offset], GI_17_On, 161,
 ,[Offset], GI_18, 162,
 ,[Offset], GI_18_On, 163,
 ,[Offset], GI_19, 164,
 ,[Offset], GI_19_On, 165,
 ,[Offset], GI_20, 166,
 ,[Offset], GI_20_On, 167,
 ,[Offset], GI_21, 168,
 ,[Offset], GI_21_On, 169,
 ,[Offset], GI_22, 170,
 ,[Offset], GI_22_On, 171,
 ,[Offset], GI_23, 172,
 ,[Offset], GI_23_On, 173,
 ,[Offset], GI_24, 174,
 ,[Offset], GI_24_On, 175,
 ,[Offset], F_17_Off, 176,
 ,[Offset], F_17_On, 177,
 ,[Offset], F_18_Off, 178,
 ,[Offset], F_18_On, 179,
 ,[Offset], F_18_B_Off, 180,
 ,[Offset], F_18_B_On, 181,
 ,[Offset], F_22_Off, 182,
 ,[Offset], F_22_On, 183,
 ,[Offset], F_22_B_Off, 184,
 ,[Offset], F_22_B_On, 185,
 ,[Offset], F_19_Off, 186,
 ,[Offset], F_19_On, 187,
 ,[Offset], F_21_Off, 188,
 ,[Offset], F_21_On, 189,
 ,[Offset], F_23_Off, 190,
 ,[Offset], F_23_On, 191,
 ,[Offset], F_24_Off, 192,
 ,[Offset], F_24_On, 193,
 ,[Offset], F_20_Off, 194,
 ,[Offset], F_20_On, 195,
 ,[Offset], F_19_alpha_plane_off, 196,
 ,[Offset], F_19_alpha_plane, 197,
 ,[Offset], F_21_alpha_plane_off, 198,
 ,[Offset], F_21_alpha_plane, 199,
 ,[Offset], F_23_alpha_plane_off, 200,
 ,[Offset], F_23_alpha_plane_on, 201,
 ,[Offset], F_24_alpha_plane_off, 202,
 ,[Offset], F_24_alpha_plane_on, 203,
 ,[Offset], F_20_alpha_plane_off, 204,
 ,[Offset], F_20_alpha_plane, 205,
RSID_TCHAMPIONPUB_TEXTURES, 3054,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], black_metal, 1,
 ,[Offset], bulb1, 2,
 ,[Offset], Cabinet_Apron_D, 3,
 ,[Offset], Cabinet_Backglass_D, 4,
 ,[Offset], Cabinet_Front_D, 5,
 ,[Offset], Cabinet_Sides_D, 6,
 ,[Offset], catapult, 7,
 ,[Offset], CoinSlots, 8,
 ,[Offset], Divider_D, 9,
 ,[Offset], Extra_Metal_Parts, 10,
 ,[Offset], Fighter_D, 11,
 ,[Offset], Flipper, 12,
 ,[Offset], Flipper_Button, 13,
 ,[Offset], Generic_Metal, 14,
 ,[Offset], HarleyBumperBody, 15,
 ,[Offset], JabRamps_D, 16,
 ,[Offset], JumpRope_D, 17,
 ,[Offset], LaunchBall_Button, 18,
 ,[Offset], metal front, 19,
 ,[Offset], Metal_Parts, 20,
 ,[Offset], Metal_Walls, 21,
 ,[Offset], Plastic_Post_Red, 22,
 ,[Offset], Plastics_Blue_D, 23,
 ,[Offset], Plastics_Clear_D, 24,
 ,[Offset], Plastics_Green_D, 25,
 ,[Offset], Playfield_bottom, 26,
 ,[Offset], Playfield_top, 27,
 ,[Offset], PunchingBag_D, 28,
 ,[Offset], PunchingBag_D_N, 29,
 ,[Offset], PunchRamps_D, 30,
 ,[Offset], Ramp_01_D, 31,
 ,[Offset], Ramp_01_S, 32,
 ,[Offset], Ramp_02_D, 33,
 ,[Offset], red_light_off, 34,
 ,[Offset], red_light_on, 35,
 ,[Offset], Rubber_Band_Black, 36,
 ,[Offset], Silver Metal Screws_Temp, 37,
 ,[Offset], speaker, 38,
 ,[Offset], spot_light, 39,
 ,[Offset], start button, 40,
 ,[Offset], Target_Yellow, 41,
 ,[Offset], Tracks_01_D, 42,
 ,[Offset], Tracks_02_D, 43,
 ,[Offset], TrainingBag_01_D, 44,
 ,[Offset], TrainingBag_02_D, 45,
 ,[Offset], TrainingBag_03_D, 46,
 ,[Offset], yellow_Plastic_Post, 47,
 ,[Offset], black_wood, 48,
 ,[Offset], Rails, 49,
 ,[Offset], white_light_off, 50,
 ,[Offset], white_light_on, 51,
 ,[Offset], slingshot_plastics, 52,
 ,[Offset], Fighter_D_N, 53,
 ,[Offset], clear_flasher_off, 54,
 ,[Offset], red_flasher_off, 55,
 ,[Offset], MetalGate, 56,
 ,[Offset], inside_walls, 57,
RSID_TCHAMPIONPUB_MODELS, 3055,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass, 1,
 ,[Offset], black_rubber, 2,
 ,[Offset], blue_plastics, 3,
 ,[Offset], bulbs, 4,
 ,[Offset], buttons, 5,
 ,[Offset], cabinet, 6,
 ,[Offset], cabinet_metal, 7,
 ,[Offset], dark_metal, 8,
 ,[Offset], flashers, 9,
 ,[Offset], green_plastics, 10,
 ,[Offset], habi_trails, 11,
 ,[Offset], jab_ramps, 12,
 ,[Offset], metal, 13,
 ,[Offset], metal_posts, 14,
 ,[Offset], plastic_clips, 15,
 ,[Offset], plastic_ramp, 16,
 ,[Offset], platform, 17,
 ,[Offset], playfield, 18,
 ,[Offset], rails, 19,
 ,[Offset], spot_lights, 20,
 ,[Offset], transparent_plastics, 21,
 ,[Offset], vertical_plastics, 22,
 ,[Offset], yellow_posts, 23,
 ,[Offset], punch_ramps, 24,
 ,[Offset], wire, 25,
 ,[Offset], catapult, 26,
 ,[Offset], fighter, 27,
 ,[Offset], fighter_base, 28,
 ,[Offset], jump_rope, 29,
 ,[Offset], left_fist, 30,
 ,[Offset], left_flipper, 31,
 ,[Offset], left_slingshot, 32,
 ,[Offset], punching_bag, 33,
 ,[Offset], right_fist, 34,
 ,[Offset], right_flipper, 35,
 ,[Offset], right_slingshot, 36,
 ,[Offset], rollover_light, 37,
 ,[Offset], small_target, 38,
 ,[Offset], speed_bag, 39,
 ,[Offset], stopper, 40,
 ,[Offset], target, 41,
 ,[Offset], deverter, 42,
 ,[Offset], multiball_post, 43,
 ,[Offset], pop_up_post, 44,
 ,[Offset], right_slingshot_extended, 45,
 ,[Offset], left_slingshot_extended, 46,
 ,[Offset], light_cutouts, 47,
 ,[Offset], nuts_n_screws, 48,
 ,[Offset], jab_bar, 49,
 ,[Offset], head, 50,
 ,[Offset], right_arm, 51,
 ,[Offset], left_arm, 52,
 ,[Offset], one_way_gate, 53,
 ,[Offset], inside_cabinet, 54,
 ,[Offset], flasher_alpha_planes, 55,
 ,[Offset], spotlight_alpha_planes, 56,
RSID_TCHAMPIONPUB_MODELS_45K, 3056,,,
 ,[Offset], apron, 0,
 ,[Offset], backglass, 1,
 ,[Offset], black_rubber, 2,
 ,[Offset], blue_plastics, 3,
 ,[Offset], bulbs, 4,
 ,[Offset], buttons, 5,
 ,[Offset], cabinet, 6,
 ,[Offset], cabinet_metal, 7,
 ,[Offset], dark_metal, 8,
 ,[Offset], flashers, 9,
 ,[Offset], green_plastics, 10,
 ,[Offset], habi_trails, 11,
 ,[Offset], jab_ramps, 12,
 ,[Offset], metal, 13,
 ,[Offset], metal_posts, 14,
 ,[Offset], plastic_clips, 15,
 ,[Offset], plastic_ramp, 16,
 ,[Offset], platform, 17,
 ,[Offset], playfield, 18,
 ,[Offset], rails, 19,
 ,[Offset], spot_lights, 20,
 ,[Offset], transparent_plastics, 21,
 ,[Offset], vertical_plastics, 22,
 ,[Offset], yellow_posts, 23,
 ,[Offset], punch_ramps, 24,
 ,[Offset], wire, 25,
 ,[Offset], catapult, 26,
 ,[Offset], fighter, 27,
 ,[Offset], fighter_base, 28,
 ,[Offset], jump_rope, 29,
 ,[Offset], left_fist, 30,
 ,[Offset], left_flipper, 31,
 ,[Offset], left_slingshot, 32,
 ,[Offset], punching_bag, 33,
 ,[Offset], right_fist, 34,
 ,[Offset], right_flipper, 35,
 ,[Offset], right_slingshot, 36,
 ,[Offset], rollover_light, 37,
 ,[Offset], small_target, 38,
 ,[Offset], speed_bag, 39,
 ,[Offset], stopper, 40,
 ,[Offset], target, 41,
 ,[Offset], deverter, 42,
 ,[Offset], multiball_post, 43,
 ,[Offset], pop_up_post, 44,
 ,[Offset], right_slingshot_extended, 45,
 ,[Offset], left_slingshot_extended, 46,
 ,[Offset], light_cutouts, 47,
 ,[Offset], nuts_n_screws, 48,
 ,[Offset], jab_bar, 49,
 ,[Offset], head, 50,
 ,[Offset], right_arm, 51,
 ,[Offset], left_arm, 52,
 ,[Offset], one_way_gate, 53,
 ,[Offset], inside_cabinet, 54,
 ,[Offset], flasher_alpha_planes, 55,
 ,[Offset], spotlight_alpha_planes, 56,
RSID_TCHAMPIONPUB_REF_MODELS, 3057,,,
RSID_TCHAMPIONPUB_COLLISION, 3058,,,
 ,[Offset], apron, 0,
 ,[Offset], ball_drain, 1,
 ,[Offset], left_habitrail, 2,
 ,[Offset], left_hook, 3,
 ,[Offset], left_outlane, 4,
 ,[Offset], left_slingshot, 5,
 ,[Offset], metal_rails, 6,
 ,[Offset], plastic_ramp, 7,
 ,[Offset], plunger_habitrail, 8,
 ,[Offset], punch_ramps, 9,
 ,[Offset], right_hook, 10,
 ,[Offset], right_outlane, 11,
 ,[Offset], right_platform, 12,
 ,[Offset], right_slingshot, 13,
 ,[Offset], rubber1, 14,
 ,[Offset], rubber2, 15,
 ,[Offset], rubber3, 16,
 ,[Offset], rubber4, 17,
 ,[Offset], small_habitrail, 18,
 ,[Offset], the_ring, 19,
 ,[Offset], wall1, 20,
 ,[Offset], wall2, 21,
 ,[Offset], wall3, 22,
 ,[Offset], wall4, 23,
 ,[Offset], speed_bag, 24,
 ,[Offset], catapult_col, 25,
 ,[Offset], fighter, 26,
 ,[Offset], fighter_bracket, 27,
 ,[Offset], jab_ramp, 28,
 ,[Offset], jump_rope, 29,
 ,[Offset], left_fist, 30,
 ,[Offset], left_flipper_back, 31,
 ,[Offset], left_flipper_front, 32,
 ,[Offset], punching_bag, 33,
 ,[Offset], right_fist, 34,
 ,[Offset], right_flipper_back, 35,
 ,[Offset], right_flipper_front, 36,
 ,[Offset], target_col, 37,
 ,[Offset], playfield, 38,
 ,[Offset], wall5, 39,
 ,[Offset], trap, 40,
 ,[Offset], optic, 41,
 ,[Offset], deverter, 42,
 ,[Offset], miltiball_post, 43,
 ,[Offset], pop_up_post, 44,
 ,[Offset], jab_rampwalls1, 45,
 ,[Offset], jab_rampwalls2, 46,
 ,[Offset], stopper, 47,
 ,[Offset], speed_bag_post, 48,
 ,[Offset], left_platform, 49,
 ,[Offset], head, 50,
 ,[Offset], right_arm, 51,
 ,[Offset], left_arm, 52,
 ,[Offset], one_way_gate_front, 53,
 ,[Offset], one_way_gate_back, 54,
 ,[Offset], cornertrap_col, 55,
 ,[Offset], kicker, 56,
 ,[Offset], cornertrap_back, 57,
RSID_TCHAMPIONPUB_PLACEMENT, 3059,,,
RSID_TCHAMPIONPUB_EMUROM, 3060,,,
 ,[Offset], cp_16, 0,
 ,[Offset], cp_16, 1,
 ,[Offset], cp_default, 2,
 ,[Offset], cp_16, 3,
 ,[Offset], cp_16, 4,
RSID_TCHAMPIONPUB_SOUNDS_START, 3061,,,
RSID_TCHAMPIONPUB_EMU_SOUNDS, 3062,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0004-LP1, 1,
 ,[Offset], S0004-LP2, 2,
 ,[Offset], S0006-LP, 3,
 ,[Offset], S0007_C3, 4,
 ,[Offset], S0008-LP1, 5,
 ,[Offset], S0008-LP2, 6,
 ,[Offset], S0009_C3, 7,
 ,[Offset], S000B_C3, 8,
 ,[Offset], S000C_C4, 9,
 ,[Offset], S000D_C4, 10,
 ,[Offset], S000E_C4, 11,
 ,[Offset], S000F-LP1, 12,
 ,[Offset], S000F-LP2, 13,
 ,[Offset], S0010-LP, 14,
 ,[Offset], S0011-LP, 15,
 ,[Offset], S0012-LP, 16,
 ,[Offset], S0013-LP1, 17,
 ,[Offset], S0014-LP, 18,
 ,[Offset], S0016-LP1, 19,
 ,[Offset], S0017-LP1, 20,
 ,[Offset], S0018-LP, 21,
 ,[Offset], S0019-LP, 22,
 ,[Offset], S001A-LP, 23,
 ,[Offset], S001B-LP1, 24,
 ,[Offset], S001B-LP2, 25,
 ,[Offset], S001C-LP1, 26,
 ,[Offset], S001C-LP2, 27,
 ,[Offset], S001D_C3, 28,
 ,[Offset], S001E_C3, 29,
 ,[Offset], S001F-LP, 30,
 ,[Offset], S0020-LP, 31,
 ,[Offset], S0021_C3, 32,
 ,[Offset], S0023-LP, 33,
 ,[Offset], S0024-LP, 34,
 ,[Offset], S0028_C3, 35,
 ,[Offset], S0029_C3, 36,
 ,[Offset], S002A_C3, 37,
 ,[Offset], S002B_C3, 38,
 ,[Offset], S002C_C3, 39,
 ,[Offset], S002D_C3, 40,
 ,[Offset], S002E_C3, 41,
 ,[Offset], S002F_C3, 42,
 ,[Offset], S0030_C3, 43,
 ,[Offset], S0031_C3, 44,
 ,[Offset], S0065_C4, 45,
 ,[Offset], S0067_C4, 46,
 ,[Offset], S0068_C4, 47,
 ,[Offset], S0069_C4, 48,
 ,[Offset], S006A_C4, 49,
 ,[Offset], S006C_C4, 50,
 ,[Offset], S006D_C4, 51,
 ,[Offset], S006E_C4, 52,
 ,[Offset], S006F_C4, 53,
 ,[Offset], S0070_C4, 54,
 ,[Offset], S0071_C4, 55,
 ,[Offset], S0072_C4, 56,
 ,[Offset], S0073_C4, 57,
 ,[Offset], S0074_C4, 58,
 ,[Offset], S0075_C4, 59,
 ,[Offset], S0076_C1, 60,
 ,[Offset], S0079_C4, 61,
 ,[Offset], S007A_C4, 62,
 ,[Offset], S007B_C4, 63,
 ,[Offset], S007C_C4, 64,
 ,[Offset], S007D_C4, 65,
 ,[Offset], S007E_C4, 66,
 ,[Offset], S007F-LP, 67,
 ,[Offset], S0080_C4, 68,
 ,[Offset], S0081_C4, 69,
 ,[Offset], S0082_C4, 70,
 ,[Offset], S0083_C4, 71,
 ,[Offset], S0084_C4, 72,
 ,[Offset], S0085_C4, 73,
 ,[Offset], S0086_C4, 74,
 ,[Offset], S0087_C4, 75,
 ,[Offset], S0088_C4, 76,
 ,[Offset], S0089_C4, 77,
 ,[Offset], S008A_C4, 78,
 ,[Offset], S008B_C4, 79,
 ,[Offset], S008C_C1, 80,
 ,[Offset], S008D_C4, 81,
 ,[Offset], S008F_C4, 82,
 ,[Offset], S0090_C4, 83,
 ,[Offset], S0091_C4, 84,
 ,[Offset], S0094_C4, 85,
 ,[Offset], S0095_C1, 86,
 ,[Offset], S0098_C6, 87,
 ,[Offset], S0099_C6, 88,
 ,[Offset], S009A_C1, 89,
 ,[Offset], S009C_C4, 90,
 ,[Offset], S009D_C4, 91,
 ,[Offset], S009E_C4, 92,
 ,[Offset], S009F_C2, 93,
 ,[Offset], S00A2_C6, 94,
 ,[Offset], S00A3_C6, 95,
 ,[Offset], S00A7_C4, 96,
 ,[Offset], S00A8_C4, 97,
 ,[Offset], S00A9_C6, 98,
 ,[Offset], S00AA_C1, 99,
 ,[Offset], S00AB_C4, 100,
 ,[Offset], S00AC_C4, 101,
 ,[Offset], S00AD_C4, 102,
 ,[Offset], S00AE_C4, 103,
 ,[Offset], S00AF_C4, 104,
 ,[Offset], S00B0_C4, 105,
 ,[Offset], S00B1_C4, 106,
 ,[Offset], S00B5_C6, 107,
 ,[Offset], S00B9_C1, 108,
 ,[Offset], S00BA_C1, 109,
 ,[Offset], S00BE_C4, 110,
 ,[Offset], S00C3_C4, 111,
 ,[Offset], S00C8_C4, 112,
 ,[Offset], S00CD_C4, 113,
 ,[Offset], S00D2_C4, 114,
 ,[Offset], S00D7_C4, 115,
 ,[Offset], S00D8_C6, 116,
 ,[Offset], S00D9_C4, 117,
 ,[Offset], S00E0_C4, 118,
 ,[Offset], S00E2_C4, 119,
 ,[Offset], S00E3_C4, 120,
 ,[Offset], S00E4_C4, 121,
 ,[Offset], S00E5_C4, 122,
 ,[Offset], S00E6_C4, 123,
 ,[Offset], S00E9_C6, 124,
 ,[Offset], S00EB_C6, 125,
 ,[Offset], S00ED_C6, 126,
 ,[Offset], S00EF_C6, 127,
 ,[Offset], S00F5_C4, 128,
 ,[Offset], S00F6_C4, 129,
 ,[Offset], S00F7_C6, 130,
 ,[Offset], S00F8_C4, 131,
 ,[Offset], S00F9-LP1, 132,
 ,[Offset], S00F9-LP2, 133,
 ,[Offset], S00FA_C4, 134,
 ,[Offset], S00FB_C4, 135,
 ,[Offset], S00FC_C4, 136,
 ,[Offset], S00FD_C4, 137,
 ,[Offset], S00FE_C4, 138,
 ,[Offset], S00FF_C4, 139,
 ,[Offset], S0100_C2, 140,
 ,[Offset], S0101_C4, 141,
 ,[Offset], S0102_C4, 142,
 ,[Offset], S0103_C4, 143,
 ,[Offset], S0104_C4, 144,
 ,[Offset], S0105_C4, 145,
 ,[Offset], S0106_C1, 146,
 ,[Offset], S0107_C1, 147,
 ,[Offset], S0108_C1, 148,
 ,[Offset], S010A_C1, 149,
 ,[Offset], S010B_C1, 150,
 ,[Offset], S010C_C1, 151,
 ,[Offset], S0112_C4, 152,
 ,[Offset], S0113_C1, 153,
 ,[Offset], S0114_C4, 154,
 ,[Offset], S0115_C4, 155,
 ,[Offset], S0116_C4, 156,
 ,[Offset], S0117_C2, 157,
 ,[Offset], S0118_C4, 158,
 ,[Offset], S0119_C4, 159,
 ,[Offset], S011B_C4, 160,
 ,[Offset], S011C_C4, 161,
 ,[Offset], S011E_C4, 162,
 ,[Offset], S011F_C2, 163,
 ,[Offset], S0120_C1, 164,
 ,[Offset], S0121_C1, 165,
 ,[Offset], S0122_C4, 166,
 ,[Offset], S0124_C1, 167,
 ,[Offset], S0125_C6, 168,
 ,[Offset], S0126_C6, 169,
 ,[Offset], S012A_C4, 170,
 ,[Offset], S012B_C6, 171,
 ,[Offset], S012C_C4, 172,
 ,[Offset], S012D_C4, 173,
 ,[Offset], S03D4_C-1, 174,
 ,[Offset], S03D5_C-1, 175,
 ,[Offset], S03D6_C5, 176,
 ,[Offset], S03D7_C5, 177,
 ,[Offset], S03D8_C5, 178,
 ,[Offset], S03D9_C-1, 179,
 ,[Offset], S03DA_C-1, 180,
 ,[Offset], S03DB_C5, 181,
 ,[Offset], S03DC_C5, 182,
 ,[Offset], S03DD_C5, 183,
 ,[Offset], S03DE_C-1, 184,
 ,[Offset], S044C_C5, 185,
 ,[Offset], S044D_C5, 186,
 ,[Offset], S044E_C5, 187,
 ,[Offset], S0450_C5, 188,
 ,[Offset], S0451_C5, 189,
 ,[Offset], S0452_C5, 190,
 ,[Offset], S0453_C5, 191,
 ,[Offset], S0454_C5, 192,
 ,[Offset], S0455_C5, 193,
 ,[Offset], S0456_C5, 194,
 ,[Offset], S0457_C5, 195,
 ,[Offset], S0458_C5, 196,
 ,[Offset], S0459_C5, 197,
 ,[Offset], S045A_C5, 198,
 ,[Offset], S045B_C5, 199,
 ,[Offset], S045C_C5, 200,
 ,[Offset], S045D_C5, 201,
 ,[Offset], S045E_C5, 202,
 ,[Offset], S0460_C5, 203,
 ,[Offset], S0461_C5, 204,
 ,[Offset], S0462_C5, 205,
 ,[Offset], S0463_C5, 206,
 ,[Offset], S0464_C5, 207,
 ,[Offset], S0465_C5, 208,
 ,[Offset], S0466_C5, 209,
 ,[Offset], S0467_C5, 210,
 ,[Offset], S046A_C5, 211,
 ,[Offset], S046B_C5, 212,
 ,[Offset], S046C_C5, 213,
 ,[Offset], S046D_C5, 214,
 ,[Offset], S046E_C5, 215,
 ,[Offset], S046F_C5, 216,
 ,[Offset], S0471_C5, 217,
 ,[Offset], S0472_C5, 218,
 ,[Offset], S0474_C5, 219,
 ,[Offset], S0475_C5, 220,
 ,[Offset], S0476_C5, 221,
 ,[Offset], S0477_C5, 222,
 ,[Offset], S0478_C5, 223,
 ,[Offset], S0479_C5, 224,
 ,[Offset], S047A_C5, 225,
 ,[Offset], S047B_C5, 226,
 ,[Offset], S047C_C5, 227,
 ,[Offset], S047D_C5, 228,
 ,[Offset], S047E_C5, 229,
 ,[Offset], S047F_C5, 230,
 ,[Offset], S0480_C5, 231,
 ,[Offset], S0481_C5, 232,
 ,[Offset], S0482_C5, 233,
 ,[Offset], S0483_C5, 234,
 ,[Offset], S0484_C5, 235,
 ,[Offset], S0485_C5, 236,
 ,[Offset], S0486_C5, 237,
 ,[Offset], S04A6_C5, 238,
 ,[Offset], S04A7_C5, 239,
 ,[Offset], S04A8_C5, 240,
 ,[Offset], S04A9_C5, 241,
 ,[Offset], S04AA_C5, 242,
 ,[Offset], S04AB_C5, 243,
 ,[Offset], S04AC_C5, 244,
 ,[Offset], S04AD_C5, 245,
 ,[Offset], S04AF_C5, 246,
 ,[Offset], S04B0_C5, 247,
 ,[Offset], S04B1_C5, 248,
 ,[Offset], S04B2_C5, 249,
 ,[Offset], S04B3_C5, 250,
 ,[Offset], S04B4_C5, 251,
 ,[Offset], S04B5_C5, 252,
 ,[Offset], S04B6_C5, 253,
 ,[Offset], S04B7_C5, 254,
 ,[Offset], S04B8_C5, 255,
 ,[Offset], S04B9_C5, 256,
 ,[Offset], S04BB_C5, 257,
 ,[Offset], S04BC_C5, 258,
 ,[Offset], S04BE_C5, 259,
 ,[Offset], S04BF_C5, 260,
 ,[Offset], S04C0_C5, 261,
 ,[Offset], S04C1_C5, 262,
 ,[Offset], S04C2_C5, 263,
 ,[Offset], S04C4_C5, 264,
 ,[Offset], S04C5_C5, 265,
 ,[Offset], S04C6_C5, 266,
 ,[Offset], S04C7_C5, 267,
 ,[Offset], S04C8_C5, 268,
 ,[Offset], S04C9_C5, 269,
 ,[Offset], S04CA_C5, 270,
 ,[Offset], S04CB_C5, 271,
 ,[Offset], S04CC_C5, 272,
 ,[Offset], S04CD_C5, 273,
 ,[Offset], S04CE_C5, 274,
 ,[Offset], S04CF_C5, 275,
 ,[Offset], S04D0_C5, 276,
 ,[Offset], S04D1_C5, 277,
 ,[Offset], S04D2_C5, 278,
 ,[Offset], S04D3_C5, 279,
 ,[Offset], S04D4_C5, 280,
 ,[Offset], S04D5_C5, 281,
 ,[Offset], S04D6_C5, 282,
 ,[Offset], S04D7_C5, 283,
 ,[Offset], S04D8_C5, 284,
 ,[Offset], S04D9_C5, 285,
 ,[Offset], S04DB_C5, 286,
 ,[Offset], S04DC_C5, 287,
 ,[Offset], S04DD_C5, 288,
 ,[Offset], S04DE_C5, 289,
 ,[Offset], S04E2_C5, 290,
 ,[Offset], S04E3_C5, 291,
 ,[Offset], S04E4_C5, 292,
 ,[Offset], S04E5_C5, 293,
 ,[Offset], S04E6_C5, 294,
 ,[Offset], S04E7_C5, 295,
 ,[Offset], S04E8_C5, 296,
 ,[Offset], S04E9_C5, 297,
 ,[Offset], S04EA_C5, 298,
 ,[Offset], S04EB_C5, 299,
 ,[Offset], S04EC_C5, 300,
 ,[Offset], S04ED_C5, 301,
 ,[Offset], S04EE_C5, 302,
 ,[Offset], S04EF_C5, 303,
 ,[Offset], S04F0_C5, 304,
 ,[Offset], S04F1_C5, 305,
 ,[Offset], S04F2_C5, 306,
 ,[Offset], S04F3_C5, 307,
 ,[Offset], S04F4_C5, 308,
 ,[Offset], S04F6_C5, 309,
 ,[Offset], S04F7_C5, 310,
 ,[Offset], S04F8_C5, 311,
 ,[Offset], S04F9_C5, 312,
 ,[Offset], S04FA_C5, 313,
 ,[Offset], S04FC_C5, 314,
 ,[Offset], S04FD_C5, 315,
 ,[Offset], S04FE_C5, 316,
 ,[Offset], S04FF_C5, 317,
 ,[Offset], S0500_C5, 318,
 ,[Offset], S0501_C5, 319,
 ,[Offset], S0502_C5, 320,
 ,[Offset], S0503_C5, 321,
 ,[Offset], S0504_C5, 322,
 ,[Offset], S0505_C5, 323,
 ,[Offset], S0506_C5, 324,
 ,[Offset], S0507_C5, 325,
 ,[Offset], S0508_C5, 326,
 ,[Offset], S0509_C5, 327,
 ,[Offset], S050A_C5, 328,
 ,[Offset], S050B_C5, 329,
 ,[Offset], S050C_C5, 330,
 ,[Offset], S050D_C5, 331,
 ,[Offset], S050F_C5, 332,
 ,[Offset], S0510_C5, 333,
 ,[Offset], S0512_C5, 334,
 ,[Offset], S0513_C5, 335,
 ,[Offset], S0514_C5, 336,
 ,[Offset], S0515_C5, 337,
 ,[Offset], S0516_C5, 338,
 ,[Offset], S0517_C5, 339,
 ,[Offset], S0518_C5, 340,
 ,[Offset], S0519_C5, 341,
 ,[Offset], S051A_C5, 342,
 ,[Offset], S051B_C5, 343,
 ,[Offset], S051D_C5, 344,
 ,[Offset], S051E_C5, 345,
 ,[Offset], S051F_C5, 346,
 ,[Offset], S0520_C5, 347,
 ,[Offset], S0521_C5, 348,
 ,[Offset], S0522_C5, 349,
 ,[Offset], S0523_C5, 350,
 ,[Offset], S0524_C5, 351,
 ,[Offset], S0525_C5, 352,
 ,[Offset], S0526_C5, 353,
 ,[Offset], S0527_C5, 354,
 ,[Offset], S0528_C5, 355,
 ,[Offset], S0529_C5, 356,
 ,[Offset], S052A_C5, 357,
 ,[Offset], S052B_C5, 358,
 ,[Offset], S052C_C5, 359,
 ,[Offset], S052D_C5, 360,
 ,[Offset], S052E_C5, 361,
 ,[Offset], S052F_C5, 362,
 ,[Offset], S0530_C5, 363,
 ,[Offset], S0532_C5, 364,
 ,[Offset], S0533_C5, 365,
 ,[Offset], S0534_C5, 366,
 ,[Offset], S0535_C5, 367,
 ,[Offset], S0536_C5, 368,
 ,[Offset], S0537_C5, 369,
 ,[Offset], S0538_C5, 370,
 ,[Offset], S053A_C5, 371,
 ,[Offset], S053B_C5, 372,
 ,[Offset], S053C_C5, 373,
 ,[Offset], S053D_C5, 374,
 ,[Offset], S053E_C5, 375,
 ,[Offset], S053F_C5, 376,
 ,[Offset], S0540_C5, 377,
 ,[Offset], S0541_C5, 378,
 ,[Offset], S0542_C5, 379,
 ,[Offset], S0543_C5, 380,
 ,[Offset], S0544_C5, 381,
 ,[Offset], S0546_C5, 382,
 ,[Offset], S0548_C5, 383,
 ,[Offset], S0549_C5, 384,
 ,[Offset], S054A_C5, 385,
 ,[Offset], S054B_C5, 386,
 ,[Offset], S054D_C5, 387,
 ,[Offset], S054E_C5, 388,
 ,[Offset], S054F_C5, 389,
 ,[Offset], S0550_C5, 390,
 ,[Offset], S0551_C5, 391,
 ,[Offset], S0552_C5, 392,
 ,[Offset], S0553_C5, 393,
 ,[Offset], S0554_C5, 394,
 ,[Offset], S0555_C5, 395,
 ,[Offset], S0556_C5, 396,
 ,[Offset], S0557_C5, 397,
 ,[Offset], S0558_C5, 398,
 ,[Offset], S0559_C5, 399,
 ,[Offset], S055A_C5, 400,
 ,[Offset], S055B_C5, 401,
 ,[Offset], S055C_C5, 402,
 ,[Offset], S055D_C5, 403,
 ,[Offset], S055E_C5, 404,
 ,[Offset], S055F_C5, 405,
 ,[Offset], S0560_C5, 406,
 ,[Offset], S0561_C5, 407,
 ,[Offset], S0562_C5, 408,
 ,[Offset], S0563_C5, 409,
 ,[Offset], S0564_C5, 410,
 ,[Offset], S0565_C5, 411,
 ,[Offset], S0566_C5, 412,
 ,[Offset], S0567_C5, 413,
 ,[Offset], S0568_C5, 414,
 ,[Offset], S0569_C5, 415,
 ,[Offset], S056A_C5, 416,
 ,[Offset], S056B_C5, 417,
 ,[Offset], S056C_C5, 418,
 ,[Offset], S056D_C5, 419,
 ,[Offset], S056E_C5, 420,
 ,[Offset], S056F_C5, 421,
 ,[Offset], S0570_C5, 422,
 ,[Offset], S0572_C5, 423,
 ,[Offset], S0573_C5, 424,
 ,[Offset], S0574_C5, 425,
 ,[Offset], S0575_C5, 426,
 ,[Offset], S0576_C5, 427,
 ,[Offset], S0578_C5, 428,
 ,[Offset], S0579_C5, 429,
 ,[Offset], S057A_C5, 430,
 ,[Offset], S057B_C5, 431,
 ,[Offset], S057C_C5, 432,
 ,[Offset], S057D_C5, 433,
 ,[Offset], S057E_C5, 434,
 ,[Offset], S057F_C5, 435,
 ,[Offset], S0580_C5, 436,
 ,[Offset], S0581_C5, 437,
 ,[Offset], S0582_C5, 438,
 ,[Offset], S0583_C5, 439,
 ,[Offset], S0584_C5, 440,
 ,[Offset], S0585_C5, 441,
 ,[Offset], S0586_C5, 442,
 ,[Offset], S0587_C5, 443,
 ,[Offset], S0588_C5, 444,
 ,[Offset], S0589_C5, 445,
 ,[Offset], S058A_C5, 446,
 ,[Offset], S058B_C5, 447,
 ,[Offset], S058C_C5, 448,
 ,[Offset], S058D_C5, 449,
 ,[Offset], S058E_C5, 450,
 ,[Offset], S058F_C5, 451,
 ,[Offset], S0590_C5, 452,
 ,[Offset], S0591_C5, 453,
 ,[Offset], S0592_C5, 454,
 ,[Offset], S05AA_C5, 455,
 ,[Offset], S05AB_C5, 456,
 ,[Offset], S05AC_C5, 457,
 ,[Offset], S05AD_C5, 458,
 ,[Offset], S05AE_C5, 459,
 ,[Offset], S05AF_C5, 460,
 ,[Offset], S05B0_C5, 461,
 ,[Offset], S05B1_C5, 462,
 ,[Offset], S05B2_C5, 463,
 ,[Offset], S05B3_C5, 464,
 ,[Offset], S05B4_C5, 465,
 ,[Offset], S05B6_C5, 466,
 ,[Offset], S05B7_C5, 467,
 ,[Offset], S05B8_C5, 468,
 ,[Offset], S05BA_C5, 469,
 ,[Offset], S05BB_C5, 470,
 ,[Offset], S05BC_C5, 471,
 ,[Offset], S05BD_C5, 472,
 ,[Offset], S05BE_C5, 473,
 ,[Offset], S05BF_C5, 474,
 ,[Offset], S05C0_C5, 475,
 ,[Offset], S05C1_C5, 476,
 ,[Offset], S05C2_C5, 477,
 ,[Offset], S05C3_C5, 478,
 ,[Offset], S05DC_C5, 479,
 ,[Offset], S05DD_C5, 480,
 ,[Offset], S05DE_C5, 481,
 ,[Offset], S05DF_C5, 482,
 ,[Offset], S05E0_C5, 483,
 ,[Offset], S05E1_C5, 484,
 ,[Offset], S05E2_C5, 485,
 ,[Offset], S05E3_C5, 486,
 ,[Offset], S05E4_C5, 487,
 ,[Offset], S05E5_C5, 488,
 ,[Offset], S05E6_C5, 489,
 ,[Offset], S05E7_C5, 490,
 ,[Offset], S05E8_C5, 491,
 ,[Offset], S05E9_C5, 492,
 ,[Offset], S05EA_C5, 493,
 ,[Offset], S060E_C5, 494,
 ,[Offset], S060F_C5, 495,
 ,[Offset], S0610_C5, 496,
 ,[Offset], S0611_C5, 497,
 ,[Offset], S0612_C5, 498,
 ,[Offset], S0613_C5, 499,
 ,[Offset], S0614_C5, 500,
 ,[Offset], S0615_C5, 501,
 ,[Offset], S0616_C5, 502,
 ,[Offset], S0617_C5, 503,
 ,[Offset], S0618_C5, 504,
 ,[Offset], S0619_C5, 505,
 ,[Offset], S061A_C5, 506,
 ,[Offset], S061B_C5, 507,
 ,[Offset], S061C_C5, 508,
 ,[Offset], S0640_C5, 509,
 ,[Offset], S0641_C5, 510,
 ,[Offset], S0642_C5, 511,
 ,[Offset], S0643_C5, 512,
 ,[Offset], S0644_C5, 513,
 ,[Offset], S0645_C5, 514,
 ,[Offset], S0647_C5, 515,
 ,[Offset], S0648_C5, 516,
 ,[Offset], S0649_C5, 517,
 ,[Offset], S064A_C5, 518,
 ,[Offset], S064B_C5, 519,
 ,[Offset], S064C_C5, 520,
 ,[Offset], S064D_C5, 521,
 ,[Offset], S064E_C5, 522,
 ,[Offset], S064F_C5, 523,
 ,[Offset], S0650_C5, 524,
 ,[Offset], S0652_C5, 525,
 ,[Offset], S0653_C5, 526,
 ,[Offset], S0654_C5, 527,
 ,[Offset], S0655_C5, 528,
 ,[Offset], S0656_C5, 529,
 ,[Offset], S0657_C5, 530,
 ,[Offset], S0658_C5, 531,
 ,[Offset], S0659_C5, 532,
 ,[Offset], S065A_C5, 533,
 ,[Offset], S065B_C5, 534,
 ,[Offset], S065D_C5, 535,
 ,[Offset], S065E_C5, 536,
 ,[Offset], S065F_C5, 537,
 ,[Offset], S0660_C5, 538,
 ,[Offset], S0661_C5, 539,
 ,[Offset], S0662_C5, 540,
 ,[Offset], S0663_C5, 541,
 ,[Offset], S0664_C5, 542,
 ,[Offset], S0665_C5, 543,
 ,[Offset], S0666_C5, 544,
 ,[Offset], S0668_C5, 545,
 ,[Offset], S0669_C5, 546,
 ,[Offset], S066A_C5, 547,
 ,[Offset], S066B_C5, 548,
 ,[Offset], S066C_C5, 549,
 ,[Offset], S066D_C5, 550,
 ,[Offset], S066E_C5, 551,
 ,[Offset], S066F_C5, 552,
 ,[Offset], S0670_C5, 553,
 ,[Offset], S067E_C5, 554,
 ,[Offset], S067F_C5, 555,
 ,[Offset], S0680_C5, 556,
 ,[Offset], S0681_C5, 557,
 ,[Offset], S0682_C5, 558,
 ,[Offset], S0683_C5, 559,
 ,[Offset], S0684_C5, 560,
 ,[Offset], S0685_C5, 561,
 ,[Offset], S0686_C5, 562,
 ,[Offset], S0687_C5, 563,
 ,[Offset], S0688_C5, 564,
 ,[Offset], S0689_C5, 565,
 ,[Offset], S068A_C5, 566,
 ,[Offset], S068B_C5, 567,
 ,[Offset], S068C_C5, 568,
 ,[Offset], S068D_C5, 569,
 ,[Offset], S068E_C5, 570,
 ,[Offset], S068F_C5, 571,
 ,[Offset], S0690_C5, 572,
 ,[Offset], S0691_C5, 573,
 ,[Offset], S06A4_C5, 574,
 ,[Offset], S06A5_C5, 575,
 ,[Offset], S06A6_C5, 576,
 ,[Offset], S06A7_C5, 577,
 ,[Offset], S06A8_C5, 578,
 ,[Offset], S06A9_C5, 579,
 ,[Offset], S06AA_C5, 580,
 ,[Offset], S06AB_C5, 581,
 ,[Offset], S06AC_C5, 582,
 ,[Offset], S06AD_C5, 583,
 ,[Offset], S06AE_C5, 584,
 ,[Offset], S06AF_C5, 585,
 ,[Offset], S06B0_C5, 586,
 ,[Offset], S06B1_C5, 587,
 ,[Offset], S06B2_C5, 588,
 ,[Offset], S0708_C5, 589,
 ,[Offset], S0709_C5, 590,
 ,[Offset], S070A_C5, 591,
 ,[Offset], S070B_C5, 592,
 ,[Offset], S070C_C5, 593,
 ,[Offset], S070D_C5, 594,
 ,[Offset], S070E_C5, 595,
 ,[Offset], S070F_C5, 596,
 ,[Offset], S0710_C5, 597,
 ,[Offset], S0711_C5, 598,
 ,[Offset], S0712_C5, 599,
 ,[Offset], S0713_C5, 600,
 ,[Offset], S0714_C5, 601,
 ,[Offset], S0715_C5, 602,
 ,[Offset], S0716_C5, 603,
 ,[Offset], S0717_C5, 604,
 ,[Offset], S0718_C5, 605,
 ,[Offset], S0719_C5, 606,
 ,[Offset], S071A_C5, 607,
 ,[Offset], S071B_C5, 608,
 ,[Offset], S071C_C5, 609,
 ,[Offset], S071D_C5, 610,
 ,[Offset], S071E_C5, 611,
 ,[Offset], S071F_C5, 612,
 ,[Offset], S0720_C5, 613,
 ,[Offset], S0721_C5, 614,
 ,[Offset], S0722_C5, 615,
 ,[Offset], S0723_C5, 616,
 ,[Offset], S0724_C5, 617,
 ,[Offset], S0725_C5, 618,
 ,[Offset], S0726_C5, 619,
 ,[Offset], S0727_C5, 620,
 ,[Offset], S0728_C5, 621,
 ,[Offset], S072A_C5, 622,
 ,[Offset], S072B_C5, 623,
 ,[Offset], S072C_C5, 624,
 ,[Offset], S072D_C5, 625,
 ,[Offset], S072E_C5, 626,
 ,[Offset], S072F_C5, 627,
 ,[Offset], S0730_C5, 628,
 ,[Offset], S0731_C5, 629,
 ,[Offset], S0732_C5, 630,
 ,[Offset], S0733_C5, 631,
 ,[Offset], S0734_C5, 632,
 ,[Offset], S0735_C5, 633,
 ,[Offset], S0736_C5, 634,
 ,[Offset], S0737_C5, 635,
 ,[Offset], S0738_C5, 636,
 ,[Offset], S0739_C5, 637,
 ,[Offset], S073A_C5, 638,
 ,[Offset], S073B_C5, 639,
 ,[Offset], S073C_C5, 640,
 ,[Offset], S073D_C5, 641,
 ,[Offset], S073E_C5, 642,
 ,[Offset], S073F_C5, 643,
 ,[Offset], S0740_C5, 644,
 ,[Offset], S0741_C5, 645,
 ,[Offset], S0742_C5, 646,
 ,[Offset], S0743_C5, 647,
 ,[Offset], S0744_C5, 648,
 ,[Offset], S0745_C5, 649,
 ,[Offset], S0746_C5, 650,
 ,[Offset], S0747_C5, 651,
 ,[Offset], S0748_C5, 652,
 ,[Offset], S0749_C5, 653,
 ,[Offset], S074A_C5, 654,
 ,[Offset], S074C_C5, 655,
 ,[Offset], S074D_C5, 656,
 ,[Offset], S074E_C5, 657,
 ,[Offset], S074F_C5, 658,
 ,[Offset], S0750_C5, 659,
 ,[Offset], S0751_C5, 660,
 ,[Offset], S0752_C5, 661,
 ,[Offset], S0753_C5, 662,
 ,[Offset], S0754_C5, 663,
 ,[Offset], S0755_C5, 664,
RSID_TCHAMPIONPUB_MECH_SOUNDS, 3063,,,
 ,[Offset], boxer_left_arm, 0,
 ,[Offset], boxer_right_arm, 1,
 ,[Offset], boxer_turn_around, 2,
 ,[Offset], boxer_turn_left, 3,
 ,[Offset], boxer_turn_right, 4,
 ,[Offset], corner_kickout, 5,
 ,[Offset], drain_post_down, 6,
 ,[Offset], drain_post_up, 7,
 ,[Offset], jab_ramps_up, 8,
 ,[Offset], jumprope_motor_slow_LP1, 9,
 ,[Offset], jumprope_motor_slow_LP2, 10,
 ,[Offset], jumprope_popper, 11,
 ,[Offset], left_jab_ramp_down, 12,
 ,[Offset], left_speed_bag_fist, 13,
 ,[Offset], rear_post_diverter_down, 14,
 ,[Offset], rear_post_diverter_up, 15,
 ,[Offset], right_jab_ramp_down, 16,
 ,[Offset], right_speed_bag_fist, 17,
 ,[Offset], speed_bag_diverter_down, 18,
 ,[Offset], speed_bag_diverter_up, 19,
 ,[Offset], jumprope_motor_med_LP2, 20,
 ,[Offset], jumprope_motor_fast_LP2, 21,
RSID_TCHAMPIONPUB_SOUNDS_END, 3064,,,
RSID_TCHAMPIONPUB_SAMPLES, 3065,,,
RSID_TCHAMPIONPUB_VERSION, 3066,,,
RSID_TCHAMPIONPUB_END, 3067,,,
