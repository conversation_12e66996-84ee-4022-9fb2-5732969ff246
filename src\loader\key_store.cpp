#include "key_store.h"
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <algorithm>

namespace ps4 {

KeyStore::KeyStore() : initialized_(false) {
  spdlog::info("KeyStore initialized");
}

KeyStore::~KeyStore() {
  keys_.clear();
  ivs_.clear();
  std::fill(last_error_.begin(), last_error_.end(), 0);
  spdlog::info("KeyStore destroyed");
}

bool KeyStore::Initialize(const std::string& master_key, const std::string& key_file_path) {
  try {
    if (master_key.size() < 32) {
      SetError("Master key must be at least 32 bytes");
      spdlog::error("{}", last_error_);
      return false;
    }

    std::vector<uint8_t> master_key_bytes(master_key.begin(), master_key.end());
    if (!LoadKeyFile(key_file_path, master_key_bytes)) {
      spdlog::error("Failed to load key file: {}", last_error_);
      return false;
    }

    initialized_ = true;
    spdlog::info("KeyStore successfully initialized with key file: {}", key_file_path);
    return true;
  } catch (const std::exception& e) {
    SetError("Exception initializing KeyStore: " + std::string(e.what()));
    spdlog::error("{}", last_error_);
    return false;
  }
}

bool KeyStore::LoadKeyFile(const std::string& key_file_path, const std::vector<uint8_t>& master_key) {
  if (!std::filesystem::exists(key_file_path)) {
    SetError("Key file does not exist: " + key_file_path);
    return false;
  }

  std::ifstream file(key_file_path, std::ios::binary | std::ios::ate);
  if (!file.is_open()) {
    SetError("Failed to open key file: " + key_file_path);
    return false;
  }

  size_t file_size = file.tellg();
  if (file_size < 16) { // Minimum size for IV + some data
    SetError("Key file too small: " + std::to_string(file_size));
    return false;
  }

  std::vector<uint8_t> encrypted_data(file_size);
  file.seekg(0, std::ios::beg);
  file.read(reinterpret_cast<char*>(encrypted_data.data()), file_size);
  if (!file.good()) {
    SetError("Failed to read key file");
    return false;
  }

  // Extract IV (first 16 bytes)
  std::array<uint8_t, 16> iv;
  std::copy(encrypted_data.begin(), encrypted_data.begin() + 16, iv.begin());
  std::vector<uint8_t> ciphertext(encrypted_data.begin() + 16, encrypted_data.end());

  // Decrypt using AES-256-CBC
  std::vector<uint8_t> plaintext;
  std::array<uint8_t, 32> key;
  std::copy(master_key.begin(), master_key.begin() + std::min(master_key.size(), size_t(32)), key.begin());
  if (!ps4::AESCrypto::DecryptAES256_CBC(ciphertext, key, iv, plaintext)) {
    SetError("Failed to decrypt key file");
    return false;
  }

  return ParseKeyData(plaintext);
}

bool KeyStore::ParseKeyData(const std::vector<uint8_t>& key_data) {
  try {
    nlohmann::json json = nlohmann::json::parse(key_data.begin(), key_data.end());
    if (!json.is_object()) {
      SetError("Invalid JSON format in key file");
      return false;
    }

    for (const auto& [key_type_str, key_map] : json.items()) {
      uint32_t key_type = std::stoul(key_type_str);
      if (!key_map.is_object()) {
        SetError("Invalid key map for type: " + key_type_str);
        return false;
      }

      for (const auto& [index_str, key_entry] : key_map.items()) {
        uint32_t index = std::stoul(index_str);
        if (!key_entry.is_object() || !key_entry.contains("key") || !key_entry.contains("iv")) {
          SetError("Invalid key entry for type: " + key_type_str + ", index: " + index_str);
          return false;
        }

        std::string key_hex = key_entry["key"].get<std::string>();
        std::string iv_hex = key_entry["iv"].get<std::string>();
        std::vector<uint8_t> key_bytes, iv_bytes;
        if (!ps4::HexToBytes(key_hex, key_bytes) || !ps4::HexToBytes(iv_hex, iv_bytes)) {
          SetError("Invalid hex encoding for key or IV");
          return false;
        }

        if (key_bytes.size() != 16 && key_bytes.size() != 32) {
          SetError("Invalid key size for type: " + key_type_str + ", index: " + index_str);
          return false;
        }
        if (iv_bytes.size() != 16) {
          SetError("Invalid IV size for type: " + key_type_str + ", index: " + index_str);
          return false;
        }

        keys_[key_type][index] = key_bytes;
        ivs_[key_type][index] = iv_bytes;
        spdlog::debug("Loaded key for type: {}, index: {}", key_type, index);
      }
    }

    return true;
  } catch (const std::exception& e) {
    SetError("Exception parsing key data: " + std::string(e.what()));
    return false;
  }
}

std::vector<uint8_t> KeyStore::GetKey(uint32_t key_type, uint32_t key_index) const {
  auto type_it = keys_.find(key_type);
  if (type_it != keys_.end()) {
    auto index_it = type_it->second.find(key_index);
    if (index_it != type_it->second.end()) {
      return index_it->second;
    }
  }
  spdlog::warn("Key not found for type: {}, index: {}", key_type, key_index);
  return {};
}

std::vector<uint8_t> KeyStore::GetIV(uint32_t key_type, uint32_t iv_index) const {
  auto type_it = ivs_.find(key_type);
  if (type_it != ivs_.end()) {
    auto index_it = type_it->second.find(iv_index);
    if (index_it != type_it->second.end()) {
      return index_it->second;
    }
  }
  spdlog::warn("IV not found for type: {}, index: {}", key_type, iv_index);
  return {};
}

void KeyStore::SetError(const std::string& error) {
  last_error_ = error;
}

} // namespace ps4