#include "ps4_gpu.h"
#include "../common/lock_ordering.h"
#include "../memory/memory_diagnostics.h"
#include <SDL_vulkan.h>
#include <algorithm>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <memory>
#include <mutex>
#include <set>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include <vulkan/vulkan.h>
#ifdef _WIN32
#include <windows.h>
#define VK_USE_PLATFORM_WIN32_KHR
#include <vulkan/vulkan_win32.h>
#endif

namespace ps4 {

PS4GPU::PS4GPU(PS4MMU &memory, std::unique_ptr<GNMShaderTranslator> translator,
               std::unique_ptr<TileManager> tileManager,
               VulkanContext *vulkanContext, SDL_Window *window)
    : m_memory(memory), m_shaderTranslator(std::move(translator)),
      m_tileManager(std::move(tileManager)), m_vulkan(vulkanContext),
      m_window(window) {
  auto start = std::chrono::steady_clock::now();
  m_registerState = &m_gnmState;
  m_stats = GPUStats();

  if (m_shaderTranslator) {
    m_shaderTranslator->SetShaderTranslationCallback_SPIRV(
        [this](GCNShaderType type, uint64_t hash,
               const std::vector<uint32_t> &spirv) {
          this->NotifyShaderTranslated(type, hash, spirv);
        });
    m_shaderTranslator->SetShaderTranslationCallback_GLSL(
        [this](GCNShaderType type, uint64_t hash, const std::string &glsl) {
          this->NotifyShaderTranslated(type, hash, glsl);
        });
  }

  m_gnmState.SetRegisterChangeCallback([this](GNMRegisterType regType,
                                              uint32_t stage, uint32_t offset,
                                              uint32_t value) {
    this->NotifyRegisterChange(regType, stage, offset, value);
  });

  spdlog::info("PS4GPU constructed");
  auto end = std::chrono::steady_clock::now();
  auto duration = end - start;
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(duration)
          .count();
}

PS4GPU::~PS4GPU() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("PS4GPU destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

bool PS4GPU::Initialize() {
  auto start = std::chrono::steady_clock::now();

  // Initial validation with GPU lock
  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    spdlog::info("PS4GPU initializing...");
    if (!m_vulkan || m_vulkan->device == VK_NULL_HANDLE) {
      spdlog::error("Invalid Vulkan context provided to PS4GPU");
      m_stats.cacheMisses++;
      throw GPUException("Invalid Vulkan context");
    }

    // Validate that all required Vulkan objects are initialized
    if (m_vulkan->instance == VK_NULL_HANDLE) {
      spdlog::error("Vulkan instance not initialized");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan instance not available");
    }
    if (m_vulkan->physicalDevice == VK_NULL_HANDLE) {
      spdlog::error("Vulkan physical device not initialized");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan physical device not available");
    }
    if (m_vulkan->surface == VK_NULL_HANDLE) {
      spdlog::error("Vulkan surface not initialized");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan surface not available");
    }

    spdlog::debug("Vulkan context validation passed");
  } // Release GPU lock before initialization operations to prevent deadlock

  try {
    // Add timeout check for each major GPU initialization step
    auto checkTimeout = [&](const std::string& step) {
      auto current = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current - start);
      if (elapsed.count() > 45) { // 45 second timeout for GPU init
        spdlog::error("PS4GPU initialization timed out during: {}", step);
        throw GPUException("GPU initialization timeout");
      }
    };

    spdlog::info("PS4GPU: Creating swapchain...");
    checkTimeout("swapchain creation");
    if (!CreateSwapchain()) {
      spdlog::error("Failed to create swapchain");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU swapchain creation failed");
    }
    spdlog::info("PS4GPU: Creating swapchain image views...");
    checkTimeout("swapchain image views");
    if (!CreateSwapchainImageViews()) {
      spdlog::error("Failed to create swapchain image views");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU swapchain image views creation failed");
    }
    spdlog::info("PS4GPU: Creating command pool...");
    checkTimeout("command pool");
    if (!CreateCommandPool()) {
      spdlog::error("Failed to create command pool");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU command pool creation failed");
    }
    spdlog::info("PS4GPU: Creating descriptor pool...");
    checkTimeout("descriptor pool");
    if (!CreateDescriptorPool()) {
      spdlog::error("Failed to create descriptor pool");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU descriptor pool creation failed");
    }
    spdlog::info("PS4GPU: Creating sync objects...");
    checkTimeout("sync objects");
    if (!CreateSyncObjects()) {
      spdlog::error("Failed to create sync objects");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU sync objects creation failed");
    }
    spdlog::info("PS4GPU: Creating default render pass...");
    checkTimeout("default render pass");
    if (!CreateDefaultRenderPass()) {
      spdlog::error("Failed to create default render pass");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU default render pass creation failed");
    }
    spdlog::info("PS4GPU: Creating framebuffers...");
    if (!CreateFramebuffers()) {
      spdlog::error("Failed to create framebuffers");
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU framebuffers creation failed");
    }

    // Final initialization steps and statistics update with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      VkCommandBufferAllocateInfo cmdAllocInfo{};
      cmdAllocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
      cmdAllocInfo.commandPool = m_vulkan->commandPool;
      cmdAllocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
      cmdAllocInfo.commandBufferCount = 1;
      if (vkAllocateCommandBuffers(m_vulkan->device, &cmdAllocInfo,
                                   &m_commandBuffer) != VK_SUCCESS) {
        spdlog::error("Failed to allocate command buffer");
        m_stats.cacheMisses++;
        throw GPUException("Vulkan command buffer allocation failed");
      }
      VkPhysicalDeviceProperties properties;
      vkGetPhysicalDeviceProperties(m_vulkan->physicalDevice, &properties);
      m_timestampPeriod = properties.limits.timestampPeriod;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("PS4GPU initialized with Vulkan, latency={}us",
                   m_stats.totalLatencyUs.load());
    }
  } catch (const std::exception &e) {
    spdlog::error("Initialize failed: {}", e.what());
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    m_stats.cacheMisses++;
    return false;
  }

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics during GPU initialization: {}", e.what());
  }

  return true;
}

void PS4GPU::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (!m_vulkan || !m_vulkan->device) {
      spdlog::trace("PS4GPU already shut down");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return;
    }
    vkDeviceWaitIdle(m_vulkan->device);
    if (m_commandBuffer != VK_NULL_HANDLE) {
      vkFreeCommandBuffers(m_vulkan->device, m_vulkan->commandPool, 1,
                           &m_commandBuffer);
      m_commandBuffer = VK_NULL_HANDLE;
    }
    for (auto &[_, fence] : m_fences) {
      if (fence != VK_NULL_HANDLE) {
        vkDestroyFence(m_vulkan->device, fence, nullptr);
      }
    }
    m_fences.clear();
    for (auto &[_, queryPool] : m_profileQueryPools) {
      if (queryPool != VK_NULL_HANDLE) {
        vkDestroyQueryPool(m_vulkan->device, queryPool, nullptr);
      }
    }
    m_profileQueryPools.clear();
    m_profileResults.clear();
    for (auto &[_, pipeline] : m_graphicsPipelineCache) {
      if (pipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(m_vulkan->device, pipeline, nullptr);
      }
    }
    m_graphicsPipelineCache.clear();
    for (auto &[_, pipeline] : m_computePipelineCache) {
      if (pipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(m_vulkan->device, pipeline, nullptr);
      }
    }
    m_computePipelineCache.clear();
    for (auto &[_, renderPass] : m_renderPassCache) {
      if (renderPass != VK_NULL_HANDLE) {
        vkDestroyRenderPass(m_vulkan->device, renderPass, nullptr);
      }
    }
    m_renderPassCache.clear();
    for (auto &[_, framebuffer] : m_framebufferCache) {
      if (framebuffer != VK_NULL_HANDLE) {
        vkDestroyFramebuffer(m_vulkan->device, framebuffer, nullptr);
      }
    }
    m_framebufferCache.clear();
    for (auto &[_, sampler] : m_samplers) {
      if (sampler != VK_NULL_HANDLE) {
        vkDestroySampler(m_vulkan->device, sampler, nullptr);
      }
    }
    m_samplers.clear();
    for (auto &[_, view] : m_textureViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_textureViews.clear();
    for (auto &[_, target] : m_renderTargets) {
      if (target.view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, target.view, nullptr);
      }
      if (target.image != VK_NULL_HANDLE) {
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
      }
      if (target.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, target.memory, nullptr);
      }
    }
    m_renderTargets.clear();
    for (auto &[_, mapping] : m_memoryMappings) {
      if (mapping.mappedData) {
        vkUnmapMemory(m_vulkan->device, mapping.memory);
      }
      if (mapping.buffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(m_vulkan->device, mapping.buffer, nullptr);
      }
      if (mapping.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, mapping.memory, nullptr);
      }
    }
    m_memoryMappings.clear();
    for (auto &[_, shader] : m_shaderModules) {
      if (shader.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, shader.module, nullptr);
      }
    }
    m_shaderModules.clear();
    if (m_currentPipelineLayout != VK_NULL_HANDLE) {
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout,
                              nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
    }
    if (m_vulkan->inFlightFence != VK_NULL_HANDLE) {
      vkDestroyFence(m_vulkan->device, m_vulkan->inFlightFence, nullptr);
      m_vulkan->inFlightFence = VK_NULL_HANDLE;
    }
    if (m_vulkan->renderFinishedSemaphore != VK_NULL_HANDLE) {
      vkDestroySemaphore(m_vulkan->device, m_vulkan->renderFinishedSemaphore,
                         nullptr);
      m_vulkan->renderFinishedSemaphore = VK_NULL_HANDLE;
    }
    if (m_vulkan->imageAvailableSemaphore != VK_NULL_HANDLE) {
      vkDestroySemaphore(m_vulkan->device, m_vulkan->imageAvailableSemaphore,
                         nullptr);
      m_vulkan->imageAvailableSemaphore = VK_NULL_HANDLE;
    }
    if (m_vulkan->descriptorPool != VK_NULL_HANDLE) {
      vkDestroyDescriptorPool(m_vulkan->device, m_vulkan->descriptorPool,
                              nullptr);
      m_vulkan->descriptorPool = VK_NULL_HANDLE;
    }
    if (m_vulkan->commandPool != VK_NULL_HANDLE) {
      vkDestroyCommandPool(m_vulkan->device, m_vulkan->commandPool, nullptr);
      m_vulkan->commandPool = VK_NULL_HANDLE;
    }
    for (auto &view : m_vulkan->swapchainImageViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_vulkan->swapchainImageViews.clear();
    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      vkDestroySwapchainKHR(m_vulkan->device, m_vulkan->swapchain, nullptr);
      m_vulkan->swapchain = VK_NULL_HANDLE;
    }
    if (m_vulkan->surface != VK_NULL_HANDLE) {
      vkDestroySurfaceKHR(m_vulkan->instance, m_vulkan->surface, nullptr);
      m_vulkan->surface = VK_NULL_HANDLE;
    }
    if (m_vulkan->device != VK_NULL_HANDLE) {
      vkDestroyDevice(m_vulkan->device, nullptr);
      m_vulkan->device = VK_NULL_HANDLE;
    }
    if (m_vulkan->instance != VK_NULL_HANDLE) {
      vkDestroyInstance(m_vulkan->instance, nullptr);
      m_vulkan->instance = VK_NULL_HANDLE;
    }
    m_vulkan = nullptr;
    m_currentRenderPass = VK_NULL_HANDLE;
    m_currentFramebuffer = VK_NULL_HANDLE;
    m_currentGraphicsPipeline = VK_NULL_HANDLE;
    m_currentComputePipeline = VK_NULL_HANDLE;
    m_currentDescriptorSets.clear();
    m_currentIndexBuffer = VK_NULL_HANDLE;
    m_currentVertexBuffers.clear();
    m_currentVertexBufferOffsets.clear();
    m_stats.vramUsage.store(0);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("PS4GPU shutdown, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Shutdown failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics during GPU shutdown: {}", e.what());
  }
}

void PS4GPU::BeginFrame() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  int maxAttempts = 3;
  int attempts = 0;
  VkResult result = VK_NOT_READY;
  try {
    vkWaitForFences(m_vulkan->device, 1, &m_vulkan->inFlightFence, VK_TRUE,
                    1000000000);
    vkResetFences(m_vulkan->device, 1, &m_vulkan->inFlightFence);
    while (attempts < maxAttempts) {
      result = vkAcquireNextImageKHR(m_vulkan->device, m_vulkan->swapchain,
                                     1000000000, m_vulkan->imageAvailableSemaphore,
                                     VK_NULL_HANDLE, &m_currentSwapchainImageIndex);
      if (result == VK_SUCCESS || result == VK_SUBOPTIMAL_KHR)
        break;
      else if (result == VK_ERROR_OUT_OF_DATE_KHR) {
        spdlog::warn("Swapchain out of date, recreating...");
        if (!RecreateSwapchain()) {
          spdlog::error("Failed to recreate swapchain");
          m_stats.cacheMisses++;
          throw GPUException("Swapchain recreation failed");
        }
      } else {
        spdlog::warn("vkAcquireNextImageKHR attempt {} failed, retrying...",
                     attempts + 1);
      }
      ++attempts;
    }
    if (result != VK_SUCCESS && result != VK_SUBOPTIMAL_KHR) {
      spdlog::error("Failed to acquire swapchain image after {} attempts",
                    attempts);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan swapchain image acquisition failed");
    }
    BeginCommandBuffer();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BeginFrame: Started frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
    ++m_currentFrame;
  } catch (const std::exception &e) {
    spdlog::error("BeginFrame failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::EndFrame() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (m_currentRenderPass != VK_NULL_HANDLE) {
      vkCmdEndRenderPass(m_commandBuffer);
      m_currentRenderPass = VK_NULL_HANDLE;
    }
    if (vkEndCommandBuffer(m_commandBuffer) != VK_SUCCESS) {
      spdlog::error("Failed to end command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer end failed");
    }
    SubmitCommandBuffer();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("EndFrame: Ended frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("EndFrame failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::Present() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkPresentInfoKHR presentInfo{};
    presentInfo.sType = VK_STRUCTURE_TYPE_PRESENT_INFO_KHR;
    presentInfo.waitSemaphoreCount = 1;
    presentInfo.pWaitSemaphores = &m_vulkan->renderFinishedSemaphore;
    presentInfo.swapchainCount = 1;
    presentInfo.pSwapchains = &m_vulkan->swapchain;
    presentInfo.pImageIndices = &m_currentSwapchainImageIndex;
    VkResult result = vkQueuePresentKHR(m_vulkan->presentQueue, &presentInfo);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      spdlog::warn(
          "Swapchain out of date during present, will recreate on next frame");
      m_stats.cacheHits++;
    } else if (result != VK_SUCCESS) {
      spdlog::error("Failed to present frame");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan present failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Present: Presented frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Present failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SetShaderRegister(uint32_t stage, uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    lock.unlock();
    m_gnmState.SetShaderRegister(stage, offset, value);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "SetShaderRegister: stage={}, offset={}, value={:x}, latency={}us",
        stage, offset, value, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetShaderRegister failed: {}", e.what());
    if (!lock.owns_lock()) {
      lock.lock();
    }
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SetContextRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    lock.unlock();
    m_gnmState.SetContextRegister(offset, value);
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetContextRegister: offset={}, value={:x}, latency={}us",
                  offset, value, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetContextRegister failed: {}", e.what());
    if (!lock.owns_lock()) {
      lock.lock();
    }
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SetRenderTarget(uint32_t index, uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end()) {
      spdlog::error("Render target not found: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid render target");
    }
    // Store render target in context register instead of using non-existent method
    m_gnmState.SetContextRegister(0x100 + index, static_cast<uint32_t>(surfaceId >> 8));
    m_renderTargetStateDirty = true;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetRenderTarget: index={}, surfaceId={}, latency={}us",
                  index, surfaceId, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetRenderTarget failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}
void PS4GPU::SetDepthRenderTarget(uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end() || !it->second.isDepthStencil) {
      spdlog::error("Depth render target not found or invalid: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid depth render target");
    }
    // Store depth render target in context register instead of using non-existent method
    m_gnmState.SetContextRegister(0x110, static_cast<uint32_t>(surfaceId >> 8));
    m_renderTargetStateDirty = true;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetDepthRenderTarget: surfaceId={}, latency={}us", surfaceId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetDepthRenderTarget failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SetViewport(float x, float y, float width, float height,
                         float minDepth, float maxDepth) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkViewport viewport{};
    viewport.x = x;
    viewport.y = y;
    viewport.width = width;
    viewport.height = height;
    viewport.minDepth = minDepth;
    viewport.maxDepth = maxDepth;
    vkCmdSetViewport(m_commandBuffer, 0, 1, &viewport);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetViewport: x={}, y={}, width={}, height={}, minDepth={}, "
                  "maxDepth={}, latency={}us",
                  x, y, width, height, minDepth, maxDepth,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetViewport failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SetScissor(int32_t x, int32_t y, uint32_t width, uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkRect2D scissor{};
    scissor.offset = {x, y};
    scissor.extent = {width, height};
    vkCmdSetScissor(m_commandBuffer, 0, 1, &scissor);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetScissor: x={}, y={}, width={}, height={}, latency={}us",
                  x, y, width, height, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetScissor failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::BindVertexBuffer(uint32_t binding, uint64_t gpuAddress,
                              uint32_t stride) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_memoryMappings.find(gpuAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Vertex buffer mapping not found: {}", gpuAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid vertex buffer mapping");
    }
    if (m_currentVertexBuffers.size() <= binding) {
      m_currentVertexBuffers.resize(binding + 1, VK_NULL_HANDLE);
      m_currentVertexBufferOffsets.resize(binding + 1, 0);
    }
    m_currentVertexBuffers[binding] = it->second.buffer;
    m_currentVertexBufferOffsets[binding] = 0;
    vkCmdBindVertexBuffers(m_commandBuffer, binding, 1,
                           &m_currentVertexBuffers[binding],
                           &m_currentVertexBufferOffsets[binding]);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BindVertexBuffer: binding={}, gpuAddress={:x}, stride={}, "
                  "latency={}us",
                  binding, gpuAddress, stride, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindVertexBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::BindIndexBuffer(uint64_t gpuAddress, VkIndexType indexType) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_memoryMappings.find(gpuAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Index buffer mapping not found: {}", gpuAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid index buffer mapping");
    }
    m_currentIndexBuffer = it->second.buffer;
    m_currentIndexType = indexType;
    vkCmdBindIndexBuffer(m_commandBuffer, m_currentIndexBuffer, 0, indexType);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        fmt::runtime(
            "BindIndexBuffer: gpuAddress={:x}, indexType={}, latency={}us"),
        gpuAddress, static_cast<uint32_t>(indexType),
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindIndexBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::BindResource(uint32_t set, uint32_t binding, uint64_t resourceId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkDescriptorSet descriptorSet =
        AllocateDescriptorSet(GetOrCreateDescriptorSetLayout());
    VkDescriptorImageInfo imageInfo{};
    auto textureIt = m_textureViews.find(resourceId);
    if (textureIt != m_textureViews.end()) {
      imageInfo.imageView = textureIt->second;
      imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
      auto samplerIt = m_samplers.find(resourceId);
      imageInfo.sampler =
          samplerIt != m_samplers.end() ? samplerIt->second : VK_NULL_HANDLE;
      VkWriteDescriptorSet write{};
      write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
      write.dstSet = descriptorSet;
      write.dstBinding = binding;
      write.dstArrayElement = 0;
      write.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
      write.descriptorCount = 1;
      write.pImageInfo = &imageInfo;
      vkUpdateDescriptorSets(m_vulkan->device, 1, &write, 0, nullptr);
    } else {
      auto bufferIt = m_memoryMappings.find(resourceId);
      if (bufferIt != m_memoryMappings.end()) {
        VkDescriptorBufferInfo bufferInfo{};
        bufferInfo.buffer = bufferIt->second.buffer;
        bufferInfo.offset = 0;
        bufferInfo.range = bufferIt->second.size;
        VkWriteDescriptorSet write{};
        write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        write.dstSet = descriptorSet;
        write.dstBinding = binding;
        write.dstArrayElement = 0;
        write.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        write.descriptorCount = 1;
        write.pBufferInfo = &bufferInfo;
        vkUpdateDescriptorSets(m_vulkan->device, 1, &write, 0, nullptr);
      } else {
        spdlog::error("Resource not found: {}", resourceId);
        m_stats.cacheMisses++;
        throw GPUException("Invalid resource ID");
      }
    }
    if (m_currentDescriptorSets.size() <= set) {
      m_currentDescriptorSets.resize(set + 1, VK_NULL_HANDLE);
    }
    m_currentDescriptorSets[set] = descriptorSet;
    vkCmdBindDescriptorSets(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                            m_currentPipelineLayout, set, 1,
                            &m_currentDescriptorSets[set], 0, nullptr);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "BindResource: set={}, binding={}, resourceId={:x}, latency={}us", set,
        binding, resourceId, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindResource failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::DrawIndex(uint32_t indexCount, uint32_t instanceCount,
                       uint32_t firstIndex, int32_t vertexOffset,
                       uint32_t firstInstance) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_currentGraphicsPipeline = GetOrCreateGraphicsPipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                      m_currentGraphicsPipeline);
    std::vector<uint64_t> colorTargetIds;
    if (m_registerState) {
      for (uint32_t i = 0; i < 8; ++i) {
        try {
          // Get render target from context register
          uint32_t rtValue = m_registerState->GetContextRegister(0x100 + i);
          uint64_t surfaceId = static_cast<uint64_t>(rtValue) << 8;
          if (surfaceId != 0 && m_renderTargets.find(surfaceId) != m_renderTargets.end()) {
            colorTargetIds.push_back(surfaceId);
          }
        } catch (const std::exception &e) {
          spdlog::trace("DrawIndex: Skipping render target {}: {}", i, e.what());
        }
      }
    }
    if (colorTargetIds.empty()) {
      for (auto &p : m_renderTargets) {
        if (!p.second.isDepthStencil) {
          colorTargetIds.push_back(p.first);
        }
      }
    }
    uint64_t depthTargetId = 0;
    if (m_registerState) {
      try {
        uint32_t depthValue = m_registerState->GetContextRegister(0x110);
        depthTargetId = static_cast<uint64_t>(depthValue) << 8;
      } catch (const std::exception &e) {
        depthTargetId = 0;
      }
    }
    m_currentFramebuffer = GetOrCreateFramebuffer(colorTargetIds, depthTargetId);
    m_currentRenderPass = GetOrCreateRenderPass(RenderPassKey{});
    VkRenderPassBeginInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
    renderPassInfo.renderPass = m_currentRenderPass;
    renderPassInfo.framebuffer = m_currentFramebuffer;
    renderPassInfo.renderArea.offset = {0, 0};
    renderPassInfo.renderArea.extent = m_vulkan->swapchainExtent;
    VkClearValue clearValues[9] = {};
    for (size_t i = 0; i < colorTargetIds.size(); ++i) {
      clearValues[i].color = {{0.0f, 0.0f, 0.0f, 1.0f}};
    }
    if (depthTargetId) {
      clearValues[colorTargetIds.size()].depthStencil = {1.0f, 0};
    }
    renderPassInfo.clearValueCount = depthTargetId ? colorTargetIds.size() + 1 : colorTargetIds.size();
    renderPassInfo.pClearValues = clearValues;
    vkCmdBeginRenderPass(m_commandBuffer, &renderPassInfo,
                         VK_SUBPASS_CONTENTS_INLINE);
    // Check if tessellation is enabled by examining relevant registers
    try {
      uint32_t tessellationReg = m_gnmState.GetContextRegister(0x300); // VGT_TESS_LEVEL_OUTER
      if (tessellationReg > 0) {
        m_stats.tessellationDraws++;
      }
    } catch (const std::exception &e) {
      // Tessellation not configured, continue without tessellation stats
    }
    vkCmdDrawIndexed(m_commandBuffer, indexCount, instanceCount, firstIndex,
                     vertexOffset, firstInstance);
    m_stats.drawCalls++;
    m_stats.drawCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DrawIndex: indices={}, instances={}, firstIndex={}, "
                  "vertexOffset={}, firstInstance={}, latency={}us",
                  indexCount, instanceCount, firstIndex, vertexOffset,
                  firstInstance, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndex failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::DrawIndexIndirect(uint64_t bufferAddress, uint32_t drawCount,
                               uint32_t stride) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_memoryMappings.find(bufferAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Indirect buffer mapping not found: {}", bufferAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid indirect buffer mapping");
    }
    m_currentGraphicsPipeline = GetOrCreateGraphicsPipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                      m_currentGraphicsPipeline);
    std::vector<uint64_t> colorTargetIds;
    if (m_registerState) {
      for (uint32_t i = 0; i < 8; ++i) {
        try {
          // Get render target from context register
          uint32_t rtValue = m_registerState->GetContextRegister(0x100 + i);
          uint64_t surfaceId = static_cast<uint64_t>(rtValue) << 8;
          if (surfaceId != 0 && m_renderTargets.find(surfaceId) != m_renderTargets.end()) {
            colorTargetIds.push_back(surfaceId);
          }
        } catch (const std::exception &e) {
          spdlog::trace("DrawIndexIndirect: Skipping render target {}: {}", i,
                        e.what());
        }
      }
    }
    if (m_registerState) {
      for (uint32_t i = 0; i < 8; ++i) {
        try {
          uint64_t surfaceId = m_registerState->GetRenderTarget(i);
          if (surfaceId != 0 && m_renderTargets.find(surfaceId) != m_renderTargets.end()) {
            colorTargetIds.push_back(surfaceId);
          }
        } catch (const std::exception &e) {
          spdlog::trace("DrawIndexIndirect: Skipping render target {}: {}", i,
                        e.what());
        }
      }
    }
    if (colorTargetIds.empty()) {
      for (auto &p : m_renderTargets) {
        if (!p.second.isDepthStencil) {
          colorTargetIds.push_back(p.first);
        }
      }
    }
    uint64_t depthTargetId = 0;
    if (m_registerState) {
      try {
        uint32_t depthValue = m_registerState->GetContextRegister(0x110);
        depthTargetId = static_cast<uint64_t>(depthValue) << 8;
      } catch (const std::exception &e) {
        depthTargetId = 0;
      }
    }
    m_currentFramebuffer = GetOrCreateFramebuffer(colorTargetIds, depthTargetId);
    m_currentRenderPass = GetOrCreateRenderPass(RenderPassKey{});
    VkRenderPassBeginInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
    renderPassInfo.renderPass = m_currentRenderPass;
    renderPassInfo.framebuffer = m_currentFramebuffer;
    renderPassInfo.renderArea.offset = {0, 0};
    renderPassInfo.renderArea.extent = m_vulkan->swapchainExtent;
    VkClearValue clearValues[9] = {};
    for (size_t i = 0; i < colorTargetIds.size(); ++i) {
      clearValues[i].color = {{0.0f, 0.0f, 0.0f, 1.0f}};
    }
    if (depthTargetId) {
      clearValues[colorTargetIds.size()].depthStencil = {1.0f, 0};
    }
    renderPassInfo.clearValueCount = depthTargetId ? colorTargetIds.size() + 1 : colorTargetIds.size();
    renderPassInfo.pClearValues = clearValues;
    vkCmdBeginRenderPass(m_commandBuffer, &renderPassInfo,
                         VK_SUBPASS_CONTENTS_INLINE);
    // Check if tessellation is enabled by examining relevant registers
    try {
      uint32_t tessellationReg = m_gnmState.GetContextRegister(0x300); // VGT_TESS_LEVEL_OUTER
      if (tessellationReg > 0) {
        m_stats.tessellationDraws++;
      }
    } catch (const std::exception &e) {
      // Tessellation not configured, continue without tessellation stats
    }
    vkCmdDrawIndexedIndirect(m_commandBuffer, it->second.buffer, 0, drawCount,
                             stride);
    m_stats.drawCalls++;
    m_stats.drawCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DrawIndexIndirect: bufferAddress={:x}, drawCount={}, "
                  "stride={}, latency={}us",
                  bufferAddress, drawCount, stride,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndexIndirect failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::DrawIndexed(uint32_t indexCount, uint32_t instanceCount,
                         uint32_t firstIndex) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    DrawIndex(indexCount, instanceCount, firstIndex, 0, 0);
    m_stats.drawCalls++;
    m_stats.drawCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "DrawIndexed: indices={}, instances={}, firstIndex={}, latency={}us",
        indexCount, instanceCount, firstIndex, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndexed failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::Dispatch(uint32_t groupCountX, uint32_t groupCountY,
                      uint32_t groupCountZ) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_currentComputePipeline = GetOrCreateComputePipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                      m_currentComputePipeline);
    UpdateDescriptorSet();
    vkCmdDispatch(m_commandBuffer, groupCountX, groupCountY, groupCountZ);
    m_stats.computeDispatches++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Dispatch: groupCountX={}, groupCountY={}, groupCountZ={}, "
                  "latency={}us",
                  groupCountX, groupCountY, groupCountZ,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Dispatch failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::DispatchIndirect(uint64_t bufferAddress, uint64_t offset) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_memoryMappings.find(bufferAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Indirect buffer mapping not found: {}", bufferAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid indirect buffer mapping");
    }
    m_currentComputePipeline = GetOrCreateComputePipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                      m_currentComputePipeline);
    UpdateDescriptorSet();
    vkCmdDispatchIndirect(m_commandBuffer, it->second.buffer, offset);
    m_stats.computeDispatches++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "DispatchIndirect: bufferAddress={:x}, offset={}, latency={}us",
        bufferAddress, offset, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DispatchIndirect failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::WaitRegisterMemory(uint64_t address, uint32_t reference,
                                uint32_t mask, uint32_t function,
                                bool isMemory) {
  auto start = std::chrono::steady_clock::now();

  // Initial logging and validation with GPU lock
  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    spdlog::info("PS4GPU: WaitRegisterMemory(address=0x{:x}, ref=0x{:x}, "
                 "mask=0x{:x}, func={}, isMemory={})",
                 address, reference, mask, function, isMemory);
  } // Release GPU lock before memory operations

  try {
    auto startTime = std::chrono::high_resolution_clock::now();
    const auto timeout = std::chrono::milliseconds(1000);
    bool conditionMet = false;
    uint32_t currentValue = 0;

    while (true) {
      currentValue = 0;

      // Perform memory operations WITHOUT holding GPU lock to prevent deadlock
      if (isMemory) {
        try {
          uint64_t physAddr =
              m_memory.VirtualToPhysical(address, sizeof(uint32_t), false);
          if (physAddr != 0) {
            m_memory.ReadVirtual(address, &currentValue, sizeof(currentValue), 1);
          }
        } catch (const std::exception &e) {
          spdlog::warn("PS4GPU: Failed to read memory at 0x{:x}: {}", address,
                       e.what());
          break;
        }
      } else {
        if (address >= 0x8000 && address < 0x9000) {
          currentValue = 0x12345678; // Dummy value
        }
      }

      // Check condition without holding GPU lock
      uint32_t maskedValue = currentValue & mask;
      uint32_t maskedReference = reference & mask;
      conditionMet = false;
      switch (function) {
      case 0: conditionMet = true; break;
      case 1: conditionMet = (maskedValue < maskedReference); break;
      case 2: conditionMet = (maskedValue <= maskedReference); break;
      case 3: conditionMet = (maskedValue == maskedReference); break;
      case 4: conditionMet = (maskedValue != maskedReference); break;
      case 5: conditionMet = (maskedValue >= maskedReference); break;
      case 6: conditionMet = (maskedValue > maskedReference); break;
      default:
        spdlog::warn("PS4GPU: Unknown wait function {}", function);
        conditionMet = true;
        break;
      }

      if (conditionMet) {
        spdlog::debug("PS4GPU: Wait condition met, value=0x{:x}", currentValue);
        break;
      }

      auto currentTime = std::chrono::high_resolution_clock::now();
      if (currentTime - startTime > timeout) {
        spdlog::warn("PS4GPU: WaitRegisterMemory timeout after 1 second");
        break;
      }

      // Sleep without holding any locks
      std::this_thread::sleep_for(std::chrono::microseconds(100));
    }

    // Update GPU statistics only after memory operations complete
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("WaitRegisterMemory: address={:x}, reference={:x}, "
                    "mask={:x}, function={}, isMemory={}, latency={}us",
                    address, reference, mask, function, isMemory,
                    m_stats.totalLatencyUs.load());
    }
  } catch (const std::exception &e) {
    spdlog::error("WaitRegisterMemory failed: {}", e.what());
    // Update error statistics with GPU lock
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    m_stats.cacheMisses++;
  }
}

void PS4GPU::AcquireMemory(uint64_t address, uint32_t size) {
  auto start = std::chrono::steady_clock::now();
  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      m_stats.vramUsage += size;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("AcquireMemory: address={:x}, size={}, vramUsage={} bytes, "
                    "latency={}us",
                    address, size, m_stats.vramUsage.load(),
                    m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("AcquireMemory failed: {}", e.what());
      m_stats.cacheMisses++;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in AcquireMemory: {}", e.what());
  }
}

void PS4GPU::ReleaseMemory(uint64_t address, uint32_t size) {
  auto start = std::chrono::steady_clock::now();
  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      m_stats.vramUsage =
          (size <= m_stats.vramUsage) ? m_stats.vramUsage - size : 0;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto duration = end - start;
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(duration)
              .count();
      spdlog::trace("ReleaseMemory: address={:x}, size={}, vramUsage={} bytes, "
                    "latency={}us",
                    address, size, m_stats.vramUsage.load(),
                    m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("ReleaseMemory failed: {}", e.what());
      m_stats.cacheMisses++;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in ReleaseMemory: {}", e.what());
  }
}

void PS4GPU::Nop() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Nop: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Nop failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint64_t PS4GPU::CompileShader(const void *gcnCode, size_t size,
                               GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkShaderStageFlagBits stage;
    switch (type) {
    case GCNShaderType::VERTEX:
      stage = VK_SHADER_STAGE_VERTEX_BIT;
      break;
    case GCNShaderType::PIXEL:
      stage = VK_SHADER_STAGE_FRAGMENT_BIT;
      break;
    case GCNShaderType::GEOMETRY:
      stage = VK_SHADER_STAGE_GEOMETRY_BIT;
      break;
    case GCNShaderType::COMPUTE:
      stage = VK_SHADER_STAGE_COMPUTE_BIT;
      break;
    case GCNShaderType::HULL:
      stage = VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT;
      break;
    case GCNShaderType::DOMAIN_SHADER:
      stage = VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT;
      break;
    default:
      spdlog::error("Invalid shader type: {}", static_cast<int>(type));
      m_stats.cacheMisses++;
      throw GPUException("Invalid shader type");
    }
    uint64_t shaderId = TranslateGCNShader(gcnCode, size, stage);
    m_stats.shaderCompilations++;
    m_stats.shaderCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "CompileShader: shaderId={:x}, size={}, type={}, latency={}us",
        shaderId, size, static_cast<int>(type), m_stats.totalLatencyUs.load());
    return shaderId;
  } catch (const std::exception &e) {
    spdlog::error("CompileShader failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

void PS4GPU::UnloadShader(uint64_t shaderId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_shaderModules.find(shaderId);
    if (it != m_shaderModules.end()) {
      if (it->second.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, it->second.module, nullptr);
      }
      m_shaderModules.erase(it);
      m_stats.shaderCount--;
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Shader not found: {}", shaderId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("UnloadShader: shaderId={:x}, latency={}us", shaderId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UnloadShader failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint64_t PS4GPU::MapMemory(uint64_t cpuAddress, size_t size,
                           VkBufferUsageFlags usage) {
  auto start = std::chrono::steady_clock::now();
  uint64_t gpuAddress = 0;

  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      VkDeviceMemory memory;
      VkBuffer buffer = CreateBuffer(size, usage,
                                     VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                                         VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                                     memory);
      void *mappedData;
      if (vkMapMemory(m_vulkan->device, memory, 0, size, 0, &mappedData) !=
          VK_SUCCESS) {
        spdlog::error("Failed to map memory");
        vkDestroyBuffer(m_vulkan->device, buffer, nullptr);
        vkFreeMemory(m_vulkan->device, memory, nullptr);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan memory mapping failed");
      }
      gpuAddress = cpuAddress;
      MemoryMapping mapping;
      mapping.gpuAddress = gpuAddress;
      mapping.cpuAddress = cpuAddress;
      mapping.size = size;
      mapping.buffer = buffer;
      mapping.memory = memory;
      mapping.mappedData = mappedData;
      m_memoryMappings[gpuAddress] = mapping;
      m_stats.vramUsage += size;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("MapMemory: cpuAddress={:x}, size={}, gpuAddress={:x}, "
                    "latency={}us",
                    cpuAddress, size, gpuAddress, m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("MapMemory failed: {}", e.what());
      m_stats.cacheMisses++;
      return 0;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in MapMemory: {}", e.what());
  }

  return gpuAddress;
}

void PS4GPU::UnmapMemory(uint64_t gpuAddress) {
  auto start = std::chrono::steady_clock::now();
  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      auto it = m_memoryMappings.find(gpuAddress);
      if (it != m_memoryMappings.end()) {
        if (it->second.mappedData) {
          vkUnmapMemory(m_vulkan->device, it->second.memory);
        }
        if (it->second.buffer != VK_NULL_HANDLE) {
          vkDestroyBuffer(m_vulkan->device, it->second.buffer, nullptr);
        }
        if (it->second.memory != VK_NULL_HANDLE) {
          vkFreeMemory(m_vulkan->device, it->second.memory, nullptr);
        }
        m_stats.vramUsage -= it->second.size;
        m_memoryMappings.erase(it);
        m_stats.cacheHits++;
      } else {
        spdlog::warn("Memory mapping not found: {:x}", gpuAddress);
        m_stats.cacheMisses++;
      }
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("UnmapMemory: gpuAddress={:x}, latency={}us", gpuAddress,
                    m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("UnmapMemory failed: {}", e.what());
      m_stats.cacheMisses++;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in UnmapMemory: {}", e.what());
  }
}

uint64_t PS4GPU::CreateTexture(uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end()) {
      spdlog::error("Surface not found for texture creation: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid surface ID for texture");
    }
    VkImageViewCreateInfo viewInfo{};
    viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    viewInfo.image = it->second.image;
    viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
    viewInfo.format = it->second.format;
    viewInfo.subresourceRange.aspectMask = it->second.isDepthStencil
        ? VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT
        : VK_IMAGE_ASPECT_COLOR_BIT;
    viewInfo.subresourceRange.baseMipLevel = 0;
    viewInfo.subresourceRange.levelCount = 1;
    viewInfo.subresourceRange.baseArrayLayer = 0;
    viewInfo.subresourceRange.layerCount = 1;
    VkImageView textureView;
    if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr, &textureView) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create texture view");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan image view creation failed");
    }
    uint64_t textureId = surfaceId;
    m_textureViews[textureId] = textureView;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateTexture: surfaceId={:x}, textureId={:x}, latency={}us",
                  surfaceId, textureId, m_stats.totalLatencyUs.load());
    return textureId;
  } catch (const std::exception &e) {
    spdlog::error("CreateTexture failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

void PS4GPU::UpdateTexture(uint64_t textureId, const void *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto viewIt = m_textureViews.find(textureId);
    if (viewIt == m_textureViews.end()) {
      spdlog::error("Texture view not found: {}", textureId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid texture ID");
    }
    auto targetIt = m_renderTargets.find(textureId);
    if (targetIt == m_renderTargets.end()) {
      spdlog::error("Render target not found for texture: {}", textureId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid render target for texture");
    }
    VkBuffer stagingBuffer;
    VkDeviceMemory stagingMemory;
    VkBufferUsageFlags usage = VK_BUFFER_USAGE_TRANSFER_SRC_BIT;
    VkMemoryPropertyFlags properties = VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                                       VK_MEMORY_PROPERTY_HOST_COHERENT_BIT;
    stagingBuffer = CreateBuffer(size, usage, properties, stagingMemory);
    void *mappedData;
    if (vkMapMemory(m_vulkan->device, stagingMemory, 0, size, 0, &mappedData) !=
        VK_SUCCESS) {
      spdlog::error("Failed to map staging buffer memory");
      vkDestroyBuffer(m_vulkan->device, stagingBuffer, nullptr);
      vkFreeMemory(m_vulkan->device, stagingMemory, nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan memory mapping failed");
    }
    memcpy(mappedData, data, size);
    vkUnmapMemory(m_vulkan->device, stagingMemory);
    TransitionImageLayout(targetIt->second.image, targetIt->second.format,
                          VK_IMAGE_LAYOUT_UNDEFINED,
                          VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL);
    CopyBufferToImage(stagingBuffer, targetIt->second.image,
                      targetIt->second.width, targetIt->second.height);
    TransitionImageLayout(targetIt->second.image, targetIt->second.format,
                          VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
                          VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
    vkDestroyBuffer(m_vulkan->device, stagingBuffer, nullptr);
    vkFreeMemory(m_vulkan->device, stagingMemory, nullptr);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("UpdateTexture: textureId={:x}, size={}, latency={}us",
                  textureId, size, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UpdateTexture failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::DeleteTexture(uint64_t textureId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_textureViews.find(textureId);
    if (it != m_textureViews.end()) {
      if (it->second != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, it->second, nullptr);
      }
      m_textureViews.erase(it);
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Texture not found: {}", textureId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DeleteTexture: textureId={:x}, latency={}us", textureId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DeleteTexture failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint64_t PS4GPU::CreateSampler() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkSamplerCreateInfo samplerInfo{};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.anisotropyEnable = VK_TRUE;
    samplerInfo.maxAnisotropy = 16.0f;
    samplerInfo.borderColor = VK_BORDER_COLOR_INT_OPAQUE_BLACK;
    samplerInfo.unnormalizedCoordinates = VK_FALSE;
    samplerInfo.compareEnable = VK_FALSE;
    samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
    samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
    VkSampler sampler;
    if (vkCreateSampler(m_vulkan->device, &samplerInfo, nullptr, &sampler) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create sampler");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan sampler creation failed");
    }
    uint64_t samplerId = reinterpret_cast<uint64_t>(sampler);
    m_samplers[samplerId] = sampler;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateSampler: samplerId={:x}, latency={}us", samplerId,
                  m_stats.totalLatencyUs.load());
    return samplerId;
  } catch (const std::exception &e) {
    spdlog::error("CreateSampler failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

void PS4GPU::DeleteSampler(uint64_t samplerId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_samplers.find(samplerId);
    if (it != m_samplers.end()) {
      if (it->second != VK_NULL_HANDLE) {
        vkDestroySampler(m_vulkan->device, it->second, nullptr);
      }
      m_samplers.erase(it);
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Sampler not found: {}", samplerId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DeleteSampler: samplerId={:x}, latency={}us", samplerId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DeleteSampler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::WaitForGPUIdle() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (m_vulkan->device != VK_NULL_HANDLE) {
      vkDeviceWaitIdle(m_vulkan->device);
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WaitForGPUIdle: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("WaitForGPUIdle failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::InsertFence(uint64_t *fenceValue) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkFenceCreateInfo fenceInfo{};
    fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;
    VkFence fence;
    if (vkCreateFence(m_vulkan->device, &fenceInfo, nullptr, &fence) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create fence");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan fence creation failed");
    }
    *fenceValue = m_nextFenceValue;
    m_fences[*fenceValue] = fence;
    ++m_nextFenceValue;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("InsertFence: fenceValue={}, latency={}us", *fenceValue,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("InsertFence failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

bool PS4GPU::CheckFence(uint64_t fenceValue) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_fences.find(fenceValue);
    if (it == m_fences.end()) {
      spdlog::warn("Fence not found: {}", fenceValue);
      m_stats.cacheMisses++;
      return false;
    }
    VkResult result = vkGetFenceStatus(m_vulkan->device, it->second);
    bool signaled = (result == VK_SUCCESS);
    if (signaled) {
      vkDestroyFence(m_vulkan->device, it->second, nullptr);
      m_fences.erase(it);
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CheckFence: fenceValue={}, signaled={}, latency={}us",
                  fenceValue, signaled, m_stats.totalLatencyUs.load());
    return signaled;
  } catch (const std::exception &e) {
    spdlog::error("CheckFence failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

void PS4GPU::BeginGPUProfiler(const std::string &label) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_profileQueryPools.find(label);
    VkQueryPool queryPool;
    if (it == m_profileQueryPools.end()) {
      VkQueryPoolCreateInfo poolInfo{};
      poolInfo.sType = VK_STRUCTURE_TYPE_QUERY_POOL_CREATE_INFO;
      poolInfo.queryType = VK_QUERY_TYPE_TIMESTAMP;
      poolInfo.queryCount = 2;
      if (vkCreateQueryPool(m_vulkan->device, &poolInfo, nullptr, &queryPool) !=
          VK_SUCCESS) {
        spdlog::error("Failed to create query pool for profiling");
        m_stats.cacheMisses++;
        throw GPUException("Vulkan query pool creation failed");
      }
      m_profileQueryPools[label] = queryPool;
    } else {
      queryPool = it->second;
    }
    vkCmdResetQueryPool(m_commandBuffer, queryPool, 0, 2);
    vkCmdWriteTimestamp(m_commandBuffer, VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT,
                        queryPool, 0);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BeginGPUProfiler: label={}, latency={}us", label,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BeginGPUProfiler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::EndGPUProfiler() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (m_profileQueryPools.empty()) {
      spdlog::warn("No active profiling session");
      m_stats.cacheMisses++;
      return;
    }
    auto it = m_profileQueryPools.begin();
    VkQueryPool queryPool = it->second;
    vkCmdWriteTimestamp(m_commandBuffer, VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT,
                        queryPool, 1);
    uint64_t timestamps[2];
    VkResult result = vkGetQueryPoolResults(
        m_vulkan->device, queryPool, 0, 2, sizeof(timestamps), timestamps,
        sizeof(uint64_t), VK_QUERY_RESULT_64_BIT | VK_QUERY_RESULT_WAIT_BIT);
    if (result == VK_SUCCESS) {
      ProfileResult pr;
      pr.label = it->first;
      pr.startTimestamp = timestamps[0];
      pr.endTimestamp = timestamps[1];
      m_profileResults.push_back(pr);
    } else {
      spdlog::warn("Failed to retrieve profiling results");
      m_stats.cacheMisses++;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("EndGPUProfiler: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("EndGPUProfiler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

std::vector<ProfileResult> PS4GPU::GetProfileResults() {
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  return m_profileResults;
}

VkDevice PS4GPU::GetDevice() const {
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  return m_vulkan ? m_vulkan->device : VK_NULL_HANDLE;
}

VkCommandBuffer PS4GPU::GetCurrentCommandBuffer() const {
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  return m_commandBuffer;
}

const GNMRegisterState &PS4GPU::GetGNMState() const {
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  return m_gnmState;
}

void PS4GPU::SetTessellationState(float tessFactor, uint32_t patchControlPoints) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    TessellationState tessState;
    tessState.tessFactor = tessFactor;
    tessState.patchControlPoints = patchControlPoints;
    tessState.topology = VK_PRIMITIVE_TOPOLOGY_PATCH_LIST;
    // Store tessellation state in context registers instead of using non-existent method
    m_gnmState.SetContextRegister(0x300, *reinterpret_cast<const uint32_t*>(&tessFactor));
    m_gnmState.SetContextRegister(0x301, patchControlPoints);
    m_pipelineStateDirty = true;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetTessellationState: tessFactor={}, patchControlPoints={}, "
                  "latency={}us",
                  tessFactor, patchControlPoints, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetTessellationState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::BindComputeShader(uint64_t shaderId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_shaderModules.find(shaderId);
    if (it == m_shaderModules.end() || it->second.type != GCNShaderType::COMPUTE) {
      spdlog::error("Compute shader not found or invalid: {}", shaderId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid compute shader ID");
    }
    // Store compute shader ID in shader register instead of using non-existent method
    m_gnmState.SetShaderRegister(static_cast<uint32_t>(GCNShaderType::COMPUTE), 0x30, static_cast<uint32_t>(shaderId & 0xFFFFFFFF));
    m_gnmState.SetShaderRegister(static_cast<uint32_t>(GCNShaderType::COMPUTE), 0x31, static_cast<uint32_t>(shaderId >> 32));
    m_pipelineStateDirty = true;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BindComputeShader: shaderId={:x}, latency={}us", shaderId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindComputeShader failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint64_t PS4GPU::CreateTiledTexture(uint64_t surfaceId, uint32_t tileMode) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end()) {
      spdlog::error("Surface not found for tiled texture creation: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid surface ID for tiled texture");
    }
    if (!m_tileManager) {
      spdlog::error("TileManager not initialized");
      m_stats.cacheMisses++;
      throw GPUException("TileManager not set");
    }
    m_tileManager->SetTileMode(surfaceId, tileMode);
    VkImageViewCreateInfo viewInfo{};
    viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    viewInfo.image = it->second.image;
    viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
    viewInfo.format = it->second.format;
    viewInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    viewInfo.subresourceRange.baseMipLevel = 0;
    viewInfo.subresourceRange.levelCount = 1;
    viewInfo.subresourceRange.baseArrayLayer = 0;
    viewInfo.subresourceRange.layerCount = 1;
    VkImageView textureView;
    if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr, &textureView) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create tiled texture view");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan image view creation failed");
    }
    uint64_t textureId = surfaceId;
    m_textureViews[textureId] = textureView;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateTiledTexture: surfaceId={:x}, textureId={:x}, tileMode={}, "
                  "latency={}us",
                  surfaceId, textureId, tileMode, m_stats.totalLatencyUs.load());
    return textureId;
  } catch (const std::exception &e) {
    spdlog::error("CreateTiledTexture failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

uint64_t PS4GPU::MapTiledMemory(uint64_t cpuAddress, size_t size, uint32_t tileMode) {
  auto start = std::chrono::steady_clock::now();
  uint64_t gpuAddress = 0;

  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      if (!m_tileManager) {
        spdlog::error("TileManager not initialized");
        m_stats.cacheMisses++;
        throw GPUException("TileManager not set");
      }
      VkDeviceMemory memory;
      VkBuffer buffer = CreateBuffer(size, VK_BUFFER_USAGE_TRANSFER_SRC_BIT |
                                          VK_BUFFER_USAGE_TRANSFER_DST_BIT |
                                          VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                                     VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                                         VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                                     memory);
      void *mappedData;
      if (vkMapMemory(m_vulkan->device, memory, 0, size, 0, &mappedData) !=
          VK_SUCCESS) {
        spdlog::error("Failed to map tiled memory");
        vkDestroyBuffer(m_vulkan->device, buffer, nullptr);
        vkFreeMemory(m_vulkan->device, memory, nullptr);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan memory mapping failed");
      }
      gpuAddress = m_tileManager->MapTiledMemory(cpuAddress, size, tileMode);
      MemoryMapping mapping;
      mapping.gpuAddress = gpuAddress;
      mapping.cpuAddress = cpuAddress;
      mapping.size = size;
      mapping.buffer = buffer;
      mapping.memory = memory;
      mapping.mappedData = mappedData;
      m_memoryMappings[gpuAddress] = mapping;
      m_stats.vramUsage += size;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("MapTiledMemory: cpuAddress={:x}, size={}, gpuAddress={:x}, "
                    "tileMode={}, latency={}us",
                    cpuAddress, size, gpuAddress, tileMode,
                    m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("MapTiledMemory failed: {}", e.what());
      m_stats.cacheMisses++;
      return 0;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in MapTiledMemory: {}", e.what());
  }

  return gpuAddress;
}

void PS4GPU::NotifyPacketProcessed(uint32_t header,
                                   const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyPacketProcessed: header=0x{:x}, data_size={}, latency={}us",
        header, data.size(), m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("NotifyPacketProcessed failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                                    const std::vector<uint32_t> &spirvCode) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    ShaderModule shader;
    shader.type = type;
    shader.spirvCode = spirvCode;
    VkShaderModuleCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
    createInfo.pCode = spirvCode.data();
    if (vkCreateShaderModule(m_vulkan->device, &createInfo, nullptr,
                             &shader.module) != VK_SUCCESS) {
      spdlog::error("Failed to create shader module from translated SPIR-V");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan shader module creation failed");
    }
    uint64_t shaderId = bytecodeHash;
    m_shaderModules[shaderId] = shader;
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderTranslated: type={}, hash={:x}, spirv_size={}, latency={}us",
        static_cast<int>(type), bytecodeHash, spirvCode.size(),
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("NotifyShaderTranslated failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                                    const std::string &glslCode) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderTranslated: type={}, hash={:x}, glsl_size={}, latency={}us",
        static_cast<int>(type), bytecodeHash, glslCode.size(),
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("NotifyShaderTranslated failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::NotifyRegisterChange(GNMRegisterType regType, uint32_t stage,
                                  uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    switch (regType) {
    case GNMRegisterType::SHADER_REG:
      HandleShaderRegisterChange(stage, offset, value);
      break;
    case GNMRegisterType::CONTEXT_REG:
      HandleContextRegisterChange(offset, value);
      break;
    case GNMRegisterType::CONFIG_REG:
      HandleConfigRegisterChange(offset, value);
      break;
    case GNMRegisterType::USER_REG:
      HandleUserRegisterChange(offset, value);
      break;
    default:
      spdlog::warn("Unknown register type: {}", static_cast<int>(regType));
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyRegisterChange: type={}, stage={}, offset=0x{:x}, value=0x{:x}, "
        "latency={}us",
        static_cast<int>(regType), stage, offset, value,
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("NotifyRegisterChange failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::HandleShaderRegisterChange(uint32_t stage, uint32_t offset,
                                        uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  try {
    switch (stage) {
    case static_cast<uint32_t>(GCNShaderType::PIXEL):
      switch (offset) {
      case 0x0: // SPI_SHADER_PGM_LO_PS
      case 0x1: // SPI_SHADER_PGM_HI_PS
      case 0x2: // SPI_SHADER_PGM_RSRC1_PS
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated PS shader register 0x{:x}: 0x{:x}", offset, value);
        break;
      default:
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        break;
      }
      break;
    case static_cast<uint32_t>(GCNShaderType::HULL):
      switch (offset) {
      case 0x10: // SPI_SHADER_PGM_LO_HS
      case 0x11: // SPI_SHADER_PGM_HI_HS
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated HS shader address register 0x{:x}: 0x{:x}", offset, value);
        break;
      case 0x12: // SPI_SHADER_PGM_RSRC1_HS
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated HS resource register 0x{:x}: 0x{:x}", offset, value);
        break;
      default:
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        break;
      }
      break;
    case static_cast<uint32_t>(GCNShaderType::DOMAIN_SHADER):
      switch (offset) {
      case 0x20: // SPI_SHADER_PGM_LO_DS
      case 0x21: // SPI_SHADER_PGM_HI_DS
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated DS shader address register 0x{:x}: 0x{:x}", offset, value);
        break;
      case 0x22: // SPI_SHADER_PGM_RSRC1_DS
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated DS resource register 0x{:x}: 0x{:x}", offset, value);
        break;
      default:
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        break;
      }
      break;
    case static_cast<uint32_t>(GCNShaderType::COMPUTE):
      switch (offset) {
      case 0x30: // COMPUTE_PGM_LO
      case 0x31: // COMPUTE_PGM_HI
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated CS shader address register 0x{:x}: 0x{:x}", offset, value);
        break;
      case 0x32: // COMPUTE_PGM_RSRC1
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated CS resource register 0x{:x}: 0x{:x}", offset, value);
        break;
      default:
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        break;
      }
      break;
    default:
      m_gnmState.SetShaderRegister(stage, offset, value);
      m_pipelineStateDirty = true;
      spdlog::trace(
          "Updated shader register: stage={}, offset=0x{:x}, value=0x{:x}",
          stage, offset, value);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("HandleShaderRegisterChange failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::HandleContextRegisterChange(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  try {
    switch (offset) {
    case 0x80: // PA_CL_VPORT_XSCALE
    case 0x81: // PA_CL_VPORT_XOFFSET
    case 0x82: // PA_CL_VPORT_YSCALE
    case 0x83: // PA_CL_VPORT_YOFFSET
    case 0x84: // PA_CL_VPORT_ZSCALE
    case 0x85: // PA_CL_VPORT_ZOFFSET
      m_gnmState.SetContextRegister(offset, value);
      m_viewportStateDirty = true;
      spdlog::debug("Updated viewport register 0x{:x}: {}", offset,
                    *reinterpret_cast<const float*>(&value));
      break;
    case 0x100: // CB_COLOR0_BASE
    case 0x101: // CB_COLOR1_BASE
    case 0x102: // CB_COLOR2_BASE
    case 0x103: // CB_COLOR3_BASE
    case 0x104: // CB_COLOR4_BASE
    case 0x105: // CB_COLOR5_BASE
    case 0x106: // CB_COLOR6_BASE
    case 0x107: // CB_COLOR7_BASE
      {
        uint32_t rtIndex = offset - 0x100;
        uint64_t baseAddr = static_cast<uint64_t>(value) << 8;
        m_gnmState.SetContextRegister(offset, value);
        m_renderTargetStateDirty = true;
        spdlog::debug("Updated render target {} base address: 0x{:x}", rtIndex,
                      baseAddr);
      }
      break;
    case 0x200: // PA_SC_VPORT_SCISSOR_0_TL
    case 0x201: // PA_SC_VPORT_SCISSOR_0_BR
      m_gnmState.SetContextRegister(offset, value);
      m_viewportStateDirty = true;
      spdlog::debug("Updated scissor register 0x{:x}: 0x{:x}", offset, value);
      break;
    case 0x300: // VGT_TESS_LEVEL_OUTER and other tessellation registers
    case 0x301:
      {
        float tessFactor = *reinterpret_cast<const float*>(&value);
        m_gnmState.SetContextRegister(offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated tessellation register 0x{:x}: {}", offset, tessFactor);
      }
      break;
    default:
      m_gnmState.SetContextRegister(offset, value);
      spdlog::trace("Updated context register: offset=0x{:x}, value=0x{:x}",
                    offset, value);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("HandleContextRegisterChange failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::HandleConfigRegisterChange(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  try {
    m_gnmState.SetConfigRegister(offset, value);
    switch (offset) {
    case 0x200: // PA_SU_SC_MODE_CNTL
      m_pipelineStateDirty = true;
      spdlog::debug("Updated screen coordinate mode control: 0x{:x}", value);
      break;
    default:
      spdlog::trace("Updated config register: offset=0x{:x}, value=0x{:x}",
                    offset, value);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("HandleConfigRegisterChange failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::HandleUserRegisterChange(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  try {
    m_gnmState.SetUserRegister(offset, value);
    spdlog::trace("Updated user register: offset=0x{:x}, value=0x{:x}", offset,
                  value);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("HandleUserRegisterChange failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::NotifyShaderExecuted(GCNShaderType shaderType,
                                  uint64_t instructionCount) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderExecuted: type={}, instruction_count={}, latency={}us",
        static_cast<int>(shaderType), instructionCount,
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("NotifyShaderExecuted failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

GPUStats PS4GPU::GetStats() const {
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  return m_stats;
}

void PS4GPU::ClearShaderCache() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    for (auto &[shaderId, shader] : m_shaderModules) {
      if (shader.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, shader.module, nullptr);
      }
    }
    m_shaderModules.clear();
    m_stats.shaderCount = 0;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Shader cache cleared, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("ClearShaderCache failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::ClearRenderTargetCache() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    for (auto &[targetId, target] : m_renderTargets) {
      if (target.view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, target.view, nullptr);
      }
      if (target.image != VK_NULL_HANDLE) {
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
      }
      if (target.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, target.memory, nullptr);
      }
    }
    m_renderTargets.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Render target cache cleared, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("ClearRenderTargetCache failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  try {
    std::vector<uint8_t> serializedData = m_gnmState.Serialize();
    out.write(reinterpret_cast<const char*>(serializedData.data()), serializedData.size());
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SaveState: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    // Read the data from stream into a vector
    std::vector<uint8_t> data((std::istreambuf_iterator<char>(in)),
                              std::istreambuf_iterator<char>());
    m_gnmState.Deserialize(data);
    m_pipelineStateDirty = true;
    m_viewportStateDirty = true;
    m_renderTargetStateDirty = true;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("LoadState: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

GNMRegisterState &PS4GPU::GetMutableGNMState() {
  return m_gnmState;
}

void PS4GPU::SetTileManager(std::unique_ptr<TileManager> tileManager) {
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  m_tileManager = std::move(tileManager);
}

TileManager &PS4GPU::GetTileManager() {
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  if (!m_tileManager) {
    throw GPUException("TileManager not set");
  }
  return *m_tileManager;
}

GNMRegisterState &PS4GPU::GetRegisterState() {
  return m_gnmState;
}

bool PS4GPU::CreateSwapchain() {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check if swapchain already exists (created by main thread)
    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      spdlog::info("PS4GPU: Reusing existing swapchain created by main thread");

      // Get existing swapchain images
      uint32_t imageCount;
      vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount, nullptr);
      m_vulkan->swapchainImages.resize(imageCount);
      vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount,
                              m_vulkan->swapchainImages.data());

      // Update statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        spdlog::info("CreateSwapchain: Reused existing swapchain with {} images, latency={}us",
                     imageCount, m_stats.totalLatencyUs.load());
      }
      return true;
    }

    // Use existing surface instead of creating a new one
    if (m_vulkan->surface == VK_NULL_HANDLE) {
      spdlog::error("Vulkan surface not initialized");

      // Update error statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheMisses++;
      }
      throw GPUException("Vulkan surface not available");
    }

    spdlog::debug("Creating new Vulkan swapchain for PS4GPU");

    // Add timeout check for surface capabilities query
    auto checkTimeout = [&](const std::string& step) {
      auto current = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current - start);
      if (elapsed.count() > 30) { // 30 second timeout for swapchain creation
        spdlog::error("PS4GPU swapchain creation timed out during: {}", step);
        throw GPUException("Swapchain creation timeout");
      }
    };

    spdlog::info("Querying surface capabilities...");
    checkTimeout("surface capabilities query");
    VkSurfaceCapabilitiesKHR capabilities;
    vkGetPhysicalDeviceSurfaceCapabilitiesKHR(m_vulkan->physicalDevice,
                                              m_vulkan->surface, &capabilities);
    spdlog::info("Surface capabilities queried successfully");

    spdlog::info("Querying surface formats...");
    checkTimeout("surface formats query");
    uint32_t formatCount;
    vkGetPhysicalDeviceSurfaceFormatsKHR(m_vulkan->physicalDevice,
                                        m_vulkan->surface, &formatCount, nullptr);
    spdlog::info("Surface formats count: {}", formatCount);
    std::vector<VkSurfaceFormatKHR> formats(formatCount);
    checkTimeout("surface formats retrieval");
    vkGetPhysicalDeviceSurfaceFormatsKHR(m_vulkan->physicalDevice,
                                        m_vulkan->surface, &formatCount,
                                        formats.data());
    VkSurfaceFormatKHR surfaceFormat = formats[0];
    for (const auto &fmt : formats) {
      if (fmt.format == VK_FORMAT_B8G8R8A8_SRGB &&
          fmt.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
        surfaceFormat = fmt;
        break;
      }
    }
    m_vulkan->swapchainImageFormat = surfaceFormat.format;
    uint32_t presentModeCount;
    vkGetPhysicalDeviceSurfacePresentModesKHR(m_vulkan->physicalDevice,
                                              m_vulkan->surface,
                                              &presentModeCount, nullptr);
    std::vector<VkPresentModeKHR> presentModes(presentModeCount);
    vkGetPhysicalDeviceSurfacePresentModesKHR(m_vulkan->physicalDevice,
                                              m_vulkan->surface,
                                              &presentModeCount,
                                              presentModes.data());
    VkPresentModeKHR presentMode = VK_PRESENT_MODE_FIFO_KHR;
    for (const auto &mode : presentModes) {
      if (mode == VK_PRESENT_MODE_MAILBOX_KHR) {
        presentMode = mode;
        break;
      }
    }
    m_vulkan->swapchainExtent = capabilities.currentExtent;
    if (m_vulkan->swapchainExtent.width == UINT32_MAX) {
      int width, height;
      SDL_Vulkan_GetDrawableSize(m_window, &width, &height);
      m_vulkan->swapchainExtent.width = std::clamp(
          static_cast<uint32_t>(width), capabilities.minImageExtent.width,
          capabilities.maxImageExtent.width);
      m_vulkan->swapchainExtent.height = std::clamp(
          static_cast<uint32_t>(height), capabilities.minImageExtent.height,
          capabilities.maxImageExtent.height);
    }
    uint32_t imageCount = capabilities.minImageCount + 1;
    if (capabilities.maxImageCount > 0 && imageCount > capabilities.maxImageCount) {
      imageCount = capabilities.maxImageCount;
    }
    VkSwapchainCreateInfoKHR createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
    createInfo.surface = m_vulkan->surface;
    createInfo.minImageCount = imageCount;
    createInfo.imageFormat = surfaceFormat.format;
    createInfo.imageColorSpace = surfaceFormat.colorSpace;
    createInfo.imageExtent = m_vulkan->swapchainExtent;
    createInfo.imageArrayLayers = 1;
    createInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    uint32_t queueFamilyIndices[] = {m_vulkan->graphicsQueueFamily,
                                     m_vulkan->presentQueueFamily};
    if (m_vulkan->graphicsQueueFamily != m_vulkan->presentQueueFamily) {
      createInfo.imageSharingMode = VK_SHARING_MODE_CONCURRENT;
      createInfo.queueFamilyIndexCount = 2;
      createInfo.pQueueFamilyIndices = queueFamilyIndices;
    } else {
      createInfo.imageSharingMode = VK_SHARING_MODE_EXCLUSIVE;
    }
    createInfo.preTransform = capabilities.currentTransform;
    createInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
    createInfo.presentMode = presentMode;
    createInfo.clipped = VK_TRUE;
    createInfo.oldSwapchain = VK_NULL_HANDLE;

    spdlog::info("Creating Vulkan swapchain with extent {}x{}, format {}, imageCount {}",
                 createInfo.imageExtent.width, createInfo.imageExtent.height,
                 static_cast<int>(createInfo.imageFormat), createInfo.minImageCount);
    checkTimeout("swapchain creation");
    VkResult result = vkCreateSwapchainKHR(m_vulkan->device, &createInfo, nullptr,
                                           &m_vulkan->swapchain);
    spdlog::info("vkCreateSwapchainKHR returned: {}", static_cast<int>(result));
    if (result != VK_SUCCESS) {
      spdlog::error("Failed to create swapchain");
      // Don't destroy the surface since it's managed by main.cpp

      // Update error statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheMisses++;
      }
      throw GPUException("Vulkan swapchain creation failed");
    }
    vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount,
                            nullptr);
    m_vulkan->swapchainImages.resize(imageCount);
    vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount,
                            m_vulkan->swapchainImages.data());

    // Update success statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("CreateSwapchain: Created swapchain with {} images, latency={}us",
                   imageCount, m_stats.totalLatencyUs.load());
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSwapchain failed: {}", e.what());

    // Update error statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

bool PS4GPU::CreateSwapchainImageViews() {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check if image views already exist (created by main thread)
    if (!m_vulkan->swapchainImageViews.empty() &&
        m_vulkan->swapchainImageViews.size() == m_vulkan->swapchainImages.size()) {
      spdlog::info("PS4GPU: Reusing existing swapchain image views created by main thread");

      // Update statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        spdlog::info("CreateSwapchainImageViews: Reused {} existing image views, latency={}us",
                     m_vulkan->swapchainImageViews.size(), m_stats.totalLatencyUs.load());
      }
      return true;
    }

    // Note: Not acquiring contextMutex during initialization to avoid deadlock
    m_vulkan->swapchainImageViews.resize(m_vulkan->swapchainImages.size());
    for (size_t i = 0; i < m_vulkan->swapchainImages.size(); ++i) {
      VkImageViewCreateInfo viewInfo{};
      viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
      viewInfo.image = m_vulkan->swapchainImages[i];
      viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
      viewInfo.format = m_vulkan->swapchainImageFormat;
      viewInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
      viewInfo.subresourceRange.baseMipLevel = 0;
      viewInfo.subresourceRange.levelCount = 1;
      viewInfo.subresourceRange.baseArrayLayer = 0;
      viewInfo.subresourceRange.layerCount = 1;
      if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr,
                            &m_vulkan->swapchainImageViews[i]) != VK_SUCCESS) {
        spdlog::error("Failed to create image view for swapchain image {}", i);

        // Update error statistics with GPU lock
        {
          COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
          m_stats.cacheMisses++;
        }
        throw GPUException("Vulkan image view creation failed");
      }
    }

    // Update success statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("CreateSwapchainImageViews: Created {} image views, latency={}us",
                   m_vulkan->swapchainImageViews.size(), m_stats.totalLatencyUs.load());
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSwapchainImageViews failed: {}", e.what());

    // Update error statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

bool PS4GPU::RecreateSwapchain() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    vkDeviceWaitIdle(m_vulkan->device);
    COMPONENT_LOCK(m_vulkan->contextMutex, "VulkanContextMutex");
    for (auto &[_, framebuffer] : m_framebufferCache) {
      if (framebuffer != VK_NULL_HANDLE) {
        vkDestroyFramebuffer(m_vulkan->device, framebuffer, nullptr);
      }
    }
    m_framebufferCache.clear();
    for (auto &view : m_vulkan->swapchainImageViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_vulkan->swapchainImageViews.clear();
    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      vkDestroySwapchainKHR(m_vulkan->device, m_vulkan->swapchain, nullptr);
      m_vulkan->swapchain = VK_NULL_HANDLE;
    }
    lock.unlock();
    if (!CreateSwapchain() || !CreateSwapchainImageViews() ||
        !CreateFramebuffers()) {
      spdlog::error("Failed to recreate swapchain");
      m_stats.cacheMisses++;
      throw GPUException("Swapchain recreation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("RecreateSwapchain: Swapchain recreated, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RecreateSwapchain failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4GPU::CreateCommandPool() {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check if command pool already exists (created by main thread)
    if (m_vulkan->commandPool != VK_NULL_HANDLE) {
      spdlog::info("PS4GPU: Reusing existing command pool created by main thread");

      // Update statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        spdlog::info("CreateCommandPool: Reused existing command pool, latency={}us",
                     m_stats.totalLatencyUs.load());
      }
      return true;
    }

    // Note: Not acquiring contextMutex during initialization to avoid deadlock
    VkCommandPoolCreateInfo poolInfo{};
    poolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
    poolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
    poolInfo.queueFamilyIndex = m_vulkan->graphicsQueueFamily;
    if (vkCreateCommandPool(m_vulkan->device, &poolInfo, nullptr,
                            &m_vulkan->commandPool) != VK_SUCCESS) {
      spdlog::error("Failed to create command pool");

      // Update error statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheMisses++;
      }
      throw GPUException("Vulkan command pool creation failed");
    }

    // Update success statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("CreateCommandPool: Command pool created, latency={}us",
                   m_stats.totalLatencyUs.load());
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateCommandPool failed: {}", e.what());

    // Update error statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

bool PS4GPU::CreateDescriptorPool() {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check if descriptor pool already exists (created by main thread)
    if (m_vulkan->descriptorPool != VK_NULL_HANDLE) {
      spdlog::info("PS4GPU: Reusing existing descriptor pool created by main thread");

      // Update statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        spdlog::info("CreateDescriptorPool: Reused existing descriptor pool, latency={}us",
                     m_stats.totalLatencyUs.load());
      }
      return true;
    }

    COMPONENT_LOCK(m_vulkan->contextMutex, "VulkanContextMutex");
    VkDescriptorPoolSize poolSizes[] = {
        {VK_DESCRIPTOR_TYPE_SAMPLER, 1000},
        {VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1000},
        {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1000},
        {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1000}
    };
    VkDescriptorPoolCreateInfo poolInfo{};
    poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
    poolInfo.poolSizeCount = sizeof(poolSizes) / sizeof(poolSizes[0]);
    poolInfo.pPoolSizes = poolSizes;
    poolInfo.maxSets = 4000;
    if (vkCreateDescriptorPool(m_vulkan->device, &poolInfo, nullptr,
                               &m_vulkan->descriptorPool) != VK_SUCCESS) {
      spdlog::error("Failed to create descriptor pool");

      // Update error statistics with GPU lock
      {
        COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
        m_stats.cacheMisses++;
      }
      throw GPUException("Vulkan descriptor pool creation failed");
    }

    // Update success statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("CreateDescriptorPool: Descriptor pool created, latency={}us",
                   m_stats.totalLatencyUs.load());
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDescriptorPool failed: {}", e.what());

    // Update error statistics with GPU lock
    {
      COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

bool PS4GPU::CreateSyncObjects() {
  auto start = std::chrono::steady_clock::now();
  // Note: m_gpuMutex is already held by the calling method (Initialize)
  try {
    // Check if sync objects already exist (created by main thread)
    if (m_vulkan->imageAvailableSemaphore != VK_NULL_HANDLE &&
        m_vulkan->renderFinishedSemaphore != VK_NULL_HANDLE &&
        m_vulkan->inFlightFence != VK_NULL_HANDLE) {
      spdlog::info("PS4GPU: Reusing existing sync objects created by main thread");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
      spdlog::info("CreateSyncObjects: Reused existing sync objects, latency={}us",
                   m_stats.totalLatencyUs.load());
      return true;
    }

    // Note: Not acquiring contextMutex during initialization to avoid deadlock
    VkSemaphoreCreateInfo semaphoreInfo{};
    semaphoreInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;
    VkFenceCreateInfo fenceInfo{};
    fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;
    if (vkCreateSemaphore(m_vulkan->device, &semaphoreInfo, nullptr,
                          &m_vulkan->imageAvailableSemaphore) != VK_SUCCESS ||
        vkCreateSemaphore(m_vulkan->device, &semaphoreInfo, nullptr,
                          &m_vulkan->renderFinishedSemaphore) != VK_SUCCESS ||
        vkCreateFence(m_vulkan->device, &fenceInfo, nullptr,
                      &m_vulkan->inFlightFence) != VK_SUCCESS) {
      spdlog::error("Failed to create synchronization objects");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan sync objects creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateSyncObjects: Sync objects created, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSyncObjects failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4GPU::CreateDefaultRenderPass() {
  auto start = std::chrono::steady_clock::now();
  // Note: m_gpuMutex is already held by the calling method (Initialize)
  try {
    // Check if render pass already exists (created by main thread)
    if (m_vulkan->renderPass != VK_NULL_HANDLE) {
      spdlog::info("PS4GPU: Reusing existing render pass created by main thread");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
      spdlog::info("CreateDefaultRenderPass: Reused existing render pass, latency={}us",
                   m_stats.totalLatencyUs.load());
      return true;
    }

    // Note: Not acquiring contextMutex during initialization to avoid deadlock
    VkAttachmentDescription colorAttachment{};
    colorAttachment.format = m_vulkan->swapchainImageFormat;
    colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
    colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
    colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
    colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
    colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
    colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;
    VkAttachmentReference colorAttachmentRef{};
    colorAttachmentRef.attachment = 0;
    colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
    VkSubpassDescription subpass{};
    subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
    subpass.colorAttachmentCount = 1;
    subpass.pColorAttachments = &colorAttachmentRef;
    VkSubpassDependency dependency{};
    dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
    dependency.dstSubpass = 0;
    dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
    dependency.srcAccessMask = 0;
    dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
    dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_READ_BIT |
                               VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
    VkRenderPassCreateInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
    renderPassInfo.attachmentCount = 1;
    renderPassInfo.pAttachments = &colorAttachment;
    renderPassInfo.subpassCount = 1;
    renderPassInfo.pSubpasses = &subpass;
    renderPassInfo.dependencyCount = 1;
    renderPassInfo.pDependencies = &dependency;
    if (vkCreateRenderPass(m_vulkan->device, &renderPassInfo, nullptr,
                           &m_vulkan->renderPass) != VK_SUCCESS) {
      spdlog::error("Failed to create default render pass");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan render pass creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateDefaultRenderPass: Render pass created, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDefaultRenderPass failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4GPU::CreateFramebuffers() {
  auto start = std::chrono::steady_clock::now();
  // Note: m_gpuMutex is already held by the calling method (Initialize/RecreateSwapchain)
  try {
    // Note: Not acquiring contextMutex during initialization to avoid deadlock
    m_vulkan->framebuffers.resize(m_vulkan->swapchainImageViews.size());
    for (size_t i = 0; i < m_vulkan->swapchainImageViews.size(); ++i) {
      VkImageView attachments[] = {m_vulkan->swapchainImageViews[i]};
      VkFramebufferCreateInfo framebufferInfo{};
      framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
      framebufferInfo.renderPass = m_vulkan->renderPass;
      framebufferInfo.attachmentCount = 1;
      framebufferInfo.pAttachments = attachments;
      framebufferInfo.width = m_vulkan->swapchainExtent.width;
      framebufferInfo.height = m_vulkan->swapchainExtent.height;
      framebufferInfo.layers = 1;
      if (vkCreateFramebuffer(m_vulkan->device, &framebufferInfo, nullptr,
                              &m_vulkan->framebuffers[i]) != VK_SUCCESS) {
        spdlog::error("Failed to create framebuffer {}", i);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan framebuffer creation failed");
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateFramebuffers: Created {} framebuffers, latency={}us",
                 m_vulkan->framebuffers.size(), m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateFramebuffers failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

GraphicsPipelineKey PS4GPU::BuildGraphicsPipelineKey() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_gpuMutex, "GPUMutex");
  GraphicsPipelineKey key;
  try {
    // Construct shader IDs from shader registers
    auto getShaderIdFromRegisters = [this](GCNShaderType type) -> uint64_t {
      try {
        uint32_t stage = static_cast<uint32_t>(type);
        uint32_t shaderLo = m_gnmState.GetShaderRegister(stage, 0x0);
        uint32_t shaderHi = m_gnmState.GetShaderRegister(stage, 0x1);
        return (static_cast<uint64_t>(shaderHi) << 32) | shaderLo;
      } catch (const std::exception &e) {
        return 0; // No shader bound for this stage
      }
    };

    key.vsShaderId = getShaderIdFromRegisters(GCNShaderType::VERTEX);
    key.psShaderId = getShaderIdFromRegisters(GCNShaderType::PIXEL);
    key.gsShaderId = getShaderIdFromRegisters(GCNShaderType::GEOMETRY);
    key.hsShaderId = getShaderIdFromRegisters(GCNShaderType::HULL);
    key.dsShaderId = getShaderIdFromRegisters(GCNShaderType::DOMAIN_SHADER);
    key.rasterizerStateHash = 0; // Default rasterizer state hash
    key.blendStateHash = 0; // Default blend state hash
    key.depthStencilStateHash = 0; // Default depth stencil state hash
    key.vertexInputHash = 0; // Default vertex input hash
    key.renderPass = m_currentRenderPass;
    key.subpassIndex = 0;
    // Determine topology based on tessellation state
    uint32_t patchControlPoints = 0;
    try {
    // Build tessellation state from context registers
    try {
      uint32_t tessFactorReg = m_gnmState.GetContextRegister(0x300);
      uint32_t patchControlPointsReg = m_gnmState.GetContextRegister(0x301);
      key.tessState.tessFactor = *reinterpret_cast<const float*>(&tessFactorReg);
      key.tessState.patchControlPoints = patchControlPointsReg;
      key.tessState.topology = VK_PRIMITIVE_TOPOLOGY_PATCH_LIST;
    } catch (const std::exception &e) {
      // Default tessellation state if registers not set
      key.tessState.tessFactor = 1.0f;
      key.tessState.patchControlPoints = 0;
      key.tessState.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
    }
    } catch (const std::exception &e) {
      patchControlPoints = 0;
    }
    key.topology = patchControlPoints > 0
        ? VK_PRIMITIVE_TOPOLOGY_PATCH_LIST
        : VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return key;
  } catch (const std::exception &e) {
    spdlog::error("BuildGraphicsPipelineKey failed: {}", e.what());
    m_stats.cacheMisses++;
    return key;
  }
}

VkPipeline PS4GPU::GetOrCreateGraphicsPipeline() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (!m_pipelineStateDirty) {
      if (m_currentGraphicsPipeline != VK_NULL_HANDLE) {
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        spdlog::trace("GetOrCreateGraphicsPipeline: Reusing pipeline, latency={}us",
                      m_stats.totalLatencyUs.load());
        return m_currentGraphicsPipeline;
      }
    }

    GraphicsPipelineKey key = BuildGraphicsPipelineKey();
    auto it = m_graphicsPipelineCache.find(key);
    if (it != m_graphicsPipelineCache.end()) {
      m_currentGraphicsPipeline = it->second;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("GetOrCreateGraphicsPipeline: Cache hit, latency={}us",
                    m_stats.totalLatencyUs.load());
      return m_currentGraphicsPipeline;
    }

    std::vector<VkPipelineShaderStageCreateInfo> shaderStages;
    std::vector<VkShaderModule> shaderModules;

    auto addShaderStage = [&](uint64_t shaderId, VkShaderStageFlagBits stage, GCNShaderType type) {
      if (shaderId != 0) {
        auto shaderIt = m_shaderModules.find(shaderId);
        if (shaderIt == m_shaderModules.end()) {
          spdlog::error("Shader module not found: {}", shaderId);
          m_stats.cacheMisses++;
          throw GPUException(fmt::format("Invalid shader ID for stage {}", static_cast<int>(type)));
        }
        VkPipelineShaderStageCreateInfo stageInfo{};
        stageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
        stageInfo.stage = stage;
        stageInfo.module = shaderIt->second.module;
        stageInfo.pName = "main";
        shaderStages.push_back(stageInfo);
        shaderModules.push_back(shaderIt->second.module);
      }
    };

    addShaderStage(key.vsShaderId, VK_SHADER_STAGE_VERTEX_BIT, GCNShaderType::VERTEX);
    addShaderStage(key.psShaderId, VK_SHADER_STAGE_FRAGMENT_BIT, GCNShaderType::PIXEL);
    addShaderStage(key.gsShaderId, VK_SHADER_STAGE_GEOMETRY_BIT, GCNShaderType::GEOMETRY);
    addShaderStage(key.hsShaderId, VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT, GCNShaderType::HULL);
    addShaderStage(key.dsShaderId, VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT, GCNShaderType::DOMAIN_SHADER);

    VkPipelineVertexInputStateCreateInfo vertexInputInfo{};
        vertexInputInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO;
    // Assume vertex input state is managed by GNM state
    std::vector<VkVertexInputBindingDescription> vertexBindings;
    std::vector<VkVertexInputAttributeDescription> vertexAttributes;
    // Populate from GNM vertex input state
    if (m_registerState) {
      try {
        auto bindings = m_registerState->GetVertexInputBindings();
        vertexBindings.clear();
        for (const auto& b : bindings) {
            VkVertexInputBindingDescription vkDesc{};
            vkDesc.binding = b.binding;
            vkDesc.stride = b.stride;
            vkDesc.inputRate = VK_VERTEX_INPUT_RATE_VERTEX; // Default to per-vertex input rate
            vertexBindings.push_back(vkDesc);
        }
        auto attributes = m_registerState->GetVertexInputAttributes();
        vertexAttributes.clear();
        for (const auto& a : attributes) {
            VkVertexInputAttributeDescription vkAttr{};
            vkAttr.location = a.location;
            vkAttr.binding = a.binding;
            vkAttr.format = static_cast<VkFormat>(a.format);
            vkAttr.offset = a.offset;
            vertexAttributes.push_back(vkAttr);
        }
      } catch (const std::exception &e) {
        spdlog::debug("Failed to get vertex input state: {}", e.what());
        // Use empty vertex input state as fallback
      }
    }
    vertexInputInfo.vertexBindingDescriptionCount = vertexBindings.size();
    vertexInputInfo.pVertexBindingDescriptions = vertexBindings.data();
    vertexInputInfo.vertexAttributeDescriptionCount = vertexAttributes.size();
    vertexInputInfo.pVertexAttributeDescriptions = vertexAttributes.data();

    VkPipelineInputAssemblyStateCreateInfo inputAssembly{};
    inputAssembly.sType = VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO;
    inputAssembly.topology = key.topology;
    inputAssembly.primitiveRestartEnable = VK_FALSE;

    VkPipelineViewportStateCreateInfo viewportState{};
    viewportState.sType = VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO;
    viewportState.viewportCount = 1;
    viewportState.scissorCount = 1;

    VkPipelineRasterizationStateCreateInfo rasterizer{};
    rasterizer.sType = VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO;
    rasterizer.depthClampEnable = VK_FALSE;
    rasterizer.rasterizerDiscardEnable = VK_FALSE;
    rasterizer.polygonMode = VK_POLYGON_MODE_FILL;
    rasterizer.lineWidth = 1.0f;
    rasterizer.cullMode = VK_CULL_MODE_BACK_BIT;
    rasterizer.frontFace = VK_FRONT_FACE_COUNTER_CLOCKWISE;
    rasterizer.depthBiasEnable = VK_FALSE;

    VkPipelineMultisampleStateCreateInfo multisampling{};
    multisampling.sType = VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO;
    multisampling.sampleShadingEnable = VK_FALSE;
    multisampling.rasterizationSamples = VK_SAMPLE_COUNT_1_BIT;

    VkPipelineColorBlendAttachmentState colorBlendAttachment{};
    colorBlendAttachment.colorWriteMask =
        VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT |
        VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT;
    colorBlendAttachment.blendEnable = VK_FALSE;

    VkPipelineColorBlendStateCreateInfo colorBlending{};
    colorBlending.sType = VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO;
    colorBlending.logicOpEnable = VK_FALSE;
    colorBlending.attachmentCount = 1;
    colorBlending.pAttachments = &colorBlendAttachment;

    VkPipelineDepthStencilStateCreateInfo depthStencil{};
    depthStencil.sType = VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO;
    depthStencil.depthTestEnable = VK_TRUE;
    depthStencil.depthWriteEnable = VK_TRUE;
    depthStencil.depthCompareOp = VK_COMPARE_OP_LESS;
    depthStencil.depthBoundsTestEnable = VK_FALSE;
    depthStencil.stencilTestEnable = VK_FALSE;

    VkPipelineTessellationStateCreateInfo tessellationState{};
    tessellationState.sType = VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_STATE_CREATE_INFO;
    tessellationState.patchControlPoints = key.tessState.patchControlPoints;

    VkDynamicState dynamicStates[] = {
        VK_DYNAMIC_STATE_VIEWPORT,
        VK_DYNAMIC_STATE_SCISSOR
    };
    VkPipelineDynamicStateCreateInfo dynamicState{};
    dynamicState.sType = VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO;
    dynamicState.dynamicStateCount = sizeof(dynamicStates) / sizeof(dynamicStates[0]);
    dynamicState.pDynamicStates = dynamicStates;

    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    VkDescriptorSetLayout descriptorSetLayout = GetOrCreateDescriptorSetLayout();
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &descriptorSetLayout;

    if (m_currentPipelineLayout != VK_NULL_HANDLE) {
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout, nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
    }
    if (vkCreatePipelineLayout(m_vulkan->device, &pipelineLayoutInfo, nullptr,
                              &m_currentPipelineLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create pipeline layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan pipeline layout creation failed");
    }

    VkGraphicsPipelineCreateInfo pipelineInfo{};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO;
    pipelineInfo.stageCount = shaderStages.size();
    pipelineInfo.pStages = shaderStages.data();
    pipelineInfo.pVertexInputState = &vertexInputInfo;
    pipelineInfo.pInputAssemblyState = &inputAssembly;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pRasterizationState = &rasterizer;
    pipelineInfo.pMultisampleState = &multisampling;
    pipelineInfo.pDepthStencilState = &depthStencil;
    pipelineInfo.pColorBlendState = &colorBlending;
    pipelineInfo.pDynamicState = &dynamicState;
    pipelineInfo.layout = m_currentPipelineLayout;
    pipelineInfo.renderPass = key.renderPass;
    pipelineInfo.subpass = key.subpassIndex;
    if (key.tessState.patchControlPoints > 0) {
      pipelineInfo.pTessellationState = &tessellationState;
    }

    VkPipeline graphicsPipeline;
    if (vkCreateGraphicsPipelines(m_vulkan->device, VK_NULL_HANDLE, 1,
                                  &pipelineInfo, nullptr, &graphicsPipeline) != VK_SUCCESS) {
      spdlog::error("Failed to create graphics pipeline");
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout, nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
      m_stats.cacheMisses++;
      throw GPUException("Vulkan graphics pipeline creation failed");
    }

    m_graphicsPipelineCache[key] = graphicsPipeline;
    m_currentGraphicsPipeline = graphicsPipeline;
    m_pipelineStateDirty = false;
    m_stats.pipelineCreations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateGraphicsPipeline: Created pipeline with {} stages, latency={}us",
                 shaderStages.size(), m_stats.totalLatencyUs.load());
    return graphicsPipeline;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateGraphicsPipeline failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

VkPipeline PS4GPU::GetOrCreateComputePipeline() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    ComputePipelineKey key;
    // Reconstruct compute shader ID from shader registers
    uint32_t shaderLo = m_gnmState.GetShaderRegister(static_cast<uint32_t>(GCNShaderType::COMPUTE), 0x30);
    uint32_t shaderHi = m_gnmState.GetShaderRegister(static_cast<uint32_t>(GCNShaderType::COMPUTE), 0x31);
    key.csShaderId = (static_cast<uint64_t>(shaderHi) << 32) | shaderLo;
    key.resourceStateHash = m_gnmState.GetResourceStateHash();

    auto it = m_computePipelineCache.find(key);
    if (it != m_computePipelineCache.end()) {
      m_currentComputePipeline = it->second;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("GetOrCreateComputePipeline: Cache hit, shaderId={:x}, latency={}us",
                    key.csShaderId, m_stats.totalLatencyUs.load());
      return m_currentComputePipeline;
    }

    auto shaderIt = m_shaderModules.find(key.csShaderId);
    if (shaderIt == m_shaderModules.end() || shaderIt->second.type != GCNShaderType::COMPUTE) {
      spdlog::error("Compute shader module not found or invalid: {}", key.csShaderId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid compute shader module");
    }

    VkPipelineShaderStageCreateInfo shaderStage{};
    shaderStage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    shaderStage.stage = VK_SHADER_STAGE_COMPUTE_BIT;
    shaderStage.module = shaderIt->second.module;
    shaderStage.pName = "main";

    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    VkDescriptorSetLayout descriptorSetLayout = GetOrCreateDescriptorSetLayout();
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &descriptorSetLayout;

    if (m_currentPipelineLayout != VK_NULL_HANDLE) {
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout, nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
    }
    if (vkCreatePipelineLayout(m_vulkan->device, &pipelineLayoutInfo, nullptr,
                              &m_currentPipelineLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create compute pipeline layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan pipeline layout creation failed");
    }

    VkComputePipelineCreateInfo pipelineInfo{};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO;
    pipelineInfo.stage = shaderStage;
    pipelineInfo.layout = m_currentPipelineLayout;

    VkPipeline computePipeline;
    if (vkCreateComputePipelines(m_vulkan->device, VK_NULL_HANDLE, 1,
                                 &pipelineInfo, nullptr, &computePipeline) != VK_SUCCESS) {
      spdlog::error("Failed to create compute pipeline");
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout, nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
      m_stats.cacheMisses++;
      throw GPUException("Vulkan compute pipeline creation failed");
    }

    m_computePipelineCache[key] = computePipeline;
    m_currentComputePipeline = computePipeline;
    m_stats.pipelineCreations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateComputePipeline: Created pipeline, shaderId={:x}, latency={}us",
                 key.csShaderId, m_stats.totalLatencyUs.load());
    return computePipeline;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateComputePipeline failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

VkRenderPass PS4GPU::GetOrCreateRenderPass(const RenderPassKey &key) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    auto it = m_renderPassCache.find(key);
    if (it != m_renderPassCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("GetOrCreateRenderPass: Cache hit, latency={}us",
                    m_stats.totalLatencyUs.load());
      return it->second;
    }

    std::vector<VkAttachmentDescription> attachments;
    std::vector<VkAttachmentReference> colorAttachments;
    for (const auto &format : key.colorFormats) {
      VkAttachmentDescription colorAttachment{};
      colorAttachment.format = format;
      colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
      colorAttachment.loadOp = key.colorLoadOp;
      colorAttachment.storeOp = key.colorStoreOp;
      colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
      colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
      colorAttachment.initialLayout = key.initialColorLayout;
      colorAttachment.finalLayout = key.finalColorLayout;
      attachments.push_back(colorAttachment);
      VkAttachmentReference colorAttachmentRef{};
      colorAttachmentRef.attachment = static_cast<uint32_t>(colorAttachments.size());
      colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
      colorAttachments.push_back(colorAttachmentRef);
    }

    VkAttachmentDescription depthAttachment{};
    VkAttachmentReference depthAttachmentRef{};
    if (key.depthFormat != VK_FORMAT_UNDEFINED) {
      depthAttachment.format = key.depthFormat;
      depthAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
      depthAttachment.loadOp = key.depthLoadOp;
      depthAttachment.storeOp = key.depthStoreOp;
      depthAttachment.stencilLoadOp = key.depthLoadOp;
      depthAttachment.stencilStoreOp = key.depthStoreOp;
      depthAttachment.initialLayout = key.initialDepthLayout;
      depthAttachment.finalLayout = key.finalDepthLayout;
      attachments.push_back(depthAttachment);
      depthAttachmentRef.attachment = static_cast<uint32_t>(attachments.size() - 1);
      depthAttachmentRef.layout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;
    }

    VkSubpassDescription subpass{};
    subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
    subpass.colorAttachmentCount = static_cast<uint32_t>(colorAttachments.size());
    subpass.pColorAttachments = colorAttachments.data();
    if (key.depthFormat != VK_FORMAT_UNDEFINED) {
      subpass.pDepthStencilAttachment = &depthAttachmentRef;
    }

    VkSubpassDependency dependency{};
    dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
    dependency.dstSubpass = 0;
    dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT |
                              VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.srcAccessMask = 0;
    dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT |
                              VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT |
                               VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;

    VkRenderPassCreateInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
    renderPassInfo.attachmentCount = static_cast<uint32_t>(attachments.size());
    renderPassInfo.pAttachments = attachments.data();
    renderPassInfo.subpassCount = 1;
    renderPassInfo.pSubpasses = &subpass;
    renderPassInfo.dependencyCount = 1;
    renderPassInfo.pDependencies = &dependency;

    VkRenderPass renderPass;
    if (vkCreateRenderPass(m_vulkan->device, &renderPassInfo, nullptr,
                           &renderPass) != VK_SUCCESS) {
      spdlog::error("Failed to create render pass");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan render pass creation failed");
    }

    m_renderPassCache[key] = renderPass;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateRenderPass: Created render pass, colorAttachments={}, latency={}us",
                 colorAttachments.size(), m_stats.totalLatencyUs.load());
    return renderPass;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateRenderPass failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

VkFramebuffer PS4GPU::GetOrCreateFramebuffer(const std::vector<uint64_t> &colorTargetIds,
                                             uint64_t depthTargetId) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    uint64_t key = 0;
    for (const auto &id : colorTargetIds) {
      key ^= id;
    }
    key ^= depthTargetId;

    auto it = m_framebufferCache.find(key);
    if (it != m_framebufferCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("GetOrCreateFramebuffer: Cache hit, latency={}us",
                    m_stats.totalLatencyUs.load());
      return it->second;
    }

    std::vector<VkImageView> attachments;
    RenderPassKey rpKey;
    for (const auto &id : colorTargetIds) {
      auto targetIt = m_renderTargets.find(id);
      if (targetIt == m_renderTargets.end()) {
        spdlog::error("Color render target not found: {}", id);
        m_stats.cacheMisses++;
        throw GPUException("Invalid color render target");
      }
      attachments.push_back(targetIt->second.view);
      rpKey.colorFormats.push_back(targetIt->second.format);
    }
    if (depthTargetId) {
      auto targetIt = m_renderTargets.find(depthTargetId);
      if (targetIt == m_renderTargets.end()) {
        spdlog::error("Depth render target not found: {}", depthTargetId);
        m_stats.cacheMisses++;
        throw GPUException("Invalid depth render target");
      }
      attachments.push_back(targetIt->second.view);
      rpKey.depthFormat = targetIt->second.format;
    }

    VkFramebufferCreateInfo framebufferInfo{};
    framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
    framebufferInfo.renderPass = GetOrCreateRenderPass(rpKey);
    framebufferInfo.attachmentCount = static_cast<uint32_t>(attachments.size());
    framebufferInfo.pAttachments = attachments.data();
    framebufferInfo.width = m_vulkan->swapchainExtent.width;
    framebufferInfo.height = m_vulkan->swapchainExtent.height;
    framebufferInfo.layers = 1;

    VkFramebuffer framebuffer;
    if (vkCreateFramebuffer(m_vulkan->device, &framebufferInfo, nullptr,
                            &framebuffer) != VK_SUCCESS) {
      spdlog::error("Failed to create framebuffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan framebuffer creation failed");
    }

    m_framebufferCache[key] = framebuffer;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateFramebuffer: Created framebuffer, attachments={}, latency={}us",
                 attachments.size(), m_stats.totalLatencyUs.load());
    return framebuffer;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateFramebuffer failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

VkDescriptorSetLayout PS4GPU::GetOrCreateDescriptorSetLayout() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    std::vector<VkDescriptorSetLayoutBinding> bindings;
    VkDescriptorSetLayoutBinding samplerBinding{};
    samplerBinding.binding = 0;
    samplerBinding.descriptorCount = 1;
    samplerBinding.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
    samplerBinding.pImmutableSamplers = nullptr;
    samplerBinding.stageFlags = VK_SHADER_STAGE_FRAGMENT_BIT | VK_SHADER_STAGE_COMPUTE_BIT;
    bindings.push_back(samplerBinding);

    VkDescriptorSetLayoutBinding bufferBinding{};
    bufferBinding.binding = 1;
    bufferBinding.descriptorCount = 1;
    bufferBinding.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bufferBinding.pImmutableSamplers = nullptr;
    bufferBinding.stageFlags = VK_SHADER_STAGE_COMPUTE_BIT | VK_SHADER_STAGE_VERTEX_BIT |
                              VK_SHADER_STAGE_FRAGMENT_BIT | VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT |
                              VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT;
    bindings.push_back(bufferBinding);

    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = static_cast<uint32_t>(bindings.size());
    layoutInfo.pBindings = bindings.data();

    VkDescriptorSetLayout descriptorSetLayout;
    if (vkCreateDescriptorSetLayout(m_vulkan->device, &layoutInfo, nullptr,
                                    &descriptorSetLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create descriptor set layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan descriptor set layout creation failed");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateDescriptorSetLayout: Created descriptor set layout, latency={}us",
                 m_stats.totalLatencyUs.load());
    return descriptorSetLayout;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateDescriptorSetLayout failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

VkDescriptorSet PS4GPU::AllocateDescriptorSet(VkDescriptorSetLayout layout) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkDescriptorSetAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
    allocInfo.descriptorPool = m_vulkan->descriptorPool;
    allocInfo.descriptorSetCount = 1;
    allocInfo.pSetLayouts = &layout;

    VkDescriptorSet descriptorSet;
    if (vkAllocateDescriptorSets(m_vulkan->device, &allocInfo, &descriptorSet) != VK_SUCCESS) {
      spdlog::error("Failed to allocate descriptor set");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan descriptor set allocation failed");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("AllocateDescriptorSet: Allocated descriptor set, latency={}us",
                  m_stats.totalLatencyUs.load());
    return descriptorSet;
  } catch (const std::exception &e) {
    spdlog::error("AllocateDescriptorSet failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

void PS4GPU::UpdateDescriptorSet() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    if (m_currentDescriptorSets.empty()) {
      spdlog::warn("No descriptor sets available for update");
      m_stats.cacheMisses++;
      return;
    }

    VkDescriptorSet descriptorSet = m_currentDescriptorSets[0];
    std::vector<VkWriteDescriptorSet> writes;
    std::vector<VkDescriptorImageInfo> imageInfos;
    std::vector<VkDescriptorBufferInfo> bufferInfos;

    uint32_t textureBinding = 0;
    for (const auto &[textureId, textureView] : m_textureViews) {
      if (textureView != VK_NULL_HANDLE && textureBinding < 16) {
        VkDescriptorImageInfo imageInfo{};
        auto samplerIt = m_samplers.find(textureId);
        imageInfo.sampler = (samplerIt != m_samplers.end()) ? samplerIt->second : VK_NULL_HANDLE;
        imageInfo.imageView = textureView;
        imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
        imageInfos.push_back(imageInfo);

        VkWriteDescriptorSet write{};
        write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        write.dstSet = descriptorSet;
        write.dstBinding = textureBinding;
        write.dstArrayElement = 0;
        write.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
        write.descriptorCount = 1;
        write.pImageInfo = &imageInfos.back();
        writes.push_back(write);
        textureBinding++;
      }
    }

    uint32_t bufferBinding = 16;
    for (const auto &[bufferAddress, mapping] : m_memoryMappings) {
      if (mapping.gpuAddress != 0 && bufferBinding < 32) {
        VkDescriptorBufferInfo bufferInfo{};
        bufferInfo.buffer = mapping.buffer;
        bufferInfo.offset = 0;
        bufferInfo.range = mapping.size;
        bufferInfos.push_back(bufferInfo);

        VkWriteDescriptorSet write{};
        write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        write.dstSet = descriptorSet;
        write.dstBinding = bufferBinding;
        write.dstArrayElement = 0;
        write.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        write.descriptorCount = 1;
        write.pBufferInfo = &bufferInfos.back();
        writes.push_back(write);
        bufferBinding++;
      }
    }

    if (!writes.empty()) {
      vkUpdateDescriptorSets(m_vulkan->device, static_cast<uint32_t>(writes.size()),
                             writes.data(), 0, nullptr);
      spdlog::debug("Updated descriptor set with {} bindings", writes.size());
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("UpdateDescriptorSet: Updated descriptor set with {} writes, latency={}us",
                  writes.size(), m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UpdateDescriptorSet failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint32_t PS4GPU::FindMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkPhysicalDeviceMemoryProperties memProperties;
    vkGetPhysicalDeviceMemoryProperties(m_vulkan->physicalDevice, &memProperties);
    for (uint32_t i = 0; i < memProperties.memoryTypeCount; ++i) {
      if ((typeFilter & (1 << i)) &&
          (memProperties.memoryTypes[i].propertyFlags & properties) == properties) {
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        spdlog::trace("FindMemoryType: Found memory type {}, latency={}us",
                      i, m_stats.totalLatencyUs.load());
        return i;
      }
    }
    spdlog::error("Failed to find suitable memory type");
    m_stats.cacheMisses++;
    throw GPUException("Suitable memory type not found");
  } catch (const std::exception &e) {
    spdlog::error("FindMemoryType failed: {}", e.what());
    m_stats.cacheMisses++;
    throw;
  }
}

VkBuffer PS4GPU::CreateBuffer(uint64_t size, VkBufferUsageFlags usage,
                              VkMemoryPropertyFlags properties, VkDeviceMemory &memory) {
  auto start = std::chrono::steady_clock::now();
  VkBuffer buffer = VK_NULL_HANDLE;

  {
    COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
    try {
      VkBufferCreateInfo bufferInfo{};
      bufferInfo.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
      bufferInfo.size = size;
      bufferInfo.usage = usage;
      bufferInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

      if (vkCreateBuffer(m_vulkan->device, &bufferInfo, nullptr, &buffer) != VK_SUCCESS) {
        spdlog::error("Failed to create buffer");
        m_stats.cacheMisses++;
        throw GPUException("Vulkan buffer creation failed");
      }

      VkMemoryRequirements memRequirements;
      vkGetBufferMemoryRequirements(m_vulkan->device, buffer, &memRequirements);

      VkMemoryAllocateInfo allocInfo{};
      allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
      allocInfo.allocationSize = memRequirements.size;
      allocInfo.memoryTypeIndex = FindMemoryType(memRequirements.memoryTypeBits, properties);

      if (vkAllocateMemory(m_vulkan->device, &allocInfo, nullptr, &memory) != VK_SUCCESS) {
        spdlog::error("Failed to allocate buffer memory");
        vkDestroyBuffer(m_vulkan->device, buffer, nullptr);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan memory allocation failed");
      }

      vkBindBufferMemory(m_vulkan->device, buffer, memory, 0);
      m_stats.vramUsage += memRequirements.size;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("CreateBuffer: Created buffer, size={}, vramUsage={} bytes, latency={}us",
                    size, m_stats.vramUsage.load(), m_stats.totalLatencyUs.load());
    } catch (const std::exception &e) {
      spdlog::error("CreateBuffer failed: {}", e.what());
      m_stats.cacheMisses++;
      throw;
    }
  } // Release GPU lock before memory diagnostics update

  // Update memory diagnostics AFTER releasing GPU lock to prevent deadlock
  try {
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::warn("Failed to update memory diagnostics in CreateBuffer: {}", e.what());
  }

  return buffer;
}

void PS4GPU::BeginCommandBuffer() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    vkResetCommandBuffer(m_commandBuffer, 0);
    VkCommandBufferBeginInfo beginInfo{};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    if (vkBeginCommandBuffer(m_commandBuffer, &beginInfo) != VK_SUCCESS) {
      spdlog::error("Failed to begin command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer begin failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BeginCommandBuffer: Begun command buffer, latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BeginCommandBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::SubmitCommandBuffer() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    VkSemaphore waitSemaphores[] = {m_vulkan->imageAvailableSemaphore};
    VkPipelineStageFlags waitStages[] = {VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT};
    submitInfo.waitSemaphoreCount = 1;
    submitInfo.pWaitSemaphores = waitSemaphores;
    submitInfo.pWaitDstStageMask = waitStages;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_commandBuffer;
    VkSemaphore signalSemaphores[] = {m_vulkan->renderFinishedSemaphore};
    submitInfo.signalSemaphoreCount = 1;
    submitInfo.pSignalSemaphores = signalSemaphores;

    if (vkQueueSubmit(m_vulkan->graphicsQueue, 1, &submitInfo,
                      m_vulkan->inFlightFence) != VK_SUCCESS) {
      spdlog::error("Failed to submit command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer submission failed");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SubmitCommandBuffer: Submitted command buffer, latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SubmitCommandBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::TransitionImageLayout(VkImage image, VkFormat format,
                                   VkImageLayout oldLayout, VkImageLayout newLayout) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkImageMemoryBarrier barrier{};
    barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
    barrier.oldLayout = oldLayout;
    barrier.newLayout = newLayout;
    barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.image = image;

    if (format == VK_FORMAT_D32_SFLOAT || format == VK_FORMAT_D32_SFLOAT_S8_UINT ||
        format == VK_FORMAT_D16_UNORM || format == VK_FORMAT_D24_UNORM_S8_UINT) {
      barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT;
      if (format == VK_FORMAT_D32_SFLOAT_S8_UINT || format == VK_FORMAT_D24_UNORM_S8_UINT) {
        barrier.subresourceRange.aspectMask |= VK_IMAGE_ASPECT_STENCIL_BIT;
      }
    } else {
      barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    }
    barrier.subresourceRange.baseMipLevel = 0;
    barrier.subresourceRange.levelCount = 1;
    barrier.subresourceRange.baseArrayLayer = 0;
    barrier.subresourceRange.layerCount = 1;

    VkPipelineStageFlags sourceStage;
    VkPipelineStageFlags destinationStage;

    if (oldLayout == VK_IMAGE_LAYOUT_UNDEFINED &&
        newLayout == VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL) {
      barrier.srcAccessMask = 0;
      barrier.dstAccessMask = VK_ACCESS_TRANSFER_WRITE_BIT;
      sourceStage = VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT;
      destinationStage = VK_PIPELINE_STAGE_TRANSFER_BIT;
    } else if (oldLayout == VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL &&
               newLayout == VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL) {
      barrier.srcAccessMask = VK_ACCESS_TRANSFER_WRITE_BIT;
      barrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
      sourceStage = VK_PIPELINE_STAGE_TRANSFER_BIT;
      destinationStage = VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT | VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT;
    } else if (oldLayout == VK_IMAGE_LAYOUT_UNDEFINED &&
               newLayout == VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL) {
      barrier.srcAccessMask = 0;
      barrier.dstAccessMask = VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT |
                              VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;
      sourceStage = VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT;
      destinationStage = VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    } else {
      spdlog::error("Unsupported layout transition: {} to {}", static_cast<uint32_t>(oldLayout), static_cast<uint32_t>(newLayout));
      m_stats.cacheMisses++;
      throw GPUException("Unsupported image layout transition");
    }

    vkCmdPipelineBarrier(m_commandBuffer, sourceStage, destinationStage, 0, 0,
                         nullptr, 0, nullptr, 1, &barrier);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("TransitionImageLayout: oldLayout={}, newLayout={}, latency={}us",
                  static_cast<uint32_t>(oldLayout), static_cast<uint32_t>(newLayout),
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("TransitionImageLayout failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

void PS4GPU::CopyBufferToImage(VkBuffer buffer, VkImage image, uint32_t width,
                               uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    VkBufferImageCopy region{};
    region.bufferOffset = 0;
    region.bufferRowLength = 0;
    region.bufferImageHeight = 0;
    region.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    region.imageSubresource.mipLevel = 0;
    region.imageSubresource.baseArrayLayer = 0;
    region.imageSubresource.layerCount = 1;
    region.imageOffset = {0, 0, 0};
    region.imageExtent = {width, height, 1};

    vkCmdCopyBufferToImage(m_commandBuffer, buffer, image,
                           VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL, 1, &region);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CopyBufferToImage: width={}, height={}, latency={}us",
                  width, height, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("CopyBufferToImage failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

uint64_t PS4GPU::TranslateGCNShader(const void *gcnCode, size_t size,
                                    VkShaderStageFlagBits stage) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_gpuMutex, "GPUMutex");
  try {
    const uint32_t *codePtr = reinterpret_cast<const uint32_t *>(gcnCode);
    size_t instrCount = size / sizeof(uint32_t);
    std::vector<uint32_t> bytecode(codePtr, codePtr + instrCount);

    GCNShaderType shaderType;
    switch (stage) {
    case VK_SHADER_STAGE_VERTEX_BIT:
      shaderType = GCNShaderType::VERTEX;
      break;
    case VK_SHADER_STAGE_FRAGMENT_BIT:
      shaderType = GCNShaderType::PIXEL;
      break;
    case VK_SHADER_STAGE_GEOMETRY_BIT:
      shaderType = GCNShaderType::GEOMETRY;
      break;
    case VK_SHADER_STAGE_COMPUTE_BIT:
      shaderType = GCNShaderType::COMPUTE;
      break;
    case VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT:
      shaderType = GCNShaderType::HULL;
      break;
    case VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT:
      shaderType = GCNShaderType::DOMAIN_SHADER;
      break;
    default:
      spdlog::error("Unsupported shader stage: {}", static_cast<uint32_t>(stage));
      m_stats.cacheMisses++;
      throw GPUException("Unsupported shader stage");
    }

    auto spirvCode = m_shaderTranslator->TranslateToSPIRV(bytecode, shaderType);

    VkShaderModuleCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
    createInfo.pCode = spirvCode.data();

    ShaderModule shader;
    shader.type = shaderType;
    shader.spirvCode = spirvCode;
    if (vkCreateShaderModule(m_vulkan->device, &createInfo, nullptr, &shader.module) != VK_SUCCESS) {
      spdlog::error("Failed to create shader module");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan shader module creation failed");
    }

    uint64_t shaderId = reinterpret_cast<uint64_t>(shader.module);
    m_shaderModules[shaderId] = shader;
    m_stats.shaderCompilations++;
    m_stats.shaderCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("TranslateGCNShader: Translated shader, id={:x}, size={}, stage={}, latency={}us",
                 shaderId, size, static_cast<uint32_t>(stage), m_stats.totalLatencyUs.load());
    return shaderId;
  } catch (const std::exception &e) {
    spdlog::error("TranslateGCNShader failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}
} // namespace ps4
