RSID_TTAXI_START, 2500,,,
 ,[Offset], Taxi_FLYER, 0,
 ,[Offset], Taxi\PBTTaxi, 1,
 ,[Offset], Taxi\InstructionsENG, 2,
 ,[Offset], Taxi\InstructionsFR, 3,
 ,[Offset], Taxi\InstructionsITAL, 4,
 ,[Offset], Taxi\InstructionsGERM, 5,
 ,[Offset], Taxi\InstructionsSPAN, 6,
 ,[Offset], Taxi\InstructionsPORT, 7,
 ,[Offset], Taxi\InstructionsDUTCH, 8,
 ,[Offset], tables\Taxi_BG_scroll, 9,
RSID_TTAXI_LIGHTS, 2501,,,
RSID_TTAXI_CAMERAS, 2502,,,
RSID_TTAXI_LAMP_TEXTURES, 2503,,,
 ,[Offset], L_09_Off, 0,
 ,[Offset], L_09_On, 1,
 ,[Offset], L_10_Off, 2,
 ,[Offset], L_10_On, 3,
 ,[Offset], L_11_Off, 4,
 ,[Offset], L_11_On, 5,
 ,[Offset], L_12_Off, 6,
 ,[Offset], L_12_On, 7,
 ,[Offset], L_13_Off, 8,
 ,[Offset], L_13_On, 9,
 ,[Offset], L_14_Off, 10,
 ,[Offset], L_14_On, 11,
 ,[Offset], L_15_Off, 12,
 ,[Offset], L_15_On, 13,
 ,[Offset], L_16_Off, 14,
 ,[Offset], L_16_On, 15,
 ,[Offset], L_17_Off, 16,
 ,[Offset], L_17_On, 17,
 ,[Offset], L_18_Off, 18,
 ,[Offset], L_18_On, 19,
 ,[Offset], L_19_Off, 20,
 ,[Offset], L_19_On, 21,
 ,[Offset], L_20_Off, 22,
 ,[Offset], L_20_On, 23,
 ,[Offset], L_21_Off, 24,
 ,[Offset], L_21_On, 25,
 ,[Offset], L_22_Off, 26,
 ,[Offset], L_22_On, 27,
 ,[Offset], L_23_Off, 28,
 ,[Offset], L_23_On, 29,
 ,[Offset], L_24_Off, 30,
 ,[Offset], L_24_On, 31,
 ,[Offset], L_30_Off, 32,
 ,[Offset], L_30_On, 33,
 ,[Offset], L_31_Off, 34,
 ,[Offset], L_31_On, 35,
 ,[Offset], L_32_Off, 36,
 ,[Offset], L_32_On, 37,
 ,[Offset], L_33_Off, 38,
 ,[Offset], L_33_On, 39,
 ,[Offset], L_34_Off, 40,
 ,[Offset], L_34_On, 41,
 ,[Offset], L_35_Off, 42,
 ,[Offset], L_35_On, 43,
 ,[Offset], L_36_Off, 44,
 ,[Offset], L_36_On, 45,
 ,[Offset], L_37_Off, 46,
 ,[Offset], L_37_On, 47,
 ,[Offset], L_38_Off, 48,
 ,[Offset], L_38_On, 49,
 ,[Offset], L_39_Off, 50,
 ,[Offset], L_39_On, 51,
 ,[Offset], L_40_Off, 52,
 ,[Offset], L_40_On, 53,
 ,[Offset], L_41_Off, 54,
 ,[Offset], L_41_On, 55,
 ,[Offset], L_42_Off, 56,
 ,[Offset], L_42_On, 57,
 ,[Offset], L_43_Off, 58,
 ,[Offset], L_43_On, 59,
 ,[Offset], L_44_Off, 60,
 ,[Offset], L_44_On, 61,
 ,[Offset], L_45_Off, 62,
 ,[Offset], L_45_On, 63,
 ,[Offset], L_46_Off, 64,
 ,[Offset], L_46_On, 65,
 ,[Offset], L_47_Off, 66,
 ,[Offset], L_47_On, 67,
 ,[Offset], L_48_Off, 68,
 ,[Offset], L_48_On, 69,
 ,[Offset], L_57_Off, 70,
 ,[Offset], L_57_On, 71,
 ,[Offset], L_58_Off, 72,
 ,[Offset], L_58_On, 73,
 ,[Offset], L_59_Off, 74,
 ,[Offset], L_59_On, 75,
 ,[Offset], L_60_Off, 76,
 ,[Offset], L_60_On, 77,
 ,[Offset], L_61_Off, 78,
 ,[Offset], L_61_On, 79,
 ,[Offset], L_62_Off, 80,
 ,[Offset], L_62_On, 81,
 ,[Offset], L_63_Off, 82,
 ,[Offset], L_63_On, 83,
 ,[Offset], F_01C_L_On, 84,
 ,[Offset], F_01C_Off, 85,
 ,[Offset], F_01C_On, 86,
 ,[Offset], F_02C_L_On, 87,
 ,[Offset], F_02C_Off, 88,
 ,[Offset], F_02C_On, 89,
 ,[Offset], F_03C_L_On, 90,
 ,[Offset], F_03C_Off, 91,
 ,[Offset], F_03C_On, 92,
 ,[Offset], F_04C_L_On, 93,
 ,[Offset], F_04C_Off, 94,
 ,[Offset], F_04C_On, 95,
 ,[Offset], F_07C_L_On, 96,
 ,[Offset], F_07C_Off, 97,
 ,[Offset], F_07C_On, 98,
 ,[Offset], F_17_Off, 99,
 ,[Offset], F_17_On, 100,
 ,[Offset], F_19_Off, 101,
 ,[Offset], F_19_On, 102,
 ,[Offset], F_21_Off, 103,
 ,[Offset], F_21_On, 104,
 ,[Offset], Taxi_LED_Border, 105,
 ,[Offset], L_01_Off, 106,
 ,[Offset], L_01_On, 107,
 ,[Offset], L_02_Off, 108,
 ,[Offset], L_02_On, 109,
 ,[Offset], L_03_Off, 110,
 ,[Offset], L_03_On, 111,
 ,[Offset], L_04_Off, 112,
 ,[Offset], L_04_On, 113,
 ,[Offset], L_05_Off, 114,
 ,[Offset], L_05_On, 115,
RSID_TTAXI_TEXTURES, 2504,,,
 ,[Offset], T_A1_0, 0,
 ,[Offset], T_A2_0, 1,
 ,[Offset], T_B1_0, 2,
 ,[Offset], T_B2_0, 3,
 ,[Offset], T_A3_0, 4,
 ,[Offset], T_A4_0, 5,
 ,[Offset], T_B3_0, 6,
 ,[Offset], T_B4_0, 7,
 ,[Offset], T_A5_0, 8,
 ,[Offset], T_A6_0, 9,
 ,[Offset], T_B5_0, 10,
 ,[Offset], T_B6_0, 11,
 ,[Offset], T_A7_0, 12,
 ,[Offset], T_A8_0, 13,
 ,[Offset], T_B7_0, 14,
 ,[Offset], T_B8_0, 15,
 ,[Offset], T_C1_0, 16,
 ,[Offset], T_C2_0, 17,
 ,[Offset], T_D1_0, 18,
 ,[Offset], T_D2_0, 19,
 ,[Offset], T_C3_0, 20,
 ,[Offset], T_C4_0, 21,
 ,[Offset], T_D3_0, 22,
 ,[Offset], T_D4_0, 23,
 ,[Offset], T_C5_0, 24,
 ,[Offset], T_C6_0, 25,
 ,[Offset], T_D5_0, 26,
 ,[Offset], T_D6_0, 27,
 ,[Offset], T_C7_0, 28,
 ,[Offset], T_C8_0, 29,
 ,[Offset], T_D7_0, 30,
 ,[Offset], T_D8_0, 31,
 ,[Offset], T_E1_0, 32,
 ,[Offset], T_E2_0, 33,
 ,[Offset], T_F1_0, 34,
 ,[Offset], T_F2_0, 35,
 ,[Offset], T_E3_0, 36,
 ,[Offset], T_E4_0, 37,
 ,[Offset], T_F3_0, 38,
 ,[Offset], T_F4_0, 39,
 ,[Offset], T_E5_0, 40,
 ,[Offset], T_E6_0, 41,
 ,[Offset], T_F5_0, 42,
 ,[Offset], T_F6_0, 43,
 ,[Offset], T_E7_0, 44,
 ,[Offset], T_E8_0, 45,
 ,[Offset], T_F7_0, 46,
 ,[Offset], T_F8_0, 47,
 ,[Offset], T_G1_0, 48,
 ,[Offset], T_G2_0, 49,
 ,[Offset], T_H1_0, 50,
 ,[Offset], T_H2_0, 51,
 ,[Offset], T_G3_0, 52,
 ,[Offset], T_G4_0, 53,
 ,[Offset], T_H3_0, 54,
 ,[Offset], T_H4_0, 55,
 ,[Offset], T_G5_0, 56,
 ,[Offset], T_G6_0, 57,
 ,[Offset], T_H5_0, 58,
 ,[Offset], T_H6_0, 59,
 ,[Offset], T_G7_0, 60,
 ,[Offset], T_G8_0, 61,
 ,[Offset], T_H7_0, 62,
 ,[Offset], T_H8_0, 63,
 ,[Offset], TT_A1_0, 64,
 ,[Offset], backGlass, 65,
 ,[Offset], post blue, 66,
 ,[Offset], Color Wheel, 67,
 ,[Offset], lamp55_bumper_Off, 68,
 ,[Offset], metal front, 69,
 ,[Offset], plunger, 70,
 ,[Offset], Rules_L1_0, 71,
 ,[Offset], Rules_L2_0, 72,
 ,[Offset], Rules_L3_0, 73,
 ,[Offset], Rules_L4_0, 74,
 ,[Offset], Rules_R1_0, 75,
 ,[Offset], Rules_R2_0, 76,
 ,[Offset], Rules_R3_0, 77,
 ,[Offset], Rules_R4_0, 78,
 ,[Offset], Taxi_TableFront_Side, 79,
 ,[Offset], top_sign, 80,
 ,[Offset], wood_strip, 81,
 ,[Offset], screw alt, 82,
 ,[Offset], rubber, 83,
 ,[Offset], rubber color, 84,
 ,[Offset], metal-parts01 copy, 85,
 ,[Offset], metal_parts01, 86,
 ,[Offset], metal_parts02, 87,
 ,[Offset], metal_parts03, 88,
 ,[Offset], metal_parts04, 89,
 ,[Offset], metal_legs, 90,
 ,[Offset], metal_trim, 91,
 ,[Offset], screw, 92,
 ,[Offset], Taxi_Table, 93,
 ,[Offset], Buttons_Parts, 94,
 ,[Offset], tile, 95,
 ,[Offset], tmp_gray, 96,
 ,[Offset], tmp_orange, 97,
 ,[Offset], flipper, 98,
 ,[Offset], post, 99,
 ,[Offset], target, 100,
 ,[Offset], glass, 101,
 ,[Offset], Taxi_Bumper_Spinout, 102,
 ,[Offset], CoinSlots, 103,
 ,[Offset], Bumper_A, 104,
 ,[Offset], Bumper_B, 105,
 ,[Offset], Bumper_C, 106,
 ,[Offset], Bumper_D, 107,
 ,[Offset], traffic, 108,
 ,[Offset], xpress, 109,
 ,[Offset], Taxi_1, 110,
 ,[Offset], ramps, 111,
 ,[Offset], WoodEdge_Tile, 112,
 ,[Offset], plunger_plate_baked, 113,
 ,[Offset], plunger_metal, 114,
 ,[Offset], GreyMetal, 115,
 ,[Offset], beige, 116,
 ,[Offset], Taxi_Bumper_Spinout_Taxi, 117,
 ,[Offset], Taxi_Spinout_Text, 118,
 ,[Offset], Taxi_2, 119,
 ,[Offset], lightbulb, 120,
 ,[Offset], jelly, 121,
 ,[Offset], post red, 122,
 ,[Offset], jelly red, 123,
 ,[Offset], plastic_clear01, 124,
 ,[Offset], plastic_clear02, 125,
 ,[Offset], rampB, 126,
 ,[Offset], ramp1, 127,
 ,[Offset], taxi_bumper, 128,
 ,[Offset], ramp2, 129,
 ,[Offset], red_ramp, 130,
 ,[Offset], Metal_screws, 131,
 ,[Offset], Rails, 132,
 ,[Offset], taxi_apron, 133,
 ,[Offset], tileb, 134,
RSID_TTAXI_MODELS, 2505,,,
 ,[Offset], floor, 0,
 ,[Offset], vertical_plastics, 1,
 ,[Offset], horizontal_plastics, 2,
 ,[Offset], objramp, 3,
 ,[Offset], back_glass, 4,
 ,[Offset], glass, 5,
 ,[Offset], metal, 6,
 ,[Offset], cabinet, 7,
 ,[Offset], lights, 8,
 ,[Offset], platform 1, 9,
 ,[Offset], flipper A, 10,
 ,[Offset], flipper A bottom, 11,
 ,[Offset], flipper B, 12,
 ,[Offset], flipper B bottom, 13,
 ,[Offset], plunger, 14,
 ,[Offset], tile, 15,
 ,[Offset], target A, 16,
 ,[Offset], gate oneway, 17,
 ,[Offset], wire A, 18,
 ,[Offset], gate, 19,
 ,[Offset], slingshot A, 20,
 ,[Offset], slingshot A ext, 21,
 ,[Offset], slingshot B, 22,
 ,[Offset], slingshot B ext, 23,
 ,[Offset], pop bumper A, 24,
 ,[Offset], trap A, 25,
 ,[Offset], trap B, 26,
 ,[Offset], trap C, 27,
 ,[Offset], trap D, 28,
 ,[Offset], generic A, 29,
 ,[Offset], generic B, 30,
 ,[Offset], generic E, 31,
 ,[Offset], metal_parts, 32,
 ,[Offset], cabinet_metals, 33,
 ,[Offset], habi_trails, 34,
 ,[Offset], apron, 35,
 ,[Offset], light_bulbs, 36,
 ,[Offset], plastic_posts, 37,
 ,[Offset], pop_bumpers, 38,
 ,[Offset], ramp2, 39,
 ,[Offset], red_ramp, 40,
 ,[Offset], rubbers, 41,
 ,[Offset], screws, 42,
 ,[Offset], wooden_rails, 43,
 ,[Offset], Bumper_Metal, 44,
 ,[Offset], Catapult, 45,
 ,[Offset], Flipper_Left, 46,
 ,[Offset], Flipper_Right, 47,
 ,[Offset], One_Way_Gate, 48,
 ,[Offset], Round_Target, 49,
 ,[Offset], One_Way_Gate_B, 50,
 ,[Offset], One_Way_Gate_C, 51,
 ,[Offset], SpinOut_Lights, 52,
 ,[Offset], target B, 53,
RSID_TTAXI_MODELS_LODS, 2506,,,
 ,[Offset], floor, 0,
 ,[Offset], vertical_plastics, 1,
 ,[Offset], horizontal_plastics, 2,
 ,[Offset], objramp, 3,
 ,[Offset], back_glass, 4,
 ,[Offset], glass, 5,
 ,[Offset], metal, 6,
 ,[Offset], cabinet, 7,
 ,[Offset], lights, 8,
 ,[Offset], platform 1, 9,
 ,[Offset], flipper A, 10,
 ,[Offset], flipper A bottom, 11,
 ,[Offset], flipper B, 12,
 ,[Offset], flipper B bottom, 13,
 ,[Offset], plunger, 14,
 ,[Offset], tile, 15,
 ,[Offset], target A, 16,
 ,[Offset], gate oneway, 17,
 ,[Offset], wire A, 18,
 ,[Offset], gate, 19,
 ,[Offset], slingshot A, 20,
 ,[Offset], slingshot A ext, 21,
 ,[Offset], slingshot B, 22,
 ,[Offset], slingshot B ext, 23,
 ,[Offset], pop bumper A, 24,
 ,[Offset], trap A, 25,
 ,[Offset], trap B, 26,
 ,[Offset], trap C, 27,
 ,[Offset], trap D, 28,
 ,[Offset], generic A, 29,
 ,[Offset], generic B, 30,
 ,[Offset], generic E, 31,
 ,[Offset], metal_parts, 32,
 ,[Offset], cabinet_metals, 33,
 ,[Offset], habi_trails, 34,
 ,[Offset], apron, 35,
 ,[Offset], light_bulbs, 36,
 ,[Offset], plastic_posts, 37,
 ,[Offset], pop_bumpers, 38,
 ,[Offset], ramp2, 39,
 ,[Offset], red_ramp, 40,
 ,[Offset], rubbers, 41,
 ,[Offset], screws, 42,
 ,[Offset], wooden_rails, 43,
 ,[Offset], Bumper_Metal, 44,
 ,[Offset], Catapult, 45,
 ,[Offset], Flipper_Left, 46,
 ,[Offset], Flipper_Right, 47,
 ,[Offset], One_Way_Gate, 48,
 ,[Offset], Round_Target, 49,
 ,[Offset], One_Way_Gate_B, 50,
 ,[Offset], One_Way_Gate_C, 51,
 ,[Offset], SpinOut_Lights, 52,
 ,[Offset], target B, 53,
RSID_TTAXI_COLLISIONS, 2507,,,
 ,[Offset], target B, 0,
 ,[Offset], target B, 1,
 ,[Offset], target B, 2,
 ,[Offset], target B, 3,
 ,[Offset], target B, 4,
 ,[Offset], target B, 5,
 ,[Offset], target B, 6,
 ,[Offset], target B, 7,
 ,[Offset], target B, 8,
 ,[Offset], target B, 9,
 ,[Offset], target B, 10,
 ,[Offset], target B, 11,
 ,[Offset], target B, 12,
 ,[Offset], target B, 13,
 ,[Offset], target B, 14,
 ,[Offset], target B, 15,
 ,[Offset], target B, 16,
 ,[Offset], target B, 17,
 ,[Offset], target B, 18,
 ,[Offset], target B, 19,
 ,[Offset], target B, 20,
 ,[Offset], target B, 21,
 ,[Offset], target B, 22,
 ,[Offset], target B, 23,
 ,[Offset], target B, 24,
 ,[Offset], target B, 25,
 ,[Offset], target B, 26,
 ,[Offset], target B, 27,
 ,[Offset], target B, 28,
 ,[Offset], target B, 29,
 ,[Offset], target B, 30,
 ,[Offset], target B, 31,
 ,[Offset], target B, 32,
 ,[Offset], target B, 33,
 ,[Offset], target B, 34,
 ,[Offset], target B, 35,
 ,[Offset], target B, 36,
 ,[Offset], target B, 37,
 ,[Offset], target B, 38,
 ,[Offset], target B, 39,
 ,[Offset], target B, 40,
 ,[Offset], target B, 41,
 ,[Offset], target B, 42,
 ,[Offset], target B, 43,
 ,[Offset], target B, 44,
 ,[Offset], Flipper_Right_Front, 45,
 ,[Offset], Flipper_Right_Back, 46,
 ,[Offset], plunger col, 47,
 ,[Offset], plunger area col, 48,
 ,[Offset], gate col, 49,
 ,[Offset], gate oneway col, 50,
 ,[Offset], slingshot a col, 51,
 ,[Offset], slingshot b col, 52,
 ,[Offset], trap a col, 53,
 ,[Offset], trap b, 54,
 ,[Offset], pop bumper a col, 55,
 ,[Offset], tile col, 56,
 ,[Offset], target a col, 57,
 ,[Offset], trap D col, 58,
 ,[Offset], bumper b col, 59,
 ,[Offset], wire a, 60,
 ,[Offset], trap c, 61,
 ,[Offset], generic A col, 62,
 ,[Offset], generic B col, 63,
 ,[Offset], generic D col, 64,
 ,[Offset], generic E col, 65,
 ,[Offset], gate oneway2 col, 66,
 ,[Offset], gate oneway3 col, 67,
 ,[Offset], generic H col, 68,
 ,[Offset], generic I col, 69,
 ,[Offset], Flipper_Left_Front, 70,
 ,[Offset], Flipper_Left_Back, 71,
 ,[Offset], backstop_COL, 72,
 ,[Offset], OneWay_Gate_Back, 73,
 ,[Offset], OneWay_Gate_Front, 74,
 ,[Offset], OneWay_Gate_2_Back, 75,
 ,[Offset], OneWay_Gate_2_Front, 76,
 ,[Offset], OneWay_Gate_3_Back, 77,
 ,[Offset], Bowl, 78,
 ,[Offset], Bowl_Wall, 79,
 ,[Offset], Floor, 80,
 ,[Offset], Habit, 81,
 ,[Offset], Left_Flipper_Lane, 82,
 ,[Offset], Left_Ramp, 83,
 ,[Offset], Left_Slingshot, 84,
 ,[Offset], Right_Flipper_Lane, 85,
 ,[Offset], Right_Ramp, 86,
 ,[Offset], Right_Slingshot, 87,
 ,[Offset], Rubber, 88,
 ,[Offset], Wall_A, 89,
 ,[Offset], Wall_B, 90,
 ,[Offset], Wall_C, 91,
 ,[Offset], Wall_D, 92,
 ,[Offset], Plunger_Ramp, 93,
 ,[Offset], Bowl_Bottom, 94,
 ,[Offset], Plunger_Rest, 95,
RSID_TTAXI_PLACEMENT, 2508,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TTAXI_EMUROM, 2509,,,
 ,[Offset], taxi_l4_u26, 0,
 ,[Offset], taxi_l4_u27, 1,
 ,[Offset], taxi_l4, 2,
 ,[Offset], taxi_l4, 3,
RSID_TTAXI_SOUNDS_START, 2510,,,
RSID_TTAXI_EMU_SOUNDS, 2511,,,
 ,[Offset], S0001-LP1, 0,
 ,[Offset], S0001-LP2, 1,
 ,[Offset], S0002-LP1, 2,
 ,[Offset], S0002-LP2, 3,
 ,[Offset], S0003-LP1, 4,
 ,[Offset], S0004-LP1, 5,
 ,[Offset], S0005-LP1, 6,
 ,[Offset], S0007-LP1, 7,
 ,[Offset], S0007-LP2, 8,
 ,[Offset], S0008-LP1, 9,
 ,[Offset], S0009-LP1, 10,
 ,[Offset], S000A-LP1, 11,
 ,[Offset], S000B-LP1, 12,
 ,[Offset], S000C-LP1, 13,
 ,[Offset], S000C-LP2, 14,
 ,[Offset], S000D-LP1, 15,
 ,[Offset], S000E-LP1, 16,
 ,[Offset], S000F-LP1, 17,
 ,[Offset], S0010-LP1, 18,
 ,[Offset], S0011-LP1, 19,
 ,[Offset], S0011-LP2, 20,
 ,[Offset], S0012-LP1, 21,
 ,[Offset], S0013-LP1, 22,
 ,[Offset], S0014-LP1, 23,
 ,[Offset], S0015-LP1, 24,
 ,[Offset], S0016-LP1, 25,
 ,[Offset], S0017-LP1, 26,
 ,[Offset], S0017-LP2, 27,
 ,[Offset], S0018-LP1, 28,
 ,[Offset], S0019-LP1, 29,
 ,[Offset], S001A-LP1, 30,
 ,[Offset], S001A-LP2, 31,
 ,[Offset], S001B-LP1, 32,
 ,[Offset], S001B-LP2, 33,
 ,[Offset], S001C-LP1, 34,
 ,[Offset], S001C-LP2, 35,
 ,[Offset], S001D_C3, 36,
 ,[Offset], S001E-LP1, 37,
 ,[Offset], S001E-LP2, 38,
 ,[Offset], S0030_C3, 39,
 ,[Offset], S0031-LP1, 40,
 ,[Offset], S0031-LP2, 41,
 ,[Offset], S0032_C3, 42,
 ,[Offset], S0080_C4, 43,
 ,[Offset], S0081_C4, 44,
 ,[Offset], S0082_C4, 45,
 ,[Offset], S0083_C4, 46,
 ,[Offset], S0084_C4, 47,
 ,[Offset], S0085_C4, 48,
 ,[Offset], S0086_C4, 49,
 ,[Offset], S0087_C4, 50,
 ,[Offset], S0088_C4, 51,
 ,[Offset], S0089_C4, 52,
 ,[Offset], S008A_C4, 53,
 ,[Offset], S008B_C4, 54,
 ,[Offset], S008C_C4, 55,
 ,[Offset], S008D_C4, 56,
 ,[Offset], S008E_C4, 57,
 ,[Offset], S008F_C4, 58,
 ,[Offset], S0090_C4, 59,
 ,[Offset], S0091_C4, 60,
 ,[Offset], S0092_C4, 61,
 ,[Offset], S0093_C4, 62,
 ,[Offset], S0094_C4, 63,
 ,[Offset], S0095_C4, 64,
 ,[Offset], S0096_C4, 65,
 ,[Offset], S0097_C4, 66,
 ,[Offset], S0098_C4, 67,
 ,[Offset], S0099_C4, 68,
 ,[Offset], S009A_C4, 69,
 ,[Offset], S009B_C4, 70,
 ,[Offset], S009C_C4, 71,
 ,[Offset], S009D_C4, 72,
 ,[Offset], S009E_C4, 73,
 ,[Offset], S009F_C4, 74,
 ,[Offset], S00A0_C4, 75,
 ,[Offset], S00A1_C4, 76,
 ,[Offset], S00A2_C4, 77,
 ,[Offset], S00A3_C4, 78,
 ,[Offset], S00A4_C4, 79,
 ,[Offset], S00A5_C4, 80,
 ,[Offset], S00A6-LP_C4, 81,
 ,[Offset], S00A7_C4, 82,
 ,[Offset], S00A8_C4, 83,
 ,[Offset], S00A9_C4, 84,
 ,[Offset], S00AB_C4, 85,
 ,[Offset], S00AC_C4, 86,
 ,[Offset], S00AD_C4, 87,
 ,[Offset], S00AE_C4, 88,
 ,[Offset], S00AF_C4, 89,
 ,[Offset], S00B0_C4, 90,
 ,[Offset], S00B1_C4, 91,
 ,[Offset], S00B2_C4, 92,
 ,[Offset], S00B3_C4, 93,
 ,[Offset], S00B4_C4, 94,
 ,[Offset], S00B5_C4, 95,
 ,[Offset], S00B6_C4, 96,
 ,[Offset], S00B7_C4, 97,
 ,[Offset], S00B8_C4, 98,
 ,[Offset], S00B9_C4, 99,
 ,[Offset], S00BA_C4, 100,
 ,[Offset], S00BB_C4, 101,
 ,[Offset], S00BC_C4, 102,
 ,[Offset], S00BD_C4, 103,
 ,[Offset], S00BE_C4, 104,
 ,[Offset], S00BF_C4, 105,
 ,[Offset], S00C0_C4, 106,
 ,[Offset], S0101_C2, 107,
 ,[Offset], S0102-LP_C2, 108,
 ,[Offset], S0103-LP_C2, 109,
 ,[Offset], S0115_C2, 110,
 ,[Offset], S0116_C2, 111,
 ,[Offset], S0117_C2, 112,
 ,[Offset], S0118_C2, 113,
 ,[Offset], S0119_C2, 114,
 ,[Offset], S011A_C2, 115,
 ,[Offset], S011B_C2, 116,
 ,[Offset], S011C_C2, 117,
 ,[Offset], S0123_C2, 118,
 ,[Offset], S0124_C2, 119,
 ,[Offset], S0125_C2, 120,
 ,[Offset], S0126_C2, 121,
 ,[Offset], S0127_C2, 122,
 ,[Offset], S0128_C2, 123,
 ,[Offset], S0129_C2, 124,
 ,[Offset], S012A_C2, 125,
 ,[Offset], S012B_C2, 126,
 ,[Offset], S012C_C2, 127,
 ,[Offset], S0130_C2, 128,
 ,[Offset], S0131_C2, 129,
 ,[Offset], S0132_C2, 130,
 ,[Offset], S0133_C2, 131,
 ,[Offset], S0134_C2, 132,
 ,[Offset], S0135_C2, 133,
 ,[Offset], S0138_C2, 134,
 ,[Offset], S0139_C2, 135,
 ,[Offset], S013A_C2, 136,
 ,[Offset], S013B_C2, 137,
 ,[Offset], S013C_C2, 138,
 ,[Offset], S013D_C2, 139,
 ,[Offset], S013E_C2, 140,
 ,[Offset], S013F_C2, 141,
 ,[Offset], S0140_C2, 142,
 ,[Offset], S0141_C2, 143,
 ,[Offset], S0142_C2, 144,
 ,[Offset], S0143_C2, 145,
 ,[Offset], S0144_C2, 146,
 ,[Offset], S0145_C2, 147,
 ,[Offset], S0146_C2, 148,
 ,[Offset], S0147_C2, 149,
 ,[Offset], S0148_C2, 150,
 ,[Offset], S0149_C2, 151,
 ,[Offset], S014A_C2, 152,
 ,[Offset], S014B_C2, 153,
 ,[Offset], S014C_C2, 154,
 ,[Offset], S0150_C2, 155,
 ,[Offset], S0151_C2, 156,
 ,[Offset], S0152_C2, 157,
 ,[Offset], S0153_C2, 158,
 ,[Offset], S0154_C2, 159,
 ,[Offset], S0155_C2, 160,
 ,[Offset], S0156_C2, 161,
 ,[Offset], S0157_C2, 162,
 ,[Offset], S0158_C2, 163,
 ,[Offset], S0159_C2, 164,
 ,[Offset], S015A_C2, 165,
 ,[Offset], S0180_C2, 166,
 ,[Offset], S0181_C2, 167,
 ,[Offset], S0182_C2, 168,
 ,[Offset], S0183_C2, 169,
 ,[Offset], S0184_C2, 170,
 ,[Offset], S0185_C2, 171,
 ,[Offset], S0186_C2, 172,
 ,[Offset], S0187_C2, 173,
 ,[Offset], S0188_C2, 174,
 ,[Offset], S0189_C2, 175,
 ,[Offset], S018A_C2, 176,
 ,[Offset], S018B_C2, 177,
 ,[Offset], S018C_C2, 178,
 ,[Offset], S018D_C2, 179,
 ,[Offset], S018E_C2, 180,
 ,[Offset], S018F_C2, 181,
 ,[Offset], S0190_C2, 182,
 ,[Offset], S0191_C2, 183,
 ,[Offset], S0192_C2, 184,
 ,[Offset], S0194_C2, 185,
 ,[Offset], S0195_C2, 186,
 ,[Offset], S0196_C2, 187,
 ,[Offset], S0199_C2, 188,
 ,[Offset], S019A_C2, 189,
 ,[Offset], S019B_C2, 190,
 ,[Offset], S019C_C2, 191,
 ,[Offset], S019D_C2, 192,
 ,[Offset], S019E_C2, 193,
 ,[Offset], S019F_C2, 194,
 ,[Offset], S01A1_C2, 195,
 ,[Offset], S01A2_C2, 196,
 ,[Offset], S01A3_C2, 197,
 ,[Offset], S01A4_C2, 198,
 ,[Offset], S01A5_C2, 199,
 ,[Offset], S01A6_C2, 200,
 ,[Offset], S01A8_C2, 201,
 ,[Offset], S01AC_C2, 202,
 ,[Offset], S01AD_C2, 203,
 ,[Offset], S01AE_C2, 204,
 ,[Offset], S01AF_C2, 205,
 ,[Offset], S01B0_C2, 206,
 ,[Offset], S01C0_C2, 207,
 ,[Offset], S01C1_C2, 208,
 ,[Offset], S01C2_C2, 209,
 ,[Offset], S01C3_C2, 210,
 ,[Offset], S01C4_C2, 211,
 ,[Offset], S01C5_C2, 212,
 ,[Offset], S01C6_C2, 213,
 ,[Offset], S01C7_C2, 214,
 ,[Offset], mian_menu_music, 215,
RSID_TTAXI_MECH_SOUNDS, 2512,,,
 ,[Offset], bell_sound, 0,
 ,[Offset], drac_catapult, 1,
 ,[Offset], drop_targets_reset, 2,
 ,[Offset], spinner_kickout, 3,
RSID_TTAXI_SOUNDS_END, 2513,,,
RSID_TTAXI_SAMPLES, 2514,,,
RSID_TTAXI_HUD, 2515,,,
RSID_TTAXI_END, 2516,,,
