﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{FCC99BE3-63FB-3429-913C-751B8792AA84}"
	ProjectSection(ProjectDependencies) = postProject
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B} = {A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}
		{3072186B-739D-3873-AE83-19BC1FBAFC53} = {3072186B-739D-3873-AE83-19BC1FBAFC53}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PS4Emulator", "PS4Emulator.vcxproj", "{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}"
	ProjectSection(ProjectDependencies) = postProject
		{3072186B-739D-3873-AE83-19BC1FBAFC53} = {3072186B-739D-3873-AE83-19BC1FBAFC53}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{3072186B-739D-3873-AE83-19BC1FBAFC53}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FCC99BE3-63FB-3429-913C-751B8792AA84}.Debug|x64.ActiveCfg = Debug|x64
		{FCC99BE3-63FB-3429-913C-751B8792AA84}.Release|x64.ActiveCfg = Release|x64
		{FCC99BE3-63FB-3429-913C-751B8792AA84}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FCC99BE3-63FB-3429-913C-751B8792AA84}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.Debug|x64.ActiveCfg = Debug|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.Debug|x64.Build.0 = Debug|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.Release|x64.ActiveCfg = Release|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.Release|x64.Build.0 = Release|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A76DD9DE-2F2F-31F7-ABE8-FA520EFE9F7B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.Debug|x64.ActiveCfg = Debug|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.Debug|x64.Build.0 = Debug|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.Release|x64.ActiveCfg = Release|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.Release|x64.Build.0 = Release|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3072186B-739D-3873-AE83-19BC1FBAFC53}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A8ACE1B4-76E1-3D44-9EA7-BF9B4F9B1A50}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
