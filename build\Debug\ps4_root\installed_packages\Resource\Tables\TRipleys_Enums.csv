RSID_TABLE_RIPLEYS_START, 2700,,,
 ,[Offset], RIPLEYS_FLYER_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\PBTRipleys, 1,
 ,[Offset], Ripleys\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON><PERSON>s\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\InstructionsITAL, 4,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\InstructionsGERM, 5,
 ,[Offset], R<PERSON>leys\InstructionsSPAN, 6,
 ,[Offset], Ripleys\InstructionsPORT, 7,
 ,[Offset], Ripleys\InstructionsDUTCH, 8,
 ,[Offset], tables\RIPLEYS_BG_scroll, 9,
RSID_TABLE_RIPLEYS_LIGHTS, 2701,,,
RSID_TABLE_RIPLEYS_CAMERAS, 2702,,,
RSID_TABLE_RIPLEYS_LAMP_TEXTURES, 2703,,,
 ,[Offset], L_01_Off, 0,
 ,[Offset], L_01_On, 1,
 ,[Offset], L_02_Off, 2,
 ,[Offset], L_02_On, 3,
 ,[Offset], L_03_Off, 4,
 ,[Offset], L_03_On, 5,
 ,[Offset], L_04_Off, 6,
 ,[Offset], L_04_On, 7,
 ,[Offset], L_05_Off, 8,
 ,[Offset], L_05_On, 9,
 ,[Offset], L_06_Off, 10,
 ,[Offset], L_06_On, 11,
 ,[Offset], L_07_Off, 12,
 ,[Offset], L_07_On, 13,
 ,[Offset], L_08_Off, 14,
 ,[Offset], L_08_On, 15,
 ,[Offset], L_09_Off, 16,
 ,[Offset], L_09_On, 17,
 ,[Offset], L_10_Off, 18,
 ,[Offset], L_10_On, 19,
 ,[Offset], L_11_Off, 20,
 ,[Offset], L_11_On, 21,
 ,[Offset], L_12_Off, 22,
 ,[Offset], L_12_On, 23,
 ,[Offset], L_13_Off, 24,
 ,[Offset], L_13_On, 25,
 ,[Offset], L_14_Off, 26,
 ,[Offset], L_14_On, 27,
 ,[Offset], L_15_Off, 28,
 ,[Offset], L_15_On, 29,
 ,[Offset], L_16_Off, 30,
 ,[Offset], L_16_On, 31,
 ,[Offset], L_17_Off, 32,
 ,[Offset], L_17_On, 33,
 ,[Offset], L_18_Off, 34,
 ,[Offset], L_18_On, 35,
 ,[Offset], L_19_Off, 36,
 ,[Offset], L_19_On, 37,
 ,[Offset], L_20_Off, 38,
 ,[Offset], L_20_On, 39,
 ,[Offset], L_21_Off, 40,
 ,[Offset], L_21_On, 41,
 ,[Offset], L_22_Off, 42,
 ,[Offset], L_22_On, 43,
 ,[Offset], L_23_Off, 44,
 ,[Offset], L_23_On, 45,
 ,[Offset], L_24_Off, 46,
 ,[Offset], L_24_On, 47,
 ,[Offset], L_25_Off, 48,
 ,[Offset], L_25_On, 49,
 ,[Offset], L_26_Off, 50,
 ,[Offset], L_26_On, 51,
 ,[Offset], L_27_Off, 52,
 ,[Offset], L_27_On, 53,
 ,[Offset], L_28_Off, 54,
 ,[Offset], L_28_On, 55,
 ,[Offset], L_29_Off, 56,
 ,[Offset], L_29_On, 57,
 ,[Offset], L_30_Off, 58,
 ,[Offset], L_30_On, 59,
 ,[Offset], L_31_Off, 60,
 ,[Offset], L_31_On, 61,
 ,[Offset], L_32_Off, 62,
 ,[Offset], L_32_On, 63,
 ,[Offset], L_33_Off, 64,
 ,[Offset], L_33_On, 65,
 ,[Offset], L_34_Off, 66,
 ,[Offset], L_34_On, 67,
 ,[Offset], L_35_Off, 68,
 ,[Offset], L_35_On, 69,
 ,[Offset], L_36_Off, 70,
 ,[Offset], L_36_On, 71,
 ,[Offset], L_37_Off, 72,
 ,[Offset], L_37_On, 73,
 ,[Offset], L_38_Off, 74,
 ,[Offset], L_38_On, 75,
 ,[Offset], L_39_Off, 76,
 ,[Offset], L_39_On, 77,
 ,[Offset], L_40_Off, 78,
 ,[Offset], L_40_On, 79,
 ,[Offset], L_41_Off, 80,
 ,[Offset], L_41_On, 81,
 ,[Offset], L_42_Off, 82,
 ,[Offset], L_42_On, 83,
 ,[Offset], L_43_Off, 84,
 ,[Offset], L_43_On, 85,
 ,[Offset], L_44_Off, 86,
 ,[Offset], L_44_On, 87,
 ,[Offset], L_45_Off, 88,
 ,[Offset], L_45_On, 89,
 ,[Offset], L_46_Off, 90,
 ,[Offset], L_46_On, 91,
 ,[Offset], L_47_Off, 92,
 ,[Offset], L_47_On, 93,
 ,[Offset], L_48_Off, 94,
 ,[Offset], L_48_On, 95,
 ,[Offset], L_49_Off, 96,
 ,[Offset], L_49_On, 97,
 ,[Offset], L_50_Off, 98,
 ,[Offset], L_50_On, 99,
 ,[Offset], L_51_Off, 100,
 ,[Offset], L_51_On, 101,
 ,[Offset], L_52_Off, 102,
 ,[Offset], L_52_On, 103,
 ,[Offset], L_53_Off, 104,
 ,[Offset], L_53_On, 105,
 ,[Offset], L_54_Off, 106,
 ,[Offset], L_54_On, 107,
 ,[Offset], L_55_Off, 108,
 ,[Offset], L_55_On, 109,
 ,[Offset], L_56_Off, 110,
 ,[Offset], L_56_On, 111,
 ,[Offset], L_57_Off, 112,
 ,[Offset], L_57_On, 113,
 ,[Offset], L_58_Off, 114,
 ,[Offset], L_58_On, 115,
 ,[Offset], L_59_Off, 116,
 ,[Offset], L_59_On, 117,
 ,[Offset], L_60_Off, 118,
 ,[Offset], L_60_On, 119,
 ,[Offset], L_61_Off, 120,
 ,[Offset], L_61_On, 121,
 ,[Offset], L_62_Off, 122,
 ,[Offset], L_62_On, 123,
 ,[Offset], L_63_Off, 124,
 ,[Offset], L_63_On, 125,
 ,[Offset], L_64_Off, 126,
 ,[Offset], L_64_On, 127,
 ,[Offset], L_65_Off, 128,
 ,[Offset], L_65_On, 129,
 ,[Offset], L_66_Off, 130,
 ,[Offset], L_66_On, 131,
 ,[Offset], L_67_Off, 132,
 ,[Offset], L_67_On, 133,
 ,[Offset], L_68_Off, 134,
 ,[Offset], L_68_On, 135,
 ,[Offset], L_69_Off, 136,
 ,[Offset], L_69_On, 137,
 ,[Offset], L_70_Off, 138,
 ,[Offset], L_70_On, 139,
 ,[Offset], L_71_Off, 140,
 ,[Offset], L_71_On, 141,
 ,[Offset], L_72_Off, 142,
 ,[Offset], L_72_On, 143,
 ,[Offset], L_73_Off, 144,
 ,[Offset], L_73_On, 145,
 ,[Offset], L_74_Off, 146,
 ,[Offset], L_74_On, 147,
 ,[Offset], L_75_Off, 148,
 ,[Offset], L_75_On, 149,
 ,[Offset], L_76_Off, 150,
 ,[Offset], L_76_On, 151,
 ,[Offset], L_77_Off, 152,
 ,[Offset], L_77_On, 153,
 ,[Offset], L_78_Off, 154,
 ,[Offset], L_78_On, 155,
 ,[Offset], F_25_Off, 156,
 ,[Offset], F_25_On, 157,
 ,[Offset], F_25B_Off, 158,
 ,[Offset], F_25B_On, 159,
 ,[Offset], F_27_Off, 160,
 ,[Offset], F_27_On, 161,
 ,[Offset], F_27_On, 162,
 ,[Offset], F_27_On, 163,
 ,[Offset], F_29_Off, 164,
 ,[Offset], F_29_On, 165,
 ,[Offset], F_30_Off, 166,
 ,[Offset], F_30_On, 167,
 ,[Offset], F_30B_Off, 168,
 ,[Offset], F_30B_On, 169,
 ,[Offset], F_32_Off, 170,
 ,[Offset], F_32_On, 171,
 ,[Offset], L_33_Off_S, 172,
 ,[Offset], L_34_Off_S, 173,
 ,[Offset], L_35_Off_S, 174,
 ,[Offset], L_60_Off_S, 175,
 ,[Offset], L_61_Off_S, 176,
 ,[Offset], L_62_Off_S, 177,
 ,[Offset], F_22_Off, 178,
 ,[Offset], F_22_On, 179,
RSID_TABLE_RIPLEYS_TEXTURES, 2704,,,
 ,[Offset], Shrunken_Head, 0,
 ,[Offset], Shrunken_Head_On, 1,
 ,[Offset], backglass, 2,
 ,[Offset], black_grain, 3,
 ,[Offset], Box_Textures, 4,
 ,[Offset], cabfront, 5,
 ,[Offset], ClearPlasticPost_01, 6,
 ,[Offset], Flasher_Clear, 7,
 ,[Offset], Flipper, 8,
 ,[Offset], Habit_Trail_1, 9,
 ,[Offset], Habit_Trail_2, 10,
 ,[Offset], Habit_Trail_3, 11,
 ,[Offset], Habit_Trail_4, 12,
 ,[Offset], Habit_Trail_1_s, 13,
 ,[Offset], Habit_Trail_2_s, 14,
 ,[Offset], Habit_Trail_3_s, 15,
 ,[Offset], Habit_Trail_4_s, 16,
 ,[Offset], House, 17,
 ,[Offset], Metal_Copper, 18,
 ,[Offset], Metal_Copper_s, 19,
 ,[Offset], Metals_Matte, 20,
 ,[Offset], Plastic_Ramp_01, 21,
 ,[Offset], Plastic_Ramp_02, 22,
 ,[Offset], Plastic_Ramp_01_s, 23,
 ,[Offset], Plastic_Ramp_02_s, 24,
 ,[Offset], Plastics_01, 25,
 ,[Offset], Plastics_02, 26,
 ,[Offset], Plastics_03, 27,
 ,[Offset], Plastics_04, 28,
 ,[Offset], Plastics_05, 29,
 ,[Offset], Plastics_06, 30,
 ,[Offset], Plastics_07, 31,
 ,[Offset], Plastics_08, 32,
 ,[Offset], Red_Post, 33,
 ,[Offset], Target_White, 34,
 ,[Offset], Target_Yellow, 35,
 ,[Offset], Apron, 36,
 ,[Offset], Back_Wall_ABC, 37,
 ,[Offset], Plunger, 38,
 ,[Offset], Extra_Metal_Parts, 39,
 ,[Offset], Flipper_Button, 40,
 ,[Offset], BlackMatte_Temp, 41,
 ,[Offset], rubberband_Temp, 42,
 ,[Offset], rubberband_Temp_s, 43,
 ,[Offset], bulb1, 44,
 ,[Offset], Metal_Temp, 45,
 ,[Offset], Metal_Temp_s, 46,
 ,[Offset], Silver Metal Screws_Temp, 47,
 ,[Offset], Silver Metal Screws_Temp_s, 48,
 ,[Offset], HarleyBumperBody, 49,
 ,[Offset], HarleyBumperBody_s, 50,
 ,[Offset], MetalPlane, 51,
 ,[Offset], playfield_bottom, 52,
 ,[Offset], playfield_top, 53,
 ,[Offset], BumperTop, 54,
 ,[Offset], BumperTop_Red, 55,
 ,[Offset], BumperTop_s, 56,
 ,[Offset], BumperTop_Red_s, 57,
 ,[Offset], Rubber Post_Temp, 58,
 ,[Offset], Shrunken_Head_s, 59,
 ,[Offset], Shrunken_Head_Hair, 60,
 ,[Offset], Shrunken_Head_Hair_s, 61,
 ,[Offset], Bumper_Hamer, 62,
 ,[Offset], Bumper_Sensors, 63,
 ,[Offset], Spinner, 64,
 ,[Offset], Harley_Spinner, 65,
 ,[Offset], Metal_Walls, 66,
 ,[Offset], Metal_Walls_s, 67,
 ,[Offset], BlackBox, 68,
 ,[Offset], Metal_Hammer, 69,
 ,[Offset], Cylinder_Motor, 70,
 ,[Offset], Plastic_Button_CMotor, 71,
 ,[Offset], HouseLed, 72,
 ,[Offset], HouseLed2, 73,
 ,[Offset], HouseLed3, 74,
 ,[Offset], ClearPlasticPost_02, 75,
 ,[Offset], NewMetalSection, 76,
 ,[Offset], NewMetalSection_s, 77,
 ,[Offset], Metal_Lamp, 78,
 ,[Offset], Metal_Lamp_s, 79,
 ,[Offset], Harley_Gate, 80,
 ,[Offset], Harley_Gate_s, 81,
 ,[Offset], PopBumper_Lights, 82,
 ,[Offset], Arrow_Metal, 83,
 ,[Offset], Round_Metal, 84,
 ,[Offset], Arrow_Metal_s, 85,
 ,[Offset], Round_Metal_s, 86,
 ,[Offset], Plastic_Ramp_Brackets2, 87,
 ,[Offset], Plastic_Ramp_Brackets2_s, 88,
 ,[Offset], Plastic_Ramp_Brackets, 89,
 ,[Offset], Plastic_Ramp_Brackets_s, 90,
 ,[Offset], Generic_Metal, 91,
 ,[Offset], Generic_Metal_S, 92,
 ,[Offset], Generic_Screw, 93,
 ,[Offset], Generic_Screw_s, 94,
 ,[Offset], Bulb_Red, 95,
RSID_TABLE_RIPLEYS_LED_TEXTURES, 2705,,,
 ,[Offset], LED_Display_Back, 0,
 ,[Offset], LED_Display_Front, 1,
RSID_TABLE_RIPLEYS_MODELS, 2706,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Wall, 1,
 ,[Offset], Cabinet_Backglass, 2,
 ,[Offset], Cabinet_Body, 3,
 ,[Offset], Cabinet_Metals, 4,
 ,[Offset], Clear_Plastics, 5,
 ,[Offset], DidYouKnow_Sign, 6,
 ,[Offset], Flashers, 7,
 ,[Offset], Flipper_Plastics, 8,
 ,[Offset], Left_Flipper, 9,
 ,[Offset], Garage, 10,
 ,[Offset], HabbiTrail, 11,
 ,[Offset], HabbiTrail_Crossing, 12,
 ,[Offset], Left_Plastic_Ramp, 13,
 ,[Offset], Lights, 14,
 ,[Offset], Metal_Pieces, 15,
 ,[Offset], Metal_Walls, 16,
 ,[Offset], Plastic_Pieces, 17,
 ,[Offset], Plastic_Posts, 18,
 ,[Offset], Playfield, 19,
 ,[Offset], PopBumpers, 20,
 ,[Offset], Right_Plastic_Ramp, 21,
 ,[Offset], Rubber_Posts, 22,
 ,[Offset], Rubbers, 23,
 ,[Offset], Shrunken_Head, 24,
 ,[Offset], Slingshot_Left, 25,
 ,[Offset], Slingshot_Right, 26,
 ,[Offset], Spinner, 27,
 ,[Offset], Spinner_Brace, 28,
 ,[Offset], Target_White, 29,
 ,[Offset], Wires, 30,
 ,[Offset], Wooden_Rails, 31,
 ,[Offset], cabinet_interior, 32,
 ,[Offset], Target_Yellow, 33,
 ,[Offset], Right_Flipper, 34,
 ,[Offset], Right_Flipper_2, 35,
 ,[Offset], PopBumper_Metal, 36,
 ,[Offset], PopBumpers_Clear, 37,
 ,[Offset], Hammer, 38,
 ,[Offset], PopBumper_Metal_Cut, 39,
 ,[Offset], Wire_Small, 40,
 ,[Offset], Motor, 41,
 ,[Offset], Gate_A, 42,
 ,[Offset], PopBumper_Lights, 43,
 ,[Offset], Shrunken_Head_Hair, 44,
 ,[Offset], Shrunken_Head_Hair_under, 45,
 ,[Offset], Slingshot_Left_Extended, 46,
 ,[Offset], Slingshot_Right_Extended, 47,
 ,[Offset], Metal_Gate_A, 48,
 ,[Offset], Metal_Gate_B, 49,
 ,[Offset], Plunger, 50,
 ,[Offset], Diverter_A, 51,
 ,[Offset], Diverter_B, 52,
 ,[Offset], Diverter_C, 53,
 ,[Offset], Light_Cutouts, 54,
 ,[Offset], Back_Wall_Lights, 55,
 ,[Offset], Garage_LED_Back, 56,
 ,[Offset], Garage_LED_Front, 57,
 ,[Offset], Motor_Shaft, 58,
 ,[Offset], Metal_Gate_C, 59,
 ,[Offset], Magnet_LED, 60,
 ,[Offset], Arrow_Light, 61,
 ,[Offset], Flasher_Lights, 62,
RSID_TABLE_RIPLEYS_MODELS_LODS, 2707,,,
 ,[Offset], Apron, 0,
 ,[Offset], Back_Wall, 1,
 ,[Offset], Cabinet_Backglass, 2,
 ,[Offset], Cabinet_Body, 3,
 ,[Offset], Cabinet_Metals, 4,
 ,[Offset], Clear_Plastics, 5,
 ,[Offset], DidYouKnow_Sign, 6,
 ,[Offset], Flashers, 7,
 ,[Offset], Flipper_Plastics, 8,
 ,[Offset], Left_Flipper, 9,
 ,[Offset], Garage, 10,
 ,[Offset], HabbiTrail, 11,
 ,[Offset], HabbiTrail_Crossing, 12,
 ,[Offset], Left_Plastic_Ramp, 13,
 ,[Offset], Lights, 14,
 ,[Offset], Metal_Pieces, 15,
 ,[Offset], Metal_Walls, 16,
 ,[Offset], Plastic_Pieces, 17,
 ,[Offset], Plastic_Posts, 18,
 ,[Offset], Playfield, 19,
 ,[Offset], PopBumpers, 20,
 ,[Offset], Right_Plastic_Ramp, 21,
 ,[Offset], Rubber_Posts, 22,
 ,[Offset], Rubbers, 23,
 ,[Offset], Shrunken_Head, 24,
 ,[Offset], Slingshot_Left, 25,
 ,[Offset], Slingshot_Right, 26,
 ,[Offset], Spinner, 27,
 ,[Offset], Spinner_Brace, 28,
 ,[Offset], Target_White, 29,
 ,[Offset], Wires, 30,
 ,[Offset], Wooden_Rails, 31,
 ,[Offset], cabinet_interior, 32,
 ,[Offset], Target_Yellow, 33,
 ,[Offset], Right_Flipper, 34,
 ,[Offset], Right_Flipper_2, 35,
 ,[Offset], PopBumper_Metal, 36,
 ,[Offset], PopBumpers_Clear, 37,
 ,[Offset], Hammer, 38,
 ,[Offset], PopBumper_Metal_Cut, 39,
 ,[Offset], Wire_Small, 40,
 ,[Offset], Motor, 41,
 ,[Offset], Gate_A, 42,
 ,[Offset], PopBumper_Lights, 43,
 ,[Offset], Shrunken_Head_Hair, 44,
 ,[Offset], Shrunken_Head_Hair_under, 45,
 ,[Offset], Slingshot_Left_Extended, 46,
 ,[Offset], Slingshot_Right_Extended, 47,
 ,[Offset], Metal_Gate_A, 48,
 ,[Offset], Metal_Gate_B, 49,
 ,[Offset], Plunger, 50,
 ,[Offset], Diverter_A, 51,
 ,[Offset], Diverter_B, 52,
 ,[Offset], Diverter_C, 53,
 ,[Offset], Light_Cutouts, 54,
 ,[Offset], Back_Wall_Lights, 55,
 ,[Offset], Garage_LED_Back, 56,
 ,[Offset], Garage_LED_Front, 57,
 ,[Offset], Motor_Shaft, 58,
 ,[Offset], Metal_Gate_C, 59,
 ,[Offset], Magnet_LED, 60,
 ,[Offset], Arrow_Light, 61,
 ,[Offset], Flasher_Lights, 62,
RSID_TABLE_RIPLEYS_COLLISIONS, 2708,,,
 ,[Offset], Apron_COL, 0,
 ,[Offset], HabbiTrail_COL, 1,
 ,[Offset], Dil_COL, 2,
 ,[Offset], InnerWall_COL, 3,
 ,[Offset], Left_Flipper_COL, 4,
 ,[Offset], Right_Flipper_COL, 5,
 ,[Offset], Left_FlipperLane_COL, 6,
 ,[Offset], Right_FlipperLane_COL, 7,
 ,[Offset], Left_PlasticRamp_COL, 8,
 ,[Offset], Right_PlasticRamp_COL, 9,
 ,[Offset], OuterWall_COL, 10,
 ,[Offset], Playfield_COL, 11,
 ,[Offset], Left_Slingshot_COL, 12,
 ,[Offset], Left_Slingshot_Front_COL, 13,
 ,[Offset], Right_Slingshot_COL, 14,
 ,[Offset], Right_Slingshot_Front_COL, 15,
 ,[Offset], Plunger_COL, 16,
 ,[Offset], Hammer_COL, 17,
 ,[Offset], YellowTarget_COL, 18,
 ,[Offset], RedTarget_COL, 19,
 ,[Offset], PopBumper_COL, 20,
 ,[Offset], Post_COL, 21,
 ,[Offset], Ball_Drain_COL, 22,
 ,[Offset], Left_Flipper_Back_COL, 23,
 ,[Offset], Right_Flipper_Back_COL, 24,
 ,[Offset], Right_Flipper_2_COL, 25,
 ,[Offset], Right_Flipper_2_Back_COL, 26,
 ,[Offset], Gate_COL, 27,
 ,[Offset], Magnet_Back_COL, 28,
 ,[Offset], Magnet_COL, 29,
 ,[Offset], Magnet_Light_Sensor, 30,
 ,[Offset], Spinner_COL, 31,
 ,[Offset], Trap_1_COL, 32,
 ,[Offset], Trap_2_COL, 33,
 ,[Offset], Trap_Skillshot_COL, 34,
 ,[Offset], Gate_A_Front_COL, 36,
 ,[Offset], Gate_A_Back_COL, 37,
 ,[Offset], Gate_B_Front_COL, 38,
 ,[Offset], Gate_B_Back_COL, 39,
 ,[Offset], Diverter_A_COL, 40,
 ,[Offset], Diverter_B_COL, 41,
 ,[Offset], Diverter_C_COL, 42,
 ,[Offset], Motor_Lane_COL, 43,
 ,[Offset], HabbiTrail_Curl_COL, 44,
 ,[Offset], HabbiTrail_Left_COL, 45,
 ,[Offset], HabbiTrail_Steep_COL, 46,
 ,[Offset], InnerWall_B_COL, 47,
 ,[Offset], InnerWall_C_COL, 48,
 ,[Offset], Left_PlasticRamp_B_COL, 49,
 ,[Offset], Post_B_COL, 50,
 ,[Offset], Post_C_COL, 51,
 ,[Offset], Post_D_COL, 52,
 ,[Offset], Post_E_COL, 53,
 ,[Offset], Ball_Trap_COL, 54,
 ,[Offset], HabbiTrail_Left_Wall_COL, 55,
 ,[Offset], HabbiTrail_Steep_Wall_COL, 56,
 ,[Offset], HabbiTrail_Wall_COL, 57,
 ,[Offset], Gate_C_Front_COL, 58,
 ,[Offset], Gate_C_Back_COL, 59,
 ,[Offset], Right_PlasticRamp_Back_COL, 60,
RSID_TABLE_RIPLEYS_PLACEMENT, 2709,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TABLE_RIPLEYS_ROM, 2710,,,
 ,[Offset], ripleys_320, 0,
 ,[Offset], ripleys_320, 1,
 ,[Offset], ripleys_320_Attract, 2,
 ,[Offset], ripleys_320_1P, 3,
 ,[Offset], ripleys_320_2P, 4,
 ,[Offset], ripleys_320_3P, 5,
 ,[Offset], ripleys_320_4P, 6,
 ,[Offset], ripleys_320, 7,
 ,[Offset], ripleys_320, 8,
 ,[Offset], ripleys_320, 9,
RSID_TABLE_RIPLEYS_SOUNDS_START, 2711,,,
RSID_TABLE_RIPLEYS_EMU_SOUNDS, 2712,,,
 ,[Offset], SFD02, 0,
 ,[Offset], SFD03, 1,
 ,[Offset], SFD04, 2,
 ,[Offset], SFD05, 3,
 ,[Offset], SFD06-LP1, 4,
 ,[Offset], SFD06-LP2, 5,
 ,[Offset], SFD07, 6,
 ,[Offset], SFD08, 7,
 ,[Offset], SFD09-LP1, 8,
 ,[Offset], SFD09-LP2, 9,
 ,[Offset], SFD0A, 10,
 ,[Offset], SFD0B-LP1, 11,
 ,[Offset], SFD0B-LP2, 12,
 ,[Offset], SFD0C, 13,
 ,[Offset], SFD0D, 14,
 ,[Offset], SFD0E, 15,
 ,[Offset], SFD0F, 16,
 ,[Offset], SFD10, 17,
 ,[Offset], SFD11, 18,
 ,[Offset], SFD12, 19,
 ,[Offset], SFD14, 20,
 ,[Offset], SFD15, 21,
 ,[Offset], SFD16, 22,
 ,[Offset], SFD17, 23,
 ,[Offset], SFD18, 24,
 ,[Offset], SFD1A, 25,
 ,[Offset], SFD1B, 26,
 ,[Offset], SFD1C, 27,
 ,[Offset], SFD1D, 28,
 ,[Offset], SFD20, 29,
 ,[Offset], SFD21, 30,
 ,[Offset], SFD3E, 31,
 ,[Offset], SFD40, 32,
 ,[Offset], SFD41, 33,
 ,[Offset], SFD42, 34,
 ,[Offset], SFD43, 35,
 ,[Offset], SFD44, 36,
 ,[Offset], SFD45, 37,
 ,[Offset], SFD46, 38,
 ,[Offset], SFD47, 39,
 ,[Offset], SFD48, 40,
 ,[Offset], SFD49, 41,
 ,[Offset], SFD4A, 42,
 ,[Offset], SFD4B, 43,
 ,[Offset], SFD4C, 44,
 ,[Offset], SFD4D, 45,
 ,[Offset], SFD4E, 46,
 ,[Offset], SFD4F, 47,
 ,[Offset], SFD50, 48,
 ,[Offset], SFD51, 49,
 ,[Offset], SFD52, 50,
 ,[Offset], SFD53, 51,
 ,[Offset], SFD54, 52,
 ,[Offset], SFD55, 53,
 ,[Offset], SFD56, 54,
 ,[Offset], SFD57, 55,
 ,[Offset], SFD58, 56,
 ,[Offset], SFD59, 57,
 ,[Offset], SFD5A, 58,
 ,[Offset], SFD5B, 59,
 ,[Offset], SFD5D, 60,
 ,[Offset], SFD5E, 61,
 ,[Offset], SFD5F, 62,
 ,[Offset], SFD60, 63,
 ,[Offset], SFD61, 64,
 ,[Offset], SFD62, 65,
 ,[Offset], SFD63, 66,
 ,[Offset], SFD64, 67,
 ,[Offset], SFD65, 68,
 ,[Offset], SFD66, 69,
 ,[Offset], SFD67, 70,
 ,[Offset], SFD68, 71,
 ,[Offset], SFD69, 72,
 ,[Offset], SFD6A, 73,
 ,[Offset], SFD6B, 74,
 ,[Offset], SFD6C, 75,
 ,[Offset], SFD6D, 76,
 ,[Offset], SFD6E, 77,
 ,[Offset], SFD70, 78,
 ,[Offset], SFD71, 79,
 ,[Offset], SFD72, 80,
 ,[Offset], SFD73, 81,
 ,[Offset], SFD74, 82,
 ,[Offset], SFD75, 83,
 ,[Offset], SFD76, 84,
 ,[Offset], SFD77, 85,
 ,[Offset], SFD78, 86,
 ,[Offset], SFD7B, 87,
 ,[Offset], SFD7C, 88,
 ,[Offset], SFD7D, 89,
 ,[Offset], SFD85, 90,
 ,[Offset], SFD86, 91,
 ,[Offset], SFD87, 92,
 ,[Offset], SFD88, 93,
 ,[Offset], SFD8C, 94,
 ,[Offset], SFD8E, 95,
 ,[Offset], SFD8F, 96,
 ,[Offset], SFD90, 97,
 ,[Offset], SFD91, 98,
 ,[Offset], SFD92, 99,
 ,[Offset], SFD93, 100,
 ,[Offset], SFD94, 101,
 ,[Offset], SFD96, 102,
 ,[Offset], SFD97, 103,
 ,[Offset], SFD98, 104,
 ,[Offset], SFD99, 105,
 ,[Offset], SFD9B, 106,
 ,[Offset], SFD9C, 107,
 ,[Offset], SFD9D, 108,
 ,[Offset], SFD9E, 109,
 ,[Offset], SFD9F, 110,
 ,[Offset], SFDA1, 111,
 ,[Offset], SFDA2, 112,
 ,[Offset], SFDA3, 113,
 ,[Offset], SFDA4, 114,
 ,[Offset], SFDA5, 115,
 ,[Offset], SFDA8, 116,
 ,[Offset], SFDA9, 117,
 ,[Offset], SFDB0, 118,
 ,[Offset], SFDB1, 119,
 ,[Offset], SFDB2, 120,
 ,[Offset], SFDB3, 121,
 ,[Offset], SFDB4, 122,
 ,[Offset], SFDB5, 123,
 ,[Offset], SFDB6, 124,
 ,[Offset], SFDB7, 125,
 ,[Offset], SFDB8, 126,
 ,[Offset], SFDB9, 127,
 ,[Offset], SFDBA, 128,
 ,[Offset], SFDBB, 129,
 ,[Offset], SFDF6, 130,
 ,[Offset], SFDF7, 131,
 ,[Offset], SFDF8, 132,
 ,[Offset], SFDF9, 133,
 ,[Offset], SFDFA, 134,
 ,[Offset], SFDFB, 135,
 ,[Offset], SFE30, 136,
 ,[Offset], SFE31, 137,
 ,[Offset], SFE32, 138,
 ,[Offset], SFE33, 139,
 ,[Offset], SFE34, 140,
 ,[Offset], SFE35, 141,
 ,[Offset], SFE36, 142,
 ,[Offset], SFE37, 143,
 ,[Offset], SFE38, 144,
 ,[Offset], SFE39, 145,
 ,[Offset], SFE3A, 146,
 ,[Offset], SFE3B, 147,
 ,[Offset], SFE3C, 148,
 ,[Offset], SFE3D, 149,
 ,[Offset], SFE3E, 150,
 ,[Offset], SFE3F, 151,
 ,[Offset], SFE40, 152,
 ,[Offset], SFE41, 153,
 ,[Offset], SFE42, 154,
 ,[Offset], SFE43, 155,
 ,[Offset], SFE44, 156,
 ,[Offset], SFE45, 157,
 ,[Offset], SFE46, 158,
 ,[Offset], SFE47, 159,
 ,[Offset], SFE48, 160,
 ,[Offset], SFE49, 161,
 ,[Offset], SFE4A, 162,
 ,[Offset], SFE4B, 163,
 ,[Offset], SFE4C, 164,
 ,[Offset], SFE4D, 165,
 ,[Offset], SFE4E, 166,
 ,[Offset], SFE4F, 167,
 ,[Offset], SFE50, 168,
 ,[Offset], SFE51, 169,
 ,[Offset], SFE52, 170,
 ,[Offset], SFE53, 171,
 ,[Offset], SFE54, 172,
 ,[Offset], SFE55, 173,
 ,[Offset], SFE56, 174,
 ,[Offset], SFE57, 175,
 ,[Offset], SFE58, 176,
 ,[Offset], SFE59, 177,
 ,[Offset], SFE5A, 178,
 ,[Offset], SFE5B, 179,
 ,[Offset], SFE5C, 180,
 ,[Offset], SFE5D, 181,
 ,[Offset], SFE5E, 182,
 ,[Offset], SFE5F, 183,
 ,[Offset], SFE60, 184,
 ,[Offset], SFE61, 185,
 ,[Offset], SFE62, 186,
 ,[Offset], SFE63, 187,
 ,[Offset], SFE64, 188,
 ,[Offset], SFE65, 189,
 ,[Offset], SFE66, 190,
 ,[Offset], SFE67, 191,
 ,[Offset], SFE70, 192,
 ,[Offset], SFE71, 193,
 ,[Offset], SFE72, 194,
 ,[Offset], SFE73, 195,
 ,[Offset], SFE74, 196,
 ,[Offset], SFE75, 197,
 ,[Offset], SFE76, 198,
 ,[Offset], SFE77, 199,
 ,[Offset], SFE78, 200,
 ,[Offset], SFE79, 201,
 ,[Offset], SFE7A, 202,
 ,[Offset], SFE7B, 203,
 ,[Offset], SFE7C, 204,
 ,[Offset], SFE7D, 205,
 ,[Offset], SFE7E, 206,
 ,[Offset], SFE7F, 207,
 ,[Offset], SFE80, 208,
 ,[Offset], SFE81, 209,
 ,[Offset], SFE82, 210,
 ,[Offset], SFE83, 211,
 ,[Offset], SFE84, 212,
 ,[Offset], SFE85, 213,
 ,[Offset], SFE86, 214,
 ,[Offset], SFE87, 215,
 ,[Offset], SFE88, 216,
 ,[Offset], SFE89, 217,
 ,[Offset], SFE8A, 218,
 ,[Offset], SFE90, 219,
 ,[Offset], SFE91, 220,
 ,[Offset], SFE92, 221,
 ,[Offset], SFE93, 222,
 ,[Offset], SFE94, 223,
 ,[Offset], SFE95, 224,
 ,[Offset], SFEA0, 225,
 ,[Offset], SFEA1, 226,
 ,[Offset], SFEA2, 227,
 ,[Offset], SFEA3, 228,
 ,[Offset], SFEA4, 229,
 ,[Offset], SFEA5, 230,
 ,[Offset], SFEA6, 231,
 ,[Offset], SFEA7, 232,
 ,[Offset], SFEA8, 233,
 ,[Offset], SFEA9, 234,
 ,[Offset], SFEAA, 235,
 ,[Offset], SFEAB, 236,
 ,[Offset], SFEAC, 237,
 ,[Offset], SFEB0, 238,
 ,[Offset], SFEB1, 239,
 ,[Offset], SFEB2, 240,
 ,[Offset], SFEB3, 241,
 ,[Offset], SFEB4, 242,
 ,[Offset], SFEB5, 243,
 ,[Offset], SFEB6, 244,
 ,[Offset], SFEB7, 245,
 ,[Offset], SFEB8, 246,
 ,[Offset], SFEB9, 247,
 ,[Offset], SFEBA, 248,
 ,[Offset], SFEBC, 249,
 ,[Offset], SFEBD, 250,
 ,[Offset], SFEBE, 251,
 ,[Offset], SFEBF, 252,
 ,[Offset], SFEC0, 253,
 ,[Offset], SFEC1, 254,
 ,[Offset], SFEC2, 255,
 ,[Offset], SFEC3, 256,
 ,[Offset], SFEC4, 257,
 ,[Offset], SFEC5, 258,
 ,[Offset], SFEC6, 259,
 ,[Offset], SFEC7, 260,
 ,[Offset], SFEC8, 261,
 ,[Offset], SFEC9, 262,
 ,[Offset], SFECA, 263,
 ,[Offset], SFECB, 264,
 ,[Offset], SFECC, 265,
 ,[Offset], SFECD, 266,
 ,[Offset], SFECE, 267,
 ,[Offset], SFECF, 268,
 ,[Offset], SFED0, 269,
 ,[Offset], SFED1, 270,
 ,[Offset], SFED2, 271,
 ,[Offset], SFED3, 272,
 ,[Offset], SFED4, 273,
 ,[Offset], SFED5, 274,
 ,[Offset], SFED6, 275,
 ,[Offset], SFED8, 276,
 ,[Offset], SFED9, 277,
 ,[Offset], SFEDA, 278,
 ,[Offset], SFEE0, 279,
 ,[Offset], SFEE1, 280,
 ,[Offset], SFEE2, 281,
 ,[Offset], SFEE3, 282,
 ,[Offset], SFEE4, 283,
 ,[Offset], SFEE5, 284,
 ,[Offset], SFEE6, 285,
 ,[Offset], SFEE7, 286,
 ,[Offset], SFEE8, 287,
 ,[Offset], SFEE9, 288,
 ,[Offset], SFEEA, 289,
 ,[Offset], SFEEB, 290,
 ,[Offset], SFEEC, 291,
 ,[Offset], SFC01, 292,
 ,[Offset], SFC02, 293,
 ,[Offset], SFC03, 294,
 ,[Offset], SFC04, 295,
 ,[Offset], SFC05, 296,
 ,[Offset], SFC06, 297,
 ,[Offset], SFC07, 298,
 ,[Offset], SFC09, 299,
 ,[Offset], SFC0A, 300,
 ,[Offset], SFC0B, 301,
 ,[Offset], SFC0C, 302,
 ,[Offset], SFC0D, 303,
 ,[Offset], SFC0F, 304,
 ,[Offset], SFC10, 305,
 ,[Offset], SFC11, 306,
 ,[Offset], SFC12, 307,
 ,[Offset], SFC13, 308,
 ,[Offset], SFC14, 309,
 ,[Offset], SFC15, 310,
 ,[Offset], SFC16, 311,
 ,[Offset], SFC17, 312,
 ,[Offset], SFC18, 313,
 ,[Offset], SFC19, 314,
 ,[Offset], SFC1A, 315,
 ,[Offset], SFC1B, 316,
 ,[Offset], SFC1C, 317,
 ,[Offset], SFC1D, 318,
 ,[Offset], SFC1E, 319,
 ,[Offset], SFC1F, 320,
 ,[Offset], SFC20, 321,
 ,[Offset], SFC21, 322,
 ,[Offset], SFC22, 323,
 ,[Offset], SFC23, 324,
 ,[Offset], SFC24, 325,
 ,[Offset], SFC25, 326,
 ,[Offset], SFC26, 327,
 ,[Offset], SFC27, 328,
 ,[Offset], SFC28, 329,
 ,[Offset], SFC29, 330,
 ,[Offset], SFC2A, 331,
 ,[Offset], SFC2B, 332,
 ,[Offset], SFC2C, 333,
 ,[Offset], SFC2D, 334,
 ,[Offset], SFC2E, 335,
 ,[Offset], SFC2F, 336,
 ,[Offset], SFC30, 337,
 ,[Offset], SFC31, 338,
 ,[Offset], SFC32, 339,
 ,[Offset], SFC33, 340,
 ,[Offset], SFC34, 341,
 ,[Offset], SFC35, 342,
 ,[Offset], SFC36, 343,
 ,[Offset], SFC37, 344,
 ,[Offset], SFC38, 345,
 ,[Offset], SFC39, 346,
 ,[Offset], SFC3A, 347,
 ,[Offset], SFC3B, 348,
 ,[Offset], SFC3C, 349,
 ,[Offset], SFC3D, 350,
 ,[Offset], SFC3E, 351,
 ,[Offset], SFC3F, 352,
 ,[Offset], SFC40, 353,
 ,[Offset], SFC41, 354,
 ,[Offset], SFC42, 355,
 ,[Offset], SFC43, 356,
 ,[Offset], SFC44, 357,
 ,[Offset], SFC45, 358,
 ,[Offset], SFC46, 359,
 ,[Offset], SFC80, 360,
 ,[Offset], SFC81, 361,
 ,[Offset], SFC82, 362,
 ,[Offset], SFC83, 363,
 ,[Offset], SFC84, 364,
 ,[Offset], SFC85, 365,
 ,[Offset], SFC86, 366,
 ,[Offset], SFC87, 367,
 ,[Offset], SFC88, 368,
 ,[Offset], SFC89, 369,
 ,[Offset], SFC8A, 370,
 ,[Offset], SFC8B, 371,
 ,[Offset], SFC8C, 372,
 ,[Offset], SFC8D, 373,
 ,[Offset], SFC8E, 374,
 ,[Offset], SFC8F, 375,
 ,[Offset], SFC90, 376,
 ,[Offset], SFC91, 377,
 ,[Offset], SFC92, 378,
 ,[Offset], SFC93, 379,
 ,[Offset], SFC94, 380,
 ,[Offset], SFC95, 381,
 ,[Offset], SFC96, 382,
 ,[Offset], SFC97, 383,
 ,[Offset], SFC98, 384,
 ,[Offset], SFC99, 385,
 ,[Offset], SFC9A, 386,
 ,[Offset], SFC9B, 387,
 ,[Offset], SFC9C, 388,
 ,[Offset], SFC9D, 389,
 ,[Offset], SFC9E, 390,
 ,[Offset], SFC9F, 391,
 ,[Offset], SFCA0, 392,
 ,[Offset], SFCA1, 393,
 ,[Offset], SFCA2, 394,
 ,[Offset], SFCA3, 395,
 ,[Offset], SFCA4, 396,
 ,[Offset], SFCA5, 397,
 ,[Offset], SFCA6, 398,
 ,[Offset], SFCA7, 399,
 ,[Offset], SFCA8, 400,
 ,[Offset], SFCA9, 401,
 ,[Offset], SFCAA, 402,
 ,[Offset], SFCAB, 403,
 ,[Offset], SFCAC, 404,
 ,[Offset], SFCAD, 405,
 ,[Offset], SFCAE, 406,
 ,[Offset], SFCAF, 407,
 ,[Offset], SFCB0, 408,
 ,[Offset], SFCB1, 409,
 ,[Offset], SFCB2, 410,
 ,[Offset], SFCB3, 411,
 ,[Offset], SFCB4, 412,
 ,[Offset], SFCB5, 413,
 ,[Offset], SFCB6, 414,
 ,[Offset], SFCB7, 415,
 ,[Offset], SFCB8, 416,
 ,[Offset], SFCB9, 417,
 ,[Offset], SFCBA, 418,
 ,[Offset], SFCBB, 419,
 ,[Offset], SFCBC, 420,
 ,[Offset], SFCBD, 421,
 ,[Offset], SFCBE, 422,
 ,[Offset], SFCBF, 423,
 ,[Offset], SFCC0, 424,
 ,[Offset], SFCC1, 425,
 ,[Offset], SFCC2, 426,
 ,[Offset], SFCC3, 427,
 ,[Offset], SFCC4, 428,
 ,[Offset], SFCC5, 429,
 ,[Offset], SFCC6, 430,
 ,[Offset], SFCC7, 431,
 ,[Offset], SFCC8, 432,
 ,[Offset], SFCC9, 433,
 ,[Offset], SFCCA, 434,
 ,[Offset], SFCCB, 435,
 ,[Offset], SFCCC, 436,
 ,[Offset], SFCCD, 437,
 ,[Offset], SFCCE, 438,
 ,[Offset], SFCCF, 439,
 ,[Offset], SFCD0, 440,
 ,[Offset], SFCD1, 441,
 ,[Offset], SFCD2, 442,
 ,[Offset], SFCD3, 443,
 ,[Offset], SFCD4, 444,
 ,[Offset], SFCD5, 445,
 ,[Offset], SFCD6, 446,
 ,[Offset], SFCD7, 447,
 ,[Offset], SFCD8, 448,
 ,[Offset], SFCD9, 449,
 ,[Offset], SFCDA, 450,
 ,[Offset], SFCDB, 451,
 ,[Offset], SFCDC, 452,
 ,[Offset], SFCDD, 453,
 ,[Offset], SFCDE, 454,
 ,[Offset], SFCDF, 455,
 ,[Offset], SFCE0, 456,
 ,[Offset], SFCE1, 457,
 ,[Offset], SFCE2, 458,
 ,[Offset], SFCE3, 459,
 ,[Offset], SFCE4, 460,
 ,[Offset], SFCE5, 461,
 ,[Offset], SFCE6, 462,
 ,[Offset], SFCE7, 463,
 ,[Offset], SFCE8, 464,
 ,[Offset], SFCE9, 465,
RSID_TABLE_RIPLEYS_MECH_SOUNDS, 2713,,,
 ,[Offset], auto_plunger, 0,
 ,[Offset], ball_lock_kickout, 1,
 ,[Offset], center_kickout, 2,
 ,[Offset], continent_eject, 3,
 ,[Offset], drop_into_bozo, 4,
 ,[Offset], drop_into_continent, 5,
RSID_TABLE_RIPLEYS_SOUNDS_END, 2714,,,
RSID_TABLE_RIPLEYS_SAMPLES, 2715,,,
RSID_TABLE_RIPLEYS_VERSION, 2716,,,
RSID_TABLE_RIPLEYS_END, 2717,,,
