{"artifacts": [{"path": "RelWithDebInfo/PS4Emulator.exe"}, {"path": "RelWithDebInfo/PS4Emulator.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "target_link_directories", "target_link_libraries", "set_target_properties", "include", "_find_package", "find_package", "target_compile_options", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["D:/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt", "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake", "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake", "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets.cmake", "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-config.cmake", "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets.cmake", "D:/vcpkg/installed/x64-windows/share/zydis/zydis-config.cmake", "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets.cmake", "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgConfig.cmake", "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets.cmake", "D:/vcpkg/installed/x64-windows/share/imgui/imgui-config.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 74, "parent": 0}, {"command": 0, "file": 0, "line": 598, "parent": 1}, {"command": 2, "file": 1, "line": 193, "parent": 0}, {"command": 3, "file": 1, "line": 144, "parent": 0}, {"command": 3, "file": 1, "line": 194, "parent": 0}, {"command": 3, "file": 1, "line": 197, "parent": 0}, {"command": 7, "file": 1, "line": 116, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 7}, {"file": 3, "parent": 8}, {"command": 5, "file": 3, "line": 42, "parent": 9}, {"file": 2, "parent": 10}, {"command": 4, "file": 2, "line": 60, "parent": 11}, {"command": 7, "file": 1, "line": 123, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 13}, {"file": 5, "parent": 14}, {"command": 5, "file": 5, "line": 10, "parent": 15}, {"file": 4, "parent": 16}, {"command": 4, "file": 4, "line": 60, "parent": 17}, {"command": 7, "file": 1, "line": 129, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 19}, {"file": 7, "parent": 20}, {"command": 5, "file": 7, "line": 32, "parent": 21}, {"file": 6, "parent": 22}, {"command": 4, "file": 6, "line": 60, "parent": 23}, {"command": 7, "file": 1, "line": 137, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 25}, {"file": 9, "parent": 26}, {"command": 5, "file": 9, "line": 30, "parent": 27}, {"file": 8, "parent": 28}, {"command": 4, "file": 8, "line": 61, "parent": 29}, {"command": 7, "file": 1, "line": 115, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 31}, {"file": 11, "parent": 32}, {"command": 5, "file": 11, "line": 65, "parent": 33}, {"file": 10, "parent": 34}, {"command": 4, "file": 10, "line": 60, "parent": 35}, {"command": 8, "file": 1, "line": 198, "parent": 0}, {"command": 9, "file": 1, "line": 178, "parent": 0}, {"command": 10, "file": 1, "line": 28, "parent": 0}, {"command": 11, "file": 1, "line": 83, "parent": 0}, {"command": 11, "file": 1, "line": 192, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++latest -MD"}, {"backtrace": 37, "fragment": "/EHsc"}, {"backtrace": 37, "fragment": "/Qopenmp"}, {"backtrace": 37, "fragment": "/Qpar"}, {"backtrace": 4, "fragment": "/wd4251"}, {"backtrace": 4, "fragment": "/wd4275"}, {"backtrace": 4, "fragment": "/utf-8"}, {"backtrace": 4, "fragment": "/Zc:preprocessor"}, {"backtrace": 4, "fragment": "/wd4201"}], "defines": [{"backtrace": 4, "define": "CAPSTONE_SHARED"}, {"backtrace": 4, "define": "FMT_SHARED"}, {"backtrace": 38, "define": "IMGUI_ENABLE_FREETYPE"}, {"backtrace": 4, "define": "PLUTOSVG_HAS_FREETYPE"}, {"backtrace": 38, "define": "PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000"}, {"backtrace": 4, "define": "SPDLOG_COMPILED_LIB"}, {"backtrace": 4, "define": "SPDLOG_FMT_EXTERNAL"}, {"backtrace": 38, "define": "SPDLOG_NO_COMPILE_TIME_FMT"}, {"backtrace": 4, "define": "SPDLOG_SHARED_LIB"}, {"backtrace": 4, "define": "TRACY_ENABLE"}, {"backtrace": 4, "define": "TRACY_IMPORTS"}, {"backtrace": 38, "define": "WIN64"}, {"backtrace": 39, "define": "WINDOWS_IGNORE_PACKING_MISMATCH"}, {"backtrace": 4, "define": "XXH_EXPORT"}, {"backtrace": 38, "define": "_AMD64_"}, {"backtrace": 38, "define": "_M_X64"}, {"backtrace": 38, "define": "_WIN64"}], "includes": [{"backtrace": 40, "path": "D:/sss/src"}, {"backtrace": 40, "path": "D:/sss/src/cache"}, {"backtrace": 40, "path": "D:/sss/src/cpu"}, {"backtrace": 40, "path": "D:/sss/src/emulator"}, {"backtrace": 40, "path": "D:/sss/src/gui/backends"}, {"backtrace": 40, "path": "D:/sss/src/gui"}, {"backtrace": 40, "path": "D:/sss/src/jit"}, {"backtrace": 40, "path": "D:/sss/src/loader"}, {"backtrace": 40, "path": "D:/sss/src/memory"}, {"backtrace": 40, "path": "D:/sss/src/ps4"}, {"backtrace": 40, "path": "D:/sss/src/common"}, {"backtrace": 40, "path": "D:/sss/src/syscall"}, {"backtrace": 40, "path": "D:/sss/src/video_core"}, {"backtrace": 40, "path": "D:/sss/build"}, {"backtrace": 40, "path": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"backtrace": 40, "path": "C:/Program Files (x86)/Intel/oneAPI/mkl/latest/include"}, {"backtrace": 41, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/include"}, {"backtrace": 40, "isSystem": true, "path": "D:/vcpkg/installed/x64-windows/include"}, {"backtrace": 4, "isSystem": true, "path": "D:/vcpkg/installed/x64-windows/include/SDL2"}, {"backtrace": 4, "isSystem": true, "path": "D:/VulkanSDK/1.4.309.0/Include"}, {"backtrace": 4, "isSystem": true, "path": "D:/vcpkg/installed/x64-windows/include/xbyak"}, {"backtrace": 4, "isSystem": true, "path": "D:/vcpkg/installed/x64-windows/include/plutosvg"}, {"backtrace": 4, "isSystem": true, "path": "D:/vcpkg/installed/x64-windows/include/plutovg"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "23"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "PS4Emulator::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "-LIBPATH:C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\DIASDK~1\\lib\\amd64", "role": "libraryPath"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\manual-link\\SDL2main.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\SDL2.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\VulkanSDK\\1.4.309.0\\Lib\\vulkan-1.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\zip.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\imgui.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\spdlog.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\portaudio.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\tbb12.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\capstone.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avdevice.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "strmiids.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "shlwapi.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "vfw32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avfilter.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avformat.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avcodec.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "strmiids.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "swresample.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "swscale.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avutil.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avdevice.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avfilter.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avformat.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avcodec.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\swresample.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\swscale.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avutil.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\Psapi.Lib", "role": "libraries"}, {"backtrace": 4, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "shlwapi.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "vfw32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "strmiids.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\TracyClient.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\xxhash.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\Zydis.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libpng16.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\glslang.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\glslang-default-resource-limits.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\SPIRV.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\SPVRemapper.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\plutosvg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "diaguids.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\VulkanSDK\\1.4.309.0\\Lib\\vulkan-1.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\fmt.lib", "role": "libraries"}, {"fragment": "winmm.lib", "role": "libraries"}, {"fragment": "dsound.lib", "role": "libraries"}, {"fragment": "setupapi.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "strmiids.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "shlwapi.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "vfw32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avfilter.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avformat.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avcodec.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "swresample.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "swscale.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "avutil.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avdevice.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avfilter.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avformat.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avcodec.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\swresample.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\swscale.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\avutil.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\Psapi.Lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "diaguids.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\pugixml.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\Zycore.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\glslang.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\plutovg.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\freetype.lib", "role": "libraries"}, {"backtrace": 36, "fragment": "D:\\vcpkg\\installed\\x64-windows\\lib\\freetype.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "PS4Emulator", "nameOnDisk": "PS4Emulator.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55]}, {"name": "", "sourceIndexes": [56, 57]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "cache/cache.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "common/lock_ordering.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "cpu/cpu_diagnostics.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "cpu/instruction_decoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "cpu/thunk_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "cpu/x86_64_cpu.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "cpu/x86_64_pipeline.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "emulator/adaptive_emulation_orchestrator.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "emulator/apic.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "emulator/interrupt_handler.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "emulator/io_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/backends/imgui_impl_sdl2.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/backends/imgui_impl_vulkan.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/game_browser.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_demo.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_draw.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_freetype.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_stdlib.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_tables.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/imgui_widgets.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/input_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/input_settings.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "gui/performance_overlay.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "jit/jit_diagnostics.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "jit/x86_64_jit_compiler.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "jit/x86_64_jit_helpers.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "loader/crypto_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "loader/elf_loader.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "loader/key_store.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "loader/pkg_installer.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "loader/self_decrypter.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/memory_compressor.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/memory_diagnostics.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/memory_prefetcher.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/physical_memory_allocator.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/ps4_mmu.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/swap_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "memory/tlb.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/fiber_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/orbis_os.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_audio.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_controllers.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_emulator.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_filesystem.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_gpu.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/ps4_tsc.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/trophy_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "ps4/zlib_wrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "syscall/syscall_handler.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "video_core/command_processor.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "video_core/gnm_shader_translator.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "video_core/gnm_state.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "video_core/shader_emulator.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "video_core/tile_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_json.natvis", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map.natvis", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}