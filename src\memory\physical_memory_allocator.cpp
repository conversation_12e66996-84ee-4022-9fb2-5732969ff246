#include "ps4_mmu.h"
#include <algorithm>
#include <chrono>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <vector>

namespace ps4 {

/**
 * @brief Constructs a PhysicalMemoryAllocator instance.
 */
PhysicalMemoryAllocator::PhysicalMemoryAllocator() {
  spdlog::info("Constructing PhysicalMemoryAllocator...");
  freeBlocks.clear();
  usedBlocks.clear();
  stats_ = AllocationStats();
  spdlog::info("PhysicalMemoryAllocator constructed with empty block lists");
}

/**
 * @brief Initializes the allocator with the total memory size.
 * @param totalSize The total size of the physical memory (bytes).
 */
void PhysicalMemoryAllocator::Initialize(uint64_t totalSize) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  try {
    spdlog::info("Initializing PhysicalMemoryAllocator with size 0x{:x}",
                 totalSize);
    freeBlocks.clear();
    usedBlocks.clear();
    stats_ = AllocationStats();

    // Reserve the first page (0x0 - 0x1000) to avoid allocating physical address 0x0
    // which is typically reserved and should never be used
    constexpr uint64_t RESERVED_START = 0x0;
    constexpr uint64_t RESERVED_SIZE = 0x1000;  // Reserve first 4KB page
    uint64_t allocatableStart = RESERVED_SIZE;
    uint64_t allocatableSize = totalSize - RESERVED_SIZE;

    if (totalSize <= RESERVED_SIZE) {
      throw std::runtime_error("Total size too small to reserve first page");
    }

    freeBlocks.push_back({allocatableStart, allocatableSize});
    auto end = std::chrono::steady_clock::now();
    stats_.latencyUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    stats_.totalLatencyUs += stats_.latencyUs;
    spdlog::info("Initialized with free block: start=0x{:x}, size=0x{:x} (reserved 0x0-0x{:x})",
                 allocatableStart, allocatableSize, RESERVED_SIZE);
  } catch (const std::exception &e) {
    spdlog::error("PhysicalMemoryAllocator initialization failed: {}",
                  e.what());
    throw;
  }
}

/**
 * @brief Allocates a block of physical memory.
 * @param size The size of the memory block (bytes).
 * @param priority Optional priority for critical allocations (0=normal,
 * 1=high).
 * @return The starting address of the allocated block, or ALLOC_FAILED if
 * allocation fails.
 */
uint64_t PhysicalMemoryAllocator::Allocate(uint64_t size, int priority) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
#ifdef DEBUG_ALLOCATOR
  spdlog::info("Allocating block of size 0x{:x}, priority={}", size, priority);
#endif

  size = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
  try {
    if (size == 0) {
#ifdef DEBUG_ALLOCATOR
      spdlog::error("Invalid allocation size: 0");
#endif
      return ALLOC_FAILED; // Return ALLOC_FAILED instead of throwing exception
    }

    // Early check: if no free blocks exist, fail immediately
    if (freeBlocks.empty()) {
      spdlog::error("PhysicalMemoryAllocator: No free blocks available for size 0x{:x}", size);
      stats_.cacheMisses++;
      return ALLOC_FAILED;
    }

    // Early check: calculate total free memory to avoid expensive search if insufficient
    uint64_t totalFreeMemory = 0;
    for (const auto& block : freeBlocks) {
      totalFreeMemory += block.size;
    }
    if (totalFreeMemory < size) {
      spdlog::error("PhysicalMemoryAllocator: Insufficient free memory: requested=0x{:x}, available=0x{:x}",
                    size, totalFreeMemory);
      stats_.cacheMisses++;
      return ALLOC_FAILED;
    }

    // Debug: Log current state of free blocks
#ifdef DEBUG_ALLOCATOR
    spdlog::info("Current free blocks count: {}", freeBlocks.size());
    for (size_t i = 0; i < freeBlocks.size(); ++i) {
      spdlog::info("  Free block {}: start=0x{:x}, size=0x{:x}", i,
                   freeBlocks[i].startAddr, freeBlocks[i].size);
    }
#endif

    // Sort free blocks by size for high-priority allocations or best fit
    if (priority > 0) {
      std::sort(freeBlocks.begin(), freeBlocks.end(),
                [](const auto &a, const auto &b) { return a.size < b.size; });
    }
    // Use best-fit strategy to reduce fragmentation
    auto it = std::min_element(freeBlocks.begin(), freeBlocks.end(),
                               [size](const PhysicalMemoryBlock &a, const PhysicalMemoryBlock &b) {
                                 return a.size >= size && b.size >= size ? a.size < b.size : a.size >= size;
                               });
    if (it == freeBlocks.end() || it->size < size) {
      stats_.cacheMisses++;
#ifdef DEBUG_ALLOCATOR
      spdlog::error("No free block found for size 0x{:x} among {} blocks", size,
                    freeBlocks.size());
#endif
      return ALLOC_FAILED; // Return ALLOC_FAILED instead of throwing exception
    }

    stats_.cacheHits++;
    uint64_t addr = it->startAddr;
    it->startAddr += size;
    it->size -= size;
    if (it->size == 0) {
      freeBlocks.erase(it);
    }

    usedBlocks.push_back({addr, size});
    stats_.totalAllocations++;
    stats_.currentAllocations++;
    stats_.peakAllocations =
        std::max(stats_.currentAllocations, stats_.peakAllocations);

    stats_.allocationCount++;
    stats_.fragmentationCount = freeBlocks.size();

    auto end = std::chrono::steady_clock::now();
    stats_.latencyUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    stats_.totalLatencyUs += stats_.latencyUs;
#ifdef DEBUG_ALLOCATOR
    spdlog::info("Allocated block: start=0x{:x}, size=0x{:x}", addr, size);
#endif
    return addr;
  } catch (const std::exception &e) {
#ifdef DEBUG_ALLOCATOR
    spdlog::error("Allocation failed for size 0x{:x}: {}", size, e.what());
#endif
    return ALLOC_FAILED; // Return ALLOC_FAILED instead of re-throwing exception
  }
}

/**
 * @brief Frees a previously allocated physical memory block.
 * @param addr The starting address of the block.
 * @param size The size of the block (bytes).
 */
void PhysicalMemoryAllocator::Free(uint64_t addr, uint64_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
#ifdef DEBUG_ALLOCATOR
  spdlog::info("Freeing block: start=0x{:x}, size=0x{:x}", addr, size);
#endif
  size = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
  try {
    if (size == 0) {
#ifdef DEBUG_ALLOCATOR
      spdlog::warn("Invalid free size: 0");
#endif
      return;
    }

    auto usedIt = std::find_if(
        usedBlocks.begin(), usedBlocks.end(), [addr, size](const auto &block) {
          return block.startAddr == addr && block.size == size;
        });
    if (usedIt == usedBlocks.end()) {
#ifdef DEBUG_ALLOCATOR
      spdlog::warn("No matching used block at 0x{:x}, size=0x{:x}", addr, size);
#endif
      return;
    }
    PhysicalMemoryBlock newBlock{addr, size};
    usedBlocks.erase(usedIt);
    stats_.totalFrees++;
    stats_.currentAllocations--;
    stats_.freeCount++;

    // Insert the new block and sort
    freeBlocks.push_back(newBlock);
    std::sort(freeBlocks.begin(), freeBlocks.end(), [](const auto &a, const auto &b) {
      return a.startAddr < b.startAddr;
    });

    // Coalesce adjacent free blocks
    for (auto it = freeBlocks.begin(); it != freeBlocks.end(); ) {
      auto nextIt = std::next(it);
      if (nextIt != freeBlocks.end() && it->startAddr + it->size == nextIt->startAddr) {
        it->size += nextIt->size; // Merge sizes
        it = freeBlocks.erase(nextIt); // Remove the next block
      } else {
        ++it;
      }
    }

    stats_.fragmentationCount = freeBlocks.size();
    auto end = std::chrono::steady_clock::now();
    stats_.latencyUs = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    stats_.totalLatencyUs += stats_.latencyUs;
#ifdef DEBUG_ALLOCATOR
    spdlog::info("Freed block: start=0x{:x}, size=0x{:x}", addr, size);
#endif
  } catch (const std::exception &e) {
#ifdef DEBUG_ALLOCATOR
    spdlog::error("Free failed for addr 0x{:x}, size 0x{:x}: {}", addr, size, e.what());
#endif
  }
}

/**
 * @brief Retrieves memory usage statistics.
 * @return A pair of used and total memory sizes (bytes).
 */
std::pair<uint64_t, uint64_t> PhysicalMemoryAllocator::GetUsageStats() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  uint64_t usedSize = 0;
  for (const auto &block : usedBlocks) {
    usedSize += block.size;
  }
  uint64_t totalSize = usedSize;
  for (const auto &block : freeBlocks) {
    totalSize += block.size;
  }
  spdlog::info("Usage stats: used=0x{:x}, total=0x{:x}, fragmentation={}",
               usedSize, totalSize, stats_.fragmentationCount);
  return {usedSize, totalSize};
}

/**
 * @brief Retrieves detailed allocation statistics.
 * @return Allocation statistics.
 */
AllocationStats PhysicalMemoryAllocator::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  // Return only the base AllocationStats part
  return stats_;
}

/**
 * @brief Resets allocation statistics.
 */
void PhysicalMemoryAllocator::ResetStats() {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  stats_ = AllocationStats();
  stats_.fragmentationCount = freeBlocks.size();
  spdlog::info("Allocation statistics reset");
}

/**
 * @brief Saves the allocator state to a stream.
 * @param out Output stream.
 */
void PhysicalMemoryAllocator::SaveState(std::ostream &out) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Save base AllocationStats
    out.write(reinterpret_cast<const char *>(&stats_), sizeof(AllocationStats));

    uint64_t freeCount = freeBlocks.size();
    out.write(reinterpret_cast<const char *>(&freeCount), sizeof(freeCount));
    for (const auto &block : freeBlocks) {
      out.write(reinterpret_cast<const char *>(&block), sizeof(block));
    }
    uint64_t usedCount = usedBlocks.size();
    out.write(reinterpret_cast<const char *>(&usedCount), sizeof(usedCount));
    for (const auto &block : usedBlocks) {
      out.write(reinterpret_cast<const char *>(&block), sizeof(block));
    }
    if (!out.good()) {
      throw std::runtime_error("Failed to write allocator state");
    }
    spdlog::info("PhysicalMemoryAllocator state saved: {} free, {} used blocks",
                 freeCount, usedCount);
  } catch (const std::exception &e) {
    spdlog::error("PhysicalMemoryAllocator SaveState failed: {}", e.what());
  }
}

/**
 * @brief Loads the allocator state from a stream.
 * @param in Input stream.
 */
void PhysicalMemoryAllocator::LoadState(std::istream &in) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported allocator state version: {}", version);
      throw std::runtime_error("Invalid allocator state version");
    }
    freeBlocks.clear();
    usedBlocks.clear();
    // Load base AllocationStats
    in.read(reinterpret_cast<char *>(&stats_), sizeof(AllocationStats));
    uint64_t freeCount;
    in.read(reinterpret_cast<char *>(&freeCount), sizeof(freeCount));
    for (uint64_t i = 0; i < freeCount && in.good(); ++i) {
      PhysicalMemoryBlock block;
      in.read(reinterpret_cast<char *>(&block), sizeof(block));
      freeBlocks.push_back(block);
    }
    uint64_t usedCount;
    in.read(reinterpret_cast<char *>(&usedCount), sizeof(usedCount));
    for (uint64_t i = 0; i < usedCount && in.good(); ++i) {
      PhysicalMemoryBlock block;
      in.read(reinterpret_cast<char *>(&block), sizeof(block));
      usedBlocks.push_back(block);
    }
    if (!in.good()) {
      throw std::runtime_error("Failed to read allocator state");
    }
    stats_.fragmentationCount = freeBlocks.size();
    spdlog::info(
        "PhysicalMemoryAllocator state loaded: {} free, {} used blocks",
        freeCount, usedCount);
  } catch (const std::exception &e) {
    spdlog::error("PhysicalMemoryAllocator LoadState failed: {}", e.what());
    freeBlocks.clear();
    usedBlocks.clear();
    stats_ = AllocationStats();
  }
}

} // namespace ps4