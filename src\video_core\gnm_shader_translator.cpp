#include "gnm_shader_translator.h"
#include <chrono>
#include <iomanip>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>
#include <string>
#include <vector>
#include <fmt/format.h>

namespace ps4 {

GNMShaderTranslator::GNMShaderTranslator() : m_stats() {
  spdlog::info("GNMShaderTranslator constructed");
}

GNMShaderTranslator::~GNMShaderTranslator() {
  Shutdown();
  spdlog::info("GNMShaderTranslator destroyed");
}

bool GNMShaderTranslator::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    InitializeOpcodeTables();
    m_stats = ShaderTranslatorStats();
    m_shaderCache.clear();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMShaderTranslator initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMShaderTranslator initialization failed: {}", e.what());
    throw ShaderTranslatorException("Initialization failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_scalarOpcodes.clear();
    m_vectorOpcodes.clear();
    m_memoryOpcodes.clear();
    m_flowControlOpcodes.clear();
    m_textureOpcodes.clear();
    m_exportOpcodes.clear();
    m_shaderCache.clear();
    m_stats = ShaderTranslatorStats();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMShaderTranslator shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMShaderTranslator shutdown failed: {}", e.what());
  }
}

std::string
GNMShaderTranslator::Disassemble(const std::vector<uint32_t> &bytecode,
                                 GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn("Disassemble: Empty shader bytecode");
      return "Empty shader bytecode";
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("Disassemble: Failed to parse shader");
      return "Failed to parse shader";
    }

    lock.unlock(); // Release mutex during string building
    std::stringstream ss;
    ss << "Shader Type: ";
    switch (type) {
    case GCNShaderType::VERTEX:
      ss << "Vertex";
      break;
    case GCNShaderType::PIXEL:
      ss << "Pixel";
      break;
    case GCNShaderType::GEOMETRY:
      ss << "Geometry";
      break;
    case GCNShaderType::COMPUTE:
      ss << "Compute";
      break;
    case GCNShaderType::HULL:
      ss << "Hull";
      break;
    case GCNShaderType::DOMAIN_SHADER:
      ss << "Domain";
      break;
    default:
      ss << "Unknown";
      break;
    }
    ss << "\nInstruction Count: " << state.instructions.size()
       << "\nBytecode Size: " << bytecode.size() << " dwords\n\n";

    for (size_t i = 0; i < state.instructions.size(); ++i) {
      uint32_t address = static_cast<uint32_t>(i * 4);
      auto labelIt = state.labelMap.find(address);
      if (labelIt != state.labelMap.end()) {
        ss << labelIt->second << ":\n";
      }
      ss << "    " << std::setw(8) << std::setfill('0') << std::hex << address
         << ": " << DisassembleInstruction(state.instructions[i], address)
         << "\n";
    }

    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("Disassemble: type={}, instructions={}, latency={}us",
                 static_cast<int>(type), state.instructions.size(), latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("Disassemble failed: {}", e.what());
    throw ShaderTranslatorException("Disassemble failed: " +
                                    std::string(e.what()));
  }
}

std::vector<uint32_t>
GNMShaderTranslator::TranslateToSPIRV(const std::vector<uint32_t> &bytecode,
                                      GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty shader bytecode");
    }

    uint64_t bytecodeHash = 0;
    for (const auto &word : bytecode) {
      bytecodeHash ^= std::hash<uint32_t>{}(word);
    }
    std::vector<uint32_t> spirvCode;
    std::string glslCode;
    if (GetCachedShader(bytecodeHash, type, spirvCode, glslCode)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "TranslateToSPIRV: Cached shader, type={}, hash=0x{:x}, latency={}us",
          static_cast<int>(type), bytecodeHash, latency);
      return spirvCode;
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Shader parsing failed");
    }

    lock.unlock(); // Release mutex during translation
    spirvCode.clear();
    spirvCode.push_back(0x07230203); // SPIR-V magic number
    spirvCode.push_back(0x00010300); // SPIR-V version 1.3
    spirvCode.push_back(0);          // Generator ID
    spirvCode.push_back(0);          // Bound (to be updated)
    spirvCode.push_back(0);          // Instruction schema

    uint32_t id = 1;
    spirvCode.push_back((4 << 16) | 1); // OpCapability Shader
    spirvCode.push_back(id++);          // Capability ID
    spirvCode.push_back((2 << 16) | 2); // OpExtInstImport "GLSL.std.450"
    spirvCode.push_back(id++);
    spirvCode.push_back('G' | ('L' << 8) | ('S' << 16) | ('L' << 24));

    // Declare type IDs at function scope
    uint32_t voidType = id++;
    uint32_t floatType = id++;
    uint32_t intType = id++;
    uint32_t vec4Type = id++;
    uint32_t vec4ArrayType = id++;
    uint32_t intArrayType = id++;
    uint32_t functionType = id++;
    uint32_t mainFunction = id++;
    uint32_t entryPoint = id++;

    // OpTypeVoid %void
    spirvCode.push_back((2 << 16) | 0x0013); // OpTypeVoid
    spirvCode.push_back(voidType);

    // OpTypeFloat %float 32
    spirvCode.push_back((3 << 16) | 0x0016); // OpTypeFloat
    spirvCode.push_back(floatType);
    spirvCode.push_back(32);

    // OpTypeInt %int 32 1
    spirvCode.push_back((4 << 16) | 0x0015); // OpTypeInt
    spirvCode.push_back(intType);
    spirvCode.push_back(32);
    spirvCode.push_back(1);

    // OpTypeVector %vec4 %float 4
    spirvCode.push_back((4 << 16) | 0x0017); // OpTypeVector
    spirvCode.push_back(vec4Type);
    spirvCode.push_back(floatType);
    spirvCode.push_back(4);

    // Map GCN instructions to SPIR-V
    m_stats.instructionsCovered = 0;
    m_stats.instructionsSkipped = 0;

    for (const auto &instr : state.instructions) {
      EnhancedGCNInstruction enhancedInstr;
      enhancedInstr.opcode = instr.opcode;
      enhancedInstr.operands = {instr.dst, instr.src0, instr.src1, instr.src2, instr.imm};
      enhancedInstr.hasDestination = (instr.dst != 0);
      enhancedInstr.destinationMask = 0xF; // Default full mask
      enhancedInstr.isControlFlow = ((instr.opcode & 0xF0) == 0xA0);
      enhancedInstr.isMemoryAccess = ((instr.opcode & 0xF0) == 0x40 || (instr.opcode & 0xF0) == 0x50 || instr.opcode == 0xA6 || instr.opcode == 0xA7);
      enhancedInstr.cycleCount = EstimateInstructionCycles(enhancedInstr);

      switch (instr.opcode) {
      // Scalar ALU Instructions
      case 0xBE: // S_MOV_B32
        enhancedInstr.type = GCNInstructionType::S_MOV;
        spirvCode.push_back((4 << 16) | 0x003d); // OpCopyObject
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x80: // S_ADD_U32
        enhancedInstr.type = GCNInstructionType::S_ADD;
        spirvCode.push_back((5 << 16) | 0x0064); // OpIAdd
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x81: // S_SUB_U32
        enhancedInstr.type = GCNInstructionType::S_SUB;
        spirvCode.push_back((5 << 16) | 0x0065); // OpISub
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x82: // S_MUL_I32
        enhancedInstr.type = GCNInstructionType::S_MUL;
        spirvCode.push_back((5 << 16) | 0x0066); // OpIMul
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x83: // S_AND_B32
        enhancedInstr.type = GCNInstructionType::S_AND;
        spirvCode.push_back((5 << 16) | 0x007f); // OpBitwiseAnd
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x84: // S_OR_B32
        enhancedInstr.type = GCNInstructionType::S_OR;
        spirvCode.push_back((5 << 16) | 0x0080); // OpBitwiseOr
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x85: // S_XOR_B32
        enhancedInstr.type = GCNInstructionType::S_XOR;
        spirvCode.push_back((5 << 16) | 0x0081); // OpBitwiseXor
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x86: // S_NOT_B32
        enhancedInstr.type = GCNInstructionType::S_NOT;
        spirvCode.push_back((4 << 16) | 0x007e); // OpNot
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x87: // S_LSHL_B32
        enhancedInstr.type = GCNInstructionType::S_LSHL;
        spirvCode.push_back((5 << 16) | 0x0089); // OpShiftLeftLogical
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x88: // S_LSHR_B32
        enhancedInstr.type = GCNInstructionType::S_LSHR;
        spirvCode.push_back((5 << 16) | 0x008a); // OpShiftRightLogical
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x89: // S_ASHR_I32
        enhancedInstr.type = GCNInstructionType::S_ASHR;
        spirvCode.push_back((5 << 16) | 0x008b); // OpShiftRightArithmetic
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x90: // S_CMP_EQ_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        spirvCode.push_back((5 << 16) | 0x00ac); // OpIEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x91: // S_CMP_LT_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        spirvCode.push_back((5 << 16) | 0x00af); // OpSLessThan
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x92: // S_CMP_LE_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        spirvCode.push_back((5 << 16) | 0x00b1); // OpSLessThanEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x93: // S_CMP_GT_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        spirvCode.push_back((5 << 16) | 0x00b0); // OpSGreaterThan
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x94: // S_CMP_GE_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        spirvCode.push_back((5 << 16) | 0x00b2); // OpSGreaterThanEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      // Vector ALU Instructions
      case 0x01: // V_MOV_B32
        enhancedInstr.type = GCNInstructionType::V_MOV;
        spirvCode.push_back((4 << 16) | 0x003d); // OpCopyObject
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x02: // V_ADD_F32
        enhancedInstr.type = GCNInstructionType::V_ADD_F32;
        spirvCode.push_back((5 << 16) | 0x0081); // OpFAdd
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x03: // V_SUB_F32
        enhancedInstr.type = GCNInstructionType::V_SUB_F32;
        spirvCode.push_back((5 << 16) | 0x0082); // OpFSub
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x04: // V_MUL_F32
        enhancedInstr.type = GCNInstructionType::V_MUL_F32;
        spirvCode.push_back((5 << 16) | 0x0085); // OpFMul
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x05: // V_MAD_F32 / V_FMA_F32
        enhancedInstr.type = GCNInstructionType::V_FMA_F32;
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst (FMA)
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(1); // GLSL.std.450
        spirvCode.push_back(50); // Fma
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x06: // V_DIV_F32
        enhancedInstr.type = GCNInstructionType::V_DIV_F32;
        spirvCode.push_back((5 << 16) | 0x0086); // OpFDiv
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x07: // V_MIN_F32
        enhancedInstr.type = GCNInstructionType::V_MIN_F32;
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(1); // GLSL.std.450
        spirvCode.push_back(37); // FMin
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x08: // V_MAX_F32
        enhancedInstr.type = GCNInstructionType::V_MAX_F32;
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(1); // GLSL.std.450
        spirvCode.push_back(40); // FMax
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x09: // V_ADD_I32
        enhancedInstr.type = GCNInstructionType::V_ADD_I32;
        spirvCode.push_back((5 << 16) | 0x0064); // OpIAdd
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0A: // V_SUB_I32
        enhancedInstr.type = GCNInstructionType::V_SUB_I32;
        spirvCode.push_back((5 << 16) | 0x0065); // OpISub
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0B: // V_MUL_I32
        enhancedInstr.type = GCNInstructionType::V_MUL_I32;
        spirvCode.push_back((5 << 16) | 0x0066); // OpIMul
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0C: // V_AND_B32
        enhancedInstr.type = GCNInstructionType::V_AND_B32;
        spirvCode.push_back((5 << 16) | 0x007f); // OpBitwiseAnd
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0D: // V_OR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        spirvCode.push_back((5 << 16) | 0x0080); // OpBitwiseOr
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0E: // V_XOR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR for simplicity
        spirvCode.push_back((5 << 16) | 0x0081); // OpBitwiseXor
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x0F: // V_NOT_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((4 << 16) | 0x007e); // OpNot
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x10: // V_LSHL_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x0089); // OpShiftLeftLogical
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x11: // V_LSHR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x008a); // OpShiftRightLogical
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x12: // V_ASHR_I32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x008b); // OpShiftRightArithmetic
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      // Memory Instructions
      case 0xA0: // BUFFER_LOAD
        enhancedInstr.type = GCNInstructionType::BUFFER_LOAD;
        spirvCode.push_back((4 << 16) | 0x003f); // OpLoad
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA1: // BUFFER_STORE
        enhancedInstr.type = GCNInstructionType::BUFFER_STORE;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA2: // IMAGE_LOAD
        enhancedInstr.type = GCNInstructionType::IMAGE_LOAD;
        spirvCode.push_back((5 << 16) | 0x0063); // OpImageRead
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA3: // IMAGE_STORE
        enhancedInstr.type = GCNInstructionType::IMAGE_STORE;
        spirvCode.push_back((4 << 16) | 0x0064); // OpImageWrite
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA4: // DS_READ
        enhancedInstr.type = GCNInstructionType::DS_READ;
        spirvCode.push_back((4 << 16) | 0x003f); // OpLoad (LDS)
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA5: // DS_WRITE
        enhancedInstr.type = GCNInstructionType::DS_WRITE;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore (LDS)
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA6: // FLAT_LOAD
        enhancedInstr.type = GCNInstructionType::FLAT_LOAD;
        spirvCode.push_back((4 << 16) | 0x003f); // OpLoad
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA7: // FLAT_STORE
        enhancedInstr.type = GCNInstructionType::FLAT_STORE;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      // Control Flow Instructions
      case 0xA8: // S_BRANCH
        enhancedInstr.type = GCNInstructionType::S_BRANCH;
        spirvCode.push_back((2 << 16) | 0x00f9); // OpBranch
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xA9: // S_CBRANCH
        enhancedInstr.type = GCNInstructionType::S_CBRANCH;
        spirvCode.push_back((4 << 16) | 0x00fa); // OpBranchConditional
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xAA: // S_WAITCNT
        enhancedInstr.type = GCNInstructionType::S_WAITCNT;
        spirvCode.push_back((1 << 16) | 0x00e0); // OpMemoryBarrier
        spirvCode.push_back(2); // Device scope
        spirvCode.push_back(72); // Memory semantics
        m_stats.instructionsCovered++;
        break;
      case 0xAB: // S_BARRIER
        enhancedInstr.type = GCNInstructionType::S_BARRIER;
        spirvCode.push_back((1 << 16) | 0x00e2); // OpControlBarrier
        spirvCode.push_back(2); // Execution scope
        spirvCode.push_back(2); // Memory scope
        spirvCode.push_back(72); // Memory semantics
        m_stats.instructionsCovered++;
        break;
      case 0xAC: // S_ENDPGM
        enhancedInstr.type = GCNInstructionType::S_ENDPGM;
        spirvCode.push_back((1 << 16) | 0x00fd); // OpReturn
        m_stats.instructionsCovered++;
        break;
      case 0xAD: // S_SENDMSG
        enhancedInstr.type = GCNInstructionType::S_SENDMSG;
        spirvCode.push_back((1 << 16) | 0x00e0); // OpMemoryBarrier (placeholder)
        m_stats.instructionsCovered++;
        break;

      // Vector Comparison Instructions
      case 0x20: // V_CMP_EQ_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b4); // OpFOrdEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x21: // V_CMP_NE_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b7); // OpFOrdNotEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x22: // V_CMP_GT_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b8); // OpFOrdGreaterThan
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x23: // V_CMP_LT_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b6); // OpFOrdLessThan
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x24: // V_CMP_GE_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b9); // OpFOrdGreaterThanEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0x25: // V_CMP_LE_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32; // Note: Reusing OR
        spirvCode.push_back((5 << 16) | 0x00b5); // OpFOrdLessThanEqual
        spirvCode.push_back(intType);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      // Texture Instructions
      case 0xB0: // IMAGE_SAMPLE
        enhancedInstr.type = GCNInstructionType::IMAGE_SAMPLE;
        spirvCode.push_back((5 << 16) | 0x0057); // OpImageSampleImplicitLod
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xB1: // IMAGE_GATHER
        enhancedInstr.type = GCNInstructionType::IMAGE_GATHER;
        spirvCode.push_back((6 << 16) | 0x005b); // OpImageGather
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xB2: // IMAGE_GET_RESINFO
        enhancedInstr.type = GCNInstructionType::IMAGE_GET_RESINFO;
        spirvCode.push_back((4 << 16) | 0x0056); // OpImageQuerySize
        spirvCode.push_back(vec4Type);
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      // Export Instructions
      case 0xC0: // EXP_POS
        enhancedInstr.type = GCNInstructionType::EXP_POS;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xC1: // EXP_PARAM
        enhancedInstr.type = GCNInstructionType::EXP_PARAM;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;
      case 0xC2: // EXP_MRT
        enhancedInstr.type = GCNInstructionType::EXP_MRT;
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);
        spirvCode.push_back(id++);
        m_stats.instructionsCovered++;
        break;

      default:
        enhancedInstr.type = GCNInstructionType::UNKNOWN;
        spdlog::debug("TranslateToSPIRV: Unhandled opcode 0x{:x}", instr.opcode);
        m_stats.instructionsSkipped++;
        continue;
      }

      // Translate using enhanced instruction
      std::string spirvStr, glslStr;
      if (!TranslateGCNInstruction(enhancedInstr, spirvStr, glslStr)) {
        spdlog::warn("Failed to translate enhanced instruction: opcode=0x{:x}", instr.opcode);
      }
    }

    // Add function end
    spirvCode.push_back((1 << 16) | 0x00fd); // OpReturn
    spirvCode.push_back((1 << 16) | 0x0038); // OpFunctionEnd

    // Update bound field
    spirvCode[3] = id;

    ShaderCacheEntry cacheEntry{spirvCode, "", type};
    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_shaderCache[bytecodeHash] = cacheEntry;
    m_stats.operationCount++;
    m_stats.spirvGenerations++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("TranslateToSPIRV: Enhanced shader translation, type={}, "
                 "instructions={}, spirv_size={} words, covered={}, "
                 "skipped={}, latency={}us",
                 static_cast<int>(type), state.instructions.size(),
                 spirvCode.size(), m_stats.instructionsCovered,
                 m_stats.instructionsSkipped, latency);
    if (m_spirvCallback) {
      m_spirvCallback(type, bytecodeHash, spirvCode);
    }
    return spirvCode;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TranslateToSPIRV failed: {}", e.what());
    throw ShaderTranslatorException("TranslateToSPIRV failed: " +
                                    std::string(e.what()));
  }
}

std::string
GNMShaderTranslator::TranslatetoGLSL(const std::vector<uint32_t> &bytecode,
                                     GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty shader bytecode");
    }

    uint64_t bytecodeHash = 0;
    for (const auto &word : bytecode) {
      bytecodeHash ^= std::hash<uint32_t>{}(word);
    }
    std::vector<uint32_t> spirvCode;
    std::string glslCode;
    if (GetCachedShader(bytecodeHash, type, spirvCode, glslCode)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "TranslatetoGLSL: Cached shader, type={}, hash=0x{:x}, latency={}us",
          static_cast<int>(type), bytecodeHash, latency);
      return glslCode;
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Shader parsing failed");
    }

    lock.unlock(); // Release mutex during string building
    std::stringstream ss;
    ss << "#version 450\n";
    ss << "#extension GL_ARB_separate_shader_objects : enable\n";
    switch (type) {
    case GCNShaderType::VERTEX:
      ss << "layout(location = 0) in vec4 inPosition;\n"
         << "layout(location = 0) out vec4 outColor;\n"
         << "void main() {\n"
         << "    gl_Position = inPosition;\n"
         << "    outColor = vec4(1.0);\n";
      break;
    case GCNShaderType::PIXEL:
      ss << "layout(location = 0) in vec4 inColor;\n"
         << "layout(location = 0) out vec4 fragColor;\n"
         << "void main() {\n"
         << "    fragColor = inColor;\n";
      break;
    case GCNShaderType::GEOMETRY:
      ss << "layout(triangles) in;\n"
         << "layout(triangle_strip, max_vertices = 3) out;\n"
         << "void main() {\n"
         << "    for (int i = 0; i < 3; i++) {\n"
         << "        gl_Position = gl_in[i].gl_Position;\n"
         << "        EmitVertex();\n"
         << "    }\n"
         << "    EndPrimitive();\n"
         << "}\n";
      break;
    case GCNShaderType::COMPUTE:
      ss << "layout(local_size_x = 1, local_size_y = 1, local_size_z = 1) in;\n"
         << "void main() {\n";
      break;
    case GCNShaderType::HULL:
      ss << "layout(vertices = 3) out;\n"
         << "void main() {\n"
         << "    gl_TessLevelOuter[0] = 1.0;\n"
         << "    gl_TessLevelOuter[1] = 1.0;\n"
         << "    gl_TessLevelOuter[2] = 1.0;\n"
         << "    gl_TessLevelInner[0] = 1.0;\n"
         << "    gl_out[gl_InvocationID].gl_Position = "
            "gl_in[gl_InvocationID].gl_Position;\n"
         << "}\n";
      break;
    case GCNShaderType::DOMAIN_SHADER:
      ss << "layout(triangles, equal_spacing, cw) in;\n"
         << "void main() {\n"
         << "    gl_Position = gl_in[0].gl_Position;\n";
      break;
    default:
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Unsupported shader type: " +
                                      std::to_string(static_cast<int>(type)));
    }
    ss << "    // Scalar registers\n";
    ss << "    uint s[256];\n";
    ss << "    // Vector registers\n";
    ss << "    vec4 v[256];\n";
    ss << "    // Texture and buffer bindings\n";
    ss << "    layout(binding = 0) uniform sampler2D image_texture;\n";
    ss << "    layout(binding = 1) uniform samplerBuffer buffer_texture;\n";
    ss << "    layout(binding = 2, rgba32f) uniform imageBuffer buffer_image;\n";
    ss << "    layout(binding = 3) uniform samplerBuffer local_memory;\n\n";

    for (const auto &instr : state.instructions) {
      EnhancedGCNInstruction enhancedInstr;
      enhancedInstr.opcode = instr.opcode;
      enhancedInstr.operands = {instr.dst, instr.src0, instr.src1, instr.src2, instr.imm};
      enhancedInstr.hasDestination = (instr.dst != 0);
      enhancedInstr.destinationMask = 0xF;
      enhancedInstr.isControlFlow = ((instr.opcode & 0xF0) == 0xA0);
      enhancedInstr.isMemoryAccess = ((instr.opcode & 0xF0) == 0x40 || (instr.opcode & 0xF0) == 0x50 || instr.opcode == 0xA6 || instr.opcode == 0xA7);
      enhancedInstr.cycleCount = EstimateInstructionCycles(enhancedInstr);

      switch (instr.opcode) {
      // Scalar ALU Instructions
      case 0xBE: // S_MOV_B32
        enhancedInstr.type = GCNInstructionType::S_MOV;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x80: // S_ADD_U32
        enhancedInstr.type = GCNInstructionType::S_ADD;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] + s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x81: // S_SUB_U32
        enhancedInstr.type = GCNInstructionType::S_SUB;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] - s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x82: // S_MUL_I32
        enhancedInstr.type = GCNInstructionType::S_MUL;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] * s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x83: // S_AND_B32
        enhancedInstr.type = GCNInstructionType::S_AND;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] & s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x84: // S_OR_B32
        enhancedInstr.type = GCNInstructionType::S_OR;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] | s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x85: // S_XOR_B32
        enhancedInstr.type = GCNInstructionType::S_XOR;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] ^ s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x86: // S_NOT_B32
        enhancedInstr.type = GCNInstructionType::S_NOT;
        ss << "    s[" << instr.dst << "] = ~s[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x87: // S_LSHL_B32
        enhancedInstr.type = GCNInstructionType::S_LSHL;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] << s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x88: // S_LSHR_B32
        enhancedInstr.type = GCNInstructionType::S_LSHR;
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] >> s[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x89: // S_ASHR_I32
        enhancedInstr.type = GCNInstructionType::S_ASHR;
        ss << "    s[" << instr.dst << "] = int(s[" << instr.src0 << "]) >> int(s[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x90: // S_CMP_EQ_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        ss << "    s[" << instr.dst << "] = (s[" << instr.src0 << "] == s[" << instr.src1 << "]) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x91: // S_CMP_LT_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0 << "]) < int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x92: // S_CMP_LE_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0 << "]) <= int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x93: // S_CMP_GT_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0 << "]) > int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x94: // S_CMP_GE_I32
        enhancedInstr.type = GCNInstructionType::S_CMP;
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0 << "]) >= int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;

      // Vector ALU Instructions
      case 0x01: // V_MOV_B32
        enhancedInstr.type = GCNInstructionType::V_MOV;
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x02: // V_ADD_F32
        enhancedInstr.type = GCNInstructionType::V_ADD_F32;
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] + v[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x03: // V_SUB_F32
        enhancedInstr.type = GCNInstructionType::V_SUB_F32;
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] - v[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x04: // V_MUL_F32
        enhancedInstr.type = GCNInstructionType::V_MUL_F32;
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] * v[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x05: // V_MAD_F32
        enhancedInstr.type = GCNInstructionType::V_FMA_F32;
        ss << "    v[" << instr.dst << "] = fma(v[" << instr.src0 << "], v[" << instr.src1 << "], v[" << instr.src2 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x06: // V_DIV_F32
        enhancedInstr.type = GCNInstructionType::V_DIV_F32;
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] / v[" << instr.src1 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0x07: // V_MIN_F32
        enhancedInstr.type = GCNInstructionType::V_MIN_F32;
        ss << "    v[" << instr.dst << "] = min(v[" << instr.src0 << "], v[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x08: // V_MAX_F32
        enhancedInstr.type = GCNInstructionType::V_MAX_F32;
        ss << "    v[" << instr.dst << "] = max(v[" << instr.src0 << "], v[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x09: // V_ADD_I32
        enhancedInstr.type = GCNInstructionType::V_ADD_I32;
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0 << "]) + ivec4(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0A: // V_SUB_I32
        enhancedInstr.type = GCNInstructionType::V_SUB_I32;
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0 << "]) - ivec4(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0B: // V_MUL_I32
        enhancedInstr.type = GCNInstructionType::V_MUL_I32;
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0 << "]) * ivec4(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0C: // V_AND_B32
        enhancedInstr.type = GCNInstructionType::V_AND_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v[" << instr.src0 << "]) & floatBitsToUint(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0D: // V_OR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v[" << instr.src0 << "]) | floatBitsToUint(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0E: // V_XOR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v[" << instr.src0 << "]) ^ floatBitsToUint(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x0F: // V_NOT_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(~floatBitsToUint(v[" << instr.src0 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x10: // V_LSHL_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v[" << instr.src0 << "]) << floatBitsToUint(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x11: // V_LSHR_B32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v[" << instr.src0 << "]) >> floatBitsToUint(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x12: // V_ASHR_I32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    v[" << instr.dst << "] = intBitsToFloat(floatBitsToInt(v[" << instr.src0 << "]) >> floatBitsToInt(v[" << instr.src1 << "]));\n";
        m_stats.instructionsCovered++;
        break;

      // Vector Comparison Instructions
      case 0x20: // V_CMP_EQ_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    s[" << instr.dst << "] = (v[" << instr.src0 << "] == v[" << instr.src1 << "]) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x21: // V_CMP_LT_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    s[" << instr.dst << "] = any(lessThan(v[" << instr.src0 << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x22: // V_CMP_LE_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    s[" << instr.dst << "] = any(lessThanEqual(v[" << instr.src0 << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x23: // V_CMP_GT_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    s[" << instr.dst << "] = any(greaterThan(v[" << instr.src0 << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;
      case 0x24: // V_CMP_GE_F32
        enhancedInstr.type = GCNInstructionType::V_OR_B32;
        ss << "    s[" << instr.dst << "] = any(greaterThanEqual(v[" << instr.src0 << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        m_stats.instructionsCovered++;
        break;

      // Memory Instructions
      case 0x40: // BUFFER_LOAD_DWORD
        enhancedInstr.type = GCNInstructionType::BUFFER_LOAD;
        ss << "    v[" << instr.dst << "] = texelFetch(buffer_texture, int(s[" << instr.src0 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x41: // BUFFER_STORE_DWORD
        enhancedInstr.type = GCNInstructionType::BUFFER_STORE;
        ss << "    imageStore(buffer_image, int(s[" << instr.src0 << "]), v[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x42: // BUFFER_LOAD_DWORDX2
        enhancedInstr.type = GCNInstructionType::BUFFER_LOAD;
        ss << "    {\n";
        ss << "        vec4 data = texelFetch(buffer_texture, int(s[" << instr.src0 << "]));\n";
        ss << "        v[" << instr.dst << "] = data;\n";
        ss << "        v[" << (instr.dst + 1) << "] = texelFetch(buffer_texture, int(s[" << instr.src0 << "]) + 1);\n";
        ss << "    }\n";
        m_stats.instructionsCovered++;
        break;
      case 0x43: // BUFFER_STORE_DWORDX2
        enhancedInstr.type = GCNInstructionType::BUFFER_STORE;
        ss << "    imageStore(buffer_image, int(s[" << instr.src0 << "]), v[" << instr.src1 << "]);\n";
        ss << "    imageStore(buffer_image, int(s[" << instr.src0 << "]) + 1, v[" << (instr.src1 + 1) << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0x44: // BUFFER_LOAD_DWORDX4
        enhancedInstr.type = GCNInstructionType::BUFFER_LOAD;
        ss << "    {\n";
        ss << "        for (int i = 0; i < 4; i++) {\n";
        ss << "            v[" << instr.dst << " + i] = texelFetch(buffer_texture, int(s[" << instr.src0 << "]) + i);\n";
        ss << "        }\n";
        ss << "    }\n";
        m_stats.instructionsCovered++;
        break;
      case 0x45: // BUFFER_STORE_DWORDX4
        enhancedInstr.type = GCNInstructionType::BUFFER_STORE;
        ss << "    for (int i = 0; i < 4; i++) {\n";
        ss << "        imageStore(buffer_image, int(s[" << instr.src0 << "]) + i, v[" << instr.src1 << " + i]);\n";
        ss << "    }\n";
        m_stats.instructionsCovered++;
        break;
      case 0x50: // DS_READ_B32
        enhancedInstr.type = GCNInstructionType::DS_READ;
        ss << "    v[" << instr.dst << "] = texelFetch(local_memory, int(s[" << instr.src0 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0x51: // DS_WRITE_B32
        enhancedInstr.type = GCNInstructionType::DS_WRITE;
        ss << "    imageStore(local_memory, int(s[" << instr.src0 << "]), v[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;
      case 0xA6: // FLAT_LOAD
        enhancedInstr.type = GCNInstructionType::FLAT_LOAD;
        ss << "    v[" << instr.dst << "] = texelFetch(flat_memory, int(s[" << instr.src0 << "]));\n";
        m_stats.instructionsCovered++;
        break;
      case 0xA7: // FLAT_STORE
        enhancedInstr.type = GCNInstructionType::FLAT_STORE;
        ss << "    imageStore(flat_memory, int(s[" << instr.src0 << "]), v[" << instr.src1 << "]);\n";
        m_stats.instructionsCovered++;
        break;

      // Control Flow Instructions
      case 0xA8: // S_BRANCH
        enhancedInstr.type = GCNInstructionType::S_BRANCH;
        ss << "    // Unconditional branch to address 0x" << std::hex << instr.imm << std::dec << "\n";
        m_stats.instructionsCovered++;
        break;
      case 0xA9: // S_CBRANCH
        enhancedInstr.type = GCNInstructionType::S_CBRANCH;
        ss << "    if (s[" << instr.src0 << "] != 0u) {\n";
        ss << "        // Conditional branch to address 0x" << std::hex << instr.imm << std::dec << "\n";
        ss << "    }\n";
        m_stats.instructionsCovered++;
        break;
      case 0xAA: // S_WAITCNT
        enhancedInstr.type = GCNInstructionType::S_WAITCNT;
        ss << "    memoryBarrier();\n";
        m_stats.instructionsCovered++;
        break;
      case 0xAB: // S_BARRIER
        enhancedInstr.type = GCNInstructionType::S_BARRIER;
        ss << "    barrier();\n";
        m_stats.instructionsCovered++;
        break;
      case 0xAC: // S_ENDPGM
        enhancedInstr.type = GCNInstructionType::S_ENDPGM;
        ss << "    return;\n";
        m_stats.instructionsCovered++;
        break;
      case 0xAD: // S_SENDMSG
        enhancedInstr.type = GCNInstructionType::S_SENDMSG;
        ss << "    // Send message (unimplemented)\n";
        m_stats.instructionsCovered++;
        break;

      // Texture Instructions
      case 0xB0: // IMAGE_SAMPLE
        enhancedInstr.type = GCNInstructionType::IMAGE_SAMPLE;
        ss << "    v[" << instr.dst << "] = texture(image_texture, vec2(v[" << instr.src0 << "].xy));\n";
        m_stats.instructionsCovered++;
        break;
      case 0xB1: // IMAGE_GATHER
        enhancedInstr.type = GCNInstructionType::IMAGE_GATHER;
        ss << "    v[" << instr.dst << "] = textureGather(image_texture, vec2(v[" << instr.src0 << "].xy));\n";
        m_stats.instructionsCovered++;
        break;
      case 0xB2: // IMAGE_GET_RESINFO
        enhancedInstr.type = GCNInstructionType::IMAGE_GET_RESINFO;
        ss << "    v[" << instr.dst << "] = vec4(ivec2(textureSize(image_texture, 0)), 0.0, 0.0);\n";
        m_stats.instructionsCovered++;
        break;

      // Export Instructions
      case 0xC0: // EXP_POS
        enhancedInstr.type = GCNInstructionType::EXP_POS;
        ss << "    gl_Position = v[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0xC1: // EXP_PARAM
        enhancedInstr.type = GCNInstructionType::EXP_PARAM;
        ss << "    out_param_" << instr.dst << " = v[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;
      case 0xC2: // EXP_MRT
        enhancedInstr.type = GCNInstructionType::EXP_MRT;
        ss << "    fragColor = v[" << instr.src0 << "];\n";
        m_stats.instructionsCovered++;
        break;

      default:
        enhancedInstr.type = GCNInstructionType::UNKNOWN;
        ss << "    // Unhandled opcode: 0x" << std::hex << instr.opcode << std::dec << "\n";
        m_stats.instructionsSkipped++;
        break;
      }

      // Translate using enhanced instruction
      std::string spirvStr, glslStr;
      if (!TranslateGCNInstruction(enhancedInstr, spirvStr, glslStr)) {
        spdlog::warn("Failed to translate enhanced instruction: opcode=0x{:x}", instr.opcode);
      }
    }

    if (type != GCNShaderType::GEOMETRY) {
      ss << "}\n";
    }

    glslCode = ss.str();
    ShaderCacheEntry cacheEntry{{}, glslCode, type};
    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_shaderCache[bytecodeHash] = cacheEntry;
    m_stats.operationCount++;
    m_stats.glslGenerations++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("TranslatetoGLSL: Translated shader, type={}, instructions={}, "
                 "covered={}, skipped={}, latency={}us",
                 static_cast<int>(type), state.instructions.size(),
                 m_stats.instructionsCovered, m_stats.instructionsSkipped, latency);
    if (m_glslCallback) {
      m_glslCallback(type, bytecodeHash, glslCode);
    }
    return glslCode;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TranslatetoGLSL failed: {}", e.what());
    throw ShaderTranslatorException("TranslatetoGLSL failed: " +
                                    std::string(e.what()));
  }
}

bool GNMShaderTranslator::Translate(const std::vector<uint32_t> &spirv,
                                    std::vector<uint32_t> &gnmBinary) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (spirv.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty SPIR-V input");
    }

    gnmBinary.clear();
    size_t i = 0;

    // Validate SPIR-V header
    if (i + 5 > spirv.size() || spirv[0] != 0x07230203) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Invalid SPIR-V header");
    }
    i += 5; // Skip header

    // Define type IDs that would be found in SPIR-V
    uint32_t vec4Type = 4; // Assume vec4 type has ID 4

    while (i < spirv.size()) {
      uint32_t word = spirv[i];
      uint32_t wordCount = word >> 16;
      uint32_t opcode = word & 0xFFFF;

      if (i + wordCount > spirv.size()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        throw ShaderTranslatorException("Incomplete SPIR-V instruction");
      }

      GCNInstruction instr;
      bool translated = false;

      // Scalar ALU Instructions
      if (opcode == 0x003d && wordCount >= 4) { // OpCopyObject
        instr.opcode = 0xBE; // S_MOV_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
                spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x008b && wordCount >= 5) { // OpShiftRightArithmetic
        instr.opcode = 0x89; // S_ASHR_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00ac && wordCount >= 5) { // OpIEqual
        instr.opcode = 0x90; // S_CMP_EQ_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00af && wordCount >= 5) { // OpSLessThan
        instr.opcode = 0x91; // S_CMP_LT_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b1 && wordCount >= 5) { // OpSLessThanEqual
        instr.opcode = 0x92; // S_CMP_LE_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b0 && wordCount >= 5) { // OpSGreaterThan
        instr.opcode = 0x93; // S_CMP_GT_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b2 && wordCount >= 5) { // OpSGreaterThanEqual
        instr.opcode = 0x94; // S_CMP_GE_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Vector ALU Instructions
      else if (opcode == 0x003d && wordCount >= 4 && spirv[i + 1] == vec4Type) { // OpCopyObject (Vector)
        instr.opcode = 0x01; // V_MOV_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0081 && wordCount >= 5) { // OpFAdd
        instr.opcode = 0x02; // V_ADD_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0082 && wordCount >= 5) { // OpFSub
        instr.opcode = 0x03; // V_SUB_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0085 && wordCount >= 5) { // OpFMul
        instr.opcode = 0x04; // V_MUL_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0088 && wordCount >= 6 && spirv[i + 4] == 50) { // OpExtInst Fma
        instr.opcode = 0x05; // V_FMA_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 5];
        instr.src1 = spirv[i + 6];
        instr.src2 = spirv[i + 7];
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0086 && wordCount >= 5) { // OpFDiv
        instr.opcode = 0x06; // V_DIV_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0088 && wordCount >= 6 && spirv[i + 4] == 37) { // OpExtInst FMin
        instr.opcode = 0x07; // V_MIN_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 5];
        instr.src1 = spirv[i + 6];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0088 && wordCount >= 6 && spirv[i + 4] == 40) { // OpExtInst FMax
        instr.opcode = 0x08; // V_MAX_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 5];
        instr.src1 = spirv[i + 6];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0064 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpIAdd (Vector)
        instr.opcode = 0x09; // V_ADD_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0065 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpISub (Vector)
        instr.opcode = 0x0A; // V_SUB_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0066 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpIMul (Vector)
        instr.opcode = 0x0B; // V_MUL_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x007f && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpBitwiseAnd (Vector)
        instr.opcode = 0x0C; // V_AND_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0080 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpBitwiseOr (Vector)
        instr.opcode = 0x0D; // V_OR_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0081 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpBitwiseXor (Vector)
        instr.opcode = 0x0E; // V_XOR_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x007e && wordCount >= 4 && spirv[i + 1] == vec4Type) { // OpNot (Vector)
        instr.opcode = 0x0F; // V_NOT_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0089 && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpShiftLeftLogical (Vector)
        instr.opcode = 0x10; // V_LSHL_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x008a && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpShiftRightLogical (Vector)
        instr.opcode = 0x11; // V_LSHR_B32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x008b && wordCount >= 5 && spirv[i + 1] == vec4Type) { // OpShiftRightArithmetic (Vector)
        instr.opcode = 0x12; // V_ASHR_I32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Vector Comparison Instructions
      else if (opcode == 0x00b4 && wordCount >= 5) { // OpFOrdEqual
        instr.opcode = 0x20; // V_CMP_EQ_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b7 && wordCount >= 5) { // OpFOrdNotEqual
        instr.opcode = 0x21; // V_CMP_NE_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b8 && wordCount >= 5) { // OpFOrdGreaterThan
        instr.opcode = 0x22; // V_CMP_GT_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b6 && wordCount >= 5) { // OpFOrdLessThan
        instr.opcode = 0x23; // V_CMP_LT_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b9 && wordCount >= 5) { // OpFOrdGreaterThanEqual
        instr.opcode = 0x24; // V_CMP_GE_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00b5 && wordCount >= 5) { // OpFOrdLessThanEqual
        instr.opcode = 0x25; // V_CMP_LE_F32
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Memory Instructions
      else if (opcode == 0x003f && wordCount >= 4 && spirv[i + 1] == vec4Type) { // OpLoad
        if (spirv[i + 3] % 3 == 0) { // Assume modulo check for BUFFER_LOAD
          instr.opcode = 0xA0; // BUFFER_LOAD
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = 0;
          instr.src2 = 0;
          instr.imm = 0;
        } else if (spirv[i + 3] % 3 == 1) { // DS_READ
          instr.opcode = 0xA4; // DS_READ
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = 0;
          instr.src2 = 0;
          instr.imm = 0;
        } else { // FLAT_LOAD
          instr.opcode = 0xA6; // FLAT_LOAD
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = 0;
          instr.src2 = 0;
          instr.imm = 0;
        }
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x003e && wordCount >= 3) { // OpStore
        if (spirv[i + 1] % 3 == 0) { // BUFFER_STORE
          instr.opcode = 0xA1; // BUFFER_STORE
          instr.dst = 0;
          instr.src0 = spirv[i + 1];
          instr.src1 = spirv[i + 2];
          instr.src2 = 0;
          instr.imm = 0;
        } else if (spirv[i + 1] % 3 == 1) { // DS_WRITE
          instr.opcode = 0xA5; // DS_WRITE
          instr.dst = 0;
          instr.src0 = spirv[i + 1];
          instr.src1 = spirv[i + 2];
          instr.src2 = 0;
          instr.imm = 0;
        } else { // FLAT_STORE
          instr.opcode = 0xA7; // FLAT_STORE
          instr.dst = 0;
          instr.src0 = spirv[i + 1];
          instr.src1 = spirv[i + 2];
          instr.src2 = 0;
          instr.imm = 0;
        }
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0063 && wordCount >= 5) { // OpImageRead
        instr.opcode = 0xA2; // IMAGE_LOAD
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0064 && wordCount >= 4) { // OpImageWrite
        instr.opcode = 0xA3; // IMAGE_STORE
        instr.dst = 0;
        instr.src0 = spirv[i + 1];
        instr.src1 = spirv[i + 2];
        instr.src2 = spirv[i + 3];
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Control Flow Instructions
      else if (opcode == 0x00f9 && wordCount >= 2) { // OpBranch
        instr.opcode = 0xA8; // S_BRANCH
        instr.dst = 0;
        instr.src0 = 0;
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = spirv[i + 1];
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00fa && wordCount >= 4) { // OpBranchConditional
        instr.opcode = 0xA9; // S_CBRANCH
        instr.dst = 0;
        instr.src0 = spirv[i + 1];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = spirv[i + 2]; // True label
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00e0 && wordCount >= 3) { // OpMemoryBarrier
        if (spirv[i + 2] == 72) { // S_WAITCNT
          instr.opcode = 0xAA; // S_WAITCNT
          instr.dst = 0;
          instr.src0 = 0;
          instr.src1 = 0;
          instr.src2 = 0;
          instr.imm = 0;
        } else { // S_SENDMSG (placeholder)
          instr.opcode = 0xAD; // S_SENDMSG
          instr.dst = 0;
          instr.src0 = 0;
          instr.src1 = 0;
          instr.src2 = 0;
          instr.imm = 0;
        }
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00e2 && wordCount >= 4) { // OpControlBarrier
        instr.opcode = 0xAB; // S_BARRIER
        instr.dst = 0;
        instr.src0 = 0;
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x00fd && wordCount >= 1) { // OpReturn
        instr.opcode = 0xAC; // S_ENDPGM
        instr.dst = 0;
        instr.src0 = 0;
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Texture Instructions
      else if (opcode == 0x0057 && wordCount >= 5) { // OpImageSampleImplicitLod
        instr.opcode = 0xB0; // IMAGE_SAMPLE
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x005b && wordCount >= 6) { // OpImageGather
        instr.opcode = 0xB1; // IMAGE_GATHER
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = spirv[i + 4];
        instr.src2 = spirv[i + 5];
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x0056 && wordCount >= 4) { // OpImageQuerySize
        instr.opcode = 0xB2; // IMAGE_GET_RESINFO
        instr.dst = spirv[i + 2];
        instr.src0 = spirv[i + 3];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      // Export Instructions
      else if (opcode == 0x003e && wordCount >= 3 && spirv[i + 1] % 4 == 1) { // OpStore (Position)
        instr.opcode = 0xC0; // EXP_POS
        instr.dst = 0;
        instr.src0 = spirv[i + 2];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x003e && wordCount >= 3 && spirv[i + 1] % 4 == 2) { // OpStore (Parameter)
        instr.opcode = 0xC1; // EXP_PARAM
        instr.dst = spirv[i + 1];
        instr.src0 = spirv[i + 2];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      } else if (opcode == 0x003e && wordCount >= 3 && spirv[i + 1] % 4 == 3) { // OpStore (MRT)
        instr.opcode = 0xC2; // EXP_MRT
        instr.dst = 0;
        instr.src0 = spirv[i + 2];
        instr.src1 = 0;
        instr.src2 = 0;
        instr.imm = 0;
        translated = true;
        m_stats.instructionsCovered++;
      }

      if (translated) {
        // Encode GCN instruction into binary
        uint32_t word0 = (instr.opcode << 24) | (instr.dst << 16) | (instr.src0 << 8) | instr.src1;
        uint32_t word1 = (instr.src2 << 24) | (instr.imm & 0xFFFFFF);
        gnmBinary.push_back(word0);
        if (instr.src2 != 0 || instr.imm != 0) {
          gnmBinary.push_back(word1);
        }
      } else {
        spdlog::debug("Translate: Unhandled SPIR-V opcode 0x{:x}", opcode);
        m_stats.instructionsSkipped++;
      }

      i += wordCount;
    }

    m_stats.operationCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("Translate: SPIR-V to GCN, instructions={}, covered={}, skipped={}, latency={}us",
                 spirv.size(), m_stats.instructionsCovered, m_stats.instructionsSkipped, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("Translate failed: {}", e.what());
    throw ShaderTranslatorException("Translate failed: " + std::string(e.what()));
  }
}

bool GNMShaderTranslator::GetCachedShader(uint64_t bytecodeHash, GCNShaderType type,
                                         std::vector<uint32_t> &spirvCode,
                                         std::string &glslCode) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    auto it = m_shaderCache.find(bytecodeHash);
    if (it != m_shaderCache.end() && it->second.type == type) {
      spirvCode = it->second.spirvCode;
      glslCode = it->second.glslCode;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::debug("GetCachedShader: Cache hit, hash=0x{:x}, type={}, latency={}us",
                    bytecodeHash, static_cast<int>(type), latency);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::debug("GetCachedShader: Cache miss, hash=0x{:x}, type={}, latency={}us",
                  bytecodeHash, static_cast<int>(type), latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedShader failed: {}", e.what());
    return false;
  }
}

void GNMShaderTranslator::ClearShaderCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_shaderCache.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("ClearShaderCache: Cache cleared, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearShaderCache failed: {}", e.what());
  }
}

ShaderTranslatorStats GNMShaderTranslator::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    ShaderTranslatorStats stats = m_stats;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: Retrieved stats, latency={}us", latency);
    return stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetStats failed: {}", e.what());
    return ShaderTranslatorStats();
  }
}

void GNMShaderTranslator::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    uint64_t scalarCount = m_scalarOpcodes.size();
    out.write(reinterpret_cast<const char *>(&scalarCount),
              sizeof(scalarCount));
    for (const auto &[opcode, name] : m_scalarOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t vectorCount = m_vectorOpcodes.size();
    out.write(reinterpret_cast<const char *>(&vectorCount),
              sizeof(vectorCount));
    for (const auto &[opcode, name] : m_vectorOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t memoryCount = m_memoryOpcodes.size();
    out.write(reinterpret_cast<const char *>(&memoryCount),
              sizeof(memoryCount));
    for (const auto &[opcode, name] : m_memoryOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t flowCount = m_flowControlOpcodes.size();
    out.write(reinterpret_cast<const char *>(&flowCount), sizeof(flowCount));
    for (const auto &[opcode, name] : m_flowControlOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t cacheCount = m_shaderCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[hash, entry] : m_shaderCache) {
      out.write(reinterpret_cast<const char *>(&hash), sizeof(hash));
      uint32_t spirvSize = static_cast<uint32_t>(entry.spirvCode.size());
      out.write(reinterpret_cast<const char *>(&spirvSize), sizeof(spirvSize));
      out.write(reinterpret_cast<const char *>(entry.spirvCode.data()),
                spirvSize * sizeof(uint32_t));
      uint32_t glslSize = static_cast<uint32_t>(entry.glslCode.size());
      out.write(reinterpret_cast<const char *>(&glslSize), sizeof(glslSize));
      out.write(entry.glslCode.data(), glslSize);
      out.write(reinterpret_cast<const char *>(&entry.type),
                sizeof(entry.type));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException(
          "Failed to write shader translator state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved shader translator state, scalar_ops={}, "
                 "vector_ops={}, memory_ops={}, flow_ops={}, cache_count={}, latency={}us",
                 scalarCount, vectorCount, memoryCount, flowCount, cacheCount,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw ShaderTranslatorException("SaveState failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException(
          "Unsupported shader translator state version: " +
          std::to_string(version));
    }

    m_scalarOpcodes.clear();
    uint64_t scalarCount;
    in.read(reinterpret_cast<char *>(&scalarCount), sizeof(scalarCount));
    for (uint64_t i = 0; i < scalarCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_scalarOpcodes[opcode] = name;
    }

    m_vectorOpcodes.clear();
    uint64_t vectorCount;
    in.read(reinterpret_cast<char *>(&vectorCount), sizeof(vectorCount));
    for (uint64_t i = 0; i < vectorCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_vectorOpcodes[opcode] = name;
    }

    m_memoryOpcodes.clear();
    uint64_t memoryCount;
    in.read(reinterpret_cast<char *>(&memoryCount), sizeof(memoryCount));
    for (uint64_t i = 0; i < memoryCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_memoryOpcodes[opcode] = name;
    }

    m_flowControlOpcodes.clear();
    uint64_t flowCount;
    in.read(reinterpret_cast<char *>(&flowCount), sizeof(flowCount));
    for (uint64_t i = 0; i < flowCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_flowControlOpcodes[opcode] = name;
    }

    m_shaderCache.clear();
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t hash;
      ShaderCacheEntry entry;
      in.read(reinterpret_cast<char *>(&hash), sizeof(hash));
      uint32_t spirvSize;
      in.read(reinterpret_cast<char *>(&spirvSize), sizeof(spirvSize));
      entry.spirvCode.resize(spirvSize);
      in.read(reinterpret_cast<char *>(entry.spirvCode.data()),
              spirvSize * sizeof(uint32_t));
      uint32_t glslSize;
      in.read(reinterpret_cast<char *>(&glslSize), sizeof(glslSize));
      entry.glslCode.resize(glslSize);
      in.read(entry.glslCode.data(), glslSize);
      in.read(reinterpret_cast<char *>(&entry.type), sizeof(entry.type));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_shaderCache[hash] = entry;
    }

    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Failed to read shader translator state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded shader translator state, scalar_ops={}, "
                 "vector_ops={}, memory_ops={}, flow_ops={}, cache_count={}, latency={}us",
                 scalarCount, vectorCount, memoryCount, flowCount, cacheCount,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw ShaderTranslatorException("LoadState failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::SetShaderTranslationCallback_SPIRV(
    const ShaderTranslationCallback_SPIRV &callback) {
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  m_spirvCallback = callback;
  spdlog::debug("GNMShaderTranslator: SPIR-V callback set");
}

void GNMShaderTranslator::SetShaderTranslationCallback_GLSL(
    const ShaderTranslationCallback_GLSL &callback) {
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  m_glslCallback = callback;
  spdlog::debug("GNMShaderTranslator: GLSL callback set");
}

bool GNMShaderTranslator::TranslateGCNInstruction(
    const EnhancedGCNInstruction &instr, std::string &spirvCode,
    std::string &glslCode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    bool success = false;

    switch (instr.type) {
    case GCNInstructionType::S_MOV:
    case GCNInstructionType::S_ADD:
    case GCNInstructionType::S_SUB:
    case GCNInstructionType::S_MUL:
    case GCNInstructionType::S_AND:
    case GCNInstructionType::S_OR:
    case GCNInstructionType::S_XOR:
    case GCNInstructionType::S_NOT:
    case GCNInstructionType::S_LSHL:
    case GCNInstructionType::S_LSHR:
    case GCNInstructionType::S_ASHR:
    case GCNInstructionType::S_CMP:
      success = ProcessScalarALU(instr, spirvCode);
      if (success)
        success = ProcessScalarALU(instr, glslCode);
      break;

    case GCNInstructionType::V_MOV:
    case GCNInstructionType::V_ADD_F32:
    case GCNInstructionType::V_SUB_F32:
    case GCNInstructionType::V_MUL_F32:
    case GCNInstructionType::V_DIV_F32:
    case GCNInstructionType::V_MAD_F32:
    case GCNInstructionType::V_FMA_F32:
    case GCNInstructionType::V_MIN_F32:
    case GCNInstructionType::V_MAX_F32:
    case GCNInstructionType::V_ADD_I32:
    case GCNInstructionType::V_SUB_I32:
    case GCNInstructionType::V_MUL_I32:
    case GCNInstructionType::V_AND_B32:
    case GCNInstructionType::V_OR_B32:
      success = ProcessVectorALU(instr, spirvCode);
      if (success)
        success = ProcessVectorALU(instr, glslCode);
      break;

    case GCNInstructionType::BUFFER_LOAD:
    case GCNInstructionType::BUFFER_STORE:
    case GCNInstructionType::IMAGE_LOAD:
    case GCNInstructionType::IMAGE_STORE:
    case GCNInstructionType::DS_READ:
    case GCNInstructionType::DS_WRITE:
    case GCNInstructionType::FLAT_LOAD:
    case GCNInstructionType::FLAT_STORE:
      success = ProcessMemoryInstruction(instr, spirvCode);
      if (success)
        success = ProcessMemoryInstruction(instr, glslCode);
      break;

    case GCNInstructionType::S_BRANCH:
    case GCNInstructionType::S_CBRANCH:
    case GCNInstructionType::S_ENDPGM:
    case GCNInstructionType::S_BARRIER:
    case GCNInstructionType::S_WAITCNT:
    case GCNInstructionType::S_SENDMSG:
      success = ProcessControlFlow(instr, spirvCode);
      if (success)
        success = ProcessControlFlow(instr, glslCode);
      break;

    case GCNInstructionType::IMAGE_SAMPLE:
    case GCNInstructionType::IMAGE_GATHER:
    case GCNInstructionType::IMAGE_GET_RESINFO:
      success = ProcessTextureInstruction(instr, spirvCode);
      if (success)
        success = ProcessTextureInstruction(instr, glslCode);
      break;

    case GCNInstructionType::EXP_POS:
    case GCNInstructionType::EXP_PARAM:
    case GCNInstructionType::EXP_MRT:
      success = ProcessExportInstruction(instr, spirvCode);
      if (success)
        success = ProcessExportInstruction(instr, glslCode);
      break;

    case GCNInstructionType::UNKNOWN:
    default:
      m_stats.instructionsSkipped++;
      spdlog::warn("GNMShaderTranslator: Unsupported instruction type: {}",
                   static_cast<int>(instr.type));
      return false;
    }

    if (success) {
      m_stats.instructionsCovered++;
    } else {
      m_stats.instructionsSkipped++;
      m_stats.errorCount++;
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    return success;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("TranslateGCNInstruction failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessScalarALU(const EnhancedGCNInstruction &instr,
                                           std::string &output) {
  try {
    std::string operation;

    switch (instr.type) {
    case GCNInstructionType::S_MOV:
      operation = "OpCopyObject";
      break;
    case GCNInstructionType::S_ADD:
      operation = "OpIAdd";
      break;
    case GCNInstructionType::S_SUB:
      operation = "OpISub";
      break;
    case GCNInstructionType::S_MUL:
      operation = "OpIMul";
      break;
    case GCNInstructionType::S_AND:
      operation = "OpBitwiseAnd";
      break;
    case GCNInstructionType::S_OR:
      operation = "OpBitwiseOr";
      break;
    case GCNInstructionType::S_XOR:
      operation = "OpBitwiseXor";
      break;
    case GCNInstructionType::S_NOT:
      operation = "OpNot";
      break;
    case GCNInstructionType::S_LSHL:
      operation = "OpShiftLeftLogical";
      break;
    case GCNInstructionType::S_LSHR:
      operation = "OpShiftRightLogical";
      break;
    case GCNInstructionType::S_ASHR:
      operation = "OpShiftRightArithmetic";
      break;
    case GCNInstructionType::S_CMP:
      operation = "OpIEqual"; // Simplified comparison
      break;
    default:
      return false;
    }

    // Generate SPIR-V or GLSL code based on output context
    if (output.find("OpCapability") != std::string::npos) {
      // SPIR-V generation
      if (instr.type == GCNInstructionType::S_NOT) {
        output += fmt::format("%{} = {} %uint %{}\n", instr.operands[0],
                              operation, instr.operands[1]);
      } else {
        output += fmt::format("%{} = {} %uint %{} %{}\n", instr.operands[0],
                              operation, instr.operands[1], instr.operands[2]);
      }
    } else {
      // GLSL generation
      std::string glslOp;
      switch (instr.type) {
      case GCNInstructionType::S_MOV:
        glslOp = "=";
        break;
      case GCNInstructionType::S_ADD:
        glslOp = "+";
        break;
      case GCNInstructionType::S_SUB:
        glslOp = "-";
        break;
      case GCNInstructionType::S_MUL:
        glslOp = "*";
        break;
      case GCNInstructionType::S_AND:
        glslOp = "&";
        break;
      case GCNInstructionType::S_OR:
        glslOp = "|";
        break;
      case GCNInstructionType::S_XOR:
        glslOp = "^";
        break;
      case GCNInstructionType::S_NOT:
        glslOp = "~";
        break;
      case GCNInstructionType::S_LSHL:
        glslOp = "<<";
        break;
      case GCNInstructionType::S_LSHR:
        glslOp = ">>";
        break;
      case GCNInstructionType::S_ASHR:
        glslOp = ">>";
        break;
      case GCNInstructionType::S_CMP:
        glslOp = "==";
        break;
      default:
        return false;
      }

      if (instr.type == GCNInstructionType::S_NOT) {
        output += fmt::format("  s{} = {}s{};\n", instr.operands[0],
                              glslOp, instr.operands[1]);
      } else if (instr.type == GCNInstructionType::S_ASHR || instr.type == GCNInstructionType::S_CMP) {
        output += fmt::format("  s{} = int(s{}) {} int(s{}) ? 1u : 0u;\n",
                              instr.operands[0], instr.operands[1], glslOp,
                              instr.operands[2]);
      } else {
        output += fmt::format("  s{} = s{} {} s{};\n", instr.operands[0],
                              instr.operands[1], glslOp, instr.operands[2]);
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessScalarALU failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessVectorALU(const EnhancedGCNInstruction &instr,
                                           std::string &output) {
  try {
    std::string operation;

    switch (instr.type) {
    case GCNInstructionType::V_MOV:
      operation = "OpCopyObject";
      break;
    case GCNInstructionType::V_ADD_F32:
      operation = "OpFAdd";
      break;
    case GCNInstructionType::V_SUB_F32:
      operation = "OpFSub";
      break;
    case GCNInstructionType::V_MUL_F32:
      operation = "OpFMul";
      break;
    case GCNInstructionType::V_DIV_F32:
      operation = "OpFDiv";
      break;
    case GCNInstructionType::V_MAD_F32:
    case GCNInstructionType::V_FMA_F32:
      operation = "OpFma";
      break;
    case GCNInstructionType::V_MIN_F32:
      operation = "OpFMin";
      break;
    case GCNInstructionType::V_MAX_F32:
      operation = "OpFMax";
      break;
    case GCNInstructionType::V_ADD_I32:
      operation = "OpIAdd";
      break;
    case GCNInstructionType::V_SUB_I32:
      operation = "OpISub";
      break;
    case GCNInstructionType::V_MUL_I32:
      operation = "OpIMul";
      break;
    case GCNInstructionType::V_AND_B32:
      operation = "OpBitwiseAnd";
      break;
    case GCNInstructionType::V_OR_B32:
      operation = "OpBitwiseOr";
      break;
    default:
      return false;
    }

    // Generate SPIR-V or GLSL code
    if (output.find("OpCapability") != std::string::npos) {
      // SPIR-V generation
      if (instr.type == GCNInstructionType::V_MAD_F32 ||
          instr.type == GCNInstructionType::V_FMA_F32) {
        output += fmt::format("%{} = {} %float %{} %{} %{}\n",
                              instr.operands[0], operation, instr.operands[1],
                              instr.operands[2], instr.operands[3]);
      } else if (instr.type == GCNInstructionType::V_MOV) {
        output += fmt::format("%{} = {} %vec4 %{}\n", instr.operands[0],
                              operation, instr.operands[1]);
      } else {
        output += fmt::format("%{} = {} %vec4 %{} %{}\n", instr.operands[0],
                              operation, instr.operands[1], instr.operands[2]);
      }
    } else {
      // GLSL generation
      std::string glslOp;
      switch (instr.type) {
      case GCNInstructionType::V_MOV:
        glslOp = "=";
        break;
      case GCNInstructionType::V_ADD_F32:
      case GCNInstructionType::V_ADD_I32:
        glslOp = "+";
        break;
      case GCNInstructionType::V_SUB_F32:
      case GCNInstructionType::V_SUB_I32:
        glslOp = "-";
        break;
      case GCNInstructionType::V_MUL_F32:
      case GCNInstructionType::V_MUL_I32:
        glslOp = "*";
        break;
      case GCNInstructionType::V_DIV_F32:
        glslOp = "/";
        break;
      case GCNInstructionType::V_MIN_F32:
        glslOp = "min";
        break;
      case GCNInstructionType::V_MAX_F32:
        glslOp = "max";
        break;
      case GCNInstructionType::V_AND_B32:
        glslOp = "&";
        break;
      case GCNInstructionType::V_OR_B32:
        glslOp = "|";
        break;
      default:
        return false;
      }

      if (instr.type == GCNInstructionType::V_MAD_F32 ||
          instr.type == GCNInstructionType::V_FMA_F32) {
        output += fmt::format("  v{} = fma(v{}, v{}, v{});\n",
                              instr.operands[0], instr.operands[1],
                              instr.operands[2], instr.operands[3]);
      } else if (instr.type == GCNInstructionType::V_MIN_F32 ||
                 instr.type == GCNInstructionType::V_MAX_F32) {
        output += fmt::format("  v{} = {}(v{}, v{});\n", instr.operands[0],
                              glslOp, instr.operands[1], instr.operands[2]);
      } else if (instr.type == GCNInstructionType::V_AND_B32 ||
                 instr.type == GCNInstructionType::V_OR_B32) {
        output += fmt::format("  v{} = uintBitsToFloat(floatBitsToUint(v{}) {} floatBitsToUint(v{}));\n",
                              instr.operands[0], instr.operands[1], glslOp,
                              instr.operands[2]);
      } else if (instr.type == GCNInstructionType::V_ADD_I32 ||
                 instr.type == GCNInstructionType::V_SUB_I32 ||
                 instr.type == GCNInstructionType::V_MUL_I32) {
        output += fmt::format("  v{} = ivec4(ivec4(v{}) {} ivec4(v{}));\n",
                              instr.operands[0], instr.operands[1], glslOp,
                              instr.operands[2]);
      } else {
        output += fmt::format("  v{} = v{} {} v{};\n", instr.operands[0],
                              instr.operands[1], glslOp, instr.operands[2]);
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessVectorALU failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessMemoryInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::BUFFER_LOAD:
      ss << "// Buffer load operation\n";
      if (instr.operands.size() >= 2) {
        ss << "vec4 buffer_data = texelFetch(buffer_texture, int(s"
           << instr.operands[1] << "));\n";
        ss << "v" << instr.operands[0] << " = buffer_data;\n";
      }
      break;
    case GCNInstructionType::BUFFER_STORE:
      ss << "// Buffer store operation\n";
      if (instr.operands.size() >= 2) {
        ss << "imageStore(buffer_image, int(s" << instr.operands[0] << "), v"
           << instr.operands[1] << ");\n";
      }
      break;
    case GCNInstructionType::IMAGE_LOAD:
      ss << "// Image load operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 image_data = texelFetch(image_texture, ivec2(s"
           << instr.operands[1] << ", s" << instr.operands[2] << "));\n";
        ss << "v" << instr.operands[0] << " = image_data;\n";
      }
      break;
    case GCNInstructionType::IMAGE_STORE:
      ss << "// Image store operation\n";
      if (instr.operands.size() >= 3) {
        ss << "imageStore(image_texture, ivec2(s" << instr.operands[0] << ", s"
           << instr.operands[1] << "), v" << instr.operands[2] << ");\n";
      }
      break;
    case GCNInstructionType::DS_READ:
      ss << "// LDS read operation\n";
      if (instr.operands.size() >= 2) {
        ss << "vec4 lds_data = texelFetch(local_memory, int(s"
           << instr.operands[1] << "));\n";
        ss << "v" << instr.operands[0] << " = lds_data;\n";
      }
      break;
    case GCNInstructionType::DS_WRITE:
      ss << "// LDS write operation\n";
      if (instr.operands.size() >= 2) {
        ss << "imageStore(local_memory, int(s" << instr.operands[0] << "), v"
           << instr.operands[1] << ");\n";
      }
      break;
    case GCNInstructionType::FLAT_LOAD:
      ss << "// Flat memory load operation\n";
      if (instr.operands.size() >= 2) {
        ss << "vec4 flat_data = texelFetch(flat_memory, int(s"
           << instr.operands[1] << "));\n";
        ss << "v" << instr.operands[0] << " = flat_data;\n";
      }
      break;
    case GCNInstructionType::FLAT_STORE:
      ss << "// Flat memory store operation\n";
      if (instr.operands.size() >= 2) {
        ss << "imageStore(flat_memory, int(s" << instr.operands[0] << "), v"
           << instr.operands[1] << ");\n";
      }
      break;
    default:
      ss << "// Unsupported memory instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessMemoryInstruction failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessControlFlow(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::S_BRANCH:
      ss << "// Unconditional branch\n";
      if (instr.operands.size() >= 1) {
        ss << "// Branch to address: 0x" << std::hex << instr.operands[0]
           << "\n";
      }
      break;
    case GCNInstructionType::S_CBRANCH:
      ss << "// Conditional branch\n";
      if (instr.operands.size() >= 2) {
        ss << "if (s" << instr.operands[0] << " != 0u) {\n";
        ss << "  // Branch to address: 0x" << std::hex << instr.operands[1]
           << "\n";
        ss << "}\n";
      }
      break;
    case GCNInstructionType::S_ENDPGM:
      ss << "// End program\n";
      ss << "return;\n";
      break;
    case GCNInstructionType::S_BARRIER:
      ss << "// Synchronization barrier\n";
      ss << "barrier();\n";
      break;
    case GCNInstructionType::S_WAITCNT:
      ss << "// Wait for memory operations\n";
      ss << "memoryBarrier();\n";
      break;
    case GCNInstructionType::S_SENDMSG:
      ss << "// Send message (unimplemented)\n";
      break;
    default:
      ss << "// Unsupported control flow instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessControlFlow failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessTextureInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::IMAGE_SAMPLE:
      ss << "// Texture sample operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 sample_result = texture(image_texture, vec2(v"
           << instr.operands[1] << ".xy));\n";
        ss << "v" << instr.operands[0] << " = sample_result;\n";
      }
      break;
    case GCNInstructionType::IMAGE_GATHER:
      ss << "// Texture gather operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 gather_result = textureGather(image_texture, vec2(v"
           << instr.operands[1] << ".xy));\n";
        ss << "v" << instr.operands[0] << " = gather_result;\n";
      }
      break;
    case GCNInstructionType::IMAGE_GET_RESINFO:
      ss << "// Get texture resolution info\n";
      if (instr.operands.size() >= 2) {
        ss << "ivec2 tex_size = textureSize(image_texture, 0);\n";
        ss << "v" << instr.operands[0] << " = vec4(tex_size.xy, 0.0, 0.0);\n";
      }
      break;
    default:
      ss << "// Unsupported texture instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessTextureInstruction failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ProcessExportInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::EXP_POS:
      ss << "// Export position\n";
      if (instr.operands.size() >= 1) {
        ss << "gl_Position = v" << instr.operands[0] << ";\n";
      }
      break;
    case GCNInstructionType::EXP_PARAM:
      ss << "// Export parameter\n";
      if (instr.operands.size() >= 2) {
        ss << "out_param_" << instr.operands[0] << " = v" << instr.operands[1]
           << ";\n";
      }
      break;
    case GCNInstructionType::EXP_MRT:
      ss << "// Export to multiple render targets\n";
      if (instr.operands.size() >= 2) {
        ss << "out_color_" << instr.operands[0] << " = v" << instr.operands[1]
           << ";\n";
      }
      break;
    default:
      ss << "// Unsupported export instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessExportInstruction failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ParseShader(ShaderParseState &state) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (!state.bytecode || state.bytecode->empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ParseShader: Invalid or empty bytecode");
      return false;
    }

    state.instructions.clear();
    state.labelMap.clear();
    state.position = 0;

    while (state.position < state.bytecode->size()) {
      GCNInstruction instr;
      if (!ParseInstruction(state, instr)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("ParseShader: Failed to parse instruction at position {}",
                      state.position);
        return false;
      }

      state.instructions.push_back(instr);

      if ((instr.opcode & 0xF0) == 0xA0) {
        uint32_t targetAddress = instr.imm * 4;
        std::stringstream labelName;
        labelName << "L_" << std::hex << std::setw(8) << std::setfill('0')
                  << targetAddress;
        state.labelMap[targetAddress] = labelName.str();
      }
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ParseShader: instructions={}, latency={}us",
                  state.instructions.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ParseShader failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ParseInstruction(ShaderParseState &state,
                                           GCNInstruction &instr) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    const std::vector<uint32_t> &bytecode = *state.bytecode;
    if (state.position >= bytecode.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      return false;
    }

    uint32_t word = bytecode[state.position++];
    instr.opcode = (word >> 24) & 0xFF;
    instr.dst = (word >> 16) & 0xFF;
    instr.src0 = (word >> 8) & 0xFF;
    instr.src1 = word & 0xFF;

    if (state.position < bytecode.size()) {
      word = bytecode[state.position++];
      instr.src2 = (word >> 24) & 0xFF;
      instr.imm = word & 0xFFFFFF;
    } else {
      instr.src2 = 0;
      instr.imm = 0;
    }

    instr.predicated = false;
    instr.predicate = 0;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ParseInstruction: opcode=0x{:x}, dst={}, src0={}, src1={}, "
                  "latency={}us",
                  instr.opcode, instr.dst, instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ParseInstruction failed: {}", e.what());
    return false;
  }
}

std::string
GNMShaderTranslator::DisassembleInstruction(const GCNInstruction &instr,
                                            uint32_t address) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::string result;
    if ((instr.opcode & 0xF0) == 0x80 || (instr.opcode & 0xF0) == 0x90 ||
        instr.opcode == 0xBE) {
      result = FormatScalarInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0x00 || (instr.opcode & 0xF0) == 0x10 ||
               (instr.opcode & 0xF0) == 0x20) {
      result = FormatVectorInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0x40 || (instr.opcode & 0xF0) == 0x50 ||
               instr.opcode == 0xA6 || instr.opcode == 0xA7) {
      result = FormatMemoryInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0xA0) {
      result = FormatFlowControlInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0xB0) {
      result = FormatTextureInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0xC0) {
      result = FormatExportInstruction(instr);
    } else {
      std::stringstream ss;
      ss << "UNKNOWN_" << std::hex << std::setw(2) << std::setfill('0')
         << instr.opcode;
      result = ss.str();
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "DisassembleInstruction: opcode=0x{:x}, address=0x{:x}, latency={}us",
        instr.opcode, address, latency);
    return result;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DisassembleInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::GetRegisterName(GCNRegisterType type,
                                                 uint32_t index) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    switch (type) {
    case GCNRegisterType::SCALAR:
      ss << "s" << index;
      break;
    case GCNRegisterType::VECTOR:
      ss << "v" << index;
      break;
    case GCNRegisterType::SPECIAL:
      switch (index) {
      case 0:
        ss << "vcc";
        break;
      case 1:
        ss << "exec";
        break;
      case 2:
        ss << "scc";
        break;
      default:
        ss << "special" << index;
        break;
      }
      break;
    default:
      ss << "unknown" << index;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetRegisterName: type={}, index={}, latency={}us",
                  static_cast<int>(type), index, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetRegisterName failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::GetOpcodeName(uint32_t opcode) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    auto scalarIt = m_scalarOpcodes.find(opcode);
    if (scalarIt != m_scalarOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, scalarIt->second, latency);
      return scalarIt->second;
    }
    auto vectorIt = m_vectorOpcodes.find(opcode);
    if (vectorIt != m_vectorOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, vectorIt->second, latency);
      return vectorIt->second;
    }
    auto memoryIt = m_memoryOpcodes.find(opcode);
    if (memoryIt != m_memoryOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, memoryIt->second, latency);
      return memoryIt->second;
    }
    auto flowIt = m_flowControlOpcodes.find(opcode);
    if (flowIt != m_flowControlOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, flowIt->second, latency);
      return flowIt->second;
    }
    auto textureIt = m_textureOpcodes.find(opcode);
    if (textureIt != m_textureOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, textureIt->second, latency);
      return textureIt->second;
    }
    auto exportIt = m_exportOpcodes.find(opcode);
    if (exportIt != m_exportOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, exportIt->second, latency);
      return exportIt->second;
    }

    std::stringstream ss;
    ss << "UNKNOWN_" << std::hex << std::setw(2) << std::setfill('0') << opcode;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us", opcode,
                  ss.str(), latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetOpcodeName failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatScalarInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xBE: // S_MOV_B32
    case 0x86: // S_NOT_B32
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0);
      break;
    case 0x80: // S_ADD_U32
    case 0x81: // S_SUB_U32
    case 0x82: // S_MUL_I32
    case 0x83: // S_AND_B32
    case 0x84: // S_OR_B32
    case 0x85: // S_XOR_B32
    case 0x87: // S_LSHL_B32
    case 0x88: // S_LSHR_B32
    case 0x89: // S_ASHR_I32
    case 0x90: // S_CMP_EQ_I32
    case 0x91: // S_CMP_LT_I32
    case 0x92: // S_CMP_LE_I32
    case 0x93: // S_CMP_GT_I32
    case 0x94: // S_CMP_GE_I32
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatScalarInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatScalarInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatVectorInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0x01: // V_MOV_B32
    case 0x0F: // V_NOT_B32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      break;
    case 0x02: // V_ADD_F32
    case 0x03: // V_SUB_F32
    case 0x04: // V_MUL_F32
    case 0x06: // V_DIV_F32
    case 0x07: // V_MIN_F32
    case 0x08: // V_MAX_F32
    case 0x09: // V_ADD_I32
    case 0x0A: // V_SUB_I32
    case 0x0B: // V_MUL_I32
    case 0x0C: // V_AND_B32
    case 0x0D: // V_OR_B32
    case 0x0E: // V_XOR_B32
    case 0x10: // V_LSHL_B32
    case 0x11: // V_LSHR_B32
    case 0x12: // V_ASHR_I32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    case 0x05: // V_MAD_F32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src2);
      break;
    case 0x20: // V_CMP_EQ_F32
    case 0x21: // V_CMP_NE_F32
    case 0x22: // V_CMP_GT_F32
    case 0x23: // V_CMP_LT_F32
    case 0x24: // V_CMP_GE_F32
    case 0x25: // V_CMP_LE_F32
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatVectorInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatVectorInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatMemoryInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0x40: // BUFFER_LOAD_DWORD
    case 0x41: // BUFFER_STORE_DWORD
    case 0x42: // BUFFER_LOAD_DWORDX2
    case 0x43: // BUFFER_STORE_DWORDX2
    case 0x44: // BUFFER_LOAD_DWORDX4
    case 0x45: // BUFFER_STORE_DWORDX4
    case 0xA6: // FLAT_LOAD
    case 0xA7: // FLAT_STORE
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1) << ", "
         << "0x" << std::hex << instr.imm << " offen";
      break;
    case 0x50: // DS_READ_B32
    case 0x51: // DS_WRITE_B32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0);
      if (instr.imm != 0) {
        ss << " offset:0x" << std::hex << instr.imm;
      }
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1) << ", "
         << "0x" << std::hex << instr.imm;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatMemoryInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatMemoryInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatFlowControlInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xA8: // S_BRANCH
      ss << "L_" << std::hex << std::setw(8) << std::setfill('0')
         << (instr.imm * 4);
      break;
    case 0xA9: // S_CBRANCH
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << "L_" << std::hex << std::setw(8) << std::setfill('0')
         << (instr.imm * 4);
      break;
    case 0xAA: // S_WAITCNT
    case 0xAB: // S_BARRIER
    case 0xAD: // S_SENDMSG
      ss << "0x" << std::hex << instr.imm;
      break;
    case 0xAC: // S_ENDPGM
      break;
    default:
      ss << "0x" << std::hex << instr.imm;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatFlowControlInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatFlowControlInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatTextureInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xB0: // IMAGE_SAMPLE
    case 0xB1: // IMAGE_GATHER
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      if (instr.src2 != 0) {
        ss << ", sampler" << instr.src2;
      }
      break;
    case 0xB2: // IMAGE_GET_RESINFO
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0);
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatTextureInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatTextureInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatExportInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xC0: // EXP_POS
    case 0xC1: // EXP_PARAM
    case 0xC2: // EXP_MRT
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      if (instr.dst != 0) {
        ss << ", out" << instr.dst;
      }
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatExportInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatExportInstruction failed: {}", e.what());
    return "ERROR";
  }
}

void GNMShaderTranslator::InitializeOpcodeTables() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_scalarOpcodes[0xBE] = "s_mov_b32";
    m_scalarOpcodes[0x80] = "s_add_u32";
    m_scalarOpcodes[0x81] = "s_sub_u32";
    m_scalarOpcodes[0x82] = "s_mul_i32";
    m_scalarOpcodes[0x83] = "s_and_b32";
    m_scalarOpcodes[0x84] = "s_or_b32";
    m_scalarOpcodes[0x85] = "s_xor_b32";
    m_scalarOpcodes[0x86] = "s_not_b32";
    m_scalarOpcodes[0x87] = "s_lshl_b32";
    m_scalarOpcodes[0x88] = "s_lshr_b32";
    m_scalarOpcodes[0x89] = "s_ashr_i32";
    m_scalarOpcodes[0x90] = "s_cmp_eq_i32";
    m_scalarOpcodes[0x91] = "s_cmp_lt_i32";
    m_scalarOpcodes[0x92] = "s_cmp_le_i32";
    m_scalarOpcodes[0x93] = "s_cmp_gt_i32";
    m_scalarOpcodes[0x94] = "s_cmp_ge_i32";

    m_vectorOpcodes[0x01] = "v_mov_b32";
    m_vectorOpcodes[0x02] = "v_add_f32";
    m_vectorOpcodes[0x03] = "v_sub_f32";
    m_vectorOpcodes[0x04] = "v_mul_f32";
    m_vectorOpcodes[0x05] = "v_mad_f32";
    m_vectorOpcodes[0x06] = "v_div_f32";
    m_vectorOpcodes[0x07] = "v_min_f32";
    m_vectorOpcodes[0x08] = "v_max_f32";
    m_vectorOpcodes[0x09] = "v_add_i32";
    m_vectorOpcodes[0x0A] = "v_sub_i32";
    m_vectorOpcodes[0x0B] = "v_mul_i32";
    m_vectorOpcodes[0x0C] = "v_and_b32";
    m_vectorOpcodes[0x0D] = "v_or_b32";
    m_vectorOpcodes[0x0E] = "v_xor_b32";
    m_vectorOpcodes[0x0F] = "v_not_b32";
    m_vectorOpcodes[0x10] = "v_lshl_b32";
    m_vectorOpcodes[0x11] = "v_lshr_b32";
    m_vectorOpcodes[0x12] = "v_ashr_i32";
    m_vectorOpcodes[0x20] = "v_cmp_eq_f32";
    m_vectorOpcodes[0x21] = "v_cmp_ne_f32";
    m_vectorOpcodes[0x22] = "v_cmp_gt_f32";
    m_vectorOpcodes[0x23] = "v_cmp_lt_f32";
    m_vectorOpcodes[0x24] = "v_cmp_ge_f32";
    m_vectorOpcodes[0x25] = "v_cmp_le_f32";

    m_memoryOpcodes[0x40] = "buffer_load_dword";
    m_memoryOpcodes[0x41] = "buffer_store_dword";
    m_memoryOpcodes[0x42] = "buffer_load_dwordx2";
    m_memoryOpcodes[0x43] = "buffer_store_dwordx2";
    m_memoryOpcodes[0x44] = "buffer_load_dwordx4";
    m_memoryOpcodes[0x45] = "buffer_store_dwordx4";
    m_memoryOpcodes[0x50] = "ds_read_b32";
    m_memoryOpcodes[0x51] = "ds_write_b32";
    m_memoryOpcodes[0xA6] = "flat_load";
    m_memoryOpcodes[0xA7] = "flat_store";

    m_flowControlOpcodes[0xA8] = "s_branch";
    m_flowControlOpcodes[0xA9] = "s_cbranch";
    m_flowControlOpcodes[0xAA] = "s_waitcnt";
    m_flowControlOpcodes[0xAB] = "s_barrier";
    m_flowControlOpcodes[0xAC] = "s_endpgm";
    m_flowControlOpcodes[0xAD] = "s_sendmsg";

    m_textureOpcodes[0xB0] = "image_sample";
    m_textureOpcodes[0xB1] = "image_gather";
    m_textureOpcodes[0xB2] = "image_get_resinfo";

    m_exportOpcodes[0xC0] = "exp_pos";
    m_exportOpcodes[0xC1] = "exp_param";
    m_exportOpcodes[0xC2] = "exp_mrt";

    m_opcodeToType[0xBE] = GCNInstructionType::S_MOV;
    m_opcodeToType[0x80] = GCNInstructionType::S_ADD;
    m_opcodeToType[0x81] = GCNInstructionType::S_SUB;
    m_opcodeToType[0x82] = GCNInstructionType::S_MUL;
    m_opcodeToType[0x83] = GCNInstructionType::S_AND;
    m_opcodeToType[0x84] = GCNInstructionType::S_OR;
    m_opcodeToType[0x85] = GCNInstructionType::S_XOR;
    m_opcodeToType[0x86] = GCNInstructionType::S_NOT;
    m_opcodeToType[0x87] = GCNInstructionType::S_LSHL;
    m_opcodeToType[0x88] = GCNInstructionType::S_LSHR;
    m_opcodeToType[0x89] = GCNInstructionType::S_ASHR;
    m_opcodeToType[0x90] = GCNInstructionType::S_CMP;
    m_opcodeToType[0x91] = GCNInstructionType::S_CMP;
    m_opcodeToType[0x92] = GCNInstructionType::S_CMP;
    m_opcodeToType[0x93] = GCNInstructionType::S_CMP;
    m_opcodeToType[0x94] = GCNInstructionType::S_CMP;
    m_opcodeToType[0x01] = GCNInstructionType::V_MOV;
    m_opcodeToType[0x02] = GCNInstructionType::V_ADD_F32;
    m_opcodeToType[0x03] = GCNInstructionType::V_SUB_F32;
    m_opcodeToType[0x04] = GCNInstructionType::V_MUL_F32;
    m_opcodeToType[0x05] = GCNInstructionType::V_FMA_F32;
    m_opcodeToType[0x06] = GCNInstructionType::V_DIV_F32;
    m_opcodeToType[0x07] = GCNInstructionType::V_MIN_F32;
    m_opcodeToType[0x08] = GCNInstructionType::V_MAX_F32;
    m_opcodeToType[0x09] = GCNInstructionType::V_ADD_I32;
    m_opcodeToType[0x0A] = GCNInstructionType::V_SUB_I32;
    m_opcodeToType[0x0B] = GCNInstructionType::V_MUL_I32;
    m_opcodeToType[0x0C] = GCNInstructionType::V_AND_B32;
    m_opcodeToType[0x0D] = GCNInstructionType::V_OR_B32;
    m_opcodeToType[0x0E] = GCNInstructionType::V_OR_B32; // V_XOR_B32
    m_opcodeToType[0x0F] = GCNInstructionType::V_OR_B32; // V_NOT_B32
    m_opcodeToType[0x10] = GCNInstructionType::V_OR_B32; // V_LSHL_B32
    m_opcodeToType[0x11] = GCNInstructionType::V_OR_B32; // V_LSHR_B32
    m_opcodeToType[0x12] = GCNInstructionType::V_OR_B32; // V_ASHR_I32
    m_opcodeToType[0x20] = GCNInstructionType::V_OR_B32; // V_CMP_EQ_F32
    m_opcodeToType[0x21] = GCNInstructionType::V_OR_B32; // V_CMP_NE_F32
    m_opcodeToType[0x22] = GCNInstructionType::V_OR_B32; // V_CMP_GT_F32
    m_opcodeToType[0x23] = GCNInstructionType::V_OR_B32; // V_CMP_LT_F32
    m_opcodeToType[0x24] = GCNInstructionType::V_OR_B32; // V_CMP_GE_F32
    m_opcodeToType[0x25] = GCNInstructionType::V_OR_B32; // V_CMP_LE_F32
    m_opcodeToType[0x40] = GCNInstructionType::BUFFER_LOAD;
    m_opcodeToType[0x41] = GCNInstructionType::BUFFER_STORE;
    m_opcodeToType[0x42] = GCNInstructionType::BUFFER_LOAD;
    m_opcodeToType[0x43] = GCNInstructionType::BUFFER_STORE;
    m_opcodeToType[0x44] = GCNInstructionType::BUFFER_LOAD;
    m_opcodeToType[0x45] = GCNInstructionType::BUFFER_STORE;
    m_opcodeToType[0x50] = GCNInstructionType::DS_READ;
    m_opcodeToType[0x51] = GCNInstructionType::DS_WRITE;
    m_opcodeToType[0xA6] = GCNInstructionType::FLAT_LOAD;
    m_opcodeToType[0xA7] = GCNInstructionType::FLAT_STORE;
    m_opcodeToType[0xA8] = GCNInstructionType::S_BRANCH;
    m_opcodeToType[0xA9] = GCNInstructionType::S_CBRANCH;
    m_opcodeToType[0xAA] = GCNInstructionType::S_WAITCNT;
    m_opcodeToType[0xAB] = GCNInstructionType::S_BARRIER;
    m_opcodeToType[0xAC] = GCNInstructionType::S_ENDPGM;
    m_opcodeToType[0xAD] = GCNInstructionType::S_SENDMSG;
    m_opcodeToType[0xB0] = GCNInstructionType::IMAGE_SAMPLE;
    m_opcodeToType[0xB1] = GCNInstructionType::IMAGE_GATHER;
    m_opcodeToType[0xB2] = GCNInstructionType::IMAGE_GET_RESINFO;
    m_opcodeToType[0xC0] = GCNInstructionType::EXP_POS;
    m_opcodeToType[0xC1] = GCNInstructionType::EXP_PARAM;
    m_opcodeToType[0xC2] = GCNInstructionType::EXP_MRT;

    m_instructionCycles[GCNInstructionType::S_MOV] = 1;
    m_instructionCycles[GCNInstructionType::S_ADD] = 2;
    m_instructionCycles[GCNInstructionType::S_SUB] = 2;
    m_instructionCycles[GCNInstructionType::S_MUL] = 4;
    m_instructionCycles[GCNInstructionType::S_AND] = 2;
    m_instructionCycles[GCNInstructionType::S_OR] = 2;
    m_instructionCycles[GCNInstructionType::S_XOR] = 2;
    m_instructionCycles[GCNInstructionType::S_NOT] = 2;
    m_instructionCycles[GCNInstructionType::S_LSHL] = 2;
    m_instructionCycles[GCNInstructionType::S_LSHR] = 2;
    m_instructionCycles[GCNInstructionType::S_ASHR] = 2;
    m_instructionCycles[GCNInstructionType::S_CMP] = 2;
    m_instructionCycles[GCNInstructionType::V_MOV] = 1;
    m_instructionCycles[GCNInstructionType::V_ADD_F32] = 4;
    m_instructionCycles[GCNInstructionType::V_SUB_F32] = 4;
    m_instructionCycles[GCNInstructionType::V_MUL_F32] = 4;
    m_instructionCycles[GCNInstructionType::V_DIV_F32] = 16;
    m_instructionCycles[GCNInstructionType::V_MAD_F32] = 8;
    m_instructionCycles[GCNInstructionType::V_FMA_F32] = 8;
    m_instructionCycles[GCNInstructionType::V_MIN_F32] = 4;
    m_instructionCycles[GCNInstructionType::V_MAX_F32] = 4;
    m_instructionCycles[GCNInstructionType::V_ADD_I32] = 4;
    m_instructionCycles[GCNInstructionType::V_SUB_I32] = 4;
    m_instructionCycles[GCNInstructionType::V_MUL_I32] = 4;
    m_instructionCycles[GCNInstructionType::V_AND_B32] = 4;
    m_instructionCycles[GCNInstructionType::V_OR_B32] = 4;
    m_instructionCycles[GCNInstructionType::BUFFER_LOAD] = 16;
    m_instructionCycles[GCNInstructionType::BUFFER_STORE] = 16;
    m_instructionCycles[GCNInstructionType::IMAGE_LOAD] = 32;
    m_instructionCycles[GCNInstructionType::IMAGE_STORE] = 32;
    m_instructionCycles[GCNInstructionType::DS_READ] = 16;
    m_instructionCycles[GCNInstructionType::DS_WRITE] = 16;
    m_instructionCycles[GCNInstructionType::FLAT_LOAD] = 16;
    m_instructionCycles[GCNInstructionType::FLAT_STORE] = 16;
    m_instructionCycles[GCNInstructionType::S_BRANCH] = 4;
    m_instructionCycles[GCNInstructionType::S_CBRANCH] = 4;
    m_instructionCycles[GCNInstructionType::S_ENDPGM] = 1;
    m_instructionCycles[GCNInstructionType::S_BARRIER] = 8;
    m_instructionCycles[GCNInstructionType::S_WAITCNT] = 4;
    m_instructionCycles[GCNInstructionType::S_SENDMSG] = 4;
    m_instructionCycles[GCNInstructionType::IMAGE_SAMPLE] = 32;
    m_instructionCycles[GCNInstructionType::IMAGE_GATHER] = 32;
    m_instructionCycles[GCNInstructionType::IMAGE_GET_RESINFO] = 8;
    m_instructionCycles[GCNInstructionType::EXP_POS] = 8;
    m_instructionCycles[GCNInstructionType::EXP_PARAM] = 8;
    m_instructionCycles[GCNInstructionType::EXP_MRT] = 8;

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("InitializeOpcodeTables: scalar={}, vector={}, memory={}, "
                  "flow={}, texture={}, export={}, latency={}us",
                  m_scalarOpcodes.size(), m_vectorOpcodes.size(),
                  m_memoryOpcodes.size(), m_flowControlOpcodes.size(),
                  m_textureOpcodes.size(), m_exportOpcodes.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("InitializeOpcodeTables failed: {}", e.what());
    throw ShaderTranslatorException("InitializeOpcodeTables failed: " +
                                    std::string(e.what()));
  }
}

std::string GNMShaderTranslator::GenerateSPIRVHeader() {
  std::stringstream ss;
  ss << "; SPIR-V\n"
     << "; Version: 1.3\n"
     << "; Generator: GNMShaderTranslator\n"
     << "; Bound: TBD\n"
     << "; Schema: 0\n"
     << "OpCapability Shader\n"
     << "OpExtInstImport \"GLSL.std.450\"\n"
     << "OpMemoryModel Logical GLSL450\n";
  return ss.str();
}

std::string GNMShaderTranslator::GenerateSPIRVDeclarations(GCNShaderType type) {
  std::stringstream ss;
  ss << "%void = OpTypeVoid\n"
     << "%float = OpTypeFloat 32\n"
     << "%uint = OpTypeInt 32 0\n"
     << "%int = OpTypeInt 32 1\n"
     << "%vec4 = OpTypeVector %float 4\n"
     << "%bool = OpTypeBool\n"
     << "%main = OpTypeFunction %void\n";

  switch (type) {
  case GCNShaderType::VERTEX:
    ss << "%in_pos = OpVariable %vec4 Input\n"
       << "%out_color = OpVariable %vec4 Output\n";
    break;
  case GCNShaderType::PIXEL:
    ss << "%in_color = OpVariable %vec4 Input\n"
       << "%frag_color = OpVariable %vec4 Output\n";
    break;
  case GCNShaderType::COMPUTE:
    ss << "%global_id = OpVariable %vec4 Input\n";
    break;
  default:
    break;
  }
  return ss.str();
}

std::string GNMShaderTranslator::GenerateSPIRVMain(
    const std::vector<EnhancedGCNInstruction> &instructions) {
  std::stringstream ss;
  ss << "OpFunction %void 0 %main\n"
     << "OpLabel\n";

  for (const auto &instr : instructions) {
    std::string spirvCode, glslCode;
    if (TranslateGCNInstruction(instr, spirvCode, glslCode)) {
      ss << spirvCode;
    }
  }

  ss << "OpReturn\n"
     << "OpFunctionEnd\n";
  return ss.str();
}

std::string GNMShaderTranslator::GenerateGLSLHeader(GCNShaderType type) {
  std::stringstream ss;
  ss << "#version 450\n"
     << "#extension GL_ARB_separate_shader_objects : enable\n";

  switch (type) {
  case GCNShaderType::VERTEX:
    ss << "layout(location = 0) in vec4 inPosition;\n"
       << "layout(location = 0) out vec4 outColor;\n";
    break;
  case GCNShaderType::PIXEL:
    ss << "layout(location = 0) in vec4 inColor;\n"
       << "layout(location = 0) out vec4 fragColor;\n";
    break;
  case GCNShaderType::GEOMETRY:
    ss << "layout(triangles) in;\n"
       << "layout(triangle_strip, max_vertices = 3) out;\n";
    break;
  case GCNShaderType::COMPUTE:
    ss << "layout(local_size_x = 1, local_size_y = 1, local_size_z = 1) in;\n";
    break;
  case GCNShaderType::HULL:
    ss << "layout(vertices = 3) out;\n";
    break;
  case GCNShaderType::DOMAIN_SHADER:
    ss << "layout(triangles, equal_spacing, cw) in;\n";
    break;
  default:
    break;
  }
  return ss.str();
}

std::string GNMShaderTranslator::GenerateGLSLDeclarations(GCNShaderType type) {
  std::stringstream ss;
  ss << "uint s[256];\n"
     << "vec4 v[256];\n"
     << "layout(binding = 0) uniform sampler2D image_texture;\n"
     << "layout(binding = 1) uniform samplerBuffer buffer_texture;\n"
     << "layout(binding = 2, rgba32f) uniform imageBuffer buffer_image;\n"
     << "layout(binding = 3) uniform samplerBuffer local_memory;\n"
     << "layout(binding = 4) uniform samplerBuffer flat_memory;\n";
  return ss.str();
}

std::string GNMShaderTranslator::GenerateGLSLMain(
    const std::vector<EnhancedGCNInstruction> &instructions) {
  std::stringstream ss;
  ss << "void main() {\n";

  for (const auto &instr : instructions) {
    std::string spirvCode, glslCode;
    if (TranslateGCNInstruction(instr, spirvCode, glslCode)) {
      ss << glslCode;
    }
  }

  ss << "}\n";
  return ss.str();
}

bool GNMShaderTranslator::AnalyzeDataFlow(
    const std::vector<EnhancedGCNInstruction> &instructions) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    // Basic data flow analysis (stubbed for now)
    for (const auto &instr : instructions) {
      if (instr.hasDestination) {
        spdlog::trace("AnalyzeDataFlow: Instruction 0x{:x} writes to register {}",
                      instr.opcode, instr.operands[0]);
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("AnalyzeDataFlow: Analyzed {} instructions, latency={}us",
                  instructions.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("AnalyzeDataFlow failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::OptimizeInstructionSequence(
    std::vector<EnhancedGCNInstruction> &instructions) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    // Basic optimization: remove redundant MOV instructions
    std::vector<EnhancedGCNInstruction> optimized;
    for (const auto &instr : instructions) {
      if (instr.type == GCNInstructionType::S_MOV &&
          instr.operands[0] == instr.operands[1]) {
        spdlog::trace("OptimizeInstructionSequence: Removed redundant S_MOV");
        continue;
      }
      optimized.push_back(instr);
    }
    instructions = std::move(optimized);
    m_stats.optimizationPasses++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("OptimizeInstructionSequence: Optimized {} instructions, latency={}us",
                  instructions.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("OptimizeInstructionSequence failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::OptimizeShader(std::vector<uint32_t> &spirvCode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    // Stubbed: Basic SPIR-V optimization (e.g., remove redundant instructions)
    m_stats.optimizationPasses++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("OptimizeShader: Processed SPIR-V, size={} words, latency={}us",
                  spirvCode.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("OptimizeShader failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ValidateShader(const std::vector<uint32_t> &spirvCode) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (spirvCode.empty() || spirvCode[0] != 0x07230203) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ValidateShader: Invalid SPIR-V header");
      return false;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ValidateShader: Validated SPIR-V, size={} words, latency={}us",
                  spirvCode.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidateShader failed: {}", e.what());
    return false;
  }
}

std::string GNMShaderTranslator::GenerateGLSLFromSPIRV(
    const std::vector<uint32_t> &spirvCode) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << "#version 450\n"
       << "// Generated from SPIR-V\n"
       << "void main() {\n"
       << "  // Stubbed SPIR-V to GLSL conversion\n"
       << "}\n";
    m_stats.glslGenerations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GenerateGLSLFromSPIRV: Generated GLSL, latency={}us", latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GenerateGLSLFromSPIRV failed: {}", e.what());
    return "";
  }
}

bool GNMShaderTranslator::AnalyzeShaderComplexity(const std::vector<uint32_t> &bytecode,
                                                 uint32_t &instructionCount,
                                                 uint32_t &cycleEstimate) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = GCNShaderType::VERTEX; // Default type for analysis

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("AnalyzeShaderComplexity: Failed to parse shader");
      return false;
    }

    instructionCount = static_cast<uint32_t>(state.instructions.size());
    cycleEstimate = 0;

    for (const auto &instr : state.instructions) {
      EnhancedGCNInstruction enhancedInstr;
      enhancedInstr.opcode = instr.opcode;
      enhancedInstr.operands = {instr.dst, instr.src0, instr.src1, instr.src2, instr.imm};
      enhancedInstr.type = m_opcodeToType[instr.opcode];
      cycleEstimate += EstimateInstructionCycles(enhancedInstr);
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("AnalyzeShaderComplexity: instructions={}, cycles={}, latency={}us",
                  instructionCount, cycleEstimate, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("AnalyzeShaderComplexity failed: {}", e.what());
    return false;
  }
}

uint32_t GNMShaderTranslator::EstimateInstructionCycles(
    const EnhancedGCNInstruction &instr) {
  auto it = m_instructionCycles.find(instr.type);
  if (it != m_instructionCycles.end()) {
    return it->second;
  }
  return 1; // Default cycle count for unknown instructions
}

} // namespace ps4