RSID_TCACTUSCANYON_START, 3000,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], <PERSON><PERSON>us<PERSON><PERSON>on\PBTCactusCanyon, 1,
 ,[Offset], CactusCanyon\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON>us<PERSON>anyon\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON>us<PERSON>anyon\InstructionsITAL, 4,
 ,[Offset], <PERSON>actusCanyon\InstructionsGERM, 5,
 ,[Offset], CactusCanyon\InstructionsSPAN, 6,
 ,[Offset], CactusCanyon\InstructionsPORT, 7,
 ,[Offset], CactusCanyon\InstructionsDUTCH, 8,
 ,[Offset], tables\Tales_BG_scroll, 9,
 ,[Offset], <PERSON><PERSON>usCanyon\Pro_TipsENG, 10,
 ,[Offset], CactusCanyon\Pro_TipsFR, 11,
 ,[Offset], CactusCanyon\Pro_TipsITAL, 12,
 ,[Offset], <PERSON>actusCanyon\Pro_TipsGERM, 13,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Pro_TipsSPAN, 14,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Pro_TipsENG, 15,
 ,[Offset], <PERSON><PERSON>us<PERSON><PERSON>on\Pro_TipsENG, 16,
RSID_TCACTUSCANYON_LIGHTS, 3001,,,
RSID_TCACTUSCANYON_CAMERAS, 3002,,,
RSID_TCACTUSCANYON_LAMP_TEXTURES, 3003,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_84_On, 120,
 ,[Offset], L_84_On, 121,
 ,[Offset], L_84_On, 122,
 ,[Offset], L_84_On, 123,
 ,[Offset], L_84_On, 124,
 ,[Offset], L_84_On, 125,
 ,[Offset], L_84_On, 126,
 ,[Offset], L_84_On, 127,
 ,[Offset], F_18_Off, 128,
 ,[Offset], F_18_On, 129,
 ,[Offset], F_19_Off, 130,
 ,[Offset], F_19_On, 131,
 ,[Offset], F_20_Off, 132,
 ,[Offset], F_20_On, 133,
 ,[Offset], F_25_Off, 134,
 ,[Offset], F_25_On, 135,
 ,[Offset], F_26_Off, 136,
 ,[Offset], F_26_On, 137,
 ,[Offset], F_27_Off, 138,
 ,[Offset], F_27_On, 139,
 ,[Offset], F_28_Off, 140,
 ,[Offset], F_28_On, 141,
 ,[Offset], F_19_off_s, 142,
 ,[Offset], F_20_off_s, 143,
 ,[Offset], F_24_On, 144,
 ,[Offset], L_17B_Off, 145,
 ,[Offset], L_17B_On, 146,
 ,[Offset], L_18B_Off, 147,
 ,[Offset], L_18B_On, 148,
RSID_TCACTUSCANYON_TEXTURES, 3004,,,
 ,[Offset], backglass, 0,
 ,[Offset], beer, 1,
 ,[Offset], black_metal, 2,
 ,[Offset], black_orb, 3,
 ,[Offset], black_wood, 4,
 ,[Offset], Bumper_Hamer, 5,
 ,[Offset], Bumper_Sensors, 6,
 ,[Offset], cabinet, 7,
 ,[Offset], cabinet_front, 8,
 ,[Offset], clear_plastics, 9,
 ,[Offset], CoinSlots, 10,
 ,[Offset], cowboy_ramp, 11,
 ,[Offset], cowboy_ramp_metal, 12,
 ,[Offset], Cowboy_t_c, 13,
 ,[Offset], display_frame_generic, 14,
 ,[Offset], Extra_Metal_Parts, 15,
 ,[Offset], Flipper, 16,
 ,[Offset], Flipper_Button, 17,
 ,[Offset], Generic_Metal, 18,
 ,[Offset], HarleyBumperBody, 19,
 ,[Offset], KeepOut_t_c, 20,
 ,[Offset], metal front, 21,
 ,[Offset], Metal_Parts, 22,
 ,[Offset], Metal_Walls, 23,
 ,[Offset], MissingRamp, 24,
 ,[Offset], MissingRampMetals, 25,
 ,[Offset], Orange_Plastic_Post, 26,
 ,[Offset], Plastic_Post_Red, 27,
 ,[Offset], PlasticBumper, 28,
 ,[Offset], plunger, 29,
 ,[Offset], Rails, 30,
 ,[Offset], Ramp1, 31,
 ,[Offset], Ramp1_Parts, 32,
 ,[Offset], Ramp2, 33,
 ,[Offset], Ramp2_Metals, 34,
 ,[Offset], Ramp3_EndSticker, 35,
 ,[Offset], Ramp3_Metal2, 36,
 ,[Offset], Ramp5, 37,
 ,[Offset], Ramp5_Metals, 38,
 ,[Offset], red_light_off, 39,
 ,[Offset], red_light_on, 40,
 ,[Offset], Red_Plastic_Post, 41,
 ,[Offset], Red_Target, 42,
 ,[Offset], rocks_t_c, 43,
 ,[Offset], Rubber Post_Temp, 44,
 ,[Offset], Rubber_Band_Black, 45,
 ,[Offset], Silver Metal Screws_Temp, 46,
 ,[Offset], slingshots_inlanes, 47,
 ,[Offset], speaker, 48,
 ,[Offset], spot_light, 49,
 ,[Offset], train, 50,
 ,[Offset], Train_Ramp, 51,
 ,[Offset], train_ramp_stickers, 52,
 ,[Offset], Train_RampMetals, 53,
 ,[Offset], vertical_plastics, 54,
 ,[Offset], yellow_plastics, 55,
 ,[Offset], pistols_t_c_s, 56,
 ,[Offset], pistols_t_c, 57,
 ,[Offset], cactus_Playfield_bottom, 58,
 ,[Offset], cactus_Playfield_top, 59,
 ,[Offset], bulb1, 60,
 ,[Offset], Harley_Spinner, 61,
 ,[Offset], Harley_Gate, 62,
 ,[Offset], Target_White, 63,
 ,[Offset], Train_t_c, 64,
 ,[Offset], Apron, 65,
 ,[Offset], Drop_Targets, 66,
 ,[Offset], rubberband_temp, 67,
 ,[Offset], plunger_ramp_stickers, 68,
 ,[Offset], Ramp_Stickers, 69,
 ,[Offset], Bulb_Blue, 70,
 ,[Offset], Bulb_Red, 71,
 ,[Offset], Metal_Lamp, 72,
 ,[Offset], Round_Metal, 73,
 ,[Offset], Brushed_Metal, 74,
 ,[Offset], bad_guy, 75,
 ,[Offset], bad_guy_B, 76,
 ,[Offset], bad_guy_C, 77,
 ,[Offset], bad_guy_D, 78,
RSID_TCACTUSCANYON_MODELS, 3005,,,
 ,[Offset], Wooden_Rails, 0,
 ,[Offset], Apron, 1,
 ,[Offset], Back_Plastics, 2,
 ,[Offset], Bulbs, 3,
 ,[Offset], Bumper_Tops, 4,
 ,[Offset], Cowboy, 5,
 ,[Offset], Flipper_Plastics, 6,
 ,[Offset], Habit, 7,
 ,[Offset], KeepOut_Sign, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Rails, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Plastic_Pieces, 12,
 ,[Offset], Plastic_Posts, 13,
 ,[Offset], Ramp_Back_Right, 14,
 ,[Offset], Ramp_Cowboy, 15,
 ,[Offset], Ramp_Left_Flipper, 16,
 ,[Offset], Ramp_Plunger, 17,
 ,[Offset], Ramp_Right_Flipper, 18,
 ,[Offset], Ramp_Train, 19,
 ,[Offset], Rocks, 20,
 ,[Offset], Rubber, 21,
 ,[Offset], Train_Rail, 22,
 ,[Offset], Playfield, 23,
 ,[Offset], Beer, 24,
 ,[Offset], Drop_Target, 25,
 ,[Offset], Flipper, 26,
 ,[Offset], Plunger, 27,
 ,[Offset], Slingshot_Left, 28,
 ,[Offset], Slingshot_Right, 29,
 ,[Offset], Target, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Bumper_Metal, 32,
 ,[Offset], Cabinet, 33,
 ,[Offset], Cabinet_Metals, 34,
 ,[Offset], Backglass, 35,
 ,[Offset], Gate, 36,
 ,[Offset], One_Way_Gate, 37,
 ,[Offset], Ramp_Switch, 38,
 ,[Offset], Stop_Post, 39,
 ,[Offset], Target_Black, 40,
 ,[Offset], Cowboy_Hat, 41,
 ,[Offset], Cowboy_Base, 42,
 ,[Offset], Train, 43,
 ,[Offset], Drop_Target_B, 44,
 ,[Offset], Drop_Target_C, 45,
 ,[Offset], Drop_Target_D, 46,
 ,[Offset], Light_Cutouts, 47,
 ,[Offset], Flashers, 48,
 ,[Offset], Guns, 49,
 ,[Offset], Metal_Braces, 50,
 ,[Offset], Vertical_LightCutouts, 51,
 ,[Offset], Bolts, 52,
 ,[Offset], Ramp_Stickers, 53,
 ,[Offset], Plastic_Side, 54,
 ,[Offset], Metal_Pieces_B, 55,
 ,[Offset], Metal_Pieces_C, 56,
 ,[Offset], Metal_Pieces_D, 57,
 ,[Offset], Metal_Pieces_E, 58,
 ,[Offset], Cabinet_Interior, 59,
 ,[Offset], Cabinet_Interior, 60,
RSID_TCACTUSCANYON_MODELS_LODS, 3006,,,
 ,[Offset], Wooden_Rails, 0,
 ,[Offset], Apron, 1,
 ,[Offset], Back_Plastics, 2,
 ,[Offset], Bulbs, 3,
 ,[Offset], Bumper_Tops, 4,
 ,[Offset], Cowboy, 5,
 ,[Offset], Flipper_Plastics, 6,
 ,[Offset], Habit, 7,
 ,[Offset], KeepOut_Sign, 8,
 ,[Offset], Metal_Pieces, 9,
 ,[Offset], Metal_Rails, 10,
 ,[Offset], Metal_Walls, 11,
 ,[Offset], Plastic_Pieces, 12,
 ,[Offset], Plastic_Posts, 13,
 ,[Offset], Ramp_Back_Right, 14,
 ,[Offset], Ramp_Cowboy, 15,
 ,[Offset], Ramp_Left_Flipper, 16,
 ,[Offset], Ramp_Plunger, 17,
 ,[Offset], Ramp_Right_Flipper, 18,
 ,[Offset], Ramp_Train, 19,
 ,[Offset], Rocks, 20,
 ,[Offset], Rubber, 21,
 ,[Offset], Train_Rail, 22,
 ,[Offset], Playfield, 23,
 ,[Offset], Beer, 24,
 ,[Offset], Drop_Target, 25,
 ,[Offset], Flipper, 26,
 ,[Offset], Plunger, 27,
 ,[Offset], Slingshot_Left, 28,
 ,[Offset], Slingshot_Right, 29,
 ,[Offset], Target, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Bumper_Metal, 32,
 ,[Offset], Cabinet, 33,
 ,[Offset], Cabinet_Metals, 34,
 ,[Offset], Backglass, 35,
 ,[Offset], Gate, 36,
 ,[Offset], One_Way_Gate, 37,
 ,[Offset], Ramp_Switch, 38,
 ,[Offset], Stop_Post, 39,
 ,[Offset], Target_Black, 40,
 ,[Offset], Cowboy_Hat, 41,
 ,[Offset], Cowboy_Base, 42,
 ,[Offset], Train, 43,
 ,[Offset], Drop_Target_B, 44,
 ,[Offset], Drop_Target_C, 45,
 ,[Offset], Drop_Target_D, 46,
 ,[Offset], Light_Cutouts, 47,
 ,[Offset], Flashers, 48,
 ,[Offset], Guns, 49,
 ,[Offset], Metal_Braces, 50,
 ,[Offset], Vertical_LightCutouts, 51,
 ,[Offset], Bolts, 52,
 ,[Offset], Ramp_Stickers, 53,
 ,[Offset], Plastic_Side, 54,
 ,[Offset], Metal_Pieces_B, 55,
 ,[Offset], Metal_Pieces_C, 56,
 ,[Offset], Metal_Pieces_D, 57,
 ,[Offset], Metal_Pieces_E, 58,
 ,[Offset], Cabinet_Interior, 59,
 ,[Offset], Cabinet_Interior, 60,
RSID_TCACTUSCANYON_COLLISION, 3007,,,
 ,[Offset], Ball_Drain, 0,
 ,[Offset], Apron, 1,
 ,[Offset], Cowboy_Walls, 2,
 ,[Offset], Flipper_Lane_Left, 3,
 ,[Offset], Flipper_Lane_Right, 4,
 ,[Offset], Playfield, 5,
 ,[Offset], Plunger_Lane, 6,
 ,[Offset], Plunger_Rest, 7,
 ,[Offset], Ramp_Back_Right, 8,
 ,[Offset], Ramp_Left_Lane, 9,
 ,[Offset], Ramp_Plunger, 10,
 ,[Offset], Ramp_Right_Lane, 11,
 ,[Offset], Ramp_Train, 12,
 ,[Offset], Rubber_Lower_Left, 13,
 ,[Offset], Rubber_Lower_Right, 14,
 ,[Offset], Rubber_Mid_Left, 15,
 ,[Offset], Rubber_Mid_Right, 16,
 ,[Offset], Slingshot_Left, 17,
 ,[Offset], Slingshot_Left_Front, 18,
 ,[Offset], Slingshot_Right, 19,
 ,[Offset], Slingshot_Right_Front, 20,
 ,[Offset], Train_Rail, 21,
 ,[Offset], Wall_Back, 22,
 ,[Offset], Wall_Right, 23,
 ,[Offset], Wall_Upper_Left, 24,
 ,[Offset], Flipper_Left_Back, 25,
 ,[Offset], Flipper_Left_Front, 26,
 ,[Offset], Flipper_Right_Back, 27,
 ,[Offset], Flipper_Right_Front, 28,
 ,[Offset], Plunger_Moving, 29,
 ,[Offset], Target, 30,
 ,[Offset], Drop_Target, 31,
 ,[Offset], Pop_Bumper, 32,
 ,[Offset], Slingshot_Back, 33,
 ,[Offset], Gate, 34,
 ,[Offset], One_Way_Gate_Back, 35,
 ,[Offset], One_Way_Gate_Front, 36,
 ,[Offset], Opto, 37,
 ,[Offset], Stop_Post, 38,
 ,[Offset], Target_Black, 39,
 ,[Offset], Cowboy_Trap, 40,
 ,[Offset], Cowboy_Trough, 41,
 ,[Offset], Mountain_Trap, 42,
 ,[Offset], Mountain_Trough, 43,
 ,[Offset], Cowboy_Launch, 44,
 ,[Offset], Cowboy, 45,
 ,[Offset], Scoop, 46,
 ,[Offset], Bottom_Bowl, 47,
RSID_TCACTUSCANYON_PLACEMENT, 3008,,,
RSID_TCACTUSCANYON_EMUROM, 3009,,,
 ,[Offset], cc_13, 0,
 ,[Offset], cc_13, 1,
 ,[Offset], cc_13, 2,
 ,[Offset], cc_13, 3,
 ,[Offset], cc_13, 4,
 ,[Offset], cc_default, 5,
RSID_TCACTUSCANYON_SOUNDS_START, 3010,,,
RSID_TCACTUSCANYON_EMU_SOUNDS, 3011,,,
 ,[Offset], S0001-LP1, 0,
 ,[Offset], S0001-LP2, 1,
 ,[Offset], S0002-LP, 2,
 ,[Offset], S0003-LP, 3,
 ,[Offset], S0004-LP1, 4,
 ,[Offset], S0004-LP2, 5,
 ,[Offset], S0005-LP, 6,
 ,[Offset], S0006_C3, 7,
 ,[Offset], S0007-LP, 8,
 ,[Offset], S0008-LP, 9,
 ,[Offset], S0009-LP1, 10,
 ,[Offset], S000B_C4, 11,
 ,[Offset], S000D_C4, 12,
 ,[Offset], S000F_C4, 13,
 ,[Offset], S0011_C4, 14,
 ,[Offset], S0014_C1, 15,
 ,[Offset], S0015_C3, 16,
 ,[Offset], S0016-LP, 17,
 ,[Offset], S0017_C4, 18,
 ,[Offset], S0019-LP1, 19,
 ,[Offset], S001A-LP, 20,
 ,[Offset], S001B-LP1, 21,
 ,[Offset], S001B-LP2, 22,
 ,[Offset], S001C-LP, 23,
 ,[Offset], S001D_C4, 24,
 ,[Offset], S001F-LP, 25,
 ,[Offset], S0020_C4, 26,
 ,[Offset], S0022_C4, 27,
 ,[Offset], S0024_C4, 28,
 ,[Offset], S0026_C4, 29,
 ,[Offset], S0029_C4, 30,
 ,[Offset], S002A-LP1, 31,
 ,[Offset], S002A-LP2, 32,
 ,[Offset], S002B_C3, 33,
 ,[Offset], S002C-LP1, 34,
 ,[Offset], S002D_C1, 35,
 ,[Offset], S002F-LP1, 36,
 ,[Offset], S0030_C4, 37,
 ,[Offset], S0032_C4, 38,
 ,[Offset], S0034_C4, 39,
 ,[Offset], S0036_C4, 40,
 ,[Offset], S0038_C4, 41,
 ,[Offset], S003A_C4, 42,
 ,[Offset], S003C_C4, 43,
 ,[Offset], S0042_C4, 44,
 ,[Offset], S0044_C4, 45,
 ,[Offset], S0046_C4, 46,
 ,[Offset], S0048_C3, 47,
 ,[Offset], S004A_C4, 48,
 ,[Offset], S004B_C4, 49,
 ,[Offset], S004D_C4, 50,
 ,[Offset], S004F_C4, 51,
 ,[Offset], S0051_C4, 52,
 ,[Offset], S0057_C4, 53,
 ,[Offset], S0059_C4, 54,
 ,[Offset], S005B_C4, 55,
 ,[Offset], S005F-LP, 56,
 ,[Offset], S0060-LP, 57,
 ,[Offset], S0062-LP, 58,
 ,[Offset], S0063-LP1, 59,
 ,[Offset], S0063-LP2, 60,
 ,[Offset], S0064_C1, 61,
 ,[Offset], S0065_C4, 62,
 ,[Offset], S0067_C4, 63,
 ,[Offset], S0069_C1, 64,
 ,[Offset], S006B_C1, 65,
 ,[Offset], S006D_C4, 66,
 ,[Offset], S006F_C4, 67,
 ,[Offset], S0071_C4, 68,
 ,[Offset], S0073_C1, 69,
 ,[Offset], S0075_C1, 70,
 ,[Offset], S0077_C4, 71,
 ,[Offset], S0079_C4, 72,
 ,[Offset], S007B_C4, 73,
 ,[Offset], S007D_C4, 74,
 ,[Offset], S007F_C4, 75,
 ,[Offset], S0081_C4, 76,
 ,[Offset], S0083_C1, 77,
 ,[Offset], S0085_C4, 78,
 ,[Offset], S0087_C4, 79,
 ,[Offset], S0089_C4, 80,
 ,[Offset], S008D_C4, 81,
 ,[Offset], S008F_C4, 82,
 ,[Offset], S0091_C4, 83,
 ,[Offset], S0093_C4, 84,
 ,[Offset], S0095_C1, 85,
 ,[Offset], S0097_C1, 86,
 ,[Offset], S009B_C4, 87,
 ,[Offset], S009D_C4, 88,
 ,[Offset], S009F_C4, 89,
 ,[Offset], S00A1_C1, 90,
 ,[Offset], S00A3_C1, 91,
 ,[Offset], S00A5_C1, 92,
 ,[Offset], S00A7_C1, 93,
 ,[Offset], S00A9_C1, 94,
 ,[Offset], S00AB_C1, 95,
 ,[Offset], S00AD_C6, 96,
 ,[Offset], S00AF_C6, 97,
 ,[Offset], S00B3_C4, 98,
 ,[Offset], S00B5_C1, 99,
 ,[Offset], S00B7_C4, 100,
 ,[Offset], S00C3_C4, 101,
 ,[Offset], S00C5_C4, 102,
 ,[Offset], S00C7_C1, 103,
 ,[Offset], S00C9_C4, 104,
 ,[Offset], S00CB_C4, 105,
 ,[Offset], S00CD_C1, 106,
 ,[Offset], S00CF_C4, 107,
 ,[Offset], S00D1_C4, 108,
 ,[Offset], S00D5_C4, 109,
 ,[Offset], S00D9_C4, 110,
 ,[Offset], S00DD_C4, 111,
 ,[Offset], S00E5_C4, 112,
 ,[Offset], S00EB_C1, 113,
 ,[Offset], S00F1_C1, 114,
 ,[Offset], S00F3_C1, 115,
 ,[Offset], S00F7_C4, 116,
 ,[Offset], S00F9_C1, 117,
 ,[Offset], S00FB_C1, 118,
 ,[Offset], S00FD_C1, 119,
 ,[Offset], S00FF_C4, 120,
 ,[Offset], S0101_C4, 121,
 ,[Offset], S0103_C1, 122,
 ,[Offset], S0105_C1, 123,
 ,[Offset], S0107_C1, 124,
 ,[Offset], S010B_C1, 125,
 ,[Offset], S010F_C4, 126,
 ,[Offset], S0111_C4, 127,
 ,[Offset], S0115_C1, 128,
 ,[Offset], S0117_C1, 129,
 ,[Offset], S011B_C4, 130,
 ,[Offset], S011D_C1, 131,
 ,[Offset], S011F_C4, 132,
 ,[Offset], S0121_C4, 133,
 ,[Offset], S0123_C4, 134,
 ,[Offset], S0125_C1, 135,
 ,[Offset], S0127_C1, 136,
 ,[Offset], S0129_C4, 137,
 ,[Offset], S012B_C4, 138,
 ,[Offset], S012D_C4, 139,
 ,[Offset], S0135_C5, 140,
 ,[Offset], S0136_C4, 141,
 ,[Offset], S0137_C1, 142,
 ,[Offset], S0139_C1, 143,
 ,[Offset], S013B_C1, 144,
 ,[Offset], S013D_C1, 145,
 ,[Offset], S013F_C4, 146,
 ,[Offset], S0141_C4, 147,
 ,[Offset], S0143_C4, 148,
 ,[Offset], S0153_C4, 149,
 ,[Offset], S0155_C4, 150,
 ,[Offset], S015B_C1, 151,
 ,[Offset], S015D_C4, 152,
 ,[Offset], S015F_C4, 153,
 ,[Offset], S0161_C4, 154,
 ,[Offset], S0165_C4, 155,
 ,[Offset], S0187_C4, 156,
 ,[Offset], S0189_C5, 157,
 ,[Offset], S0197_C4, 158,
 ,[Offset], S01F5_C6, 159,
 ,[Offset], S01F6_C6, 160,
 ,[Offset], S01F7_C6, 161,
 ,[Offset], S01F8_C6, 162,
 ,[Offset], S01F9_C6, 163,
 ,[Offset], S01FA_C6, 164,
 ,[Offset], S01FD_C6, 165,
 ,[Offset], S01FF_C6, 166,
 ,[Offset], S0200_C6, 167,
 ,[Offset], S0201_C6, 168,
 ,[Offset], S0206_C6, 169,
 ,[Offset], S0207_C6, 170,
 ,[Offset], S0209_C6, 171,
 ,[Offset], S0210_C6, 172,
 ,[Offset], S0211_C6, 173,
 ,[Offset], S0212_C6, 174,
 ,[Offset], S0213_C6, 175,
 ,[Offset], S0214_C6, 176,
 ,[Offset], S0215_C6, 177,
 ,[Offset], S0218_C6, 178,
 ,[Offset], S021A_C6, 179,
 ,[Offset], S021B_C6, 180,
 ,[Offset], S021C_C6, 181,
 ,[Offset], S021D_C6, 182,
 ,[Offset], S021E_C6, 183,
 ,[Offset], S021F_C6, 184,
 ,[Offset], S0220_C6, 185,
 ,[Offset], S0264_C6, 186,
 ,[Offset], S0265_C6, 187,
 ,[Offset], S0266_C6, 188,
 ,[Offset], S0267_C6, 189,
 ,[Offset], S026E_C6, 190,
 ,[Offset], S026F_C6, 191,
 ,[Offset], S0270_C6, 192,
 ,[Offset], S02BC_C1, 193,
 ,[Offset], S02BE_C1, 194,
 ,[Offset], S0322_C6, 195,
 ,[Offset], S0323_C6, 196,
 ,[Offset], S0329_C6, 197,
 ,[Offset], S032A_C6, 198,
 ,[Offset], S032B_C6, 199,
 ,[Offset], S032E_C6, 200,
 ,[Offset], S032F_C6, 201,
 ,[Offset], S0330_C6, 202,
 ,[Offset], S0331_C6, 203,
 ,[Offset], S0332_C6, 204,
 ,[Offset], S0333_C6, 205,
 ,[Offset], S0334_C6, 206,
 ,[Offset], S0335_C6, 207,
 ,[Offset], S0336_C6, 208,
 ,[Offset], S0337_C6, 209,
 ,[Offset], S0338_C6, 210,
 ,[Offset], S0339_C6, 211,
 ,[Offset], S033D_C6, 212,
 ,[Offset], S033E_C6, 213,
 ,[Offset], S033F_C6, 214,
 ,[Offset], S0343_C6, 215,
 ,[Offset], S0344_C6, 216,
 ,[Offset], S0345_C6, 217,
 ,[Offset], S0346_C6, 218,
 ,[Offset], S0347_C6, 219,
 ,[Offset], S0348_C6, 220,
 ,[Offset], S034A_C6, 221,
 ,[Offset], S034F_C6, 222,
 ,[Offset], S0351_C6, 223,
 ,[Offset], S0352_C6, 224,
 ,[Offset], S0353_C6, 225,
 ,[Offset], S0354_C6, 226,
 ,[Offset], S0356_C6, 227,
 ,[Offset], S0357_C6, 228,
 ,[Offset], S0358_C6, 229,
 ,[Offset], S0359_C6, 230,
 ,[Offset], S035A_C6, 231,
 ,[Offset], S035B_C6, 232,
 ,[Offset], S035C_C6, 233,
 ,[Offset], S035D_C6, 234,
 ,[Offset], S035E_C6, 235,
 ,[Offset], S035F_C6, 236,
 ,[Offset], S0360_C6, 237,
 ,[Offset], S0366_C6, 238,
 ,[Offset], S0367_C6, 239,
 ,[Offset], S0368_C6, 240,
 ,[Offset], S0369_C6, 241,
 ,[Offset], S036A_C6, 242,
 ,[Offset], S036B_C6, 243,
 ,[Offset], S036C_C6, 244,
 ,[Offset], S036D_C6, 245,
 ,[Offset], S036E_C6, 246,
 ,[Offset], S036F_C6, 247,
 ,[Offset], S03D4_C-1, 248,
 ,[Offset], S03D5_C-1, 249,
 ,[Offset], S03D6_C5, 250,
 ,[Offset], S03D7_C5, 251,
 ,[Offset], S03D8_C5, 252,
 ,[Offset], S03D9_C-1, 253,
 ,[Offset], S03DA_C-1, 254,
 ,[Offset], S03DB_C5, 255,
 ,[Offset], S03DC_C5, 256,
 ,[Offset], S03DD_C5, 257,
 ,[Offset], S03DE_C-1, 258,
 ,[Offset], S03EB_C6, 259,
 ,[Offset], S03EC_C6, 260,
 ,[Offset], S03ED_C6, 261,
 ,[Offset], S03EE_C6, 262,
 ,[Offset], S03EF_C6, 263,
 ,[Offset], S03F0_C6, 264,
 ,[Offset], S03F2_C6, 265,
 ,[Offset], S03F4_C6, 266,
 ,[Offset], S03F5_C6, 267,
 ,[Offset], S03F6_C6, 268,
 ,[Offset], S03FC_C6, 269,
 ,[Offset], S0401_C6, 270,
 ,[Offset], S0402_C6, 271,
 ,[Offset], S0403_C6, 272,
 ,[Offset], S0404_C6, 273,
 ,[Offset], S0405_C6, 274,
 ,[Offset], S0406_C6, 275,
 ,[Offset], S0407_C6, 276,
 ,[Offset], S0408_C6, 277,
 ,[Offset], S0409_C6, 278,
 ,[Offset], S040A_C6, 279,
 ,[Offset], S040C_C6, 280,
 ,[Offset], S040D_C6, 281,
 ,[Offset], S040E_C6, 282,
 ,[Offset], S040F_C6, 283,
 ,[Offset], S0410_C6, 284,
 ,[Offset], S0411_C6, 285,
 ,[Offset], S0412_C6, 286,
 ,[Offset], S0413_C6, 287,
 ,[Offset], S044C_C6, 288,
 ,[Offset], S044D_C6, 289,
 ,[Offset], S0456_C6, 290,
 ,[Offset], S0457_C6, 291,
 ,[Offset], S045C_C6, 292,
 ,[Offset], S045D_C6, 293,
 ,[Offset], S045E_C6, 294,
 ,[Offset], S045F_C6, 295,
 ,[Offset], S0460_C6, 296,
 ,[Offset], S0468_C6, 297,
 ,[Offset], S0469_C6, 298,
 ,[Offset], S046A_C6, 299,
 ,[Offset], S046C_C6, 300,
 ,[Offset], S046D_C6, 301,
 ,[Offset], S046E_C6, 302,
 ,[Offset], S046F_C6, 303,
 ,[Offset], S0474_C6, 304,
 ,[Offset], S0476_C6, 305,
 ,[Offset], S0477_C6, 306,
 ,[Offset], S0478_C6, 307,
 ,[Offset], S0479_C6, 308,
 ,[Offset], S047A_C6, 309,
 ,[Offset], S047B_C6, 310,
 ,[Offset], S047E_C6, 311,
 ,[Offset], S047F_C6, 312,
 ,[Offset], S0480_C6, 313,
 ,[Offset], S0481_C6, 314,
 ,[Offset], S0483_C6, 315,
 ,[Offset], S0484_C6, 316,
 ,[Offset], S0485_C6, 317,
 ,[Offset], S048D_C6, 318,
 ,[Offset], S048F_C6, 319,
 ,[Offset], S0491_C6, 320,
 ,[Offset], S0492_C6, 321,
 ,[Offset], S0493_C6, 322,
 ,[Offset], S0494_C6, 323,
 ,[Offset], S0495_C6, 324,
 ,[Offset], S0497_C6, 325,
 ,[Offset], S049F_C6, 326,
 ,[Offset], S04A1_C6, 327,
 ,[Offset], S04A2_C6, 328,
 ,[Offset], S04A3_C6, 329,
 ,[Offset], S04A5_C6, 330,
 ,[Offset], S04A9_C6, 331,
 ,[Offset], S04AC_C6, 332,
 ,[Offset], S04AE_C6, 333,
 ,[Offset], S04AF_C6, 334,
 ,[Offset], S04B0_C6, 335,
 ,[Offset], S04B2_C6, 336,
 ,[Offset], S04B5_C6, 337,
 ,[Offset], S04B9_C6, 338,
 ,[Offset], S04BA_C6, 339,
 ,[Offset], S04C2_C6, 340,
 ,[Offset], S04CB_C6, 341,
 ,[Offset], S04D1_C6, 342,
 ,[Offset], S04D2_C6, 343,
 ,[Offset], S04D5_C6, 344,
 ,[Offset], S04D6_C6, 345,
 ,[Offset], S04D9_C6, 346,
 ,[Offset], S04DB_C6, 347,
 ,[Offset], S04DC_C6, 348,
 ,[Offset], S04DE_C6, 349,
 ,[Offset], S04DF_C6, 350,
 ,[Offset], S04E0_C6, 351,
 ,[Offset], S04E1_C6, 352,
 ,[Offset], S04E4_C6, 353,
 ,[Offset], S04E6_C6, 354,
 ,[Offset], S04E7_C6, 355,
 ,[Offset], S04E8_C6, 356,
 ,[Offset], S04EA_C6, 357,
 ,[Offset], S04EB_C6, 358,
 ,[Offset], S04EC_C6, 359,
 ,[Offset], S04F1_C6, 360,
 ,[Offset], S04F2_C6, 361,
 ,[Offset], S04F3_C6, 362,
 ,[Offset], S04F4_C6, 363,
 ,[Offset], S04F5_C6, 364,
 ,[Offset], S04F7_C6, 365,
 ,[Offset], S04FA_C6, 366,
 ,[Offset], S04FD_C6, 367,
 ,[Offset], S0500_C6, 368,
 ,[Offset], S0517_C6, 369,
 ,[Offset], S0518_C6, 370,
 ,[Offset], S0519_C6, 371,
 ,[Offset], S051A_C6, 372,
 ,[Offset], S051B_C6, 373,
 ,[Offset], S051C_C6, 374,
 ,[Offset], S051D_C6, 375,
 ,[Offset], S0523_C6, 376,
 ,[Offset], S0524_C6, 377,
 ,[Offset], S0525_C6, 378,
 ,[Offset], S0526_C6, 379,
 ,[Offset], S0529_C6, 380,
 ,[Offset], S052C_C6, 381,
 ,[Offset], S0579_C6, 382,
 ,[Offset], S057E_C6, 383,
 ,[Offset], S0580_C6, 384,
 ,[Offset], S0581_C6, 385,
 ,[Offset], S0583_C6, 386,
 ,[Offset], S0586_C6, 387,
 ,[Offset], S0588_C6, 388,
 ,[Offset], S058A_C6, 389,
 ,[Offset], S058D_C6, 390,
 ,[Offset], S058F_C6, 391,
 ,[Offset], S0591_C6, 392,
 ,[Offset], S0593_C6, 393,
 ,[Offset], S0594_C6, 394,
 ,[Offset], S0596_C6, 395,
 ,[Offset], S0597_C6, 396,
 ,[Offset], S0598_C6, 397,
 ,[Offset], S0599_C6, 398,
 ,[Offset], S05AA_C6, 399,
 ,[Offset], S05AB_C6, 400,
 ,[Offset], S05AC_C6, 401,
 ,[Offset], S05AE_C6, 402,
 ,[Offset], S05B0_C6, 403,
 ,[Offset], S05B1_C6, 404,
 ,[Offset], S05B4_C6, 405,
 ,[Offset], S05B6_C6, 406,
 ,[Offset], S05B7_C6, 407,
 ,[Offset], S05B8_C6, 408,
 ,[Offset], S05B9_C6, 409,
 ,[Offset], S05BA_C6, 410,
 ,[Offset], S05BB_C6, 411,
 ,[Offset], S05BC_C6, 412,
 ,[Offset], S05BD_C6, 413,
 ,[Offset], S05BE_C6, 414,
 ,[Offset], S05C0_C6, 415,
 ,[Offset], S05DD_C6, 416,
 ,[Offset], S05DF_C6, 417,
 ,[Offset], S05E0_C6, 418,
 ,[Offset], S05E1_C6, 419,
 ,[Offset], S05E2_C6, 420,
 ,[Offset], S05E3_C6, 421,
 ,[Offset], S05E4_C6, 422,
 ,[Offset], S05E5_C6, 423,
 ,[Offset], S05E6_C6, 424,
 ,[Offset], S05E7_C6, 425,
 ,[Offset], S05EF_C6, 426,
 ,[Offset], S05F0_C6, 427,
 ,[Offset], S05F5_C6, 428,
 ,[Offset], S05FD_C6, 429,
 ,[Offset], S05FF_C6, 430,
 ,[Offset], S0602_C6, 431,
 ,[Offset], S0605_C6, 432,
 ,[Offset], S0606_C6, 433,
 ,[Offset], S0608_C6, 434,
 ,[Offset], S0609_C6, 435,
 ,[Offset], S060A_C6, 436,
 ,[Offset], S060B_C6, 437,
 ,[Offset], S060C_C6, 438,
 ,[Offset], S060D_C6, 439,
 ,[Offset], S060E_C6, 440,
 ,[Offset], S060F_C6, 441,
 ,[Offset], S0610_C6, 442,
 ,[Offset], S0611_C6, 443,
 ,[Offset], S0612_C6, 444,
 ,[Offset], S0613_C6, 445,
 ,[Offset], S0614_C6, 446,
 ,[Offset], S0615_C6, 447,
 ,[Offset], S0616_C6, 448,
 ,[Offset], S0617_C6, 449,
 ,[Offset], S0618_C6, 450,
 ,[Offset], S0619_C6, 451,
 ,[Offset], S061A_C6, 452,
 ,[Offset], S061B_C6, 453,
 ,[Offset], S061C_C6, 454,
 ,[Offset], S061D_C6, 455,
 ,[Offset], S061E_C6, 456,
 ,[Offset], S061F_C6, 457,
 ,[Offset], S0620_C6, 458,
 ,[Offset], S0621_C6, 459,
 ,[Offset], S0622_C6, 460,
 ,[Offset], S0623_C6, 461,
 ,[Offset], S0624_C6, 462,
 ,[Offset], S0625_C6, 463,
 ,[Offset], S0626_C6, 464,
 ,[Offset], S0627_C6, 465,
 ,[Offset], S0628_C6, 466,
 ,[Offset], S0629_C6, 467,
 ,[Offset], S062A_C6, 468,
 ,[Offset], S062B_C6, 469,
 ,[Offset], S062C_C6, 470,
 ,[Offset], S062D_C6, 471,
 ,[Offset], S062E_C6, 472,
 ,[Offset], S062F_C6, 473,
 ,[Offset], S0630_C6, 474,
 ,[Offset], S0631_C6, 475,
 ,[Offset], S0632_C6, 476,
 ,[Offset], S0709_C6, 477,
 ,[Offset], S070B_C6, 478,
 ,[Offset], S070C_C6, 479,
 ,[Offset], S070D_C6, 480,
 ,[Offset], S070E_C6, 481,
 ,[Offset], S070F_C6, 482,
 ,[Offset], S0710_C6, 483,
 ,[Offset], S0711_C6, 484,
 ,[Offset], S0712_C6, 485,
 ,[Offset], S0714_C6, 486,
 ,[Offset], S0715_C6, 487,
 ,[Offset], S0716_C6, 488,
 ,[Offset], S071C_C6, 489,
 ,[Offset], S071D_C6, 490,
 ,[Offset], S071F_C6, 491,
 ,[Offset], S0720_C6, 492,
 ,[Offset], S073A_C6, 493,
 ,[Offset], S073B_C6, 494,
 ,[Offset], S073C_C6, 495,
 ,[Offset], S073D_C6, 496,
 ,[Offset], S073F_C6, 497,
 ,[Offset], S0740_C6, 498,
 ,[Offset], S0741_C6, 499,
 ,[Offset], S0743_C6, 500,
 ,[Offset], S0744_C6, 501,
 ,[Offset], S0745_C6, 502,
 ,[Offset], S0746_C6, 503,
 ,[Offset], S0748_C6, 504,
 ,[Offset], S0749_C6, 505,
 ,[Offset], S074A_C6, 506,
 ,[Offset], S074B_C6, 507,
 ,[Offset], S074C_C6, 508,
 ,[Offset], S074D_C6, 509,
 ,[Offset], S074E_C6, 510,
 ,[Offset], S074F_C6, 511,
 ,[Offset], S076C_C6, 512,
 ,[Offset], S076D_C6, 513,
 ,[Offset], S076E_C6, 514,
 ,[Offset], S076F_C6, 515,
 ,[Offset], S0770_C6, 516,
 ,[Offset], S0771_C6, 517,
 ,[Offset], S0772_C6, 518,
 ,[Offset], S0775_C6, 519,
 ,[Offset], S0776_C6, 520,
 ,[Offset], S0778_C6, 521,
 ,[Offset], S077A_C6, 522,
 ,[Offset], S077B_C6, 523,
 ,[Offset], S077C_C6, 524,
 ,[Offset], S077D_C6, 525,
 ,[Offset], S077E_C6, 526,
 ,[Offset], S077F_C6, 527,
 ,[Offset], S079E_C6, 528,
 ,[Offset], S079F_C6, 529,
 ,[Offset], S07A0_C6, 530,
 ,[Offset], S07A1_C6, 531,
 ,[Offset], S07A3_C6, 532,
 ,[Offset], S07A4_C6, 533,
 ,[Offset], S07A7_C6, 534,
 ,[Offset], S07AA_C6, 535,
 ,[Offset], S07AE_C4, 536,
 ,[Offset], S07D2_C6, 537,
 ,[Offset], S07D3_C6, 538,
 ,[Offset], S07D5_C6, 539,
 ,[Offset], S07D7_C6, 540,
 ,[Offset], S07D8_C6, 541,
 ,[Offset], S07DD_C6, 542,
 ,[Offset], S07DF_C6, 543,
 ,[Offset], S07E1_C6, 544,
 ,[Offset], S07E2_C6, 545,
 ,[Offset], S07E4_C6, 546,
 ,[Offset], S07E5_C6, 547,
 ,[Offset], S07E7_C6, 548,
 ,[Offset], S07E8_C6, 549,
 ,[Offset], S07E9_C6, 550,
 ,[Offset], S07EA_C6, 551,
 ,[Offset], S07EB_C6, 552,
 ,[Offset], S07F8_C6, 553,
 ,[Offset], S07F9_C6, 554,
 ,[Offset], S07FA_C6, 555,
 ,[Offset], S0834_C6, 556,
 ,[Offset], S0835_C6, 557,
 ,[Offset], S0836_C6, 558,
 ,[Offset], S0837_C6, 559,
 ,[Offset], S0838_C6, 560,
 ,[Offset], S0839_C6, 561,
 ,[Offset], S083A_C6, 562,
 ,[Offset], S083B_C6, 563,
 ,[Offset], S083C_C6, 564,
 ,[Offset], S083D_C6, 565,
 ,[Offset], S083E_C6, 566,
 ,[Offset], S083F_C6, 567,
 ,[Offset], S0841_C6, 568,
 ,[Offset], S0843_C6, 569,
 ,[Offset], S0844_C6, 570,
 ,[Offset], S0846_C6, 571,
 ,[Offset], S0847_C6, 572,
 ,[Offset], S0848_C6, 573,
 ,[Offset], S084A_C6, 574,
 ,[Offset], S084B_C6, 575,
 ,[Offset], S084C_C6, 576,
 ,[Offset], S0898_C6, 577,
 ,[Offset], S0899_C6, 578,
 ,[Offset], S089A_C6, 579,
 ,[Offset], S089B_C6, 580,
 ,[Offset], S089C_C6, 581,
 ,[Offset], S089D_C6, 582,
 ,[Offset], S089E_C6, 583,
 ,[Offset], S089F_C6, 584,
 ,[Offset], S08A0_C6, 585,
 ,[Offset], S08A1_C6, 586,
 ,[Offset], S08A2_C6, 587,
 ,[Offset], S08A3_C6, 588,
 ,[Offset], S08A4_C6, 589,
 ,[Offset], S08A5_C6, 590,
 ,[Offset], S08A6_C6, 591,
 ,[Offset], S08A7_C6, 592,
 ,[Offset], S08A8_C6, 593,
 ,[Offset], S08A9_C6, 594,
 ,[Offset], S08AA_C6, 595,
 ,[Offset], S08AB_C6, 596,
 ,[Offset], S08AC_C6, 597,
 ,[Offset], S08AD_C6, 598,
 ,[Offset], S08AE_C6, 599,
 ,[Offset], S08AF_C6, 600,
 ,[Offset], S08B0_C6, 601,
 ,[Offset], S08B1_C6, 602,
 ,[Offset], S08B2_C6, 603,
 ,[Offset], S08B3_C6, 604,
 ,[Offset], S08B4_C6, 605,
 ,[Offset], S08B5_C6, 606,
 ,[Offset], S08B6_C6, 607,
RSID_TCACTUSCANYON_MECH_SOUNDS, 3012,,,
 ,[Offset], bad_guy_down, 0,
 ,[Offset], bad_guy_up, 1,
 ,[Offset], cowboy, 2,
 ,[Offset], inlane_post_down, 3,
 ,[Offset], inlane_post_up, 4,
 ,[Offset], mine_down, 5,
 ,[Offset], mine_up, 6,
 ,[Offset], train_fast, 7,
 ,[Offset], train_slow, 8,
RSID_TCACTUSCANYON_SOUNDS_END, 3013,,,
RSID_TCACTUSCANYON_SAMPLES, 3014,,,
RSID_TCACTUSCANYON_END, 3015,,,
