RSID_TBIGSHOT_START, 3500,,,
 ,[Offset], BIG_SHOT_FLYER_1, 0,
 ,[Offset], BigShot\PBTBigShot, 1,
 ,[Offset], BigShot\InstructionsENG, 2,
 ,[Offset], BigShot\InstructionsFR, 3,
 ,[Offset], BigShot\InstructionsITAL, 4,
 ,[Offset], BigShot\InstructionsGERM, 5,
 ,[Offset], BigShot\InstructionsSPAN, 6,
 ,[Offset], BigShot\InstructionsPORT, 7,
 ,[Offset], BigShot\InstructionsDUTCH, 8,
 ,[Offset], tables\BigShot_BG_scroll, 9,
RSID_TBIGSHOT_LIGHTS, 3501,,,
RSID_TBIGSHOT_CAMERAS, 3502,,,
RSID_TBIGSHOT_LAMP_TEXTURES, 3503,,,
 ,[Offset], button 1, 0,
 ,[Offset], button 1 lit, 1,
 ,[Offset], button 2, 2,
 ,[Offset], button 2 lit, 3,
 ,[Offset], button 3, 4,
 ,[Offset], button 3 lit, 5,
 ,[Offset], ball01, 6,
 ,[Offset], ball01 lit, 7,
 ,[Offset], ball02, 8,
 ,[Offset], ball02 lit, 9,
 ,[Offset], ball03, 10,
 ,[Offset], ball03 lit, 11,
 ,[Offset], ball04, 12,
 ,[Offset], ball04 lit, 13,
 ,[Offset], ball05, 14,
 ,[Offset], ball05 lit, 15,
 ,[Offset], ball06, 16,
 ,[Offset], ball06 lit, 17,
 ,[Offset], ball07, 18,
 ,[Offset], ball07 lit, 19,
 ,[Offset], ball08, 20,
 ,[Offset], ball08 lit, 21,
 ,[Offset], ball09, 22,
 ,[Offset], ball09 lit, 23,
 ,[Offset], ball10, 24,
 ,[Offset], ball10 lit, 25,
 ,[Offset], ball11, 26,
 ,[Offset], ball11 lit, 27,
 ,[Offset], ball12, 28,
 ,[Offset], ball12 lit, 29,
 ,[Offset], ball13, 30,
 ,[Offset], ball13 lit, 31,
 ,[Offset], ball14, 32,
 ,[Offset], ball14 lit, 33,
 ,[Offset], ball15, 34,
 ,[Offset], ball15 lit, 35,
 ,[Offset], light, 36,
 ,[Offset], light lit, 37,
 ,[Offset], light r, 38,
 ,[Offset], light lit r, 39,
 ,[Offset], sp light, 40,
 ,[Offset], sp light lit, 41,
 ,[Offset], sp light r, 42,
 ,[Offset], sp light lit r, 43,
RSID_TBIGSHOT_TEXTURES, 3504,,,
 ,[Offset], env, 0,
 ,[Offset], env ball, 1,
 ,[Offset], env, 2,
 ,[Offset], pinball_t1_A, 3,
 ,[Offset], pinball_t1_B, 4,
 ,[Offset], pinball_t1_C, 5,
 ,[Offset], pinball_t1_D, 6,
 ,[Offset], screw alt, 7,
 ,[Offset], red_base, 8,
 ,[Offset], red_base_t, 9,
 ,[Offset], top_wheel, 10,
 ,[Offset], wt_base, 11,
 ,[Offset], wt_base_t, 12,
 ,[Offset], metal_trim, 13,
 ,[Offset], upper_bummper, 14,
 ,[Offset], pinball_t2_A, 15,
 ,[Offset], pinball_t2_B, 16,
 ,[Offset], pinball_t2_C, 17,
 ,[Offset], pinball_t2_D, 18,
 ,[Offset], fingerplate, 19,
 ,[Offset], Wood_Tile, 20,
 ,[Offset], green_base, 21,
 ,[Offset], TableRules_R, 22,
 ,[Offset], TableRules_L, 23,
 ,[Offset], backglass, 24,
 ,[Offset], metal_tex, 25,
 ,[Offset], metal_side, 26,
 ,[Offset], metal-parts01 copy, 27,
 ,[Offset], coin, 28,
 ,[Offset], wood, 29,
 ,[Offset], frame, 30,
 ,[Offset], metal front, 31,
 ,[Offset], log, 32,
 ,[Offset], plunger, 33,
 ,[Offset], pinball_sidegraphic, 34,
 ,[Offset], dryWall, 35,
 ,[Offset], wood_ceilling, 36,
 ,[Offset], PinballSign, 37,
 ,[Offset], sidewalk01, 38,
 ,[Offset], WoodTrim_Decor, 39,
 ,[Offset], bar-reflection01, 40,
 ,[Offset], back_box, 41,
 ,[Offset], backglass, 42,
 ,[Offset], bH_frontplate, 43,
 ,[Offset], cabinet, 44,
 ,[Offset], cabinet_metal, 45,
 ,[Offset], coin_box, 46,
 ,[Offset], Metal_Parts, 47,
 ,[Offset], Plunger, 48,
 ,[Offset], Plunger_Plate, 49,
 ,[Offset], 8ballhole, 50,
 ,[Offset], checkered_plastic, 51,
 ,[Offset], flipper, 52,
 ,[Offset], metal, 53,
 ,[Offset], nut, 54,
 ,[Offset], onewaygate, 55,
 ,[Offset], plastic_gates, 56,
 ,[Offset], posts, 57,
 ,[Offset], rails, 58,
 ,[Offset], Silver_screws, 59,
 ,[Offset], stopper, 60,
 ,[Offset], upper_apron, 61,
 ,[Offset], upper_bummper, 62,
 ,[Offset], rubber, 63,
 ,[Offset], tile, 64,
 ,[Offset], pop_bumper, 65,
RSID_TBIGSHOT_MODELS, 3505,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper C extended, 3,
 ,[Offset], bumper C, 4,
 ,[Offset], bumper D extended, 5,
 ,[Offset], bumper D, 6,
 ,[Offset], bumper E, 7,
 ,[Offset], bumper F, 8,
 ,[Offset], bumper G, 9,
 ,[Offset], complete, 10,
 ,[Offset], empty, 11,
 ,[Offset], flipper A bottom, 12,
 ,[Offset], flipper A, 13,
 ,[Offset], flipper B bottom, 14,
 ,[Offset], flipper B, 15,
 ,[Offset], floor, 16,
 ,[Offset], gate oneway, 17,
 ,[Offset], gate, 18,
 ,[Offset], glass, 19,
 ,[Offset], metal, 20,
 ,[Offset], platform 1, 21,
 ,[Offset], platform 2, 22,
 ,[Offset], platform 3, 23,
 ,[Offset], plunger, 24,
 ,[Offset], stopper, 25,
 ,[Offset], target, 26,
 ,[Offset], tile, 27,
 ,[Offset], wire A, 28,
 ,[Offset], wire B, 29,
 ,[Offset], wire C, 30,
 ,[Offset], wire D, 31,
 ,[Offset], wire E, 32,
 ,[Offset], wire F, 33,
 ,[Offset], Apron, 34,
 ,[Offset], BackGlass, 35,
 ,[Offset], Cabinet, 36,
 ,[Offset], Cabinet_Metals, 37,
 ,[Offset], Green_Posts, 38,
 ,[Offset], Inner_Wall, 39,
 ,[Offset], Light_Cutouts, 40,
 ,[Offset], Metal, 41,
 ,[Offset], Metal_Arc, 42,
 ,[Offset], Middle_Trap, 43,
 ,[Offset], Plastic_Pieces, 44,
 ,[Offset], Playfield, 45,
 ,[Offset], Pop_Bumper, 46,
 ,[Offset], Red_Posts, 47,
 ,[Offset], White_Posts, 48,
 ,[Offset], Stopper, 49,
 ,[Offset], Wooden_Rails, 50,
 ,[Offset], Flipper_Left, 51,
 ,[Offset], Gate_A, 52,
 ,[Offset], OneWay_Gate_A, 53,
 ,[Offset], Plunger, 54,
 ,[Offset], Slingshot_Left, 55,
 ,[Offset], Slingshot_Right, 56,
 ,[Offset], Target_A, 57,
 ,[Offset], Tile_A, 58,
 ,[Offset], Wire, 59,
 ,[Offset], Bumper, 60,
RSID_TBIGSHOT_MODELS_LODS, 3506,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper C extended, 3,
 ,[Offset], bumper C, 4,
 ,[Offset], bumper D extended, 5,
 ,[Offset], bumper D, 6,
 ,[Offset], bumper E, 7,
 ,[Offset], bumper F, 8,
 ,[Offset], bumper G, 9,
 ,[Offset], complete, 10,
 ,[Offset], empty, 11,
 ,[Offset], flipper A bottom, 12,
 ,[Offset], flipper A, 13,
 ,[Offset], flipper B bottom, 14,
 ,[Offset], flipper B, 15,
 ,[Offset], floor, 16,
 ,[Offset], gate oneway, 17,
 ,[Offset], gate, 18,
 ,[Offset], glass, 19,
 ,[Offset], metal, 20,
 ,[Offset], platform 1, 21,
 ,[Offset], platform 2, 22,
 ,[Offset], platform 3, 23,
 ,[Offset], plunger, 24,
 ,[Offset], stopper, 25,
 ,[Offset], target, 26,
 ,[Offset], tile, 27,
 ,[Offset], wire A, 28,
 ,[Offset], wire B, 29,
 ,[Offset], wire C, 30,
 ,[Offset], wire D, 31,
 ,[Offset], wire E, 32,
 ,[Offset], wire F, 33,
 ,[Offset], Apron, 34,
 ,[Offset], BackGlass, 35,
 ,[Offset], Cabinet, 36,
 ,[Offset], Cabinet_Metals, 37,
 ,[Offset], Green_Posts, 38,
 ,[Offset], Inner_Wall, 39,
 ,[Offset], Light_Cutouts, 40,
 ,[Offset], Metal, 41,
 ,[Offset], Metal_Arc, 42,
 ,[Offset], Middle_Trap, 43,
 ,[Offset], Plastic_Pieces, 44,
 ,[Offset], Playfield, 45,
 ,[Offset], Pop_Bumper, 46,
 ,[Offset], Red_Posts, 47,
 ,[Offset], White_Posts, 48,
 ,[Offset], Stopper, 49,
 ,[Offset], Wooden_Rails, 50,
 ,[Offset], Flipper_Left, 51,
 ,[Offset], Gate_A, 52,
 ,[Offset], OneWay_Gate_A, 53,
 ,[Offset], Plunger, 54,
 ,[Offset], Slingshot_Left, 55,
 ,[Offset], Slingshot_Right, 56,
 ,[Offset], Target_A, 57,
 ,[Offset], Tile_A, 58,
 ,[Offset], Wire, 59,
 ,[Offset], Bumper, 60,
RSID_TBIGSHOT_COLLISION, 3507,,,
 ,[Offset], alt wall collision, 0,
 ,[Offset], arc col, 1,
 ,[Offset], bumper A col, 2,
 ,[Offset], bumper B col, 3,
 ,[Offset], bumper C col, 4,
 ,[Offset], bumper D col, 5,
 ,[Offset], bumper E col, 6,
 ,[Offset], bumper F col, 7,
 ,[Offset], bumper G col, 8,
 ,[Offset], flipper A B col, 9,
 ,[Offset], flipper A col, 10,
 ,[Offset], flipper A F col, 11,
 ,[Offset], flipper B B col, 12,
 ,[Offset], flipper B col, 13,
 ,[Offset], flipper B F col, 14,
 ,[Offset], floor collision, 15,
 ,[Offset], gate col, 16,
 ,[Offset], gate oneway col, 17,
 ,[Offset], plunger area col, 18,
 ,[Offset], plunger col, 19,
 ,[Offset], stopper col, 20,
 ,[Offset], target col, 21,
 ,[Offset], tile col, 22,
 ,[Offset], wall collision, 23,
 ,[Offset], platform 1, 24,
 ,[Offset], platform 2, 25,
 ,[Offset], platform 3, 26,
 ,[Offset], Back_Left_Rubber, 27,
 ,[Offset], Back_Mid_Rubber, 28,
 ,[Offset], Back_Right_Rubber, 29,
 ,[Offset], Ball_Drain, 30,
 ,[Offset], Left_Flipper_Lane, 31,
 ,[Offset], Left_Rubber, 32,
 ,[Offset], Left_Slingshot, 33,
 ,[Offset], Left_Slingshot_Front, 34,
 ,[Offset], Lower_Wall, 35,
 ,[Offset], Mid_Rubber, 36,
 ,[Offset], Outer_Wall, 37,
 ,[Offset], Plunger_Moving, 38,
 ,[Offset], Plunger_Rest, 39,
 ,[Offset], Right_Flipper_Lane, 40,
 ,[Offset], Right_Rubber, 41,
 ,[Offset], Right_Slingshot, 42,
 ,[Offset], Right_Slingshot_Front, 43,
 ,[Offset], Stopper, 44,
 ,[Offset], OneWayGate_Front, 45,
 ,[Offset], OneWayGate_Back, 46,
 ,[Offset], Left_Flipper_Back, 47,
 ,[Offset], Left_Flipper_Front, 48,
 ,[Offset], Right_Flipper_Back, 49,
 ,[Offset], Right_Flipper_Front, 50,
 ,[Offset], Middle_Trap, 51,
 ,[Offset], Left_Switch, 52,
 ,[Offset], Right_Switch, 53,
 ,[Offset], Left_Rubber_Switch, 54,
 ,[Offset], Right_Rubber_Switch, 55,
RSID_TBIGSHOT_PLACEMENT, 3508,,,
 ,[Offset], bumpers, 0,
 ,[Offset], Lights, 1,
RSID_TBIGSHOT_SOUNDS_START, 3509,,,
RSID_TBIGSHOT_SOUNDS, 3510,,,
 ,[Offset], center_island_side_targets, 0,
 ,[Offset], center_trap_2_20, 1,
 ,[Offset], center_trap_80, 2,
 ,[Offset], center_upper_rollover_25, 3,
 ,[Offset], center_upper_rollover_2_25, 4,
 ,[Offset], center_upper_rollover_3_25, 5,
 ,[Offset], jet_bumper_1_20, 6,
 ,[Offset], jet_bumper_1_80, 7,
 ,[Offset], left_inlane_2_25, 8,
 ,[Offset], left_inlane_3_25, 9,
 ,[Offset], left_inlane_50, 10,
 ,[Offset], left_outlane_2_20, 11,
 ,[Offset], left_outlane_80, 12,
 ,[Offset], left_mid_rollover_2_20, 13,
 ,[Offset], left_mid_rollover_80, 14,
 ,[Offset], left_right_upper_rollovers_2_25, 15,
 ,[Offset], left_right_upper_rollovers_3_25, 16,
 ,[Offset], left_right_upper_rollovers_4_25, 17,
 ,[Offset], left_right_upper_rollovers_50, 18,
 ,[Offset], right_mid_rollover_2_25, 19,
 ,[Offset], right_mid_rollover_3_25, 20,
 ,[Offset], right_mid_rollover_50, 21,
 ,[Offset], slingshots_2_25, 22,
 ,[Offset], slingshots_3_25, 23,
 ,[Offset], slingshots_50, 24,
 ,[Offset], targets_reset, 25,
 ,[Offset], drain, 26,
 ,[Offset], eject, 27,
 ,[Offset], score_tabulation_loop, 28,
 ,[Offset], drop_targets_left, 29,
 ,[Offset], drop_targets_right, 30,
 ,[Offset], big_shot_main_menu_instructions, 31,
RSID_TBIGSHOT_SOUNDS_END, 3511,,,
RSID_TBIGSHOT_HUD, 3512,,,
 ,[Offset], Big_Shot_Hud_Border, 0,
 ,[Offset], BigShot_HUD, 1,
 ,[Offset], Big_Shot_Hud_Border_BallsInPlay, 2,
RSID_TBIGSHOT_END, 3513,,,
