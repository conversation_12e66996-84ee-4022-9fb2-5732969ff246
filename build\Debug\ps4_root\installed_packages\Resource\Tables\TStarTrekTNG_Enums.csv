RSID_TSTARTREKTNG_START, 3150,,,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 0,
 ,[Offset], <PERSON><PERSON><PERSON>T<PERSON>\PBTStarTrekTNG, 1,
 ,[Offset], StarTrekTNG\InstructionsENG, 2,
 ,[Offset], <PERSON>T<PERSON>TNG\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON><PERSON>T<PERSON>\InstructionsITAL, 4,
 ,[Offset], StarTrekTNG\InstructionsGERM, 5,
 ,[Offset], StarTrekTNG\InstructionsSPAN, 6,
 ,[Offset], StarTrekTNG\InstructionsPORT, 7,
 ,[Offset], StarTrekTNG\InstructionsDUTCH, 8,
 ,[Offset], tables\Tales_BG_scroll, 9,
 ,[Offset], StarTrekTNG\Pro_TipsENG, 10,
 ,[Offset], StarTrekTNG\Pro_TipsFR, 11,
 ,[Offset], StarTrekTNG\Pro_TipsITAL, 12,
 ,[Offset], <PERSON>TrekTNG\Pro_TipsGERM, 13,
 ,[Offset], <PERSON>TrekTNG\Pro_TipsSPAN, 14,
 ,[Offset], <PERSON><PERSON><PERSON>TNG\Pro_TipsENG, 15,
 ,[Offset], <PERSON><PERSON><PERSON>T<PERSON>\Pro_TipsENG, 16,
RSID_TSTARTREKTNG_LIGHTS, 3151,,,
RSID_TSTARTREKTNG_CAMERAS, 3152,,,
RSID_TSTARTREKTNG_LAMP_TEXTURES, 3153,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_26_b_Off, 28,
 ,[Offset], L_26_b_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_31_Off, 32,
 ,[Offset], L_31_On, 33,
 ,[Offset], L_32_Off, 34,
 ,[Offset], L_32_On, 35,
 ,[Offset], L_33_Off, 36,
 ,[Offset], L_33_On, 37,
 ,[Offset], L_34_Off, 38,
 ,[Offset], L_34_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_51_On, 66,
 ,[Offset], L_51_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_77_On, 110,
 ,[Offset], L_77_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_81_On, 114,
 ,[Offset], L_81_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_86_On, 124,
 ,[Offset], L_86_On, 125,
 ,[Offset], L_86_On, 126,
 ,[Offset], L_86_On, 127,
 ,[Offset], L_26_bulb_off, 128,
 ,[Offset], L_26_bulb_on, 129,
 ,[Offset], F_10_Off, 130,
 ,[Offset], F_10_On, 131,
 ,[Offset], F_20_Off, 132,
 ,[Offset], F_20_On, 133,
 ,[Offset], F_20_On, 134,
 ,[Offset], F_20_On, 135,
 ,[Offset], F_20_On, 136,
 ,[Offset], F_20_On, 137,
 ,[Offset], F_20_On, 138,
 ,[Offset], F_20_On, 139,
 ,[Offset], F_24_off, 140,
 ,[Offset], F_24_on, 141,
 ,[Offset], L_thruster01_off, 142,
 ,[Offset], L_thruster01_on, 143,
 ,[Offset], L_thruster02_off, 144,
 ,[Offset], L_thruster02_on, 145,
 ,[Offset], F_23_a_off, 153,
 ,[Offset], F_23_a_on_01, 154,
 ,[Offset], F_23_a_on_02, 155,
 ,[Offset], F_23_a_on_03, 156,
 ,[Offset], F_23_a_on_04, 157,
 ,[Offset], F_23_a_on_05, 158,
 ,[Offset], F_23_a_on_06, 159,
 ,[Offset], F_23_a_on_07, 160,
 ,[Offset], F_23_a_on, 161,
 ,[Offset], F_23_b_off, 162,
 ,[Offset], F_23_b_on_01, 163,
 ,[Offset], F_23_b_on_02, 164,
 ,[Offset], F_23_b_on_03, 165,
 ,[Offset], F_23_b_on_04, 166,
 ,[Offset], F_23_b_on_05, 167,
 ,[Offset], F_23_b_on_06, 168,
 ,[Offset], F_23_b_on_07, 169,
 ,[Offset], F_23_b_on, 170,
 ,[Offset], F_23_c_off, 171,
 ,[Offset], F_23_c_on_01, 172,
 ,[Offset], F_23_c_on_02, 173,
 ,[Offset], F_23_c_on_03, 174,
 ,[Offset], F_23_c_on_04, 175,
 ,[Offset], F_23_c_on_05, 176,
 ,[Offset], F_23_c_on_06, 177,
 ,[Offset], F_23_c_on_07, 178,
 ,[Offset], F_23_c_on, 179,
 ,[Offset], F_21_Off, 180,
 ,[Offset], F_21_On, 181,
 ,[Offset], F_21_Off_B, 182,
 ,[Offset], F_21_On_B, 183,
 ,[Offset], F_25_Off, 184,
 ,[Offset], F_25_On, 185,
 ,[Offset], F_25_Off_B, 186,
 ,[Offset], F_25_On_B, 187,
 ,[Offset], F_22_Shallow_Ramp_Off, 188,
 ,[Offset], F_22_Shallow_Ramp_On, 189,
 ,[Offset], F_22_Steep_Ramp_Off, 190,
 ,[Offset], F_22_Steep_Ramp_On, 191,
 ,[Offset], L_78_Off, 192,
 ,[Offset], L_78_On, 193,
 ,[Offset], L_78_Off_B, 194,
 ,[Offset], L_78_On_B, 195,
 ,[Offset], L_78_Off_C, 196,
 ,[Offset], F_26_On, 197,
 ,[Offset], F_27_On, 198,
 ,[Offset], F_28_On, 199,
RSID_TSTARTREKTNG_TEXTURES, 3154,,,
 ,[Offset], Display_frame_generic, 0,
 ,[Offset], alpha_ramp, 1,
 ,[Offset], apron, 2,
 ,[Offset], beta_ramp, 3,
 ,[Offset], catapult, 4,
 ,[Offset], clear_plastics, 5,
 ,[Offset], Clingon, 6,
 ,[Offset], deltaramp_t_c, 7,
 ,[Offset], deltaRampTarget_t_c, 8,
 ,[Offset], large_plastics, 9,
 ,[Offset], largeWireRamp_t_c, 10,
 ,[Offset], LongWireRamp_t_c, 11,
 ,[Offset], metal, 12,
 ,[Offset], metal_base, 13,
 ,[Offset], outlane_plastics, 14,
 ,[Offset], Playfield_Lower, 15,
 ,[Offset], Playfield_Upper, 16,
 ,[Offset], Rails, 17,
 ,[Offset], Signs, 18,
 ,[Offset], Slingshots, 19,
 ,[Offset], SmallWireRamp_t_c, 20,
 ,[Offset], SRamp_t_c, 21,
 ,[Offset], Starship_einstein, 22,
 ,[Offset], Trigger, 23,
 ,[Offset], rubberband_temp, 24,
 ,[Offset], Bumper_Hamer, 25,
 ,[Offset], Bumper_Sensors, 26,
 ,[Offset], Rubber Post_Temp, 27,
 ,[Offset], ClearPlasticPost_01, 28,
 ,[Offset], Generic_Metal, 29,
 ,[Offset], Silver Metal Screws_Temp, 30,
 ,[Offset], frog_targets, 31,
 ,[Offset], Plastic_Ramp_02, 32,
 ,[Offset], PopBumperBody, 33,
 ,[Offset], Flipper, 34,
 ,[Offset], Targets, 35,
 ,[Offset], metal_temp, 36,
 ,[Offset], yellow_target, 37,
 ,[Offset], Harley_Spinner, 38,
 ,[Offset], Harley_gate, 39,
 ,[Offset], HarleyBumperBody, 40,
 ,[Offset], start button, 41,
 ,[Offset], bulb1, 42,
 ,[Offset], metalPiece_t_c, 43,
 ,[Offset], metal_walls, 44,
 ,[Offset], 8_plastics, 45,
 ,[Offset], Red_Plastic_Post, 46,
 ,[Offset], Red_Bumper, 47,
 ,[Offset], bigShip_t_c, 48,
 ,[Offset], backglass, 49,
 ,[Offset], black_metal, 50,
 ,[Offset], cabinet, 51,
 ,[Offset], cabinet_front, 52,
 ,[Offset], cabinet_Head, 53,
 ,[Offset], legs, 54,
 ,[Offset], metalPieces_t_c, 55,
 ,[Offset], romulan_ship, 56,
 ,[Offset], rotatingPiece_t_c, 57,
 ,[Offset], to_be_continued_button, 58,
 ,[Offset], Flipper_button, 59,
 ,[Offset], Black_wood, 60,
 ,[Offset], Extra_Metal_Parts, 61,
 ,[Offset], Coin_Slot, 62,
 ,[Offset], Spinner, 63,
 ,[Offset], Spinner_S, 64,
 ,[Offset], Romulan_Target, 65,
 ,[Offset], lower_metal, 66,
 ,[Offset], metal_parts, 67,
 ,[Offset], Rubber Post_yellow, 68,
 ,[Offset], red_targets, 69,
 ,[Offset], Deverter, 70,
 ,[Offset], Clear_Plastic_Post, 71,
 ,[Offset], Generic_Screw, 72,
 ,[Offset], turret_shadow, 73,
RSID_TSTARTREKTNG_MODELS, 3155,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Catapult, 2,
 ,[Offset], Flipper, 3,
 ,[Offset], Flipper_Plastics, 4,
 ,[Offset], Habit_Cross, 5,
 ,[Offset], Habit_Curl, 6,
 ,[Offset], Habit_Gun_Left, 7,
 ,[Offset], Habit_Gun_Right, 8,
 ,[Offset], Habit_Left, 9,
 ,[Offset], Habit_Right, 10,
 ,[Offset], Metal_Pieces, 11,
 ,[Offset], Metal_Walls, 12,
 ,[Offset], Plastic_Pieces, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], Playfield, 15,
 ,[Offset], Pop_Bumpers, 16,
 ,[Offset], Ramp_Left_Shallow, 17,
 ,[Offset], Ramp_Left_Steep, 18,
 ,[Offset], Ramp_Right, 19,
 ,[Offset], Rollover_Posts, 20,
 ,[Offset], Rubber_Pieces, 21,
 ,[Offset], Slingshot_Left, 22,
 ,[Offset], Slingshot_Right, 23,
 ,[Offset], Starship, 24,
 ,[Offset], STarship_Ramp, 25,
 ,[Offset], Target, 26,
 ,[Offset], Target_Yellow, 27,
 ,[Offset], Turret_Left, 28,
 ,[Offset], Turret_Right, 29,
 ,[Offset], Vertical_Plastics, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Wooden_Rails, 32,
 ,[Offset], Main_Ship, 33,
 ,[Offset], Blue_ship, 34,
 ,[Offset], Cabinet, 35,
 ,[Offset], Cabinet_Backglass, 36,
 ,[Offset], Cabinet_Buttons, 37,
 ,[Offset], Cabinet_Interior, 38,
 ,[Offset], Cabinet_Metals, 39,
 ,[Offset], Diverter_Target, 40,
 ,[Offset], Gate_Left_Ramp, 41,
 ,[Offset], Gate_Right_Ramp_Lower, 42,
 ,[Offset], Gate_Right_Ramp_Upper, 43,
 ,[Offset], One_Way_Gate, 44,
 ,[Offset], PLunger_Trigger, 45,
 ,[Offset], Spinner, 46,
 ,[Offset], Ramp_Stickers, 47,
 ,[Offset], Romulan_Target, 48,
 ,[Offset], Bumper_A, 49,
 ,[Offset], light_cutouts, 50,
 ,[Offset], ramp_base_metal, 51,
 ,[Offset], light_cutouts02, 52,
 ,[Offset], light_cutouts03, 53,
 ,[Offset], clear_plastics, 54,
 ,[Offset], metal_parts, 55,
 ,[Offset], lightcutout_thruster, 56,
 ,[Offset], sign_bulbs, 57,
 ,[Offset], red_light, 58,
 ,[Offset], Target_Red, 59,
 ,[Offset], Back_Ramp_Diverter, 60,
 ,[Offset], Screws_And_Bolts, 61,
 ,[Offset], Blue_Ship_Inside, 62,
 ,[Offset], Plastic_Pieces_B, 63,
RSID_TSTARTREKTNG_MODELS_LODS, 3156,,,
 ,[Offset], Apron, 0,
 ,[Offset], Bulbs, 1,
 ,[Offset], Catapult, 2,
 ,[Offset], Flipper, 3,
 ,[Offset], Flipper_Plastics, 4,
 ,[Offset], Habit_Cross, 5,
 ,[Offset], Habit_Curl, 6,
 ,[Offset], Habit_Gun_Left, 7,
 ,[Offset], Habit_Gun_Right, 8,
 ,[Offset], Habit_Left, 9,
 ,[Offset], Habit_Right, 10,
 ,[Offset], Metal_Pieces, 11,
 ,[Offset], Metal_Walls, 12,
 ,[Offset], Plastic_Pieces, 13,
 ,[Offset], Plastic_Posts, 14,
 ,[Offset], Playfield, 15,
 ,[Offset], Pop_Bumpers, 16,
 ,[Offset], Ramp_Left_Shallow, 17,
 ,[Offset], Ramp_Left_Steep, 18,
 ,[Offset], Ramp_Right, 19,
 ,[Offset], Rollover_Posts, 20,
 ,[Offset], Rubber_Pieces, 21,
 ,[Offset], Slingshot_Left, 22,
 ,[Offset], Slingshot_Right, 23,
 ,[Offset], Starship, 24,
 ,[Offset], STarship_Ramp, 25,
 ,[Offset], Target, 26,
 ,[Offset], Target_Yellow, 27,
 ,[Offset], Turret_Left, 28,
 ,[Offset], Turret_Right, 29,
 ,[Offset], Vertical_Plastics, 30,
 ,[Offset], Wire, 31,
 ,[Offset], Wooden_Rails, 32,
 ,[Offset], Main_Ship, 33,
 ,[Offset], Blue_ship, 34,
 ,[Offset], Cabinet, 35,
 ,[Offset], Cabinet_Backglass, 36,
 ,[Offset], Cabinet_Buttons, 37,
 ,[Offset], Cabinet_Interior, 38,
 ,[Offset], Cabinet_Metals, 39,
 ,[Offset], Diverter_Target, 40,
 ,[Offset], Gate_Left_Ramp, 41,
 ,[Offset], Gate_Right_Ramp_Lower, 42,
 ,[Offset], Gate_Right_Ramp_Upper, 43,
 ,[Offset], One_Way_Gate, 44,
 ,[Offset], PLunger_Trigger, 45,
 ,[Offset], Spinner, 46,
 ,[Offset], Ramp_Stickers, 47,
 ,[Offset], Romulan_Target, 48,
 ,[Offset], Bumper_A, 49,
 ,[Offset], light_cutouts, 50,
 ,[Offset], ramp_base_metal, 51,
 ,[Offset], light_cutouts02, 52,
 ,[Offset], light_cutouts03, 53,
 ,[Offset], clear_plastics, 54,
 ,[Offset], metal_parts, 55,
 ,[Offset], lightcutout_thruster, 56,
 ,[Offset], sign_bulbs, 57,
 ,[Offset], red_light, 58,
 ,[Offset], Target_Red, 59,
 ,[Offset], Back_Ramp_Diverter, 60,
 ,[Offset], Screws_And_Bolts, 61,
 ,[Offset], Blue_Ship_Inside, 62,
 ,[Offset], Plastic_Pieces_B, 63,
RSID_TSTARTREKTNG_COLLISION, 3157,,,
 ,[Offset], Apron, 0,
 ,[Offset], Ball_Drain, 1,
 ,[Offset], FlipperLane_Left, 2,
 ,[Offset], FlipperLane_Right, 3,
 ,[Offset], Habit_Crossing, 4,
 ,[Offset], Habit_Curl, 5,
 ,[Offset], Habit_Left, 6,
 ,[Offset], Habit_Right, 7,
 ,[Offset], Playfield, 8,
 ,[Offset], Ramp_Left_Shallow, 9,
 ,[Offset], Ramp_Left_Steep, 10,
 ,[Offset], Ramp_Right, 11,
 ,[Offset], Rubber_A, 12,
 ,[Offset], Rubber_B, 13,
 ,[Offset], Rubber_C, 14,
 ,[Offset], Rubber_D, 15,
 ,[Offset], Rubber_E, 16,
 ,[Offset], Rubber_F, 17,
 ,[Offset], Rubber_G, 18,
 ,[Offset], Slingshot_Left, 19,
 ,[Offset], Slingshot_Right, 20,
 ,[Offset], Turret_Left_Entrance, 21,
 ,[Offset], Turret_Right_Entrance, 22,
 ,[Offset], Wall_Left_Inner, 23,
 ,[Offset], Wall_Right_Inner, 24,
 ,[Offset], Wall_Right_Lower, 25,
 ,[Offset], Wall_Right_Upper, 26,
 ,[Offset], Wall_Stop, 27,
 ,[Offset], Target, 28,
 ,[Offset], TargetYellow, 29,
 ,[Offset], Wall_Left, 30,
 ,[Offset], Catapult_Trap, 31,
 ,[Offset], Flipper_Left_Back, 32,
 ,[Offset], Flipper_Left_Front, 33,
 ,[Offset], Flipper_Right_Back, 34,
 ,[Offset], Flipper_Right_Front, 35,
 ,[Offset], Slingshot_Left_Front, 36,
 ,[Offset], Slingshot_Right_Front, 37,
 ,[Offset], Main_Ship_Eject, 38,
 ,[Offset], Main_Ship_Trap, 39,
 ,[Offset], Left_Scoop_Trap, 40,
 ,[Offset], Left_Turret_Trap, 41,
 ,[Offset], Lower_Diverter, 42,
 ,[Offset], Right_Turret_Trap, 43,
 ,[Offset], Trough, 44,
 ,[Offset], Upper_Diverter, 45,
 ,[Offset], Diverter_Target, 46,
 ,[Offset], Gate_Left, 47,
 ,[Offset], Gate_Right_Lower, 48,
 ,[Offset], Gate_Right_upper, 49,
 ,[Offset], One_Way_Gate_Back, 50,
 ,[Offset], One_Way_Gate_Front, 51,
 ,[Offset], Spinner, 52,
 ,[Offset], Main_Ship_Opto, 53,
 ,[Offset], Reverse_Scoop, 54,
 ,[Offset], Romulan_Target, 55,
 ,[Offset], Bumper_A, 56,
 ,[Offset], Left_Turret_Catch, 57,
 ,[Offset], Right_Turret_Catch, 58,
 ,[Offset], Back_Ramp_Diverter, 59,
RSID_TSTARTREKTNG_PLACEMENT, 3158,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TSTARTREKTNG_EMUROM, 3159,,,
 ,[Offset], trek_lx7, 0,
 ,[Offset], trek_lx7_attract, 1,
 ,[Offset], sttng_l7, 2,
 ,[Offset], sttng_l7, 3,
 ,[Offset], sttng_l7, 4,
 ,[Offset], trek_default, 5,
RSID_TSTARTREKTNG_SOUNDS_START, 3160,,,
RSID_TSTARTREKTNG_EMU_SOUNDS, 3161,,,
 ,[Offset], S0001-LP1, 0,
 ,[Offset], S0002-LP1, 1,
 ,[Offset], S0003-LP, 2,
 ,[Offset], S0004-LP1, 3,
 ,[Offset], S0005-LP, 4,
 ,[Offset], S0006-LP, 5,
 ,[Offset], S0007-LP, 6,
 ,[Offset], S0008-LP, 7,
 ,[Offset], S0009-LP, 8,
 ,[Offset], S000A-LP1, 9,
 ,[Offset], S000B-LP, 10,
 ,[Offset], S000C_C3, 11,
 ,[Offset], S000D-LP1, 12,
 ,[Offset], S000E-LP, 13,
 ,[Offset], S000F_C3, 14,
 ,[Offset], S0010-LP1, 15,
 ,[Offset], S0010-LP2, 16,
 ,[Offset], S0011-LP, 17,
 ,[Offset], S0012-LP1, 18,
 ,[Offset], S0013-LP, 19,
 ,[Offset], S0014-LP, 20,
 ,[Offset], S0015-LP, 21,
 ,[Offset], S0016-LP, 22,
 ,[Offset], S0017-LP, 23,
 ,[Offset], S0018-LP1, 24,
 ,[Offset], S0018-LP2, 25,
 ,[Offset], S0019-LP, 26,
 ,[Offset], S001A_C6, 27,
 ,[Offset], S001B-LP1, 28,
 ,[Offset], S001C-LP1, 29,
 ,[Offset], S001C-LP2, 30,
 ,[Offset], S001D-LP1, 31,
 ,[Offset], S001E_C5, 32,
 ,[Offset], S001F_C5, 33,
 ,[Offset], S0020_C5, 34,
 ,[Offset], S0021_C5, 35,
 ,[Offset], S0022_C5, 36,
 ,[Offset], S0023_C5, 37,
 ,[Offset], S0028-LP, 38,
 ,[Offset], S0029-LP, 39,
 ,[Offset], S002A-LP, 40,
 ,[Offset], S0064_C4, 41,
 ,[Offset], S0065_C4, 42,
 ,[Offset], S0066_C4, 43,
 ,[Offset], S0067_C4, 44,
 ,[Offset], S0068_C4, 45,
 ,[Offset], S0069_C4, 46,
 ,[Offset], S006A_C4, 47,
 ,[Offset], S006C_C4, 48,
 ,[Offset], S006D_C4, 49,
 ,[Offset], S006E_C4, 50,
 ,[Offset], S006F_C4, 51,
 ,[Offset], S0070_C4, 52,
 ,[Offset], S0071_C4, 53,
 ,[Offset], S0072_C4, 54,
 ,[Offset], S0073_C4, 55,
 ,[Offset], S0074_C4, 56,
 ,[Offset], S0075_C4, 57,
 ,[Offset], S0076_C4, 58,
 ,[Offset], S0077_C4, 59,
 ,[Offset], S0078_C4, 60,
 ,[Offset], S0079_C4, 61,
 ,[Offset], S007A_C4, 62,
 ,[Offset], S007B_C4, 63,
 ,[Offset], S007C_C4, 64,
 ,[Offset], S007D_C4, 65,
 ,[Offset], S007E_C4, 66,
 ,[Offset], S007F_C4, 67,
 ,[Offset], S0096_C4, 68,
 ,[Offset], S0097_C4, 69,
 ,[Offset], S0098_C4, 70,
 ,[Offset], S0099_C4, 71,
 ,[Offset], S009A_C4, 72,
 ,[Offset], S009B_C4, 73,
 ,[Offset], S009C_C4, 74,
 ,[Offset], S009D_C4, 75,
 ,[Offset], S009E_C4, 76,
 ,[Offset], S009F_C4, 77,
 ,[Offset], S00A0_C4, 78,
 ,[Offset], S00AA_C4, 79,
 ,[Offset], S00AB_C4, 80,
 ,[Offset], S00AC_C4, 81,
 ,[Offset], S00AD_C4, 82,
 ,[Offset], S00AE_C4, 83,
 ,[Offset], S00AF_C4, 84,
 ,[Offset], S00B0_C4, 85,
 ,[Offset], S00B1_C4, 86,
 ,[Offset], S00C8_C4, 87,
 ,[Offset], S00C9_C4, 88,
 ,[Offset], S00CA_C4, 89,
 ,[Offset], S00CB_C4, 90,
 ,[Offset], S00CC_C4, 91,
 ,[Offset], S00CD_C4, 92,
 ,[Offset], S00CE_C4, 93,
 ,[Offset], S00CF_C4, 94,
 ,[Offset], S00D0_C4, 95,
 ,[Offset], S00D1_C4, 96,
 ,[Offset], S00D2_C4, 97,
 ,[Offset], S00D3_C4, 98,
 ,[Offset], S00D4_C4, 99,
 ,[Offset], S00D5_C4, 100,
 ,[Offset], S00D6_C4, 101,
 ,[Offset], S00D7_C4, 102,
 ,[Offset], S00D8_C4, 103,
 ,[Offset], S00D9_C4, 104,
 ,[Offset], S00E5_C4, 105,
 ,[Offset], S00E6_C4, 106,
 ,[Offset], S00E7_C4, 107,
 ,[Offset], S00E8_C4, 108,
 ,[Offset], S00E9_C4, 109,
 ,[Offset], S00EA_C4, 110,
 ,[Offset], S00EB_C4, 111,
 ,[Offset], S00EC_C4, 112,
 ,[Offset], S00ED_C4, 113,
 ,[Offset], S00EE_C4, 114,
 ,[Offset], S00EF_C4, 115,
 ,[Offset], S00F0_C4, 116,
 ,[Offset], S00F1_C4, 117,
 ,[Offset], S00F2_C4, 118,
 ,[Offset], S00F3_C4, 119,
 ,[Offset], S00F4_C4, 120,
 ,[Offset], S00F5_C4, 121,
 ,[Offset], S00F6_C4, 122,
 ,[Offset], S00F7_C4, 123,
 ,[Offset], S00F8_C4, 124,
 ,[Offset], S00F9_C4, 125,
 ,[Offset], S00FA_C4, 126,
 ,[Offset], S00FB_C4, 127,
 ,[Offset], S00FC_C4, 128,
 ,[Offset], S00FD_C4, 129,
 ,[Offset], S00FE_C4, 130,
 ,[Offset], S00FF_C4, 131,
 ,[Offset], S0101_C4, 132,
 ,[Offset], S0102_C4, 133,
 ,[Offset], S0103_C4, 134,
 ,[Offset], S0104_C4, 135,
 ,[Offset], S0105_C4, 136,
 ,[Offset], S0106_C4, 137,
 ,[Offset], S0107_C4, 138,
 ,[Offset], S0108_C4, 139,
 ,[Offset], S0109_C4, 140,
 ,[Offset], S010A_C4, 141,
 ,[Offset], S010B_C4, 142,
 ,[Offset], S010C_C4, 143,
 ,[Offset], S010D_C4, 144,
 ,[Offset], S010E_C4, 145,
 ,[Offset], S010F_C4, 146,
 ,[Offset], S0110_C4, 147,
 ,[Offset], S0111_C4, 148,
 ,[Offset], S012C_C4, 149,
 ,[Offset], S012D_C4, 150,
 ,[Offset], S012E_C4, 151,
 ,[Offset], S012F_C4, 152,
 ,[Offset], S0130_C4, 153,
 ,[Offset], S0131_C4, 154,
 ,[Offset], S0132_C4, 155,
 ,[Offset], S0133_C4, 156,
 ,[Offset], S0134_C4, 157,
 ,[Offset], S0135_C4, 158,
 ,[Offset], S0136_C4, 159,
 ,[Offset], S0137_C4, 160,
 ,[Offset], S0138_C4, 161,
 ,[Offset], S0139_C4, 162,
 ,[Offset], S013A_C4, 163,
 ,[Offset], S013B_C4, 164,
 ,[Offset], S013C_C4, 165,
 ,[Offset], S013D_C4, 166,
 ,[Offset], S013E_C4, 167,
 ,[Offset], S013F_C4, 168,
 ,[Offset], S0140_C4, 169,
 ,[Offset], S0141_C4, 170,
 ,[Offset], S0142_C4, 171,
 ,[Offset], S0143_C4, 172,
 ,[Offset], S0144_C4, 173,
 ,[Offset], S0145_C4, 174,
 ,[Offset], S0146_C4, 175,
 ,[Offset], S0147_C4, 176,
 ,[Offset], S0148_C4, 177,
 ,[Offset], S0149_C4, 178,
 ,[Offset], S014A_C4, 179,
 ,[Offset], S014B_C4, 180,
 ,[Offset], S014C_C4, 181,
 ,[Offset], S014D_C4, 182,
 ,[Offset], S014E_C4, 183,
 ,[Offset], S014F_C4, 184,
 ,[Offset], S0150_C4, 185,
 ,[Offset], S0152_C4, 186,
 ,[Offset], S0153_C4, 187,
 ,[Offset], S0154_C4, 188,
 ,[Offset], S0155_C4, 189,
 ,[Offset], S0156_C4, 190,
 ,[Offset], S0157_C4, 191,
 ,[Offset], S0159_C4, 192,
 ,[Offset], S015A_C4, 193,
 ,[Offset], S015B_C4, 194,
 ,[Offset], S015C_C4, 195,
 ,[Offset], S015D_C4, 196,
 ,[Offset], S015E_C4, 197,
 ,[Offset], S015F_C4, 198,
 ,[Offset], S0160_C4, 199,
 ,[Offset], S0161_C4, 200,
 ,[Offset], S0162_C4, 201,
 ,[Offset], S0163_C4, 202,
 ,[Offset], S0164_C4, 203,
 ,[Offset], S0165_C4, 204,
 ,[Offset], S0166_C4, 205,
 ,[Offset], S0167_C4, 206,
 ,[Offset], S0168_C4, 207,
 ,[Offset], S0169_C4, 208,
 ,[Offset], S016A_C4, 209,
 ,[Offset], S016B_C4, 210,
 ,[Offset], S016C_C4, 211,
 ,[Offset], S016D_C4, 212,
 ,[Offset], S016E_C4, 213,
 ,[Offset], S016F_C4, 214,
 ,[Offset], S0170_C4, 215,
 ,[Offset], S0171_C4, 216,
 ,[Offset], S0172_C4, 217,
 ,[Offset], S0173_C4, 218,
 ,[Offset], S0174_C4, 219,
 ,[Offset], S0175_C4, 220,
 ,[Offset], S0176_C4, 221,
 ,[Offset], S0177_C4, 222,
 ,[Offset], S017B_C4, 223,
 ,[Offset], S017C_C4, 224,
 ,[Offset], S0190_C4, 225,
 ,[Offset], S0191_C4, 226,
 ,[Offset], S0192_C4, 227,
 ,[Offset], S0193_C4, 228,
 ,[Offset], S0194_C4, 229,
 ,[Offset], S0195_C4, 230,
 ,[Offset], S0196_C4, 231,
 ,[Offset], S0197_C4, 232,
 ,[Offset], S0198_C4, 233,
 ,[Offset], S0199_C4, 234,
 ,[Offset], S019A_C4, 235,
 ,[Offset], S019B_C4, 236,
 ,[Offset], S019C_C4, 237,
 ,[Offset], S019D_C4, 238,
 ,[Offset], S019E_C4, 239,
 ,[Offset], S019F_C4, 240,
 ,[Offset], S01A0_C4, 241,
 ,[Offset], S01A1_C4, 242,
 ,[Offset], S01A2_C4, 243,
 ,[Offset], S01A3_C4, 244,
 ,[Offset], S01A4_C4, 245,
 ,[Offset], S01A5_C4, 246,
 ,[Offset], S01A6_C4, 247,
 ,[Offset], S01A7_C4, 248,
 ,[Offset], S01A8_C4, 249,
 ,[Offset], S01A9_C4, 250,
 ,[Offset], S01AA_C4, 251,
 ,[Offset], S01AB_C4, 252,
 ,[Offset], S01AC_C4, 253,
 ,[Offset], S01AD_C4, 254,
 ,[Offset], S01AE_C4, 255,
 ,[Offset], S01AF_C4, 256,
 ,[Offset], S01C2_C4, 257,
 ,[Offset], S01C4_C4, 258,
 ,[Offset], S01C5_C4, 259,
 ,[Offset], S01C6_C4, 260,
 ,[Offset], S01C7_C4, 261,
 ,[Offset], S01CC_C4, 262,
 ,[Offset], S01CD_C4, 263,
 ,[Offset], S01F4_C4, 264,
 ,[Offset], S01F5_C4, 265,
 ,[Offset], S01F6_C4, 266,
 ,[Offset], S01F7_C4, 267,
 ,[Offset], S01F8_C4, 268,
 ,[Offset], S01F9_C4, 269,
 ,[Offset], S01FA_C4, 270,
 ,[Offset], S01FB_C4, 271,
 ,[Offset], S01FC_C4, 272,
 ,[Offset], S01FE_C4, 273,
 ,[Offset], S01FF_C4, 274,
 ,[Offset], S0200_C4, 275,
 ,[Offset], S0201_C4, 276,
 ,[Offset], S0202_C4, 277,
 ,[Offset], S0203_C4, 278,
 ,[Offset], S0204_C4, 279,
 ,[Offset], S0206_C4, 280,
 ,[Offset], S0207_C4, 281,
 ,[Offset], S0208_C4, 282,
 ,[Offset], S0209_C4, 283,
 ,[Offset], S020A_C4, 284,
 ,[Offset], S020B_C4, 285,
 ,[Offset], S020C_C4, 286,
 ,[Offset], S020D_C4, 287,
 ,[Offset], S020E_C4, 288,
 ,[Offset], S020F_C4, 289,
 ,[Offset], S0210_C4, 290,
 ,[Offset], S0211_C4, 291,
 ,[Offset], S0212_C4, 292,
 ,[Offset], S0213_C4, 293,
 ,[Offset], S0214_C4, 294,
 ,[Offset], S0215_C4, 295,
 ,[Offset], S0216_C4, 296,
 ,[Offset], S0217_C4, 297,
 ,[Offset], S0218_C4, 298,
 ,[Offset], S0219_C4, 299,
 ,[Offset], S021B_C4, 300,
 ,[Offset], S021C_C4, 301,
 ,[Offset], S021D_C4, 302,
 ,[Offset], S021E_C4, 303,
 ,[Offset], S021F_C4, 304,
 ,[Offset], S0220_C4, 305,
 ,[Offset], S0221_C4, 306,
 ,[Offset], S0222_C4, 307,
 ,[Offset], S0223_C4, 308,
 ,[Offset], S0224_C4, 309,
 ,[Offset], S0225_C4, 310,
 ,[Offset], S0226_C4, 311,
 ,[Offset], S0227_C4, 312,
 ,[Offset], S0228_C4, 313,
 ,[Offset], S0229_C4, 314,
 ,[Offset], S022A_C4, 315,
 ,[Offset], S022B_C4, 316,
 ,[Offset], S022C_C4, 317,
 ,[Offset], S022D_C4, 318,
 ,[Offset], S022E_C4, 319,
 ,[Offset], S022F_C4, 320,
 ,[Offset], S0230_C4, 321,
 ,[Offset], S0231_C4, 322,
 ,[Offset], S0232_C4, 323,
 ,[Offset], S0233_C4, 324,
 ,[Offset], S0234_C4, 325,
 ,[Offset], S0235_C4, 326,
 ,[Offset], S0236_C4, 327,
 ,[Offset], S0237_C4, 328,
 ,[Offset], S0238_C4, 329,
 ,[Offset], S0258_C4, 330,
 ,[Offset], S0259_C4, 331,
 ,[Offset], S025A_C4, 332,
 ,[Offset], S025B_C4, 333,
 ,[Offset], S025C_C4, 334,
 ,[Offset], S025D_C4, 335,
 ,[Offset], S025E_C4, 336,
 ,[Offset], S025F_C4, 337,
 ,[Offset], S0260_C4, 338,
 ,[Offset], S0261_C4, 339,
 ,[Offset], S0262_C4, 340,
 ,[Offset], S0263_C4, 341,
 ,[Offset], S0264_C4, 342,
 ,[Offset], S0265_C4, 343,
 ,[Offset], S0266_C4, 344,
 ,[Offset], S0267_C4, 345,
 ,[Offset], S0268_C4, 346,
 ,[Offset], S0269_C4, 347,
 ,[Offset], S026A_C4, 348,
 ,[Offset], S026D_C4, 349,
 ,[Offset], S026E_C4, 350,
 ,[Offset], S026F_C4, 351,
 ,[Offset], S0270_C4, 352,
 ,[Offset], S0271_C4, 353,
 ,[Offset], S0275_C4, 354,
 ,[Offset], S0277_C4, 355,
 ,[Offset], S028A_C4, 356,
 ,[Offset], S028B_C4, 357,
 ,[Offset], S028C_C4, 358,
 ,[Offset], S028D_C4, 359,
 ,[Offset], S028E_C4, 360,
 ,[Offset], S028F_C4, 361,
 ,[Offset], S0290_C4, 362,
 ,[Offset], S0291_C4, 363,
 ,[Offset], S03D4_C-1, 364,
 ,[Offset], S03D5_C-1, 365,
 ,[Offset], S03D6_C5, 366,
 ,[Offset], S03D7_C5, 367,
 ,[Offset], S03D8_C5, 368,
 ,[Offset], S03D9_C-1, 369,
 ,[Offset], S03DA_C-1, 370,
 ,[Offset], S03DB_C5, 371,
 ,[Offset], S03DC_C5, 372,
 ,[Offset], S03DD_C5, 373,
 ,[Offset], S03E8_C5, 374,
 ,[Offset], S03E9_C5, 375,
 ,[Offset], S03EA_C-1, 376,
 ,[Offset], S03EB_C-1, 377,
 ,[Offset], S03EC_C5, 378,
 ,[Offset], S03ED_C-1, 379,
 ,[Offset], S03EE_C5, 380,
 ,[Offset], S03EF_C5, 381,
 ,[Offset], S03F0_C5, 382,
 ,[Offset], S03F1_C5, 383,
 ,[Offset], S03F2_C5, 384,
 ,[Offset], S03F3_C5, 385,
 ,[Offset], S03F4_C5, 386,
 ,[Offset], S03F5_C5, 387,
 ,[Offset], S03F6_C5, 388,
 ,[Offset], S03F7_C6, 389,
 ,[Offset], S03F8_C5, 390,
 ,[Offset], S03F9_C5, 391,
 ,[Offset], S03FA_C5, 392,
 ,[Offset], S03FB_C5, 393,
 ,[Offset], S03FC_C5, 394,
 ,[Offset], S03FD_C5, 395,
 ,[Offset], S03FE_C5, 396,
 ,[Offset], S03FF_C5, 397,
 ,[Offset], S0400_C5, 398,
 ,[Offset], S0401_C6, 399,
 ,[Offset], S0402_C5, 400,
 ,[Offset], S0403_C5, 401,
 ,[Offset], S0404_C5, 402,
 ,[Offset], S0405_C5, 403,
 ,[Offset], S0406_C5, 404,
 ,[Offset], S0407_C5, 405,
 ,[Offset], S0408_C5, 406,
 ,[Offset], S0409_C5, 407,
 ,[Offset], S040A_C3, 408,
 ,[Offset], S040B_C5, 409,
 ,[Offset], S040C-LP1, 410,
 ,[Offset], S040C-LP2, 411,
 ,[Offset], S040D_C6, 412,
 ,[Offset], S040E_C5, 413,
 ,[Offset], S040F_C5, 414,
 ,[Offset], S0410_C5, 415,
 ,[Offset], S0411_C6, 416,
 ,[Offset], S0412_C6, 417,
 ,[Offset], S0413_C5, 418,
 ,[Offset], S0414_C6, 419,
 ,[Offset], S0415_C5, 420,
 ,[Offset], S0416_C5, 421,
 ,[Offset], S0417_C5, 422,
 ,[Offset], S0418_C5, 423,
 ,[Offset], S0419_C5, 424,
 ,[Offset], S041C_C5, 425,
 ,[Offset], S041D_C5, 426,
 ,[Offset], S0420_C5, 427,
 ,[Offset], S0421_C5, 428,
 ,[Offset], S0422_C5, 429,
 ,[Offset], S0423_C5, 430,
 ,[Offset], S0425_C5, 431,
 ,[Offset], S0426_C6, 432,
 ,[Offset], S0427_C6, 433,
 ,[Offset], S0428_C6, 434,
 ,[Offset], S0429_C6, 435,
 ,[Offset], S042A_C6, 436,
 ,[Offset], S042B_C6, 437,
 ,[Offset], S042C_C6, 438,
 ,[Offset], S042D_C6, 439,
 ,[Offset], S042E_C6, 440,
 ,[Offset], S042F_C6, 441,
 ,[Offset], S0430_C6, 442,
 ,[Offset], S0431_C5, 443,
 ,[Offset], S0432_C5, 444,
 ,[Offset], S0433_C5, 445,
 ,[Offset], S043C_C5, 446,
 ,[Offset], S043D_C5, 447,
 ,[Offset], S043F_C5, 448,
 ,[Offset], S0440_C5, 449,
 ,[Offset], S0441_C5, 450,
 ,[Offset], S0442_C5, 451,
 ,[Offset], S0443_C5, 452,
 ,[Offset], S0444_C5, 453,
 ,[Offset], S0445_C5, 454,
 ,[Offset], S0446_C5, 455,
 ,[Offset], S0447_C5, 456,
 ,[Offset], S0449_C5, 457,
 ,[Offset], S044A_C5, 458,
 ,[Offset], S044B_C-1, 459,
 ,[Offset], S044C_C5, 460,
 ,[Offset], S044D_C5, 461,
 ,[Offset], S044E_C5, 462,
 ,[Offset], S044F_C5, 463,
 ,[Offset], S0450_C5, 464,
 ,[Offset], S0451_C6, 465,
 ,[Offset], S0452_C5, 466,
 ,[Offset], S0453_C5, 467,
 ,[Offset], S0454_C5, 468,
 ,[Offset], S0456_C6, 469,
 ,[Offset], S0457_C5, 470,
 ,[Offset], S0458_C5, 471,
 ,[Offset], S0460_C5, 472,
 ,[Offset], S0462_C5, 473,
 ,[Offset], S0463_C5, 474,
RSID_TSTARTREKTNG_MECH_SOUNDS, 3162,,,
 ,[Offset], ball_falls_into_upper_lock, 0,
 ,[Offset], borg_eject, 1,
 ,[Offset], fall_into_advance_rank, 2,
 ,[Offset], fall_into_center_trap, 3,
 ,[Offset], fire_turret, 4,
 ,[Offset], left_vertical_eject, 5,
 ,[Offset], load_turret, 6,
 ,[Offset], plunge, 7,
 ,[Offset], turrets_rotate, 8,
 ,[Offset], fall_into_neutral_zone, 9,
RSID_TSTARTREKTNG_SOUNDS_END, 3163,,,
RSID_TSTARTREKTNG_SAMPLES, 3164,,,
RSID_TSTARTREK_BUTTON_1, 3165,,,
RSID_TSTARTREK_BUTTON_2, 3166,,,
RSID_TSTARTREK_BUTTON_3, 3167,,,
RSID_TSTARTREK_BUTTON_4, 3168,,,
RSID_TSTARTREK_BUTTON_5, 3169,,,
RSID_TSTARTREK_BUTTON_6, 3170,,,
RSID_TSTARTREK_BUTTON_7, 3171,,,
RSID_TSTARTREKTNG_END, 3172,,,
