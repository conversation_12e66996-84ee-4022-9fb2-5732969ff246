RSID_TWHIRLWIND_START, 2925,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], Whirlwind\PBTWhirlwind, 1,
 ,[Offset], Whirlwind\InstructionsENG, 2,
 ,[Offset], Whirlwind\InstructionsFR, 3,
 ,[Offset], <PERSON>hirlwind\InstructionsITAL, 4,
 ,[Offset], Whirlwind\InstructionsGERM, 5,
 ,[Offset], <PERSON>hirlwind\InstructionsSPAN, 6,
 ,[Offset], Whirlwind\InstructionsPORT, 7,
 ,[Offset], Whirlwind\InstructionsDUTCH, 8,
 ,[Offset], Whirlwind\InstructionsDUTCH, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TWHIRLWIND_LIGHTS, 2926,,,
RSID_TWHIRLWIND_CAMERAS, 2927,,,
RSID_TWHIRLWIND_LAMP_TEXTURES, 2928,,,
 ,[Offset], whirlwindCameras, 0,
 ,[Offset], L01_on, 1,
 ,[Offset], L02_off, 2,
 ,[Offset], L02_on, 3,
 ,[Offset], L03_off, 4,
 ,[Offset], L03_on, 5,
 ,[Offset], L04_off, 6,
 ,[Offset], L04_on, 7,
 ,[Offset], L05_off, 8,
 ,[Offset], L05_on, 9,
 ,[Offset], L06_off, 10,
 ,[Offset], L06_on, 11,
 ,[Offset], L07_off, 12,
 ,[Offset], L07_on, 13,
 ,[Offset], L08_off, 14,
 ,[Offset], L08_on, 15,
 ,[Offset], L09_off, 16,
 ,[Offset], L09_on, 17,
 ,[Offset], L10_off, 18,
 ,[Offset], L10_on, 19,
 ,[Offset], L11_off, 20,
 ,[Offset], L11_on, 21,
 ,[Offset], L12_off, 22,
 ,[Offset], L12_on, 23,
 ,[Offset], L13_off, 24,
 ,[Offset], L13_on, 25,
 ,[Offset], L14_off, 26,
 ,[Offset], L14_on, 27,
 ,[Offset], L15_off, 28,
 ,[Offset], L15_on, 29,
 ,[Offset], L16_off, 30,
 ,[Offset], L16_on, 31,
 ,[Offset], L17_off, 32,
 ,[Offset], L17_on, 33,
 ,[Offset], L18_off, 34,
 ,[Offset], L18_on, 35,
 ,[Offset], L19_off, 36,
 ,[Offset], L19_on, 37,
 ,[Offset], L20_off, 38,
 ,[Offset], L20_on, 39,
 ,[Offset], L21_off, 40,
 ,[Offset], L21_on, 41,
 ,[Offset], L22_off, 42,
 ,[Offset], L22_on, 43,
 ,[Offset], L23_off, 44,
 ,[Offset], L23_on, 45,
 ,[Offset], L24_off, 46,
 ,[Offset], L24_on, 47,
 ,[Offset], L25_off, 48,
 ,[Offset], L25_on, 49,
 ,[Offset], L26_off, 50,
 ,[Offset], L26_on, 51,
 ,[Offset], L27_off, 52,
 ,[Offset], L27_on, 53,
 ,[Offset], L28_off, 54,
 ,[Offset], L28_on, 55,
 ,[Offset], L29_off, 56,
 ,[Offset], L29_on, 57,
 ,[Offset], L30_off, 58,
 ,[Offset], L30_on, 59,
 ,[Offset], L31_off, 60,
 ,[Offset], L31_on, 61,
 ,[Offset], L32_off, 62,
 ,[Offset], L32_on, 63,
 ,[Offset], L33_off, 64,
 ,[Offset], L33_on, 65,
 ,[Offset], L34_off, 66,
 ,[Offset], L34_on, 67,
 ,[Offset], L35_off, 68,
 ,[Offset], L35_on, 69,
 ,[Offset], L36_off, 70,
 ,[Offset], L36_on, 71,
 ,[Offset], L37_off, 72,
 ,[Offset], L37_on, 73,
 ,[Offset], L38_off, 74,
 ,[Offset], L38_on, 75,
 ,[Offset], L39_off, 76,
 ,[Offset], L39_on, 77,
 ,[Offset], L40_off, 78,
 ,[Offset], L40_on, 79,
 ,[Offset], L41_off, 80,
 ,[Offset], L41_on, 81,
 ,[Offset], L42_off, 82,
 ,[Offset], L42_on, 83,
 ,[Offset], L43_off, 84,
 ,[Offset], L43_on, 85,
 ,[Offset], L44_off, 86,
 ,[Offset], L44_on, 87,
 ,[Offset], L45_off, 88,
 ,[Offset], L45_on, 89,
 ,[Offset], L46_off, 90,
 ,[Offset], L46_on, 91,
 ,[Offset], L47_off, 92,
 ,[Offset], L47_on, 93,
 ,[Offset], L48_off, 94,
 ,[Offset], L48_on, 95,
 ,[Offset], L49_off, 96,
 ,[Offset], L49_on, 97,
 ,[Offset], L50_off, 98,
 ,[Offset], L50_on, 99,
 ,[Offset], L51_off, 100,
 ,[Offset], L51_on, 101,
 ,[Offset], L52_off, 102,
 ,[Offset], L52_on, 103,
 ,[Offset], L53_off, 104,
 ,[Offset], L53_on, 105,
 ,[Offset], L54_off, 106,
 ,[Offset], L54_on, 107,
 ,[Offset], L55_off, 108,
 ,[Offset], L55_on, 109,
 ,[Offset], L56_off, 110,
 ,[Offset], L56_on, 111,
 ,[Offset], L57_off, 112,
 ,[Offset], L57_on, 113,
 ,[Offset], L58_off, 114,
 ,[Offset], L58_on, 115,
 ,[Offset], L59_off, 116,
 ,[Offset], L59_on, 117,
 ,[Offset], L60_off, 118,
 ,[Offset], L60_on, 119,
 ,[Offset], L61_off, 120,
 ,[Offset], L61_on, 121,
 ,[Offset], L62_off, 122,
 ,[Offset], L62_on, 123,
 ,[Offset], L63_off, 124,
 ,[Offset], L63_on, 125,
 ,[Offset], L63_on, 126,
 ,[Offset], L64_on, 127,
 ,[Offset], F02_off, 128,
 ,[Offset], F02_on, 129,
 ,[Offset], F03_off, 130,
 ,[Offset], f03_on, 131,
 ,[Offset], F05_off, 132,
 ,[Offset], F05_on, 133,
 ,[Offset], F07a_off, 134,
 ,[Offset], F07a_on, 135,
 ,[Offset], F07b_off, 136,
 ,[Offset], F07b_on, 137,
 ,[Offset], F08a_off, 138,
 ,[Offset], F08a_on, 139,
 ,[Offset], F01_off, 140,
 ,[Offset], F01_on, 141,
 ,[Offset], F03b_off, 142,
 ,[Offset], F03b_on, 143,
 ,[Offset], F04_off, 144,
 ,[Offset], F04_on, 145,
 ,[Offset], F05b_off, 146,
 ,[Offset], F05b_on, 147,
 ,[Offset], F06_off, 148,
 ,[Offset], F06_on, 149,
 ,[Offset], F01_alphaplane_off, 150,
 ,[Offset], F01_alphaplane_on, 151,
 ,[Offset], F03b_alphaplane_off, 152,
 ,[Offset], F03b_alphaplane_on, 153,
 ,[Offset], F04_alphaplane_off, 154,
 ,[Offset], F04_alphaplane_on, 155,
 ,[Offset], F05b_alphaplane_off, 156,
 ,[Offset], F05b_alphaplane_on, 157,
 ,[Offset], F06_alphaplane_off, 158,
 ,[Offset], F06_alphaplane_on, 159,
 ,[Offset], lightingbolt01, 160,
 ,[Offset], lightingbolt01_on, 161,
 ,[Offset], lightingbolt01_onA, 162,
 ,[Offset], lightingbolt01_onB, 163,
 ,[Offset], lightingbolt01_onC, 164,
 ,[Offset], lightingbolt01_onD, 165,
RSID_TWHIRLWIND_TEXTURES, 2929,,,
 ,[Offset], Whirlwind_LED_Border, 0,
 ,[Offset], WoodEdge_Tile, 1,
 ,[Offset], Backglass, 2,
 ,[Offset], backplate01, 3,
 ,[Offset], bolt-tp01 copy, 4,
 ,[Offset], brushed_metal01, 5,
 ,[Offset], BumperA01, 6,
 ,[Offset], BumperB01, 7,
 ,[Offset], BumperC01, 8,
 ,[Offset], BumperD01, 9,
 ,[Offset], BumperE01, 10,
 ,[Offset], BumperF01, 11,
 ,[Offset], BumperG01, 12,
 ,[Offset], BumperH01, 13,
 ,[Offset], BumperI01, 14,
 ,[Offset], BumperJ01, 15,
 ,[Offset], BumperK01, 16,
 ,[Offset], BumperL01, 17,
 ,[Offset], BumperM01, 18,
 ,[Offset], BumperN01, 19,
 ,[Offset], BumperO01, 20,
 ,[Offset], BumperP01, 21,
 ,[Offset], Buttons_Parts, 22,
 ,[Offset], cabinet01, 23,
 ,[Offset], cabinetwalls01, 24,
 ,[Offset], CoinSlots, 25,
 ,[Offset], color wheel, 26,
 ,[Offset], Copy of Metal front, 27,
 ,[Offset], flipper, 28,
 ,[Offset], Generic_Metal, 29,
 ,[Offset], glass, 30,
 ,[Offset], L77_off, 31,
 ,[Offset], L78_off, 32,
 ,[Offset], L79_off, 33,
 ,[Offset], L80_off, 34,
 ,[Offset], Metal front, 35,
 ,[Offset], metal_legs, 36,
 ,[Offset], metal_parts01, 37,
 ,[Offset], metal_parts02, 38,
 ,[Offset], metal_parts03, 39,
 ,[Offset], metal_trim, 40,
 ,[Offset], metal01, 41,
 ,[Offset], metal-parts01 copy, 42,
 ,[Offset], mirrorsign01, 43,
 ,[Offset], nut01, 44,
 ,[Offset], partA01, 45,
 ,[Offset], plastic_clear01, 46,
 ,[Offset], plastic_clear02, 47,
 ,[Offset], playfield_bottom, 48,
 ,[Offset], playfield_top, 49,
 ,[Offset], plunger, 50,
 ,[Offset], plunger_Metal, 51,
 ,[Offset], Plunger_Plate_Baked, 52,
 ,[Offset], post red, 53,
 ,[Offset], post, 54,
 ,[Offset], postA01 copy, 55,
 ,[Offset], postA01, 56,
 ,[Offset], postB01, 57,
 ,[Offset], postC01, 58,
 ,[Offset], ramp_switch01, 59,
 ,[Offset], rampA, 60,
 ,[Offset], rampB, 61,
 ,[Offset], RampSticker01, 62,
 ,[Offset], RbumpA01, 63,
 ,[Offset], Red_targets, 64,
 ,[Offset], rubber color, 65,
 ,[Offset], rubber, 66,
 ,[Offset], rubberband_white01, 67,
 ,[Offset], rules, 68,
 ,[Offset], screw white, 69,
 ,[Offset], screw_silver01, 70,
 ,[Offset], SignA01, 71,
 ,[Offset], SignB01, 72,
 ,[Offset], SignC01, 73,
 ,[Offset], SkywayToll01, 74,
 ,[Offset], spiral01, 75,
 ,[Offset], spiral02, 76,
 ,[Offset], spiral03, 77,
 ,[Offset], table_fan01, 78,
 ,[Offset], table_trimmetal01, 79,
 ,[Offset], target_blue01, 80,
 ,[Offset], targetR01 copy, 81,
 ,[Offset], tileA01, 82,
 ,[Offset], tileB01, 83,
 ,[Offset], tileC01, 84,
 ,[Offset], tileD01, 85,
 ,[Offset], trough01, 86,
 ,[Offset], trough02, 87,
 ,[Offset], washer_silver01, 88,
 ,[Offset], white_target, 89,
 ,[Offset], wire_trigger01, 90,
 ,[Offset], wires01, 91,
 ,[Offset], wood_strip, 92,
 ,[Offset], wood01, 93,
 ,[Offset], backglassH_0, 94,
 ,[Offset], Target_Yellow, 95,
 ,[Offset], bulbs, 96,
 ,[Offset], red_flasher_off, 97,
 ,[Offset], PopBumperBody, 98,
 ,[Offset], Red_Bumper, 99,
 ,[Offset], yellow_Bumper, 100,
 ,[Offset], ClearPlasticPost_01, 101,
 ,[Offset], Silver Metal Screws_Temp, 102,
 ,[Offset], rails, 103,
 ,[Offset], Metal_Walls, 104,
 ,[Offset], base_metal, 105,
 ,[Offset], ramp1, 106,
 ,[Offset], ramp2, 107,
RSID_TWHIRLWIND_MODELS, 2930,,,
 ,[Offset], yellow_popbumpers, 0,
 ,[Offset], apron, 1,
 ,[Offset], backglass, 2,
 ,[Offset], black_rubber, 3,
 ,[Offset], blue_rubber, 4,
 ,[Offset], bulbs, 5,
 ,[Offset], cabinet, 6,
 ,[Offset], cabinet_metal, 7,
 ,[Offset], diverter, 8,
 ,[Offset], flashers, 9,
 ,[Offset], lamps, 10,
 ,[Offset], left_flipper, 11,
 ,[Offset], left_slingshot, 12,
 ,[Offset], metal, 13,
 ,[Offset], plastic_ramp01, 14,
 ,[Offset], plastic_ramp02, 15,
 ,[Offset], plastics, 16,
 ,[Offset], playfield, 17,
 ,[Offset], plunger, 18,
 ,[Offset], pop_bumper_ring, 19,
 ,[Offset], ramp_bar, 20,
 ,[Offset], ramp_entrance, 21,
 ,[Offset], red_popbumpers, 22,
 ,[Offset], red_target, 23,
 ,[Offset], right_flipper, 24,
 ,[Offset], right_slingshot, 25,
 ,[Offset], small_yellow_target, 26,
 ,[Offset], spinner, 27,
 ,[Offset], spinnerA, 28,
 ,[Offset], spinnerB, 29,
 ,[Offset], spinnerC, 30,
 ,[Offset], tileA, 31,
 ,[Offset], tileB, 32,
 ,[Offset], tileC, 33,
 ,[Offset], tileD, 34,
 ,[Offset], upper_flipper, 35,
 ,[Offset], vertical_plastics, 36,
 ,[Offset], white_rubber, 37,
 ,[Offset], white_target, 38,
 ,[Offset], wire, 39,
 ,[Offset], wires, 40,
 ,[Offset], wooden_rails, 41,
 ,[Offset], ramp_wire, 42,
 ,[Offset], switch, 43,
 ,[Offset], twowaygate, 44,
 ,[Offset], kicker, 45,
 ,[Offset], onewaygate, 46,
 ,[Offset], F07b, 47,
 ,[Offset], plastic_posts, 48,
 ,[Offset], screws, 49,
 ,[Offset], rails, 50,
 ,[Offset], metal_walls, 51,
 ,[Offset], metal_posts, 52,
 ,[Offset], alphaplanes1, 53,
 ,[Offset], alphaplanes2, 54,
 ,[Offset], alphaplanes3, 55,
 ,[Offset], inside_cabinet, 56,
 ,[Offset], left_slingshot_extended, 57,
 ,[Offset], right_slingshot_extended, 58,
 ,[Offset], backglass_lamps, 59,
 ,[Offset], plastics2, 60,
 ,[Offset], clear_plastics, 61,
RSID_TWHIRLWIND_MODELS_45K, 2931,,,
 ,[Offset], yellow_popbumpers, 0,
 ,[Offset], apron, 1,
 ,[Offset], backglass, 2,
 ,[Offset], black_rubber, 3,
 ,[Offset], blue_rubber, 4,
 ,[Offset], bulbs, 5,
 ,[Offset], cabinet, 6,
 ,[Offset], cabinet_metal, 7,
 ,[Offset], diverter, 8,
 ,[Offset], flashers, 9,
 ,[Offset], lamps, 10,
 ,[Offset], left_flipper, 11,
 ,[Offset], left_slingshot, 12,
 ,[Offset], metal, 13,
 ,[Offset], plastic_ramp01, 14,
 ,[Offset], plastic_ramp02, 15,
 ,[Offset], plastics, 16,
 ,[Offset], playfield, 17,
 ,[Offset], plunger, 18,
 ,[Offset], pop_bumper_ring, 19,
 ,[Offset], ramp_bar, 20,
 ,[Offset], ramp_entrance, 21,
 ,[Offset], red_popbumpers, 22,
 ,[Offset], red_target, 23,
 ,[Offset], right_flipper, 24,
 ,[Offset], right_slingshot, 25,
 ,[Offset], small_yellow_target, 26,
 ,[Offset], spinner, 27,
 ,[Offset], spinnerA, 28,
 ,[Offset], spinnerB, 29,
 ,[Offset], spinnerC, 30,
 ,[Offset], tileA, 31,
 ,[Offset], tileB, 32,
 ,[Offset], tileC, 33,
 ,[Offset], tileD, 34,
 ,[Offset], upper_flipper, 35,
 ,[Offset], vertical_plastics, 36,
 ,[Offset], white_rubber, 37,
 ,[Offset], white_target, 38,
 ,[Offset], wire, 39,
 ,[Offset], wires, 40,
 ,[Offset], wooden_rails, 41,
 ,[Offset], ramp_wire, 42,
 ,[Offset], switch, 43,
 ,[Offset], twowaygate, 44,
 ,[Offset], kicker, 45,
 ,[Offset], onewaygate, 46,
 ,[Offset], F07b, 47,
 ,[Offset], plastic_posts, 48,
 ,[Offset], screws, 49,
 ,[Offset], rails, 50,
 ,[Offset], metal_walls, 51,
 ,[Offset], metal_posts, 52,
 ,[Offset], alphaplanes1, 53,
 ,[Offset], alphaplanes2, 54,
 ,[Offset], alphaplanes3, 55,
 ,[Offset], inside_cabinet, 56,
 ,[Offset], left_slingshot_extended, 57,
 ,[Offset], right_slingshot_extended, 58,
 ,[Offset], backglass_lamps, 59,
 ,[Offset], plastics2, 60,
 ,[Offset], clear_plastics, 61,
RSID_TWHIRLWIND_REF_MODELS, 2932,,,
RSID_TWHIRLWIND_COLLISION, 2933,,,
 ,[Offset], wall06, 0,
 ,[Offset], apron, 1,
 ,[Offset], ball_drain, 2,
 ,[Offset], ball_drain2, 3,
 ,[Offset], bumperA, 4,
 ,[Offset], bumperB, 5,
 ,[Offset], diverter, 6,
 ,[Offset], floor, 7,
 ,[Offset], gate_back, 8,
 ,[Offset], gate_front, 9,
 ,[Offset], habi_trail, 10,
 ,[Offset], left_flipper_back, 11,
 ,[Offset], left_flipper_front, 12,
 ,[Offset], left_plungerlane, 13,
 ,[Offset], left_slingshot, 14,
 ,[Offset], plungerA, 15,
 ,[Offset], plungerB, 16,
 ,[Offset], plungerlane, 17,
 ,[Offset], pop_bumper, 18,
 ,[Offset], ramp_entrance, 19,
 ,[Offset], ramp1, 20,
 ,[Offset], ramp2, 21,
 ,[Offset], right_flipper_back, 22,
 ,[Offset], right_flipper_front, 23,
 ,[Offset], right_slingshot, 24,
 ,[Offset], rubber_stopper, 25,
 ,[Offset], rubber01, 26,
 ,[Offset], rubber02, 27,
 ,[Offset], rubber03, 28,
 ,[Offset], rubber04, 29,
 ,[Offset], rubber05, 30,
 ,[Offset], spinnerA, 31,
 ,[Offset], spinnerB, 32,
 ,[Offset], spinnerC, 33,
 ,[Offset], target_col, 34,
 ,[Offset], tile_col, 35,
 ,[Offset], top_arc, 36,
 ,[Offset], undertable, 37,
 ,[Offset], upper_flipper_back, 38,
 ,[Offset], upper_flipper_front, 39,
 ,[Offset], wall01, 40,
 ,[Offset], wall02, 41,
 ,[Offset], wall03, 42,
 ,[Offset], wall04, 43,
 ,[Offset], wall05, 44,
 ,[Offset], twowaygate, 45,
 ,[Offset], spinner, 46,
 ,[Offset], kicker, 47,
 ,[Offset], round_target, 48,
 ,[Offset], trap, 49,
 ,[Offset], trap_stopper, 50,
 ,[Offset], scoop, 51,
 ,[Offset], habi_trailstopper, 52,
RSID_TWHIRLWIND_PLACEMENT, 2934,,,
RSID_TWHIRLWIND_EMUROM, 2935,,,
 ,[Offset], whir_u26_l3, 0,
 ,[Offset], whir_u27_l3, 1,
 ,[Offset], whirl_attract, 2,
 ,[Offset], whirl_default, 3,
 ,[Offset], whirl_l3, 4,
 ,[Offset], whirl_l3, 5,
 ,[Offset], whirl_l3, 6,
 ,[Offset], whirl_l3, 7,
 ,[Offset], whirl_l3, 8,
 ,[Offset], whirl_l3, 9,
RSID_TWHIRLWIND_SOUNDS_START, 2936,,,
RSID_TWHIRLWIND_EMU_SOUNDS, 2937,,,
 ,[Offset], S0001_LP1, 0,
 ,[Offset], S0001_LP2, 1,
 ,[Offset], S0002_LP, 2,
 ,[Offset], S0003_LP, 3,
 ,[Offset], S0004_LP, 4,
 ,[Offset], S0005_LP, 5,
 ,[Offset], S0006_LP, 6,
 ,[Offset], S0007_LP1, 7,
 ,[Offset], S0007_LP2, 8,
 ,[Offset], S0008_LP1, 9,
 ,[Offset], S0008_LP2, 10,
 ,[Offset], S0009_LP, 11,
 ,[Offset], S000A_LP, 12,
 ,[Offset], S000B_LP, 13,
 ,[Offset], S000C_C3, 14,
 ,[Offset], S000D_C3, 15,
 ,[Offset], S000E_C3, 16,
 ,[Offset], S000F_C3, 17,
 ,[Offset], S0010_C3, 18,
 ,[Offset], S0011_C3, 19,
 ,[Offset], S0012_C3, 20,
 ,[Offset], S0013_C3, 21,
 ,[Offset], S0014_LP1, 22,
 ,[Offset], S0014_LP2, 23,
 ,[Offset], S0015_LP1, 24,
 ,[Offset], S0015_LP2, 25,
 ,[Offset], S0016_C3, 26,
 ,[Offset], S0020_C2, 27,
 ,[Offset], S0021_C2, 28,
 ,[Offset], S0022_C2, 29,
 ,[Offset], S0030_C2, 30,
 ,[Offset], S0031_C2, 31,
 ,[Offset], S0032_C2, 32,
 ,[Offset], S0033_C2, 33,
 ,[Offset], S0034_C2, 34,
 ,[Offset], S0035_C2, 35,
 ,[Offset], S0036_C2, 36,
 ,[Offset], S0037_C2, 37,
 ,[Offset], S0038_C2, 38,
 ,[Offset], S0039_C2, 39,
 ,[Offset], S003A_C2, 40,
 ,[Offset], S003B_C2, 41,
 ,[Offset], S003C_C2, 42,
 ,[Offset], S003D_C2, 43,
 ,[Offset], S003E_C2, 44,
 ,[Offset], S003F_C2, 45,
 ,[Offset], S0048_C2, 46,
 ,[Offset], S0050_C2, 47,
 ,[Offset], S0051_C2, 48,
 ,[Offset], S0052_C2, 49,
 ,[Offset], S0053_C2, 50,
 ,[Offset], S0054_C2, 51,
 ,[Offset], S0055_C2, 52,
 ,[Offset], S0056_C2, 53,
 ,[Offset], S0057_C2, 54,
 ,[Offset], S0082_C2, 55,
 ,[Offset], S0083_C2, 56,
 ,[Offset], S0084_C2, 57,
 ,[Offset], S0085_C2, 58,
 ,[Offset], S0086_C2, 59,
 ,[Offset], S0087_C2, 60,
 ,[Offset], S0088_C2, 61,
 ,[Offset], S0089_C2, 62,
 ,[Offset], S008A_C2, 63,
 ,[Offset], S008B_C2, 64,
 ,[Offset], S008C_C2, 65,
 ,[Offset], S008D_C2, 66,
 ,[Offset], S008E_C2, 67,
 ,[Offset], S008F_C2, 68,
 ,[Offset], S0090_C2, 69,
 ,[Offset], S0091_C2, 70,
 ,[Offset], S0092_C2, 71,
 ,[Offset], S0093_C2, 72,
 ,[Offset], S0094_C2, 73,
 ,[Offset], S0095_C2, 74,
 ,[Offset], S0096_C2, 75,
 ,[Offset], S0097_C2, 76,
 ,[Offset], S0098_C2, 77,
 ,[Offset], S0099_C2, 78,
 ,[Offset], S009A_C2, 79,
 ,[Offset], S009B_C2, 80,
 ,[Offset], S009C_C2, 81,
 ,[Offset], S009D_C2, 82,
 ,[Offset], S009E_C2, 83,
 ,[Offset], S009F_C2, 84,
 ,[Offset], S00A0_C2, 85,
 ,[Offset], S00A1_C2, 86,
 ,[Offset], S00A2_C2, 87,
 ,[Offset], S00A3_C2, 88,
 ,[Offset], S00A4_C2, 89,
 ,[Offset], S00A5_C2, 90,
 ,[Offset], S00A6_C2, 91,
 ,[Offset], S00A7_C2, 92,
 ,[Offset], S00A8_C2, 93,
 ,[Offset], S00A9_C2, 94,
 ,[Offset], S00AA_C2, 95,
 ,[Offset], S00AB_C2, 96,
 ,[Offset], S00AC_C2, 97,
 ,[Offset], S00AD_C2, 98,
 ,[Offset], S00AE_C2, 99,
 ,[Offset], S00AF_C2, 100,
 ,[Offset], S00B0_C2, 101,
 ,[Offset], S00B1_C2, 102,
 ,[Offset], S00B2_C2, 103,
 ,[Offset], S00B3_C2, 104,
 ,[Offset], S00B4_C2, 105,
 ,[Offset], S00B5_C2, 106,
 ,[Offset], S00B6_C2, 107,
 ,[Offset], S00B7_C2, 108,
 ,[Offset], S00B8_C2, 109,
 ,[Offset], S00B9_C2, 110,
 ,[Offset], S00BA_C2, 111,
 ,[Offset], S00BB_C2, 112,
 ,[Offset], S00BC_C2, 113,
 ,[Offset], S00BD_C2, 114,
 ,[Offset], S00BE_C2, 115,
 ,[Offset], S00BF_C2, 116,
 ,[Offset], S00C0_C2, 117,
 ,[Offset], S00C1_C2, 118,
 ,[Offset], S00C2_C2, 119,
 ,[Offset], S00C3_C2, 120,
 ,[Offset], S00C4_C2, 121,
 ,[Offset], S00C5_C2, 122,
 ,[Offset], S00C6_C2, 123,
 ,[Offset], S00C7_C2, 124,
 ,[Offset], S00C8_C2, 125,
 ,[Offset], S00C9_C2, 126,
 ,[Offset], S00CA_C2, 127,
 ,[Offset], S00CB_C2, 128,
 ,[Offset], S00CC_C2, 129,
 ,[Offset], S00CD_C2, 130,
 ,[Offset], S00CE_C2, 131,
 ,[Offset], S00CF_C2, 132,
 ,[Offset], S00D0_C2, 133,
 ,[Offset], S00D1_C2, 134,
 ,[Offset], S00D2_C2, 135,
 ,[Offset], S00D3_C2, 136,
 ,[Offset], S00D4_C2, 137,
 ,[Offset], S00D5_C2, 138,
 ,[Offset], S00D6_C2, 139,
 ,[Offset], S00D7_C2, 140,
 ,[Offset], S00D8_C2, 141,
 ,[Offset], S00D9_C2, 142,
 ,[Offset], S00DA_C2, 143,
 ,[Offset], S00DB_C2, 144,
 ,[Offset], S00DC_C2, 145,
 ,[Offset], S00DD_C2, 146,
 ,[Offset], S0101_C4, 147,
 ,[Offset], S0102_C4, 148,
 ,[Offset], S0103_C4, 149,
 ,[Offset], S0104_C4, 150,
 ,[Offset], S0105_C4, 151,
 ,[Offset], S0106_C4, 152,
 ,[Offset], S0107_C4, 153,
 ,[Offset], S0108_C4, 154,
 ,[Offset], S010A_C4, 155,
 ,[Offset], S010B_C4, 156,
 ,[Offset], S010C_C4, 157,
 ,[Offset], S010D_C4, 158,
 ,[Offset], S010E_C4, 159,
 ,[Offset], S010F_C4, 160,
 ,[Offset], S0110_C4, 161,
 ,[Offset], S0111_C4, 162,
 ,[Offset], S0112_C4, 163,
 ,[Offset], S0113_C4, 164,
 ,[Offset], S0114_C4, 165,
 ,[Offset], S0115_C4, 166,
 ,[Offset], S0116_C4, 167,
 ,[Offset], S0117_C4, 168,
 ,[Offset], S0118_C4, 169,
 ,[Offset], S0119_C4, 170,
 ,[Offset], S011A_C4, 171,
 ,[Offset], S011B_C4, 172,
 ,[Offset], S011C_C4, 173,
 ,[Offset], S011D_C4, 174,
 ,[Offset], S011E_C4, 175,
 ,[Offset], S011F_C4, 176,
 ,[Offset], S0120_C4, 177,
 ,[Offset], S0121_C4, 178,
 ,[Offset], S0122_C4, 179,
 ,[Offset], S0123_C4, 180,
 ,[Offset], S0124_C4, 181,
 ,[Offset], S0125_C4, 182,
RSID_TWHIRLWIND_MECH_SOUNDS, 2938,,,
 ,[Offset], ball_lock_kickout, 0,
 ,[Offset], celler_kickout, 1,
 ,[Offset], floor_spinners, 2,
 ,[Offset], ramp_down, 3,
 ,[Offset], ramp_up, 4,
 ,[Offset], drop_targets_reset, 5,
RSID_TWHIRLWIND_SOUNDS_END, 2939,,,
RSID_TWHIRLWIND_SAMPLES, 2940,,,
RSID_TWHIRLWIND_HUD, 2941,,,
RSID_TWHIRLWIND_VERSION, 2942,,,
RSID_TWHIRLWIND_END, 2943,,,
