<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREAD_EQUAL(3) manual page</TITLE>
	<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="Ross Johnson">
	<META NAME="CHANGED" CONTENT="20160229;19421115">
	<STYLE TYPE="text/css">
	<!--
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-AU" BGCOLOR="#ffffff" DIR="LTR">
<P LANG="en-GB"><A HREF="#toc">Table of Contents</A></P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P LANG="en-GB">pthread_equal - compare two thread identifiers 
</P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P LANG="en-GB"><B>#include &lt;pthread.h&gt;</B> 
</P>
<P LANG="en-GB"><B>int pthread_equal(pthread_t </B><I>thread1</I><B>,
pthread_t </B><I>thread2</I><B>);</B> 
</P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P LANG="en-GB"><B>pthread_equal</B> determines if two thread
identifiers refer to the same thread. 
</P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc3" NAME="sect3">Return
Value</A></H2>
<P LANG="en-GB">A non-zero value is returned if <I>thread1</I> and
<I>thread2</I> refer to the same thread. Otherwise, 0 is returned. 
</P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc4" NAME="sect4">Author</A></H2>
<P LANG="en-GB">Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<H2 LANG="en-GB" CLASS="western"><A HREF="#toc5" NAME="sect5">See
Also</A></H2>
<P LANG="en-GB"><A HREF="pthread_self.html"><B>pthread_self</B>(3)</A>
. 
</P>
<HR>
<P LANG="en-GB"><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P LANG="en-GB" STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P LANG="en-GB" STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P LANG="en-GB" STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P LANG="en-GB" STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P LANG="en-GB" STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Author</A>
		</P>
	<LI><P LANG="en-GB"><A HREF="#sect5" NAME="toc5">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>