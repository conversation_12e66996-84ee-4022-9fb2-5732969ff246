RSID_TFIREPOWER_START, 3650,,,
 ,[Offset], FIREPOWER_FLYER_1, 0,
 ,[Offset], FirePower\PBTFirepower, 1,
 ,[Offset], FirePower\InstructionsENG, 2,
 ,[Offset], <PERSON>Power\InstructionsFR, 3,
 ,[Offset], FirePower\InstructionsITAL, 4,
 ,[Offset], FirePower\InstructionsGERM, 5,
 ,[Offset], FirePower\InstructionsSPAN, 6,
 ,[Offset], FirePower\InstructionsPORT, 7,
 ,[Offset], FirePower\InstructionsDUTCH, 8,
RSID_TFIREPOWER_SCRIPT, 3651,,,
RSID_TFIREPOWER_LIGHTS, 3652,,,
RSID_TFIREPOWER_CAMERAS, 3653,,,
RSID_TFIREPOWER_LAMP_TEXTURES, 3654,,,
 ,[Offset], L01_unlit, 0,
 ,[Offset], L01_lit, 1,
 ,[Offset], L02_unlit, 2,
 ,[Offset], L02_lit, 3,
 ,[Offset], L03_unlit, 4,
 ,[Offset], L03_lit, 5,
 ,[Offset], L04_unlit, 6,
 ,[Offset], L04_lit, 7,
 ,[Offset], L05_unlit, 8,
 ,[Offset], L05_lit, 9,
 ,[Offset], L06_unlit, 10,
 ,[Offset], L06_lit, 11,
 ,[Offset], L07_unlit, 12,
 ,[Offset], L07_lit, 13,
 ,[Offset], L08_unlit, 14,
 ,[Offset], L08_lit, 15,
 ,[Offset], L09_unlit, 16,
 ,[Offset], L09_lit, 17,
 ,[Offset], L10_unlit, 18,
 ,[Offset], L10_lit, 19,
 ,[Offset], L11_unlit, 20,
 ,[Offset], L11_lit, 21,
 ,[Offset], L12_unlit, 22,
 ,[Offset], L12_lit, 23,
 ,[Offset], L13_unlit, 24,
 ,[Offset], L13_lit, 25,
 ,[Offset], L14_unlit, 26,
 ,[Offset], L14_lit, 27,
 ,[Offset], L15_unlit, 28,
 ,[Offset], L15_lit, 29,
 ,[Offset], L16_unlit, 30,
 ,[Offset], L16_lit, 31,
 ,[Offset], L17_unlit, 32,
 ,[Offset], L17_lit, 33,
 ,[Offset], L18_unlit, 34,
 ,[Offset], L18_lit, 35,
 ,[Offset], L19_unlit, 36,
 ,[Offset], L19_lit, 37,
 ,[Offset], L20_unlit, 38,
 ,[Offset], L20_lit, 39,
 ,[Offset], L21_unlit, 40,
 ,[Offset], L21_lit, 41,
 ,[Offset], L22_unlit, 42,
 ,[Offset], L22_lit, 43,
 ,[Offset], L23_unlit, 44,
 ,[Offset], L23_lit, 45,
 ,[Offset], L24_unlit, 46,
 ,[Offset], L24_lit, 47,
 ,[Offset], L25_unlit, 48,
 ,[Offset], L25_lit, 49,
 ,[Offset], L26_unlit, 50,
 ,[Offset], L26_lit, 51,
 ,[Offset], L27_unlit, 52,
 ,[Offset], L27_lit, 53,
 ,[Offset], L28_unlit, 54,
 ,[Offset], L28_lit, 55,
 ,[Offset], L29_unlit, 56,
 ,[Offset], L29_lit, 57,
 ,[Offset], L30_unlit, 58,
 ,[Offset], L30_lit, 59,
 ,[Offset], L31_unlit, 60,
 ,[Offset], L31_lit, 61,
 ,[Offset], L32_unlit, 62,
 ,[Offset], L32_lit, 63,
 ,[Offset], L33_unlit, 64,
 ,[Offset], L33_lit, 65,
 ,[Offset], L34_unlit, 66,
 ,[Offset], L34_lit, 67,
 ,[Offset], L35_unlit, 68,
 ,[Offset], L35_lit, 69,
 ,[Offset], L36_unlit, 70,
 ,[Offset], L36_lit, 71,
 ,[Offset], L37_unlit, 72,
 ,[Offset], L37_lit, 73,
 ,[Offset], L38_unlit, 74,
 ,[Offset], L38_lit, 75,
 ,[Offset], L39_unlit, 76,
 ,[Offset], L39_lit, 77,
 ,[Offset], L40_unlit, 78,
 ,[Offset], L40_lit, 79,
 ,[Offset], L41_unlit, 80,
 ,[Offset], L41_lit, 81,
 ,[Offset], L42_unlit, 82,
 ,[Offset], L42_lit, 83,
 ,[Offset], L43_unlit, 84,
 ,[Offset], L43_lit, 85,
 ,[Offset], L44_unlit, 86,
 ,[Offset], L44_lit, 87,
 ,[Offset], L45_unlit, 88,
 ,[Offset], L45_lit, 89,
 ,[Offset], L46_unlit, 90,
 ,[Offset], L46_lit, 91,
 ,[Offset], LB01_unlit, 92,
 ,[Offset], LB01_lit, 93,
 ,[Offset], LB02_unlit, 94,
 ,[Offset], LB02_lit, 95,
 ,[Offset], LB03_unlit, 96,
 ,[Offset], LB03_lit, 97,
 ,[Offset], LB04_unlit, 98,
 ,[Offset], LB04_lit, 99,
RSID_TFIREPOWER_TEXTURES, 3655,,,
 ,[Offset], backGlass, 0,
 ,[Offset], backGlassA_0, 1,
 ,[Offset], backGlassB_0, 2,
 ,[Offset], backGlassC_0, 3,
 ,[Offset], backGlassD_0, 4,
 ,[Offset], backGlassE_0, 5,
 ,[Offset], backGlassF_0, 6,
 ,[Offset], backGlassG_0, 7,
 ,[Offset], backGlassH_0, 8,
 ,[Offset], blackscrew, 9,
 ,[Offset], bumper A, 10,
 ,[Offset], bumper B, 11,
 ,[Offset], bumpers C, 12,
 ,[Offset], bumpers D, 13,
 ,[Offset], Buttons_Parts, 14,
 ,[Offset], clear, 15,
 ,[Offset], CoinSlots, 16,
 ,[Offset], color wheel, 17,
 ,[Offset], flipper, 18,
 ,[Offset], FP_A1_0, 19,
 ,[Offset], FP_A2_0, 20,
 ,[Offset], FP_A3_0, 21,
 ,[Offset], FP_A4_0, 22,
 ,[Offset], FP_A5_0, 23,
 ,[Offset], FP_A6_0, 24,
 ,[Offset], FP_A7_0, 25,
 ,[Offset], FP_A8_0, 26,
 ,[Offset], FP_B1_0, 27,
 ,[Offset], FP_B2_0, 28,
 ,[Offset], FP_B3_0, 29,
 ,[Offset], FP_B4_0, 30,
 ,[Offset], FP_B5_0, 31,
 ,[Offset], FP_B6_0, 32,
 ,[Offset], FP_B7_0, 33,
 ,[Offset], FP_B8_0, 34,
 ,[Offset], FP_C1_0, 35,
 ,[Offset], FP_C2_0, 36,
 ,[Offset], FP_C3_0, 37,
 ,[Offset], FP_C4_0, 38,
 ,[Offset], FP_C5_0, 39,
 ,[Offset], FP_C6_0, 40,
 ,[Offset], FP_C7_0, 41,
 ,[Offset], FP_C8_0, 42,
 ,[Offset], FP_D1_0, 43,
 ,[Offset], FP_D2_0, 44,
 ,[Offset], FP_D3_0, 45,
 ,[Offset], FP_D4_0, 46,
 ,[Offset], FP_D5_0, 47,
 ,[Offset], FP_D6_0, 48,
 ,[Offset], FP_D7_0, 49,
 ,[Offset], FP_D8_0, 50,
 ,[Offset], FP_E1_0, 51,
 ,[Offset], FP_E2_0, 52,
 ,[Offset], FP_E3_0, 53,
 ,[Offset], FP_E4_0, 54,
 ,[Offset], FP_E5_0, 55,
 ,[Offset], FP_E6_0, 56,
 ,[Offset], FP_E7_0, 57,
 ,[Offset], FP_E8_0, 58,
 ,[Offset], FP_F1_0, 59,
 ,[Offset], FP_F2_0, 60,
 ,[Offset], FP_F3_0, 61,
 ,[Offset], FP_F4_0, 62,
 ,[Offset], FP_F5_0, 63,
 ,[Offset], FP_F6_0, 64,
 ,[Offset], FP_F7_0, 65,
 ,[Offset], FP_F8_0, 66,
 ,[Offset], FP_G1_0, 67,
 ,[Offset], FP_G2_0, 68,
 ,[Offset], FP_G3_0, 69,
 ,[Offset], FP_G4_0, 70,
 ,[Offset], FP_G5_0, 71,
 ,[Offset], FP_G6_0, 72,
 ,[Offset], FP_G7_0, 73,
 ,[Offset], FP_G8_0, 74,
 ,[Offset], FP_H1_0, 75,
 ,[Offset], FP_H2_0, 76,
 ,[Offset], FP_H3_0, 77,
 ,[Offset], FP_H4_0, 78,
 ,[Offset], FP_H5_0, 79,
 ,[Offset], FP_H6_0, 80,
 ,[Offset], FP_H7_0, 81,
 ,[Offset], FP_H8_0, 82,
 ,[Offset], FP_TableFront_Side, 83,
 ,[Offset], glass, 84,
 ,[Offset], GorGar_Table_down_B, 85,
 ,[Offset], jelly, 86,
 ,[Offset], lightbulb, 87,
 ,[Offset], metal front, 88,
 ,[Offset], metal-parts01 copy, 89,
 ,[Offset], metal_legs, 90,
 ,[Offset], metal_parts01, 91,
 ,[Offset], metal_parts02, 92,
 ,[Offset], metal_trim, 93,
 ,[Offset], plunger, 94,
 ,[Offset], plunger_Metal, 95,
 ,[Offset], Plunger_Plate_Baked, 96,
 ,[Offset], post red, 97,
 ,[Offset], post, 98,
 ,[Offset], rubber, 99,
 ,[Offset], screw alt, 100,
 ,[Offset], screw white, 101,
 ,[Offset], screw, 102,
 ,[Offset], speaker, 103,
 ,[Offset], spinner, 104,
 ,[Offset], TableRules_L1_0, 105,
 ,[Offset], TableRules_L2_0, 106,
 ,[Offset], TableRules_L3_0, 107,
 ,[Offset], TableRules_L4_0, 108,
 ,[Offset], TableRules_R1_0, 109,
 ,[Offset], TableRules_R2_0, 110,
 ,[Offset], TableRules_R3_0, 111,
 ,[Offset], TableRules_R4_0, 112,
 ,[Offset], target, 113,
 ,[Offset], tile, 114,
 ,[Offset], tmp_gray, 115,
 ,[Offset], tmp_orange, 116,
 ,[Offset], playfield_top, 117,
 ,[Offset], playfield_bottom, 118,
 ,[Offset], black_wood, 119,
 ,[Offset], WoodFine, 120,
 ,[Offset], rails, 121,
 ,[Offset], Metal_Screws, 122,
 ,[Offset], white_nut, 123,
 ,[Offset], transparent_gate, 124,
RSID_TFIREPOWER_MODELS, 3656,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper C, 3,
 ,[Offset], bumper D, 4,
 ,[Offset], bumper E, 5,
 ,[Offset], bumper F, 6,
 ,[Offset], cabinet, 7,
 ,[Offset], flipper A bottom, 8,
 ,[Offset], flipper A, 9,
 ,[Offset], flipper B bottom, 10,
 ,[Offset], flipper B, 11,
 ,[Offset], floor, 12,
 ,[Offset], gate oneway, 13,
 ,[Offset], gate oneway2, 14,
 ,[Offset], generic A, 15,
 ,[Offset], generic D, 16,
 ,[Offset], generic E, 17,
 ,[Offset], generic F, 18,
 ,[Offset], glass, 19,
 ,[Offset], metal, 20,
 ,[Offset], parts, 21,
 ,[Offset], plastic, 22,
 ,[Offset], plunger, 23,
 ,[Offset], pop bumper A, 24,
 ,[Offset], slingshot A ext, 25,
 ,[Offset], slingshot A, 26,
 ,[Offset], slingshot B ext, 27,
 ,[Offset], slingshot B, 28,
 ,[Offset], spinner, 29,
 ,[Offset], star, 30,
 ,[Offset], target B, 31,
 ,[Offset], target C, 32,
 ,[Offset], target, 33,
 ,[Offset], tile, 34,
 ,[Offset], trap A, 35,
 ,[Offset], trap B, 36,
 ,[Offset], trap C, 37,
 ,[Offset], wire short, 38,
 ,[Offset], lamps, 39,
 ,[Offset], pop_bumpers, 40,
 ,[Offset], wooden_rails, 41,
 ,[Offset], cabinet_metal, 42,
 ,[Offset], metal_nuts, 43,
 ,[Offset], metal_posts, 44,
 ,[Offset], screws, 45,
 ,[Offset], rubbers, 46,
 ,[Offset], lights, 47,
 ,[Offset], white_nuts, 48,
 ,[Offset], flipper, 49,
RSID_TFIREPOWER_MODELS_LODS, 3657,,,
 ,[Offset], backglass, 0,
 ,[Offset], bumper A, 1,
 ,[Offset], bumper B, 2,
 ,[Offset], bumper C, 3,
 ,[Offset], bumper D, 4,
 ,[Offset], bumper E, 5,
 ,[Offset], bumper F, 6,
 ,[Offset], cabinet, 7,
 ,[Offset], flipper A bottom, 8,
 ,[Offset], flipper A, 9,
 ,[Offset], flipper B bottom, 10,
 ,[Offset], flipper B, 11,
 ,[Offset], floor, 12,
 ,[Offset], gate oneway, 13,
 ,[Offset], gate oneway2, 14,
 ,[Offset], generic A, 15,
 ,[Offset], generic D, 16,
 ,[Offset], generic E, 17,
 ,[Offset], generic F, 18,
 ,[Offset], glass, 19,
 ,[Offset], metal, 20,
 ,[Offset], parts, 21,
 ,[Offset], plastic, 22,
 ,[Offset], plunger, 23,
 ,[Offset], pop bumper A, 24,
 ,[Offset], slingshot A ext, 25,
 ,[Offset], slingshot A, 26,
 ,[Offset], slingshot B ext, 27,
 ,[Offset], slingshot B, 28,
 ,[Offset], spinner, 29,
 ,[Offset], star, 30,
 ,[Offset], target B, 31,
 ,[Offset], target C, 32,
 ,[Offset], target, 33,
 ,[Offset], tile, 34,
 ,[Offset], trap A, 35,
 ,[Offset], trap B, 36,
 ,[Offset], trap C, 37,
 ,[Offset], wire short, 38,
 ,[Offset], lamps, 39,
 ,[Offset], pop_bumpers, 40,
 ,[Offset], wooden_rails, 41,
 ,[Offset], cabinet_metal, 42,
 ,[Offset], metal_nuts, 43,
 ,[Offset], metal_posts, 44,
 ,[Offset], screws, 45,
 ,[Offset], rubbers, 46,
 ,[Offset], lights, 47,
 ,[Offset], white_nuts, 48,
 ,[Offset], flipper, 49,
RSID_TFIREPOWER_COLLISIONS, 3658,,,
 ,[Offset], bumper A col, 0,
 ,[Offset], bumper B col, 1,
 ,[Offset], bumper C col, 2,
 ,[Offset], bumper D col, 3,
 ,[Offset], bumper E col, 4,
 ,[Offset], bumper F col, 5,
 ,[Offset], col alt wall 01, 6,
 ,[Offset], col alt wall 02, 7,
 ,[Offset], col alt wall 03, 8,
 ,[Offset], col alt wall 04, 9,
 ,[Offset], col alt wall 05, 10,
 ,[Offset], col alt wall 06, 11,
 ,[Offset], col alt wall 07, 12,
 ,[Offset], col alt wall 08, 13,
 ,[Offset], col alt wall 09, 14,
 ,[Offset], col alt wall top, 15,
 ,[Offset], col arc 01, 16,
 ,[Offset], col arc 02, 17,
 ,[Offset], col arc 03, 18,
 ,[Offset], col arc 04, 19,
 ,[Offset], col arc 05, 20,
 ,[Offset], col arc 06, 21,
 ,[Offset], col floor 01, 22,
 ,[Offset], col floor 02, 23,
 ,[Offset], col floor 03, 24,
 ,[Offset], col floor 04, 25,
 ,[Offset], col wall 01, 26,
 ,[Offset], col wall 02, 27,
 ,[Offset], col wall 03, 28,
 ,[Offset], col wall 04, 29,
 ,[Offset], col wall 05, 30,
 ,[Offset], col wall 06, 31,
 ,[Offset], col wall 07, 32,
 ,[Offset], col wall 08, 33,
 ,[Offset], flipper A B col, 34,
 ,[Offset], flipper A F col, 35,
 ,[Offset], flipper B B col, 36,
 ,[Offset], flipper B F col, 37,
 ,[Offset], gate oneway col, 38,
 ,[Offset], gate oneway2 col, 39,
 ,[Offset], generic A col, 40,
 ,[Offset], RightTrapWall, 41,
 ,[Offset], TopTrapWall, 42,
 ,[Offset], generic F col, 43,
 ,[Offset], platform 1, 44,
 ,[Offset], plunger area col, 45,
 ,[Offset], plunger col, 46,
 ,[Offset], pop bumper A col, 47,
 ,[Offset], slingshot A col, 48,
 ,[Offset], slingshot B col, 49,
 ,[Offset], star col, 50,
 ,[Offset], target B col, 51,
 ,[Offset], target C col, 52,
 ,[Offset], target col, 53,
 ,[Offset], tile col, 54,
 ,[Offset], spinner, 55,
 ,[Offset], trap A, 56,
 ,[Offset], trap B, 57,
 ,[Offset], trap C, 58,
 ,[Offset], flipper_L_back, 59,
 ,[Offset], flipper_L_front, 60,
 ,[Offset], flipper_r_back, 61,
 ,[Offset], flipper_r_front, 62,
 ,[Offset], one_way_gate_back, 63,
 ,[Offset], one_way_gate_front, 64,
 ,[Offset], one_way_gate_3_back, 65,
 ,[Offset], one_way_gate_3_front, 66,
 ,[Offset], one_way_gate_2_back, 67,
 ,[Offset], one_way_gate_2_front, 68,
RSID_TFIREPOWER_PLACEMENT, 3659,,,
 ,[Offset], placement, 0,
 ,[Offset], flipper, 1,
RSID_TFIREPOWER_SOUNDS, 3660,,,
 ,[Offset], ball_locks_complete, 0,
 ,[Offset], ballroll02, 1,
 ,[Offset], bonus_counter2, 2,
 ,[Offset], bonus_counter_loop, 3,
 ,[Offset], bumpers, 4,
 ,[Offset], bumpers_mech_only, 5,
 ,[Offset], bumpers_w_mech, 6,
 ,[Offset], drop_targets, 7,
 ,[Offset], drop_targets_complete, 8,
 ,[Offset], Endgame, 9,
 ,[Offset], enemy_deployed, 10,
 ,[Offset], enemy_deployed_w_fx, 11,
 ,[Offset], enemy_deployed_you, 12,
 ,[Offset], fire, 13,
 ,[Offset], fire_deployed_you, 14,
 ,[Offset], fire_deployed_you_w_fx, 15,
 ,[Offset], fire_one, 16,
 ,[Offset], fire_w_fx, 17,
 ,[Offset], firepower, 18,
 ,[Offset], firepower_w_fx, 19,
 ,[Offset], firepower_deployed_you, 20,
 ,[Offset], FP_back_1, 21,
 ,[Offset], FP_back_2, 22,
 ,[Offset], FP_back_3, 23,
 ,[Offset], FP_back_4, 24,
 ,[Offset], FP_back_5, 25,
 ,[Offset], FP_back_6, 26,
 ,[Offset], FP_back_7, 27,
 ,[Offset], FP_back_8, 28,
 ,[Offset], FP_back_9, 29,
 ,[Offset], FP_back_10, 30,
 ,[Offset], FP_back_11, 31,
 ,[Offset], FP_back_12, 32,
 ,[Offset], FP_back_13, 33,
 ,[Offset], FP_back_14, 34,
 ,[Offset], FP_back_15, 35,
 ,[Offset], FP_back_16, 36,
 ,[Offset], FP_back_17, 37,
 ,[Offset], FP_back_18, 38,
 ,[Offset], FP_back_19, 39,
 ,[Offset], FP_back_20, 40,
 ,[Offset], FP_back_21, 41,
 ,[Offset], FP_back_22, 42,
 ,[Offset], FP_back_23, 43,
 ,[Offset], FP_back_24, 44,
 ,[Offset], FP_back_25, 45,
 ,[Offset], FP_back_26, 46,
 ,[Offset], FP_back_27, 47,
 ,[Offset], FP_back_28, 48,
 ,[Offset], FP_back_29, 49,
 ,[Offset], FP_back_30, 50,
 ,[Offset], free_game, 51,
 ,[Offset], game_begin, 52,
 ,[Offset], kickback, 53,
 ,[Offset], left_trap, 54,
 ,[Offset], left_trap_mech, 55,
 ,[Offset], left_trap_w_mech, 56,
 ,[Offset], loop1_asending, 57,
 ,[Offset], loop1_single, 58,
 ,[Offset], loop2_asending, 59,
 ,[Offset], loop2_single, 60,
 ,[Offset], loop3_asending, 61,
 ,[Offset], loop3_single, 62,
 ,[Offset], loop4_asending, 63,
 ,[Offset], loop4_single, 64,
 ,[Offset], loop5_asending, 65,
 ,[Offset], loop5_single, 66,
 ,[Offset], loop6_asending, 67,
 ,[Offset], loop6_single, 68,
 ,[Offset], loop7_single, 69,
 ,[Offset], loop8_single, 70,
 ,[Offset], loop9_single, 71,
 ,[Offset], loop10_single, 72,
 ,[Offset], loop11_single, 73,
 ,[Offset], loop12_ asending, 74,
 ,[Offset], loop12_ single, 75,
 ,[Offset], loop13_asending, 76,
 ,[Offset], loop13_single, 77,
 ,[Offset], loop14_single, 78,
 ,[Offset], loop15_single, 79,
 ,[Offset], loop16_single, 80,
 ,[Offset], loop17_asending, 81,
 ,[Offset], loop18_single, 82,
 ,[Offset], mission_acomplished, 83,
 ,[Offset], mission_deployed_you, 84,
 ,[Offset], new_slingshot, 85,
 ,[Offset], new_start_up, 86,
 ,[Offset], right_side_targets, 87,
 ,[Offset], right_side_targets_complete, 88,
 ,[Offset], slingshot2, 89,
 ,[Offset], slingshot, 90,
 ,[Offset], spinner_1, 91,
 ,[Offset], spinner_2, 92,
 ,[Offset], spinner_3, 93,
 ,[Offset], spinner_4, 94,
 ,[Offset], spinner_5, 95,
 ,[Offset], spinner_6, 96,
 ,[Offset], spinner_7, 97,
 ,[Offset], spinner_8, 98,
 ,[Offset], spinner_9, 99,
 ,[Offset], spinner_10, 100,
 ,[Offset], spinner_11, 101,
 ,[Offset], spinner_12, 102,
 ,[Offset], spinner_13, 103,
 ,[Offset], spinner_14, 104,
 ,[Offset], spinner_15, 105,
 ,[Offset], spinner_16, 106,
 ,[Offset], spinner_17, 107,
 ,[Offset], spinner_18, 108,
 ,[Offset], spinner_19, 109,
 ,[Offset], spinner_20, 110,
 ,[Offset], spinner_21, 111,
 ,[Offset], spinner_22, 112,
 ,[Offset], spinner_23, 113,
 ,[Offset], spinner_24, 114,
 ,[Offset], spinner_25, 115,
 ,[Offset], spinner_26, 116,
 ,[Offset], spinner_begin, 117,
 ,[Offset], spinner_end, 118,
 ,[Offset], spinner_loop_single, 119,
 ,[Offset], targets_complete, 120,
 ,[Offset], three, 121,
 ,[Offset], TILT, 122,
 ,[Offset], tilt_reference_firepower, 123,
 ,[Offset], top_gates, 124,
 ,[Offset], top_gates_complete, 125,
 ,[Offset], two, 126,
 ,[Offset], unknown_1, 127,
 ,[Offset], unknown_2_loop, 128,
 ,[Offset], unknown_3, 129,
 ,[Offset], unknown_4, 130,
 ,[Offset], wire_rollovers, 131,
 ,[Offset], you_are_enemy, 132,
 ,[Offset], you_are_enemy_w_fx, 133,
 ,[Offset], you_deployed_enemy_mission, 134,
 ,[Offset], you_deployed_enemy_mission_w_fx, 135,
 ,[Offset], you_deployed_firepower, 136,
 ,[Offset], you_won_1_mission, 137,
 ,[Offset], firepower_deployed, 138,
 ,[Offset], 1_2_3_acomplished, 139,
 ,[Offset], spinner_short, 140,
RSID_TFIREPOWER_HUD_LD, 3661,,,
RSID_TFIREPOWER_HUD, 3662,,,
RSID_TFIREPOWER_HUD_HD, 3663,,,
RSID_FONTTABLE_TFIREPOWER_FONT, 3664,,,
RSID_FONT_TFIREPOWER_HUD, 3665,,,
RSID_FONTTABLE_TFIREPOWER_FONTHD, 3666,,,
RSID_FONT_TFIREPOWER_HUDHD, 3667,,,
RSID_FONTTABLE_TFIREPOWER_FONTHDD, 3668,,,
RSID_FONT_TFIREPOWER_HUDHDD, 3669,,,
RSID_TFIREPOWER_VERSION, 3670,,,
RSID_TFIREPOWER_END, 3671,,,
