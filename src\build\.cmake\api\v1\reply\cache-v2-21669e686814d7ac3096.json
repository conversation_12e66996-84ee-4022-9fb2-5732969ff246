{"entries": [{"name": "BZIP2_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "BZIP2_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/bz2d.lib"}, {"name": "BZIP2_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/bz2.lib"}, {"name": "BZIP2_NEED_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Have symbol BZ2_bzCompressInit"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/sss/src/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "30"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "5"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": "Semicolon separated list of supported configuration types, only supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything else will be ignored."}], "type": "STRING", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "HELPSTRING", "value": "Intel C++ Compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Intel/oneAPI/compiler/latest/bin/icx.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "HELPSTRING", "value": "Intel C Compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Intel/oneAPI/compiler/latest/bin/icx.exe"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS"}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/sss/src/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREADS_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthreads"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREAD_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthread"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/sss/src"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files (x86)/PS4Emulator"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_MT-NOTFOUND"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "PS4Emulator"}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "rc"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_TOOLCHAIN_FILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The CMake toolchain file"}], "type": "FILEPATH", "value": "D:/vcpkg/scripts/buildsystems/vcpkg.cmake"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "FFMPEG_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "TRUE"}, {"name": "FFMPEG_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avdevice.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avfilter.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avformat.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avformat.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avcodec.lib;optimized;D:/vcpkg/installed/x64-windows/lib/swresample.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swresample.lib;optimized;D:/vcpkg/installed/x64-windows/lib/swscale.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swscale.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avutil.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avutil.lib;psapi;uuid;oleaut32;shlwapi;gdi32;vfw32;secur32;ws2_32;mfuuid;strmiids;ole32;user32;bcrypt"}, {"name": "FFMPEG_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-ID:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "FFMPEG_PKGS_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-LD:/vcpkg/installed/x64-windows/debug/lib;-lavdevice;psapi.lib;-lole32;-lstrmiids;-luuid;-loleaut32;-lshlwapi;-lgdi32;-lvfw32;-lavfilter;-lavformat;secur32.lib;-lws2_32;-lavcodec;mfuuid.lib;-lole32;-lstrmiids;-lole32;-luser32;-lswresample;-lswscale;-lavutil;user32.lib;-lbcrypt"}, {"name": "FFMPEG_PKGS_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "psapi.lib;secur32.lib;mfuuid.lib;user32.lib"}, {"name": "FFMPEG_PKGS_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avdevice;ole32;strmiids;uuid;oleaut32;shlwapi;gdi32;vfw32;avfilter;avformat;ws2_32;avcodec;ole32;strmiids;ole32;user32;swresample;swscale;avutil;bcrypt"}, {"name": "FFMPEG_PKGS_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-ID:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-LD:/vcpkg/installed/x64-windows/debug/lib;-lavdevice;psapi.lib;-lole32;-lstrmiids;-luuid;-loleaut32;-lshlwapi;-lgdi32;-lvfw32;-lavfilter;-lavformat;secur32.lib;-lws2_32;-lavcodec;mfuuid.lib;-lole32;-lstrmiids;-lole32;-luser32;-lswresample;-lswscale;-lavutil;user32.lib;-lbcrypt"}, {"name": "FFMPEG_PKGS_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "psapi.lib;secur32.lib;mfuuid.lib;user32.lib"}, {"name": "FFMPEG_PKGS_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avdevice;ole32;strmiids;uuid;oleaut32;shlwapi;gdi32;vfw32;avfilter;avformat;ws2_32;avcodec;ole32;strmiids;ole32;user32;swresample;swscale;avutil;bcrypt"}, {"name": "FFMPEG_PKGS_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FFMPEG_PKGS_libavcodec_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libavcodec_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libavcodec_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavcodec"}, {"name": "FFMPEG_PKGS_libavcodec_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libavcodec_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "61.19.101"}, {"name": "FFMPEG_PKGS_libavdevice_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libavdevice_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libavdevice_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavdevice"}, {"name": "FFMPEG_PKGS_libavdevice_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libavdevice_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "61.3.100"}, {"name": "FFMPEG_PKGS_libavfilter_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libavfilter_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libavfilter_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavfilter"}, {"name": "FFMPEG_PKGS_libavfilter_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libavfilter_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "10.4.100"}, {"name": "FFMPEG_PKGS_libavformat_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libavformat_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libavformat_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavformat"}, {"name": "FFMPEG_PKGS_libavformat_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libavformat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "61.7.100"}, {"name": "FFMPEG_PKGS_libavutil_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libavutil_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libavutil_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "liba<PERSON><PERSON>"}, {"name": "FFMPEG_PKGS_libavutil_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libavutil_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "59.39.100"}, {"name": "FFMPEG_PKGS_libswresample_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libswresample_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libswresample_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libswresample"}, {"name": "FFMPEG_PKGS_libswresample_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libswresample_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "5.3.100"}, {"name": "FFMPEG_PKGS_libswscale_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/../include"}, {"name": "FFMPEG_PKGS_libswscale_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "FFMPEG_PKGS_libswscale_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libswscale"}, {"name": "FFMPEG_PKGS_libswscale_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug"}, {"name": "FFMPEG_PKGS_libswscale_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "8.3.100"}, {"name": "FFMPEG_libavcodec_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libavcodec_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avcodec.lib"}, {"name": "FFMPEG_libavcodec_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avcodec.lib"}, {"name": "FFMPEG_libavcodec_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/avcodec.lib"}, {"name": "FFMPEG_libavcodec_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "61.19.101"}, {"name": "FFMPEG_libavdevice_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libavdevice_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avdevice.lib"}, {"name": "FFMPEG_libavdevice_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avdevice.lib"}, {"name": "FFMPEG_libavdevice_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/avdevice.lib"}, {"name": "FFMPEG_libavdevice_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "61.3.100"}, {"name": "FFMPEG_libavfilter_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libavfilter_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avfilter.lib"}, {"name": "FFMPEG_libavfilter_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avfilter.lib"}, {"name": "FFMPEG_libavfilter_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/avfilter.lib"}, {"name": "FFMPEG_libavfilter_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "10.4.100"}, {"name": "FFMPEG_libavformat_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libavformat_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avformat.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avformat.lib"}, {"name": "FFMPEG_libavformat_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avformat.lib"}, {"name": "FFMPEG_libavformat_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/avformat.lib"}, {"name": "FFMPEG_libavformat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "61.7.100"}, {"name": "FFMPEG_libavutil_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libavutil_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/avutil.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avutil.lib"}, {"name": "FFMPEG_libavutil_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avutil.lib"}, {"name": "FFMPEG_libavutil_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/avutil.lib"}, {"name": "FFMPEG_libavutil_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "59.39.100"}, {"name": "FFMPEG_libswresample_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libswresample_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/swresample.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swresample.lib"}, {"name": "FFMPEG_libswresample_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/swresample.lib"}, {"name": "FFMPEG_libswresample_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/swresample.lib"}, {"name": "FFMPEG_libswresample_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "5.3.100"}, {"name": "FFMPEG_libswscale_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "FFMPEG_libswscale_LIBRARY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "optimized;D:/vcpkg/installed/x64-windows/lib/swscale.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swscale.lib"}, {"name": "FFMPEG_libswscale_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/swscale.lib"}, {"name": "FFMPEG_libswscale_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/swscale.lib"}, {"name": "FFMPEG_libswscale_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "8.3.100"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_BZip2", "properties": [{"name": "HELPSTRING", "value": "Details about finding BZip2"}], "type": "INTERNAL", "value": "[optimized;D:/vcpkg/installed/x64-windows/debug/lib/bz2.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/bz2d.lib][D:/vcpkg/installed/x64-windows/include][v1.0.8()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_FFMPEG", "properties": [{"name": "HELPSTRING", "value": "Details about finding FFMPEG"}], "type": "INTERNAL", "value": "[optimized;D:/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avdevice.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avfilter.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avformat.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avformat.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avcodec.lib;optimized;D:/vcpkg/installed/x64-windows/lib/swresample.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swresample.lib;optimized;D:/vcpkg/installed/x64-windows/lib/swscale.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/swscale.lib;optimized;D:/vcpkg/installed/x64-windows/lib/avutil.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/avutil.lib;psapi;uuid;oleaut32;shlwapi;gdi32;vfw32;secur32;ws2_32;mfuuid;strmiids;ole32;user32;bcrypt][D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/debug/lib][D:/vcpkg/installed/x64-windows/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenSSL"}], "type": "INTERNAL", "value": "[optimized;D:/vcpkg/installed/x64-windows/lib/libcrypto.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/libcrypto.lib][D:/vcpkg/installed/x64-windows/include][c ][v3.4.1()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PNG", "properties": [{"name": "HELPSTRING", "value": "Details about finding PNG"}], "type": "INTERNAL", "value": "[optimized;D:/vcpkg/installed/x64-windows/lib/libpng16.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/libpng16d.lib][D:/vcpkg/installed/x64-windows/include][v1.6.46()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[D:/vcpkg/installed/x64-windows/tools/pkgconf/pkgconf.exe][v2.4.3()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Vulkan", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON><PERSON>"}], "type": "INTERNAL", "value": "[D:/VulkanSDK/1.4.309.0/Lib/vulkan-1.lib][D:/VulkanSDK/1.4.309.0/Include][cfound components: glslc glslangValidator ][v1.4.309()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZLIB", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZLIB"}], "type": "INTERNAL", "value": "[optimized;D:/vcpkg/installed/x64-windows/lib/zlib.lib;debug;D:/vcpkg/installed/x64-windows/debug/lib/zlibd.lib][D:/vcpkg/installed/x64-windows/include][c ][v1.3.1()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_n<PERSON>hmann_json", "properties": [{"name": "HELPSTRING", "value": "Details about finding n<PERSON><PERSON>_<PERSON>son"}], "type": "INTERNAL", "value": "[D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfig.cmake][v3.11.3()]"}, {"name": "FREETYPE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "vcpkg"}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib/freetyped.lib"}, {"name": "FREETYPE_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "vcpkg"}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/lib/freetype.lib"}, {"name": "Freetype_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Freetype."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/freetype"}, {"name": "HALF_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "LIB_EAY_DEBUG", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/libcrypto.lib"}, {"name": "LIB_EAY_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LIB_EAY_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LIB_EAY_RELEASE", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/libcrypto.lib"}, {"name": "ONEAPI_ROOT", "properties": [{"name": "HELPSTRING", "value": "Intel oneAPI installation path"}], "type": "PATH", "value": "C:/Program Files (x86)/Intel/oneAPI"}, {"name": "OPENSSL_APPLINK_SOURCE", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/include/openssl/applink.c"}, {"name": "OPENSSL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/vcpkg/installed/x64-windows/share/ffmpeg/../../../x64-windows/tools/pkgconf/pkgconf.exe"}, {"name": "PNG_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/libpng16d.lib"}, {"name": "PNG_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/libpng16.lib"}, {"name": "PNG_PNG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "PS4Emulator_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/sss/src/build"}, {"name": "PS4Emulator_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "PS4Emulator_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/sss/src"}, {"name": "SDL2_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for SDL2."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/sdl2"}, {"name": "SSL_EAY_DEBUG", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/libssl.lib"}, {"name": "SSL_EAY_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "SSL_EAY_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "SSL_EAY_RELEASE", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/libssl.lib"}, {"name": "TBB_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for TBB."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/tbb"}, {"name": "Tracy_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for <PERSON>."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/Tracy"}, {"name": "VCPKG_APPLOCAL_DEPS", "properties": [{"name": "HELPSTRING", "value": "Automatically copy dependencies into the output directory for executables."}], "type": "BOOL", "value": "ON"}, {"name": "VCPKG_INSTALLED_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory which contains the installed libraries for each triplet"}], "type": "PATH", "value": "D:/vcpkg/installed"}, {"name": "VCPKG_MANIFEST_DIR", "properties": [{"name": "HELPSTRING", "value": "The path to the vcpkg manifest directory."}], "type": "PATH", "value": ""}, {"name": "VCPKG_MANIFEST_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Install the dependencies listed in your manifest:\n    If this is off, you will have to manually install your dependencies.\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md for more info.\n"}], "type": "INTERNAL", "value": "OFF"}, {"name": "VCPKG_MANIFEST_MODE", "properties": [{"name": "HELPSTRING", "value": "Use manifest mode, as opposed to classic mode."}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_PREFER_SYSTEM_LIBS", "properties": [{"name": "HELPSTRING", "value": "Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are found after toolchain/system libraries/packages."}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_SETUP_CMAKE_PROGRAM_PATH", "properties": [{"name": "HELPSTRING", "value": "Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths"}], "type": "BOOL", "value": "ON"}, {"name": "VCPKG_TARGET_TRIPLET", "properties": [{"name": "HELPSTRING", "value": "Vcpkg target triplet (ex. x86-windows)"}], "type": "STRING", "value": "x64-windows"}, {"name": "VCPKG_TRACE_FIND_PACKAGE", "properties": [{"name": "HELPSTRING", "value": "Trace calls to find_package()"}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_VERBOSE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enables messages from the VCPKG toolchain for debugging purposes."}], "type": "BOOL", "value": "OFF"}, {"name": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/VulkanSDK/1.4.309.0/Bin/glslangValidator.exe"}, {"name": "Vulkan_GLSLC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/tools/shaderc/glslc.exe"}, {"name": "Vulkan_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/VulkanSDK/1.4.309.0/Include"}, {"name": "Vulkan_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/VulkanSDK/1.4.309.0/Lib/vulkan-1.lib"}, {"name": "X_VCPKG_APPLOCAL_DEPS_INSTALL", "properties": [{"name": "HELPSTRING", "value": "(experimental) Automatically copy dependencies into the install target directory for executables. Requires CMake 3.14."}], "type": "BOOL", "value": "OFF"}, {"name": "X_VCPKG_APPLOCAL_DEPS_SERIALIZED", "properties": [{"name": "HELPSTRING", "value": "(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force serialization."}], "type": "BOOL", "value": "OFF"}, {"name": "ZLIB_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/include"}, {"name": "ZLIB_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/zlibd.lib"}, {"name": "ZLIB_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/lib/zlib.lib"}, {"name": "Z_VCPKG_BUILTIN_POWERSHELL_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe"}, {"name": "Z_VCPKG_CHECK_MANIFEST_MODE", "properties": [{"name": "HELPSTRING", "value": "Making sure VCPKG_MANIFEST_MODE doesn't change"}], "type": "INTERNAL", "value": "OFF"}, {"name": "Z_VCPKG_POWERSHELL_PATH", "properties": [{"name": "HELPSTRING", "value": "The path to the PowerShell implementation to use."}], "type": "INTERNAL", "value": "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe"}, {"name": "Z_VCPKG_PWSH_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Z_VCPKG_PWSH_PATH-NOTFOUND"}, {"name": "Z_VCPKG_ROOT_DIR", "properties": [{"name": "HELPSTRING", "value": "Vcpkg root directory"}], "type": "INTERNAL", "value": "D:/vcpkg"}, {"name": "Zycore_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Zycore."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/zycore"}, {"name": "_VCPKG_INSTALLED_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory which contains the installed libraries for each triplet"}], "type": "PATH", "value": "D:/vcpkg/installed"}, {"name": "__pkg_config_arguments_FFMPEG_PKGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;libavcodec;libavdevice;libavfilter;libavformat;libavutil;libswresample;libswscale"}, {"name": "__pkg_config_checked_FFMPEG_PKGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "capstone_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for capstone."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/capstone"}, {"name": "elfio_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for elfio."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/elfio"}, {"name": "fmt_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for fmt."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/fmt"}, {"name": "freetype_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for freetype."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/freetype"}, {"name": "glslang_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for glslang."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/glslang"}, {"name": "imgui_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for imgui."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/imgui"}, {"name": "libzip_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for libzip."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/libzip"}, {"name": "n<PERSON>hmann_json_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for nlohmann_json."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/nlo<PERSON>_json"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_avcodec", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avcodec.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_avdevice", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avdevice.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_avfilter", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avfilter.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_avformat", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avformat.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_avutil", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/avutil.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_bcrypt", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_bcrypt-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_gdi32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_gdi32-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_ole32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_ole32-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_oleaut32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_oleaut32-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_shlwapi", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_shlwapi-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_strmiids", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_strmiids-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_swresample", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/swresample.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_swscale", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "D:/vcpkg/installed/x64-windows/debug/lib/swscale.lib"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_user32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_user32-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_uuid", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_uuid-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_vfw32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_vfw32-NOTFOUND"}, {"name": "pkgcfg_lib_FFMPEG_PKGS_ws2_32", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "pkgcfg_lib_FFMPEG_PKGS_ws2_32-NOTFOUND"}, {"name": "plutosvg_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for plutosvg."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/plutosvg"}, {"name": "plutovg_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for plutovg."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/plutovg"}, {"name": "portaudio_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for portaudio."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/portaudio"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "D:/vcpkg/installed/x64-windows/debug/lib"}, {"name": "pugixml_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for pugixml."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/pugixml"}, {"name": "spdlog_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for spdlog."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/spdlog"}, {"name": "toml11_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for toml11."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/toml11"}, {"name": "tsl-robin-map_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tsl-robin-map."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map"}, {"name": "unofficial-spirv-reflect_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for unofficial-spirv-reflect."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect"}, {"name": "xbyak_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for xbyak."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/xbyak"}, {"name": "xxHash_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for xxHash."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/xxhash"}, {"name": "zlib-ng_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for zlib-ng."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/zlib-ng"}, {"name": "zydis_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for zydis."}], "type": "PATH", "value": "D:/vcpkg/installed/x64-windows/share/zydis"}], "kind": "cache", "version": {"major": 2, "minor": 0}}