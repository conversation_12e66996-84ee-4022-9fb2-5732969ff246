<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_WIN32_ATTACH_DETACH_NP(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: medium">pthread_win32_process_attach_np,
pthread_win32_process_detach_np, pthread_win32_thread_attach_np,
pthread_win32_thread_detach_np – exposed versions of the
PThreads4W DLL dllMain() switch functionality for use when
statically linking the library.</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>BOOL pthread_win32_process_attach_np (void);</B></P>
<P><B>BOOL pthread_win32_process_detach_np (void);</B></P>
<P><B>BOOL pthread_win32_thread_attach_np (void);</B></P>
<P><B>BOOL pthread_win32_thread_detach_np (void);</B></P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>These functions contain the code normally run via <b>dllMain</b>
when the library is used as a dll but which need to be called
explicitly by an application when the library is statically linked. As of version 2.9.0,  the  static library built using either MSC or GCC includes RT hooks which will call the pthread_win32_process_*_np routines automatically on  start/exit of the application.</P>
<P>You will need to call <B>pthread_win32_process_attach_np</B>
before you can call any pthread routines when statically linking. You
should call <B>pthread_win32_process_detach_np</B> before exiting
your application to clean up.</P>
<P><B>pthread_win32_thread_attach_np</B> is currently a no-op, but
<B>pthread_win32_thread_detach_np</B> is needed to clean up the
implicit  pthread handle that is allocated to a Win32 thread if it
calls certain pthreads routines. Call this routine when the Win32
thread exits.</P>
<P>These functions invariably return TRUE except for
<B>pthread_win32_process_attach_np</B> which will return FALSE if
PThreads4W initialisation fails.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None.</P>
<H2><A HREF="#toc4" NAME="sect4"><FONT COLOR="#000080">Return Value</FONT></A></H2>
<P>These routines return TRUE (non-zero) on success, or FALSE (0) if
they fail.</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>None.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>
