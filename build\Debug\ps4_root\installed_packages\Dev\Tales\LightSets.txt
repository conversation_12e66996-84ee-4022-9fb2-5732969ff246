//Light Sets

Table = LightSet:Def_Ball
	0.35, 0.35, 0.35, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.31, 0.31, 0.31, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.31, 0.31, 0.31, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.31, 0.31, 0.31, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	9.50, 9.50, 9.52, 8.00	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.73, -0.49, 0.49, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.73, -0.49, 0.49, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.00, -0.56, -0.83, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-295.63, -1902.82, 2170.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Ball

Table = LightSet:Def_Floor
	0.55, 0.55, 0.55, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.50, 1.50, 1.50, 16.19	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Floor

Table = LightSet:Def_Plastic
	0.52, 0.52, 0.50, 0.87	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.61, 0.38, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.67, 0.21, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.31, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	7.03, 3.00, 0.25, 8.69	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.66, -0.64, 0.37, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.56, -0.75, 0.29, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.00, -0.87, 0.46, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	5.43, 3.25, 4.05, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Plastic

Table = LightSet:Def_Plastic2
	0.70, 0.70, 0.70, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	1.48, 0.59, 0.00, 7.09	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.32, 0.36, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.21, 1.00, 1.12, 7.25	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.07, 0.50, -0.86, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.24, -0.96, 0.06, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.24, -0.90, 0.35, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-2.20, 28.04, 4.31, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Plastic2

Table = LightSet:Def_Wood
	0.30, 0.30, 0.30, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.63, 0.32, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.27, 0.40, 0.56, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	4.92, 3.00, 2.23, 18.60	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.59, -0.75, 0.21, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.66, -0.69, 0.25, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.35, -0.87, 0.32, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	3.98, 49.25, 5.45, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Wood

Table = LightSet:Def_Metal
	0.25, 0.25, 0.25, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.43, 0.46, 0.40, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.28, 0.28, 0.20, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.38, 0.38, 0.43, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.66, 0.83, 0.69, 2.50	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.31, -0.93, 0.18, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.91, -0.38, 0.12, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	9.30, -37.47, 9.89, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Metal

Table = LightSet:Def_Rubber
	0.40, 0.40, 0.40, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.00, 1.00, 0.20	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Rubber

Table = LightSet:Def_emissives
	0.00, 0.06, 0.00, 1.20	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.83, 1.00, 1.00, 45.99	  //	light1_red, light1_green, light1_blue, light1_alpha;
	1.00, 1.00, 1.00, 0.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	1.50, 1.50, 1.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.12, 1.29, 0.24, 0.00	  //	specular_red, specular_green, specular_blue, specular_shininess;
	0.00, -0.98, 0.15, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.93, 0.31, 0.18, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.54, 0.62, 0.54, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-0.39, -14.06, 6.65, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_emissives

Table = LightSet:Def_popbumpers
	0.35, 0.35, 0.35, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.31, 0.00, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.00, 1.00, 0.20	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.07, -0.50, 0.84, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	-0.10, -0.95, 0.28, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_popbumpers

Table = LightSet:Def_Metal2
	0.24, 0.26, 0.15, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 1.36, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.48, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	3.25, 3.25, 4.01, 7.67	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.69, -0.66, 0.24, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.56, -0.79, 0.20, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.00, -0.96, 0.26, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-0.56, -27.84, 5.51, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Metal2

Table = LightSet:Def_Rubberdull
	0.30, 0.30, 0.30, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.60, 0.60, 0.60, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.30, 0.30, 0.30, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.30, 0.30, 0.30, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.00, 1.00, 0.20	  //	specular_red, specular_green, specular_blue, specular_shininess;
	0.95, -0.21, 0.18, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	-0.83, -0.48, 0.25, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.01, -0.99, 0.10, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Rubberdull

Table = LightSet:Def_Plastic3
	0.40, 0.19, 0.12, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	1.00, 1.00, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.75, 3.25, 1.00, 0.00	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	2.00, 20.42, 7.25, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Plastic3

Table = LightSet:Def_Bulbs
	0.40, 0.55, 0.55, 1.29	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	1.63, 1.62, 1.79, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.84, 0.61, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.74, 0.60, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.37, 1.37, 1.79, 0.00	  //	specular_red, specular_green, specular_blue, specular_shininess;
	0.01, -0.94, 0.31, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.50, 0.83, 0.12, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.75, 0.61, 0.20, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-0.50, -14.52, -1.50, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Bulbs

Table = LightSet:Def_BumperBlues
	0.00, 0.00, 0.81, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	3.00, 3.00, 2.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	3.00, 2.23, 1.16, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	3.00, 2.25, 2.75, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.75, 3.25, 1.00, 4.69	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.03, -0.98, 0.15, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.69, 0.67, 0.12, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.75, 0.60, 0.21, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	2.00, 20.42, 7.25, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_BumperBlues

Table = LightSet:Def_Floorlights
	0.59, 0.60, 0.40, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	3.00, 2.75, 2.50, 16.19	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Floorlights

Table = LightSet:Def_Cabinet
	0.80, 0.80, 0.90, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.43, 0.46, 0.87, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.61, 0.83, 0.69, 5.59	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	9.30, -37.47, 9.89, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Cabinet

Table = LightSet:Def_Metaltrim
	0.46, 0.43, 0.39, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.43, 0.46, 0.87, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.61, 0.83, 0.69, 5.59	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.91, -0.37, -0.12, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.06, -0.90, 0.43, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	9.30, -37.47, 9.89, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Metaltrim

Table = LightSet:Def_Plasticpost
	0.61, 0.61, 0.63, 0.99	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.50, 0.87	  //	light1_red, light1_green, light1_blue, light1_alpha;
	1.47, 0.81, 0.00, 1.29	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.08, 0.25, 1.50	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.10, -0.96, 0.25, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.84, 0.50, 0.14, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.00, -0.87, 0.46, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-1.87, -12.86, 1.96, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Plasticpost

Table = LightSet:Def_Flippers
	0.10, 0.10, 0.10, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	1.11, 0.75, 0.00, 7.09	  //	light1_red, light1_green, light1_blue, light1_alpha;
	1.10, 1.22, 1.29, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.42, 0.81, 1.11, 17.84	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.07, 0.94, 0.31, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.03, -0.96, 0.25, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-2.25, 28.68, 11.32, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Flippers

Table = LightSet:Def_Floor_Harley
	0.80, 0.80, 0.80, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.20, 0.00, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.33, 0.75, 0.69, 9.39	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.50, 1.50, 1.50, 14.46	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.68, -0.67, 0.27, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.68, -0.68, 0.25, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.29, -0.91, 0.27, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Floor_Harley

Table = LightSet:Def_Cabinet_Harley
	0.55, 0.55, 0.55, 0.99	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.27, 0.04, 0.50, 0.87	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.17, 0.08, 0.34, 1.29	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.08, 0.25, 1.50	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.10, -0.96, 0.25, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.84, 0.50, 0.14, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.27, -0.96, 0.00, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-1.87, -12.86, 1.96, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Cabinet_Harley

Table = LightSet:Def_emissives_Harley
	0.18, 0.06, 0.00, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.83, 1.00, 1.00, 45.99	  //	light1_red, light1_green, light1_blue, light1_alpha;
	1.00, 1.00, 1.00, 0.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	1.50, 1.50, 1.50, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	3.00, 3.00, 3.00, 2.49	  //	specular_red, specular_green, specular_blue, specular_shininess;
	0.00, -0.98, 0.15, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.93, 0.31, 0.18, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	-0.54, 0.62, 0.54, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	1229.16, -1978.83, 790.94, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_emissives_Harley

Table = LightSet:Def_pop_Harley
	0.58, 0.63, 0.59, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.31, 0.00, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.50, 0.50, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.00, 1.00, 1.00, 0.20	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.07, -0.50, 0.84, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	-0.10, -0.95, 0.28, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.75, -0.36, -0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	0.56, 0.56, 0.56, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_pop_Harley

Table = LightSet:Def_Plastic_HRL
	0.42, 0.34, 0.28, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.48, 0.43, 0.39, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	0.62, 0.46, 0.80, 6.09	  //	specular_red, specular_green, specular_blue, specular_shininess;
	0.25, -0.90, 0.32, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	0.56, -0.75, 0.29, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.00, -0.87, 0.46, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	3927.29, -2446.89, 664.79, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_Plastic_HRL

Table = LightSet:Def_MTLtrim_HRL
	0.50, 0.49, 0.56, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.50, 0.50, 0.36, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.40, 0.74, 0.50, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	1.09, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.98, 3.75, 4.03, 15.69	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.75, 0.39, 0.48, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	-0.02, -0.84, 0.50, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.81, -0.31, 0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-3717.69, 2649.51, 3539.77, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_MTLtrim_HRL

Table = LightSet:Def_generic_env
	1.21, 1.29, 1.66, 1.00	  //	ambient_red, ambient_green, ambient_blue, ambient_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light1_red, light1_green, light1_blue, light1_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light2_red, light2_green, light2_blue, light2_alpha;
	0.00, 0.00, 0.00, 1.00	  //	light3_red, light3_green, light3_blue, light3_alpha;
	1.98, 3.75, 4.03, 15.69	  //	specular_red, specular_green, specular_blue, specular_shininess;
	-0.75, 0.39, 0.48, 1.00	  //	light1_x, light1_y, light1_z, light1_w;
	-0.02, -0.84, 0.50, 1.00	  //	light2_x, light2_y, light2_z, light2_w;
	0.81, -0.31, 0.48, 1.00	  //	light3_x, light3_y, light3_z, light3_w;
	-3717.69, 2649.51, 3539.77, 1.00	  //	specular_x, specular_y, specular_z, specular_w;	// position, not direction
TableEnd = LightSet:Def_generic_env

