#pragma once
#include "../common/lock_ordering.h"
#include "../cpu/register.h"
#include "../cpu/x86_64_cpu.h"
#include "../memory/ps4_mmu.h"
#include <functional>
#include <istream>
#include <mutex>
#include <ostream>
#include <queue>
#include <unordered_map>
#include <vector>

namespace x86_64 {
class X86_64CPU;

/**
 * @brief Represents the IDT register structure.
 */
#pragma pack(push, 1)
struct IDTRegister {
  uint16_t limit; ///< Size of the IDT minus 1
  uint64_t base;  ///< Base address of the IDT
};
#pragma pack(pop)

/**
 * @brief Interrupt gate types.
 */
constexpr uint8_t IDT_GATE_INTERRUPT = 0x8E; ///< Interrupt gate
constexpr uint8_t IDT_GATE_TRAP = 0x8F;      ///< Trap gate
constexpr uint8_t IDT_GATE_SYSCALL = 0xEE;   ///< System call gate

/**
 * @brief Structure for an IDT descriptor.
 */
#pragma pack(push, 1)
struct InterruptDescriptor {
  uint16_t offset_1;       ///< Lower 16 bits of handler address
  uint16_t selector;       ///< Segment selector
  uint8_t ist;             ///< Interrupt Stack Table index
  uint8_t type_attributes; ///< Gate type and attributes
  uint16_t offset_2;       ///< Middle 16 bits of handler address
  uint32_t offset_3;       ///< Upper 32 bits of handler address
  uint32_t reserved;       ///< Reserved
};
#pragma pack(pop)

/**
 * @brief Context for an interrupt or exception.
 */
struct InterruptContext {
  uint64_t vector;     ///< Interrupt vector
  uint64_t error_code; ///< Error code (if applicable)
  uint64_t rip;        ///< Instruction pointer
  uint64_t cs;         ///< Code segment
  uint64_t rflags;     ///< RFLAGS register
  uint64_t rsp;        ///< Stack pointer
  uint64_t ss;         ///< Stack segment
};

/**
 * @brief Manages x86_64 interrupts and exceptions with fiber-aware handling.
 */
class InterruptHandler {
public:
  using HandlerFunc = std::function<void(InterruptContext &)>;

  /**
   * @brief Constructs an InterruptHandler instance.
   * @param cpu Reference to the X86_64CPU instance.
   * @param memory Reference to the PS4MMU-based memory system.
   */
  explicit InterruptHandler(X86_64CPU &cpu, ps4::PS4MMU &memory);

  /**
   * @brief Constructs an InterruptHandler instance with preallocated memory.
   * @param cpu Reference to the X86_64CPU instance.
   * @param memory Reference to the PS4MMU-based memory system.
   * @param idtBase Preallocated IDT base address.
   * @param handlerBase Preallocated handler stubs base address.
   */
  explicit InterruptHandler(X86_64CPU &cpu, ps4::PS4MMU &memory,
                            uint64_t idtBase, uint64_t handlerBase);

  /**
   * @brief Destructs the InterruptHandler, ensuring cleanup.
   */
  ~InterruptHandler() noexcept;

  /**
   * @brief Initializes the InterruptHandler.
   * @details Allocates IDT memory, sets up descriptors, and registers handlers.
   */
  void Initialize();

  /**
   * @brief Loads the IDT into the CPU.
   * @return True on success, false on failure.
   */
  bool LoadIDT();

  /**
   * @brief Handles an interrupt or exception.
   * @param vector The interrupt vector.
   * @param errorCode The error code (if any).
   * @param isSoftwareInterrupt True if it's a software interrupt.
   */
  void HandleInterrupt(uint8_t vector, uint64_t errorCode = 0,
                       bool isSoftwareInterrupt = false);

  /**
   * @brief Registers a custom handler for an interrupt vector.
   * @param vector The interrupt vector.
   * @param handler The handler function.
   */
  void RegisterHandler(uint8_t vector, HandlerFunc handler);

  /**
   * @brief Sets an IDT descriptor for a vector.
   * @param vector The interrupt vector.
   * @param handlerAddress The handler's address.
   * @param selector The segment selector.
   * @param type_attributes The gate type and attributes.
   * @param ist The Interrupt Stack Table index.
   */
  void SetDescriptor(uint8_t vector, uint64_t handlerAddress, uint16_t selector,
                     uint8_t type_attributes, uint8_t ist = 0);

  /**
   * @brief Sets an IDT descriptor for a vector without acquiring locks.
   * @details This method is intended for use during initialization when
   *          thread safety is not required. It avoids lock ordering violations
   *          by not acquiring the InterruptMutex before calling memory operations.
   * @param vector The interrupt vector.
   * @param handlerAddress The handler's address.
   * @param selector The segment selector.
   * @param type_attributes The gate type and attributes.
   * @param ist The Interrupt Stack Table index.
   * @warning Only use during single-threaded initialization!
   */
  void SetDescriptorUnsafe(uint8_t vector, uint64_t handlerAddress, uint16_t selector,
                           uint8_t type_attributes, uint8_t ist = 0);

  /**
   * @brief Retrieves an IDT descriptor for a vector.
   * @param vector The interrupt vector.
   * @param desc The output descriptor.
   * @return True if the descriptor is valid, false otherwise.
   */
  bool GetDescriptor(uint8_t vector, InterruptDescriptor &desc);

  /**
   * @brief Saves the InterruptHandler state.
   * @param out The output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the InterruptHandler state.
   * @param in The input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Gets a custom handler for a vector (for external dispatcher).
   * @param vector The interrupt vector.
   * @return The handler function.
   */
  HandlerFunc GetHandler(uint8_t vector) const;

  /**
   * @brief Retrieves interrupt statistics.
   * @return Map of vector to interrupt count.
   */
  const std::unordered_map<uint8_t, uint64_t> &GetStats() const {
    return m_stats;
  }

  /**
   * @brief Retrieves the internal mutex for external try-lock.
   * @return Reference to the mutex.
   */
  std::mutex &GetMutex() { return m_mutex; }

  /**
   * @brief Attempts to lock the mutex.
   * @return True if locked, false otherwise.
   */
  bool try_lock() { return m_mutex.try_lock(); }

  /**
   * @brief Locks the mutex.
   */
  void lock() { m_mutex.lock(); }

  /**
   * @brief Unlocks the mutex.
   */
  void unlock() { m_mutex.unlock(); }

private:
  /**
   * @brief Default handler for unhandled exceptions.
   * @param context The interrupt context.
   */
  void DefaultExceptionHandler(InterruptContext &context);

  /**
   * @brief Handler for spurious interrupts.
   * @param context The interrupt context.
   */
  void SpuriousInterruptHandler(InterruptContext &context);

  /**
   * @brief Handler for page fault exceptions.
   * @param context The interrupt context.
   */
  void PageFaultHandler(InterruptContext &context);

  /**
   * @brief Handler for double fault exceptions.
   * @param context The interrupt context.
   */
  void DoubleFaultHandler(InterruptContext &context);

  void ProcessInterrupt(uint8_t vector, uint64_t errorCode,
                        bool isSoftwareInterrupt);

  /**
   * @brief Internal helper to set an IDT descriptor without acquiring mutex.
   * @param vector The interrupt vector.
   * @param handlerAddress The handler's address.
   * @param selector The segment selector.
   * @param type_attributes The gate type and attributes.
   * @param ist The Interrupt Stack Table index.
   */
  void SetDescriptorInternal(uint8_t vector, uint64_t handlerAddress,
                             uint16_t selector, uint8_t type_attributes,
                             uint8_t ist = 0);

  X86_64CPU &m_cpu;                ///< Reference to the CPU
  ps4::PS4MMU &m_memory;           ///< Reference to the memory system
  mutable std::mutex m_mutex;      ///< Thread safety mutex
  mutable std::mutex m_fiberMutex; ///< Mutex for fiber actions
  uint64_t m_idtBaseAddress;       ///< IDT base address
  uint64_t m_handlerBase;          ///< Handler stubs base address
  static constexpr size_t DEFAULT_IDT_ENTRY_COUNT =
      256; ///< Default number of IDT entries
  size_t m_idtEntryCount =
      DEFAULT_IDT_ENTRY_COUNT; ///< Current number of IDT entries
  size_t m_idtSize =
      m_idtEntryCount * sizeof(InterruptDescriptor); ///< IDT size
  IDTRegister m_idtDescriptor;                       ///< IDT descriptor
  std::vector<HandlerFunc> m_customHandlers;     ///< Custom interrupt handlers
  std::unordered_map<uint8_t, uint64_t> m_stats; ///< Interrupt statistics
  struct PendingInterrupt {
    uint8_t vector;
    uint64_t errorCode;
    bool isSoftwareInterrupt;
    uint8_t priority;
    bool operator<(const PendingInterrupt &other) const {
      return priority < other.priority;
    }
  };
  std::priority_queue<PendingInterrupt>
      m_pendingInterrupts; ///< Priority queue for pending interrupts
  std::queue<PendingInterrupt>
      m_deferredInterrupts; ///< Queue for deferred interrupts to prevent
                            ///< recursion
  struct FiberAction {
    uint64_t fiberId;
    bool suspend; // true for suspend, false for resume
  };
  std::vector<FiberAction> m_pendingFiberActions; ///< Deferred fiber actions
  uint64_t m_interruptLatencyUs = 0; ///< Cumulative interrupt latency (µs)
};
} // namespace x86_64