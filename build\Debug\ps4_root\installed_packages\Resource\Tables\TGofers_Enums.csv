RSID_TGOFERS_START, 3675,,,
 ,[Offset], Go<PERSON>_FLYER, 0,
 ,[Offset], <PERSON><PERSON>\PBTGofers, 1,
 ,[Offset], Gofers\InstructionsENG, 2,
 ,[Offset], Gofers\InstructionsFR, 3,
 ,[Offset], Go<PERSON>\InstructionsITAL, 4,
 ,[Offset], Go<PERSON>\InstructionsGERM, 5,
 ,[Offset], Go<PERSON>\InstructionsSPAN, 6,
 ,[Offset], Go<PERSON>\InstructionsPORT, 7,
 ,[Offset], Gofers\InstructionsDUTCH, 8,
 ,[Offset], tables\NGGofers_BG_scroll, 9,
RSID_TGOFERS_LIGHTS, 3676,,,
RSID_TGOFERS_CAMERAS, 3677,,,
RSID_TGOFERS_LAMP_TEXTURES, 3678,,,
 ,[Offset], L_11_off, 0,
 ,[Offset], L_11_on, 1,
 ,[Offset], L_12_off, 2,
 ,[Offset], L_12_on, 3,
 ,[Offset], L_13_off, 4,
 ,[Offset], L_13_on, 5,
 ,[Offset], L_14_off, 6,
 ,[Offset], L_14_on, 7,
 ,[Offset], L_15_off, 8,
 ,[Offset], L_15_on, 9,
 ,[Offset], L_16_off, 10,
 ,[Offset], L_16_on, 11,
 ,[Offset], L_17_off, 12,
 ,[Offset], L_17_on, 13,
 ,[Offset], L_18_off, 14,
 ,[Offset], L_18_on, 15,
 ,[Offset], L_21_off, 16,
 ,[Offset], L_21_on, 17,
 ,[Offset], L_22_off, 18,
 ,[Offset], L_22_on, 19,
 ,[Offset], L_23_off, 20,
 ,[Offset], L_23_on, 21,
 ,[Offset], L_24_off, 22,
 ,[Offset], L_24_on, 23,
 ,[Offset], L_25_off, 24,
 ,[Offset], L_25_on, 25,
 ,[Offset], L_26_off, 26,
 ,[Offset], L_26_on, 27,
 ,[Offset], L_27_off, 28,
 ,[Offset], L_27_on, 29,
 ,[Offset], L_28_off, 30,
 ,[Offset], L_28_on, 31,
 ,[Offset], L_31_off, 32,
 ,[Offset], L_31_on, 33,
 ,[Offset], L_32_off, 34,
 ,[Offset], L_32_on, 35,
 ,[Offset], L_33_off, 36,
 ,[Offset], L_33_on, 37,
 ,[Offset], L_34_off, 38,
 ,[Offset], L_34_on, 39,
 ,[Offset], L_35_off, 40,
 ,[Offset], L_35_on, 41,
 ,[Offset], L_36_off, 42,
 ,[Offset], L_36_on, 43,
 ,[Offset], L_37_off, 44,
 ,[Offset], L_37_on, 45,
 ,[Offset], L_38_off, 46,
 ,[Offset], L_38_on, 47,
 ,[Offset], L_41_off, 48,
 ,[Offset], L_41_on, 49,
 ,[Offset], L_42_off, 50,
 ,[Offset], L_42_on, 51,
 ,[Offset], L_43_off, 52,
 ,[Offset], L_43_on, 53,
 ,[Offset], L_44_off, 54,
 ,[Offset], L_44_on, 55,
 ,[Offset], L_45_off, 56,
 ,[Offset], L_45_on, 57,
 ,[Offset], L_46_off, 58,
 ,[Offset], L_46_on, 59,
 ,[Offset], L_47_off, 60,
 ,[Offset], L_47_on, 61,
 ,[Offset], L_48_off, 62,
 ,[Offset], L_48_on, 63,
 ,[Offset], L_51_off, 64,
 ,[Offset], L_51_on, 65,
 ,[Offset], L_52_off, 66,
 ,[Offset], L_52_on, 67,
 ,[Offset], L_53_off, 68,
 ,[Offset], L_53_on, 69,
 ,[Offset], L_54_off, 70,
 ,[Offset], L_54_on, 71,
 ,[Offset], L_55_off, 72,
 ,[Offset], L_55_on, 73,
 ,[Offset], L_56_off, 74,
 ,[Offset], L_56_on, 75,
 ,[Offset], L_57_off, 76,
 ,[Offset], L_57_on, 77,
 ,[Offset], L_58_off, 78,
 ,[Offset], L_58_on, 79,
 ,[Offset], L_61_off, 80,
 ,[Offset], L_61_on, 81,
 ,[Offset], L_62_off, 82,
 ,[Offset], L_62_on, 83,
 ,[Offset], L_63_off, 84,
 ,[Offset], L_63_on, 85,
 ,[Offset], L_64_off, 86,
 ,[Offset], L_64_on, 87,
 ,[Offset], L_65_off, 88,
 ,[Offset], L_65_on, 89,
 ,[Offset], L_66_off, 90,
 ,[Offset], L_66_on, 91,
 ,[Offset], L_67_off, 92,
 ,[Offset], L_67_on, 93,
 ,[Offset], L_68_off, 94,
 ,[Offset], L_68_on, 95,
 ,[Offset], L_71_off, 96,
 ,[Offset], L_71_on, 97,
 ,[Offset], L_72_off, 98,
 ,[Offset], L_72_on, 99,
 ,[Offset], L_73_off, 100,
 ,[Offset], L_73_on, 101,
 ,[Offset], L_74_off, 102,
 ,[Offset], L_74_on, 103,
 ,[Offset], L_75_off, 104,
 ,[Offset], L_75_on, 105,
 ,[Offset], L_76_off, 106,
 ,[Offset], L_76_on, 107,
 ,[Offset], L_77_off, 108,
 ,[Offset], L_77_on, 109,
 ,[Offset], L_78_off, 110,
 ,[Offset], L_78_on, 111,
 ,[Offset], L_81_off, 112,
 ,[Offset], L_81_on, 113,
 ,[Offset], L_82_off, 114,
 ,[Offset], L_82_on, 115,
 ,[Offset], L_83_off, 116,
 ,[Offset], L_83_on, 117,
 ,[Offset], L_84_off, 118,
 ,[Offset], L_84_on, 119,
 ,[Offset], L_85_off, 120,
 ,[Offset], L_85_on, 121,
 ,[Offset], L_86_off, 122,
 ,[Offset], L_86_on, 123,
 ,[Offset], L_87_off, 124,
 ,[Offset], L_87_on, 125,
 ,[Offset], L_55B_off, 126,
 ,[Offset], L_55B_on, 127,
 ,[Offset], L_71B_off, 128,
 ,[Offset], L_71B_on, 129,
 ,[Offset], F01_off, 130,
 ,[Offset], F01_on, 131,
 ,[Offset], F02_off, 132,
 ,[Offset], F02_on, 133,
 ,[Offset], F03_off, 134,
 ,[Offset], F03_on, 135,
 ,[Offset], F04_off, 136,
 ,[Offset], F04_on, 137,
 ,[Offset], F05_off, 138,
 ,[Offset], F05_on, 139,
 ,[Offset], F06_off, 140,
 ,[Offset], F06_on, 141,
 ,[Offset], F07_off, 142,
 ,[Offset], F07_on, 143,
 ,[Offset], F08_off, 144,
 ,[Offset], F08_on, 145,
 ,[Offset], F09_off, 146,
 ,[Offset], F09_on, 147,
 ,[Offset], F_17_off, 148,
 ,[Offset], F_17_on, 149,
 ,[Offset], F_19_off, 150,
 ,[Offset], F_19_on, 151,
 ,[Offset], F_20_off, 152,
 ,[Offset], F_20_on, 153,
 ,[Offset], F_25_On, 154,
 ,[Offset], F_26_on, 155,
RSID_TGOFERS_TEXTURES, 3679,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], flipper, 1,
 ,[Offset], BUD_DIFFUSE, 2,
 ,[Offset], BUZZ_DIFFUSE, 3,
 ,[Offset], GolfCart_Diffuse, 4,
 ,[Offset], NGG_green, 5,
 ,[Offset], NGG006, 6,
 ,[Offset], NGG007, 7,
 ,[Offset], NGG008, 8,
 ,[Offset], NGG010, 9,
 ,[Offset], NGG011, 10,
 ,[Offset], NGG012, 11,
 ,[Offset], NGG014, 12,
 ,[Offset], NGG_Intro_LunchBall, 13,
 ,[Offset], NGG_Intro_Kick, 14,
 ,[Offset], target_yellow, 15,
 ,[Offset], target2, 16,
 ,[Offset], target2_blue, 17,
 ,[Offset], target, 18,
 ,[Offset], target_orange, 19,
 ,[Offset], screw, 20,
 ,[Offset], screw alt, 21,
 ,[Offset], rubber, 22,
 ,[Offset], rubber_black, 23,
 ,[Offset], wire_black, 24,
 ,[Offset], wire_white, 25,
 ,[Offset], wire_red, 26,
 ,[Offset], midRamp, 27,
 ,[Offset], lights, 28,
 ,[Offset], ball, 29,
 ,[Offset], button, 30,
 ,[Offset], cabinet_wood, 31,
 ,[Offset], plunger, 32,
 ,[Offset], NGG_Playfield_Bottom, 33,
 ,[Offset], NGG_Playfield_Top, 34,
 ,[Offset], NGG_Wheel, 35,
 ,[Offset], NG_Hole_In_One_L, 36,
 ,[Offset], NG_Hole_In_One_R, 37,
 ,[Offset], NG_LowerTable_L, 38,
 ,[Offset], NG_LowerTable_R, 39,
 ,[Offset], CoinSlots, 40,
 ,[Offset], BrushedMetal, 41,
 ,[Offset], BrushedMetal01-, 42,
 ,[Offset], Instructions_Piece, 43,
 ,[Offset], Cabinet, 44,
 ,[Offset], lockbox, 45,
 ,[Offset], ball_specular_WW, 46,
 ,[Offset], NGG013, 47,
 ,[Offset], NGG009, 48,
 ,[Offset], NGG015, 49,
 ,[Offset], GopherRamp_R, 50,
 ,[Offset], GopherRamp_L, 51,
 ,[Offset], legs, 52,
 ,[Offset], metal_rims, 53,
 ,[Offset], plunger_box, 54,
 ,[Offset], scoreboard, 55,
 ,[Offset], Flag_YL, 56,
 ,[Offset], lightbulb, 57,
 ,[Offset], lightbulbglow, 58,
 ,[Offset], light_red, 59,
 ,[Offset], light_blue, 60,
 ,[Offset], light_white, 61,
 ,[Offset], popbumper_Red, 62,
 ,[Offset], popbumper_Blue, 63,
 ,[Offset], popbumper_white, 64,
 ,[Offset], bumpers, 65,
 ,[Offset], plastic_clear, 66,
 ,[Offset], post, 67,
 ,[Offset], spec, 68,
 ,[Offset], blue_ramps, 69,
 ,[Offset], blue_ramps02, 70,
 ,[Offset], ramp_light_holders, 71,
 ,[Offset], ramp_R, 72,
 ,[Offset], ramp_L, 73,
 ,[Offset], ball2, 74,
 ,[Offset], glass, 75,
 ,[Offset], start, 76,
 ,[Offset], flashing_sticker1, 78,
 ,[Offset], cart, 79,
 ,[Offset], L_gopher_ramp, 80,
 ,[Offset], metal_parts1, 81,
 ,[Offset], metal_walls, 82,
 ,[Offset], middle_ramp, 83,
 ,[Offset], other_ramp, 84,
 ,[Offset], r_gopher_ramp, 85,
 ,[Offset], clear_lights, 86,
 ,[Offset], BrushedMetal_5, 87,
 ,[Offset], clear_plastic, 88,
 ,[Offset], Shadow, 89,
RSID_TGOFERS_MODELS, 3680,,,
 ,[Offset], Flipper, 0,
 ,[Offset], Gofer_Left, 1,
 ,[Offset], Gofer_Right, 2,
 ,[Offset], OneWay_Gate, 3,
 ,[Offset], Plunger, 4,
 ,[Offset], Ramp_Left, 5,
 ,[Offset], Ramp_Middle, 6,
 ,[Offset], Ramp_Right, 7,
 ,[Offset], Slingshot_Left, 8,
 ,[Offset], Slingshot_Right, 9,
 ,[Offset], Spinner, 10,
 ,[Offset], Switch, 11,
 ,[Offset], Target_A, 12,
 ,[Offset], Target_B, 13,
 ,[Offset], Target_C, 14,
 ,[Offset], Target_G, 15,
 ,[Offset], Target_L, 16,
 ,[Offset], Target_Red, 17,
 ,[Offset], Wheel, 18,
 ,[Offset], Wire, 19,
 ,[Offset], Backglass, 20,
 ,[Offset], Bulbs, 21,
 ,[Offset], Cabinet, 22,
 ,[Offset], Cabinet_Metal, 23,
 ,[Offset], Clear_Plastic, 24,
 ,[Offset], Flashers, 25,
 ,[Offset], Golf_Cart, 26,
 ,[Offset], Metal_Pieces, 27,
 ,[Offset], Metal_Ramp_Parts, 28,
 ,[Offset], Metal_Walls, 29,
 ,[Offset], Plastic_Pieces, 30,
 ,[Offset], Plastic_Posts, 31,
 ,[Offset], Plastic_Ramps, 32,
 ,[Offset], Playfield, 33,
 ,[Offset], PopBumpers, 34,
 ,[Offset], Rubber_Pieces, 35,
 ,[Offset], Spot_Lights, 36,
 ,[Offset], Wires, 37,
 ,[Offset], Bumper_A, 38,
 ,[Offset], Light_Cutouts, 39,
 ,[Offset], Light_Cutouts_2, 40,
 ,[Offset], Shadow, 41,
RSID_TGOFERS_MODELS_LODS, 3681,,,
 ,[Offset], Flipper, 0,
 ,[Offset], Gofer_Left, 1,
 ,[Offset], Gofer_Right, 2,
 ,[Offset], OneWay_Gate, 3,
 ,[Offset], Plunger, 4,
 ,[Offset], Ramp_Left, 5,
 ,[Offset], Ramp_Middle, 6,
 ,[Offset], Ramp_Right, 7,
 ,[Offset], Slingshot_Left, 8,
 ,[Offset], Slingshot_Right, 9,
 ,[Offset], Spinner, 10,
 ,[Offset], Switch, 11,
 ,[Offset], Target_A, 12,
 ,[Offset], Target_B, 13,
 ,[Offset], Target_C, 14,
 ,[Offset], Target_G, 15,
 ,[Offset], Target_L, 16,
 ,[Offset], Target_Red, 17,
 ,[Offset], Wheel, 18,
 ,[Offset], Wire, 19,
 ,[Offset], Backglass, 20,
 ,[Offset], Bulbs, 21,
 ,[Offset], Cabinet, 22,
 ,[Offset], Cabinet_Metal, 23,
 ,[Offset], Clear_Plastic, 24,
 ,[Offset], Flashers, 25,
 ,[Offset], Golf_Cart, 26,
 ,[Offset], Metal_Pieces, 27,
 ,[Offset], Metal_Ramp_Parts, 28,
 ,[Offset], Metal_Walls, 29,
 ,[Offset], Plastic_Pieces, 30,
 ,[Offset], Plastic_Posts, 31,
 ,[Offset], Plastic_Ramps, 32,
 ,[Offset], Playfield, 33,
 ,[Offset], PopBumpers, 34,
 ,[Offset], Rubber_Pieces, 35,
 ,[Offset], Spot_Lights, 36,
 ,[Offset], Wires, 37,
 ,[Offset], Bumper_A, 38,
 ,[Offset], Light_Cutouts, 39,
 ,[Offset], Light_Cutouts_2, 40,
 ,[Offset], Shadow, 41,
RSID_TGOFERS_COLLISIONS, 3682,,,
 ,[Offset], Flipper_Lane_Left, 0,
 ,[Offset], Flipper_Lane_Right, 1,
 ,[Offset], Flipper_Left_Back, 2,
 ,[Offset], Flipper_Left_Front, 3,
 ,[Offset], Flipper_Right_Back, 4,
 ,[Offset], Flipper_Right_Front, 5,
 ,[Offset], Metal_Wall, 6,
 ,[Offset], Playfield, 7,
 ,[Offset], Playfield_Upper, 8,
 ,[Offset], Plunger_Moving, 9,
 ,[Offset], Plunger_Rest, 10,
 ,[Offset], Ramp_Left, 11,
 ,[Offset], Ramp_Left_Upper, 12,
 ,[Offset], Ramp_Middle, 13,
 ,[Offset], Ramp_Right, 14,
 ,[Offset], Ramp_Right_Moving, 15,
 ,[Offset], Rubber_A, 16,
 ,[Offset], Rubber_B, 17,
 ,[Offset], Rubber_C, 18,
 ,[Offset], Rubber_D, 19,
 ,[Offset], Slingshot_Left, 20,
 ,[Offset], Slingshot_Left_Front, 21,
 ,[Offset], Slingshot_Right, 22,
 ,[Offset], Slingshot_Right_Front, 23,
 ,[Offset], Eject_Trap, 24,
 ,[Offset], Eject_Trough, 25,
 ,[Offset], Left_Trap, 26,
 ,[Offset], Main_Trap, 27,
 ,[Offset], Main_Trough, 28,
 ,[Offset], Right_Trap, 29,
 ,[Offset], Ramp_Left_Moving, 31,
 ,[Offset], Ball_Drain, 32,
 ,[Offset], Bumper, 33,
 ,[Offset], OneWayGate_Back, 34,
 ,[Offset], OneWayGate_Front, 35,
 ,[Offset], Spinner, 36,
 ,[Offset], Target_A, 37,
 ,[Offset], Target_B, 38,
 ,[Offset], Target_C, 39,
 ,[Offset], Target_G, 40,
 ,[Offset], Target_L, 41,
 ,[Offset], Target_Red, 42,
 ,[Offset], Trapped_Ball, 43,
 ,[Offset], Gofer_Right, 44,
 ,[Offset], Gofer_Left, 45,
 ,[Offset], Left_Eddie_Back, 46,
 ,[Offset], Left_Eddie_Front, 47,
 ,[Offset], Right_Eddie_Back, 48,
 ,[Offset], Right_Eddie_Front, 49,
 ,[Offset], Wall_Upper, 50,
 ,[Offset], Wedge_Upper, 51,
 ,[Offset], Scoop, 52,
RSID_TGOFERS_PLACEMENT, 3683,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_TGOFERS_EMUROM, 3684,,,
 ,[Offset], ngg, 0,
 ,[Offset], ngg_13, 1,
 ,[Offset], ngg_13, 2,
RSID_TGOFERS_SOUNDS_START, 3685,,,
RSID_TGOFERS_EMU_SOUNDS, 3686,,,
 ,[Offset], S0001_LP, 0,
 ,[Offset], S0002_LP, 1,
 ,[Offset], S0003_LP, 2,
 ,[Offset], S0005_LP1, 3,
 ,[Offset], S0005_LP2, 4,
 ,[Offset], S0006_LP1, 5,
 ,[Offset], S0006_LP2, 6,
 ,[Offset], S0007_LP1, 7,
 ,[Offset], S0007_LP2, 8,
 ,[Offset], S0008_LP1, 9,
 ,[Offset], S0008_LP2, 10,
 ,[Offset], S0009_LP1, 11,
 ,[Offset], S0009_LP2, 12,
 ,[Offset], S000A_LP, 13,
 ,[Offset], S000D_C6, 14,
 ,[Offset], S000E_LP, 15,
 ,[Offset], S000F_LP, 16,
 ,[Offset], S0010_LP, 17,
 ,[Offset], S0012_LP, 18,
 ,[Offset], S0013_C3, 19,
 ,[Offset], S0014_LP1, 20,
 ,[Offset], S0014_LP2, 21,
 ,[Offset], S0017_C6, 22,
 ,[Offset], S0018_LP, 23,
 ,[Offset], S001B_C6, 24,
 ,[Offset], S001C_C2, 25,
 ,[Offset], S001D_C2, 26,
 ,[Offset], S001E_C2, 27,
 ,[Offset], S001F_C2, 28,
 ,[Offset], S0020_C2, 29,
 ,[Offset], S0021_C3, 30,
 ,[Offset], S0022_C6, 31,
 ,[Offset], S0023_C6, 32,
 ,[Offset], S0024_C6, 33,
 ,[Offset], S0025_C6, 34,
 ,[Offset], S0026_C6, 35,
 ,[Offset], S0027_LP1, 36,
 ,[Offset], S0027_LP2, 37,
 ,[Offset], S0028_C3, 38,
 ,[Offset], S0029_C3, 39,
 ,[Offset], S002A_LP, 40,
 ,[Offset], S002B_LP1, 41,
 ,[Offset], S002B_LP2, 42,
 ,[Offset], S002D_LP, 43,
 ,[Offset], S0064_C4, 44,
 ,[Offset], S0065_C6, 45,
 ,[Offset], S0068_C4, 46,
 ,[Offset], S0069_C4, 47,
 ,[Offset], S006A_C4, 48,
 ,[Offset], S006B_C4, 49,
 ,[Offset], S006C_C6, 50,
 ,[Offset], S006D_C4, 51,
 ,[Offset], S006F_C4, 52,
 ,[Offset], S0070_C4, 53,
 ,[Offset], S0072_C4, 54,
 ,[Offset], S0073_C4, 55,
 ,[Offset], S0074_C4, 56,
 ,[Offset], S0075_C4, 57,
 ,[Offset], S0076_C4, 58,
 ,[Offset], S0077_C4, 59,
 ,[Offset], S0078_C4, 60,
 ,[Offset], S0079_C4, 61,
 ,[Offset], S007A_LP, 62,
 ,[Offset], S007B_C4, 63,
 ,[Offset], S007E_C4, 64,
 ,[Offset], S007F_C4, 65,
 ,[Offset], S0080_C4, 66,
 ,[Offset], S0081_C4, 67,
 ,[Offset], S0082_C4, 68,
 ,[Offset], S0083_C4, 69,
 ,[Offset], S0087_C4, 70,
 ,[Offset], S0088_LP, 71,
 ,[Offset], S0089_C4, 72,
 ,[Offset], S008A_C4, 73,
 ,[Offset], S008B_C4, 74,
 ,[Offset], S008C_C4, 75,
 ,[Offset], S008D_C4, 76,
 ,[Offset], S008E_C4, 77,
 ,[Offset], S008F_C6, 78,
 ,[Offset], S0090_C4, 79,
 ,[Offset], S0091_C4, 80,
 ,[Offset], S0092_C4, 81,
 ,[Offset], S0093_C4, 82,
 ,[Offset], S0094_C4, 83,
 ,[Offset], S0095_C6, 84,
 ,[Offset], S0096_C4, 85,
 ,[Offset], S0097_C4, 86,
 ,[Offset], S009A_C4, 87,
 ,[Offset], S009B_C4, 88,
 ,[Offset], S009C_C4, 89,
 ,[Offset], S009D_C4, 90,
 ,[Offset], S00A1_C6, 91,
 ,[Offset], S00A2_C6, 92,
 ,[Offset], S00A3_C4, 93,
 ,[Offset], S00A6_C4, 94,
 ,[Offset], S00A8_C4, 95,
 ,[Offset], S00A9_C4, 96,
 ,[Offset], S00AA_C4, 97,
 ,[Offset], S00AB_C4, 98,
 ,[Offset], S00AC_C6, 99,
 ,[Offset], S00AD_C4, 100,
 ,[Offset], S00AE_C4, 101,
 ,[Offset], S00AF_C4, 102,
 ,[Offset], S00B1_C1, 103,
 ,[Offset], S00B2_C4, 104,
 ,[Offset], S00B3_C1, 105,
 ,[Offset], S00B4_C4, 106,
 ,[Offset], S00B5_C4, 107,
 ,[Offset], S00B6_C4, 108,
 ,[Offset], S00B8_C1, 109,
 ,[Offset], S00BA_C1, 110,
 ,[Offset], S00BD_C1, 111,
 ,[Offset], S00BE_C1, 112,
 ,[Offset], S00C0_C4, 113,
 ,[Offset], S00C1_C4, 114,
 ,[Offset], S00C2_C4, 115,
 ,[Offset], S00C3_C4, 116,
 ,[Offset], S00C4_C6, 117,
 ,[Offset], S00C5_C4, 118,
 ,[Offset], S00C6_C4, 119,
 ,[Offset], S00C8_C4, 120,
 ,[Offset], S00CB_C4, 121,
 ,[Offset], S00CC_C4, 122,
 ,[Offset], S00CD_LP, 123,
 ,[Offset], S00CE_C4, 124,
 ,[Offset], S00CF_C6, 125,
 ,[Offset], S00D0_C6, 126,
 ,[Offset], S00D1_C6, 127,
 ,[Offset], S00D2_C4, 128,
 ,[Offset], S00D3_C4, 129,
 ,[Offset], S00D4_C6, 130,
 ,[Offset], S00D5_C4, 131,
 ,[Offset], S00D6_C4, 132,
 ,[Offset], S00D7_C4, 133,
 ,[Offset], S00D8_C4, 134,
 ,[Offset], S00D9_C4, 135,
 ,[Offset], S00DA_C4, 136,
 ,[Offset], S00DB_C4, 137,
 ,[Offset], S00DE_C6, 138,
 ,[Offset], S00E0_C6, 139,
 ,[Offset], S00E1_C6, 140,
 ,[Offset], S00E4_C6, 141,
 ,[Offset], S00E6_C6, 142,
 ,[Offset], S00E9_C1, 143,
 ,[Offset], S00EA_C1, 144,
 ,[Offset], S012C_C5, 145,
 ,[Offset], S012D_C5, 146,
 ,[Offset], S012F_C5, 147,
 ,[Offset], S0130_C5, 148,
 ,[Offset], S0131_C5, 149,
 ,[Offset], S0132_C5, 150,
 ,[Offset], S0133_C5, 151,
 ,[Offset], S0134_C5, 152,
 ,[Offset], S0135_C5, 153,
 ,[Offset], S0136_C5, 154,
 ,[Offset], S0137_C5, 155,
 ,[Offset], S0138_C5, 156,
 ,[Offset], S0139_C5, 157,
 ,[Offset], S013A_C5, 158,
 ,[Offset], S013B_C5, 159,
 ,[Offset], S013C_C5, 160,
 ,[Offset], S013D_C5, 161,
 ,[Offset], S013E_C5, 162,
 ,[Offset], S013F_C5, 163,
 ,[Offset], S0140_C5, 164,
 ,[Offset], S0141_C5, 165,
 ,[Offset], S0142_C5, 166,
 ,[Offset], S0143_C5, 167,
 ,[Offset], S0144_C5, 168,
 ,[Offset], S0145_C5, 169,
 ,[Offset], S0146_C5, 170,
 ,[Offset], S0147_C5, 171,
 ,[Offset], S0148_C5, 172,
 ,[Offset], S0149_C5, 173,
 ,[Offset], S014A_C5, 174,
 ,[Offset], S014C_C5, 175,
 ,[Offset], S014D_C5, 176,
 ,[Offset], S014E_C5, 177,
 ,[Offset], S0150_C5, 178,
 ,[Offset], S0152_C5, 179,
 ,[Offset], S0153_C5, 180,
 ,[Offset], S0154_C5, 181,
 ,[Offset], S0155_C5, 182,
 ,[Offset], S0157_C5, 183,
 ,[Offset], S0158_C5, 184,
 ,[Offset], S0159_C5, 185,
 ,[Offset], S015A_C5, 186,
 ,[Offset], S015B_C5, 187,
 ,[Offset], S015C_C5, 188,
 ,[Offset], S015D_C5, 189,
 ,[Offset], S015E_C5, 190,
 ,[Offset], S015F_C5, 191,
 ,[Offset], S0160_C5, 192,
 ,[Offset], S0161_C5, 193,
 ,[Offset], S0162_C5, 194,
 ,[Offset], S0163_C5, 195,
 ,[Offset], S0164_C5, 196,
 ,[Offset], S0165_C5, 197,
 ,[Offset], S0166_C5, 198,
 ,[Offset], S0167_C5, 199,
 ,[Offset], S016F_C5, 200,
 ,[Offset], S0170_C5, 201,
 ,[Offset], S0171_C5, 202,
 ,[Offset], S0172_C5, 203,
 ,[Offset], S0173_C5, 204,
 ,[Offset], S0174_C5, 205,
 ,[Offset], S0175_C5, 206,
 ,[Offset], S0176_C5, 207,
 ,[Offset], S0177_C5, 208,
 ,[Offset], S0178_C5, 209,
 ,[Offset], S0179_C5, 210,
 ,[Offset], S017A_C5, 211,
 ,[Offset], S017B_C5, 212,
 ,[Offset], S017C_C5, 213,
 ,[Offset], S017D_C5, 214,
 ,[Offset], S017E_C5, 215,
 ,[Offset], S017F_C5, 216,
 ,[Offset], S0180_C5, 217,
 ,[Offset], S0181_C5, 218,
 ,[Offset], S0182_C5, 219,
 ,[Offset], S0183_C5, 220,
 ,[Offset], S0184_C5, 221,
 ,[Offset], S0185_C5, 222,
 ,[Offset], S0186_C5, 223,
 ,[Offset], S0187_C5, 224,
 ,[Offset], S0188_C5, 225,
 ,[Offset], S0189_C5, 226,
 ,[Offset], S018A_C5, 227,
 ,[Offset], S018B_C5, 228,
 ,[Offset], S018C_C5, 229,
 ,[Offset], S018D_C5, 230,
 ,[Offset], S018E_C5, 231,
 ,[Offset], S018F_C5, 232,
 ,[Offset], S0190_C5, 233,
 ,[Offset], S0191_C5, 234,
 ,[Offset], S0192_C5, 235,
 ,[Offset], S0193_C5, 236,
 ,[Offset], S0194_C5, 237,
 ,[Offset], S0195_C5, 238,
 ,[Offset], S0196_C5, 239,
 ,[Offset], S0197_C5, 240,
 ,[Offset], S0198_C5, 241,
 ,[Offset], S019E_C5, 242,
 ,[Offset], S019F_C5, 243,
 ,[Offset], S01A0_C5, 244,
 ,[Offset], S01A1_C5, 245,
 ,[Offset], S01A2_C5, 246,
 ,[Offset], S01A3_C5, 247,
 ,[Offset], S01A4_C5, 248,
 ,[Offset], S01A5_C5, 249,
 ,[Offset], S01A6_C5, 250,
 ,[Offset], S01A7_C5, 251,
 ,[Offset], S01A8_C5, 252,
 ,[Offset], S01A9_C5, 253,
 ,[Offset], S01AA_C5, 254,
 ,[Offset], S01AB_C5, 255,
 ,[Offset], S01AC_C5, 256,
 ,[Offset], S01AD_C5, 257,
 ,[Offset], S01AE_C5, 258,
 ,[Offset], S01AF_C5, 259,
 ,[Offset], S01B0_C5, 260,
 ,[Offset], S01B1_C5, 261,
 ,[Offset], S01B2_C5, 262,
 ,[Offset], S01B3_C5, 263,
 ,[Offset], S01B4_C5, 264,
 ,[Offset], S01B5_C5, 265,
 ,[Offset], S01B6_C5, 266,
 ,[Offset], S01B7_C5, 267,
 ,[Offset], S01B8_C5, 268,
 ,[Offset], S01B9_C5, 269,
 ,[Offset], S01BA_C5, 270,
 ,[Offset], S01BB_C5, 271,
 ,[Offset], S01BC_C5, 272,
 ,[Offset], S01BD_C5, 273,
 ,[Offset], S01BE_C5, 274,
 ,[Offset], S01BF_C5, 275,
 ,[Offset], S01C0_C5, 276,
 ,[Offset], S01C1_C5, 277,
 ,[Offset], S01C2_C5, 278,
 ,[Offset], S01C3_C5, 279,
 ,[Offset], S01C4_C5, 280,
 ,[Offset], S01C5_C5, 281,
 ,[Offset], S01C6_C5, 282,
 ,[Offset], S01C7_C5, 283,
 ,[Offset], S01C8_C5, 284,
 ,[Offset], S01C9_C5, 285,
 ,[Offset], S01CA_C5, 286,
 ,[Offset], S01CE_C5, 287,
 ,[Offset], S01CF_C5, 288,
 ,[Offset], S01D0_C5, 289,
 ,[Offset], S01D1_C5, 290,
 ,[Offset], S01D2_C5, 291,
 ,[Offset], S01D3_C5, 292,
 ,[Offset], S01D4_C5, 293,
 ,[Offset], S01D5_C5, 294,
 ,[Offset], S01D6_C5, 295,
 ,[Offset], S0258_C5, 296,
 ,[Offset], S0259_C5, 297,
 ,[Offset], S025A_C5, 298,
 ,[Offset], S025B_C5, 299,
 ,[Offset], S025C_C5, 300,
 ,[Offset], S025D_C5, 301,
 ,[Offset], S025E_C5, 302,
 ,[Offset], S025F_C5, 303,
 ,[Offset], S0260_C5, 304,
 ,[Offset], S0261_C5, 305,
 ,[Offset], S0263_C5, 306,
 ,[Offset], S0264_C5, 307,
 ,[Offset], S0265_C5, 308,
 ,[Offset], S0266_C5, 309,
 ,[Offset], S0267_C5, 310,
 ,[Offset], S0268_C5, 311,
 ,[Offset], S0269_C5, 312,
 ,[Offset], S026A_C5, 313,
 ,[Offset], S026B_C5, 314,
 ,[Offset], S026C_C5, 315,
 ,[Offset], S026D_C5, 316,
 ,[Offset], S026E_C5, 317,
 ,[Offset], S026F_C5, 318,
 ,[Offset], S0270_C5, 319,
 ,[Offset], S0271_C5, 320,
 ,[Offset], S0272_C5, 321,
 ,[Offset], S0273_C5, 322,
 ,[Offset], S0274_C5, 323,
 ,[Offset], S0275_C5, 324,
 ,[Offset], S0276_C5, 325,
 ,[Offset], S0278_C5, 326,
 ,[Offset], S027A_C5, 327,
 ,[Offset], S027B_C5, 328,
 ,[Offset], S027D_C1, 329,
 ,[Offset], S027E_C5, 330,
 ,[Offset], S027F_C5, 331,
 ,[Offset], S0280_C5, 332,
 ,[Offset], S0281_C5, 333,
 ,[Offset], S0282_C5, 334,
 ,[Offset], S0283_C5, 335,
 ,[Offset], S0284_C5, 336,
 ,[Offset], S0285_C5, 337,
 ,[Offset], S0286_C5, 338,
 ,[Offset], S0287_C5, 339,
 ,[Offset], S0288_C5, 340,
 ,[Offset], S0289_C5, 341,
 ,[Offset], S028A_C5, 342,
 ,[Offset], S028B_C5, 343,
 ,[Offset], S028C_C5, 344,
 ,[Offset], S028D_C5, 345,
 ,[Offset], S028E_C5, 346,
 ,[Offset], S028F_C5, 347,
 ,[Offset], S0290_C5, 348,
 ,[Offset], S0291_C5, 349,
 ,[Offset], S0292_C5, 350,
 ,[Offset], S0293_C5, 351,
 ,[Offset], S0294_C5, 352,
 ,[Offset], S0295_C5, 353,
 ,[Offset], S0296_C5, 354,
 ,[Offset], S0297_C5, 355,
 ,[Offset], S0298_C5, 356,
 ,[Offset], S0299_C5, 357,
 ,[Offset], S029A_C5, 358,
 ,[Offset], S029B_C5, 359,
 ,[Offset], S029C_C5, 360,
 ,[Offset], S029D_C5, 361,
 ,[Offset], S029E_C5, 362,
 ,[Offset], S029F_C5, 363,
 ,[Offset], S02A0_C5, 364,
 ,[Offset], S02A1_C5, 365,
 ,[Offset], S02A2_C5, 366,
 ,[Offset], S02A3_C5, 367,
 ,[Offset], S02A4_C5, 368,
 ,[Offset], S02A5_C5, 369,
 ,[Offset], S02A6_C5, 370,
 ,[Offset], S02A7_C5, 371,
 ,[Offset], S02A8_C5, 372,
 ,[Offset], S02A9_C5, 373,
 ,[Offset], S02AA_C5, 374,
 ,[Offset], S02AB_C5, 375,
 ,[Offset], S02AC_C5, 376,
 ,[Offset], S02AD_C5, 377,
 ,[Offset], S02AE_C5, 378,
 ,[Offset], S02AF_C5, 379,
 ,[Offset], S02B0_C5, 380,
 ,[Offset], S02B1_C5, 381,
 ,[Offset], S02B2_C5, 382,
 ,[Offset], S02B3_C5, 383,
 ,[Offset], S02B4_C5, 384,
 ,[Offset], S02B5_C5, 385,
 ,[Offset], S02B6_C5, 386,
 ,[Offset], S02B7_C5, 387,
 ,[Offset], S02B8_C5, 388,
 ,[Offset], S02B9_C5, 389,
 ,[Offset], S02BA_C5, 390,
 ,[Offset], S02BB_C5, 391,
 ,[Offset], S02BC_C5, 392,
 ,[Offset], S02BD_C5, 393,
 ,[Offset], S02BE_C5, 394,
 ,[Offset], S02BF_C5, 395,
 ,[Offset], S02C0_C5, 396,
 ,[Offset], S02C1_C5, 397,
 ,[Offset], S02C2_C5, 398,
 ,[Offset], S02C3_C5, 399,
 ,[Offset], S02C4_C5, 400,
 ,[Offset], S02C5_C5, 401,
 ,[Offset], S03D4_C5, 402,
 ,[Offset], S03D5_C5, 403,
 ,[Offset], S03D6_C5, 404,
 ,[Offset], S03D7_C5, 405,
 ,[Offset], S03D8_C5, 406,
 ,[Offset], S03D9_C5, 407,
 ,[Offset], S03DA_C5, 408,
 ,[Offset], S03DB_C5, 409,
 ,[Offset], S03DC_C5, 410,
 ,[Offset], S03DD_C5, 411,
 ,[Offset], S03DE_C2, 412,
 ,[Offset], S07D0_C5, 413,
RSID_TGOFERS_MECH_SOUNDS, 3687,,,
 ,[Offset], ball_lock_down, 0,
 ,[Offset], gof_gofer_down, 1,
 ,[Offset], gof_gofer_up, 2,
RSID_TGOFERS_SOUNDS_END, 3688,,,
RSID_TGOFERS_SAMPLES, 3689,,,
RSID_TGOFERS_END, 3690,,,
