<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_MUTEX_INIT(3) manual page</TITLE>
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "AR PL UMing CN" }
		H4.ctl { font-family: "Lohit Devanagari" }
		H2.cjk { font-family: "AR PL UMing CN" }
		H2.ctl { font-family: "Lohit Devanagari" }
		PRE.cjk { font-family: "AR PL UMing CN", monospace }
		PRE.ctl { font-family: "Lohit Devanagari", monospace }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows â€“ REFERENCE -
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_mutex_init, pthread_mutex_lock, pthread_mutex_trylock,
pthread_mutex_timedlock, pthread_mutex_unlock,
pthread_mutex_consistent, pthread_mutex_destroy - operations on
mutexes 
</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>#include &lt;time.h&gt;</B></P>
<P><B>pthread_mutex_t </B><I>fastmutex</I> <B>=
PTHREAD_MUTEX_INITIALIZER;</B> 
</P>
<P><B>pthread_mutex_t </B><I>recmutex</I> <B>=
PTHREAD_RECURSIVE_MUTEX_INITIALIZER;</B> 
</P>
<P><B>pthread_mutex_t </B><I>errchkmutex</I> <B>=
PTHREAD_ERRORCHECK_MUTEX_INITIALIZER;</B> 
</P>
<P><B>pthread_mutex_t </B><I>recmutex</I> <B>=
PTHREAD_RECURSIVE_MUTEX_INITIALIZER_NP;</B> 
</P>
<P><B>pthread_mutex_t </B><I>errchkmutex</I> <B>=
PTHREAD_ERRORCHECK_MUTEX_INITIALIZER_NP;</B> 
</P>
<P><B>int pthread_mutex_init(pthread_mutex_t *</B><I>mutex</I><B>,
const pthread_mutexattr_t *</B><I>mutexattr</I><B>);</B> 
</P>
<P><B>int pthread_mutex_lock(pthread_mutex_t *</B><I>mutex</I><B>);</B>
</P>
<P><B>int pthread_mutex_trylock(pthread_mutex_t *</B><I>mutex</I><B>);</B>
</P>
<P><B>int pthread_mutex_timedlock(pthread_mutex_t *</B><I>mutex,
</I><B>const struct timespec *</B><I>abs_timeout</I><B>);</B> 
</P>
<P><B>int pthread_mutex_unlock(pthread_mutex_t *</B><I>mutex</I><B>);</B>
</P>
<P><B>int pthread_mutex_consistent(pthread_mutex_t *</B><I>mutex</I><B>);</B>
</P>
<P><B>int pthread_mutex_destroy(pthread_mutex_t *</B><I>mutex</I><B>);</B>
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>A mutex is a MUTual EXclusion device, and is useful for protecting
shared data structures from concurrent modifications, and
implementing critical sections and monitors. 
</P>
<P>A mutex has two possible states: unlocked (not owned by any
thread), and locked (owned by one thread). A mutex can never be owned
by two different threads simultaneously. A thread attempting to lock
a mutex that is already locked by another thread is suspended until
the owning thread unlocks the mutex first. 
</P>
<P><B>pthread_mutex_init</B> initializes the mutex object pointed to
by <I>mutex</I> according to the mutex attributes specified in
<I>mutexattr</I>. If <I>mutexattr</I> is <B>NULL</B>, default
attributes are used instead. 
</P>
<P>The type of a mutex determines whether it can be locked again by a
thread that already owns it. The default type is â€œnormalâ€�. See
<A HREF="pthread_mutexattr_init.html"><B>pthread_mutexattr_init</B>(3)</A>
for more information on mutex attributes. 
</P>
<P>Variables of type <B>pthread_mutex_t</B> can also be initialized
statically, using the constants <B>PTHREAD_MUTEX_INITIALIZER</B> (for
normal â€œfastâ€� mutexes), <B>PTHREAD_RECURSIVE_MUTEX_INITIALIZER</B>
(for recursive mutexes), and <B>PTHREAD_ERRORCHECK_MUTEX_INITIALIZER</B>
(for error checking mutexes). <SPAN STYLE="font-weight: normal">In
the </SPAN><B>PThreads4W</B> <SPAN STYLE="font-weight: normal">implementation,
an application should still call </SPAN><B>pthread_mutex_destroy</B>
<SPAN STYLE="font-weight: normal">at some point to ensure that any
resources consumed by the mutex are released.</SPAN></P>
<P><SPAN STYLE="font-weight: normal">Any mutex type can be
initialized as a </SPAN><B>robust mutex</B><SPAN STYLE="font-weight: normal">.
See </SPAN><A HREF="pthread_mutexattr_init.html"><B>pthread_mutexattr_init</B><SPAN STYLE="font-weight: normal">(3)</SPAN></A>
<SPAN STYLE="font-weight: normal">for more information as well as the
section </SPAN><I><U><SPAN STYLE="font-weight: normal">Robust Mutexes</SPAN></U></I>
<SPAN STYLE="font-weight: normal">below.</SPAN></P>
<P><B>pthread_mutex_lock</B> locks the given mutex. If the mutex is
currently unlocked, it becomes locked and owned by the calling
thread, and <B>pthread_mutex_lock</B> returns immediately. If the
mutex is already locked by another thread, <B>pthread_mutex_lock</B>
suspends the calling thread until the mutex is unlocked.</P>
<P>If the mutex is already locked by the calling thread, the behavior
of <B>pthread_mutex_lock</B> depends on the type of the mutex. If the
mutex is of the â€œnormalâ€� type, the calling thread is suspended
until the mutex is unlocked, thus effectively causing the calling
thread to deadlock. If the mutex is of the â€˜â€˜error checkingâ€™â€™
type, <B>pthread_mutex_lock</B> returns immediately with the error
code <B>EDEADLK</B>. If the mutex is of the â€˜â€˜recursiveâ€™â€™
type, <B>pthread_mutex_lock</B> succeeds and returns immediately,
recording the number of times the calling thread has locked the
mutex. An equal number of <B>pthread_mutex_unlock</B> operations must
be performed before the mutex returns to the unlocked state. 
</P>
<P><B>pthread_mutex_trylock</B> behaves identically to
<B>pthread_mutex_lock</B>, except that it does not block the calling
thread if the mutex is already locked by another thread (or by the
calling thread in the case of a â€œnormalâ€� or â€œ<SPAN STYLE="font-style: normal">errorcheck</SPAN>â€�
mutex). Instead, <B>pthread_mutex_trylock</B> returns immediately
with the error code <B>EBUSY</B>. 
</P>
<P><B>pthread_mutex_timedlock</B> behaves identically to
<B>pthread_mutex_lock</B>, except that if it cannot acquire the lock
before the <I>abs_timeout</I> time, the call returns with the error
code <B>ETIMEDOUT</B>. If the mutex can be locked immediately it is,
and the <B>abs_timeout</B> parameter is ignored.</P>
<P><B>pthread_mutex_consistent</B> may only be called for
<B>PTHREAD_MUTEX_ROBUST</B> mutexes. It simply marks the mutex as
consistent. See <I><U>Robust Mutexes</U></I> below.</P>
<P><B>pthread_mutex_unlock</B> unlocks the given mutex. The mutex is
assumed to be locked and owned by the calling thread on entrance to
<B>pthread_mutex_unlock</B>. If the mutex is of the â€œnormalâ€�
type, <B>pthread_mutex_unlock</B> always returns it to the unlocked
state. If it is of the â€˜â€˜recursiveâ€™â€™ type, it decrements the
locking count of the mutex (number of <B>pthread_mutex_lock</B>
operations performed on it by the calling thread), and only when this
count reaches zero is the mutex actually unlocked. In <B>PThreads4W</B>,
non-robust normal or default mutex types do not check the owner of
the mutex. For all types of robust mutexes the owner is checked and
an error code is returned if the calling thread does not own the
mutex.</P>
<P>On â€˜â€˜error checkingâ€™â€™ mutexes, <B>pthread_mutex_unlock</B>
actually checks at run-time that the mutex is locked on entrance, and
that it was locked by the same thread that is now calling
<B>pthread_mutex_unlock</B>. If these conditions are not met, an
error code is returned and the mutex remains unchanged. â€˜â€˜Normalâ€™â€™
[non-robust] mutexes perform no such checks, thus allowing a locked
mutex to be unlocked by a thread other than its owner. This is
non-portable behavior and is not meant to be used as a feature.</P>
<P><B>pthread_mutex_destroy</B> destroys a mutex object, freeing the
resources it might hold. The mutex must be unlocked on entrance.</P>
<H2 CLASS="western"><A HREF="#toc10" NAME="sect10">Robust Mutexes</A></H2>
<P>If the mutex is <B>PTHREAD_MUTEX_ROBUST</B> and the owning thread
terminates without unlocking the mutex the implementation will wake
one waiting thread, if any. The next thread to acquire the mutex will
receive the error code <B>EOWNERDEAD</B><SPAN STYLE="font-weight: normal">,
in which case that thread should if possible ensure that the state
protected by the mutex is consistent and then call
</SPAN><B>pthread_mutex_consistent</B> <SPAN STYLE="font-weight: normal">before
unlocking. The mutex may then be used normally from then on.</SPAN></P>
<P><SPAN STYLE="font-weight: normal">If the thread cannot recover the
state then it must call </SPAN><B>pthread_mutex_unlock</B><SPAN STYLE="font-weight: normal">
without calling </SPAN><B>pthread_mutex_consistent</B><SPAN STYLE="font-weight: normal">.
This will mark the mutex as unusable and wake all currently waiting
threads with the return code </SPAN><B>ENOTRECOVERABLE</B><SPAN STYLE="font-weight: normal">.
The error indicates that the mutex is no longer usable and any
threads that receive this error code from any lock operation have not
acquired the mutex. The mutex can be made consistent by calling
</SPAN><B>pthread_mutex_destroy</B> <SPAN STYLE="font-weight: normal">to
uninitialize the mutex, and calling </SPAN><B>pthread_mutex_int</B>
<SPAN STYLE="font-weight: normal">to reinitialize the mutex. However,
the state that was protected by the mutex remains inconsistent and
some form of application recovery is required.</SPAN></P>
<P><SPAN STYLE="font-weight: normal">If a thread that receives the
</SPAN><B>EOWNERDEAD</B> <SPAN STYLE="font-weight: normal">error code
itself terminates without unlocking the mutex then this behaviour
repeats for the next acquiring thread.</SPAN></P>
<P><SPAN STYLE="font-weight: normal">Applications must ensure that
they check the return values from all calls targeting robust mutexes.</SPAN></P>
<P STYLE="font-weight: normal">Robust mutexes are slower because they
require some additional overhead, however they are not very much
slower than the non-robust recursive type.</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None of the mutex functions is a cancellation point, not even
<B>pthread_mutex_lock</B>, in spite of the fact that it can suspend a
thread for arbitrary durations. This way, the status of mutexes at
cancellation points is predictable, allowing cancellation handlers to
unlock precisely those mutexes that need to be unlocked before the
thread stops executing. Consequently, threads using deferred
cancellation should never hold a mutex for extended periods of time. 
</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Async-signal Safety</A></H2>
<P>The mutex functions are not async-signal safe. What this means is
that they should not be called from a signal handler. In particular,
calling <B>pthread_mutex_lock</B> or <B>pthread_mutex_unlock</B> from
a signal handler may deadlock the calling thread. 
</P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Return Value</A></H2>
<P><B>pthread_mutex_init</B> always returns 0. The other mutex
functions return 0 on success and a non-zero error code on error. 
</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">Errors</A></H2>
<P>The <B>pthread_mutex_lock</B> function returns the following error
code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in"><B>EINVAL</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex has not been properly initialized. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EDEADLK</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex is already locked by the calling thread (â€˜â€˜error
		checkingâ€™â€™ mutexes only). 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EOWNERDEAD</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is now locked by the calling thread after the
		previous owner terminated without unlocking it.</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>ENOTRECOVERABLE</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is not locked and is no longer usable after the
		previous owner unlocked it without calling
		pthread_mutex_consistent.</DD></DL>
	<DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
	The <B>pthread_mutex_trylock</B> function returns the following
	error codes on error: 
	</DD><DL>
		<DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EBUSY</B> 
		</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex could not be acquired because it was currently locked. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex has not been properly initialized. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EOWNERDEAD</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is now locked by the calling thread after the
		previous owner terminated without unlocking it.</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>ENOTRECOVERABLE</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is not locked and is no longer usable after the
		previous owner unlocked it without calling
		pthread_mutex_consistent.</DD></DL>
</DL>
<P>
The <B>pthread_mutex_timedlock</B> function returns the following
error codes on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in"><B>ETIMEDOUT</B>
				</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex could not be acquired before the <I>abs_timeout</I> time
		arrived. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex has not been properly initialized. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EOWNERDEAD</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is now locked by the calling thread after the
		previous owner terminated without unlocking it.</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>ENOTRECOVERABLE</B></DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the robust mutex is not locked and is no longer usable after the
		previous owner unlocked it without calling
		pthread_mutex_consistent.</DD></DL>
</DL>
<P>
The <B>pthread_mutex_unlock</B> function returns the following error
code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in"><B>EINVAL</B>
				</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex has not been properly initialized. 
		</DD><DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		<B>EPERM</B> 
		</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the calling thread does not own the mutex (â€˜â€˜error checkingâ€™â€™
		mutexes only). 
		</DD></DL>
</DL>
<P>
The <B>pthread_mutex_destroy</B> function returns the following error
code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 0.39in; margin-bottom: 0.2in"><B>EBUSY</B>
				</DT><DD STYLE="margin-right: 0.39in; margin-bottom: 0.2in">
		the mutex is currently locked. 
		</DD></DL>
</DL>
<H2 CLASS="western">
<A HREF="#toc7" NAME="sect7">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2 CLASS="western"><A HREF="#toc8" NAME="sect8">See Also</A></H2>
<P><A HREF="pthread_mutexattr_init.html"><B>pthread_mutexattr_init</B>(3)</A>
, <A HREF="pthread_mutexattr_init.html"><B>pthread_mutexattr_settype</B>(3)</A>
, <A HREF="pthread_cancel.html"><B>pthread_cancel</B>(3)</A> . 
</P>
<H2 CLASS="western"><A HREF="#toc9" NAME="sect9">Example</A></H2>
<P>A shared global variable <I>x</I> can be protected by a mutex as
follows: 
</P>
<PRE CLASS="western" STYLE="margin-left: 0.39in; margin-right: 0.39in">int x;
pthread_mutex_t mut = PTHREAD_MUTEX_INITIALIZER;</PRE><BLOCKQUOTE STYLE="margin-left: 0in; margin-right: 0in">
All accesses and modifications to <I>x</I> should be bracketed by
calls to <B>pthread_mutex_lock</B> and <B>pthread_mutex_unlock</B> as
follows: 
</BLOCKQUOTE>
<PRE CLASS="western" STYLE="margin-left: 0.41in; margin-right: 0.79in">pthread_mutex_lock(&amp;mut);
/* operate on x */
pthread_mutex_unlock(&amp;mut);</PRE>
<HR>
<BLOCKQUOTE STYLE="margin-right: 2.75in"><A NAME="toc"></A><B>Table
of Contents</B></BLOCKQUOTE>
<UL>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect0" NAME="toc0">Name</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect2" NAME="toc2">Description</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect10" NAME="toc10">Robust
	Mutexes</A></BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect4" NAME="toc4">Async-signal
	Safety</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect5" NAME="toc5">Return
	Value</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect6" NAME="toc6">Errors</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect7" NAME="toc7">Author</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in; margin-bottom: 0in"><A HREF="#sect8" NAME="toc8">See
	Also</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 2.75in"><A HREF="#sect9" NAME="toc9">Example</A>
		</BLOCKQUOTE>
</UL>
</BODY>
</HTML>