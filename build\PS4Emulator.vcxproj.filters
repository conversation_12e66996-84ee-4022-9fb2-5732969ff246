﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\sss\src\cache\cache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\common\lock_ordering.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\cpu\cpu_diagnostics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\cpu\instruction_decoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\cpu\thunk_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\cpu\x86_64_cpu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\cpu\x86_64_pipeline.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\emulator\adaptive_emulation_orchestrator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\emulator\apic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\emulator\interrupt_handler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\emulator\io_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\backends\imgui_impl_sdl2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\backends\imgui_impl_vulkan.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\game_browser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_freetype.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_stdlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\input_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\input_settings.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\gui\performance_overlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\jit\jit_diagnostics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\jit\x86_64_jit_compiler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\jit\x86_64_jit_helpers.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\loader\crypto_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\loader\elf_loader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\loader\key_store.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\loader\pkg_installer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\loader\self_decrypter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\memory_compressor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\memory_diagnostics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\memory_prefetcher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\physical_memory_allocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\ps4_mmu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\swap_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\memory\tlb.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\fiber_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\orbis_os.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_audio.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_controllers.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_emulator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_filesystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_gpu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_tsc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\trophy_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\zlib_wrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\syscall\syscall_handler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\video_core\command_processor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\video_core\gnm_shader_translator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\video_core\gnm_state.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\video_core\shader_emulator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\video_core\tile_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\sss\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_json.natvis" />
    <Natvis Include="D:\vcpkg\installed\x64-windows\share\tsl-robin-map.natvis" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{92AB6C6D-E8D0-3E5A-9E73-69DC0CD0C5C3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
