RSID_MEDIEVAL_START, 2025,,,
 ,[Offset], MADNESS_FLYER_1, 0,
 ,[Offset], MedievalMadness\PBTMedieval, 1,
 ,[Offset], MedievalMadness\InstructionsENG, 2,
 ,[Offset], MedievalMadness\InstructionsFR, 3,
 ,[Offset], MedievalMadness\InstructionsITAL, 4,
 ,[Offset], MedievalMadness\InstructionsGERM, 5,
 ,[Offset], MedievalMadness\InstructionsSPAN, 6,
 ,[Offset], MedievalMadness\InstructionsPORT, 7,
 ,[Offset], MedievalMadness\InstructionsDUTCH, 8,
 ,[Offset], tables\Medieval_BG, 9,
 ,[Offset], MedievalMadness\Pro_TipsENG, 10,
 ,[Offset], MedievalMadness\Pro_TipsFR, 11,
 ,[Offset], MedievalMadness\Pro_TipsITAL, 12,
 ,[Offset], MedievalMadness\Pro_TipsGERM, 13,
 ,[Offset], MedievalMadness\Pro_TipsSPAN, 14,
 ,[Offset], MedievalMadness\Pro_TipsENG, 15,
 ,[Offset], MedievalMadness\Pro_TipsENG, 16,
RSID_MEDIEVAL_LIGHTS, 2026,,,
RSID_MEDIEVAL_CAMERAS, 2027,,,
RSID_MEDIEVAL_LAMP_TEXTURES, 2028,,,
 ,[Offset], L01_off, 0,
 ,[Offset], L01_on, 1,
 ,[Offset], L02_off, 2,
 ,[Offset], L02_on, 3,
 ,[Offset], L03_off, 4,
 ,[Offset], L03_on, 5,
 ,[Offset], L04_off, 6,
 ,[Offset], L04_on, 7,
 ,[Offset], L05_off, 8,
 ,[Offset], L05_on, 9,
 ,[Offset], L06_off, 10,
 ,[Offset], L06_on, 11,
 ,[Offset], L07_off, 12,
 ,[Offset], L07_on, 13,
 ,[Offset], L08_off, 14,
 ,[Offset], L08_on, 15,
 ,[Offset], L09_off, 16,
 ,[Offset], L09_on, 17,
 ,[Offset], L10_off, 18,
 ,[Offset], L10_on, 19,
 ,[Offset], L11_off, 20,
 ,[Offset], L11_on, 21,
 ,[Offset], L12_off, 22,
 ,[Offset], L12_on, 23,
 ,[Offset], L13_off, 24,
 ,[Offset], L13_on, 25,
 ,[Offset], L14_off, 26,
 ,[Offset], L14_on, 27,
 ,[Offset], L15_off, 28,
 ,[Offset], L15_on, 29,
 ,[Offset], L16_off, 30,
 ,[Offset], L16_on, 31,
 ,[Offset], L17_off, 32,
 ,[Offset], L17_on, 33,
 ,[Offset], L18_off, 34,
 ,[Offset], L18_on, 35,
 ,[Offset], L19_off, 36,
 ,[Offset], L19_on, 37,
 ,[Offset], L20_off, 38,
 ,[Offset], L20_on, 39,
 ,[Offset], L21_off, 40,
 ,[Offset], L21_on, 41,
 ,[Offset], L22_off, 42,
 ,[Offset], L22_on, 43,
 ,[Offset], L23_off, 44,
 ,[Offset], L23_on, 45,
 ,[Offset], L24_off, 46,
 ,[Offset], L24_on, 47,
 ,[Offset], L25_off, 48,
 ,[Offset], L25_on, 49,
 ,[Offset], L26_off, 50,
 ,[Offset], L26_on, 51,
 ,[Offset], L27_off, 52,
 ,[Offset], L27_on, 53,
 ,[Offset], L28_off, 54,
 ,[Offset], L28_on, 55,
 ,[Offset], L29_off, 56,
 ,[Offset], L29_on, 57,
 ,[Offset], L30_off, 58,
 ,[Offset], L30_on, 59,
 ,[Offset], L31_off, 60,
 ,[Offset], L31_on, 61,
 ,[Offset], L32_off, 62,
 ,[Offset], L32_on, 63,
 ,[Offset], L33_off, 64,
 ,[Offset], L33_on, 65,
 ,[Offset], L34_off, 66,
 ,[Offset], L34_on, 67,
 ,[Offset], L35_off, 68,
 ,[Offset], L35_on, 69,
 ,[Offset], L36_off, 70,
 ,[Offset], L36_on, 71,
 ,[Offset], L37_off, 72,
 ,[Offset], L37_on, 73,
 ,[Offset], L38_off, 74,
 ,[Offset], L38_on, 75,
 ,[Offset], L39_off, 76,
 ,[Offset], L39_on, 77,
 ,[Offset], L40_off, 78,
 ,[Offset], L40_on, 79,
 ,[Offset], L41_off, 80,
 ,[Offset], L41_on, 81,
 ,[Offset], L42_off, 82,
 ,[Offset], L42_on, 83,
 ,[Offset], L43_off, 84,
 ,[Offset], L43_on, 85,
 ,[Offset], L44_off, 86,
 ,[Offset], L44_on, 87,
 ,[Offset], L45_off, 88,
 ,[Offset], L45_on, 89,
 ,[Offset], L46_off, 90,
 ,[Offset], L46_on, 91,
 ,[Offset], L47_off, 92,
 ,[Offset], L47_on, 93,
 ,[Offset], L48_off, 94,
 ,[Offset], L48_on, 95,
 ,[Offset], L49_off, 96,
 ,[Offset], L49_on, 97,
 ,[Offset], L50_off, 98,
 ,[Offset], L50_on, 99,
 ,[Offset], L51_off, 100,
 ,[Offset], L51_on, 101,
 ,[Offset], L52_off, 102,
 ,[Offset], L52_on, 103,
 ,[Offset], L53_off, 104,
 ,[Offset], L53_on, 105,
 ,[Offset], L54_off, 106,
 ,[Offset], L54_on, 107,
 ,[Offset], L55_off, 108,
 ,[Offset], L55_on, 109,
 ,[Offset], L56_off, 110,
 ,[Offset], L56_on, 111,
 ,[Offset], L57_off, 112,
 ,[Offset], L57_on, 113,
 ,[Offset], L58_off, 114,
 ,[Offset], L58_on, 115,
 ,[Offset], L59_off, 116,
 ,[Offset], L59_on, 117,
 ,[Offset], L60_off, 118,
 ,[Offset], L60_on, 119,
 ,[Offset], L61_off, 120,
 ,[Offset], L61_on, 121,
 ,[Offset], L62_off, 122,
 ,[Offset], L62_on, 123,
 ,[Offset], L63_off, 124,
 ,[Offset], L63_on, 125,
 ,[Offset], F64_off, 126,
 ,[Offset], F64_on, 127,
 ,[Offset], F65_off, 128,
 ,[Offset], F65_on, 129,
 ,[Offset], F66_off, 130,
 ,[Offset], F66_on, 131,
 ,[Offset], F67_off, 132,
 ,[Offset], F67_on, 133,
 ,[Offset], F68_off, 134,
 ,[Offset], F68_on, 135,
 ,[Offset], F69_off, 136,
 ,[Offset], F69_on, 137,
 ,[Offset], F70_off, 138,
 ,[Offset], F70_on, 139,
 ,[Offset], L71_off, 140,
 ,[Offset], L71_on, 141,
 ,[Offset], L72_off, 142,
 ,[Offset], L72_on, 143,
 ,[Offset], L73_off, 144,
 ,[Offset], L73_on, 145,
 ,[Offset], Dragon_Belly_off, 146,
 ,[Offset], Dragon_Belly_on, 147,
 ,[Offset], Dragon_Eyes_off, 148,
 ,[Offset], Dragon_Eyes_on, 149,
 ,[Offset], Dragon_Mouth_off, 150,
 ,[Offset], Dragon_Mouth_on, 151,
RSID_MEDIEVAL_TEXTURES, 2029,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], color wheel, 1,
 ,[Offset], flipper, 2,
 ,[Offset], Body_DIffuse, 3,
 ,[Offset], Wings_Diffuse, 4,
 ,[Offset], speaker, 5,
 ,[Offset], buttons_parts, 6,
 ,[Offset], metal_legs, 7,
 ,[Offset], metal_trim, 8,
 ,[Offset], CoinSlots, 9,
 ,[Offset], Backglass, 10,
 ,[Offset], Troll_Diffuse, 11,
 ,[Offset], troll_bridge, 12,
 ,[Offset], Plate_Red_01, 13,
 ,[Offset], Plate_Red_02, 14,
 ,[Offset], Plate_Red_03, 15,
 ,[Offset], Plate_Red_04, 16,
 ,[Offset], lights, 17,
 ,[Offset], Plate_Side_Bumper, 18,
 ,[Offset], Plate_Side_Bumper2, 19,
 ,[Offset], target2, 20,
 ,[Offset], tmp_gray, 21,
 ,[Offset], Wizard_P, 22,
 ,[Offset], SideP_long, 23,
 ,[Offset], Towers_DIFFUSE, 24,
 ,[Offset], Ramp_Stick_L, 25,
 ,[Offset], Ramp_Stick_R_down, 26,
 ,[Offset], Ramp_Stick_R_up, 27,
 ,[Offset], Main_DIFFUSE, 28,
 ,[Offset], SideP_Tri, 29,
 ,[Offset], SidePlate_Purple_L, 30,
 ,[Offset], SidePlate_Purple_R, 31,
 ,[Offset], SidePlate_Red2_L, 32,
 ,[Offset], SidePlate_Red_L, 33,
 ,[Offset], drawbridge, 34,
 ,[Offset], SideP_long, 35,
 ,[Offset], screw alt, 36,
 ,[Offset], rubber, 37,
 ,[Offset], rubber color, 38,
 ,[Offset], post, 39,
 ,[Offset], screw, 40,
 ,[Offset], wood_strip, 41,
 ,[Offset], plunger, 42,
 ,[Offset], table_trimmetal01, 43,
 ,[Offset], P1, 44,
 ,[Offset], color wheel, 45,
 ,[Offset], MM_floor_lower, 46,
 ,[Offset], MM_LowerTable_L, 47,
 ,[Offset], MM_LowerTable_R, 48,
 ,[Offset], MM_floor_upper, 49,
 ,[Offset], MM_BackPlate_S, 50,
 ,[Offset], MM_Table_All, 51,
 ,[Offset], copper, 52,
 ,[Offset], metal front, 53,
 ,[Offset], metal-parts01 copy, 54,
 ,[Offset], LaunchBall_Button, 55,
 ,[Offset], popbumper, 56,
 ,[Offset], post red really, 57,
 ,[Offset], jelly red, 58,
 ,[Offset], plastic_green, 59,
 ,[Offset], L_Piece_DIFFUSE, 60,
 ,[Offset], glass, 61,
 ,[Offset], lightbulb, 62,
 ,[Offset], plastic_clear01, 63,
 ,[Offset], plastic_clear02, 64,
 ,[Offset], post red, 65,
 ,[Offset], target3, 66,
 ,[Offset], Metal_Ramp, 67,
 ,[Offset], Extra_Metal_Parts, 68,
 ,[Offset], Metal_Parts, 69,
 ,[Offset], Generic_Metal, 70,
 ,[Offset], Inner_Cabinet, 71,
 ,[Offset], plastic_ramp, 72,
 ,[Offset], plastic_ramp_2, 73,
 ,[Offset], Warrior_Arrow_L, 74,
 ,[Offset], Silver Metal Screws_Temp, 75,
 ,[Offset], Right_Habbi_Trail, 76,
 ,[Offset], Left_Habbi_Trail, 77,
 ,[Offset], Left_Habbi_Trail_B, 78,
 ,[Offset], Mid_Habbi_Trail, 79,
 ,[Offset], L_Piece_Light_Alpha, 80,
 ,[Offset], Tower_Light_Alpha, 81,
RSID_MEDIEVAL_MODELS, 2030,,,
 ,[Offset], Flipper_L, 0,
 ,[Offset], Flipper_R, 1,
 ,[Offset], Flipper_R, 2,
 ,[Offset], Flipper_R, 3,
 ,[Offset], Castle, 4,
 ,[Offset], Castle_Back_Left_Tower, 5,
 ,[Offset], Castle_Back_Right_Tower, 6,
 ,[Offset], Castle_Draw_Bridge, 7,
 ,[Offset], Castle_Front_Left_Tower, 8,
 ,[Offset], Castle_Front_Right_Tower, 9,
 ,[Offset], Castle_Gate_Left, 10,
 ,[Offset], Castle_Gate_Main, 11,
 ,[Offset], Castle_Mid_Tower, 12,
 ,[Offset], Catapult, 13,
 ,[Offset], Dragon, 14,
 ,[Offset], Flashers, 15,
 ,[Offset], Gate_A, 16,
 ,[Offset], LightCutouts, 17,
 ,[Offset], Metal_Pieces, 18,
 ,[Offset], OneWay_Gate_A, 19,
 ,[Offset], Plastic_Pieces, 20,
 ,[Offset], Playfield, 21,
 ,[Offset], PopBumpers, 22,
 ,[Offset], PopBumper_Metal, 23,
 ,[Offset], Rubber_Pieces, 24,
 ,[Offset], Slingshot_Left, 25,
 ,[Offset], Slingshot_Right, 26,
 ,[Offset], Switch_A, 27,
 ,[Offset], Table, 28,
 ,[Offset], Target_A, 29,
 ,[Offset], Target_Yellow_A, 30,
 ,[Offset], Transparent_Parts, 31,
 ,[Offset], Troll_Left, 32,
 ,[Offset], Wire_A, 33,
 ,[Offset], Dragon_Ramp_Diverter, 34,
 ,[Offset], Flashers_B, 35,
 ,[Offset], Flashers_C, 36,
 ,[Offset], Flashers_D, 37,
 ,[Offset], Transparent_Parts_2, 38,
 ,[Offset], Habit_Trails, 39,
 ,[Offset], Lamps, 40,
 ,[Offset], Dragon_Lights, 41,
 ,[Offset], Dragon_Lights, 42,
RSID_MEDIEVAL_MODELS_LODS, 2031,,,
 ,[Offset], Flipper_L, 0,
 ,[Offset], Flipper_R, 1,
 ,[Offset], Flipper_R, 2,
 ,[Offset], Flipper_R, 3,
 ,[Offset], Castle, 4,
 ,[Offset], Castle_Back_Left_Tower, 5,
 ,[Offset], Castle_Back_Right_Tower, 6,
 ,[Offset], Castle_Draw_Bridge, 7,
 ,[Offset], Castle_Front_Left_Tower, 8,
 ,[Offset], Castle_Front_Right_Tower, 9,
 ,[Offset], Castle_Gate_Left, 10,
 ,[Offset], Castle_Gate_Main, 11,
 ,[Offset], Castle_Mid_Tower, 12,
 ,[Offset], Catapult, 13,
 ,[Offset], Dragon, 14,
 ,[Offset], Flashers, 15,
 ,[Offset], Gate_A, 16,
 ,[Offset], LightCutouts, 17,
 ,[Offset], Metal_Pieces, 18,
 ,[Offset], OneWay_Gate_A, 19,
 ,[Offset], Plastic_Pieces, 20,
 ,[Offset], Playfield, 21,
 ,[Offset], PopBumpers, 22,
 ,[Offset], PopBumper_Metal, 23,
 ,[Offset], Rubber_Pieces, 24,
 ,[Offset], Slingshot_Left, 25,
 ,[Offset], Slingshot_Right, 26,
 ,[Offset], Switch_A, 27,
 ,[Offset], Table, 28,
 ,[Offset], Target_A, 29,
 ,[Offset], Target_Yellow_A, 30,
 ,[Offset], Transparent_Parts, 31,
 ,[Offset], Troll_Left, 32,
 ,[Offset], Wire_A, 33,
 ,[Offset], Dragon_Ramp_Diverter, 34,
 ,[Offset], Flashers_B, 35,
 ,[Offset], Flashers_C, 36,
 ,[Offset], Flashers_D, 37,
 ,[Offset], Transparent_Parts_2, 38,
 ,[Offset], Habit_Trails, 39,
 ,[Offset], Lamps, 40,
 ,[Offset], Dragon_Lights, 41,
 ,[Offset], LightCutouts, 42,
RSID_MEDIEVAL_COLLISIONS, 2032,,,
 ,[Offset], Flipper_Left_Front, 0,
 ,[Offset], Flipper_Left_Back, 1,
 ,[Offset], Flipper_Right_Front, 2,
 ,[Offset], Flipper_Right_Back, 3,
 ,[Offset], Arc, 4,
 ,[Offset], Ball_Drain, 5,
 ,[Offset], Left_Ramp, 6,
 ,[Offset], Left_Slingshot, 7,
 ,[Offset], Lower_Ramp, 8,
 ,[Offset], Mid_Ramp, 9,
 ,[Offset], Playfield, 10,
 ,[Offset], Right_Ramp, 11,
 ,[Offset], Right_Slingshot, 12,
 ,[Offset], Rubber, 13,
 ,[Offset], SkillShot, 14,
 ,[Offset], Wall, 15,
 ,[Offset], Castle_Trap, 16,
 ,[Offset], Catapult_Trap, 17,
 ,[Offset], MerlinsMagicTrap, 18,
 ,[Offset], OneWayGate_Back, 19,
 ,[Offset], OneWayGate_Front, 20,
 ,[Offset], Castle_Main_Gate, 21,
 ,[Offset], Castle_Gate_Left, 22,
 ,[Offset], Castle_DrawBridge, 23,
 ,[Offset], Castle_Main_Gate_Optical, 24,
 ,[Offset], Target_Yellow, 25,
 ,[Offset], Target A col, 26,
 ,[Offset], Gate col, 27,
 ,[Offset], Popbumper col, 28,
 ,[Offset], Dragon_Ramp_Diverter, 29,
 ,[Offset], Dragon_Tower_Diverter, 30,
 ,[Offset], Gate_Back, 31,
 ,[Offset], Gate_Front, 32,
 ,[Offset], Troll, 33,
 ,[Offset], Right_Ramp_Curve, 34,
 ,[Offset], Flipper_Lane, 35,
 ,[Offset], Rubber_Jets, 36,
RSID_MEDIEVAL_PLACEMENT, 2033,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_MEDIEVAL_ROM, 2034,,,
 ,[Offset], mm_10, 0,
 ,[Offset], medieval_reset, 1,
 ,[Offset], mm_start1p, 2,
 ,[Offset], mm_start2p, 3,
 ,[Offset], mm_start3p, 4,
 ,[Offset], mm_start4p, 5,
 ,[Offset], mm_10, 6,
 ,[Offset], mm_default, 7,
RSID_MEDIEVAL_SOUNDS_START, 2035,,,
RSID_MEDIEVAL_EMU_SOUNDS, 2036,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP, 1,
 ,[Offset], S0003-LP1, 2,
 ,[Offset], S0003-LP2, 3,
 ,[Offset], S0004-LP, 4,
 ,[Offset], S0005-LP1, 5,
 ,[Offset], S0005-LP2, 6,
 ,[Offset], S0006-LP, 7,
 ,[Offset], S0007-LP1, 8,
 ,[Offset], S0007-LP2, 9,
 ,[Offset], S0008-LP, 10,
 ,[Offset], S0009_C3, 11,
 ,[Offset], S000A-LP, 12,
 ,[Offset], S000B-LP, 13,
 ,[Offset], S000C-LP1, 14,
 ,[Offset], S000C-LP2, 15,
 ,[Offset], S000D-LP1, 16,
 ,[Offset], S000D-LP2, 17,
 ,[Offset], S000E_C3, 18,
 ,[Offset], S000F-LP, 19,
 ,[Offset], S0010-LP, 20,
 ,[Offset], S0014_C3, 21,
 ,[Offset], S0019-LP1, 22,
 ,[Offset], S0019-LP2, 23,
 ,[Offset], S001A-LP, 24,
 ,[Offset], S001E_C3, 25,
 ,[Offset], S001F-LP1, 26,
 ,[Offset], S001F-LP2, 27,
 ,[Offset], S0066_C4, 28,
 ,[Offset], S0068_C4, 29,
 ,[Offset], S006A_C4, 30,
 ,[Offset], S006C_C4, 31,
 ,[Offset], S0072_C4, 32,
 ,[Offset], S007A_C4, 33,
 ,[Offset], S007C_C4, 34,
 ,[Offset], S007E_C4, 35,
 ,[Offset], S0080_C4, 36,
 ,[Offset], S0082_C4, 37,
 ,[Offset], S0084_C4, 38,
 ,[Offset], S0086_C4, 39,
 ,[Offset], S008A_C4, 40,
 ,[Offset], S0090_C4, 41,
 ,[Offset], S0092_C4, 42,
 ,[Offset], S0094_C4, 43,
 ,[Offset], S0096_C4, 44,
 ,[Offset], S0098_C4, 45,
 ,[Offset], S009A_C4, 46,
 ,[Offset], S00A0_C4, 47,
 ,[Offset], S00A2_C4, 48,
 ,[Offset], S00A4_C4, 49,
 ,[Offset], S00A6_C4, 50,
 ,[Offset], S00A8_C4, 51,
 ,[Offset], S00AE_C4, 52,
 ,[Offset], S00B2_C4, 53,
 ,[Offset], S00B4_C4, 54,
 ,[Offset], S00B6_C4, 55,
 ,[Offset], S00B8_C4, 56,
 ,[Offset], S00BA_C4, 57,
 ,[Offset], S00BC_C4, 58,
 ,[Offset], S00BE_C4, 59,
 ,[Offset], S00C0_C4, 60,
 ,[Offset], S00C2_C4, 61,
 ,[Offset], S00C4_C4, 62,
 ,[Offset], S00C8_C4, 63,
 ,[Offset], S00CE_C4, 64,
 ,[Offset], S00D0_C4, 65,
 ,[Offset], S00D2_C4, 66,
 ,[Offset], S00D4_C4, 67,
 ,[Offset], S00D6_C4, 68,
 ,[Offset], S00D8_C4, 69,
 ,[Offset], S00DC_C4, 70,
 ,[Offset], S00DE_C4, 71,
 ,[Offset], S00E6_C4, 72,
 ,[Offset], S00E8_C4, 73,
 ,[Offset], S00EA_C4, 74,
 ,[Offset], S00EC_C4, 75,
 ,[Offset], S00EE_C4, 76,
 ,[Offset], S00F0_C4, 77,
 ,[Offset], S00F4_C4, 78,
 ,[Offset], S00F6_C4, 79,
 ,[Offset], S00F8_C4, 80,
 ,[Offset], S00FA_C4, 81,
 ,[Offset], S0104_C4, 82,
 ,[Offset], S0106_C4, 83,
 ,[Offset], S010A_C4, 84,
 ,[Offset], S010C_C4, 85,
 ,[Offset], S010E_C4, 86,
 ,[Offset], S0110_C4, 87,
 ,[Offset], S0112_C4, 88,
 ,[Offset], S0114_C4, 89,
 ,[Offset], S0116_C4, 90,
 ,[Offset], S0118_C4, 91,
 ,[Offset], S011A_C4, 92,
 ,[Offset], S0120_C4, 93,
 ,[Offset], S0122_C4, 94,
 ,[Offset], S0124_C4, 95,
 ,[Offset], S0126_C4, 96,
 ,[Offset], S0128_C4, 97,
 ,[Offset], S012A_C4, 98,
 ,[Offset], S012C_C4, 99,
 ,[Offset], S012E_C4, 100,
 ,[Offset], S0130_C4, 101,
 ,[Offset], S0134_C4, 102,
 ,[Offset], S0136_C4, 103,
 ,[Offset], S0138_C4, 104,
 ,[Offset], S013C_C4, 105,
 ,[Offset], S013E_C4, 106,
 ,[Offset], S0140_C4, 107,
 ,[Offset], S0142_C4, 108,
 ,[Offset], S0144_C4, 109,
 ,[Offset], S0146_C4, 110,
 ,[Offset], S0148_C4, 111,
 ,[Offset], S014A_C4, 112,
 ,[Offset], S014C_C4, 113,
 ,[Offset], S014E_C4, 114,
 ,[Offset], S0150_C4, 115,
 ,[Offset], S0156_C4, 116,
 ,[Offset], S015A_C4, 117,
 ,[Offset], S015C_C4, 118,
 ,[Offset], S015E_C4, 119,
 ,[Offset], S0160_C4, 120,
 ,[Offset], S0162_C4, 121,
 ,[Offset], S0164_C4, 122,
 ,[Offset], S0166_C4, 123,
 ,[Offset], S0168_C4, 124,
 ,[Offset], S016E_C4, 125,
 ,[Offset], S0170_C4, 126,
 ,[Offset], S0172_C4, 127,
 ,[Offset], S0174_C4, 128,
 ,[Offset], S0176_C4, 129,
 ,[Offset], S0178_C4, 130,
 ,[Offset], S017A_C4, 131,
 ,[Offset], S017C_C4, 132,
 ,[Offset], S017E_C4, 133,
 ,[Offset], S0180_C4, 134,
 ,[Offset], S0182_C4, 135,
 ,[Offset], S0184_C4, 136,
 ,[Offset], S018C_C4, 137,
 ,[Offset], S018E_C4, 138,
 ,[Offset], S0190_C4, 139,
 ,[Offset], S0192_C4, 140,
 ,[Offset], S0198_C4, 141,
 ,[Offset], S019C_C4, 142,
 ,[Offset], S019E_C4, 143,
 ,[Offset], S01A2_C4, 144,
 ,[Offset], S01A4_C4, 145,
 ,[Offset], S01A6_C4, 146,
 ,[Offset], S01A8_C4, 147,
 ,[Offset], S01AA_C4, 148,
 ,[Offset], S01AC_C4, 149,
 ,[Offset], S01AE_C4, 150,
 ,[Offset], S01B0_C4, 151,
 ,[Offset], S01B2_C4, 152,
 ,[Offset], S01B4_C4, 153,
 ,[Offset], S01B6_C4, 154,
 ,[Offset], S0200_C6, 155,
 ,[Offset], S0201_C6, 156,
 ,[Offset], S0202_C6, 157,
 ,[Offset], S0203_C6, 158,
 ,[Offset], S0204_C6, 159,
 ,[Offset], S0205_C6, 160,
 ,[Offset], S0206_C6, 161,
 ,[Offset], S0208_C6, 162,
 ,[Offset], S020A_C6, 163,
 ,[Offset], S020D_C6, 164,
 ,[Offset], S020E_C6, 165,
 ,[Offset], S020F_C6, 166,
 ,[Offset], S0216_C6, 167,
 ,[Offset], S0217_C6, 168,
 ,[Offset], S0218_C6, 169,
 ,[Offset], S0219_C6, 170,
 ,[Offset], S021A_C6, 171,
 ,[Offset], S021B_C6, 172,
 ,[Offset], S021C_C6, 173,
 ,[Offset], S021D_C6, 174,
 ,[Offset], S021F_C6, 175,
 ,[Offset], S0220_C6, 176,
 ,[Offset], S0221_C6, 177,
 ,[Offset], S0222_C6, 178,
 ,[Offset], S0225_C6, 179,
 ,[Offset], S0226_C6, 180,
 ,[Offset], S0227_C6, 181,
 ,[Offset], S0228_C6, 182,
 ,[Offset], S0229_C6, 183,
 ,[Offset], S022B_C6, 184,
 ,[Offset], S022C_C6, 185,
 ,[Offset], S022E_C6, 186,
 ,[Offset], S0230_C6, 187,
 ,[Offset], S0231_C6, 188,
 ,[Offset], S023A_C6, 189,
 ,[Offset], S023B_C6, 190,
 ,[Offset], S0242_C6, 191,
 ,[Offset], S0243_C6, 192,
 ,[Offset], S0247_C6, 193,
 ,[Offset], S0258_C6, 194,
 ,[Offset], S025A_C6, 195,
 ,[Offset], S025B_C6, 196,
 ,[Offset], S025C_C6, 197,
 ,[Offset], S025D_C6, 198,
 ,[Offset], S025E_C6, 199,
 ,[Offset], S0260_C6, 200,
 ,[Offset], S0261_C6, 201,
 ,[Offset], S0262_C6, 202,
 ,[Offset], S0263_C6, 203,
 ,[Offset], S0264_C6, 204,
 ,[Offset], S0265_C6, 205,
 ,[Offset], S0266_C6, 206,
 ,[Offset], S0267_C6, 207,
 ,[Offset], S0268_C6, 208,
 ,[Offset], S0269_C6, 209,
 ,[Offset], S026A_C6, 210,
 ,[Offset], S026C_C6, 211,
 ,[Offset], S026D_C6, 212,
 ,[Offset], S026E_C6, 213,
 ,[Offset], S0270_C6, 214,
 ,[Offset], S0271_C6, 215,
 ,[Offset], S0272_C6, 216,
 ,[Offset], S0277_C6, 217,
 ,[Offset], S0278_C6, 218,
 ,[Offset], S0281_C6, 219,
 ,[Offset], S0283_C6, 220,
 ,[Offset], S028A_C6, 221,
 ,[Offset], S028B_C6, 222,
 ,[Offset], S028C_C6, 223,
 ,[Offset], S028D_C6, 224,
 ,[Offset], S0290_C6, 225,
 ,[Offset], S0291_C6, 226,
 ,[Offset], S0292_C6, 227,
 ,[Offset], S0293_C6, 228,
 ,[Offset], S0294_C6, 229,
 ,[Offset], S0295_C6, 230,
 ,[Offset], S0296_C6, 231,
 ,[Offset], S0297_C6, 232,
 ,[Offset], S0298_C6, 233,
 ,[Offset], S029A_C6, 234,
 ,[Offset], S029E_C6, 235,
 ,[Offset], S029F_C6, 236,
 ,[Offset], S02A1_C6, 237,
 ,[Offset], S02A2_C6, 238,
 ,[Offset], S02A3_C6, 239,
 ,[Offset], S02A4_C6, 240,
 ,[Offset], S02A5_C6, 241,
 ,[Offset], S02A6_C6, 242,
 ,[Offset], S02A7_C6, 243,
 ,[Offset], S02BC_C6, 244,
 ,[Offset], S02BD_C6, 245,
 ,[Offset], S02BE_C6, 246,
 ,[Offset], S02BF_C6, 247,
 ,[Offset], S02C0_C6, 248,
 ,[Offset], S02C1_C6, 249,
 ,[Offset], S02C2_C6, 250,
 ,[Offset], S02C3_C6, 251,
 ,[Offset], S02C4_C6, 252,
 ,[Offset], S02C5_C6, 253,
 ,[Offset], S02C6_C6, 254,
 ,[Offset], S02C7_C6, 255,
 ,[Offset], S02C8_C6, 256,
 ,[Offset], S02C9_C6, 257,
 ,[Offset], S02CA_C6, 258,
 ,[Offset], S02CB_C6, 259,
 ,[Offset], S02CC_C6, 260,
 ,[Offset], S02CD_C6, 261,
 ,[Offset], S02CE_C6, 262,
 ,[Offset], S02CF_C6, 263,
 ,[Offset], S02D0_C6, 264,
 ,[Offset], S02D1_C6, 265,
 ,[Offset], S02D2_C6, 266,
 ,[Offset], S02D3_C6, 267,
 ,[Offset], S02D4_C6, 268,
 ,[Offset], S02DC_C6, 269,
 ,[Offset], S02DD_C6, 270,
 ,[Offset], S02DE_C6, 271,
 ,[Offset], S02DF_C6, 272,
 ,[Offset], S02E0_C6, 273,
 ,[Offset], S02E1_C6, 274,
 ,[Offset], S02E4_C6, 275,
 ,[Offset], S02E8_C6, 276,
 ,[Offset], S02E9_C6, 277,
 ,[Offset], S02EA_C6, 278,
 ,[Offset], S02EC_C6, 279,
 ,[Offset], S02EE_C6, 280,
 ,[Offset], S02EF_C6, 281,
 ,[Offset], S02F0_C6, 282,
 ,[Offset], S02F1_C6, 283,
 ,[Offset], S02F2_C6, 284,
 ,[Offset], S02F3_C6, 285,
 ,[Offset], S02F4_C6, 286,
 ,[Offset], S02F5_C6, 287,
 ,[Offset], S02F6_C6, 288,
 ,[Offset], S02F7_C6, 289,
 ,[Offset], S02F9_C6, 290,
 ,[Offset], S02FA_C6, 291,
 ,[Offset], S02FB_C6, 292,
 ,[Offset], S02FC_C6, 293,
 ,[Offset], S02FD_C6, 294,
 ,[Offset], S02FE_C6, 295,
 ,[Offset], S02FF_C6, 296,
 ,[Offset], S0300_C6, 297,
 ,[Offset], S0301_C6, 298,
 ,[Offset], S0302_C6, 299,
 ,[Offset], S0303_C6, 300,
 ,[Offset], S0304_C6, 301,
 ,[Offset], S0305_C6, 302,
 ,[Offset], S0306_C6, 303,
 ,[Offset], S0308_C6, 304,
 ,[Offset], S0309_C6, 305,
 ,[Offset], S030A_C6, 306,
 ,[Offset], S030B_C6, 307,
 ,[Offset], S030D_C6, 308,
 ,[Offset], S030E_C6, 309,
 ,[Offset], S0311_C6, 310,
 ,[Offset], S0312_C6, 311,
 ,[Offset], S0316_C6, 312,
 ,[Offset], S0317_C6, 313,
 ,[Offset], S0318_C6, 314,
 ,[Offset], S0319_C6, 315,
 ,[Offset], S031A_C6, 316,
 ,[Offset], S031B_C6, 317,
 ,[Offset], S031C_C6, 318,
 ,[Offset], S031D_C6, 319,
 ,[Offset], S031E_C6, 320,
 ,[Offset], S031F_C6, 321,
 ,[Offset], S03AA_C1, 322,
 ,[Offset], S03AC_C1, 323,
 ,[Offset], S03AE_C1, 324,
 ,[Offset], S03B2_C1, 325,
 ,[Offset], S03B4_C1, 326,
 ,[Offset], S03B6_C1, 327,
 ,[Offset], S03BC_C1, 328,
 ,[Offset], S03BE_C1, 329,
 ,[Offset], S03C0_C1, 330,
 ,[Offset], S03D4_C5, 331,
 ,[Offset], S03D5_C5, 332,
 ,[Offset], S03D6_C5, 333,
 ,[Offset], S03D7_C5, 334,
 ,[Offset], S03D8_C5, 335,
 ,[Offset], S03D9_C5, 336,
 ,[Offset], S03DA_C5, 337,
 ,[Offset], S03DB_C5, 338,
 ,[Offset], S03DC_C5, 339,
 ,[Offset], S03DD_C5, 340,
 ,[Offset], S044C_C6, 341,
 ,[Offset], S044D_C6, 342,
 ,[Offset], S044E_C6, 343,
 ,[Offset], S044F_C6, 344,
 ,[Offset], S0450_C6, 345,
 ,[Offset], S0451_C6, 346,
 ,[Offset], S0452_C6, 347,
 ,[Offset], S0453_C6, 348,
 ,[Offset], S0454_C6, 349,
 ,[Offset], S0455_C6, 350,
 ,[Offset], S0457_C6, 351,
 ,[Offset], S0459_C6, 352,
 ,[Offset], S045A_C6, 353,
 ,[Offset], S045B_C6, 354,
 ,[Offset], S045C_C6, 355,
 ,[Offset], S045D_C6, 356,
 ,[Offset], S045E_C6, 357,
 ,[Offset], S0460_C6, 358,
 ,[Offset], S0461_C6, 359,
 ,[Offset], S0462_C6, 360,
 ,[Offset], S046A_C6, 361,
 ,[Offset], S046B_C6, 362,
 ,[Offset], S046C_C6, 363,
 ,[Offset], S046D_C6, 364,
 ,[Offset], S046E_C6, 365,
 ,[Offset], S0470_C6, 366,
 ,[Offset], S0471_C6, 367,
 ,[Offset], S0472_C6, 368,
 ,[Offset], S0473_C6, 369,
 ,[Offset], S0474_C6, 370,
 ,[Offset], S0475_C6, 371,
 ,[Offset], S0477_C6, 372,
 ,[Offset], S0478_C6, 373,
 ,[Offset], S047A_C6, 374,
 ,[Offset], S047D_C6, 375,
 ,[Offset], S047E_C6, 376,
 ,[Offset], S047F_C6, 377,
 ,[Offset], S0488_C6, 378,
 ,[Offset], S0489_C6, 379,
 ,[Offset], S048A_C6, 380,
 ,[Offset], S048B_C6, 381,
 ,[Offset], S048C_C6, 382,
 ,[Offset], S048D_C6, 383,
 ,[Offset], S048E_C6, 384,
 ,[Offset], S0490_C6, 385,
 ,[Offset], S0491_C6, 386,
 ,[Offset], S0492_C6, 387,
 ,[Offset], S0493_C6, 388,
 ,[Offset], S0494_C6, 389,
 ,[Offset], S0496_C6, 390,
 ,[Offset], S0497_C6, 391,
 ,[Offset], S0498_C6, 392,
 ,[Offset], S049C_C6, 393,
 ,[Offset], S049D_C6, 394,
 ,[Offset], S049E_C6, 395,
 ,[Offset], S049F_C6, 396,
 ,[Offset], S04A6_C6, 397,
 ,[Offset], S04A7_C6, 398,
 ,[Offset], S04A8_C6, 399,
 ,[Offset], S04A9_C6, 400,
 ,[Offset], S04AA_C6, 401,
 ,[Offset], S04AB_C6, 402,
 ,[Offset], S04AC_C6, 403,
 ,[Offset], S04AE_C6, 404,
 ,[Offset], S04B0_C6, 405,
 ,[Offset], S04B1_C6, 406,
 ,[Offset], S04B2_C6, 407,
 ,[Offset], S04B3_C6, 408,
 ,[Offset], S04B4_C6, 409,
 ,[Offset], S04B6_C6, 410,
 ,[Offset], S04B7_C6, 411,
 ,[Offset], S04B8_C6, 412,
 ,[Offset], S04B9_C6, 413,
 ,[Offset], S04BA_C6, 414,
 ,[Offset], S04BB_C6, 415,
 ,[Offset], S04BC_C6, 416,
 ,[Offset], S04BD_C6, 417,
 ,[Offset], S04CE_C6, 418,
 ,[Offset], S04CF_C6, 419,
 ,[Offset], S04D0_C6, 420,
 ,[Offset], S04D4_C6, 421,
 ,[Offset], S04D5_C6, 422,
 ,[Offset], S04D6_C6, 423,
 ,[Offset], S04D7_C6, 424,
 ,[Offset], S04DA_C6, 425,
 ,[Offset], S04DB_C6, 426,
 ,[Offset], S04DF_C6, 427,
 ,[Offset], S04E0_C6, 428,
 ,[Offset], S04E2_C6, 429,
 ,[Offset], S04E3_C6, 430,
 ,[Offset], S04E4_C6, 431,
 ,[Offset], S04E5_C6, 432,
 ,[Offset], S04E7_C6, 433,
 ,[Offset], S04E8_C6, 434,
 ,[Offset], S04E9_C6, 435,
 ,[Offset], S04EA_C6, 436,
 ,[Offset], S04EB_C6, 437,
 ,[Offset], S04ED_C6, 438,
 ,[Offset], S04EE_C6, 439,
 ,[Offset], S04EF_C6, 440,
 ,[Offset], S04F1_C6, 441,
 ,[Offset], S04F2_C6, 442,
 ,[Offset], S04F4_C6, 443,
 ,[Offset], S04F5_C6, 444,
 ,[Offset], S04FC_C6, 445,
 ,[Offset], S04FD_C6, 446,
 ,[Offset], S0500_C6, 447,
 ,[Offset], S0501_C6, 448,
 ,[Offset], S0503_C6, 449,
 ,[Offset], S0504_C6, 450,
 ,[Offset], S050A_C6, 451,
 ,[Offset], S050B_C6, 452,
 ,[Offset], S050C_C6, 453,
 ,[Offset], S050D_C6, 454,
 ,[Offset], S050E_C6, 455,
 ,[Offset], S0514_C6, 456,
 ,[Offset], S0515_C6, 457,
 ,[Offset], S0516_C6, 458,
 ,[Offset], S0517_C6, 459,
 ,[Offset], S0518_C6, 460,
 ,[Offset], S0519_C6, 461,
 ,[Offset], S051A_C6, 462,
 ,[Offset], S051D_C6, 463,
 ,[Offset], S051E_C6, 464,
 ,[Offset], S051F_C6, 465,
 ,[Offset], S0520_C6, 466,
 ,[Offset], S0523_C6, 467,
 ,[Offset], S0524_C6, 468,
 ,[Offset], S0532_C6, 469,
 ,[Offset], S0534_C6, 470,
 ,[Offset], S0535_C6, 471,
 ,[Offset], S0536_C6, 472,
 ,[Offset], S0537_C6, 473,
 ,[Offset], S0538_C6, 474,
 ,[Offset], S0539_C6, 475,
 ,[Offset], S053C_C6, 476,
 ,[Offset], S053D_C6, 477,
 ,[Offset], S053E_C6, 478,
 ,[Offset], S053F_C6, 479,
 ,[Offset], S0540_C6, 480,
 ,[Offset], S0541_C6, 481,
 ,[Offset], S0542_C6, 482,
 ,[Offset], S0551_C6, 483,
 ,[Offset], S0553_C6, 484,
 ,[Offset], S0554_C6, 485,
 ,[Offset], S0555_C6, 486,
 ,[Offset], S0556_C6, 487,
 ,[Offset], S0557_C6, 488,
 ,[Offset], S0558_C6, 489,
 ,[Offset], S0559_C6, 490,
 ,[Offset], S055A_C6, 491,
 ,[Offset], S055C_C6, 492,
 ,[Offset], S055D_C6, 493,
 ,[Offset], S055E_C6, 494,
 ,[Offset], S055F_C6, 495,
 ,[Offset], S056E_C6, 496,
 ,[Offset], S0570_C6, 497,
 ,[Offset], S0571_C6, 498,
 ,[Offset], S0572_C6, 499,
 ,[Offset], S0574_C6, 500,
 ,[Offset], S0577_C6, 501,
 ,[Offset], S0578_C6, 502,
 ,[Offset], S057B_C6, 503,
 ,[Offset], S057C_C6, 504,
 ,[Offset], S057D_C6, 505,
 ,[Offset], S057E_C6, 506,
 ,[Offset], S057F_C6, 507,
 ,[Offset], S058C_C6, 508,
 ,[Offset], S058E_C6, 509,
 ,[Offset], S0590_C6, 510,
 ,[Offset], S0591_C6, 511,
 ,[Offset], S0593_C6, 512,
 ,[Offset], S0595_C6, 513,
 ,[Offset], S0596_C6, 514,
 ,[Offset], S0597_C6, 515,
 ,[Offset], S0598_C6, 516,
 ,[Offset], S0599_C6, 517,
 ,[Offset], S059A_C6, 518,
 ,[Offset], S059B_C6, 519,
 ,[Offset], S05E2_C4, 520,
 ,[Offset], S05EA_C4, 521,
 ,[Offset], S06A7_C6, 522,
 ,[Offset], S06AA_C6, 523,
 ,[Offset], S06AB_C6, 524,
 ,[Offset], S06AC_C6, 525,
 ,[Offset], S06AD_C6, 526,
 ,[Offset], S06AE_C6, 527,
 ,[Offset], S06AF_C6, 528,
 ,[Offset], S06B0_C6, 529,
 ,[Offset], S06B1_C6, 530,
 ,[Offset], S06B4_C6, 531,
 ,[Offset], S06B5_C6, 532,
 ,[Offset], S06B6_C6, 533,
 ,[Offset], S06B7_C6, 534,
 ,[Offset], S06B8_C6, 535,
 ,[Offset], S06B9_C6, 536,
 ,[Offset], S06BA_C6, 537,
 ,[Offset], S06BB_C6, 538,
 ,[Offset], S06BC_C6, 539,
 ,[Offset], S06BD_C6, 540,
 ,[Offset], S06BE_C6, 541,
 ,[Offset], S06BF_C6, 542,
 ,[Offset], S06C0_C6, 543,
 ,[Offset], S06C1_C6, 544,
 ,[Offset], S06C2_C6, 545,
 ,[Offset], S06C4_C6, 546,
 ,[Offset], S06C6_C6, 547,
 ,[Offset], S06C7_C6, 548,
 ,[Offset], S06C8_C6, 549,
 ,[Offset], S06C9_C6, 550,
 ,[Offset], S06CA_C6, 551,
 ,[Offset], S0708_C6, 552,
 ,[Offset], S0709_C6, 553,
 ,[Offset], S070A_C6, 554,
 ,[Offset], S070B_C6, 555,
 ,[Offset], S0718_C6, 556,
 ,[Offset], S071A_C6, 557,
 ,[Offset], S071B_C6, 558,
 ,[Offset], S071C_C6, 559,
 ,[Offset], S071D_C6, 560,
 ,[Offset], S071E_C6, 561,
 ,[Offset], S071F_C6, 562,
 ,[Offset], S0720_C6, 563,
 ,[Offset], S0721_C6, 564,
 ,[Offset], S0722_C6, 565,
 ,[Offset], S0723_C6, 566,
 ,[Offset], S0724_C6, 567,
 ,[Offset], S0725_C6, 568,
 ,[Offset], S0726_C6, 569,
 ,[Offset], S0727_C6, 570,
 ,[Offset], S0728_C6, 571,
 ,[Offset], S0729_C6, 572,
 ,[Offset], S072A_C6, 573,
 ,[Offset], S072B_C6, 574,
 ,[Offset], S072C_C6, 575,
 ,[Offset], S072D_C6, 576,
 ,[Offset], S072E_C6, 577,
 ,[Offset], S072F_C6, 578,
 ,[Offset], S0730_C6, 579,
 ,[Offset], S0731_C6, 580,
 ,[Offset], S0732_C6, 581,
 ,[Offset], S0733_C6, 582,
 ,[Offset], S0734_C6, 583,
 ,[Offset], S0735_C6, 584,
 ,[Offset], S0736_C6, 585,
 ,[Offset], S0737_C6, 586,
 ,[Offset], S073A_C6, 587,
 ,[Offset], S073B_C6, 588,
 ,[Offset], S073C_C6, 589,
 ,[Offset], S073D_C6, 590,
 ,[Offset], S073E_C6, 591,
 ,[Offset], S073F_C6, 592,
 ,[Offset], S0740_C6, 593,
 ,[Offset], S0741_C6, 594,
 ,[Offset], S0742_C6, 595,
 ,[Offset], S0743_C6, 596,
RSID_MEDIEVAL_MECH_SOUNDS, 2037,,,
 ,[Offset], Med_ball_lock, 0,
 ,[Offset], Med_bumper1, 1,
 ,[Offset], Med_bumper2, 2,
 ,[Offset], Med_bumper3, 3,
 ,[Offset], med_castle_gate_close, 4,
 ,[Offset], med_castle_gate_open, 5,
 ,[Offset], Med_catapult, 6,
 ,[Offset], med_damsel_door_save_princess, 7,
 ,[Offset], med_destroy_castle_1_hit, 8,
 ,[Offset], med_destroy_castle_3_hits, 9,
 ,[Offset], med_drawbridge_motor, 10,
 ,[Offset], Med_enter_catapult, 11,
 ,[Offset], med_hit_castle_gate, 12,
 ,[Offset], med_hit_drawbridge, 13,
 ,[Offset], Med_moat_eject, 14,
 ,[Offset], Med_plunge, 15,
 ,[Offset], Med_plunge_oneway, 16,
 ,[Offset], Med_plunge_stop, 17,
 ,[Offset], Med_princess_diverter_down, 18,
 ,[Offset], Med_princess_hit_diverter, 19,
 ,[Offset], Med_princess_hit_switch, 20,
 ,[Offset], Med_tower_post_eject, 21,
 ,[Offset], gate_down, 22,
 ,[Offset], gate_up, 23,
 ,[Offset], trolls_down, 24,
 ,[Offset], trolls_up, 25,
RSID_MEDIEVAL_SAMPLES, 2038,,,
RSID_MEDIEVAL_SOUNDS_END, 2039,,,
RSID_TABLE_MEDIEVAL_VERSION, 2040,,,
RSID_MEDIEVAL_END, 2041,,,
