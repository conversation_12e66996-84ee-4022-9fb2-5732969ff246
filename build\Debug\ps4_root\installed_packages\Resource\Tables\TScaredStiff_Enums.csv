RSID_TSCAREDSTIFF_START, 3125,,,
 ,[Offset], SCARED_STIFF_FLYER_1, 0,
 ,[Offset], ScaredStiff\PBTScaredStiff, 1,
 ,[Offset], ScaredStiff\InstructionsENG, 2,
 ,[Offset], <PERSON><PERSON>Stiff\InstructionsFR, 3,
 ,[Offset], ScaredStiff\InstructionsITAL, 4,
 ,[Offset], ScaredStiff\InstructionsGERM, 5,
 ,[Offset], ScaredStiff\InstructionsSPAN, 6,
 ,[Offset], ScaredStiff\InstructionsPORT, 7,
 ,[Offset], ScaredStiff\InstructionsDUTCH, 8,
 ,[Offset], tables\ScaredStiff_BG_scroll, 9,
 ,[Offset], Pro_TipsENG, 10,
 ,[Offset], Pro_TipsFR, 11,
 ,[Offset], Pro_TipsITAL, 12,
 ,[Offset], Pro_TipsGERM, 13,
 ,[Offset], Pro_TipsSPAN, 14,
 ,[Offset], Pro_TipsENG, 15,
 ,[Offset], Pro_TipsENG, 16,
RSID_TSCAREDSTIFF_LIGHTS, 3126,,,
RSID_TSCAREDSTIFF_CAMERAS, 3127,,,
RSID_TSCAREDSTIFF_LAMP_TEXTURES, 3128,,,
 ,[Offset], L_11_Off, 0,
 ,[Offset], L_11_On, 1,
 ,[Offset], L_12_Off, 2,
 ,[Offset], L_12_On, 3,
 ,[Offset], L_13_Off, 4,
 ,[Offset], L_13_On, 5,
 ,[Offset], L_14_Off, 6,
 ,[Offset], L_14_On, 7,
 ,[Offset], L_15_Off, 8,
 ,[Offset], L_15_On, 9,
 ,[Offset], L_16_Off, 10,
 ,[Offset], L_16_On, 11,
 ,[Offset], L_17_Off, 12,
 ,[Offset], L_17_On, 13,
 ,[Offset], L_18_Off, 14,
 ,[Offset], L_18_On, 15,
 ,[Offset], L_21_Off, 16,
 ,[Offset], L_21_On, 17,
 ,[Offset], L_22_Off, 18,
 ,[Offset], L_22_On, 19,
 ,[Offset], L_23_Off, 20,
 ,[Offset], L_23_On, 21,
 ,[Offset], L_24_Off, 22,
 ,[Offset], L_24_On, 23,
 ,[Offset], L_25_Off, 24,
 ,[Offset], L_25_On, 25,
 ,[Offset], L_26_Off, 26,
 ,[Offset], L_26_On, 27,
 ,[Offset], L_27_Off, 28,
 ,[Offset], L_27_On, 29,
 ,[Offset], L_28_Off, 30,
 ,[Offset], L_28_On, 31,
 ,[Offset], L_28_On, 32,
 ,[Offset], L_28_On, 33,
 ,[Offset], L_28_On, 34,
 ,[Offset], L_28_On, 35,
 ,[Offset], L_28_On, 36,
 ,[Offset], L_28_On, 37,
 ,[Offset], L_28_On, 38,
 ,[Offset], L_28_On, 39,
 ,[Offset], L_35_Off, 40,
 ,[Offset], L_35_On, 41,
 ,[Offset], L_36_Off, 42,
 ,[Offset], L_36_On, 43,
 ,[Offset], L_37_Off, 44,
 ,[Offset], L_37_On, 45,
 ,[Offset], L_38_Off, 46,
 ,[Offset], L_38_On, 47,
 ,[Offset], L_41_Off, 48,
 ,[Offset], L_41_On, 49,
 ,[Offset], L_42_Off, 50,
 ,[Offset], L_42_On, 51,
 ,[Offset], L_43_Off, 52,
 ,[Offset], L_43_On, 53,
 ,[Offset], L_44_Off, 54,
 ,[Offset], L_44_On, 55,
 ,[Offset], L_45_Off, 56,
 ,[Offset], L_45_On, 57,
 ,[Offset], L_46_Off, 58,
 ,[Offset], L_46_On, 59,
 ,[Offset], L_47_Off, 60,
 ,[Offset], L_47_On, 61,
 ,[Offset], L_48_Off, 62,
 ,[Offset], L_48_On, 63,
 ,[Offset], L_51_Off, 64,
 ,[Offset], L_51_On, 65,
 ,[Offset], L_52_Off, 66,
 ,[Offset], L_52_On, 67,
 ,[Offset], L_53_Off, 68,
 ,[Offset], L_53_On, 69,
 ,[Offset], L_54_Off, 70,
 ,[Offset], L_54_On, 71,
 ,[Offset], L_55_Off, 72,
 ,[Offset], L_55_On, 73,
 ,[Offset], L_56_Off, 74,
 ,[Offset], L_56_On, 75,
 ,[Offset], L_57_Off, 76,
 ,[Offset], L_57_On, 77,
 ,[Offset], L_58_Off, 78,
 ,[Offset], L_58_On, 79,
 ,[Offset], L_61_Off, 80,
 ,[Offset], L_61_On, 81,
 ,[Offset], L_62_Off, 82,
 ,[Offset], L_62_On, 83,
 ,[Offset], L_63_Off, 84,
 ,[Offset], L_63_On, 85,
 ,[Offset], L_64_Off, 86,
 ,[Offset], L_64_On, 87,
 ,[Offset], L_65_Off, 88,
 ,[Offset], L_65_On, 89,
 ,[Offset], L_66_Off, 90,
 ,[Offset], L_66_On, 91,
 ,[Offset], L_67_Off, 92,
 ,[Offset], L_67_On, 93,
 ,[Offset], L_68_Off, 94,
 ,[Offset], L_68_On, 95,
 ,[Offset], L_71_Off, 96,
 ,[Offset], L_71_On, 97,
 ,[Offset], L_72_Off, 98,
 ,[Offset], L_72_On, 99,
 ,[Offset], L_73_Off, 100,
 ,[Offset], L_73_On, 101,
 ,[Offset], L_74_Off, 102,
 ,[Offset], L_74_On, 103,
 ,[Offset], L_75_Off, 104,
 ,[Offset], L_75_On, 105,
 ,[Offset], L_76_Off, 106,
 ,[Offset], L_76_On, 107,
 ,[Offset], L_77_Off, 108,
 ,[Offset], L_77_On, 109,
 ,[Offset], L_78_Off, 110,
 ,[Offset], L_78_On, 111,
 ,[Offset], L_81_Off, 112,
 ,[Offset], L_81_On, 113,
 ,[Offset], L_82_Off, 114,
 ,[Offset], L_82_On, 115,
 ,[Offset], L_83_Off, 116,
 ,[Offset], L_83_On, 117,
 ,[Offset], L_84_Off, 118,
 ,[Offset], L_84_On, 119,
 ,[Offset], L_85_Off, 120,
 ,[Offset], L_85_On, 121,
 ,[Offset], L_86_Off, 122,
 ,[Offset], L_86_On, 123,
 ,[Offset], L_86_On, 124,
 ,[Offset], L_86_On, 125,
 ,[Offset], L_86_On, 126,
 ,[Offset], L_86_On, 127,
 ,[Offset], F_20_Off, 128,
 ,[Offset], F_20_On, 129,
 ,[Offset], F_25_Off, 130,
 ,[Offset], F_25_On, 131,
 ,[Offset], F_17_Off, 132,
 ,[Offset], F_17_On, 133,
 ,[Offset], F_17_B_Off, 134,
 ,[Offset], F_17_B_On, 135,
 ,[Offset], F_18_Off, 136,
 ,[Offset], F_18_On, 137,
 ,[Offset], F_18_B_Off, 138,
 ,[Offset], F_18_B_On, 139,
 ,[Offset], F_19_Off, 140,
 ,[Offset], F_19_On, 141,
 ,[Offset], F_19_B_Off, 142,
 ,[Offset], F_19_B_On, 143,
 ,[Offset], F_22_Off, 144,
 ,[Offset], F_22_On, 145,
 ,[Offset], F_22_B_Off, 146,
 ,[Offset], F_22_B_On, 147,
 ,[Offset], F_24_Off, 148,
 ,[Offset], F_24_On, 149,
 ,[Offset], F_24_B_Off, 150,
 ,[Offset], F_24_B_On, 151,
 ,[Offset], F_27_Off, 152,
 ,[Offset], F_27_On, 153,
 ,[Offset], F_27_B_Off, 154,
 ,[Offset], F_27_B_On, 155,
 ,[Offset], F_28_Off, 156,
 ,[Offset], F_28_On, 157,
 ,[Offset], F_28_B_Off, 158,
 ,[Offset], F_28_B_On, 159,
 ,[Offset], F_35_Off, 160,
 ,[Offset], F_35_On, 161,
 ,[Offset], F_35_B_Off, 162,
 ,[Offset], F_35_B_On, 163,
 ,[Offset], F_36_Off, 164,
 ,[Offset], F_36_On, 165,
 ,[Offset], F_36_B_Off, 166,
 ,[Offset], F_36_B_On, 167,
 ,[Offset], F_21_Off, 168,
 ,[Offset], F_21_On, 169,
 ,[Offset], F_56_B_Off, 170,
 ,[Offset], F_56_B_On, 171,
 ,[Offset], F_21_B_Off, 172,
 ,[Offset], F_21_B_On, 173,
 ,[Offset], F_21_C_Off, 174,
 ,[Offset], F_21_C_On, 175,
 ,[Offset], F_21_D_Off, 176,
 ,[Offset], F_21_D_On, 177,
 ,[Offset], F_26_B_Off, 178,
 ,[Offset], F_26_B_On, 179,
 ,[Offset], F_26_C_Off, 180,
 ,[Offset], F_26_C_On, 181,
 ,[Offset], F_26_Off, 182,
 ,[Offset], F_26_On, 183,
 ,[Offset], F_21_E_Off, 184,
 ,[Offset], F_21_E_On, 185,
 ,[Offset], Crate_A_Off, 186,
 ,[Offset], Crate_A_On, 187,
 ,[Offset], Crate_B_Off, 188,
 ,[Offset], Crate_B_On, 189,
 ,[Offset], Crate_C_Off, 190,
 ,[Offset], Crate_C_On, 191,
 ,[Offset], Crate_D_Off, 192,
 ,[Offset], Crate_D_On, 193,
 ,[Offset], Crate_E_Off, 194,
 ,[Offset], Crate_E_On, 195,
 ,[Offset], Crate_F_Off, 196,
 ,[Offset], Crate_F_On, 197,
 ,[Offset], Crate_G_Off, 198,
 ,[Offset], Crate_G_On, 199,
 ,[Offset], Crate_H_Off, 200,
 ,[Offset], Crate_H_On, 201,
RSID_TSCAREDSTIFF_TEXTURES, 3129,,,
 ,[Offset], display_frame_generic, 0,
 ,[Offset], Black_Grain, 1,
 ,[Offset], Black_Metal, 2,
 ,[Offset], Blue_Plastics, 3,
 ,[Offset], Cabinet, 4,
 ,[Offset], Cabinet_Front, 5,
 ,[Offset], Coin_Slot, 6,
 ,[Offset], Extra_Metal_Parts, 7,
 ,[Offset], Flipper_Button, 8,
 ,[Offset], Metal_Parts, 9,
 ,[Offset], Ramp_Backbones, 10,
 ,[Offset], ramp_metals, 11,
 ,[Offset], red_plastics, 12,
 ,[Offset], skull_t_c, 13,
 ,[Offset], Snake_RampDetails, 14,
 ,[Offset], ss_apron, 15,
 ,[Offset], start_button, 16,
 ,[Offset], tower_t_c, 17,
 ,[Offset], coffin_t_c, 18,
 ,[Offset], spider_t_c, 19,
 ,[Offset], frog_texture01, 20,
 ,[Offset], frog_texture02, 21,
 ,[Offset], frog_texture03, 22,
 ,[Offset], arm_t_c, 23,
 ,[Offset], DoNotOpen_Box, 24,
 ,[Offset], Silver Metal Screws_Temp, 25,
 ,[Offset], rubberband_temp, 26,
 ,[Offset], generic_Metal, 27,
 ,[Offset], rails, 28,
 ,[Offset], Plastic_outlane, 29,
 ,[Offset], ClearPlasticPost_01, 30,
 ,[Offset], Red_Plastic_Post, 31,
 ,[Offset], Playfield_Upper, 32,
 ,[Offset], Playfield_Lower, 33,
 ,[Offset], Plunger, 34,
 ,[Offset], HarleyBumperBody, 35,
 ,[Offset], Rubber Post_Temp, 36,
 ,[Offset], Bumper_Sensors, 37,
 ,[Offset], Bumper_Hamer, 38,
 ,[Offset], frog_targets, 39,
 ,[Offset], Harley_Spinner, 40,
 ,[Offset], Harley_Gate, 41,
 ,[Offset], puppet_t_c, 42,
 ,[Offset], blue_boogieman, 43,
 ,[Offset], frontofcabinet, 44,
 ,[Offset], popout, 45,
 ,[Offset], DeadHead_Base, 46,
 ,[Offset], DeadHead_Candle, 47,
 ,[Offset], DeadHeads, 48,
 ,[Offset], Ramp_Sticker, 49,
 ,[Offset], bulb1, 50,
 ,[Offset], Plastic_Ramp_02, 51,
 ,[Offset], red_target, 52,
 ,[Offset], slingshot, 53,
 ,[Offset], Spot_Light, 54,
 ,[Offset], ramp, 55,
 ,[Offset], ramp_plastics, 56,
 ,[Offset], P_Ramp1, 57,
 ,[Offset], P_Ramp2, 58,
 ,[Offset], pop_bumper, 59,
 ,[Offset], Metal_Walls, 60,
 ,[Offset], Clear_Plastics, 61,
 ,[Offset], Flipper, 62,
 ,[Offset], Scoreboard, 63,
 ,[Offset], Scoreboard_Back, 64,
 ,[Offset], flipper_t_c, 65,
 ,[Offset], flipper_t_c_n, 66,
 ,[Offset], vertabre_t_c, 67,
 ,[Offset], vertabre_t_c_n, 68,
RSID_TSCAREDSTIFF_MODELS, 3130,,,
 ,[Offset], Apron, 0,
 ,[Offset], Backglass, 1,
 ,[Offset], Cabinet, 2,
 ,[Offset], Cabinet_Metal, 3,
 ,[Offset], Coffin, 4,
 ,[Offset], Coffin_Base, 5,
 ,[Offset], Coffin_Gate, 6,
 ,[Offset], Crate, 7,
 ,[Offset], Flipper_Lane_Plastics, 8,
 ,[Offset], Flipper_Left, 9,
 ,[Offset], Flipper_Right, 10,
 ,[Offset], Light_Bulbs, 11,
 ,[Offset], Light_Towers, 12,
 ,[Offset], Metal_Pieces, 13,
 ,[Offset], Metal_Switch, 14,
 ,[Offset], Metal_Walls, 15,
 ,[Offset], Plastic_Pieces, 16,
 ,[Offset], Plastic_Posts, 17,
 ,[Offset], Playfield, 18,
 ,[Offset], Plunger, 19,
 ,[Offset], PopBumpers, 20,
 ,[Offset], Ramp_Left, 21,
 ,[Offset], Ramp_Skull, 22,
 ,[Offset], Rubber_Pieces, 23,
 ,[Offset], Slingshot_Left, 24,
 ,[Offset], Slingshot_Right, 25,
 ,[Offset], Wire, 26,
 ,[Offset], Wooden_Rails, 27,
 ,[Offset], Crate_Gate, 28,
 ,[Offset], Frog_A, 29,
 ,[Offset], Frog_B, 30,
 ,[Offset], Frog_C, 31,
 ,[Offset], Metal_Target_A, 32,
 ,[Offset], Metal_Target_B, 33,
 ,[Offset], Metal_Target_C, 34,
 ,[Offset], Red_Target, 35,
 ,[Offset], Bumper_A, 36,
 ,[Offset], One_Way_Gate_A, 37,
 ,[Offset], One_Way_Gate_B, 38,
 ,[Offset], One_Way_Gate_C, 39,
 ,[Offset], One_Way_Gate_D, 40,
 ,[Offset], One_Way_Gate_E, 41,
 ,[Offset], One_Way_Gate_F, 42,
 ,[Offset], Diverter, 43,
 ,[Offset], Slingshot_C, 44,
 ,[Offset], Puppets, 45,
 ,[Offset], Light_Cutouts, 46,
 ,[Offset], Alpha_Planes, 47,
 ,[Offset], Alpha_Planes_B, 48,
 ,[Offset], Spider, 49,
 ,[Offset], Bulb_Flashers, 50,
 ,[Offset], Backglass_Alpha, 51,
 ,[Offset], Skull_Rock, 52,
 ,[Offset], Backglass_Lights, 53,
 ,[Offset], Ramp_Sticker, 54,
 ,[Offset], Skull_Base, 55,
 ,[Offset], Skull_Candles, 56,
 ,[Offset], Skull, 57,
 ,[Offset], Crate_Lamps, 58,
 ,[Offset], Skull_Ramp_Bones, 59,
 ,[Offset], Slingshot_Left_Extended, 60,
 ,[Offset], Slingshot_Right_Extended, 61,
 ,[Offset], Slingshot_C_Extended, 62,
RSID_TSCAREDSTIFF_MODELS_LODS, 3131,,,
 ,[Offset], Apron, 0,
 ,[Offset], Backglass, 1,
 ,[Offset], Cabinet, 2,
 ,[Offset], Cabinet_Metal, 3,
 ,[Offset], Coffin, 4,
 ,[Offset], Coffin_Base, 5,
 ,[Offset], Coffin_Gate, 6,
 ,[Offset], Crate, 7,
 ,[Offset], Flipper_Lane_Plastics, 8,
 ,[Offset], Flipper_Left, 9,
 ,[Offset], Flipper_Right, 10,
 ,[Offset], Light_Bulbs, 11,
 ,[Offset], Light_Towers, 12,
 ,[Offset], Metal_Pieces, 13,
 ,[Offset], Metal_Switch, 14,
 ,[Offset], Metal_Walls, 15,
 ,[Offset], Plastic_Pieces, 16,
 ,[Offset], Plastic_Posts, 17,
 ,[Offset], Playfield, 18,
 ,[Offset], Plunger, 19,
 ,[Offset], PopBumpers, 20,
 ,[Offset], Ramp_Left, 21,
 ,[Offset], Ramp_Skull, 22,
 ,[Offset], Rubber_Pieces, 23,
 ,[Offset], Slingshot_Left, 24,
 ,[Offset], Slingshot_Right, 25,
 ,[Offset], Wire, 26,
 ,[Offset], Wooden_Rails, 27,
 ,[Offset], Crate_Gate, 28,
 ,[Offset], Frog_A, 29,
 ,[Offset], Frog_B, 30,
 ,[Offset], Frog_C, 31,
 ,[Offset], Metal_Target_A, 32,
 ,[Offset], Metal_Target_B, 33,
 ,[Offset], Metal_Target_C, 34,
 ,[Offset], Red_Target, 35,
 ,[Offset], Bumper_A, 36,
 ,[Offset], One_Way_Gate_A, 37,
 ,[Offset], One_Way_Gate_B, 38,
 ,[Offset], One_Way_Gate_C, 39,
 ,[Offset], One_Way_Gate_D, 40,
 ,[Offset], One_Way_Gate_E, 41,
 ,[Offset], One_Way_Gate_F, 42,
 ,[Offset], Diverter, 43,
 ,[Offset], Slingshot_C, 44,
 ,[Offset], Puppets, 45,
 ,[Offset], Light_Cutouts, 46,
 ,[Offset], Alpha_Planes, 47,
 ,[Offset], Alpha_Planes_B, 48,
 ,[Offset], Spider, 49,
 ,[Offset], Bulb_Flashers, 50,
 ,[Offset], Backglass_Alpha, 51,
 ,[Offset], Skull_Rock, 52,
 ,[Offset], Backglass_Lights, 53,
 ,[Offset], Ramp_Sticker, 54,
 ,[Offset], Skull_Base, 55,
 ,[Offset], Skull_Candles, 56,
 ,[Offset], Skull, 57,
 ,[Offset], Crate_Lamps, 58,
 ,[Offset], Skull_Ramp_Bones, 59,
 ,[Offset], Slingshot_Left_Extended, 60,
 ,[Offset], Slingshot_Right_Extended, 61,
 ,[Offset], Slingshot_C_Extended, 62,
RSID_TSCAREDSTIFF_COLLISION, 3132,,,
 ,[Offset], Back_Right_Rubber, 0,
 ,[Offset], Back_Rubber, 1,
 ,[Offset], Back_Wall, 2,
 ,[Offset], Ball_Drain, 3,
 ,[Offset], Front_Left_Rubber, 4,
 ,[Offset], Front_Right_Rubber, 5,
 ,[Offset], Left_Fliipper_Lane, 6,
 ,[Offset], Left_Inner_Wall, 7,
 ,[Offset], Left_Metal_Wall, 8,
 ,[Offset], Lower_Metal_Wall, 9,
 ,[Offset], Mid_Rubber, 10,
 ,[Offset], PLayfield, 11,
 ,[Offset], Plunger_Moving, 12,
 ,[Offset], Plunger_Rest, 13,
 ,[Offset], Right_Flipper_Lane, 14,
 ,[Offset], Right_Metal_Wall, 15,
 ,[Offset], Right_Slingshot, 16,
 ,[Offset], Slingshot_Left, 17,
 ,[Offset], Skull_Ramp, 18,
 ,[Offset], Left_Ramp, 19,
 ,[Offset], Crate_Trap, 20,
 ,[Offset], Diverter_Trap, 21,
 ,[Offset], Scoop_Trap, 22,
 ,[Offset], Spider_Trap, 23,
 ,[Offset], Flipper_Left_Back, 24,
 ,[Offset], Flipper_Left_Front, 25,
 ,[Offset], Flipper_Right_Back, 26,
 ,[Offset], Flipper_Right_Front, 27,
 ,[Offset], Slingshot_Left_Front, 28,
 ,[Offset], Slingshot_Right_Front, 29,
 ,[Offset], Crate_Gate, 30,
 ,[Offset], Bumper, 31,
 ,[Offset], Frog_Target, 32,
 ,[Offset], Red_Target, 33,
 ,[Offset], Eddie_Switch, 34,
 ,[Offset], Frog_B_Target, 35,
 ,[Offset], Frog_C_Target, 36,
 ,[Offset], Opto_Left_Ramp, 37,
 ,[Offset], Opto_Left_Ramp_Back, 38,
 ,[Offset], Opto_Skull_Ramp, 39,
 ,[Offset], Opto_Skull_Ramp_Back, 40,
 ,[Offset], Diverter, 41,
 ,[Offset], One_Way_Gate_Back, 42,
 ,[Offset], One_Way_Gate_Front, 43,
 ,[Offset], One_Way_Gate_F_Back, 44,
 ,[Offset], One_Way_Gate_F_Front, 45,
 ,[Offset], Slingshot_C, 46,
 ,[Offset], Gate, 47,
 ,[Offset], Coffin_Eject, 48,
 ,[Offset], Coffin_Eject_Trap, 49,
 ,[Offset], Coffin_Inside, 50,
 ,[Offset], Rubber_Switch, 51,
RSID_TSCAREDSTIFF_PLACEMENT, 3133,,,
 ,[Offset], Placement, 0,
 ,[Offset], Lights, 1,
RSID_TSCAREDSTIFF_EMUROM, 3134,,,
 ,[Offset], ss_15, 0,
 ,[Offset], ss_15, 1,
 ,[Offset], ss_default, 2,
 ,[Offset], ss_default, 3,
RSID_TSCAREDSTIFF_SOUNDS_START, 3135,,,
RSID_TSCAREDSTIFF_EMU_SOUNDS, 3136,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP, 1,
 ,[Offset], S0003-LP1, 2,
 ,[Offset], S0003-LP2, 3,
 ,[Offset], S0004-LP, 4,
 ,[Offset], S0005-LP, 5,
 ,[Offset], S0006-LP1, 6,
 ,[Offset], S0007-LP, 7,
 ,[Offset], S0008-LP1, 8,
 ,[Offset], S0009-LP, 9,
 ,[Offset], S000B-LP, 10,
 ,[Offset], S000C-LP, 11,
 ,[Offset], S000D-LP, 12,
 ,[Offset], S000E-LP, 13,
 ,[Offset], S000F-LP, 14,
 ,[Offset], S0010-LP, 15,
 ,[Offset], S0011-LP, 16,
 ,[Offset], S0012-LP, 17,
 ,[Offset], S0013-LP, 18,
 ,[Offset], S0014-LP, 19,
 ,[Offset], S0015-LP, 20,
 ,[Offset], S0016-LP, 21,
 ,[Offset], S0017-LP, 22,
 ,[Offset], S0018-LP, 23,
 ,[Offset], S0019-LP, 24,
 ,[Offset], S001A-LP, 25,
 ,[Offset], S007D_C5, 26,
 ,[Offset], S0097_C5, 27,
 ,[Offset], S00A7_C5, 28,
 ,[Offset], S00A9_C5, 29,
 ,[Offset], S00AA_C5, 30,
 ,[Offset], S00AB_C5, 31,
 ,[Offset], S00AC_C5, 32,
 ,[Offset], S00AD_C1, 33,
 ,[Offset], S00AE_C5, 34,
 ,[Offset], S00AF_C5, 35,
 ,[Offset], S00B0_C5, 36,
 ,[Offset], S00B1_C5, 37,
 ,[Offset], S00B3_C5, 38,
 ,[Offset], S00B4_C5, 39,
 ,[Offset], S00B6_C5, 40,
 ,[Offset], S00B7_C5, 41,
 ,[Offset], S00C6_C4, 42,
 ,[Offset], S00C9_C4, 43,
 ,[Offset], S00CD_C1, 44,
 ,[Offset], S00CE_C5, 45,
 ,[Offset], S00CF_C1, 46,
 ,[Offset], S00D0_C4, 47,
 ,[Offset], S00D1_C5, 48,
 ,[Offset], S00D3_C5, 49,
 ,[Offset], S00D7_C4, 50,
 ,[Offset], S00D9_C4, 51,
 ,[Offset], S00DA_C4, 52,
 ,[Offset], S00DB_C4, 53,
 ,[Offset], S00DD_C5, 54,
 ,[Offset], S00DE_C5, 55,
 ,[Offset], S00DF_C5, 56,
 ,[Offset], S00E0_C5, 57,
 ,[Offset], S00E1_C3, 58,
 ,[Offset], S00E2_C3, 59,
 ,[Offset], S00E3_C4, 60,
 ,[Offset], S00E4_C5, 61,
 ,[Offset], S00E5_C5, 62,
 ,[Offset], S00E7_C-1, 63,
 ,[Offset], S00E8_C5, 64,
 ,[Offset], S00ED_C5, 65,
 ,[Offset], S00EF_C5, 66,
 ,[Offset], S00F0_C4, 67,
 ,[Offset], S00F6_C5, 68,
 ,[Offset], S00F7_C5, 69,
 ,[Offset], S00F8_C5, 70,
 ,[Offset], S00F9_C5, 71,
 ,[Offset], S00FD_C5, 72,
 ,[Offset], S00FE_C5, 73,
 ,[Offset], S00FF_C5, 74,
 ,[Offset], S0100_C3, 75,
 ,[Offset], S0101_C3, 76,
 ,[Offset], S0103_C4, 77,
 ,[Offset], S0108_C4, 78,
 ,[Offset], S0109_C4, 79,
 ,[Offset], S010F_C5, 80,
 ,[Offset], S0110_C5, 81,
 ,[Offset], S0113_C5, 82,
 ,[Offset], S0114_C4, 83,
 ,[Offset], S0115_C5, 84,
 ,[Offset], S0116_C5, 85,
 ,[Offset], S0117_C5, 86,
 ,[Offset], S0118_C5, 87,
 ,[Offset], S011D_C5, 88,
 ,[Offset], S011E_C5, 89,
 ,[Offset], S011F_C5, 90,
 ,[Offset], S0121_C5, 91,
 ,[Offset], S0122_C5, 92,
 ,[Offset], S0123_C5, 93,
 ,[Offset], S0124_C5, 94,
 ,[Offset], S0125_C5, 95,
 ,[Offset], S0127_C5, 96,
 ,[Offset], S0128_C5, 97,
 ,[Offset], S0129_C5, 98,
 ,[Offset], S012A_C5, 99,
 ,[Offset], S012B_C5, 100,
 ,[Offset], S012C_C5, 101,
 ,[Offset], S012F_C5, 102,
 ,[Offset], S0130_C5, 103,
 ,[Offset], S0132_C5, 104,
 ,[Offset], S0136_C5, 105,
 ,[Offset], S0137_C4, 106,
 ,[Offset], S0138_C5, 107,
 ,[Offset], S0139_C4, 108,
 ,[Offset], S013A_C5, 109,
 ,[Offset], S013B_C4, 110,
 ,[Offset], S013C_C5, 111,
 ,[Offset], S013D_C4, 112,
 ,[Offset], S0140_C5, 113,
 ,[Offset], S0141_C4, 114,
 ,[Offset], S0142_C5, 115,
 ,[Offset], S0143_C4, 116,
 ,[Offset], S0146_C5, 117,
 ,[Offset], S0147_C4, 118,
 ,[Offset], S0149_C4, 119,
 ,[Offset], S014A_C5, 120,
 ,[Offset], S0157_C4, 121,
 ,[Offset], S015E_C5, 122,
 ,[Offset], S015F_C4, 123,
 ,[Offset], S0162_C5, 124,
 ,[Offset], S0164_C5, 125,
 ,[Offset], S0165_C4, 126,
 ,[Offset], S0166_C5, 127,
 ,[Offset], S0167_C4, 128,
 ,[Offset], S0168_C5, 129,
 ,[Offset], S016A_C4, 130,
 ,[Offset], S016C_C4, 131,
 ,[Offset], S016D_C4, 132,
 ,[Offset], S016E_C4, 133,
 ,[Offset], S016F_C5, 134,
 ,[Offset], S0170_C5, 135,
 ,[Offset], S0171_C5, 136,
 ,[Offset], S0173_C5, 137,
 ,[Offset], S0174_C5, 138,
 ,[Offset], S0175_C4, 139,
 ,[Offset], S0176_C5, 140,
 ,[Offset], S0177_C4, 141,
 ,[Offset], S0178_C5, 142,
 ,[Offset], S0179_C5, 143,
 ,[Offset], S017A_C5, 144,
 ,[Offset], S017B_C5, 145,
 ,[Offset], S0180_C4, 146,
 ,[Offset], S0181_C5, 147,
 ,[Offset], S0182_C4, 148,
 ,[Offset], S0183_C4, 149,
 ,[Offset], S0184_C5, 150,
 ,[Offset], S0185_C4, 151,
 ,[Offset], S0186_C5, 152,
 ,[Offset], S0188_C5, 153,
 ,[Offset], S0189_C5, 154,
 ,[Offset], S018A_C5, 155,
 ,[Offset], S018B_C5, 156,
 ,[Offset], S018C_C5, 157,
 ,[Offset], S018D_C5, 158,
 ,[Offset], S018E_C5, 159,
 ,[Offset], S03D4_C-1, 160,
 ,[Offset], S03D5_C-1, 161,
 ,[Offset], S03D6_C5, 162,
 ,[Offset], S03D7_C5, 163,
 ,[Offset], S03D8_C2, 164,
 ,[Offset], S03D9_C-1, 165,
 ,[Offset], S03DA_C-1, 166,
 ,[Offset], S03DB_C5, 167,
 ,[Offset], S03DC_C5, 168,
 ,[Offset], S03DD_C5, 169,
 ,[Offset], S03F1_C6, 170,
 ,[Offset], S03F4_C6, 171,
 ,[Offset], S03F6_C6, 172,
 ,[Offset], S07D0_C6, 173,
 ,[Offset], S07E3_C6, 174,
 ,[Offset], S07E4_C6, 175,
 ,[Offset], S07E7_C6, 176,
 ,[Offset], S07E8_C6, 177,
 ,[Offset], S07EA_C6, 178,
 ,[Offset], S07EE_C6, 179,
 ,[Offset], S07F0_C6, 180,
 ,[Offset], S07F1_C6, 181,
 ,[Offset], S07F2_C6, 182,
 ,[Offset], S07F5_C6, 183,
 ,[Offset], S07F7_C6, 184,
 ,[Offset], S07F9_C6, 185,
 ,[Offset], S07FA_C6, 186,
 ,[Offset], S07FE_C6, 187,
 ,[Offset], S07FF_C6, 188,
 ,[Offset], S0802_C6, 189,
 ,[Offset], S0803_C6, 190,
 ,[Offset], S0804_C6, 191,
 ,[Offset], S0805_C6, 192,
 ,[Offset], S0809_C6, 193,
 ,[Offset], S080E_C6, 194,
 ,[Offset], S081A_C6, 195,
 ,[Offset], S081B_C6, 196,
 ,[Offset], S0821_C6, 197,
 ,[Offset], S0823_C6, 198,
 ,[Offset], S0824_C6, 199,
 ,[Offset], S0826_C6, 200,
 ,[Offset], S0827_C6, 201,
 ,[Offset], S0828_C6, 202,
 ,[Offset], S082A_C6, 203,
 ,[Offset], S082B_C6, 204,
 ,[Offset], S082C_C6, 205,
 ,[Offset], S082D_C6, 206,
 ,[Offset], S082E_C6, 207,
 ,[Offset], S082F_C6, 208,
 ,[Offset], S0830_C6, 209,
 ,[Offset], S0832_C6, 210,
 ,[Offset], S0839_C3, 211,
 ,[Offset], S083C_C6, 212,
 ,[Offset], S083E_C6, 213,
 ,[Offset], S083F_C6, 214,
 ,[Offset], S0840_C6, 215,
 ,[Offset], S0841_C6, 216,
 ,[Offset], S0842_C6, 217,
 ,[Offset], S0843_C6, 218,
 ,[Offset], S0844_C6, 219,
 ,[Offset], S0845_C6, 220,
 ,[Offset], S0846_C6, 221,
 ,[Offset], S0847_C6, 222,
 ,[Offset], S0848_C6, 223,
 ,[Offset], S0849_C6, 224,
 ,[Offset], S084A_C6, 225,
 ,[Offset], S084B_C6, 226,
 ,[Offset], S084C_C6, 227,
 ,[Offset], S084E_C6, 228,
 ,[Offset], S084F_C6, 229,
 ,[Offset], S0850_C6, 230,
 ,[Offset], S0851_C6, 231,
 ,[Offset], S0852_C0, 232,
 ,[Offset], S0853_C6, 233,
 ,[Offset], S0854_C6, 234,
 ,[Offset], S0855_C6, 235,
 ,[Offset], S085E_C6, 236,
 ,[Offset], S0860_C6, 237,
 ,[Offset], S0861_C6, 238,
 ,[Offset], S0862_C6, 239,
 ,[Offset], S0863_C6, 240,
 ,[Offset], S0864_C6, 241,
 ,[Offset], S0865_C6, 242,
 ,[Offset], S0867_C6, 243,
 ,[Offset], S0868_C6, 244,
 ,[Offset], S086A_C6, 245,
 ,[Offset], S086B_C6, 246,
 ,[Offset], S086D_C6, 247,
 ,[Offset], S0873_C6, 248,
 ,[Offset], S0876_C6, 249,
 ,[Offset], S0877_C6, 250,
 ,[Offset], S0879_C6, 251,
 ,[Offset], S087F_C6, 252,
 ,[Offset], S0884_C6, 253,
 ,[Offset], S088C_C6, 254,
 ,[Offset], S088D_C6, 255,
 ,[Offset], S08A9_C6, 256,
 ,[Offset], S08AC_C6, 257,
 ,[Offset], S08B5_C6, 258,
 ,[Offset], S08B8_C6, 259,
 ,[Offset], S08B9_C6, 260,
 ,[Offset], S08BA_C6, 261,
 ,[Offset], S08BB_C6, 262,
 ,[Offset], S08BD_C6, 263,
 ,[Offset], S08BF_C6, 264,
 ,[Offset], S08C2_C6, 265,
 ,[Offset], S08C4_C6, 266,
 ,[Offset], S08C6_C6, 267,
 ,[Offset], S08D2_C6, 268,
 ,[Offset], S08D3_C6, 269,
 ,[Offset], S08D5_C6, 270,
 ,[Offset], S08D6_C6, 271,
 ,[Offset], S08D7_C6, 272,
 ,[Offset], S08D8_C6, 273,
 ,[Offset], S08DA_C6, 274,
 ,[Offset], S08FD_C6, 275,
 ,[Offset], S08FE_C6, 276,
 ,[Offset], S08FF_C6, 277,
 ,[Offset], S0900_C6, 278,
 ,[Offset], S090D_C6, 279,
 ,[Offset], S0913_C6, 280,
 ,[Offset], S091B_C6, 281,
 ,[Offset], S091C_C6, 282,
 ,[Offset], S091D_C6, 283,
 ,[Offset], S091E_C6, 284,
 ,[Offset], S0922_C6, 285,
 ,[Offset], S0923_C6, 286,
 ,[Offset], S0924_C6, 287,
 ,[Offset], S0926_C6, 288,
 ,[Offset], S092C_C6, 289,
 ,[Offset], S092E_C6, 290,
 ,[Offset], S0931_C6, 291,
 ,[Offset], S0932_C6, 292,
 ,[Offset], S0938_C6, 293,
 ,[Offset], S0939_C6, 294,
 ,[Offset], S093A_C6, 295,
 ,[Offset], S093B_C6, 296,
 ,[Offset], S093C_C6, 297,
 ,[Offset], S093D_C6, 298,
 ,[Offset], S093E_C6, 299,
 ,[Offset], S093F_C6, 300,
 ,[Offset], S0940_C6, 301,
 ,[Offset], S0941_C6, 302,
 ,[Offset], S0942_C6, 303,
 ,[Offset], S0943_C6, 304,
 ,[Offset], S0944_C6, 305,
 ,[Offset], S0946_C6, 306,
 ,[Offset], S0948_C6, 307,
 ,[Offset], S0949_C6, 308,
 ,[Offset], S094A_C6, 309,
 ,[Offset], S0950_C6, 310,
 ,[Offset], S0952_C6, 311,
 ,[Offset], S0953_C6, 312,
 ,[Offset], S0955_C6, 313,
 ,[Offset], S0956_C6, 314,
 ,[Offset], S0957_C6, 315,
 ,[Offset], S0958_C6, 316,
 ,[Offset], S0959_C6, 317,
 ,[Offset], S095C_C6, 318,
 ,[Offset], S0961_C6, 319,
 ,[Offset], S0BBA_C6, 320,
 ,[Offset], S0BBB_C6, 321,
 ,[Offset], S0BBD_C6, 322,
 ,[Offset], S0BBE_C6, 323,
 ,[Offset], S0BC0_C6, 324,
 ,[Offset], S0BC1_C6, 325,
 ,[Offset], S0BC2_C6, 326,
 ,[Offset], S0BC3_C6, 327,
 ,[Offset], S0BC5_C6, 328,
 ,[Offset], S0BCA_C6, 329,
 ,[Offset], S0BCB_C6, 330,
 ,[Offset], S0BCC_C6, 331,
 ,[Offset], S0BCD_C6, 332,
 ,[Offset], S0BCE_C6, 333,
 ,[Offset], S0BCF_C6, 334,
 ,[Offset], S0BD0_C6, 335,
 ,[Offset], S0BD1_C6, 336,
 ,[Offset], S0BD2_C6, 337,
 ,[Offset], S0BD3_C6, 338,
 ,[Offset], S0BD4_C6, 339,
 ,[Offset], S0BD8_C6, 340,
 ,[Offset], S0BD9_C6, 341,
 ,[Offset], S0BDA_C6, 342,
 ,[Offset], S0BDC_C6, 343,
 ,[Offset], S0BDD_C6, 344,
 ,[Offset], S0BDF_C6, 345,
 ,[Offset], S0BE0_C6, 346,
 ,[Offset], S0BE7_C6, 347,
 ,[Offset], S0BE8_C6, 348,
 ,[Offset], S0BE9_C6, 349,
 ,[Offset], S0BEA_C6, 350,
 ,[Offset], S0BEB_C6, 351,
 ,[Offset], S0BEE_C6, 352,
 ,[Offset], S0BEF_C6, 353,
 ,[Offset], S0BF0_C6, 354,
 ,[Offset], S0BF1_C6, 355,
 ,[Offset], S0BF2_C6, 356,
 ,[Offset], S0BF3_C6, 357,
 ,[Offset], S0BF6_C6, 358,
 ,[Offset], S0BF7_C6, 359,
 ,[Offset], S0BF8_C6, 360,
 ,[Offset], S0BF9_C6, 361,
 ,[Offset], S0BFE_C6, 362,
 ,[Offset], S0C00_C6, 363,
 ,[Offset], S0C01_C6, 364,
 ,[Offset], S0C04_C6, 365,
 ,[Offset], S0C06_C6, 366,
 ,[Offset], S0C07_C6, 367,
 ,[Offset], S0C08_C6, 368,
 ,[Offset], S0C0A_C6, 369,
 ,[Offset], S0C0B_C6, 370,
 ,[Offset], S0C0C_C6, 371,
 ,[Offset], S0C10_C6, 372,
 ,[Offset], S0C11_C6, 373,
 ,[Offset], S0C14_C6, 374,
 ,[Offset], S0C15_C6, 375,
 ,[Offset], S0C17_C6, 376,
 ,[Offset], S0C18_C6, 377,
 ,[Offset], S0C19_C6, 378,
 ,[Offset], S0C1C_C6, 379,
 ,[Offset], S0C1D_C6, 380,
 ,[Offset], S0C1E_C6, 381,
 ,[Offset], S0C20_C6, 382,
 ,[Offset], S0C21_C6, 383,
 ,[Offset], S0C22_C6, 384,
 ,[Offset], S0C2E_C6, 385,
 ,[Offset], S0C33_C6, 386,
 ,[Offset], S0C34_C6, 387,
 ,[Offset], S0C3A_C6, 388,
 ,[Offset], S0C3B_C6, 389,
 ,[Offset], S0C3C_C6, 390,
 ,[Offset], S0C3D_C6, 391,
RSID_TSCAREDSTIFF_MECH_SOUNDS, 3137,,,
 ,[Offset], coffin_close, 0,
 ,[Offset], coffin_open, 1,
 ,[Offset], crate_entry, 2,
 ,[Offset], spider_hole, 3,
 ,[Offset], spider_eject, 4,
RSID_TSCAREDSTIFF_SOUNDS_END, 3138,,,
RSID_TSCAREDSTIFF_SAMPLES, 3139,,,
RSID_TSCAREDSTIFF_END, 3140,,,
