RSID_TPINBOT_START, 2900,,,
 ,[Offset], flyer_1, 0,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\PBTPinBot, 1,
 ,[Offset], <PERSON>n<PERSON>ot\InstructionsENG, 2,
 ,[Offset], <PERSON>n<PERSON><PERSON>\InstructionsFR, 3,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\InstructionsITAL, 4,
 ,[Offset], Pin<PERSON>ot\InstructionsGERM, 5,
 ,[Offset], <PERSON>n<PERSON>ot\InstructionsSPAN, 6,
 ,[Offset], <PERSON>n<PERSON>ot\InstructionsPORT, 7,
 ,[Offset], PinBot\InstructionsDUTCH, 8,
 ,[Offset], PinBot\InstructionsDUTCH, 9,
 ,[Offset], Pin<PERSON>ot\Pro_TipsENG, 10,
 ,[Offset], PinBot\Pro_TipsFR, 11,
 ,[Offset], Pin<PERSON>ot\Pro_TipsITAL, 12,
 ,[Offset], PinBot\Pro_TipsGERM, 13,
 ,[Offset], <PERSON>n<PERSON><PERSON>\Pro_TipsSPAN, 14,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\Pro_TipsENG, 15,
 ,[Offset], <PERSON><PERSON><PERSON><PERSON>\Pro_TipsENG, 16,
RSID_TPINBOT_LIGHTS, 2901,,,
RSID_TPINBOT_CAMERAS, 2902,,,
RSID_TPINBOT_LAMP_TEXTURES, 2903,,,
 ,[Offset], PinBotCameras, 0,
 ,[Offset], PinBotCameras, 1,
 ,[Offset], PinBotCameras, 2,
 ,[Offset], PinBotCameras, 3,
 ,[Offset], PinBotCameras, 4,
 ,[Offset], PinBotCameras, 5,
 ,[Offset], PinBotCameras, 6,
 ,[Offset], PinBotCameras, 7,
 ,[Offset], PinBotCameras, 8,
 ,[Offset], PinBotCameras, 9,
 ,[Offset], PinBotCameras, 10,
 ,[Offset], PinBotCameras, 11,
 ,[Offset], PinBotCameras, 12,
 ,[Offset], PinBotCameras, 13,
 ,[Offset], PinBotCameras, 14,
 ,[Offset], PinBotCameras, 15,
 ,[Offset], L09_off, 16,
 ,[Offset], L09_on, 17,
 ,[Offset], L10_off, 18,
 ,[Offset], L10_on, 19,
 ,[Offset], L11_off, 20,
 ,[Offset], L11_on, 21,
 ,[Offset], L12_off, 22,
 ,[Offset], L12_on, 23,
 ,[Offset], L13_off, 24,
 ,[Offset], L13_on, 25,
 ,[Offset], L14_off, 26,
 ,[Offset], L14_on, 27,
 ,[Offset], L15_off, 28,
 ,[Offset], L15_on, 29,
 ,[Offset], L16_off, 30,
 ,[Offset], L16_on, 31,
 ,[Offset], L17_off, 32,
 ,[Offset], L17_on, 33,
 ,[Offset], L18_off, 34,
 ,[Offset], L18_on, 35,
 ,[Offset], L19_off, 36,
 ,[Offset], L19_on, 37,
 ,[Offset], L20_off, 38,
 ,[Offset], L20_on, 39,
 ,[Offset], L21_off, 40,
 ,[Offset], L21_on, 41,
 ,[Offset], L22_off, 42,
 ,[Offset], L22_on, 43,
 ,[Offset], L23_off, 44,
 ,[Offset], L23_on, 45,
 ,[Offset], L24_off, 46,
 ,[Offset], L24_on, 47,
 ,[Offset], L25_off, 48,
 ,[Offset], L25_on, 49,
 ,[Offset], L26_off, 50,
 ,[Offset], L26_on, 51,
 ,[Offset], L27_off, 52,
 ,[Offset], L27_on, 53,
 ,[Offset], L28_off, 54,
 ,[Offset], L28_on, 55,
 ,[Offset], L29_off, 56,
 ,[Offset], L29_on, 57,
 ,[Offset], L30_off, 58,
 ,[Offset], L30_on, 59,
 ,[Offset], L31_off, 60,
 ,[Offset], L31_on, 61,
 ,[Offset], L32_off, 62,
 ,[Offset], L32_on, 63,
 ,[Offset], L33_off, 64,
 ,[Offset], L33_on, 65,
 ,[Offset], L34_off, 66,
 ,[Offset], L34_on, 67,
 ,[Offset], L35_off, 68,
 ,[Offset], L35_on, 69,
 ,[Offset], L36_off, 70,
 ,[Offset], L36_on, 71,
 ,[Offset], L37_off, 72,
 ,[Offset], L37_on, 73,
 ,[Offset], L38_off, 74,
 ,[Offset], L38_on, 75,
 ,[Offset], L39_off, 76,
 ,[Offset], L39_on, 77,
 ,[Offset], L40_off, 78,
 ,[Offset], L40_on, 79,
 ,[Offset], L41_off, 80,
 ,[Offset], L41_on, 81,
 ,[Offset], L42_off, 82,
 ,[Offset], L42_on, 83,
 ,[Offset], L43_off, 84,
 ,[Offset], L43_on, 85,
 ,[Offset], L44_off, 86,
 ,[Offset], L44_on, 87,
 ,[Offset], L45_off, 88,
 ,[Offset], L45_on, 89,
 ,[Offset], L46_off, 90,
 ,[Offset], L46_on, 91,
 ,[Offset], L47_off, 92,
 ,[Offset], L47_on, 93,
 ,[Offset], L48_off, 94,
 ,[Offset], L48_on, 95,
 ,[Offset], L49_off, 96,
 ,[Offset], L49_on, 97,
 ,[Offset], L50_off, 98,
 ,[Offset], L50_on, 99,
 ,[Offset], L51_off, 100,
 ,[Offset], L51_on, 101,
 ,[Offset], L52_off, 102,
 ,[Offset], L52_on, 103,
 ,[Offset], L53_off, 104,
 ,[Offset], L53_on, 105,
 ,[Offset], L54_off, 106,
 ,[Offset], L54_on, 107,
 ,[Offset], L55_off, 108,
 ,[Offset], L55_on, 109,
 ,[Offset], L56_off, 110,
 ,[Offset], L56_on, 111,
 ,[Offset], L57_off, 112,
 ,[Offset], L57_on, 113,
 ,[Offset], L58_off, 114,
 ,[Offset], L58_on, 115,
 ,[Offset], L58_on, 116,
 ,[Offset], L58_on, 117,
 ,[Offset], L60_off, 118,
 ,[Offset], L60_on, 119,
 ,[Offset], L61_off, 120,
 ,[Offset], L61_on, 121,
 ,[Offset], L62_off, 122,
 ,[Offset], L62_on, 123,
 ,[Offset], L63_off, 124,
 ,[Offset], L63_on, 125,
 ,[Offset], L64_off, 126,
 ,[Offset], L64_on, 127,
 ,[Offset], L65_off, 128,
 ,[Offset], L65_on, 129,
 ,[Offset], L66_off, 130,
 ,[Offset], L66_on, 131,
 ,[Offset], L67_off, 132,
 ,[Offset], L67_on, 133,
 ,[Offset], L68_off, 134,
 ,[Offset], L68_on, 135,
 ,[Offset], L69_off, 136,
 ,[Offset], L69_on, 137,
 ,[Offset], L70_off, 138,
 ,[Offset], L70_on, 139,
 ,[Offset], L71_off, 140,
 ,[Offset], L71_on, 141,
 ,[Offset], L72_off, 142,
 ,[Offset], L72_on, 143,
 ,[Offset], L73_off, 144,
 ,[Offset], L73_on, 145,
 ,[Offset], L74_off, 146,
 ,[Offset], L74_on, 147,
 ,[Offset], L75_off, 148,
 ,[Offset], L75_on, 149,
 ,[Offset], L76_off, 150,
 ,[Offset], L76_on, 151,
 ,[Offset], L77_off, 152,
 ,[Offset], L77_on, 153,
 ,[Offset], L78_off, 154,
 ,[Offset], L78_on, 155,
 ,[Offset], L79_off, 156,
 ,[Offset], L79_on, 157,
 ,[Offset], L80_off, 158,
 ,[Offset], L80_on, 159,
 ,[Offset], L81_off, 160,
 ,[Offset], L81_on, 161,
 ,[Offset], L82_off, 162,
 ,[Offset], L82_on, 163,
 ,[Offset], L83_off, 164,
 ,[Offset], L83_on, 165,
 ,[Offset], L84_off, 166,
 ,[Offset], L84_on, 167,
 ,[Offset], L85_off, 168,
 ,[Offset], L85_on, 169,
 ,[Offset], L8c_off, 170,
 ,[Offset], L8c_on, 171,
 ,[Offset], L2c_off, 172,
 ,[Offset], L2c_on, 173,
 ,[Offset], L5c_off, 174,
 ,[Offset], L5c_on, 175,
 ,[Offset], L6c_off, 176,
 ,[Offset], L6c_on, 177,
 ,[Offset], L7c_off, 178,
 ,[Offset], L7c_on, 179,
RSID_TPINBOT_TEXTURES, 2904,,,
 ,[Offset], PinBot_LED_Border, 0,
 ,[Offset], blue_plasticposts, 1,
 ,[Offset], blue_post, 2,
 ,[Offset], Bumper_Side01, 3,
 ,[Offset], Bumper_Space01, 4,
 ,[Offset], BumperTopA01, 5,
 ,[Offset], CoinSlots, 6,
 ,[Offset], flasher, 7,
 ,[Offset], flipper, 8,
 ,[Offset], Generic_Metal, 9,
 ,[Offset], metal front, 10,
 ,[Offset], partB01, 11,
 ,[Offset], partC01, 12,
 ,[Offset], Pinbot_playfield_bottom, 13,
 ,[Offset], Pinbot_playfield_top, 14,
 ,[Offset], plastic_clear01, 15,
 ,[Offset], plastic_clear02, 16,
 ,[Offset], Plunger, 17,
 ,[Offset], PopBumper_on, 18,
 ,[Offset], PopBumper_off, 19,
 ,[Offset], PopBumperBody, 20,
 ,[Offset], ramp_base, 21,
 ,[Offset], RampBlue01, 22,
 ,[Offset], red_plasticposts, 23,
 ,[Offset], red_post, 24,
 ,[Offset], robot_lady01, 25,
 ,[Offset], robot_lady02, 26,
 ,[Offset], RobotTop01, 27,
 ,[Offset], rubberband, 28,
 ,[Offset], rules, 29,
 ,[Offset], Silver_screws, 30,
 ,[Offset], slingshots, 31,
 ,[Offset], small_plastic_post, 32,
 ,[Offset], spiralscores01, 33,
 ,[Offset], targets, 34,
 ,[Offset], TopMask01, 35,
 ,[Offset], TopSpaceShip01, 36,
 ,[Offset], TopSpaceShip02, 37,
 ,[Offset], white_nut, 38,
 ,[Offset], SpaceShuttle01, 39,
 ,[Offset], Buttons_Parts, 40,
 ,[Offset], cabinet02, 41,
 ,[Offset], metal_legs, 42,
 ,[Offset], metal_trim, 43,
 ,[Offset], Plunger_Plate01, 44,
 ,[Offset], postB01 copy, 45,
 ,[Offset], dropB copy, 46,
 ,[Offset], rubberband01, 47,
 ,[Offset], glass_head01, 48,
 ,[Offset], Spiral Ramp01b, 49,
 ,[Offset], spiral_sticker01, 50,
 ,[Offset], wood01, 51,
 ,[Offset], habi_trail, 52,
 ,[Offset], Rails, 53,
 ,[Offset], inside_walls, 54,
 ,[Offset], blue_plastic, 55,
 ,[Offset], wooden_rails, 56,
 ,[Offset], apron, 57,
 ,[Offset], backglass, 58,
 ,[Offset], cabinet01, 59,
 ,[Offset], helmet, 60,
 ,[Offset], Pinbot_playfield, 61,
 ,[Offset], spaceship, 62,
RSID_TPINBOT_MODELS, 2905,,,
 ,[Offset], yellow_target, 0,
 ,[Offset], adv_X_ramp, 1,
 ,[Offset], apron, 2,
 ,[Offset], backglass, 3,
 ,[Offset], black_posts, 4,
 ,[Offset], blue_posts, 5,
 ,[Offset], blue_ramp, 6,
 ,[Offset], blue_target, 7,
 ,[Offset], bride_plastic, 8,
 ,[Offset], bulbs, 9,
 ,[Offset], bumper, 10,
 ,[Offset], buttons, 11,
 ,[Offset], cabinet, 12,
 ,[Offset], cabinet_metal, 13,
 ,[Offset], droptile, 14,
 ,[Offset], flashers, 15,
 ,[Offset], Flipper_Left, 16,
 ,[Offset], Flipper_Right, 17,
 ,[Offset], green_target, 18,
 ,[Offset], left_slingshot, 19,
 ,[Offset], metal_parts, 20,
 ,[Offset], metal_posts, 21,
 ,[Offset], orange_target, 22,
 ,[Offset], pin-bot_targets, 23,
 ,[Offset], pin-bot_visor, 24,
 ,[Offset], plastic1, 25,
 ,[Offset], plastic2, 26,
 ,[Offset], plastic3, 27,
 ,[Offset], playfield, 28,
 ,[Offset], Plunger, 29,
 ,[Offset], pop_bumpers, 30,
 ,[Offset], red_posts, 31,
 ,[Offset], red_target, 32,
 ,[Offset], rubbers, 33,
 ,[Offset], spiral_ramp, 34,
 ,[Offset], wire, 35,
 ,[Offset], wooden_rails, 36,
 ,[Offset], yellow_posts, 37,
 ,[Offset], right_slingshot, 38,
 ,[Offset], pop_bumper_metal, 39,
 ,[Offset], lamps, 40,
 ,[Offset], onewaygateA, 41,
 ,[Offset], onewaygateB, 42,
 ,[Offset], small_red_posts, 43,
 ,[Offset], habi_trail, 44,
 ,[Offset], pop_bumper_bases, 45,
 ,[Offset], rails, 46,
 ,[Offset], screws, 47,
 ,[Offset], white_nuts, 48,
 ,[Offset], 2c_alphaplane, 49,
 ,[Offset], 5c_alphaplane, 50,
 ,[Offset], 7c_alphaplane, 51,
RSID_TPINBOT_MODELS_LODS, 2906,,,
 ,[Offset], yellow_target, 0,
 ,[Offset], adv_X_ramp, 1,
 ,[Offset], apron, 2,
 ,[Offset], backglass, 3,
 ,[Offset], black_posts, 4,
 ,[Offset], blue_posts, 5,
 ,[Offset], blue_ramp, 6,
 ,[Offset], blue_target, 7,
 ,[Offset], bride_plastic, 8,
 ,[Offset], bulbs, 9,
 ,[Offset], bumper, 10,
 ,[Offset], buttons, 11,
 ,[Offset], cabinet, 12,
 ,[Offset], cabinet_metal, 13,
 ,[Offset], droptile, 14,
 ,[Offset], flashers, 15,
 ,[Offset], Flipper_Left, 16,
 ,[Offset], Flipper_Right, 17,
 ,[Offset], green_target, 18,
 ,[Offset], left_slingshot, 19,
 ,[Offset], metal_parts, 20,
 ,[Offset], metal_posts, 21,
 ,[Offset], orange_target, 22,
 ,[Offset], pin-bot_targets, 23,
 ,[Offset], pin-bot_visor, 24,
 ,[Offset], plastic1, 25,
 ,[Offset], plastic2, 26,
 ,[Offset], plastic3, 27,
 ,[Offset], playfield, 28,
 ,[Offset], Plunger, 29,
 ,[Offset], pop_bumpers, 30,
 ,[Offset], red_posts, 31,
 ,[Offset], red_target, 32,
 ,[Offset], rubbers, 33,
 ,[Offset], spiral_ramp, 34,
 ,[Offset], wire, 35,
 ,[Offset], wooden_rails, 36,
 ,[Offset], yellow_posts, 37,
 ,[Offset], right_slingshot, 38,
 ,[Offset], pop_bumper_metal, 39,
 ,[Offset], lamps, 40,
 ,[Offset], onewaygateA, 41,
 ,[Offset], onewaygateB, 42,
 ,[Offset], small_red_posts, 43,
 ,[Offset], habi_trail, 44,
 ,[Offset], pop_bumper_bases, 45,
 ,[Offset], rails, 46,
 ,[Offset], screws, 47,
 ,[Offset], white_nuts, 48,
 ,[Offset], 2c_alphaplane, 49,
 ,[Offset], 5c_alphaplane, 50,
 ,[Offset], 7c_alphaplane, 51,
RSID_TPINBOT_COLLISION, 2907,,,
 ,[Offset], upper_playfield_col, 0,
 ,[Offset], adv_X_ramp_col, 1,
 ,[Offset], bumper_col, 2,
 ,[Offset], droptile_col, 3,
 ,[Offset], Flipper_Left_back, 4,
 ,[Offset], Flipper_Left_front, 5,
 ,[Offset], Flipper_right_back, 6,
 ,[Offset], Flipper_right_front, 7,
 ,[Offset], habit_trail_col, 8,
 ,[Offset], one_way_gate_back_col, 9,
 ,[Offset], one_way_gate_front_col, 10,
 ,[Offset], playfield_col, 11,
 ,[Offset], plunger_col, 12,
 ,[Offset], target_col, 13,
 ,[Offset], trap_col, 14,
 ,[Offset], trapwall_col, 15,
 ,[Offset], upper_walls, 16,
 ,[Offset], ball_drain, 17,
 ,[Offset], eye_lanes, 18,
 ,[Offset], left_inoutlane, 19,
 ,[Offset], left_slingshot_col, 20,
 ,[Offset], left_trap_lane, 21,
 ,[Offset], low_target_lane, 22,
 ,[Offset], plunger_lane, 23,
 ,[Offset], pop_bumper_col, 24,
 ,[Offset], ramp_col, 25,
 ,[Offset], right_inoutlane, 26,
 ,[Offset], right_slingshot_col, 27,
 ,[Offset], rubber_stopper, 28,
 ,[Offset], rubber1, 29,
 ,[Offset], rubber2, 30,
 ,[Offset], rubber3, 31,
 ,[Offset], rubber4, 32,
 ,[Offset], rubber5, 33,
 ,[Offset], spiral_ramp_col, 34,
 ,[Offset], under_ramp_lane, 35,
 ,[Offset], upper_rubbers, 36,
 ,[Offset], one_way_gateB_back_col, 37,
 ,[Offset], one_way_gateB_front_col, 38,
 ,[Offset], pinbot targets col, 39,
 ,[Offset], vortex_pin_front, 40,
 ,[Offset], vortex_pin_back, 41,
 ,[Offset], bumperA_col, 42,
 ,[Offset], bumperB_col, 43,
 ,[Offset], bumperC_col, 44,
 ,[Offset], targetB_col, 45,
RSID_TPINBOT_PLACEMENT, 2908,,,
RSID_TPINBOT_EMUROM, 2909,,,
 ,[Offset], pbot_u26_l5, 0,
 ,[Offset], pbot_u27_l5, 1,
 ,[Offset], pbot_l5, 2,
RSID_TPINBOT_SOUNDS_START, 2910,,,
RSID_TPINBOT_EMU_SOUNDS, 2911,,,
 ,[Offset], S0001_LP1, 0,
 ,[Offset], S0001_LP2, 1,
 ,[Offset], S0002_LP1, 2,
 ,[Offset], S0002_LP2, 3,
 ,[Offset], S0003_LP, 4,
 ,[Offset], S0004_LP1, 5,
 ,[Offset], S0004_LP2, 6,
 ,[Offset], S0005_LP1, 7,
 ,[Offset], S0005_LP2, 8,
 ,[Offset], S0006_LP1, 9,
 ,[Offset], S0006_LP2, 10,
 ,[Offset], S0007_C3, 11,
 ,[Offset], S0008_LP1, 12,
 ,[Offset], S0008_LP2, 13,
 ,[Offset], S0009_LP1, 14,
 ,[Offset], S0009_LP2, 15,
 ,[Offset], S0080_C4, 16,
 ,[Offset], S0081_C4, 17,
 ,[Offset], S0082_C4, 18,
 ,[Offset], S0083_C4, 19,
 ,[Offset], S0084_C4, 20,
 ,[Offset], S0085_C4, 21,
 ,[Offset], S0086_C4, 22,
 ,[Offset], S0087_C4, 23,
 ,[Offset], S0088_C4, 24,
 ,[Offset], S0089_C4, 25,
 ,[Offset], S008A_C4, 26,
 ,[Offset], S008B_C4, 27,
 ,[Offset], S008C_C4, 28,
 ,[Offset], S008D_C4, 29,
 ,[Offset], S008E_C4, 30,
 ,[Offset], S008F_C4, 31,
 ,[Offset], S0090_C4, 32,
 ,[Offset], S0091_C4, 33,
 ,[Offset], S0092_C4, 34,
 ,[Offset], S0093_C4, 35,
 ,[Offset], S0094_LP1, 36,
 ,[Offset], S0094_LP2, 37,
 ,[Offset], S0095_C4, 38,
 ,[Offset], S0096_C4, 39,
 ,[Offset], S0097_C4, 40,
 ,[Offset], S0098_C4, 41,
 ,[Offset], S0099_C4, 42,
 ,[Offset], S009A_C4, 43,
 ,[Offset], S009B_C4, 44,
 ,[Offset], S009C_C4, 45,
 ,[Offset], S009D_C4, 46,
 ,[Offset], S009E_C4, 47,
 ,[Offset], S009F_C4, 48,
 ,[Offset], S00A0_C4, 49,
 ,[Offset], S00A1_C4, 50,
 ,[Offset], S00A2_C4, 51,
 ,[Offset], S00A3_C4, 52,
 ,[Offset], S00A4_C4, 53,
 ,[Offset], S00A5_C4, 54,
 ,[Offset], S00A6_C4, 55,
 ,[Offset], S00A7_C4, 56,
 ,[Offset], S00A8_C4, 57,
 ,[Offset], S00A9_C4, 58,
 ,[Offset], S00AA_C4, 59,
 ,[Offset], S00AB_C4, 60,
 ,[Offset], S00AC_C4, 61,
 ,[Offset], S00AD_C4, 62,
 ,[Offset], S011C_C2, 63,
 ,[Offset], S011D_C2, 64,
 ,[Offset], S011E_C2, 65,
 ,[Offset], S011F_C2, 66,
 ,[Offset], S0120_C2, 67,
 ,[Offset], S0121_C2, 68,
 ,[Offset], S0122_C2, 69,
 ,[Offset], S0123_C2, 70,
 ,[Offset], S0124_C2, 71,
 ,[Offset], S0125_C4_LP, 72,
 ,[Offset], S0126_C4_LP, 73,
 ,[Offset], S0127_C2, 74,
 ,[Offset], S0127_C4_LP, 75,
 ,[Offset], S0128_C2, 76,
 ,[Offset], S0129_C2, 77,
 ,[Offset], S012A_C2, 78,
 ,[Offset], S012B_C2, 79,
 ,[Offset], S012C_C2, 80,
 ,[Offset], S012D_C2, 81,
 ,[Offset], S012E_C2, 82,
 ,[Offset], S012F_C2, 83,
 ,[Offset], S0130_C2, 84,
 ,[Offset], S013D_C2, 85,
 ,[Offset], S013E_C2, 86,
 ,[Offset], S013F_C2, 87,
 ,[Offset], S0140_C2, 88,
 ,[Offset], S0141_C2, 89,
 ,[Offset], S0142_C2, 90,
 ,[Offset], S0143_C2, 91,
 ,[Offset], S0144_C2, 92,
 ,[Offset], S0145_C2, 93,
 ,[Offset], S0146_C2, 94,
 ,[Offset], S0147_C2, 95,
 ,[Offset], S0148_C2, 96,
 ,[Offset], S0149_C2, 97,
 ,[Offset], S014A_C2, 98,
 ,[Offset], S014B_C2, 99,
 ,[Offset], S014C_C2, 100,
 ,[Offset], S014D_C2, 101,
 ,[Offset], S014E_C2, 102,
 ,[Offset], S014F_C2, 103,
 ,[Offset], S0150_C2, 104,
 ,[Offset], S0151_C2, 105,
 ,[Offset], S0152_C2, 106,
 ,[Offset], S0153_C2, 107,
 ,[Offset], S0154_C2, 108,
 ,[Offset], S0155_C2, 109,
 ,[Offset], S0156_C2, 110,
 ,[Offset], S0157_C2, 111,
 ,[Offset], S0158_C2, 112,
 ,[Offset], S0159_C2, 113,
 ,[Offset], S015A_C2, 114,
 ,[Offset], S015B_C2, 115,
 ,[Offset], S015C_C2, 116,
 ,[Offset], S015D_C2, 117,
 ,[Offset], S015E_C2, 118,
 ,[Offset], S015F_C2, 119,
 ,[Offset], S0160_C2, 120,
 ,[Offset], S0161_C2, 121,
 ,[Offset], S0162_C2, 122,
 ,[Offset], S0163_C2, 123,
 ,[Offset], S0164_C2, 124,
 ,[Offset], S0165_C2, 125,
 ,[Offset], S0166_C2, 126,
 ,[Offset], S0167_C2, 127,
 ,[Offset], S0168_C2, 128,
 ,[Offset], S0169_C2, 129,
 ,[Offset], S016A_C2, 130,
 ,[Offset], S016B_C2, 131,
 ,[Offset], S016C_C2, 132,
 ,[Offset], S016D_C2, 133,
 ,[Offset], S016E_C2, 134,
 ,[Offset], S016F_C2, 135,
 ,[Offset], S0170_C2, 136,
 ,[Offset], S0171_C2, 137,
 ,[Offset], S0172_C2, 138,
 ,[Offset], S0173_C2, 139,
 ,[Offset], S0174_C2, 140,
 ,[Offset], S0175_C2, 141,
 ,[Offset], S0176_C2, 142,
 ,[Offset], S0177_C2, 143,
 ,[Offset], S0178_C2, 144,
 ,[Offset], S0179_C2, 145,
 ,[Offset], S017A_C2, 146,
 ,[Offset], S017B_C2, 147,
 ,[Offset], S017C_C2, 148,
 ,[Offset], S017D_C2, 149,
 ,[Offset], S017E_C2, 150,
 ,[Offset], S017F_C2, 151,
 ,[Offset], S0180_C2, 152,
 ,[Offset], S0181_C2, 153,
 ,[Offset], S0182_C2, 154,
 ,[Offset], S0183_C2, 155,
 ,[Offset], S0184_C2, 156,
 ,[Offset], S0185_C2, 157,
 ,[Offset], S0186_C2, 158,
 ,[Offset], S0187_C2, 159,
 ,[Offset], S0188_C2, 160,
 ,[Offset], S0189_C2, 161,
 ,[Offset], S018A_C2, 162,
 ,[Offset], S018B_C2, 163,
 ,[Offset], S018C_C2, 164,
 ,[Offset], S018D_C2, 165,
 ,[Offset], S018E_C2, 166,
 ,[Offset], S018F_C2, 167,
 ,[Offset], S0190_C2, 168,
 ,[Offset], S0191_C2, 169,
 ,[Offset], S0192_C2, 170,
 ,[Offset], S0193_C2, 171,
 ,[Offset], S0194_C2, 172,
 ,[Offset], S0195_C2, 173,
 ,[Offset], S0196_C2, 174,
 ,[Offset], S0197_C2, 175,
 ,[Offset], S0198_C2, 176,
 ,[Offset], S0199_C2, 177,
 ,[Offset], S019A_C2, 178,
 ,[Offset], S019B_C2, 179,
 ,[Offset], S019C_C2, 180,
 ,[Offset], S019D_C2, 181,
 ,[Offset], S019E_C2, 182,
 ,[Offset], S019F_C2, 183,
 ,[Offset], S01A0_C2, 184,
 ,[Offset], S01A1_C2, 185,
 ,[Offset], S01A2_C2, 186,
 ,[Offset], S01A3_C2, 187,
 ,[Offset], S01A4_C2, 188,
 ,[Offset], S01A5_C2, 189,
 ,[Offset], S01A6_C2, 190,
 ,[Offset], S01A7_C2, 191,
 ,[Offset], S01A8_C2, 192,
 ,[Offset], S01A9_C2, 193,
 ,[Offset], S01AA_C2, 194,
 ,[Offset], S01AB_C2, 195,
 ,[Offset], S01AC_C2, 196,
 ,[Offset], S01AD_C2, 197,
 ,[Offset], S01AE_C2, 198,
 ,[Offset], S01AF_C2, 199,
 ,[Offset], S01B0_C2, 200,
 ,[Offset], S01B1_C2, 201,
 ,[Offset], S01B2_C2, 202,
 ,[Offset], S01B3_C2, 203,
 ,[Offset], S01B4_C2, 204,
 ,[Offset], S01B5_C2, 205,
 ,[Offset], S01B8_C2, 206,
 ,[Offset], S01B9_C2, 207,
 ,[Offset], S01BA_C2, 208,
 ,[Offset], S01BB_C2, 209,
 ,[Offset], S01BC_C2, 210,
 ,[Offset], S01BD_C2, 211,
 ,[Offset], S01BE_C4_LP, 212,
 ,[Offset], S01BF_C4_LP, 213,
 ,[Offset], S01C0_C4_LP, 214,
 ,[Offset], S01C1_C4_LP, 215,
 ,[Offset], S01C2_C4_LP, 216,
 ,[Offset], S01C3_C4_LP, 217,
 ,[Offset], S01C4_C4_LP, 218,
 ,[Offset], S01C5_C4_LP, 219,
 ,[Offset], S01C6_C4_LP, 220,
 ,[Offset], S01C9_C2, 221,
 ,[Offset], S01CA_C2, 222,
 ,[Offset], S01CE_C2, 223,
 ,[Offset], S01CF_C2, 224,
 ,[Offset], S01D7_C2, 225,
 ,[Offset], S01DB_C2, 226,
 ,[Offset], S01DF_C2, 227,
 ,[Offset], S01E0_C2, 228,
 ,[Offset], S01E1_C2, 229,
 ,[Offset], S01E2_C2, 230,
 ,[Offset], S01E3_C2, 231,
 ,[Offset], S01E4_C2, 232,
 ,[Offset], S01E5_C2, 233,
 ,[Offset], S01E6_C2, 234,
 ,[Offset], S01E7_C2, 235,
 ,[Offset], S01E8_C2, 236,
 ,[Offset], S01E9_C2, 237,
 ,[Offset], S01EB_C2, 238,
 ,[Offset], S01EC_C2, 239,
 ,[Offset], S01ED_C2, 240,
 ,[Offset], S01EE_C2, 241,
 ,[Offset], S01EF_C2, 242,
 ,[Offset], S01F0_LP, 243,
 ,[Offset], S01F1_C2, 244,
 ,[Offset], S01F2_C2, 245,
 ,[Offset], S01F3_C2, 246,
 ,[Offset], S01F7_C2, 247,
 ,[Offset], S01F8_C2, 248,
 ,[Offset], S01F9_C2, 249,
 ,[Offset], S01FA_C2, 250,
 ,[Offset], S01FB_C2, 251,
 ,[Offset], S01FC_C2, 252,
 ,[Offset], S01FD_C2, 253,
 ,[Offset], S01FF_C2, 254,
 ,[Offset], S01FF_C2, 255,
RSID_TPINBOT_MECH_SOUNDS, 2912,,,
 ,[Offset], drop_targets_reset, 0,
RSID_TPINBOT_SOUNDS_END, 2913,,,
RSID_TPINBOT_SAMPLES, 2914,,,
RSID_TPINBOT_HUD, 2915,,,
RSID_TPINBOT_END, 2916,,,
