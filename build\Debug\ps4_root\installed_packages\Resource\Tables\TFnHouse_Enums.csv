RSID_TFUNHOUSE_START, 3425,,,
 ,[Offset], FUNHOUSE_FLYER_1, 0,
 ,[Offset], Funhouse\PBTFunhouse, 1,
 ,[Offset], Funhouse\InstructionsENG, 2,
 ,[Offset], Funhouse\InstructionsFR, 3,
 ,[Offset], Funhouse\InstructionsITAL, 4,
 ,[Offset], Funhouse\InstructionsGERM, 5,
 ,[Offset], Funhouse\InstructionsSPAN, 6,
 ,[Offset], Funhouse\InstructionsPORT, 7,
 ,[Offset], Funhouse\InstructionsDUTCH, 8,
 ,[Offset], tables\FUNHOUSE_BG_Scroll, 9,
RSID_TFUNHOUSE_LIGHTS, 3426,,,
RSID_TFUNHOUSE_CAMERAS, 3427,,,
RSID_TFUNHOUSE_LAMP_TEXTURES, 3428,,,
 ,[Offset], L01_off, 0,
 ,[Offset], L01_on, 1,
 ,[Offset], L02_off, 2,
 ,[Offset], L02_on, 3,
 ,[Offset], L03_off, 4,
 ,[Offset], L03_on, 5,
 ,[Offset], L04_off, 6,
 ,[Offset], L04_on, 7,
 ,[Offset], L05_off, 8,
 ,[Offset], L05_on, 9,
 ,[Offset], L06_off, 10,
 ,[Offset], L06_on, 11,
 ,[Offset], L07_off, 12,
 ,[Offset], L07_on, 13,
 ,[Offset], L08_off, 14,
 ,[Offset], L08_on, 15,
 ,[Offset], L09_off, 16,
 ,[Offset], L09_on, 17,
 ,[Offset], L10_off, 18,
 ,[Offset], L10_on, 19,
 ,[Offset], L11_off, 20,
 ,[Offset], L11_on, 21,
 ,[Offset], L12_off, 22,
 ,[Offset], L12_on, 23,
 ,[Offset], L13_off, 24,
 ,[Offset], L13_on, 25,
 ,[Offset], L14_off, 26,
 ,[Offset], L14_on, 27,
 ,[Offset], L15_off, 28,
 ,[Offset], L15_on, 29,
 ,[Offset], L16_off, 30,
 ,[Offset], L16_on, 31,
 ,[Offset], L17_off, 32,
 ,[Offset], L17_on, 33,
 ,[Offset], L18_off, 34,
 ,[Offset], L18_on, 35,
 ,[Offset], L19_off, 36,
 ,[Offset], L19_on, 37,
 ,[Offset], L20_off, 38,
 ,[Offset], L20_on, 39,
 ,[Offset], L21_off, 40,
 ,[Offset], L21_on, 41,
 ,[Offset], L22_off, 42,
 ,[Offset], L22_on, 43,
 ,[Offset], L23_off, 44,
 ,[Offset], L23_on, 45,
 ,[Offset], L24_off, 46,
 ,[Offset], L24_on, 47,
 ,[Offset], L25_off, 48,
 ,[Offset], L25_on, 49,
 ,[Offset], L26_off, 50,
 ,[Offset], L26_on, 51,
 ,[Offset], L27_off, 52,
 ,[Offset], L27_on, 53,
 ,[Offset], L28_off, 54,
 ,[Offset], L28_on, 55,
 ,[Offset], L29_off, 56,
 ,[Offset], L29_on, 57,
 ,[Offset], L30_off, 58,
 ,[Offset], L30_on, 59,
 ,[Offset], L31_off, 60,
 ,[Offset], L31_on, 61,
 ,[Offset], L32_off, 62,
 ,[Offset], L32_on, 63,
 ,[Offset], L33_off, 64,
 ,[Offset], L33_on, 65,
 ,[Offset], L34_off, 66,
 ,[Offset], L34_on, 67,
 ,[Offset], L35_off, 68,
 ,[Offset], L35_on, 69,
 ,[Offset], L36_off, 70,
 ,[Offset], L36_on, 71,
 ,[Offset], L37_off, 72,
 ,[Offset], L37_on, 73,
 ,[Offset], L38_off, 74,
 ,[Offset], L38_on, 75,
 ,[Offset], L39_off, 76,
 ,[Offset], L39_on, 77,
 ,[Offset], L40_off, 78,
 ,[Offset], L40_on, 79,
 ,[Offset], L41_off, 80,
 ,[Offset], L41_on, 81,
 ,[Offset], L42_off, 82,
 ,[Offset], L42_on, 83,
 ,[Offset], L43_off, 84,
 ,[Offset], L43_on, 85,
 ,[Offset], L44_off, 86,
 ,[Offset], L44_on, 87,
 ,[Offset], L45_off, 88,
 ,[Offset], L45_on, 89,
 ,[Offset], L46_off, 90,
 ,[Offset], L46_on, 91,
 ,[Offset], L47_off, 92,
 ,[Offset], L47_on, 93,
 ,[Offset], L48_off, 94,
 ,[Offset], L48_on, 95,
 ,[Offset], L49_off, 96,
 ,[Offset], L49_on, 97,
 ,[Offset], L50_off, 98,
 ,[Offset], L50_on, 99,
 ,[Offset], L51_off, 100,
 ,[Offset], L51_on, 101,
 ,[Offset], L52_off, 102,
 ,[Offset], L52_on, 103,
 ,[Offset], L53_off, 104,
 ,[Offset], L53_on, 105,
 ,[Offset], L54_off, 106,
 ,[Offset], L54_on, 107,
 ,[Offset], L55_off, 108,
 ,[Offset], L55_on, 109,
 ,[Offset], L56_off, 110,
 ,[Offset], L56_on, 111,
 ,[Offset], L57_off, 112,
 ,[Offset], L57_on, 113,
 ,[Offset], L58_off, 114,
 ,[Offset], L58_on, 115,
 ,[Offset], L59_off, 116,
 ,[Offset], L59_on, 117,
 ,[Offset], L60_off, 118,
 ,[Offset], L60_on, 119,
 ,[Offset], L61_off, 120,
 ,[Offset], L61_on, 121,
 ,[Offset], L62_off, 122,
 ,[Offset], L62_on, 123,
 ,[Offset], L63_off, 124,
 ,[Offset], L63_on, 125,
 ,[Offset], L64_off, 126,
 ,[Offset], L64_on, 127,
 ,[Offset], L65_off, 128,
 ,[Offset], L65_on, 129,
 ,[Offset], L66_off, 130,
 ,[Offset], L66_on, 131,
 ,[Offset], L67_off, 132,
 ,[Offset], L67_on, 133,
 ,[Offset], L68_off, 134,
 ,[Offset], L68_on, 135,
 ,[Offset], L69_off, 136,
 ,[Offset], L69_on, 137,
 ,[Offset], L70_off, 138,
 ,[Offset], L70_on, 139,
 ,[Offset], L71_off, 140,
 ,[Offset], L71_on, 141,
 ,[Offset], L72_off, 142,
 ,[Offset], L72_on, 143,
 ,[Offset], L73_off, 144,
 ,[Offset], L73_on, 145,
 ,[Offset], L74_off, 146,
 ,[Offset], L74_on, 147,
 ,[Offset], L75_off, 148,
 ,[Offset], L75_on, 149,
 ,[Offset], L76_off, 150,
 ,[Offset], L76_on, 151,
 ,[Offset], L77_off, 152,
 ,[Offset], L77_on, 153,
 ,[Offset], L78_off, 154,
 ,[Offset], L78_on, 155,
 ,[Offset], L79_off, 156,
 ,[Offset], L79_on, 157,
 ,[Offset], L80_off, 158,
 ,[Offset], L80_on, 159,
 ,[Offset], L81_off, 160,
 ,[Offset], L81_on, 161,
 ,[Offset], L82_off, 162,
 ,[Offset], L82_on, 163,
 ,[Offset], L83_off, 164,
 ,[Offset], L83_on, 165,
 ,[Offset], L84_off, 166,
 ,[Offset], L84_on, 167,
 ,[Offset], L85_off, 168,
 ,[Offset], L85_on, 169,
 ,[Offset], L86_off, 170,
 ,[Offset], L86_on, 171,
 ,[Offset], L87_off, 172,
 ,[Offset], L87_on, 173,
RSID_TFUNHOUSE_TEXTURES, 3429,,,
 ,[Offset], FH_A1_0, 0,
 ,[Offset], FH_A2_0, 1,
 ,[Offset], FH_B1_0, 2,
 ,[Offset], FH_B2_0, 3,
 ,[Offset], FH_A3_0, 4,
 ,[Offset], FH_A4_0, 5,
 ,[Offset], FH_B3_0, 6,
 ,[Offset], FH_B4_0, 7,
 ,[Offset], FH_A5_0, 8,
 ,[Offset], FH_A6_0, 9,
 ,[Offset], FH_B5_0, 10,
 ,[Offset], FH_B6_0, 11,
 ,[Offset], FH_A7_0, 12,
 ,[Offset], FH_A8_0, 13,
 ,[Offset], FH_B7_0, 14,
 ,[Offset], FH_B8_0, 15,
 ,[Offset], FH_C1_0, 16,
 ,[Offset], FH_C2_0, 17,
 ,[Offset], FH_D1_0, 18,
 ,[Offset], FH_D2_0, 19,
 ,[Offset], FH_C3_0, 20,
 ,[Offset], FH_C4_0, 21,
 ,[Offset], FH_D3_0, 22,
 ,[Offset], FH_D4_0, 23,
 ,[Offset], FH_C5_0, 24,
 ,[Offset], FH_C6_0, 25,
 ,[Offset], FH_D5_0, 26,
 ,[Offset], FH_D6_0, 27,
 ,[Offset], FH_C7_0, 28,
 ,[Offset], FH_C8_0, 29,
 ,[Offset], FH_D7_0, 30,
 ,[Offset], FH_D8_0, 31,
 ,[Offset], FH_E1_0, 32,
 ,[Offset], FH_E2_0, 33,
 ,[Offset], FH_F1_0, 34,
 ,[Offset], FH_F2_0, 35,
 ,[Offset], FH_E3_0, 36,
 ,[Offset], FH_E4_0, 37,
 ,[Offset], FH_F3_0, 38,
 ,[Offset], FH_F4_0, 39,
 ,[Offset], FH_E5_0, 40,
 ,[Offset], FH_E6_0, 41,
 ,[Offset], FH_F5_0, 42,
 ,[Offset], FH_F6_0, 43,
 ,[Offset], FH_E7_0, 44,
 ,[Offset], FH_E8_0, 45,
 ,[Offset], FH_F7_0, 46,
 ,[Offset], FH_F8_0, 47,
 ,[Offset], FH_G1_0, 48,
 ,[Offset], FH_G2_0, 49,
 ,[Offset], FH_H1_0, 50,
 ,[Offset], FH_H2_0, 51,
 ,[Offset], FH_G3_0, 52,
 ,[Offset], FH_G4_0, 53,
 ,[Offset], FH_H3_0, 54,
 ,[Offset], FH_H4_0, 55,
 ,[Offset], FH_G5_0, 56,
 ,[Offset], FH_G6_0, 57,
 ,[Offset], FH_H5_0, 58,
 ,[Offset], FH_H6_0, 59,
 ,[Offset], FH_G7_0, 60,
 ,[Offset], FH_G8_0, 61,
 ,[Offset], FH_H7_0, 62,
 ,[Offset], FH_H8_0, 63,
 ,[Offset], amazingrudy01, 64,
 ,[Offset], backglass01, 65,
 ,[Offset], balloons01, 66,
 ,[Offset], balloons02, 67,
 ,[Offset], bolt-tp01 copy, 68,
 ,[Offset], brushed_metal01, 69,
 ,[Offset], BumperTop_A01, 70,
 ,[Offset], BumperTop_B01, 71,
 ,[Offset], BumperTop_C01, 72,
 ,[Offset], BumperTop_D01, 73,
 ,[Offset], BumperTop_F01, 74,
 ,[Offset], BumperTop_G01, 75,
 ,[Offset], BumperTop_H01, 76,
 ,[Offset], BumperTop_I01, 77,
 ,[Offset], BumperTop_J01, 78,
 ,[Offset], BumperTop_K01, 79,
 ,[Offset], BumperTop_L01, 80,
 ,[Offset], BumperTop_M01, 81,
 ,[Offset], BumperTop_N01, 82,
 ,[Offset], BumperTop_O01, 83,
 ,[Offset], Buttons_Parts, 84,
 ,[Offset], cabinet01, 85,
 ,[Offset], CoinSlots, 86,
 ,[Offset], collision, 87,
 ,[Offset], color wheel, 88,
 ,[Offset], flipper, 89,
 ,[Offset], glass, 90,
 ,[Offset], Hand_A01, 91,
 ,[Offset], Hand_B01, 92,
 ,[Offset], Hand_C01, 93,
 ,[Offset], Hand_D01, 94,
 ,[Offset], metal01, 95,
 ,[Offset], metal_parts01, 96,
 ,[Offset], metal_parts02, 97,
 ,[Offset], metal_parts03, 98,
 ,[Offset], metal_legs, 99,
 ,[Offset], metal_trim, 100,
 ,[Offset], mirrorsign01, 101,
 ,[Offset], nut01, 102,
 ,[Offset], partA01, 103,
 ,[Offset], plastic_stem01, 104,
 ,[Offset], postB01, 105,
 ,[Offset], postC01, 106,
 ,[Offset], ramp_switch01, 107,
 ,[Offset], rubberband_white01, 108,
 ,[Offset], Rudy_Eye01, 109,
 ,[Offset], rules, 110,
 ,[Offset], screw_silver01, 111,
 ,[Offset], sign01, 112,
 ,[Offset], sign02, 113,
 ,[Offset], sign03, 114,
 ,[Offset], sign04, 115,
 ,[Offset], table_trimmetal01, 116,
 ,[Offset], target_blue01, 117,
 ,[Offset], target_red01, 118,
 ,[Offset], target-red-tp01 copy, 119,
 ,[Offset], TicketBox_A01, 120,
 ,[Offset], TicketBox_B01, 121,
 ,[Offset], TrapDoor, 122,
 ,[Offset], Trough01, 123,
 ,[Offset], Trough02, 124,
 ,[Offset], wall_reflections01, 125,
 ,[Offset], washer_silver01, 126,
 ,[Offset], wire_trigger01, 127,
 ,[Offset], wires01, 128,
 ,[Offset], wood01, 129,
 ,[Offset], plunger_plate_baked, 130,
 ,[Offset], plunger_metal, 131,
 ,[Offset], metal front, 132,
 ,[Offset], screw white, 133,
 ,[Offset], Bumper_Logo01, 134,
 ,[Offset], postA01, 135,
 ,[Offset], plastic_clear01, 136,
 ,[Offset], plastic_clear02, 137,
 ,[Offset], Blue_Bumper, 138,
 ,[Offset], Clear_Bumper, 139,
 ,[Offset], Habit1, 140,
 ,[Offset], Habit2, 141,
 ,[Offset], Ramp1, 142,
 ,[Offset], Red_Bumper, 143,
 ,[Offset], Rudy_Chin, 144,
 ,[Offset], Rudy_Eyes&Ears, 145,
 ,[Offset], Rudy_Head, 146,
 ,[Offset], PopBumperBody, 147,
 ,[Offset], Ramp2, 148,
 ,[Offset], Plastic_Group1, 149,
 ,[Offset], Plastic_Group2_, 150,
 ,[Offset], Flipper_Button, 151,
 ,[Offset], Cabinet_Head, 152,
 ,[Offset], Black_Grain, 153,
 ,[Offset], Extra_Metal_Parts, 154,
 ,[Offset], Generic_Metal, 155,
 ,[Offset], Metal_Parts, 156,
 ,[Offset], Coin_Slot, 157,
 ,[Offset], Front_LittleHabit, 158,
 ,[Offset], Group1, 159,
 ,[Offset], Silver Metal Screws_Temp, 160,
 ,[Offset], Blue_Target, 161,
 ,[Offset], Habbi_Texture, 162,
 ,[Offset], Rails, 163,
 ,[Offset], Round_Metal, 164,
RSID_TFUNHOUSE_MODELS, 3430,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet_Body, 1,
 ,[Offset], Cabinet_Interior, 2,
 ,[Offset], Habit_Trails, 3,
 ,[Offset], Head, 4,
 ,[Offset], Light_Cutouts, 5,
 ,[Offset], Metal_Pieces, 6,
 ,[Offset], Metal_Pieces_2, 7,
 ,[Offset], Plastic_Pieces, 8,
 ,[Offset], Plastic_Pieces_2, 9,
 ,[Offset], Playfield, 10,
 ,[Offset], Rubber_Pieces, 11,
 ,[Offset], Transparent_Parts, 12,
 ,[Offset], Wooden_Rails, 13,
 ,[Offset], Plastic_Ramps, 14,
 ,[Offset], Plastic_Posts, 15,
 ,[Offset], Plastic_Posts_2, 16,
 ,[Offset], Metal_Walls, 17,
 ,[Offset], Flashers, 18,
 ,[Offset], Cabinet_Backglass, 19,
 ,[Offset], Bumper_Tops, 20,
 ,[Offset], Bulbs, 21,
 ,[Offset], Flipper_Left, 22,
 ,[Offset], Flipper_Right, 23,
 ,[Offset], Chin, 24,
 ,[Offset], Eye_Left, 25,
 ,[Offset], Eye_Right, 26,
 ,[Offset], Eye_Left_Lid, 27,
 ,[Offset], Eye_Right_Lid, 28,
 ,[Offset], Plunger, 29,
 ,[Offset], Ramp_Wire, 30,
 ,[Offset], Slingshot_Left, 31,
 ,[Offset], Slingshot_Right, 32,
 ,[Offset], Target_Big_Blue, 33,
 ,[Offset], Target_Blue, 34,
 ,[Offset], Target_Red, 35,
 ,[Offset], Vertical_Wire, 36,
 ,[Offset], Wire, 37,
 ,[Offset], Trap_Door, 38,
 ,[Offset], Cabinet_Metals, 39,
 ,[Offset], Bumper_Metal, 40,
 ,[Offset], Gate_A, 41,
 ,[Offset], Ramp_Diverter, 42,
 ,[Offset], Left_Plunger_Gate, 43,
 ,[Offset], Light_Cutouts_2, 44,
 ,[Offset], Light_Cutouts_3, 45,
 ,[Offset], Spinner_Trap, 46,
 ,[Offset], Plunger_Gate, 47,
 ,[Offset], Plastic_Flashers, 48,
RSID_TFUNHOUSE_MODELS_LODS, 3431,,,
 ,[Offset], Apron, 0,
 ,[Offset], Cabinet_Body, 1,
 ,[Offset], Cabinet_Interior, 2,
 ,[Offset], Habit_Trails, 3,
 ,[Offset], Head, 4,
 ,[Offset], Light_Cutouts, 5,
 ,[Offset], Metal_Pieces, 6,
 ,[Offset], Metal_Pieces_2, 7,
 ,[Offset], Plastic_Pieces, 8,
 ,[Offset], Plastic_Pieces_2, 9,
 ,[Offset], Playfield, 10,
 ,[Offset], Rubber_Pieces, 11,
 ,[Offset], Transparent_Parts, 12,
 ,[Offset], Wooden_Rails, 13,
 ,[Offset], Plastic_Ramps, 14,
 ,[Offset], Plastic_Posts, 15,
 ,[Offset], Plastic_Posts_2, 16,
 ,[Offset], Metal_Walls, 17,
 ,[Offset], Flashers, 18,
 ,[Offset], Cabinet_Backglass, 19,
 ,[Offset], Bumper_Tops, 20,
 ,[Offset], Bulbs, 21,
 ,[Offset], Flipper_Left, 22,
 ,[Offset], Flipper_Right, 23,
 ,[Offset], Chin, 24,
 ,[Offset], Eye_Left, 25,
 ,[Offset], Eye_Right, 26,
 ,[Offset], Eye_Left_Lid, 27,
 ,[Offset], Eye_Right_Lid, 28,
 ,[Offset], Plunger, 29,
 ,[Offset], Ramp_Wire, 30,
 ,[Offset], Slingshot_Left, 31,
 ,[Offset], Slingshot_Right, 32,
 ,[Offset], Target_Big_Blue, 33,
 ,[Offset], Target_Blue, 34,
 ,[Offset], Target_Red, 35,
 ,[Offset], Vertical_Wire, 36,
 ,[Offset], Wire, 37,
 ,[Offset], Trap_Door, 38,
 ,[Offset], Cabinet_Metals, 39,
 ,[Offset], Bumper_Metal, 40,
 ,[Offset], Gate_A, 41,
 ,[Offset], Ramp_Diverter, 42,
 ,[Offset], Left_Plunger_Gate, 43,
 ,[Offset], Light_Cutouts_2, 44,
 ,[Offset], Light_Cutouts_3, 45,
 ,[Offset], Spinner_Trap, 46,
 ,[Offset], Plunger_Gate, 47,
 ,[Offset], Plastic_Flashers, 48,
RSID_TFUNHOUSE_COLLISIONS, 3432,,,
 ,[Offset], Flipper_Left_Back, 0,
 ,[Offset], Flipper_Left_Front, 1,
 ,[Offset], Flipper_Right_Back, 2,
 ,[Offset], Flipper_Right_Front, 3,
 ,[Offset], Floor, 4,
 ,[Offset], Habit_Trail, 5,
 ,[Offset], Plastic_Ramp_Left, 6,
 ,[Offset], Plunger, 7,
 ,[Offset], Rubber_Col, 8,
 ,[Offset], Wall_Col, 9,
 ,[Offset], Ball_Drain, 10,
 ,[Offset], Target_Big, 11,
 ,[Offset], Target_Small, 12,
 ,[Offset], BallTrap_A, 13,
 ,[Offset], Bumper, 14,
 ,[Offset], Right_Slingshot, 15,
 ,[Offset], Left_Slingshot, 16,
 ,[Offset], One_Way_Gate_Back, 17,
 ,[Offset], Left_Plunger_Diverter, 18,
 ,[Offset], Ramp_Diverter, 19,
 ,[Offset], Trap_Door, 20,
 ,[Offset], Gate, 21,
 ,[Offset], Ball_Lock, 22,
 ,[Offset], Chin, 23,
 ,[Offset], One_Way_Gate_Front, 24,
 ,[Offset], Flipper_Lane_Left, 25,
 ,[Offset], Flipper_Lane_Right, 26,
 ,[Offset], Habit_Trail_B, 27,
 ,[Offset], Left_Slingshot_Rubber, 28,
 ,[Offset], Right_Slingshot_Rubber, 29,
 ,[Offset], Rubber_B, 30,
 ,[Offset], Rubber_C, 31,
 ,[Offset], Plastic_Ramp_Right, 32,
 ,[Offset], Metal_Scoop, 33,
 ,[Offset], Wall_B, 34,
 ,[Offset], Trap_A, 35,
 ,[Offset], Mouth_Trap, 36,
RSID_TFUNHOUSE_PLACEMENT, 3433,,,
 ,[Offset], placement, 0,
 ,[Offset], Lights, 1,
RSID_TFUNHOUSE_ROM, 3434,,,
 ,[Offset], fh_l9, 0,
 ,[Offset], fh_l9, 1,
RSID_TFUNHOUSE_SOUNDS_START, 3435,,,
RSID_TFUNHOUSE_EMU_SOUNDS, 3436,,,
 ,[Offset], S0001-LP, 0,
 ,[Offset], S0002-LP1, 1,
 ,[Offset], S0002-LP2, 2,
 ,[Offset], S0004-LP, 3,
 ,[Offset], S0005-LP, 4,
 ,[Offset], S0006-LP, 5,
 ,[Offset], S0007-LP1, 6,
 ,[Offset], S0007-LP2, 7,
 ,[Offset], S0008-LP1, 8,
 ,[Offset], S0008-LP2, 9,
 ,[Offset], S0009-LP1, 10,
 ,[Offset], S0009-LP2, 11,
 ,[Offset], S000A-LP, 12,
 ,[Offset], S000C-LP, 13,
 ,[Offset], S000D-LP, 14,
 ,[Offset], S000E-LP, 15,
 ,[Offset], S000F-LP, 16,
 ,[Offset], S0010-LP, 17,
 ,[Offset], S0011-LP, 18,
 ,[Offset], S0012-LP, 19,
 ,[Offset], S0013-LP, 20,
 ,[Offset], S0040_C3, 21,
 ,[Offset], S0050_C4, 22,
 ,[Offset], S0051_C4, 23,
 ,[Offset], S0052_C4, 24,
 ,[Offset], S0053_C4, 25,
 ,[Offset], S0054_C4, 26,
 ,[Offset], S0055_C4, 27,
 ,[Offset], S0056_C4, 28,
 ,[Offset], S0057_C4, 29,
 ,[Offset], S0058_C4, 30,
 ,[Offset], S0059_C4, 31,
 ,[Offset], S0080_C4, 32,
 ,[Offset], S0081_C4, 33,
 ,[Offset], S0082_C4, 34,
 ,[Offset], S0083_C4, 35,
 ,[Offset], S0084_C4, 36,
 ,[Offset], S0086_C4, 37,
 ,[Offset], S0087_C4, 38,
 ,[Offset], S0088_C4, 39,
 ,[Offset], S0089_C4, 40,
 ,[Offset], S008A_C4, 41,
 ,[Offset], S008B_C4, 42,
 ,[Offset], S008C_C4, 43,
 ,[Offset], S008D_C4, 44,
 ,[Offset], S008E_C4, 45,
 ,[Offset], S0090_C4, 46,
 ,[Offset], S0091_C4, 47,
 ,[Offset], S0092_C4, 48,
 ,[Offset], S0093_C4, 49,
 ,[Offset], S0094_C4, 50,
 ,[Offset], S0095_C4, 51,
 ,[Offset], S0096_C4, 52,
 ,[Offset], S0097_C4, 53,
 ,[Offset], S0098_C4, 54,
 ,[Offset], S0099_C4, 55,
 ,[Offset], S009A_C4, 56,
 ,[Offset], S009B_C4, 57,
 ,[Offset], S009C_C4, 58,
 ,[Offset], S009D_C4, 59,
 ,[Offset], S009F_C4, 60,
 ,[Offset], S00A0_C4, 61,
 ,[Offset], S00A1_C4, 62,
 ,[Offset], S00A2_C4, 63,
 ,[Offset], S00A3_C4, 64,
 ,[Offset], S00A4_C4, 65,
 ,[Offset], S00A5_C4, 66,
 ,[Offset], S00A7_C4, 67,
 ,[Offset], S00A9_C4, 68,
 ,[Offset], S00AA_C4, 69,
 ,[Offset], S00AC_C4, 70,
 ,[Offset], S00AD_C4, 71,
 ,[Offset], S00AE_C4, 72,
 ,[Offset], S00B0_C4, 73,
 ,[Offset], S00B5_C4, 74,
 ,[Offset], S00B6_C4, 75,
 ,[Offset], S00B8_C4, 76,
 ,[Offset], S00BA_C4, 77,
 ,[Offset], S00BC_C4, 78,
 ,[Offset], S00BF_C4, 79,
 ,[Offset], S00C0_C4, 80,
 ,[Offset], S00C1_C4, 81,
 ,[Offset], S00C2_C4, 82,
 ,[Offset], S00C3_C4, 83,
 ,[Offset], S00C4_C4, 84,
 ,[Offset], S00C6_C2, 85,
 ,[Offset], S00C7_C2, 86,
 ,[Offset], S00C8_C4, 87,
 ,[Offset], S00C9_C4, 88,
 ,[Offset], S00CA_C4, 89,
 ,[Offset], S00CB_C4, 90,
 ,[Offset], S00CC_C4, 91,
 ,[Offset], S00CE_C4, 92,
 ,[Offset], S00D0_C4, 93,
 ,[Offset], S00D1_C4, 94,
 ,[Offset], S00D2_C4, 95,
 ,[Offset], S00D3_C4, 96,
 ,[Offset], S00D4_C4, 97,
 ,[Offset], S00D5_C4, 98,
 ,[Offset], S00D6_C4, 99,
 ,[Offset], S00D7_C4, 100,
 ,[Offset], S00D8_C4, 101,
 ,[Offset], S00D9_C4, 102,
 ,[Offset], S00DB_C4, 103,
 ,[Offset], S00DF_C2, 104,
 ,[Offset], S00E0_C4, 105,
 ,[Offset], S00E1_C4, 106,
 ,[Offset], S00E2_C4, 107,
 ,[Offset], S00E3_C4, 108,
 ,[Offset], S00E4_C4, 109,
 ,[Offset], S00E5_C4, 110,
 ,[Offset], S00E6_C4, 111,
 ,[Offset], S0100_C2, 112,
 ,[Offset], S0101_C2, 113,
 ,[Offset], S0102_C2, 114,
 ,[Offset], S0103_C2, 115,
 ,[Offset], S0104_C2, 116,
 ,[Offset], S0105_C2, 117,
 ,[Offset], S0106_C2, 118,
 ,[Offset], S0107_C2, 119,
 ,[Offset], S0108_C2, 120,
 ,[Offset], S0109_C2, 121,
 ,[Offset], S010A_C2, 122,
 ,[Offset], S010B_C2, 123,
 ,[Offset], S010C_C2, 124,
 ,[Offset], S010D_C2, 125,
 ,[Offset], S010E_C2, 126,
 ,[Offset], S010F_C2, 127,
 ,[Offset], S0110_C2, 128,
 ,[Offset], S0111_C2, 129,
 ,[Offset], S0112_C2, 130,
 ,[Offset], S0113_C2, 131,
 ,[Offset], S0114_C2, 132,
 ,[Offset], S0115_C2, 133,
 ,[Offset], S0116_C2, 134,
 ,[Offset], S0117_C2, 135,
 ,[Offset], S0118_C2, 136,
 ,[Offset], S0119_C2, 137,
 ,[Offset], S011A_C2, 138,
 ,[Offset], S011B_C2, 139,
 ,[Offset], S011C_C2, 140,
 ,[Offset], S011D_C2, 141,
 ,[Offset], S011E_C2, 142,
 ,[Offset], S011F_C2, 143,
 ,[Offset], S0120_C2, 144,
 ,[Offset], S0121_C2, 145,
 ,[Offset], S0122_C2, 146,
 ,[Offset], S0123_C2, 147,
 ,[Offset], S0124_C2, 148,
 ,[Offset], S0125_C2, 149,
 ,[Offset], S0126_C2, 150,
 ,[Offset], S0127_C2, 151,
 ,[Offset], S0128_C2, 152,
 ,[Offset], S0129_C2, 153,
 ,[Offset], S012A_C2, 154,
 ,[Offset], S012B_C2, 155,
 ,[Offset], S012C_C2, 156,
 ,[Offset], S012D_C2, 157,
 ,[Offset], S012E_C2, 158,
 ,[Offset], S012F_C2, 159,
 ,[Offset], S0130_C2, 160,
 ,[Offset], S0131_C2, 161,
 ,[Offset], S0132_C2, 162,
 ,[Offset], S0133_C2, 163,
 ,[Offset], S0134_C2, 164,
 ,[Offset], S0135_C2, 165,
 ,[Offset], S0136_C2, 166,
 ,[Offset], S0137_C2, 167,
 ,[Offset], S0138_C2, 168,
 ,[Offset], S0139_C2, 169,
 ,[Offset], S013A_C2, 170,
 ,[Offset], S013B_C2, 171,
 ,[Offset], S013C_C2, 172,
 ,[Offset], S013D_C2, 173,
 ,[Offset], S013E_C2, 174,
 ,[Offset], S013F_C2, 175,
 ,[Offset], S0140_C2, 176,
 ,[Offset], S0141_C2, 177,
 ,[Offset], S0142_C2, 178,
 ,[Offset], S0143_C2, 179,
 ,[Offset], S0144_C2, 180,
 ,[Offset], S0145_C2, 181,
 ,[Offset], S0146_C2, 182,
 ,[Offset], S0147_C2, 183,
 ,[Offset], S0148_C2, 184,
 ,[Offset], S0149_C2, 185,
 ,[Offset], S014A_C2, 186,
 ,[Offset], S014B_C2, 187,
 ,[Offset], S014C_C2, 188,
 ,[Offset], S014D_C2, 189,
 ,[Offset], S014E_C2, 190,
 ,[Offset], S014F_C2, 191,
 ,[Offset], S0150_C2, 192,
 ,[Offset], S0151_C2, 193,
 ,[Offset], S0152_C2, 194,
 ,[Offset], S0153_C2, 195,
 ,[Offset], S0154_C2, 196,
 ,[Offset], S0155_C2, 197,
 ,[Offset], S0156_C2, 198,
 ,[Offset], S0157_C2, 199,
 ,[Offset], S0158_C2, 200,
 ,[Offset], S0159_C2, 201,
 ,[Offset], S015A_C2, 202,
 ,[Offset], S015B_C2, 203,
 ,[Offset], S015C_C2, 204,
 ,[Offset], S015D_C2, 205,
 ,[Offset], S015E_C2, 206,
 ,[Offset], S015F_C2, 207,
 ,[Offset], S0160_C2, 208,
 ,[Offset], S0161_C2, 209,
 ,[Offset], S0162_C2, 210,
 ,[Offset], S0163_C2, 211,
 ,[Offset], S0165_C2, 212,
 ,[Offset], S0168_C2, 213,
 ,[Offset], S0169_C2, 214,
 ,[Offset], S016A_C2, 215,
 ,[Offset], S016B_C2, 216,
 ,[Offset], S0200_C3, 217,
 ,[Offset], S0201_C3, 218,
 ,[Offset], FH_MAIN_MUSIC, 219,
RSID_TFUNHOUSE_MECH_SOUNDS, 3437,,,
 ,[Offset], eyes, 0,
 ,[Offset], eyes_open, 1,
 ,[Offset], eyes_snap_closed, 2,
RSID_TFUNHOUSE_SAMPLES, 3438,,,
RSID_TFUNHOUSE_SOUNDS_END, 3439,,,
RSID_TFUNHOUSE_HUD, 3440,,,
RSID_TFUNHOUSE_VERSION, 3441,,,
RSID_TFUNHOUSE_END, 3442,,,
