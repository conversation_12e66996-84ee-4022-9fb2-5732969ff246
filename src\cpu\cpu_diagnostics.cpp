// Copyright 2025 <Copyright Owner>

#include "cpu_diagnostics.h"
#include "decoded_instruction.h" // Add for DecodedInstruction

#include <algorithm>
#include <chrono>
#include <mutex>
#include <string>
#include <unordered_map>

#include <spdlog/spdlog.h>

#include "ps4/fiber_manager.h"
#include "ps4/ps4_emulator.h"
#include "emulator/apic.h"

namespace x86_64 {

/**
 * @brief Constructs a CPUDiagnostics singleton instance.
 */
CPUDiagnostics::CPUDiagnostics() {
  try {
    ResetMetrics();
    spdlog::info("CPUDiagnostics initialized");
  } catch (const std::exception &e) {
    spdlog::error("CPUDiagnostics initialization failed: {}", e.what());
    // Initialize with empty metrics to prevent crashes
    m_metrics.clear();
  }
}

/**
 * @brief Retrieves the singleton instance of CPUDiagnostics.
 * @return Reference to the singleton instance.
 */
CPUDiagnostics &CPUDiagnostics::GetInstance() {
  static CPUDiagnostics instance;
  return instance;
}

/**
 * @brief Updates CPU-related diagnostic metrics.
 * @details Collects metrics from all CPUs, pipelines, APIC, fibers, and caches.
 */
void CPUDiagnostics::UpdateMetrics(DiagnosticLevel level) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: UpdateMetrics called but emulator data not available");
    return;
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::UpdateMetrics failed: {}", e.what());
  }
}

/**
 * @brief Updates CPU-related diagnostic metrics with provided emulator data.
 * @details Collects metrics from provided CPU data to break circular dependency.
 */
void CPUDiagnostics::UpdateMetricsWithData(const std::vector<x86_64::X86_64CPU*>& cpus,
                                           const ps4::FiberManager& fiberManager,
                                           DiagnosticLevel level) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    size_t cpu_count = cpus.size();
    if (cpu_count == 0) {
      spdlog::trace("CPU diagnostics: no CPUs available yet, skipping update");
      return;
    }

    uint64_t total_cycles = 0, total_instructions = 0, total_utilization = 0;
    uint64_t total_stalls = 0, total_data_hazards = 0, total_memory_stalls = 0;
    uint64_t total_branch_hits = 0, total_branch_mispredictions = 0;
    uint64_t total_apic_ops = 0, total_apic_latency = 0,
             total_simd_instructions = 0;
    uint64_t cache_hits = 0, cache_misses = 0;

    for (size_t i = 0; i < cpu_count; ++i) {
      auto &cpu = *cpus[i];
      auto s = cpu.GetPipeline().GetStats();
      total_cycles += s.cycles;
      total_instructions += s.instructionsExecuted;
      total_utilization += static_cast<uint64_t>(cpu.GetUtilization() * 100);
      total_stalls += s.stalls;
      total_data_hazards += s.data_hazard_stalls;
      total_memory_stalls += s.memory_stalls;
      total_branch_hits += s.branch_hits;
      total_branch_mispredictions += s.branch_mispredictions;
      auto ap = cpu.GetAPIC().GetStats();
      total_apic_ops += ap.operationCount;
      total_apic_latency += ap.totalLatencyUs;

      // SIMD instruction counts from pipeline stats
      auto pipeline_stats = cpu.GetPipeline().GetStats();
      total_simd_instructions += pipeline_stats.simd_instructions;

      // Cache stats (assuming L1 cache access)
      auto cache_stats = cpu.GetMemory().GetStats();
      cache_hits += cache_stats.hits.load();
      cache_misses += cache_stats.misses.load();
    }

    m_metrics["cpu_cycles"] = total_cycles / cpu_count;
    m_metrics["cpu_instructions"] = total_instructions / cpu_count;
    m_metrics["cpu_utilization"] = total_utilization / cpu_count;
    m_metrics["pipeline_stalls"] = total_stalls / cpu_count;
    m_metrics["data_hazard_stalls"] = total_data_hazards / cpu_count;
    m_metrics["memory_stalls"] = total_memory_stalls / cpu_count;
    m_metrics["branch_hits"] = total_branch_hits / cpu_count;
    m_metrics["branch_mispredictions"] = total_branch_mispredictions / cpu_count;
    m_metrics["branch_misprediction_rate"] =
        (total_branch_hits + total_branch_mispredictions) > 0
            ? (total_branch_mispredictions * 100) /
                  (total_branch_hits + total_branch_mispredictions)
            : 0;
    m_metrics["apic_operations"] = total_apic_ops / cpu_count;
    m_metrics["apic_latency_us"] = total_apic_latency / cpu_count;
    m_metrics["apic_interrupts"] = total_apic_ops / cpu_count;
    m_metrics["fiber_count"] = fiberManager.GetFiberCount();
    m_metrics["simd_instructions"] = total_simd_instructions / cpu_count;
    m_metrics["cache_hit_ratio"] =
        (cache_hits + cache_misses) > 0
            ? (cache_hits * 100) / (cache_hits + cache_misses)
            : 0;

    spdlog::trace(
        "CPU diagnostics updated: cycles={}, instr={}, stalls={}, apic_ops={}",
        m_metrics.at("cpu_cycles"),
        m_metrics.at("cpu_instructions"),
        m_metrics.at("pipeline_stalls"),
        m_metrics.at("apic_operations"));
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::UpdateMetricsWithData failed: {}", e.what());
  }
}

/**
 * @brief Resets all diagnostic metrics to default values.
 */
void CPUDiagnostics::ResetMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  m_metrics.clear();
  m_performance_metrics.clear();
  spdlog::info("CPU diagnostics reset");
}

/**
 * @brief Get performance summary
 */
std::string CPUDiagnostics::GetPerformanceSummary() const {
  std::lock_guard<std::mutex> lock(m_mutex);
  std::string summary = "CPU Performance Summary:\n";
  for (const auto &[key, value] : m_metrics) {
    summary += "  " + key + ": " + std::to_string(value) + "\n";
  }
  return summary;
}

/**
 * @brief Export metrics to JSON format
 */
std::string CPUDiagnostics::ExportToJSON() const {
  std::lock_guard<std::mutex> lock(m_mutex);
  std::string json = "{\n";
  bool first = true;
  for (const auto &[key, value] : m_metrics) {
    if (!first)
      json += ",\n";
    json += "  \"" + key + "\": " + std::to_string(value);
    first = false;
  }
  json += "\n}";
  return json;
}

/**
 * @brief Set update interval for automatic metrics collection
 */
void CPUDiagnostics::SetUpdateInterval(uint32_t intervalMs) {
  std::lock_guard<std::mutex> lock(m_mutex);
  m_update_interval_ms = intervalMs;
}

/**
 * @brief Collect basic metrics
 */
void CPUDiagnostics::CollectBasicMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: CollectBasicMetrics called but emulator data not available");
    return;
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectBasicMetrics failed: {}", e.what());
  }
}

/**
 * @brief Collect detailed metrics
 */
void CPUDiagnostics::CollectDetailedMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: CollectDetailedMetrics called but emulator data not available");
    return;

  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectDetailedMetrics failed: {}", e.what());
  }
}

/**
 * @brief Collect verbose metrics
 */
void CPUDiagnostics::CollectVerboseMetrics() {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    // For verbose, include detailed decoded instruction info or other debug data
    // Placeholder: currently same as detailed metrics
    CollectDetailedMetrics();
    // TODO: Add more verbose metrics such as instruction decode traces, etc.
    spdlog::trace("CPU verbose metrics collected (placeholder)");
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectVerboseMetrics failed: {}", e.what());
  }
}

/**
 * @brief Retrieves the current diagnostic metrics.
 * @return Map of metric names to values.
 */
std::unordered_map<std::string, uint64_t>
CPUDiagnostics::GetMetrics(DiagnosticLevel level) const {
  std::lock_guard<std::mutex> lock(m_mutex);
  return m_metrics;
}

/**
 * @brief Logs detailed diagnostic information.
 */
void CPUDiagnostics::LogDiagnostics(DiagnosticLevel level) {
  std::lock_guard<std::mutex> lock(m_mutex);
  spdlog::info("CPU Diagnostics Report:");
  for (const auto &[key, value] : m_metrics) {
    spdlog::info("  {}: {}", key, value);
  }
}
} // namespace x86_64