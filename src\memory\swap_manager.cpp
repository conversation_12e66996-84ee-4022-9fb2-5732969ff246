// Copyright 2025 <Copyright Owner>

#include "memory/swap_manager.h"
#include <algorithm>
#include <chrono>
#include <fstream>
#include <spdlog/spdlog.h>
#include <stdexcept>

// Define PAGE_SIZE for memory page operations
constexpr uint64_t PAGE_SIZE = 4096;

namespace ps4 {

/**
 * @brief Constructs a SwapManager instance.
 * @param swapFilePath Path to the swap file.
 * @param maxSwapSize Maximum swap file size (bytes).
 */
SwapManager::SwapManager(const std::string &swapFilePath, uint64_t maxSwapSize)
    : swapFilePath_(swapFilePath), maxSwapSize_(maxSwapSize),
      currentSwapSize_(0), allocatedSwapSize_(0) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Constructing SwapManager: path='{}', maxSize={:#x}",
               swapFilePath_, maxSwapSize_);
  try {
    swapEntries_.clear();
    freeBlocks_.clear();
    stats_ = SwapStats();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager constructed");
  } catch (const std::exception &e) {
    spdlog::error("SwapManager construction failed: {}", e.what());
    throw;
  }
}

/**
 * @brief Destructor, cleaning up resources.
 */
SwapManager::~SwapManager() {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Destructing SwapManager...");
  try {
    Shutdown();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager destructed");
  } catch (const std::exception &e) {
    spdlog::error("SwapManager destruction failed: {}", e.what());
  }
}

/**
 * @brief Initializes the swap manager.
 * @return True on success, false otherwise.
 */
bool SwapManager::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  spdlog::info("Initializing SwapManager: path='{}'", swapFilePath_);
  try {
    swapFile_.open(swapFilePath_, std::ios::binary | std::ios::in |
                                      std::ios::out | std::ios::trunc);
    if (!swapFile_.is_open()) {
      spdlog::error("Failed to open swap file '{}'", swapFilePath_);
      return false;
    }
    swapEntries_.clear();
    freeBlocks_.clear();
    currentSwapSize_ = 0;
    allocatedSwapSize_ = 0;
    stats_ = SwapStats();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager initialized");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SwapManager initialization failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Shuts down the swap manager.
 */
void SwapManager::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  spdlog::info("Shutting down SwapManager...");
  try {
    if (swapFile_.is_open()) {
      swapFile_.close();
    }
    swapEntries_.clear();
    freeBlocks_.clear();
    currentSwapSize_ = 0;
    allocatedSwapSize_ = 0;
    stats_ = SwapStats();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager shutdown completed");
  } catch (const std::exception &e) {
    spdlog::error("SwapManager shutdown failed: {}", e.what());
  }
}

/**
 * @brief Swaps a page to disk.
 * @param virtAddr Virtual address of the page.
 * @param processId Process ID.
 * @param pageData Page data to write.
 * @return True on success, false otherwise.
 */
bool SwapManager::SwapOut(uint64_t virtAddr, uint64_t processId,
                          const uint8_t *pageData) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  uint64_t pageAlignedAddr = virtAddr & ~(PAGE_SIZE - 1);
  #ifdef DEBUG_SWAP
  spdlog::info("Swapping out page: virt=0x{:x}, process={}", pageAlignedAddr,
               processId);
  #endif
  try {
    auto it =
        std::find_if(swapEntries_.begin(), swapEntries_.end(),
                     [pageAlignedAddr, processId](const SwapEntry &entry) {
                       return entry.virtAddr == pageAlignedAddr &&
                              entry.processId == processId;
                     });
    if (it != swapEntries_.end()) {
      #ifdef DEBUG_SWAP
      spdlog::warn("Page already swapped: 0x{:x}", pageAlignedAddr);
      #endif
      return false;
    }
    uint64_t swapOffset = FindFreeSwapOffset();
    if (swapOffset == 0 || currentSwapSize_ + PAGE_SIZE > maxSwapSize_) {
      #ifdef DEBUG_SWAP
      spdlog::error("Swap file full: current={:#x}, max={:#x}",
                    currentSwapSize_, maxSwapSize_);
      #endif
      stats_.cacheMisses++;
      return false;
    }
    stats_.cacheHits++;

    // PERFORMANCE FIX: Release mutex during blocking I/O to prevent
    // serialization
    lock.unlock();
    swapFile_.seekp(swapOffset, std::ios::beg);
    swapFile_.write(reinterpret_cast<const char *>(pageData), PAGE_SIZE);
    bool writeSuccess = swapFile_.good();
    lock.lock();

    if (!writeSuccess) {
      #ifdef DEBUG_SWAP
      spdlog::error("Failed to write to swap file at offset 0x{:x}",
                    swapOffset);
      #endif
      return false;
    }
    SwapEntry entry{pageAlignedAddr, processId, swapOffset, 0,
                    std::chrono::steady_clock::now()};
    swapEntries_.push_back(entry);
    currentSwapSize_ += PAGE_SIZE;
    stats_.pagesSwapped++;
    stats_.totalBytesSwapped += PAGE_SIZE;
    stats_.swapOutCount++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    #ifdef DEBUG_SWAP
    spdlog::info("Swapped out page: virt=0x{:x}, offset=0x{:x}",
                 pageAlignedAddr, swapOffset);
    #endif
    return true;
  } catch (const std::exception &e) {
    #ifdef DEBUG_SWAP
    spdlog::error("Swap out failed: {}", e.what());
    #endif
    return false;
  }
}

/**
 * @brief Swaps a page back from disk.
 * @param virtAddr Virtual address of the page.
 * @param processId Process ID.
 * @param pageData Buffer for read data.
 * @return True on success, false otherwise.
 */
bool SwapManager::SwapIn(uint64_t virtAddr, uint64_t processId,
                         uint8_t *pageData) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  uint64_t pageAlignedAddr = virtAddr & ~(PAGE_SIZE - 1);
  #ifdef DEBUG_SWAP
  spdlog::info("Swapping in page: virt=0x{:x}, process={}", pageAlignedAddr,
               processId);
  #endif
  try {
    auto it =
        std::find_if(swapEntries_.begin(), swapEntries_.end(),
                     [pageAlignedAddr, processId](const SwapEntry &entry) {
                       return entry.virtAddr == pageAlignedAddr &&
                              entry.processId == processId;
                     });
    if (it == swapEntries_.end()) {
      #ifdef DEBUG_SWAP
      spdlog::warn("Page not swapped: 0x{:x}", pageAlignedAddr);
      #endif
      stats_.cacheMisses++;
      return false;
    }
    uint64_t swapOffset = it->swapOffset;

    // PERFORMANCE FIX: Release mutex during blocking I/O to prevent
    // serialization
    lock.unlock();
    swapFile_.seekg(swapOffset, std::ios::beg);
    swapFile_.read(reinterpret_cast<char *>(pageData), PAGE_SIZE);
    bool readSuccess = swapFile_.good();
    lock.lock();

    if (!readSuccess) {
      #ifdef DEBUG_SWAP
      spdlog::error("Failed to read from swap file at offset 0x{:x}",
                    swapOffset);
      #endif
      return false;
    }

    // Add freed block to free list
    freeBlocks_.insert(swapOffset);

    currentSwapSize_ -= PAGE_SIZE;
    stats_.swapInCount++;
    stats_.totalBytesSwapped -= PAGE_SIZE;
    swapEntries_.erase(it);
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    #ifdef DEBUG_SWAP
    spdlog::info("Swapped in page: virt=0x{:x}, offset=0x{:x}",
                 pageAlignedAddr, swapOffset);
    #endif
    return true;
  } catch (const std::exception &e) {
    #ifdef DEBUG_SWAP
    spdlog::error("Swap in failed: {}", e.what());
    #endif
    return false;
  }
}

/**
 * @brief Checks if a page is swapped out.
 * @param virtAddr Virtual address.
 * @param processId Process ID.
 * @return True if swapped, false otherwise.
 */
bool SwapManager::IsPageSwapped(uint64_t virtAddr, uint64_t processId) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(mutex_);
  uint64_t pageAlignedAddr = virtAddr & ~(PAGE_SIZE - 1);
  spdlog::info("Checking if page is swapped: virt=0x{:x}, process={}",
               pageAlignedAddr, processId);
  try {
    auto it =
        std::find_if(swapEntries_.begin(), swapEntries_.end(),
                     [pageAlignedAddr, processId](const SwapEntry &entry) {
                       return entry.virtAddr == pageAlignedAddr &&
                              entry.processId == processId;
                     });
    bool isSwapped = it != swapEntries_.end();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Page is {} swapped: 0x{:x}", isSwapped ? "" : "not",
                 pageAlignedAddr);
    return isSwapped;
  } catch (const std::exception &e) {
    spdlog::error("IsPageSwapped failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Gets swap usage statistics.
 * @return Swap statistics.
 */
SwapStats SwapManager::GetSwapStats() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  spdlog::info("Getting swap stats...");
  try {
    spdlog::info("Swap stats: pagesSwapped={}, totalBytesSwapped={:#x}",
                 stats_.pagesSwapped, stats_.totalBytesSwapped);
    return stats_;
  } catch (const std::exception &e) {
    spdlog::error("Get swap stats failed: {}", e.what());
    return SwapStats();
  }
}

/**
 * @brief Saves the swap manager state to a stream.
 * @param out Output stream.
 */
void SwapManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(mutex_);
  spdlog::info("Saving SwapManager state...");
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(&stats_), sizeof(stats_));
    out.write(reinterpret_cast<const char *>(&currentSwapSize_),
              sizeof(currentSwapSize_));
    uint64_t entryCount = swapEntries_.size();
    out.write(reinterpret_cast<const char *>(&entryCount), sizeof(entryCount));
    for (const auto &entry : swapEntries_) {
      out.write(reinterpret_cast<const char *>(&entry.virtAddr),
                sizeof(entry.virtAddr));
      out.write(reinterpret_cast<const char *>(&entry.processId),
                sizeof(entry.processId));
      out.write(reinterpret_cast<const char *>(&entry.swapOffset),
                sizeof(entry.swapOffset));
      out.write(reinterpret_cast<const char *>(&entry.accessCount),
                sizeof(entry.accessCount));
      uint64_t timestampUs =
          std::chrono::duration_cast<std::chrono::microseconds>(
              entry.lastAccessTime.time_since_epoch())
              .count();
      out.write(reinterpret_cast<const char *>(&timestampUs),
                sizeof(timestampUs));
    }
    if (!out.good()) {
      throw std::runtime_error("Failed to write SwapManager state");
    }
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager state saved: {} entries", entryCount);
  } catch (const std::exception &e) {
    spdlog::error("SwapManager SaveState failed: {}", e.what());
  }
}

/**
 * @brief Loads the swap manager state from a stream.
 * @param in Input stream.
 */
void SwapManager::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(mutex_);
  spdlog::info("Loading SwapManager state...");
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported SwapManager state version: {}", version);
      throw std::runtime_error("Invalid SwapManager state version");
    }
    swapEntries_.clear();
    in.read(reinterpret_cast<char *>(&stats_), sizeof(stats_));
    in.read(reinterpret_cast<char *>(&currentSwapSize_),
            sizeof(currentSwapSize_));
    uint64_t entryCount;
    in.read(reinterpret_cast<char *>(&entryCount), sizeof(entryCount));
    for (uint64_t i = 0; i < entryCount && in.good(); ++i) {
      SwapEntry entry;
      in.read(reinterpret_cast<char *>(&entry.virtAddr),
              sizeof(entry.virtAddr));
      in.read(reinterpret_cast<char *>(&entry.processId),
              sizeof(entry.processId));
      in.read(reinterpret_cast<char *>(&entry.swapOffset),
              sizeof(entry.swapOffset));
      in.read(reinterpret_cast<char *>(&entry.accessCount),
              sizeof(entry.accessCount));
      uint64_t timestampUs;
      in.read(reinterpret_cast<char *>(&timestampUs), sizeof(timestampUs));
      entry.lastAccessTime = std::chrono::steady_clock::time_point(
          std::chrono::microseconds(timestampUs));
      swapEntries_.push_back(entry);
    }
    if (!in.good()) {
      throw std::runtime_error("Failed to read SwapManager state");
    }
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SwapManager state loaded: {} entries", entryCount);
  } catch (const std::exception &e) {
    spdlog::error("SwapManager LoadState failed: {}", e.what());
    swapEntries_.clear();
    currentSwapSize_ = 0;
    stats_ = SwapStats();
  }
}

/**
 * @brief Expands the swap file size.
 * @param newSize New swap file size.
 * @return True on success, false otherwise.
 */
bool SwapManager::ExpandSwapFile(uint64_t newSize) {
  if (newSize <= allocatedSwapSize_ || newSize > maxSwapSize_) {
    return false;
  }

  try {
    // Expand file by seeking to new size and writing a byte
    swapFile_.seekp(newSize - 1, std::ios::beg);
    swapFile_.put(0);
    swapFile_.flush();

    if (!swapFile_.good()) {
      spdlog::error("Failed to expand swap file to size {:#x}", newSize);
      return false;
    }

    spdlog::info("Expanded swap file from {:#x} to {:#x}", allocatedSwapSize_, newSize);
    allocatedSwapSize_ = newSize;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Swap file expansion failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Finds a free offset in the swap file.
 * @return Offset, or 0 if none available.
 */
uint64_t SwapManager::FindFreeSwapOffset() const {
  auto start = std::chrono::steady_clock::now();
  #ifdef DEBUG_SWAP
  spdlog::debug("Finding free swap offset: currentSize={:#x}, allocatedSize={:#x}",
                currentSwapSize_, allocatedSwapSize_);
  #endif

  try {
    // First check if we have any freed blocks
    if (!freeBlocks_.empty()) {
      uint64_t offset = *freeBlocks_.begin();
      freeBlocks_.erase(freeBlocks_.begin());
      #ifdef DEBUG_SWAP
      spdlog::debug("Reusing freed block at offset: 0x{:x}", offset);
      #endif
      return offset;
    }

    // Check if we can allocate at the end of currently used space
    uint64_t offset = allocatedSwapSize_;

    // If we need to expand the file
    if (offset + PAGE_SIZE > allocatedSwapSize_) {
      // Calculate new size (expand by 1MB chunks or remaining space, whichever is smaller)
      constexpr uint64_t EXPANSION_SIZE = 1024 * 1024; // 1MB
      uint64_t newSize = std::min(allocatedSwapSize_ + EXPANSION_SIZE, maxSwapSize_);

      // Ensure we have enough space for at least one page
      if (newSize < offset + PAGE_SIZE) {
        newSize = std::min(offset + PAGE_SIZE, maxSwapSize_);
      }

      if (newSize > maxSwapSize_ || newSize <= allocatedSwapSize_) {
        spdlog::warn("Cannot expand swap file: would exceed max size {:#x}", maxSwapSize_);
        return 0;
      }

      // Temporarily cast away const to expand file (this is safe as we're the only writer)
      const_cast<SwapManager*>(this)->ExpandSwapFile(newSize);
    }

    // Final check if we have space
    if (offset + PAGE_SIZE > maxSwapSize_) {
      spdlog::warn("No free offset: offset=0x{:x}, max={:#x}", offset, maxSwapSize_);
      return 0;
    }

    auto end = std::chrono::steady_clock::now();
    const_cast<SwapManager*>(this)->stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();

    #ifdef DEBUG_SWAP
    spdlog::debug("Allocated new block at offset: 0x{:x}", offset);
    #endif
    return offset;

  } catch (const std::exception &e) {
    spdlog::error("Find free swap offset failed: {}", e.what());
    return 0;
  }
}

} // namespace ps4