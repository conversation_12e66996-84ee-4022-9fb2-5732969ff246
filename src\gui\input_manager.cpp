#include "input_manager.h"
#include <chrono>
#include <spdlog/spdlog.h>


namespace ps4 {
InputManager::InputManager(PS4ControllerManager &controllerManager)
    : m_controller<PERSON>anager(controllerManager), m_eventCount(0),
      m_totalLatencyUs(0) {
  spdlog::info("InputManager constructed");
}

InputManager::~InputManager() { spdlog::info("InputManager destructed"); }

bool InputManager::Initialize() {
  // PS4ControllerManager handles SDL initialization
  spdlog::info("InputManager initialized");
  return true;
}

void InputManager::Update() {
  auto start = std::chrono::high_resolution_clock::now();
  m_controllerManager.ProcessEvents();
  auto stats =
      m_controllerManager.GetControllerStats(); // Corrected from GetStats
  m_eventCount += stats.eventCount;
  m_totalLatencyUs +=
      stats.latencyUs; // Adjusted to match ControllerStats structure
  auto end = std::chrono::high_resolution_clock::now();
  m_totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}
void InputManager::PollEvents() // Corrected to member function
{
  // SDL event polling is now centralized to the main thread to prevent race
  // conditions. This method now just updates internal stats without triggering
  // SDL event polling.
  auto start = std::chrono::high_resolution_clock::now();
  auto controllerStats = m_controllerManager.GetControllerStats();
  m_eventCount = controllerStats.eventCount;
  m_totalLatencyUs = controllerStats.latencyUs;
  auto end = std::chrono::high_resolution_clock::now();
  m_totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

InputStats InputManager::GetStats() const {
  return {m_eventCount, m_totalLatencyUs};
}

ControllerState InputManager::GetControllerState(int controllerIndex) const {
  return m_controllerManager.GetControllerState(controllerIndex);
}

bool InputManager::IsControllerConnected(int controllerIndex) const {
  return m_controllerManager.IsControllerConnected(controllerIndex);
}
} // namespace ps4