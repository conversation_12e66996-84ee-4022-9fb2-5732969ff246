<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_CONDATTR_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_condattr_init, pthread_condattr_destroy - condition
creation 
</P>
<P>attributes 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_condattr_init(pthread_condattr_t *</B><I>attr</I><B>);</B>
</P>
<P><B>int pthread_condattr_destroy(pthread_condattr_t *</B><I>attr</I><B>);</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Condition attributes can be specified at condition creation time,
by passing a condition attribute object as second argument to
<A HREF="pthread_cond_init.html"><B>pthread_cond_init</B>(3)</A> .
Passing <B>NULL</B> is equivalent to passing a condition attribute
object with all attributes set to their default values. 
</P>
<P><B>pthread_condattr_init</B> initializes the condition attribute
object <I>attr</I> and fills it with default values for the
attributes. <B>pthread_condattr_destroy</B> destroys a condition
attribute object, which must not be reused until it is reinitialized.</P>
<P><B>PThreads4W</B> defines _<B>POSIX_THREAD_PROCESS_SHARED</B> in
pthread.h as -1 to indicate that the attribute routines are
implemented but that the process shared attribute is not supported.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>All condition variable functions return 0 on success and a
non-zero error code on error.</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_condattr_init</B> function returns the following
error code on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ENOMEM</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The was insufficient memory to create the attribute.<SPAN STYLE="font-weight: medium">
		</SPAN>
		</DD>
</DL>
<P>
The <B>pthread_condattr_destroy</B> function returns the following
error code on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>attr</I> argument is not valid.<SPAN STYLE="font-weight: medium">
		</SPAN>
		</DD>
</DL>
<H2>
<A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="pthread_cond_init.html"><B>pthread_cond_init</B>(3)</A> .
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Author</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>
