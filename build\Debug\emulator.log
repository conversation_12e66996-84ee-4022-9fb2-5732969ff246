[2025-07-06 01:50:09.805] [info] Starting PS4 Emulator
[2025-07-06 01:50:09.807] [info] LoadSettings: Loaded from C:\Users\<USER>\AppData\Roaming\PS4Emulator\settings.ini, latency=1135us
[2025-07-06 01:50:10.078] [info] Vulkan context initialization complete, waiting for device idle...
[2025-07-06 01:50:10.078] [info] Vulkan device idle, starting emulator initialization
[2025-07-06 01:50:10.078] [info] PS4Emulator constructed
[2025-07-06 01:50:10.079] [info] Initializing PS4 emulator asynchronously
[2025-07-06 01:50:10.086] [info] Applied display settings: width=1938, height=1084, fullscreen=false, ui_scale=1.046
[2025-07-06 01:50:10.179] [info] === PS4 Emulator Initialization Started ===
[2025-07-06 01:50:10.179] [info] Step 1/10: Validating system requirements...
[2025-07-06 01:50:10.180] [warning] Low disk space: 10627768320 bytes available
[2025-07-06 01:50:10.180] [info] Step 2/10: Creating component instances...
[2025-07-06 01:50:10.180] [info] Constructing PS4MMU (default)...
[2025-07-06 01:50:10.180] [info] Attempting to allocate 1024 MB of physical memory...
[2025-07-06 01:50:10.379] [info] Successfully allocated 1024 MB of physical memory
[2025-07-06 01:50:10.380] [info] Constructing PhysicalMemoryAllocator...
[2025-07-06 01:50:10.380] [info] PhysicalMemoryAllocator constructed with empty block lists
[2025-07-06 01:50:10.380] [info] Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-06 01:50:10.380] [info] Initialized with free block: start=0x1000, size=0x3ffff000 (reserved 0x0-0x1000)
[2025-07-06 01:50:10.380] [info] Constructing SwapManager: path='ps4_swap.bin', maxSize=0x20000000
[2025-07-06 01:50:10.380] [info] SwapManager constructed
[2025-07-06 01:50:10.380] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.380] [debug] All compressed pages cleared
[2025-07-06 01:50:10.380] [info] Memory compressor initialized with algorithm 0 and policy 1
[2025-07-06 01:50:10.380] [info] Initializing SwapManager: path='ps4_swap.bin'
[2025-07-06 01:50:10.380] [info] SwapManager initialized
[2025-07-06 01:50:10.381] [info] PS4MMU: Initialized memory stats - total pages: 262144, free pages: 262144
[2025-07-06 01:50:10.381] [info] PS4MMU default constructed with size: 0x40000000 bytes
[2025-07-06 01:50:10.381] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.381] [info] OrbisOS constructed
[2025-07-06 01:50:10.381] [info] PS4Filesystem constructed
[2025-07-06 01:50:10.381] [info] SyscallHandler constructed
[2025-07-06 01:50:10.382] [info] PS4ControllerManager constructed
[2025-07-06 01:50:10.382] [info] AudioDevice constructed
[2025-07-06 01:50:10.382] [info] PS4Audio constructed
[2025-07-06 01:50:10.382] [info] PS4TSC constructed
[2025-07-06 01:50:10.382] [info] GNMShaderTranslator constructed
[2025-07-06 01:50:10.382] [info] GNMRegisterState constructed with zero-initialized registers and stats.
[2025-07-06 01:50:10.382] [debug] GNMShaderTranslator: SPIR-V callback set
[2025-07-06 01:50:10.382] [debug] GNMShaderTranslator: GLSL callback set
[2025-07-06 01:50:10.382] [debug] GNMRegisterState: Register change callback set.
[2025-07-06 01:50:10.382] [info] PS4GPU constructed
[2025-07-06 01:50:10.382] [info] CommandProcessor constructed with zero-initialized stats
[2025-07-06 01:50:10.382] [info] FiberManager constructed
[2025-07-06 01:50:10.383] [info] TrophyManager constructed
[2025-07-06 01:50:10.383] [info] ZlibWrapper constructed
[2025-07-06 01:50:10.383] [error] ReadFile: Invalid path: /system/packages.json
[2025-07-06 01:50:10.383] [info] PKGInstaller initialized with filesystem, install root: /app
[2025-07-06 01:50:10.383] [info] JIT compiler initialized for CPU 0x2da9f300 with zero-initialized stats
[2025-07-06 01:50:10.383] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.383] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.384] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.384] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.384] [info] JIT compiler initialized for CPU 0x2da9f300 with zero-initialized stats
[2025-07-06 01:50:10.384] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.384] [info] Pipeline statistics reset
[2025-07-06 01:50:10.384] [info] Initialized 7 execution units
[2025-07-06 01:50:10.384] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.384] [info] Pipeline initialized for CPU at 0x2da9f300 with 7 execution units
[2025-07-06 01:50:10.384] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.384] [info] X86_64CPU[0]: Starting register initialization
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Initializing SIMD registers
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.385] [info] X86_64CPU[0]: Creating devices
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Creating PIC
[2025-07-06 01:50:10.385] [info] PIC constructed
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Creating PIT
[2025-07-06 01:50:10.385] [info] PIT constructed
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Creating APIC
[2025-07-06 01:50:10.385] [info] APIC initialized for core 0
[2025-07-06 01:50:10.385] [info] X86_64CPU[0]: Registering devices
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Registering PIC at 0x20
[2025-07-06 01:50:10.385] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Registering PIT at 0x40
[2025-07-06 01:50:10.385] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.385] [debug] X86_64CPU[0]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.385] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.385] [info] X86_64CPU[0] created with 16 XMM registers
[2025-07-06 01:50:10.385] [info] JIT compiler initialized for CPU 0x2da9f300 with zero-initialized stats
[2025-07-06 01:50:10.385] [info] JIT compiler initialized for CPU 0x2db14040 with zero-initialized stats
[2025-07-06 01:50:10.386] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.386] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.386] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.386] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.386] [info] JIT compiler initialized for CPU 0x2db14040 with zero-initialized stats
[2025-07-06 01:50:10.386] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.386] [info] Pipeline statistics reset
[2025-07-06 01:50:10.386] [info] Initialized 7 execution units
[2025-07-06 01:50:10.386] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.386] [info] Pipeline initialized for CPU at 0x2db14040 with 7 execution units
[2025-07-06 01:50:10.386] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.387] [info] X86_64CPU[1]: Starting register initialization
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Initializing SIMD registers
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.387] [info] X86_64CPU[1]: Creating devices
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Creating PIC
[2025-07-06 01:50:10.387] [info] PIC constructed
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Creating PIT
[2025-07-06 01:50:10.387] [info] PIT constructed
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Creating APIC
[2025-07-06 01:50:10.387] [info] APIC initialized for core 1
[2025-07-06 01:50:10.387] [info] X86_64CPU[1]: Registering devices
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Registering PIC at 0x20
[2025-07-06 01:50:10.387] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Registering PIT at 0x40
[2025-07-06 01:50:10.387] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.387] [debug] X86_64CPU[1]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.387] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.388] [info] X86_64CPU[1] created with 16 XMM registers
[2025-07-06 01:50:10.388] [info] JIT compiler initialized for CPU 0x2db14040 with zero-initialized stats
[2025-07-06 01:50:10.388] [info] JIT compiler initialized for CPU 0x2db4ed40 with zero-initialized stats
[2025-07-06 01:50:10.388] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.388] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.388] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.388] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.388] [info] JIT compiler initialized for CPU 0x2db4ed40 with zero-initialized stats
[2025-07-06 01:50:10.388] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.388] [info] Pipeline statistics reset
[2025-07-06 01:50:10.389] [info] Initialized 7 execution units
[2025-07-06 01:50:10.389] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.389] [info] Pipeline initialized for CPU at 0x2db4ed40 with 7 execution units
[2025-07-06 01:50:10.389] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.389] [info] X86_64CPU[2]: Starting register initialization
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: Initializing SIMD registers
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.389] [info] X86_64CPU[2]: Creating devices
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: Creating PIC
[2025-07-06 01:50:10.389] [info] PIC constructed
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: Creating PIT
[2025-07-06 01:50:10.389] [info] PIT constructed
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: Creating APIC
[2025-07-06 01:50:10.389] [info] APIC initialized for core 2
[2025-07-06 01:50:10.389] [info] X86_64CPU[2]: Registering devices
[2025-07-06 01:50:10.389] [debug] X86_64CPU[2]: Registering PIC at 0x20
[2025-07-06 01:50:10.389] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.390] [debug] X86_64CPU[2]: Registering PIT at 0x40
[2025-07-06 01:50:10.390] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.390] [debug] X86_64CPU[2]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.390] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.390] [info] X86_64CPU[2] created with 16 XMM registers
[2025-07-06 01:50:10.390] [info] JIT compiler initialized for CPU 0x2db4ed40 with zero-initialized stats
[2025-07-06 01:50:10.390] [info] JIT compiler initialized for CPU 0x2db99600 with zero-initialized stats
[2025-07-06 01:50:10.390] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.390] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.390] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.390] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.390] [info] JIT compiler initialized for CPU 0x2db99600 with zero-initialized stats
[2025-07-06 01:50:10.392] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.392] [info] Pipeline statistics reset
[2025-07-06 01:50:10.392] [info] Initialized 7 execution units
[2025-07-06 01:50:10.393] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.393] [info] Pipeline initialized for CPU at 0x2db99600 with 7 execution units
[2025-07-06 01:50:10.393] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.393] [info] X86_64CPU[3]: Starting register initialization
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Initializing SIMD registers
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.393] [info] X86_64CPU[3]: Creating devices
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Creating PIC
[2025-07-06 01:50:10.393] [info] PIC constructed
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Creating PIT
[2025-07-06 01:50:10.393] [info] PIT constructed
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Creating APIC
[2025-07-06 01:50:10.393] [info] APIC initialized for core 3
[2025-07-06 01:50:10.393] [info] X86_64CPU[3]: Registering devices
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Registering PIC at 0x20
[2025-07-06 01:50:10.393] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.393] [debug] X86_64CPU[3]: Registering PIT at 0x40
[2025-07-06 01:50:10.393] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.394] [debug] X86_64CPU[3]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.394] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.394] [info] X86_64CPU[3] created with 16 XMM registers
[2025-07-06 01:50:10.394] [info] JIT compiler initialized for CPU 0x2db99600 with zero-initialized stats
[2025-07-06 01:50:10.394] [info] JIT compiler initialized for CPU 0x2dbda0c0 with zero-initialized stats
[2025-07-06 01:50:10.394] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.394] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.394] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.394] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.394] [info] JIT compiler initialized for CPU 0x2dbda0c0 with zero-initialized stats
[2025-07-06 01:50:10.394] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.395] [info] Pipeline statistics reset
[2025-07-06 01:50:10.395] [info] Initialized 7 execution units
[2025-07-06 01:50:10.395] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.395] [info] Pipeline initialized for CPU at 0x2dbda0c0 with 7 execution units
[2025-07-06 01:50:10.395] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.395] [info] X86_64CPU[4]: Starting register initialization
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Initializing SIMD registers
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.395] [info] X86_64CPU[4]: Creating devices
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Creating PIC
[2025-07-06 01:50:10.395] [info] PIC constructed
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Creating PIT
[2025-07-06 01:50:10.395] [info] PIT constructed
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Creating APIC
[2025-07-06 01:50:10.395] [info] APIC initialized for core 4
[2025-07-06 01:50:10.395] [info] X86_64CPU[4]: Registering devices
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Registering PIC at 0x20
[2025-07-06 01:50:10.395] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.395] [debug] X86_64CPU[4]: Registering PIT at 0x40
[2025-07-06 01:50:10.396] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.396] [debug] X86_64CPU[4]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.396] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.396] [info] X86_64CPU[4] created with 16 XMM registers
[2025-07-06 01:50:10.396] [info] JIT compiler initialized for CPU 0x2dbda0c0 with zero-initialized stats
[2025-07-06 01:50:10.396] [info] JIT compiler initialized for CPU 0x2dbfb580 with zero-initialized stats
[2025-07-06 01:50:10.396] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.396] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.396] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.397] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.397] [info] JIT compiler initialized for CPU 0x2dbfb580 with zero-initialized stats
[2025-07-06 01:50:10.397] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.397] [info] Pipeline statistics reset
[2025-07-06 01:50:10.397] [info] Initialized 7 execution units
[2025-07-06 01:50:10.397] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.397] [info] Pipeline initialized for CPU at 0x2dbfb580 with 7 execution units
[2025-07-06 01:50:10.397] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.397] [info] X86_64CPU[5]: Starting register initialization
[2025-07-06 01:50:10.397] [debug] X86_64CPU[5]: Initializing SIMD registers
[2025-07-06 01:50:10.397] [debug] X86_64CPU[5]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.397] [info] X86_64CPU[5]: Creating devices
[2025-07-06 01:50:10.397] [debug] X86_64CPU[5]: Creating PIC
[2025-07-06 01:50:10.397] [info] PIC constructed
[2025-07-06 01:50:10.397] [debug] X86_64CPU[5]: Creating PIT
[2025-07-06 01:50:10.397] [info] PIT constructed
[2025-07-06 01:50:10.397] [debug] X86_64CPU[5]: Creating APIC
[2025-07-06 01:50:10.397] [info] APIC initialized for core 5
[2025-07-06 01:50:10.398] [info] X86_64CPU[5]: Registering devices
[2025-07-06 01:50:10.398] [debug] X86_64CPU[5]: Registering PIC at 0x20
[2025-07-06 01:50:10.398] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.398] [debug] X86_64CPU[5]: Registering PIT at 0x40
[2025-07-06 01:50:10.398] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.398] [debug] X86_64CPU[5]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.398] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.398] [info] X86_64CPU[5] created with 16 XMM registers
[2025-07-06 01:50:10.398] [info] JIT compiler initialized for CPU 0x2dbfb580 with zero-initialized stats
[2025-07-06 01:50:10.398] [info] JIT compiler initialized for CPU 0x2dc5d180 with zero-initialized stats
[2025-07-06 01:50:10.398] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.398] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.398] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.399] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.399] [info] JIT compiler initialized for CPU 0x2dc5d180 with zero-initialized stats
[2025-07-06 01:50:10.399] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.399] [info] Pipeline statistics reset
[2025-07-06 01:50:10.399] [info] Initialized 7 execution units
[2025-07-06 01:50:10.399] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.399] [info] Pipeline initialized for CPU at 0x2dc5d180 with 7 execution units
[2025-07-06 01:50:10.399] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.400] [info] X86_64CPU[6]: Starting register initialization
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: Initializing SIMD registers
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.400] [info] X86_64CPU[6]: Creating devices
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: Creating PIC
[2025-07-06 01:50:10.400] [info] PIC constructed
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: Creating PIT
[2025-07-06 01:50:10.400] [info] PIT constructed
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: Creating APIC
[2025-07-06 01:50:10.400] [info] APIC initialized for core 6
[2025-07-06 01:50:10.400] [info] X86_64CPU[6]: Registering devices
[2025-07-06 01:50:10.400] [debug] X86_64CPU[6]: Registering PIC at 0x20
[2025-07-06 01:50:10.400] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.401] [debug] X86_64CPU[6]: Registering PIT at 0x40
[2025-07-06 01:50:10.401] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.401] [debug] X86_64CPU[6]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.401] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.401] [info] X86_64CPU[6] created with 16 XMM registers
[2025-07-06 01:50:10.401] [info] JIT compiler initialized for CPU 0x2dc5d180 with zero-initialized stats
[2025-07-06 01:50:10.401] [info] JIT compiler initialized for CPU 0x2dc70e00 with zero-initialized stats
[2025-07-06 01:50:10.401] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.401] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.401] [info] InstructionDecoder constructed with lazy initialization
[2025-07-06 01:50:10.402] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-06 01:50:10.402] [info] JIT compiler initialized for CPU 0x2dc70e00 with zero-initialized stats
[2025-07-06 01:50:10.402] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-06 01:50:10.402] [info] Pipeline statistics reset
[2025-07-06 01:50:10.402] [info] Initialized 7 execution units
[2025-07-06 01:50:10.402] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-06 01:50:10.402] [info] Pipeline initialized for CPU at 0x2dc70e00 with 7 execution units
[2025-07-06 01:50:10.402] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.402] [info] X86_64CPU[7]: Starting register initialization
[2025-07-06 01:50:10.402] [debug] X86_64CPU[7]: Initializing SIMD registers
[2025-07-06 01:50:10.402] [debug] X86_64CPU[7]: AVX-512 not supported, registers zero-initialized
[2025-07-06 01:50:10.402] [info] X86_64CPU[7]: Creating devices
[2025-07-06 01:50:10.402] [debug] X86_64CPU[7]: Creating PIC
[2025-07-06 01:50:10.402] [info] PIC constructed
[2025-07-06 01:50:10.402] [debug] X86_64CPU[7]: Creating PIT
[2025-07-06 01:50:10.402] [info] PIT constructed
[2025-07-06 01:50:10.402] [debug] X86_64CPU[7]: Creating APIC
[2025-07-06 01:50:10.403] [info] APIC initialized for core 7
[2025-07-06 01:50:10.403] [info] X86_64CPU[7]: Registering devices
[2025-07-06 01:50:10.403] [debug] X86_64CPU[7]: Registering PIC at 0x20
[2025-07-06 01:50:10.403] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-06 01:50:10.403] [debug] X86_64CPU[7]: Registering PIT at 0x40
[2025-07-06 01:50:10.403] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-06 01:50:10.403] [debug] X86_64CPU[7]: Registering APIC at 0xFEE00000
[2025-07-06 01:50:10.403] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-06 01:50:10.403] [info] X86_64CPU[7] created with 16 XMM registers
[2025-07-06 01:50:10.403] [info] JIT compiler initialized for CPU 0x2dc70e00 with zero-initialized stats
[2025-07-06 01:50:10.403] [info] InterruptHandler initialized for CPU 0
[2025-07-06 01:50:10.403] [info] Step 3/10: Initializing core components...
[2025-07-06 01:50:10.404] [info] Initializing PS4MMU...
[2025-07-06 01:50:10.404] [info] PS4MMU: Allocating physical memory buffer of size 0x40000000 bytes
[2025-07-06 01:50:10.404] [info] PS4MMU: Physical memory buffer allocated successfully
[2025-07-06 01:50:10.404] [info] Constructing PhysicalMemoryAllocator...
[2025-07-06 01:50:10.404] [info] PhysicalMemoryAllocator constructed with empty block lists
[2025-07-06 01:50:10.404] [info] PS4MMU: Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-06 01:50:10.404] [info] Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-06 01:50:10.404] [info] Initialized with free block: start=0x1000, size=0x3ffff000 (reserved 0x0-0x1000)
[2025-07-06 01:50:10.404] [info] PS4MMU: PhysicalMemoryAllocator initialized successfully
[2025-07-06 01:50:10.404] [info] PS4MMU: Testing PhysicalMemoryAllocator with a small allocation...
[2025-07-06 01:50:10.404] [info] PS4MMU: Test allocation successful, got address 0x1000
[2025-07-06 01:50:10.404] [info] PS4MMU: Test free successful
[2025-07-06 01:50:10.405] [info] Memory prefetcher initialized
[2025-07-06 01:50:10.405] [debug] All compressed pages cleared
[2025-07-06 01:50:10.405] [info] Memory compressor initialized with algorithm 0 and policy 1
[2025-07-06 01:50:10.405] [info] Constructing SwapManager: path='ps4_swap.bin', maxSize=0x20000000
[2025-07-06 01:50:10.405] [info] SwapManager constructed
[2025-07-06 01:50:10.405] [info] Destructing SwapManager...
[2025-07-06 01:50:10.405] [info] Shutting down SwapManager...
[2025-07-06 01:50:10.405] [info] SwapManager shutdown completed
[2025-07-06 01:50:10.405] [info] SwapManager destructed
[2025-07-06 01:50:10.405] [info] Initializing SwapManager: path='ps4_swap.bin'
[2025-07-06 01:50:10.405] [info] SwapManager initialized
[2025-07-06 01:50:10.406] [info] TLB initialized with max entries: 64
[2025-07-06 01:50:10.406] [info] PS4MMU: Initialized memory stats - total pages: 262144, free pages: 262144
[2025-07-06 01:50:10.406] [info] PS4MMU initialized with size: 0x40000000 bytes
[2025-07-06 01:50:10.406] [info] MMU initialized successfully
[2025-07-06 01:50:10.406] [info] PS4TSC calibrated: elapsed=1200ns, increment=12 cycles, host_freq=10000000 Hz, drift=1200.00%, latency=0us
[2025-07-06 01:50:10.406] [info] PS4TSC initialized with CPU frequency: 10000000 Hz, latency=68us
[2025-07-06 01:50:10.406] [info] TSC initialized successfully
[2025-07-06 01:50:10.408] [info] Initializing Filesystem with 45s timeout...
[2025-07-06 01:50:10.408] [info] Mounted ./ps4_root to /app0
[2025-07-06 01:50:10.408] [info] Mounted ./ps4_root\installed_packages to /mnt/sandbox/pfsmnt
[2025-07-06 01:50:10.409] [info] Mounted ./ps4_root\dev to /dev
[2025-07-06 01:50:10.409] [info] Mounted ./ps4_root\savedata to /savedata
[2025-07-06 01:50:10.409] [info] Mounted ./ps4_root\trophy to /trophy
[2025-07-06 01:50:10.409] [info] Mounted ./ps4_root\system to /system
[2025-07-06 01:50:10.409] [info] Initializing device files...
[2025-07-06 01:50:10.409] [info] Starting device file initialization...
[2025-07-06 01:50:10.409] [info] Creating standard device files...
[2025-07-06 01:50:10.409] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/null'
[2025-07-06 01:50:10.409] [debug] MapToHostPathLocked: Starting mapping for '/dev/null'
[2025-07-06 01:50:10.409] [debug] GetHostPath: Constructed path './ps4_root\dev\null' from mount './ps4_root\dev' + remaining 'null'
[2025-07-06 01:50:10.409] [debug] MapToHostPathLocked: virtualPath='/dev/null', mountPoint result='./ps4_root\dev\null'
[2025-07-06 01:50:10.409] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\null'
[2025-07-06 01:50:10.409] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\null' for '/dev/null'
[2025-07-06 01:50:10.409] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/null'
[2025-07-06 01:50:10.410] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/zero'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: Starting mapping for '/dev/zero'
[2025-07-06 01:50:10.410] [debug] GetHostPath: Constructed path './ps4_root\dev\zero' from mount './ps4_root\dev' + remaining 'zero'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: virtualPath='/dev/zero', mountPoint result='./ps4_root\dev\zero'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\zero'
[2025-07-06 01:50:10.410] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\zero' for '/dev/zero'
[2025-07-06 01:50:10.410] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/zero'
[2025-07-06 01:50:10.410] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/random'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: Starting mapping for '/dev/random'
[2025-07-06 01:50:10.410] [debug] GetHostPath: Constructed path './ps4_root\dev\random' from mount './ps4_root\dev' + remaining 'random'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: virtualPath='/dev/random', mountPoint result='./ps4_root\dev\random'
[2025-07-06 01:50:10.410] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\random'
[2025-07-06 01:50:10.410] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\random' for '/dev/random'
[2025-07-06 01:50:10.411] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/random'
[2025-07-06 01:50:10.411] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/urandom'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: Starting mapping for '/dev/urandom'
[2025-07-06 01:50:10.411] [debug] GetHostPath: Constructed path './ps4_root\dev\urandom' from mount './ps4_root\dev' + remaining 'urandom'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: virtualPath='/dev/urandom', mountPoint result='./ps4_root\dev\urandom'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\urandom'
[2025-07-06 01:50:10.411] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\urandom' for '/dev/urandom'
[2025-07-06 01:50:10.411] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/urandom'
[2025-07-06 01:50:10.411] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stdout'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: Starting mapping for '/dev/stdout'
[2025-07-06 01:50:10.411] [debug] GetHostPath: Constructed path './ps4_root\dev\stdout' from mount './ps4_root\dev' + remaining 'stdout'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: virtualPath='/dev/stdout', mountPoint result='./ps4_root\dev\stdout'
[2025-07-06 01:50:10.411] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stdout'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stdout' for '/dev/stdout'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stdout'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stderr'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: Starting mapping for '/dev/stderr'
[2025-07-06 01:50:10.412] [debug] GetHostPath: Constructed path './ps4_root\dev\stderr' from mount './ps4_root\dev' + remaining 'stderr'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: virtualPath='/dev/stderr', mountPoint result='./ps4_root\dev\stderr'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stderr'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stderr' for '/dev/stderr'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stderr'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stdin'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: Starting mapping for '/dev/stdin'
[2025-07-06 01:50:10.412] [debug] GetHostPath: Constructed path './ps4_root\dev\stdin' from mount './ps4_root\dev' + remaining 'stdin'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: virtualPath='/dev/stdin', mountPoint result='./ps4_root\dev\stdin'
[2025-07-06 01:50:10.412] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stdin'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stdin' for '/dev/stdin'
[2025-07-06 01:50:10.412] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stdin'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/console'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: Starting mapping for '/dev/console'
[2025-07-06 01:50:10.413] [debug] GetHostPath: Constructed path './ps4_root\dev\console' from mount './ps4_root\dev' + remaining 'console'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: virtualPath='/dev/console', mountPoint result='./ps4_root\dev\console'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\console'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\console' for '/dev/console'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/console'
[2025-07-06 01:50:10.413] [info] Created standard I/O devices
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/dipsw'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: Starting mapping for '/dev/dipsw'
[2025-07-06 01:50:10.413] [debug] GetHostPath: Constructed path './ps4_root\dev\dipsw' from mount './ps4_root\dev' + remaining 'dipsw'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: virtualPath='/dev/dipsw', mountPoint result='./ps4_root\dev\dipsw'
[2025-07-06 01:50:10.413] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\dipsw'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\dipsw' for '/dev/dipsw'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/dipsw'
[2025-07-06 01:50:10.413] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/hid'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: Starting mapping for '/dev/hid'
[2025-07-06 01:50:10.414] [debug] GetHostPath: Constructed path './ps4_root\dev\hid' from mount './ps4_root\dev' + remaining 'hid'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: virtualPath='/dev/hid', mountPoint result='./ps4_root\dev\hid'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\hid'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\hid' for '/dev/hid'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/hid'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/gc'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: Starting mapping for '/dev/gc'
[2025-07-06 01:50:10.414] [debug] GetHostPath: Constructed path './ps4_root\dev\gc' from mount './ps4_root\dev' + remaining 'gc'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: virtualPath='/dev/gc', mountPoint result='./ps4_root\dev\gc'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\gc'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\gc' for '/dev/gc'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/gc'
[2025-07-06 01:50:10.414] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/rng'
[2025-07-06 01:50:10.414] [debug] MapToHostPathLocked: Starting mapping for '/dev/rng'
[2025-07-06 01:50:10.415] [debug] GetHostPath: Constructed path './ps4_root\dev\rng' from mount './ps4_root\dev' + remaining 'rng'
[2025-07-06 01:50:10.415] [debug] MapToHostPathLocked: virtualPath='/dev/rng', mountPoint result='./ps4_root\dev\rng'
[2025-07-06 01:50:10.415] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\rng'
[2025-07-06 01:50:10.415] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\rng' for '/dev/rng'
[2025-07-06 01:50:10.415] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/rng'
[2025-07-06 01:50:10.415] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_icc_mgr'
[2025-07-06 01:50:10.415] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_icc_mgr'
[2025-07-06 01:50:10.415] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_icc_mgr' from mount './ps4_root\dev' + remaining 'sbl_icc_mgr'
[2025-07-06 01:50:10.415] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_icc_mgr', mountPoint result='./ps4_root\dev\sbl_icc_mgr'
[2025-07-06 01:50:10.415] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_icc_mgr'
[2025-07-06 01:50:10.415] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_icc_mgr' for '/dev/sbl_icc_mgr'
[2025-07-06 01:50:10.416] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_icc_mgr'
[2025-07-06 01:50:10.416] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_core_mgr' from mount './ps4_root\dev' + remaining 'sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_core_mgr', mountPoint result='./ps4_root\dev\sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_core_mgr' for '/dev/sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_core_mgr'
[2025-07-06 01:50:10.416] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_crypto_mgr'
[2025-07-06 01:50:10.416] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_crypto_mgr'
[2025-07-06 01:50:10.416] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_crypto_mgr' from mount './ps4_root\dev' + remaining 'sbl_crypto_mgr'
[2025-07-06 01:50:10.416] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_crypto_mgr', mountPoint result='./ps4_root\dev\sbl_crypto_mgr'
[2025-07-06 01:50:10.417] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_crypto_mgr'
[2025-07-06 01:50:10.417] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_crypto_mgr' for '/dev/sbl_crypto_mgr'
[2025-07-06 01:50:10.417] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_crypto_mgr'
[2025-07-06 01:50:10.417] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_auth_mgr' from mount './ps4_root\dev' + remaining 'sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_auth_mgr', mountPoint result='./ps4_root\dev\sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_auth_mgr' for '/dev/sbl_auth_mgr'
[2025-07-06 01:50:10.417] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_auth_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_self_mgr' from mount './ps4_root\dev' + remaining 'sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_self_mgr', mountPoint result='./ps4_root\dev\sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_self_mgr' for '/dev/sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_self_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: Starting mapping for '/dev/sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] GetHostPath: Constructed path './ps4_root\dev\sandbox_mgr' from mount './ps4_root\dev' + remaining 'sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: virtualPath='/dev/sandbox_mgr', mountPoint result='./ps4_root\dev\sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sandbox_mgr' for '/dev/sandbox_mgr'
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sandbox_mgr'
[2025-07-06 01:50:10.418] [info] Created system management devices
[2025-07-06 01:50:10.418] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/vce'
[2025-07-06 01:50:10.419] [debug] MapToHostPathLocked: Starting mapping for '/dev/vce'
[2025-07-06 01:50:10.419] [debug] GetHostPath: Constructed path './ps4_root\dev\vce' from mount './ps4_root\dev' + remaining 'vce'
[2025-07-06 01:50:10.419] [debug] MapToHostPathLocked: virtualPath='/dev/vce', mountPoint result='./ps4_root\dev\vce'
[2025-07-06 01:50:10.419] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\vce'
[2025-07-06 01:50:10.419] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\vce' for '/dev/vce'
[2025-07-06 01:50:10.419] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/vce'
[2025-07-06 01:50:10.419] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/dmem'
[2025-07-06 01:50:10.419] [debug] MapToHostPathLocked: Starting mapping for '/dev/dmem'
[2025-07-06 01:50:10.419] [debug] GetHostPath: Constructed path './ps4_root\dev\dmem' from mount './ps4_root\dev' + remaining 'dmem'
[2025-07-06 01:50:10.419] [debug] MapToHostPathLocked: virtualPath='/dev/dmem', mountPoint result='./ps4_root\dev\dmem'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\dmem'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\dmem' for '/dev/dmem'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/dmem'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sherlock'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: Starting mapping for '/dev/sherlock'
[2025-07-06 01:50:10.420] [debug] GetHostPath: Constructed path './ps4_root\dev\sherlock' from mount './ps4_root\dev' + remaining 'sherlock'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: virtualPath='/dev/sherlock', mountPoint result='./ps4_root\dev\sherlock'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sherlock'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sherlock' for '/dev/sherlock'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sherlock'
[2025-07-06 01:50:10.420] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/tty'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: Starting mapping for '/dev/tty'
[2025-07-06 01:50:10.420] [debug] GetHostPath: Constructed path './ps4_root\dev\tty' from mount './ps4_root\dev' + remaining 'tty'
[2025-07-06 01:50:10.420] [debug] MapToHostPathLocked: virtualPath='/dev/tty', mountPoint result='./ps4_root\dev\tty'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\tty'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\tty' for '/dev/tty'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/tty'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/ttyu0'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: Starting mapping for '/dev/ttyu0'
[2025-07-06 01:50:10.421] [debug] GetHostPath: Constructed path './ps4_root\dev\ttyu0' from mount './ps4_root\dev' + remaining 'ttyu0'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: virtualPath='/dev/ttyu0', mountPoint result='./ps4_root\dev\ttyu0'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\ttyu0'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\ttyu0' for '/dev/ttyu0'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/ttyu0'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/ttyu1'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: Starting mapping for '/dev/ttyu1'
[2025-07-06 01:50:10.421] [debug] GetHostPath: Constructed path './ps4_root\dev\ttyu1' from mount './ps4_root\dev' + remaining 'ttyu1'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: virtualPath='/dev/ttyu1', mountPoint result='./ps4_root\dev\ttyu1'
[2025-07-06 01:50:10.421] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\ttyu1'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\ttyu1' for '/dev/ttyu1'
[2025-07-06 01:50:10.421] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/ttyu1'
[2025-07-06 01:50:10.421] [info] Created hardware interface devices
[2025-07-06 01:50:10.423] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/cd0'
[2025-07-06 01:50:10.423] [debug] MapToHostPathLocked: Starting mapping for '/dev/cd0'
[2025-07-06 01:50:10.423] [debug] GetHostPath: Constructed path './ps4_root\dev\cd0' from mount './ps4_root\dev' + remaining 'cd0'
[2025-07-06 01:50:10.423] [debug] MapToHostPathLocked: virtualPath='/dev/cd0', mountPoint result='./ps4_root\dev\cd0'
[2025-07-06 01:50:10.423] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\cd0'
[2025-07-06 01:50:10.423] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\cd0' for '/dev/cd0'
[2025-07-06 01:50:10.423] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/cd0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/da0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: Starting mapping for '/dev/da0'
[2025-07-06 01:50:10.424] [debug] GetHostPath: Constructed path './ps4_root\dev\da0' from mount './ps4_root\dev' + remaining 'da0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: virtualPath='/dev/da0', mountPoint result='./ps4_root\dev\da0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\da0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\da0' for '/dev/da0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/da0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/nvme0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: Starting mapping for '/dev/nvme0'
[2025-07-06 01:50:10.424] [debug] GetHostPath: Constructed path './ps4_root\dev\nvme0' from mount './ps4_root\dev' + remaining 'nvme0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: virtualPath='/dev/nvme0', mountPoint result='./ps4_root\dev\nvme0'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\nvme0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\nvme0' for '/dev/nvme0'
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/nvme0'
[2025-07-06 01:50:10.424] [info] Created storage devices
[2025-07-06 01:50:10.424] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/gpu'
[2025-07-06 01:50:10.424] [debug] MapToHostPathLocked: Starting mapping for '/dev/gpu'
[2025-07-06 01:50:10.424] [debug] GetHostPath: Constructed path './ps4_root\dev\gpu' from mount './ps4_root\dev' + remaining 'gpu'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: virtualPath='/dev/gpu', mountPoint result='./ps4_root\dev\gpu'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\gpu'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\gpu' for '/dev/gpu'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/gpu'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/hdmi'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: Starting mapping for '/dev/hdmi'
[2025-07-06 01:50:10.425] [debug] GetHostPath: Constructed path './ps4_root\dev\hdmi' from mount './ps4_root\dev' + remaining 'hdmi'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: virtualPath='/dev/hdmi', mountPoint result='./ps4_root\dev\hdmi'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\hdmi'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\hdmi' for '/dev/hdmi'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/hdmi'
[2025-07-06 01:50:10.425] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/audioout'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: Starting mapping for '/dev/audioout'
[2025-07-06 01:50:10.425] [debug] GetHostPath: Constructed path './ps4_root\dev\audioout' from mount './ps4_root\dev' + remaining 'audioout'
[2025-07-06 01:50:10.425] [debug] MapToHostPathLocked: virtualPath='/dev/audioout', mountPoint result='./ps4_root\dev\audioout'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\audioout'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\audioout' for '/dev/audioout'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/audioout'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/audioin'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: Starting mapping for '/dev/audioin'
[2025-07-06 01:50:10.426] [debug] GetHostPath: Constructed path './ps4_root\dev\audioin' from mount './ps4_root\dev' + remaining 'audioin'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: virtualPath='/dev/audioin', mountPoint result='./ps4_root\dev\audioin'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\audioin'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\audioin' for '/dev/audioin'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/audioin'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/bluetooth'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: Starting mapping for '/dev/bluetooth'
[2025-07-06 01:50:10.426] [debug] GetHostPath: Constructed path './ps4_root\dev\bluetooth' from mount './ps4_root\dev' + remaining 'bluetooth'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: virtualPath='/dev/bluetooth', mountPoint result='./ps4_root\dev\bluetooth'
[2025-07-06 01:50:10.426] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\bluetooth'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\bluetooth' for '/dev/bluetooth'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/bluetooth'
[2025-07-06 01:50:10.426] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/wlan'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: Starting mapping for '/dev/wlan'
[2025-07-06 01:50:10.427] [debug] GetHostPath: Constructed path './ps4_root\dev\wlan' from mount './ps4_root\dev' + remaining 'wlan'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: virtualPath='/dev/wlan', mountPoint result='./ps4_root\dev\wlan'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\wlan'
[2025-07-06 01:50:10.427] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\wlan' for '/dev/wlan'
[2025-07-06 01:50:10.427] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/wlan'
[2025-07-06 01:50:10.427] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/usb'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: Starting mapping for '/dev/usb'
[2025-07-06 01:50:10.427] [debug] GetHostPath: Constructed path './ps4_root\dev\usb' from mount './ps4_root\dev' + remaining 'usb'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: virtualPath='/dev/usb', mountPoint result='./ps4_root\dev\usb'
[2025-07-06 01:50:10.427] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\usb'
[2025-07-06 01:50:10.427] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\usb' for '/dev/usb'
[2025-07-06 01:50:10.427] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/usb'
[2025-07-06 01:50:10.428] [info] Created media and I/O devices
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/camera'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: Starting mapping for '/dev/camera'
[2025-07-06 01:50:10.428] [debug] GetHostPath: Constructed path './ps4_root\dev\camera' from mount './ps4_root\dev' + remaining 'camera'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: virtualPath='/dev/camera', mountPoint result='./ps4_root\dev\camera'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\camera'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\camera' for '/dev/camera'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/camera'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/pad'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: Starting mapping for '/dev/pad'
[2025-07-06 01:50:10.428] [debug] GetHostPath: Constructed path './ps4_root\dev\pad' from mount './ps4_root\dev' + remaining 'pad'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: virtualPath='/dev/pad', mountPoint result='./ps4_root\dev\pad'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\pad'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\pad' for '/dev/pad'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/pad'
[2025-07-06 01:50:10.428] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/touchpad'
[2025-07-06 01:50:10.428] [debug] MapToHostPathLocked: Starting mapping for '/dev/touchpad'
[2025-07-06 01:50:10.429] [debug] GetHostPath: Constructed path './ps4_root\dev\touchpad' from mount './ps4_root\dev' + remaining 'touchpad'
[2025-07-06 01:50:10.429] [debug] MapToHostPathLocked: virtualPath='/dev/touchpad', mountPoint result='./ps4_root\dev\touchpad'
[2025-07-06 01:50:10.429] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\touchpad'
[2025-07-06 01:50:10.429] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\touchpad' for '/dev/touchpad'
[2025-07-06 01:50:10.429] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/touchpad'
[2025-07-06 01:50:10.429] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/motion'
[2025-07-06 01:50:10.429] [debug] MapToHostPathLocked: Starting mapping for '/dev/motion'
[2025-07-06 01:50:10.429] [debug] GetHostPath: Constructed path './ps4_root\dev\motion' from mount './ps4_root\dev' + remaining 'motion'
[2025-07-06 01:50:10.429] [debug] MapToHostPathLocked: virtualPath='/dev/motion', mountPoint result='./ps4_root\dev\motion'
[2025-07-06 01:50:10.429] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\motion'
[2025-07-06 01:50:10.429] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\motion' for '/dev/motion'
[2025-07-06 01:50:10.429] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/motion'
[2025-07-06 01:50:10.429] [info] Created input devices
[2025-07-06 01:50:10.429] [info] Registering device handlers...
[2025-07-06 01:50:10.429] [debug] Registered handler for device: /dev/null
[2025-07-06 01:50:10.429] [debug] Registered handler for device: /dev/zero
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/random
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/urandom
[2025-07-06 01:50:10.430] [info] Registered basic I/O device handlers
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/stdout
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/stderr
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/dipsw
[2025-07-06 01:50:10.430] [info] Registered standard I/O device handlers
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/sbl_icc_mgr
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/gc
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/pad
[2025-07-06 01:50:10.430] [debug] Registered handler for device: /dev/audioout
[2025-07-06 01:50:10.430] [info] Registered system device handlers
[2025-07-06 01:50:10.430] [info] Initialized 38 device files with handlers
[2025-07-06 01:50:10.430] [debug] Creating standard handles (FD 0, 1, 2)
[2025-07-06 01:50:10.430] [info] Device files initialized
[2025-07-06 01:50:10.430] [info] Initializing PFS...
[2025-07-06 01:50:10.430] [info] Initializing Protected File System (PFS)...
[2025-07-06 01:50:10.434] [debug] Created PFS directory: ./ps4_root\pfs
[2025-07-06 01:50:10.434] [debug] Created PFS directory: ./ps4_root\mnt/pfs
[2025-07-06 01:50:10.434] [debug] GetHostPath: Constructed path './ps4_root\installed_packages' from mount './ps4_root\installed_packages' + remaining ''
[2025-07-06 01:50:10.434] [debug] Created PFS directory: ./ps4_root\installed_packages
[2025-07-06 01:50:10.435] [info] Mounted ./ps4_root\pfs\app.pfs to /mnt/pfs/app
[2025-07-06 01:50:10.435] [info] Mounted PFS: ./ps4_root\pfs\app.pfs -> /mnt/pfs/app
[2025-07-06 01:50:10.435] [info] Mounted ./ps4_root\pfs\patch.pfs to /mnt/pfs/patch
[2025-07-06 01:50:10.435] [info] Mounted PFS: ./ps4_root\pfs\patch.pfs -> /mnt/pfs/patch
[2025-07-06 01:50:10.435] [info] Mounted ./ps4_root\pfs\system.pfs to /mnt/pfs/system
[2025-07-06 01:50:10.435] [info] Mounted PFS: ./ps4_root\pfs\system.pfs -> /mnt/pfs/system
[2025-07-06 01:50:10.436] [info] Mounted ./ps4_root\pfs\addcont.pfs to /mnt/pfs/addcont
[2025-07-06 01:50:10.436] [info] Mounted PFS: ./ps4_root\pfs\addcont.pfs -> /mnt/pfs/addcont
[2025-07-06 01:50:10.436] [info] PFS initialization completed successfully
[2025-07-06 01:50:10.436] [info] PFS initialized
[2025-07-06 01:50:10.436] [info] PS4Filesystem initialized with root path: ./ps4_root (took 27ms)
[2025-07-06 01:50:10.436] [info] Memory diagnostics reset
[2025-07-06 01:50:10.436] [info] MemoryDiagnostics initialized
[2025-07-06 01:50:10.438] [info] Filesystem initialized successfully in 30ms
[2025-07-06 01:50:10.439] [info] OrbisOS initialized
[2025-07-06 01:50:10.439] [info] OS initialized successfully
[2025-07-06 01:50:10.439] [info] IOManager initialized successfully
[2025-07-06 01:50:10.439] [info] Initializing FiberManager with 15s timeout...
[2025-07-06 01:50:10.440] [debug] Converting thread to fiber...
[2025-07-06 01:50:10.440] [debug] Successfully converted thread to fiber
[2025-07-06 01:50:10.440] [info] FiberManager initialized
[2025-07-06 01:50:10.450] [info] FiberManager initialized successfully in 10ms
[2025-07-06 01:50:10.450] [info] TrophyManager initialized for user default_user, latency=1us
[2025-07-06 01:50:10.450] [info] TrophyManager initialized successfully
[2025-07-06 01:50:10.450] [info] Step 4/10: Initializing graphics subsystem...
[2025-07-06 01:50:10.450] [info] Initializing TileManager with 10s timeout...
[2025-07-06 01:50:10.450] [info] TileManager initialized, latency=2us
[2025-07-06 01:50:10.461] [info] TileManager initialized successfully in 10ms
[2025-07-06 01:50:10.461] [info] Initializing GPU with 60s timeout...
[2025-07-06 01:50:10.461] [info] PS4GPU initializing...
[2025-07-06 01:50:10.461] [debug] Vulkan context validation passed
[2025-07-06 01:50:10.461] [info] PS4GPU: Creating swapchain...
[2025-07-06 01:50:10.461] [info] PS4GPU: Reusing existing swapchain created by main thread
[2025-07-06 01:50:10.461] [info] CreateSwapchain: Reused existing swapchain with 3 images, latency=311us
[2025-07-06 01:50:10.461] [info] PS4GPU: Creating swapchain image views...
[2025-07-06 01:50:10.461] [info] PS4GPU: Reusing existing swapchain image views created by main thread
[2025-07-06 01:50:10.461] [info] CreateSwapchainImageViews: Reused 3 existing image views, latency=343us
[2025-07-06 01:50:10.461] [info] PS4GPU: Creating command pool...
[2025-07-06 01:50:10.461] [info] PS4GPU: Reusing existing command pool created by main thread
[2025-07-06 01:50:10.461] [info] CreateCommandPool: Reused existing command pool, latency=370us
[2025-07-06 01:50:10.461] [info] PS4GPU: Creating descriptor pool...
[2025-07-06 01:50:10.461] [info] PS4GPU: Reusing existing descriptor pool created by main thread
[2025-07-06 01:50:10.461] [info] CreateDescriptorPool: Reused existing descriptor pool, latency=412us
[2025-07-06 01:50:10.461] [info] PS4GPU: Creating sync objects...
[2025-07-06 01:50:10.462] [info] PS4GPU: Reusing existing sync objects created by main thread
[2025-07-06 01:50:10.462] [info] CreateSyncObjects: Reused existing sync objects, latency=444us
[2025-07-06 01:50:10.462] [info] PS4GPU: Creating default render pass...
[2025-07-06 01:50:10.462] [info] PS4GPU: Reusing existing render pass created by main thread
[2025-07-06 01:50:10.462] [info] CreateDefaultRenderPass: Reused existing render pass, latency=497us
[2025-07-06 01:50:10.462] [info] PS4GPU: Creating framebuffers...
[2025-07-06 01:50:10.462] [info] CreateFramebuffers: Created 3 framebuffers, latency=500us
[2025-07-06 01:50:10.462] [info] PS4GPU initialized with Vulkan, latency=1679us
[2025-07-06 01:50:10.471] [info] GPU initialized successfully in 10ms
[2025-07-06 01:50:10.471] [info] Step 5/10: Initializing input and audio subsystems...
[2025-07-06 01:50:10.471] [info] PS4ControllerManager initialized
[2025-07-06 01:50:10.471] [info] ControllerManager initialized successfully
[2025-07-06 01:50:10.471] [info] Audio initialization: enabled=true
[2025-07-06 01:50:10.471] [info] Initializing Audio with 20s timeout...
[2025-07-06 01:50:10.472] [info] Starting PortAudio initialization with 3000ms timeout
[2025-07-06 01:50:10.570] [debug] Pa_Initialize completed in 98ms with result: 0
[2025-07-06 01:50:10.575] [info] PortAudio initialized successfully
[2025-07-06 01:50:10.575] [debug] Enumerated device 0: name=Microsoft Sound Mapper - Output, hostApi=2, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.575] [debug] Enumerated device 1: name=Realtek Digital Output (Realtek, hostApi=2, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.575] [debug] Enumerated device 2: name=Speakers (Realtek(R) Audio), hostApi=2, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.575] [debug] Enumerated device 3: name=Primary Sound Driver, hostApi=1, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.575] [debug] Enumerated device 4: name=Realtek Digital Output (Realtek(R) Audio), hostApi=1, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Enumerated device 5: name=Speakers (Realtek(R) Audio), hostApi=1, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Enumerated device 6: name=Speakers (Realtek(R) Audio), hostApi=13, channels=2, sampleRate=48000, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Enumerated device 7: name=Realtek Digital Output (Realtek(R) Audio), hostApi=13, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Enumerated device 8: name=Output (AMD HD Audio DP out #5), hostApi=11, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Skipping device 9: no output channels
[2025-07-06 01:50:10.576] [debug] Skipping device 10: no output channels
[2025-07-06 01:50:10.576] [debug] Enumerated device 11: name=SPDIF Out (Realtek HDA SPDIF Out), hostApi=11, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [debug] Skipping device 12: no output channels
[2025-07-06 01:50:10.576] [debug] Enumerated device 13: name=Speakers (Realtek HD Audio output), hostApi=11, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-06 01:50:10.576] [info] Selected audio device: Speakers (Realtek(R) Audio) (index=6, hostApi=13)
[2025-07-06 01:50:10.576] [debug] Opening audio stream...
[2025-07-06 01:50:10.607] [info] Audio stream opened successfully
[2025-07-06 01:50:10.607] [info] AudioDevice initialized with device 6 (name=Speakers (Realtek(R) Audio))
[2025-07-06 01:50:10.607] [info] PS4Audio initialized: success=true
[2025-07-06 01:50:10.617] [info] Audio initialized successfully in 145ms
[2025-07-06 01:50:10.617] [info] Step 6/10: Initializing 8 CPU cores...
[2025-07-06 01:50:10.617] [info] Initializing CPU[0] with 20s timeout...
[2025-07-06 01:50:10.618] [info] Pipeline statistics reset
[2025-07-06 01:50:10.618] [info] X86_64CPU[0] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.628] [info] CPU[0] initialized successfully in 10ms
[2025-07-06 01:50:10.628] [info] Initializing CPU[1] with 20s timeout...
[2025-07-06 01:50:10.628] [info] Pipeline statistics reset
[2025-07-06 01:50:10.628] [info] X86_64CPU[1] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.638] [info] CPU[1] initialized successfully in 10ms
[2025-07-06 01:50:10.638] [info] Initializing CPU[2] with 20s timeout...
[2025-07-06 01:50:10.639] [info] Pipeline statistics reset
[2025-07-06 01:50:10.639] [info] X86_64CPU[2] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.649] [info] CPU[2] initialized successfully in 10ms
[2025-07-06 01:50:10.649] [info] Initializing CPU[3] with 20s timeout...
[2025-07-06 01:50:10.649] [info] Pipeline statistics reset
[2025-07-06 01:50:10.649] [info] X86_64CPU[3] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.659] [info] CPU[3] initialized successfully in 10ms
[2025-07-06 01:50:10.659] [info] Initializing CPU[4] with 20s timeout...
[2025-07-06 01:50:10.660] [info] Pipeline statistics reset
[2025-07-06 01:50:10.660] [info] X86_64CPU[4] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.670] [info] CPU[4] initialized successfully in 10ms
[2025-07-06 01:50:10.670] [info] Initializing CPU[5] with 20s timeout...
[2025-07-06 01:50:10.670] [info] Pipeline statistics reset
[2025-07-06 01:50:10.670] [info] X86_64CPU[5] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.680] [info] CPU[5] initialized successfully in 10ms
[2025-07-06 01:50:10.681] [info] Initializing CPU[6] with 20s timeout...
[2025-07-06 01:50:10.681] [info] Pipeline statistics reset
[2025-07-06 01:50:10.681] [info] X86_64CPU[6] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.691] [info] CPU[6] initialized successfully in 10ms
[2025-07-06 01:50:10.691] [info] Initializing CPU[7] with 20s timeout...
[2025-07-06 01:50:10.691] [info] Pipeline statistics reset
[2025-07-06 01:50:10.691] [info] X86_64CPU[7] initialized with full register support. RIP=0x1000
[2025-07-06 01:50:10.701] [info] CPU[7] initialized successfully in 10ms
[2025-07-06 01:50:10.702] [info] Step 7/10: Initializing interrupt handler...
[2025-07-06 01:50:10.702] [info] Initializing InterruptHandler with 10s timeout...
[2025-07-06 01:50:10.702] [info] Initializing InterruptHandler...
[2025-07-06 01:50:10.702] [info] Allocating IDT memory: size=0x1000
[2025-07-06 01:50:10.702] [info] Allocating virtual memory: process=0, size=0x1000
[2025-07-06 01:50:10.702] [info] Searching for free virtual range: size=0x1000, alignment=0x1000
[2025-07-06 01:50:10.702] [info] Found free virtual range at 0x10000 (beyond page table)
[2025-07-06 01:50:10.702] [info] AllocateVirtual: Allocating 1 pages starting at 0x10000
[2025-07-06 01:50:10.702] [debug] Resizing page table to accommodate index 16
[2025-07-06 01:50:10.702] [debug] AllocatePage: Allocated phys=0x1000 in 0ms
[2025-07-06 01:50:10.702] [debug] AllocatePage: TLB inserted: virt=0x10000, phys=0x1000
[2025-07-06 01:50:10.702] [info] Allocated page: virt=0x10000, phys=0x1000
[2025-07-06 01:50:10.703] [info] Allocated virtual memory: addr=0x10000, size=0x1000, process=0
[2025-07-06 01:50:10.703] [info] IDT allocated at 0x10000
[2025-07-06 01:50:10.703] [info] Zeroing IDT memory
[2025-07-06 01:50:10.703] [debug] Writing virtual memory chunk: virt=0x10000, size=0x1000, process=0
[2025-07-06 01:50:10.703] [info] Translating virtual to physical: virt=0x10000, process=0
[2025-07-06 01:50:10.703] [debug] Marked page dirty: virt=0x10000
[2025-07-06 01:50:10.703] [debug] Wrote virtual memory chunk: virt=0x10000, size=0x1000
[2025-07-06 01:50:10.703] [info] IDT memory zeroed
[2025-07-06 01:50:10.703] [info] Allocating handler stubs: entry_count=256, size=0x1000
[2025-07-06 01:50:10.703] [info] Allocating virtual memory: process=0, size=0x1000
[2025-07-06 01:50:10.703] [info] Searching for free virtual range: size=0x1000, alignment=0x1000
[2025-07-06 01:50:10.703] [info] Found free virtual range at 0x11000 (beyond page table)
[2025-07-06 01:50:10.703] [info] AllocateVirtual: Allocating 1 pages starting at 0x11000
[2025-07-06 01:50:10.703] [debug] Resizing page table to accommodate index 17
[2025-07-06 01:50:10.703] [debug] AllocatePage: Allocated phys=0x2000 in 0ms
[2025-07-06 01:50:10.703] [debug] AllocatePage: TLB inserted: virt=0x11000, phys=0x2000
[2025-07-06 01:50:10.704] [info] Allocated page: virt=0x11000, phys=0x2000
[2025-07-06 01:50:10.704] [info] Allocated virtual memory: addr=0x11000, size=0x1000, process=0
[2025-07-06 01:50:10.704] [info] Handler stubs allocated at 0x11000
[2025-07-06 01:50:10.704] [info] Setting up IDT descriptors
[2025-07-06 01:50:10.704] [debug] Writing virtual memory chunk: virt=0x11000, size=0x1, process=0
[2025-07-06 01:50:10.704] [info] Translating virtual to physical: virt=0x11000, process=0
[2025-07-06 01:50:10.704] [debug] Marked page dirty: virt=0x11000
[2025-07-06 01:50:10.704] [debug] Wrote virtual memory chunk: virt=0x11000, size=0x1
[2025-07-06 01:50:10.704] [debug] Writing virtual memory chunk: virt=0x10000, size=0x10, process=0
[2025-07-06 01:50:10.704] [info] Translating virtual to physical: virt=0x10000, process=0
[2025-07-06 01:50:10.704] [debug] Marked page dirty: virt=0x10000
[2025-07-06 01:50:10.704] [debug] Wrote virtual memory chunk: virt=0x10000, size=0x10
[2025-07-06 01:50:10.704] [info] Processed descriptor 0
[2025-07-06 01:50:10.704] [debug] Writing virtual memory chunk: virt=0x11010, size=0x1, process=0
[2025-07-06 01:50:10.704] [info] Translating virtual to physical: virt=0x11010, process=0
[2025-07-06 01:50:10.704] [debug] Marked page dirty: virt=0x11010
[2025-07-06 01:50:10.705] [debug] Wrote virtual memory chunk: virt=0x11010, size=0x1
[2025-07-06 01:50:10.705] [debug] Writing virtual memory chunk: virt=0x10010, size=0x10, process=0
[2025-07-06 01:50:10.705] [info] Translating virtual to physical: virt=0x10010, process=0
[2025-07-06 01:50:10.705] [debug] Marked page dirty: virt=0x10010
[2025-07-06 01:50:10.705] [debug] Wrote virtual memory chunk: virt=0x10010, size=0x10
[2025-07-06 01:50:10.705] [debug] Writing virtual memory chunk: virt=0x11020, size=0x1, process=0
[2025-07-06 01:50:10.705] [info] Translating virtual to physical: virt=0x11020, process=0
[2025-07-06 01:50:10.705] [debug] Marked page dirty: virt=0x11020
[2025-07-06 01:50:10.705] [debug] Wrote virtual memory chunk: virt=0x11020, size=0x1
[2025-07-06 01:50:10.705] [debug] Writing virtual memory chunk: virt=0x10020, size=0x10, process=0
[2025-07-06 01:50:10.705] [info] Translating virtual to physical: virt=0x10020, process=0
[2025-07-06 01:50:10.705] [debug] Marked page dirty: virt=0x10020
[2025-07-06 01:50:10.705] [debug] Wrote virtual memory chunk: virt=0x10020, size=0x10
[2025-07-06 01:50:10.705] [debug] Writing virtual memory chunk: virt=0x11030, size=0x1, process=0
[2025-07-06 01:50:10.705] [info] Translating virtual to physical: virt=0x11030, process=0
[2025-07-06 01:50:10.705] [debug] Marked page dirty: virt=0x11030
[2025-07-06 01:50:10.706] [debug] Wrote virtual memory chunk: virt=0x11030, size=0x1
[2025-07-06 01:50:10.706] [debug] Writing virtual memory chunk: virt=0x10030, size=0x10, process=0
[2025-07-06 01:50:10.706] [info] Translating virtual to physical: virt=0x10030, process=0
[2025-07-06 01:50:10.706] [debug] Marked page dirty: virt=0x10030
[2025-07-06 01:50:10.706] [debug] Wrote virtual memory chunk: virt=0x10030, size=0x10
[2025-07-06 01:50:10.706] [debug] Writing virtual memory chunk: virt=0x11040, size=0x1, process=0
[2025-07-06 01:50:10.706] [info] Translating virtual to physical: virt=0x11040, process=0
[2025-07-06 01:50:10.706] [debug] Marked page dirty: virt=0x11040
[2025-07-06 01:50:10.706] [debug] Wrote virtual memory chunk: virt=0x11040, size=0x1
[2025-07-06 01:50:10.706] [debug] Writing virtual memory chunk: virt=0x10040, size=0x10, process=0
[2025-07-06 01:50:10.706] [info] Translating virtual to physical: virt=0x10040, process=0
[2025-07-06 01:50:10.706] [debug] Marked page dirty: virt=0x10040
[2025-07-06 01:50:10.706] [debug] Wrote virtual memory chunk: virt=0x10040, size=0x10
[2025-07-06 01:50:10.706] [debug] Writing virtual memory chunk: virt=0x11050, size=0x1, process=0
[2025-07-06 01:50:10.706] [info] Translating virtual to physical: virt=0x11050, process=0
[2025-07-06 01:50:10.706] [debug] Marked page dirty: virt=0x11050
[2025-07-06 01:50:10.706] [debug] Wrote virtual memory chunk: virt=0x11050, size=0x1
[2025-07-06 01:50:10.707] [debug] Writing virtual memory chunk: virt=0x10050, size=0x10, process=0
[2025-07-06 01:50:10.707] [info] Translating virtual to physical: virt=0x10050, process=0
[2025-07-06 01:50:10.707] [debug] Marked page dirty: virt=0x10050
[2025-07-06 01:50:10.707] [debug] Wrote virtual memory chunk: virt=0x10050, size=0x10
[2025-07-06 01:50:10.707] [debug] Writing virtual memory chunk: virt=0x11060, size=0x1, process=0
[2025-07-06 01:50:10.707] [info] Translating virtual to physical: virt=0x11060, process=0
[2025-07-06 01:50:10.707] [debug] Marked page dirty: virt=0x11060
[2025-07-06 01:50:10.707] [debug] Wrote virtual memory chunk: virt=0x11060, size=0x1
[2025-07-06 01:50:10.707] [debug] Writing virtual memory chunk: virt=0x10060, size=0x10, process=0
[2025-07-06 01:50:10.707] [info] Translating virtual to physical: virt=0x10060, process=0
[2025-07-06 01:50:10.707] [debug] Marked page dirty: virt=0x10060
[2025-07-06 01:50:10.707] [debug] Wrote virtual memory chunk: virt=0x10060, size=0x10
[2025-07-06 01:50:10.707] [debug] Writing virtual memory chunk: virt=0x11070, size=0x1, process=0
[2025-07-06 01:50:10.707] [info] Translating virtual to physical: virt=0x11070, process=0
[2025-07-06 01:50:10.707] [debug] Marked page dirty: virt=0x11070
[2025-07-06 01:50:10.707] [debug] Wrote virtual memory chunk: virt=0x11070, size=0x1
[2025-07-06 01:50:10.708] [debug] Writing virtual memory chunk: virt=0x10070, size=0x10, process=0
[2025-07-06 01:50:10.708] [info] Translating virtual to physical: virt=0x10070, process=0
[2025-07-06 01:50:10.708] [debug] Marked page dirty: virt=0x10070
[2025-07-06 01:50:10.708] [debug] Wrote virtual memory chunk: virt=0x10070, size=0x10
[2025-07-06 01:50:10.708] [debug] Writing virtual memory chunk: virt=0x11080, size=0x1, process=0
[2025-07-06 01:50:10.708] [info] Translating virtual to physical: virt=0x11080, process=0
[2025-07-06 01:50:10.708] [debug] Marked page dirty: virt=0x11080
[2025-07-06 01:50:10.708] [debug] Wrote virtual memory chunk: virt=0x11080, size=0x1
[2025-07-06 01:50:10.708] [debug] Writing virtual memory chunk: virt=0x10080, size=0x10, process=0
[2025-07-06 01:50:10.708] [info] Translating virtual to physical: virt=0x10080, process=0
[2025-07-06 01:50:10.708] [debug] Marked page dirty: virt=0x10080
[2025-07-06 01:50:10.708] [debug] Wrote virtual memory chunk: virt=0x10080, size=0x10
[2025-07-06 01:50:10.708] [debug] Writing virtual memory chunk: virt=0x11090, size=0x1, process=0
[2025-07-06 01:50:10.708] [info] Translating virtual to physical: virt=0x11090, process=0
[2025-07-06 01:50:10.708] [debug] Marked page dirty: virt=0x11090
[2025-07-06 01:50:10.708] [debug] Wrote virtual memory chunk: virt=0x11090, size=0x1
[2025-07-06 01:50:10.709] [debug] Writing virtual memory chunk: virt=0x10090, size=0x10, process=0
[2025-07-06 01:50:10.709] [info] Translating virtual to physical: virt=0x10090, process=0
[2025-07-06 01:50:10.709] [debug] Marked page dirty: virt=0x10090
[2025-07-06 01:50:10.709] [debug] Wrote virtual memory chunk: virt=0x10090, size=0x10
[2025-07-06 01:50:10.709] [debug] Writing virtual memory chunk: virt=0x110a0, size=0x1, process=0
[2025-07-06 01:50:10.709] [info] Translating virtual to physical: virt=0x110a0, process=0
[2025-07-06 01:50:10.709] [debug] Marked page dirty: virt=0x110a0
[2025-07-06 01:50:10.709] [debug] Wrote virtual memory chunk: virt=0x110a0, size=0x1
[2025-07-06 01:50:10.709] [debug] Writing virtual memory chunk: virt=0x100a0, size=0x10, process=0
[2025-07-06 01:50:10.709] [info] Translating virtual to physical: virt=0x100a0, process=0
[2025-07-06 01:50:10.709] [debug] Marked page dirty: virt=0x100a0
[2025-07-06 01:50:10.709] [debug] Wrote virtual memory chunk: virt=0x100a0, size=0x10
[2025-07-06 01:50:10.709] [debug] Writing virtual memory chunk: virt=0x110b0, size=0x1, process=0
[2025-07-06 01:50:10.709] [info] Translating virtual to physical: virt=0x110b0, process=0
[2025-07-06 01:50:10.709] [debug] Marked page dirty: virt=0x110b0
[2025-07-06 01:50:10.709] [debug] Wrote virtual memory chunk: virt=0x110b0, size=0x1
[2025-07-06 01:50:10.710] [debug] Writing virtual memory chunk: virt=0x100b0, size=0x10, process=0
[2025-07-06 01:50:10.710] [info] Translating virtual to physical: virt=0x100b0, process=0
[2025-07-06 01:50:10.710] [debug] Marked page dirty: virt=0x100b0
[2025-07-06 01:50:10.710] [debug] Wrote virtual memory chunk: virt=0x100b0, size=0x10
[2025-07-06 01:50:10.710] [debug] Writing virtual memory chunk: virt=0x110c0, size=0x1, process=0
[2025-07-06 01:50:10.710] [info] Translating virtual to physical: virt=0x110c0, process=0
[2025-07-06 01:50:10.710] [debug] Marked page dirty: virt=0x110c0
[2025-07-06 01:50:10.710] [debug] Wrote virtual memory chunk: virt=0x110c0, size=0x1
[2025-07-06 01:50:10.710] [debug] Writing virtual memory chunk: virt=0x100c0, size=0x10, process=0
[2025-07-06 01:50:10.710] [info] Translating virtual to physical: virt=0x100c0, process=0
[2025-07-06 01:50:10.710] [debug] Marked page dirty: virt=0x100c0
[2025-07-06 01:50:10.710] [debug] Wrote virtual memory chunk: virt=0x100c0, size=0x10
[2025-07-06 01:50:10.710] [debug] Writing virtual memory chunk: virt=0x110d0, size=0x1, process=0
[2025-07-06 01:50:10.710] [info] Translating virtual to physical: virt=0x110d0, process=0
[2025-07-06 01:50:10.710] [debug] Marked page dirty: virt=0x110d0
[2025-07-06 01:50:10.710] [debug] Wrote virtual memory chunk: virt=0x110d0, size=0x1
[2025-07-06 01:50:10.710] [debug] Writing virtual memory chunk: virt=0x100d0, size=0x10, process=0
[2025-07-06 01:50:10.711] [info] Translating virtual to physical: virt=0x100d0, process=0
[2025-07-06 01:50:10.711] [debug] Marked page dirty: virt=0x100d0
[2025-07-06 01:50:10.711] [debug] Wrote virtual memory chunk: virt=0x100d0, size=0x10
[2025-07-06 01:50:10.711] [debug] Writing virtual memory chunk: virt=0x110e0, size=0x1, process=0
[2025-07-06 01:50:10.711] [info] Translating virtual to physical: virt=0x110e0, process=0
[2025-07-06 01:50:10.711] [debug] Marked page dirty: virt=0x110e0
[2025-07-06 01:50:10.711] [debug] Wrote virtual memory chunk: virt=0x110e0, size=0x1
[2025-07-06 01:50:10.711] [debug] Writing virtual memory chunk: virt=0x100e0, size=0x10, process=0
[2025-07-06 01:50:10.711] [info] Translating virtual to physical: virt=0x100e0, process=0
[2025-07-06 01:50:10.711] [debug] Marked page dirty: virt=0x100e0
[2025-07-06 01:50:10.711] [debug] Wrote virtual memory chunk: virt=0x100e0, size=0x10
[2025-07-06 01:50:10.711] [debug] Writing virtual memory chunk: virt=0x110f0, size=0x1, process=0
[2025-07-06 01:50:10.711] [info] Translating virtual to physical: virt=0x110f0, process=0
[2025-07-06 01:50:10.711] [debug] Marked page dirty: virt=0x110f0
[2025-07-06 01:50:10.711] [debug] Wrote virtual memory chunk: virt=0x110f0, size=0x1
[2025-07-06 01:50:10.711] [debug] Writing virtual memory chunk: virt=0x100f0, size=0x10, process=0
[2025-07-06 01:50:10.711] [info] Translating virtual to physical: virt=0x100f0, process=0
[2025-07-06 01:50:10.712] [debug] Marked page dirty: virt=0x100f0
[2025-07-06 01:50:10.712] [debug] Wrote virtual memory chunk: virt=0x100f0, size=0x10
[2025-07-06 01:50:10.712] [debug] Writing virtual memory chunk: virt=0x11100, size=0x1, process=0
[2025-07-06 01:50:10.712] [info] Translating virtual to physical: virt=0x11100, process=0
[2025-07-06 01:50:10.712] [debug] Marked page dirty: virt=0x11100
[2025-07-06 01:50:10.712] [debug] Wrote virtual memory chunk: virt=0x11100, size=0x1
[2025-07-06 01:50:10.712] [debug] Writing virtual memory chunk: virt=0x10100, size=0x10, process=0
[2025-07-06 01:50:10.712] [info] Translating virtual to physical: virt=0x10100, process=0
[2025-07-06 01:50:10.712] [debug] Marked page dirty: virt=0x10100
[2025-07-06 01:50:10.712] [debug] Wrote virtual memory chunk: virt=0x10100, size=0x10
[2025-07-06 01:50:10.712] [debug] Writing virtual memory chunk: virt=0x11110, size=0x1, process=0
[2025-07-06 01:50:10.712] [info] Translating virtual to physical: virt=0x11110, process=0
[2025-07-06 01:50:10.712] [debug] Marked page dirty: virt=0x11110
[2025-07-06 01:50:10.712] [debug] Wrote virtual memory chunk: virt=0x11110, size=0x1
[2025-07-06 01:50:10.712] [debug] Writing virtual memory chunk: virt=0x10110, size=0x10, process=0
[2025-07-06 01:50:10.712] [info] Translating virtual to physical: virt=0x10110, process=0
[2025-07-06 01:50:10.712] [debug] Marked page dirty: virt=0x10110
[2025-07-06 01:50:10.713] [debug] Wrote virtual memory chunk: virt=0x10110, size=0x10
[2025-07-06 01:50:10.713] [debug] Writing virtual memory chunk: virt=0x11120, size=0x1, process=0
[2025-07-06 01:50:10.713] [info] Translating virtual to physical: virt=0x11120, process=0
[2025-07-06 01:50:10.713] [debug] Marked page dirty: virt=0x11120
[2025-07-06 01:50:10.713] [debug] Wrote virtual memory chunk: virt=0x11120, size=0x1
[2025-07-06 01:50:10.713] [debug] Writing virtual memory chunk: virt=0x10120, size=0x10, process=0
[2025-07-06 01:50:10.713] [info] Translating virtual to physical: virt=0x10120, process=0
[2025-07-06 01:50:10.713] [debug] Marked page dirty: virt=0x10120
[2025-07-06 01:50:10.713] [debug] Wrote virtual memory chunk: virt=0x10120, size=0x10
[2025-07-06 01:50:10.713] [debug] Writing virtual memory chunk: virt=0x11130, size=0x1, process=0
[2025-07-06 01:50:10.713] [info] Translating virtual to physical: virt=0x11130, process=0
[2025-07-06 01:50:10.713] [debug] Marked page dirty: virt=0x11130
[2025-07-06 01:50:10.713] [debug] Wrote virtual memory chunk: virt=0x11130, size=0x1
[2025-07-06 01:50:10.713] [debug] Writing virtual memory chunk: virt=0x10130, size=0x10, process=0
[2025-07-06 01:50:10.713] [info] Translating virtual to physical: virt=0x10130, process=0
[2025-07-06 01:50:10.713] [debug] Marked page dirty: virt=0x10130
[2025-07-06 01:50:10.714] [debug] Wrote virtual memory chunk: virt=0x10130, size=0x10
[2025-07-06 01:50:10.714] [debug] Writing virtual memory chunk: virt=0x11140, size=0x1, process=0
[2025-07-06 01:50:10.714] [info] Translating virtual to physical: virt=0x11140, process=0
[2025-07-06 01:50:10.714] [debug] Marked page dirty: virt=0x11140
[2025-07-06 01:50:10.714] [debug] Wrote virtual memory chunk: virt=0x11140, size=0x1
[2025-07-06 01:50:10.714] [debug] Writing virtual memory chunk: virt=0x10140, size=0x10, process=0
[2025-07-06 01:50:10.714] [info] Translating virtual to physical: virt=0x10140, process=0
[2025-07-06 01:50:10.714] [debug] Marked page dirty: virt=0x10140
[2025-07-06 01:50:10.714] [debug] Wrote virtual memory chunk: virt=0x10140, size=0x10
[2025-07-06 01:50:10.714] [debug] Writing virtual memory chunk: virt=0x11150, size=0x1, process=0
[2025-07-06 01:50:10.714] [info] Translating virtual to physical: virt=0x11150, process=0
[2025-07-06 01:50:10.714] [debug] Marked page dirty: virt=0x11150
[2025-07-06 01:50:10.714] [debug] Wrote virtual memory chunk: virt=0x11150, size=0x1
[2025-07-06 01:50:10.714] [debug] Writing virtual memory chunk: virt=0x10150, size=0x10, process=0
[2025-07-06 01:50:10.714] [info] Translating virtual to physical: virt=0x10150, process=0
[2025-07-06 01:50:10.715] [debug] Marked page dirty: virt=0x10150
[2025-07-06 01:50:10.715] [debug] Wrote virtual memory chunk: virt=0x10150, size=0x10
[2025-07-06 01:50:10.715] [debug] Writing virtual memory chunk: virt=0x11160, size=0x1, process=0
[2025-07-06 01:50:10.715] [info] Translating virtual to physical: virt=0x11160, process=0
[2025-07-06 01:50:10.715] [debug] Marked page dirty: virt=0x11160
[2025-07-06 01:50:10.715] [debug] Wrote virtual memory chunk: virt=0x11160, size=0x1
[2025-07-06 01:50:10.715] [debug] Writing virtual memory chunk: virt=0x10160, size=0x10, process=0
[2025-07-06 01:50:10.715] [info] Translating virtual to physical: virt=0x10160, process=0
[2025-07-06 01:50:10.715] [debug] Marked page dirty: virt=0x10160
[2025-07-06 01:50:10.715] [debug] Wrote virtual memory chunk: virt=0x10160, size=0x10
[2025-07-06 01:50:10.715] [debug] Writing virtual memory chunk: virt=0x11170, size=0x1, process=0
[2025-07-06 01:50:10.715] [info] Translating virtual to physical: virt=0x11170, process=0
[2025-07-06 01:50:10.715] [debug] Marked page dirty: virt=0x11170
[2025-07-06 01:50:10.715] [debug] Wrote virtual memory chunk: virt=0x11170, size=0x1
[2025-07-06 01:50:10.715] [debug] Writing virtual memory chunk: virt=0x10170, size=0x10, process=0
[2025-07-06 01:50:10.715] [info] Translating virtual to physical: virt=0x10170, process=0
[2025-07-06 01:50:10.716] [debug] Marked page dirty: virt=0x10170
[2025-07-06 01:50:10.716] [debug] Wrote virtual memory chunk: virt=0x10170, size=0x10
[2025-07-06 01:50:10.716] [debug] Writing virtual memory chunk: virt=0x11180, size=0x1, process=0
[2025-07-06 01:50:10.716] [info] Translating virtual to physical: virt=0x11180, process=0
[2025-07-06 01:50:10.716] [debug] Marked page dirty: virt=0x11180
[2025-07-06 01:50:10.716] [debug] Wrote virtual memory chunk: virt=0x11180, size=0x1
[2025-07-06 01:50:10.716] [debug] Writing virtual memory chunk: virt=0x10180, size=0x10, process=0
[2025-07-06 01:50:10.718] [info] Translating virtual to physical: virt=0x10180, process=0
[2025-07-06 01:50:10.718] [debug] Marked page dirty: virt=0x10180
[2025-07-06 01:50:10.718] [debug] Wrote virtual memory chunk: virt=0x10180, size=0x10
[2025-07-06 01:50:10.718] [debug] Writing virtual memory chunk: virt=0x11190, size=0x1, process=0
[2025-07-06 01:50:10.718] [info] Translating virtual to physical: virt=0x11190, process=0
[2025-07-06 01:50:10.718] [debug] Marked page dirty: virt=0x11190
[2025-07-06 01:50:10.718] [debug] Wrote virtual memory chunk: virt=0x11190, size=0x1
[2025-07-06 01:50:10.718] [debug] Writing virtual memory chunk: virt=0x10190, size=0x10, process=0
[2025-07-06 01:50:10.718] [info] Translating virtual to physical: virt=0x10190, process=0
[2025-07-06 01:50:10.718] [debug] Marked page dirty: virt=0x10190
[2025-07-06 01:50:10.718] [debug] Wrote virtual memory chunk: virt=0x10190, size=0x10
[2025-07-06 01:50:10.718] [debug] Writing virtual memory chunk: virt=0x111a0, size=0x1, process=0
[2025-07-06 01:50:10.718] [info] Translating virtual to physical: virt=0x111a0, process=0
[2025-07-06 01:50:10.718] [debug] Marked page dirty: virt=0x111a0
[2025-07-06 01:50:10.719] [debug] Wrote virtual memory chunk: virt=0x111a0, size=0x1
[2025-07-06 01:50:10.719] [debug] Writing virtual memory chunk: virt=0x101a0, size=0x10, process=0
[2025-07-06 01:50:10.719] [info] Translating virtual to physical: virt=0x101a0, process=0
[2025-07-06 01:50:10.719] [debug] Marked page dirty: virt=0x101a0
[2025-07-06 01:50:10.719] [debug] Wrote virtual memory chunk: virt=0x101a0, size=0x10
[2025-07-06 01:50:10.719] [debug] Writing virtual memory chunk: virt=0x111b0, size=0x1, process=0
[2025-07-06 01:50:10.719] [info] Translating virtual to physical: virt=0x111b0, process=0
[2025-07-06 01:50:10.719] [debug] Marked page dirty: virt=0x111b0
[2025-07-06 01:50:10.719] [debug] Wrote virtual memory chunk: virt=0x111b0, size=0x1
[2025-07-06 01:50:10.719] [debug] Writing virtual memory chunk: virt=0x101b0, size=0x10, process=0
[2025-07-06 01:50:10.719] [info] Translating virtual to physical: virt=0x101b0, process=0
[2025-07-06 01:50:10.719] [debug] Marked page dirty: virt=0x101b0
[2025-07-06 01:50:10.719] [debug] Wrote virtual memory chunk: virt=0x101b0, size=0x10
[2025-07-06 01:50:10.719] [debug] Writing virtual memory chunk: virt=0x111c0, size=0x1, process=0
[2025-07-06 01:50:10.719] [info] Translating virtual to physical: virt=0x111c0, process=0
[2025-07-06 01:50:10.719] [debug] Marked page dirty: virt=0x111c0
[2025-07-06 01:50:10.720] [debug] Wrote virtual memory chunk: virt=0x111c0, size=0x1
[2025-07-06 01:50:10.720] [debug] Writing virtual memory chunk: virt=0x101c0, size=0x10, process=0
[2025-07-06 01:50:10.720] [info] Translating virtual to physical: virt=0x101c0, process=0
[2025-07-06 01:50:10.720] [debug] Marked page dirty: virt=0x101c0
[2025-07-06 01:50:10.720] [debug] Wrote virtual memory chunk: virt=0x101c0, size=0x10
[2025-07-06 01:50:10.720] [debug] Writing virtual memory chunk: virt=0x111d0, size=0x1, process=0
[2025-07-06 01:50:10.720] [info] Translating virtual to physical: virt=0x111d0, process=0
[2025-07-06 01:50:10.720] [debug] Marked page dirty: virt=0x111d0
[2025-07-06 01:50:10.720] [debug] Wrote virtual memory chunk: virt=0x111d0, size=0x1
[2025-07-06 01:50:10.720] [debug] Writing virtual memory chunk: virt=0x101d0, size=0x10, process=0
[2025-07-06 01:50:10.720] [info] Translating virtual to physical: virt=0x101d0, process=0
[2025-07-06 01:50:10.720] [debug] Marked page dirty: virt=0x101d0
[2025-07-06 01:50:10.720] [debug] Wrote virtual memory chunk: virt=0x101d0, size=0x10
[2025-07-06 01:50:10.720] [debug] Writing virtual memory chunk: virt=0x111e0, size=0x1, process=0
[2025-07-06 01:50:10.720] [info] Translating virtual to physical: virt=0x111e0, process=0
[2025-07-06 01:50:10.720] [debug] Marked page dirty: virt=0x111e0
[2025-07-06 01:50:10.720] [debug] Wrote virtual memory chunk: virt=0x111e0, size=0x1
[2025-07-06 01:50:10.721] [debug] Writing virtual memory chunk: virt=0x101e0, size=0x10, process=0
[2025-07-06 01:50:10.721] [info] Translating virtual to physical: virt=0x101e0, process=0
[2025-07-06 01:50:10.721] [debug] Marked page dirty: virt=0x101e0
[2025-07-06 01:50:10.721] [debug] Wrote virtual memory chunk: virt=0x101e0, size=0x10
[2025-07-06 01:50:10.721] [debug] Writing virtual memory chunk: virt=0x111f0, size=0x1, process=0
[2025-07-06 01:50:10.721] [info] Translating virtual to physical: virt=0x111f0, process=0
[2025-07-06 01:50:10.721] [debug] Marked page dirty: virt=0x111f0
[2025-07-06 01:50:10.721] [debug] Wrote virtual memory chunk: virt=0x111f0, size=0x1
[2025-07-06 01:50:10.721] [debug] Writing virtual memory chunk: virt=0x101f0, size=0x10, process=0
[2025-07-06 01:50:10.721] [info] Translating virtual to physical: virt=0x101f0, process=0
[2025-07-06 01:50:10.721] [debug] Marked page dirty: virt=0x101f0
[2025-07-06 01:50:10.721] [debug] Wrote virtual memory chunk: virt=0x101f0, size=0x10
[2025-07-06 01:50:10.721] [debug] Writing virtual memory chunk: virt=0x11200, size=0x1, process=0
[2025-07-06 01:50:10.721] [info] Translating virtual to physical: virt=0x11200, process=0
[2025-07-06 01:50:10.721] [debug] Marked page dirty: virt=0x11200
[2025-07-06 01:50:10.721] [debug] Wrote virtual memory chunk: virt=0x11200, size=0x1
[2025-07-06 01:50:10.722] [debug] Writing virtual memory chunk: virt=0x10200, size=0x10, process=0
[2025-07-06 01:50:10.722] [info] Translating virtual to physical: virt=0x10200, process=0
[2025-07-06 01:50:10.722] [debug] Marked page dirty: virt=0x10200
[2025-07-06 01:50:10.722] [debug] Wrote virtual memory chunk: virt=0x10200, size=0x10
[2025-07-06 01:50:10.722] [debug] Writing virtual memory chunk: virt=0x11210, size=0x1, process=0
[2025-07-06 01:50:10.722] [info] Translating virtual to physical: virt=0x11210, process=0
[2025-07-06 01:50:10.722] [debug] Marked page dirty: virt=0x11210
[2025-07-06 01:50:10.722] [debug] Wrote virtual memory chunk: virt=0x11210, size=0x1
[2025-07-06 01:50:10.722] [debug] Writing virtual memory chunk: virt=0x10210, size=0x10, process=0
[2025-07-06 01:50:10.722] [info] Translating virtual to physical: virt=0x10210, process=0
[2025-07-06 01:50:10.722] [debug] Marked page dirty: virt=0x10210
[2025-07-06 01:50:10.722] [debug] Wrote virtual memory chunk: virt=0x10210, size=0x10
[2025-07-06 01:50:10.722] [debug] Writing virtual memory chunk: virt=0x11220, size=0x1, process=0
[2025-07-06 01:50:10.722] [info] Translating virtual to physical: virt=0x11220, process=0
[2025-07-06 01:50:10.722] [debug] Marked page dirty: virt=0x11220
[2025-07-06 01:50:10.722] [debug] Wrote virtual memory chunk: virt=0x11220, size=0x1
[2025-07-06 01:50:10.723] [debug] Writing virtual memory chunk: virt=0x10220, size=0x10, process=0
[2025-07-06 01:50:10.723] [info] Translating virtual to physical: virt=0x10220, process=0
[2025-07-06 01:50:10.723] [debug] Marked page dirty: virt=0x10220
[2025-07-06 01:50:10.723] [debug] Wrote virtual memory chunk: virt=0x10220, size=0x10
[2025-07-06 01:50:10.723] [debug] Writing virtual memory chunk: virt=0x11230, size=0x1, process=0
[2025-07-06 01:50:10.723] [info] Translating virtual to physical: virt=0x11230, process=0
[2025-07-06 01:50:10.723] [debug] Marked page dirty: virt=0x11230
[2025-07-06 01:50:10.723] [debug] Wrote virtual memory chunk: virt=0x11230, size=0x1
[2025-07-06 01:50:10.723] [debug] Writing virtual memory chunk: virt=0x10230, size=0x10, process=0
[2025-07-06 01:50:10.723] [info] Translating virtual to physical: virt=0x10230, process=0
[2025-07-06 01:50:10.723] [debug] Marked page dirty: virt=0x10230
[2025-07-06 01:50:10.723] [debug] Wrote virtual memory chunk: virt=0x10230, size=0x10
[2025-07-06 01:50:10.723] [debug] Writing virtual memory chunk: virt=0x11240, size=0x1, process=0
[2025-07-06 01:50:10.723] [info] Translating virtual to physical: virt=0x11240, process=0
[2025-07-06 01:50:10.723] [debug] Marked page dirty: virt=0x11240
[2025-07-06 01:50:10.723] [debug] Wrote virtual memory chunk: virt=0x11240, size=0x1
[2025-07-06 01:50:10.723] [debug] Writing virtual memory chunk: virt=0x10240, size=0x10, process=0
[2025-07-06 01:50:10.724] [info] Translating virtual to physical: virt=0x10240, process=0
[2025-07-06 01:50:10.724] [debug] Marked page dirty: virt=0x10240
[2025-07-06 01:50:10.724] [debug] Wrote virtual memory chunk: virt=0x10240, size=0x10
[2025-07-06 01:50:10.724] [debug] Writing virtual memory chunk: virt=0x11250, size=0x1, process=0
[2025-07-06 01:50:10.724] [info] Translating virtual to physical: virt=0x11250, process=0
[2025-07-06 01:50:10.724] [debug] Marked page dirty: virt=0x11250
[2025-07-06 01:50:10.724] [debug] Wrote virtual memory chunk: virt=0x11250, size=0x1
[2025-07-06 01:50:10.724] [debug] Writing virtual memory chunk: virt=0x10250, size=0x10, process=0
[2025-07-06 01:50:10.724] [info] Translating virtual to physical: virt=0x10250, process=0
[2025-07-06 01:50:10.724] [debug] Marked page dirty: virt=0x10250
[2025-07-06 01:50:10.724] [debug] Wrote virtual memory chunk: virt=0x10250, size=0x10
[2025-07-06 01:50:10.724] [debug] Writing virtual memory chunk: virt=0x11260, size=0x1, process=0
[2025-07-06 01:50:10.724] [info] Translating virtual to physical: virt=0x11260, process=0
[2025-07-06 01:50:10.724] [debug] Marked page dirty: virt=0x11260
[2025-07-06 01:50:10.724] [debug] Wrote virtual memory chunk: virt=0x11260, size=0x1
[2025-07-06 01:50:10.724] [debug] Writing virtual memory chunk: virt=0x10260, size=0x10, process=0
[2025-07-06 01:50:10.724] [info] Translating virtual to physical: virt=0x10260, process=0
[2025-07-06 01:50:10.725] [debug] Marked page dirty: virt=0x10260
[2025-07-06 01:50:10.725] [debug] Wrote virtual memory chunk: virt=0x10260, size=0x10
[2025-07-06 01:50:10.725] [debug] Writing virtual memory chunk: virt=0x11270, size=0x1, process=0
[2025-07-06 01:50:10.725] [info] Translating virtual to physical: virt=0x11270, process=0
[2025-07-06 01:50:10.725] [debug] Marked page dirty: virt=0x11270
[2025-07-06 01:50:10.725] [debug] Wrote virtual memory chunk: virt=0x11270, size=0x1
[2025-07-06 01:50:10.725] [debug] Writing virtual memory chunk: virt=0x10270, size=0x10, process=0
[2025-07-06 01:50:10.725] [info] Translating virtual to physical: virt=0x10270, process=0
[2025-07-06 01:50:10.725] [debug] Marked page dirty: virt=0x10270
[2025-07-06 01:50:10.725] [debug] Wrote virtual memory chunk: virt=0x10270, size=0x10
[2025-07-06 01:50:10.725] [debug] Writing virtual memory chunk: virt=0x11280, size=0x1, process=0
[2025-07-06 01:50:10.725] [info] Translating virtual to physical: virt=0x11280, process=0
[2025-07-06 01:50:10.725] [debug] Marked page dirty: virt=0x11280
[2025-07-06 01:50:10.725] [debug] Wrote virtual memory chunk: virt=0x11280, size=0x1
[2025-07-06 01:50:10.725] [debug] Writing virtual memory chunk: virt=0x10280, size=0x10, process=0
[2025-07-06 01:50:10.725] [info] Translating virtual to physical: virt=0x10280, process=0
[2025-07-06 01:50:10.725] [debug] Marked page dirty: virt=0x10280
[2025-07-06 01:50:10.726] [debug] Wrote virtual memory chunk: virt=0x10280, size=0x10
[2025-07-06 01:50:10.726] [debug] Writing virtual memory chunk: virt=0x11290, size=0x1, process=0
[2025-07-06 01:50:10.726] [info] Translating virtual to physical: virt=0x11290, process=0
[2025-07-06 01:50:10.726] [debug] Marked page dirty: virt=0x11290
[2025-07-06 01:50:10.726] [debug] Wrote virtual memory chunk: virt=0x11290, size=0x1
[2025-07-06 01:50:10.726] [debug] Writing virtual memory chunk: virt=0x10290, size=0x10, process=0
[2025-07-06 01:50:10.726] [info] Translating virtual to physical: virt=0x10290, process=0
[2025-07-06 01:50:10.726] [debug] Marked page dirty: virt=0x10290
[2025-07-06 01:50:10.726] [debug] Wrote virtual memory chunk: virt=0x10290, size=0x10
[2025-07-06 01:50:10.726] [debug] Writing virtual memory chunk: virt=0x112a0, size=0x1, process=0
[2025-07-06 01:50:10.726] [info] Translating virtual to physical: virt=0x112a0, process=0
[2025-07-06 01:50:10.726] [debug] Marked page dirty: virt=0x112a0
[2025-07-06 01:50:10.726] [debug] Wrote virtual memory chunk: virt=0x112a0, size=0x1
[2025-07-06 01:50:10.726] [debug] Writing virtual memory chunk: virt=0x102a0, size=0x10, process=0
[2025-07-06 01:50:10.726] [info] Translating virtual to physical: virt=0x102a0, process=0
[2025-07-06 01:50:10.726] [debug] Marked page dirty: virt=0x102a0
[2025-07-06 01:50:10.726] [debug] Wrote virtual memory chunk: virt=0x102a0, size=0x10
[2025-07-06 01:50:10.726] [debug] Writing virtual memory chunk: virt=0x112b0, size=0x1, process=0
[2025-07-06 01:50:10.727] [info] Translating virtual to physical: virt=0x112b0, process=0
[2025-07-06 01:50:10.727] [debug] Marked page dirty: virt=0x112b0
[2025-07-06 01:50:10.727] [debug] Wrote virtual memory chunk: virt=0x112b0, size=0x1
[2025-07-06 01:50:10.727] [debug] Writing virtual memory chunk: virt=0x102b0, size=0x10, process=0
[2025-07-06 01:50:10.727] [info] Translating virtual to physical: virt=0x102b0, process=0
[2025-07-06 01:50:10.727] [debug] Marked page dirty: virt=0x102b0
[2025-07-06 01:50:10.727] [debug] Wrote virtual memory chunk: virt=0x102b0, size=0x10
[2025-07-06 01:50:10.727] [debug] Writing virtual memory chunk: virt=0x112c0, size=0x1, process=0
[2025-07-06 01:50:10.727] [info] Translating virtual to physical: virt=0x112c0, process=0
[2025-07-06 01:50:10.727] [debug] Marked page dirty: virt=0x112c0
[2025-07-06 01:50:10.727] [debug] Wrote virtual memory chunk: virt=0x112c0, size=0x1
[2025-07-06 01:50:10.727] [debug] Writing virtual memory chunk: virt=0x102c0, size=0x10, process=0
[2025-07-06 01:50:10.727] [info] Translating virtual to physical: virt=0x102c0, process=0
[2025-07-06 01:50:10.727] [debug] Marked page dirty: virt=0x102c0
[2025-07-06 01:50:10.727] [debug] Wrote virtual memory chunk: virt=0x102c0, size=0x10
[2025-07-06 01:50:10.727] [debug] Writing virtual memory chunk: virt=0x112d0, size=0x1, process=0
[2025-07-06 01:50:10.728] [info] Translating virtual to physical: virt=0x112d0, process=0
[2025-07-06 01:50:10.728] [debug] Marked page dirty: virt=0x112d0
[2025-07-06 01:50:10.728] [debug] Wrote virtual memory chunk: virt=0x112d0, size=0x1
[2025-07-06 01:50:10.728] [debug] Writing virtual memory chunk: virt=0x102d0, size=0x10, process=0
[2025-07-06 01:50:10.728] [info] Translating virtual to physical: virt=0x102d0, process=0
[2025-07-06 01:50:10.728] [debug] Marked page dirty: virt=0x102d0
[2025-07-06 01:50:10.728] [debug] Wrote virtual memory chunk: virt=0x102d0, size=0x10
[2025-07-06 01:50:10.728] [debug] Writing virtual memory chunk: virt=0x112e0, size=0x1, process=0
[2025-07-06 01:50:10.728] [info] Translating virtual to physical: virt=0x112e0, process=0
[2025-07-06 01:50:10.728] [debug] Marked page dirty: virt=0x112e0
[2025-07-06 01:50:10.728] [debug] Wrote virtual memory chunk: virt=0x112e0, size=0x1
[2025-07-06 01:50:10.728] [debug] Writing virtual memory chunk: virt=0x102e0, size=0x10, process=0
[2025-07-06 01:50:10.728] [info] Translating virtual to physical: virt=0x102e0, process=0
[2025-07-06 01:50:10.728] [debug] Marked page dirty: virt=0x102e0
[2025-07-06 01:50:10.728] [debug] Wrote virtual memory chunk: virt=0x102e0, size=0x10
[2025-07-06 01:50:10.728] [debug] Writing virtual memory chunk: virt=0x112f0, size=0x1, process=0
[2025-07-06 01:50:10.729] [info] Translating virtual to physical: virt=0x112f0, process=0
[2025-07-06 01:50:10.729] [debug] Marked page dirty: virt=0x112f0
[2025-07-06 01:50:10.729] [debug] Wrote virtual memory chunk: virt=0x112f0, size=0x1
[2025-07-06 01:50:10.729] [debug] Writing virtual memory chunk: virt=0x102f0, size=0x10, process=0
[2025-07-06 01:50:10.729] [info] Translating virtual to physical: virt=0x102f0, process=0
[2025-07-06 01:50:10.729] [debug] Marked page dirty: virt=0x102f0
[2025-07-06 01:50:10.729] [debug] Wrote virtual memory chunk: virt=0x102f0, size=0x10
[2025-07-06 01:50:10.729] [debug] Writing virtual memory chunk: virt=0x11300, size=0x1, process=0
[2025-07-06 01:50:10.729] [info] Translating virtual to physical: virt=0x11300, process=0
[2025-07-06 01:50:10.729] [debug] Marked page dirty: virt=0x11300
[2025-07-06 01:50:10.729] [debug] Wrote virtual memory chunk: virt=0x11300, size=0x1
[2025-07-06 01:50:10.729] [debug] Writing virtual memory chunk: virt=0x10300, size=0x10, process=0
[2025-07-06 01:50:10.729] [info] Translating virtual to physical: virt=0x10300, process=0
[2025-07-06 01:50:10.729] [debug] Marked page dirty: virt=0x10300
[2025-07-06 01:50:10.729] [debug] Wrote virtual memory chunk: virt=0x10300, size=0x10
[2025-07-06 01:50:10.729] [debug] Writing virtual memory chunk: virt=0x11310, size=0x1, process=0
[2025-07-06 01:50:10.729] [info] Translating virtual to physical: virt=0x11310, process=0
[2025-07-06 01:50:10.729] [debug] Marked page dirty: virt=0x11310
[2025-07-06 01:50:10.730] [debug] Wrote virtual memory chunk: virt=0x11310, size=0x1
[2025-07-06 01:50:10.730] [debug] Writing virtual memory chunk: virt=0x10310, size=0x10, process=0
[2025-07-06 01:50:10.730] [info] Translating virtual to physical: virt=0x10310, process=0
[2025-07-06 01:50:10.730] [debug] Marked page dirty: virt=0x10310
[2025-07-06 01:50:10.730] [debug] Wrote virtual memory chunk: virt=0x10310, size=0x10
[2025-07-06 01:50:10.730] [debug] Writing virtual memory chunk: virt=0x11320, size=0x1, process=0
[2025-07-06 01:50:10.730] [info] Translating virtual to physical: virt=0x11320, process=0
[2025-07-06 01:50:10.730] [debug] Marked page dirty: virt=0x11320
[2025-07-06 01:50:10.730] [debug] Wrote virtual memory chunk: virt=0x11320, size=0x1
[2025-07-06 01:50:10.730] [debug] Writing virtual memory chunk: virt=0x10320, size=0x10, process=0
[2025-07-06 01:50:10.730] [info] Translating virtual to physical: virt=0x10320, process=0
[2025-07-06 01:50:10.730] [debug] Marked page dirty: virt=0x10320
[2025-07-06 01:50:10.730] [debug] Wrote virtual memory chunk: virt=0x10320, size=0x10
[2025-07-06 01:50:10.730] [info] Processed descriptor 50
[2025-07-06 01:50:10.730] [debug] Writing virtual memory chunk: virt=0x11330, size=0x1, process=0
[2025-07-06 01:50:10.730] [info] Translating virtual to physical: virt=0x11330, process=0
[2025-07-06 01:50:10.731] [debug] Marked page dirty: virt=0x11330
[2025-07-06 01:50:10.731] [debug] Wrote virtual memory chunk: virt=0x11330, size=0x1
[2025-07-06 01:50:10.731] [debug] Writing virtual memory chunk: virt=0x10330, size=0x10, process=0
[2025-07-06 01:50:10.731] [info] Translating virtual to physical: virt=0x10330, process=0
[2025-07-06 01:50:10.731] [debug] Marked page dirty: virt=0x10330
[2025-07-06 01:50:10.731] [debug] Wrote virtual memory chunk: virt=0x10330, size=0x10
[2025-07-06 01:50:10.731] [debug] Writing virtual memory chunk: virt=0x11340, size=0x1, process=0
[2025-07-06 01:50:10.731] [info] Translating virtual to physical: virt=0x11340, process=0
[2025-07-06 01:50:10.731] [debug] Marked page dirty: virt=0x11340
[2025-07-06 01:50:10.731] [debug] Wrote virtual memory chunk: virt=0x11340, size=0x1
[2025-07-06 01:50:10.731] [debug] Writing virtual memory chunk: virt=0x10340, size=0x10, process=0
[2025-07-06 01:50:10.731] [info] Translating virtual to physical: virt=0x10340, process=0
[2025-07-06 01:50:10.731] [debug] Marked page dirty: virt=0x10340
[2025-07-06 01:50:10.731] [debug] Wrote virtual memory chunk: virt=0x10340, size=0x10
[2025-07-06 01:50:10.731] [debug] Writing virtual memory chunk: virt=0x11350, size=0x1, process=0
[2025-07-06 01:50:10.731] [info] Translating virtual to physical: virt=0x11350, process=0
[2025-07-06 01:50:10.731] [debug] Marked page dirty: virt=0x11350
[2025-07-06 01:50:10.732] [debug] Wrote virtual memory chunk: virt=0x11350, size=0x1
[2025-07-06 01:50:10.732] [debug] Writing virtual memory chunk: virt=0x10350, size=0x10, process=0
[2025-07-06 01:50:10.733] [info] Translating virtual to physical: virt=0x10350, process=0
[2025-07-06 01:50:10.733] [debug] Marked page dirty: virt=0x10350
[2025-07-06 01:50:10.733] [debug] Wrote virtual memory chunk: virt=0x10350, size=0x10
[2025-07-06 01:50:10.733] [debug] Writing virtual memory chunk: virt=0x11360, size=0x1, process=0
[2025-07-06 01:50:10.733] [info] Translating virtual to physical: virt=0x11360, process=0
[2025-07-06 01:50:10.734] [debug] Marked page dirty: virt=0x11360
[2025-07-06 01:50:10.734] [debug] Wrote virtual memory chunk: virt=0x11360, size=0x1
[2025-07-06 01:50:10.734] [debug] Writing virtual memory chunk: virt=0x10360, size=0x10, process=0
[2025-07-06 01:50:10.734] [info] Translating virtual to physical: virt=0x10360, process=0
[2025-07-06 01:50:10.734] [debug] Marked page dirty: virt=0x10360
[2025-07-06 01:50:10.734] [debug] Wrote virtual memory chunk: virt=0x10360, size=0x10
[2025-07-06 01:50:10.734] [debug] Writing virtual memory chunk: virt=0x11370, size=0x1, process=0
[2025-07-06 01:50:10.734] [info] Translating virtual to physical: virt=0x11370, process=0
[2025-07-06 01:50:10.734] [debug] Marked page dirty: virt=0x11370
[2025-07-06 01:50:10.734] [debug] Wrote virtual memory chunk: virt=0x11370, size=0x1
[2025-07-06 01:50:10.734] [debug] Writing virtual memory chunk: virt=0x10370, size=0x10, process=0
[2025-07-06 01:50:10.734] [info] Translating virtual to physical: virt=0x10370, process=0
[2025-07-06 01:50:10.734] [debug] Marked page dirty: virt=0x10370
[2025-07-06 01:50:10.735] [debug] Wrote virtual memory chunk: virt=0x10370, size=0x10
[2025-07-06 01:50:10.735] [debug] Writing virtual memory chunk: virt=0x11380, size=0x1, process=0
[2025-07-06 01:50:10.735] [info] Translating virtual to physical: virt=0x11380, process=0
[2025-07-06 01:50:10.735] [debug] Marked page dirty: virt=0x11380
[2025-07-06 01:50:10.735] [debug] Wrote virtual memory chunk: virt=0x11380, size=0x1
[2025-07-06 01:50:10.735] [debug] Writing virtual memory chunk: virt=0x10380, size=0x10, process=0
[2025-07-06 01:50:10.735] [info] Translating virtual to physical: virt=0x10380, process=0
[2025-07-06 01:50:10.735] [debug] Marked page dirty: virt=0x10380
[2025-07-06 01:50:10.735] [debug] Wrote virtual memory chunk: virt=0x10380, size=0x10
[2025-07-06 01:50:10.735] [debug] Writing virtual memory chunk: virt=0x11390, size=0x1, process=0
[2025-07-06 01:50:10.735] [info] Translating virtual to physical: virt=0x11390, process=0
[2025-07-06 01:50:10.735] [debug] Marked page dirty: virt=0x11390
[2025-07-06 01:50:10.735] [debug] Wrote virtual memory chunk: virt=0x11390, size=0x1
[2025-07-06 01:50:10.735] [debug] Writing virtual memory chunk: virt=0x10390, size=0x10, process=0
[2025-07-06 01:50:10.735] [info] Translating virtual to physical: virt=0x10390, process=0
[2025-07-06 01:50:10.735] [debug] Marked page dirty: virt=0x10390
[2025-07-06 01:50:10.735] [debug] Wrote virtual memory chunk: virt=0x10390, size=0x10
[2025-07-06 01:50:10.736] [debug] Writing virtual memory chunk: virt=0x113a0, size=0x1, process=0
[2025-07-06 01:50:10.736] [info] Translating virtual to physical: virt=0x113a0, process=0
[2025-07-06 01:50:10.736] [debug] Marked page dirty: virt=0x113a0
[2025-07-06 01:50:10.736] [debug] Wrote virtual memory chunk: virt=0x113a0, size=0x1
[2025-07-06 01:50:10.736] [debug] Writing virtual memory chunk: virt=0x103a0, size=0x10, process=0
[2025-07-06 01:50:10.736] [info] Translating virtual to physical: virt=0x103a0, process=0
[2025-07-06 01:50:10.736] [debug] Marked page dirty: virt=0x103a0
[2025-07-06 01:50:10.736] [debug] Wrote virtual memory chunk: virt=0x103a0, size=0x10
[2025-07-06 01:50:10.736] [debug] Writing virtual memory chunk: virt=0x113b0, size=0x1, process=0
[2025-07-06 01:50:10.736] [info] Translating virtual to physical: virt=0x113b0, process=0
[2025-07-06 01:50:10.736] [debug] Marked page dirty: virt=0x113b0
[2025-07-06 01:50:10.736] [debug] Wrote virtual memory chunk: virt=0x113b0, size=0x1
[2025-07-06 01:50:10.736] [debug] Writing virtual memory chunk: virt=0x103b0, size=0x10, process=0
[2025-07-06 01:50:10.736] [info] Translating virtual to physical: virt=0x103b0, process=0
[2025-07-06 01:50:10.736] [debug] Marked page dirty: virt=0x103b0
[2025-07-06 01:50:10.736] [debug] Wrote virtual memory chunk: virt=0x103b0, size=0x10
[2025-07-06 01:50:10.736] [debug] Writing virtual memory chunk: virt=0x113c0, size=0x1, process=0
[2025-07-06 01:50:10.736] [info] Translating virtual to physical: virt=0x113c0, process=0
[2025-07-06 01:50:10.737] [debug] Marked page dirty: virt=0x113c0
[2025-07-06 01:50:10.737] [debug] Wrote virtual memory chunk: virt=0x113c0, size=0x1
[2025-07-06 01:50:10.737] [debug] Writing virtual memory chunk: virt=0x103c0, size=0x10, process=0
[2025-07-06 01:50:10.737] [info] Translating virtual to physical: virt=0x103c0, process=0
[2025-07-06 01:50:10.737] [debug] Marked page dirty: virt=0x103c0
[2025-07-06 01:50:10.737] [debug] Wrote virtual memory chunk: virt=0x103c0, size=0x10
[2025-07-06 01:50:10.737] [debug] Writing virtual memory chunk: virt=0x113d0, size=0x1, process=0
[2025-07-06 01:50:10.737] [info] Translating virtual to physical: virt=0x113d0, process=0
[2025-07-06 01:50:10.737] [debug] Marked page dirty: virt=0x113d0
[2025-07-06 01:50:10.737] [debug] Wrote virtual memory chunk: virt=0x113d0, size=0x1
[2025-07-06 01:50:10.737] [debug] Writing virtual memory chunk: virt=0x103d0, size=0x10, process=0
[2025-07-06 01:50:10.737] [info] Translating virtual to physical: virt=0x103d0, process=0
[2025-07-06 01:50:10.737] [debug] Marked page dirty: virt=0x103d0
[2025-07-06 01:50:10.737] [debug] Wrote virtual memory chunk: virt=0x103d0, size=0x10
[2025-07-06 01:50:10.737] [debug] Writing virtual memory chunk: virt=0x113e0, size=0x1, process=0
[2025-07-06 01:50:10.737] [info] Translating virtual to physical: virt=0x113e0, process=0
[2025-07-06 01:50:10.737] [debug] Marked page dirty: virt=0x113e0
[2025-07-06 01:50:10.737] [debug] Wrote virtual memory chunk: virt=0x113e0, size=0x1
[2025-07-06 01:50:10.738] [debug] Writing virtual memory chunk: virt=0x103e0, size=0x10, process=0
[2025-07-06 01:50:10.738] [info] Translating virtual to physical: virt=0x103e0, process=0
[2025-07-06 01:50:10.738] [debug] Marked page dirty: virt=0x103e0
[2025-07-06 01:50:10.738] [debug] Wrote virtual memory chunk: virt=0x103e0, size=0x10
[2025-07-06 01:50:10.738] [debug] Writing virtual memory chunk: virt=0x113f0, size=0x1, process=0
[2025-07-06 01:50:10.738] [info] Translating virtual to physical: virt=0x113f0, process=0
[2025-07-06 01:50:10.738] [debug] Marked page dirty: virt=0x113f0
[2025-07-06 01:50:10.738] [debug] Wrote virtual memory chunk: virt=0x113f0, size=0x1
[2025-07-06 01:50:10.738] [debug] Writing virtual memory chunk: virt=0x103f0, size=0x10, process=0
[2025-07-06 01:50:10.738] [info] Translating virtual to physical: virt=0x103f0, process=0
[2025-07-06 01:50:10.738] [debug] Marked page dirty: virt=0x103f0
[2025-07-06 01:50:10.738] [debug] Wrote virtual memory chunk: virt=0x103f0, size=0x10
[2025-07-06 01:50:10.738] [debug] Writing virtual memory chunk: virt=0x11400, size=0x1, process=0
[2025-07-06 01:50:10.738] [info] Translating virtual to physical: virt=0x11400, process=0
[2025-07-06 01:50:10.738] [debug] Marked page dirty: virt=0x11400
[2025-07-06 01:50:10.738] [debug] Wrote virtual memory chunk: virt=0x11400, size=0x1
[2025-07-06 01:50:10.738] [debug] Writing virtual memory chunk: virt=0x10400, size=0x10, process=0
[2025-07-06 01:50:10.738] [info] Translating virtual to physical: virt=0x10400, process=0
[2025-07-06 01:50:10.739] [debug] Marked page dirty: virt=0x10400
[2025-07-06 01:50:10.739] [debug] Wrote virtual memory chunk: virt=0x10400, size=0x10
[2025-07-06 01:50:10.739] [debug] Writing virtual memory chunk: virt=0x11410, size=0x1, process=0
[2025-07-06 01:50:10.739] [info] Translating virtual to physical: virt=0x11410, process=0
[2025-07-06 01:50:10.739] [debug] Marked page dirty: virt=0x11410
[2025-07-06 01:50:10.739] [debug] Wrote virtual memory chunk: virt=0x11410, size=0x1
[2025-07-06 01:50:10.739] [debug] Writing virtual memory chunk: virt=0x10410, size=0x10, process=0
[2025-07-06 01:50:10.739] [info] Translating virtual to physical: virt=0x10410, process=0
[2025-07-06 01:50:10.739] [debug] Marked page dirty: virt=0x10410
[2025-07-06 01:50:10.739] [debug] Wrote virtual memory chunk: virt=0x10410, size=0x10
[2025-07-06 01:50:10.739] [debug] Writing virtual memory chunk: virt=0x11420, size=0x1, process=0
[2025-07-06 01:50:10.739] [info] Translating virtual to physical: virt=0x11420, process=0
[2025-07-06 01:50:10.739] [debug] Marked page dirty: virt=0x11420
[2025-07-06 01:50:10.739] [debug] Wrote virtual memory chunk: virt=0x11420, size=0x1
[2025-07-06 01:50:10.739] [debug] Writing virtual memory chunk: virt=0x10420, size=0x10, process=0
[2025-07-06 01:50:10.739] [info] Translating virtual to physical: virt=0x10420, process=0
[2025-07-06 01:50:10.740] [debug] Marked page dirty: virt=0x10420
[2025-07-06 01:50:10.740] [debug] Wrote virtual memory chunk: virt=0x10420, size=0x10
[2025-07-06 01:50:10.740] [debug] Writing virtual memory chunk: virt=0x11430, size=0x1, process=0
[2025-07-06 01:50:10.740] [info] Translating virtual to physical: virt=0x11430, process=0
[2025-07-06 01:50:10.740] [debug] Marked page dirty: virt=0x11430
[2025-07-06 01:50:10.740] [debug] Wrote virtual memory chunk: virt=0x11430, size=0x1
[2025-07-06 01:50:10.740] [debug] Writing virtual memory chunk: virt=0x10430, size=0x10, process=0
[2025-07-06 01:50:10.740] [info] Translating virtual to physical: virt=0x10430, process=0
[2025-07-06 01:50:10.740] [debug] Marked page dirty: virt=0x10430
[2025-07-06 01:50:10.740] [debug] Wrote virtual memory chunk: virt=0x10430, size=0x10
[2025-07-06 01:50:10.740] [debug] Writing virtual memory chunk: virt=0x11440, size=0x1, process=0
[2025-07-06 01:50:10.740] [info] Translating virtual to physical: virt=0x11440, process=0
[2025-07-06 01:50:10.740] [debug] Marked page dirty: virt=0x11440
[2025-07-06 01:50:10.740] [debug] Wrote virtual memory chunk: virt=0x11440, size=0x1
[2025-07-06 01:50:10.741] [debug] Writing virtual memory chunk: virt=0x10440, size=0x10, process=0
[2025-07-06 01:50:10.741] [info] Translating virtual to physical: virt=0x10440, process=0
[2025-07-06 01:50:10.741] [debug] Marked page dirty: virt=0x10440
[2025-07-06 01:50:10.741] [debug] Wrote virtual memory chunk: virt=0x10440, size=0x10
[2025-07-06 01:50:10.741] [debug] Writing virtual memory chunk: virt=0x11450, size=0x1, process=0
[2025-07-06 01:50:10.741] [info] Translating virtual to physical: virt=0x11450, process=0
[2025-07-06 01:50:10.741] [debug] Marked page dirty: virt=0x11450
[2025-07-06 01:50:10.741] [debug] Wrote virtual memory chunk: virt=0x11450, size=0x1
[2025-07-06 01:50:10.741] [debug] Writing virtual memory chunk: virt=0x10450, size=0x10, process=0
[2025-07-06 01:50:10.741] [info] Translating virtual to physical: virt=0x10450, process=0
[2025-07-06 01:50:10.741] [debug] Marked page dirty: virt=0x10450
[2025-07-06 01:50:10.741] [debug] Wrote virtual memory chunk: virt=0x10450, size=0x10
[2025-07-06 01:50:10.741] [debug] Writing virtual memory chunk: virt=0x11460, size=0x1, process=0
[2025-07-06 01:50:10.741] [info] Translating virtual to physical: virt=0x11460, process=0
[2025-07-06 01:50:10.741] [debug] Marked page dirty: virt=0x11460
[2025-07-06 01:50:10.741] [debug] Wrote virtual memory chunk: virt=0x11460, size=0x1
[2025-07-06 01:50:10.741] [debug] Writing virtual memory chunk: virt=0x10460, size=0x10, process=0
[2025-07-06 01:50:10.742] [info] Translating virtual to physical: virt=0x10460, process=0
[2025-07-06 01:50:10.742] [debug] Marked page dirty: virt=0x10460
[2025-07-06 01:50:10.742] [debug] Wrote virtual memory chunk: virt=0x10460, size=0x10
[2025-07-06 01:50:10.742] [debug] Writing virtual memory chunk: virt=0x11470, size=0x1, process=0
[2025-07-06 01:50:10.742] [info] Translating virtual to physical: virt=0x11470, process=0
[2025-07-06 01:50:10.742] [debug] Marked page dirty: virt=0x11470
[2025-07-06 01:50:10.742] [debug] Wrote virtual memory chunk: virt=0x11470, size=0x1
[2025-07-06 01:50:10.742] [debug] Writing virtual memory chunk: virt=0x10470, size=0x10, process=0
[2025-07-06 01:50:10.742] [info] Translating virtual to physical: virt=0x10470, process=0
[2025-07-06 01:50:10.742] [debug] Marked page dirty: virt=0x10470
[2025-07-06 01:50:10.742] [debug] Wrote virtual memory chunk: virt=0x10470, size=0x10
[2025-07-06 01:50:10.742] [debug] Writing virtual memory chunk: virt=0x11480, size=0x1, process=0
[2025-07-06 01:50:10.742] [info] Translating virtual to physical: virt=0x11480, process=0
[2025-07-06 01:50:10.742] [debug] Marked page dirty: virt=0x11480
[2025-07-06 01:50:10.742] [debug] Wrote virtual memory chunk: virt=0x11480, size=0x1
[2025-07-06 01:50:10.742] [debug] Writing virtual memory chunk: virt=0x10480, size=0x10, process=0
[2025-07-06 01:50:10.742] [info] Translating virtual to physical: virt=0x10480, process=0
[2025-07-06 01:50:10.743] [debug] Marked page dirty: virt=0x10480
[2025-07-06 01:50:10.743] [debug] Wrote virtual memory chunk: virt=0x10480, size=0x10
[2025-07-06 01:50:10.743] [debug] Writing virtual memory chunk: virt=0x11490, size=0x1, process=0
[2025-07-06 01:50:10.743] [info] Translating virtual to physical: virt=0x11490, process=0
[2025-07-06 01:50:10.743] [debug] Marked page dirty: virt=0x11490
[2025-07-06 01:50:10.743] [debug] Wrote virtual memory chunk: virt=0x11490, size=0x1
[2025-07-06 01:50:10.743] [debug] Writing virtual memory chunk: virt=0x10490, size=0x10, process=0
[2025-07-06 01:50:10.743] [info] Translating virtual to physical: virt=0x10490, process=0
[2025-07-06 01:50:10.743] [debug] Marked page dirty: virt=0x10490
[2025-07-06 01:50:10.743] [debug] Wrote virtual memory chunk: virt=0x10490, size=0x10
[2025-07-06 01:50:10.743] [debug] Writing virtual memory chunk: virt=0x114a0, size=0x1, process=0
[2025-07-06 01:50:10.743] [info] Translating virtual to physical: virt=0x114a0, process=0
[2025-07-06 01:50:10.743] [debug] Marked page dirty: virt=0x114a0
[2025-07-06 01:50:10.743] [debug] Wrote virtual memory chunk: virt=0x114a0, size=0x1
[2025-07-06 01:50:10.743] [debug] Writing virtual memory chunk: virt=0x104a0, size=0x10, process=0
[2025-07-06 01:50:10.743] [info] Translating virtual to physical: virt=0x104a0, process=0
[2025-07-06 01:50:10.744] [debug] Marked page dirty: virt=0x104a0
[2025-07-06 01:50:10.744] [debug] Wrote virtual memory chunk: virt=0x104a0, size=0x10
[2025-07-06 01:50:10.744] [debug] Writing virtual memory chunk: virt=0x114b0, size=0x1, process=0
[2025-07-06 01:50:10.744] [info] Translating virtual to physical: virt=0x114b0, process=0
[2025-07-06 01:50:10.744] [debug] Marked page dirty: virt=0x114b0
[2025-07-06 01:50:10.744] [debug] Wrote virtual memory chunk: virt=0x114b0, size=0x1
[2025-07-06 01:50:10.744] [debug] Writing virtual memory chunk: virt=0x104b0, size=0x10, process=0
[2025-07-06 01:50:10.744] [info] Translating virtual to physical: virt=0x104b0, process=0
[2025-07-06 01:50:10.744] [debug] Marked page dirty: virt=0x104b0
[2025-07-06 01:50:10.744] [debug] Wrote virtual memory chunk: virt=0x104b0, size=0x10
[2025-07-06 01:50:10.744] [debug] Writing virtual memory chunk: virt=0x114c0, size=0x1, process=0
[2025-07-06 01:50:10.744] [info] Translating virtual to physical: virt=0x114c0, process=0
[2025-07-06 01:50:10.744] [debug] Marked page dirty: virt=0x114c0
[2025-07-06 01:50:10.744] [debug] Wrote virtual memory chunk: virt=0x114c0, size=0x1
[2025-07-06 01:50:10.744] [debug] Writing virtual memory chunk: virt=0x104c0, size=0x10, process=0
[2025-07-06 01:50:10.744] [info] Translating virtual to physical: virt=0x104c0, process=0
[2025-07-06 01:50:10.744] [debug] Marked page dirty: virt=0x104c0
[2025-07-06 01:50:10.744] [debug] Wrote virtual memory chunk: virt=0x104c0, size=0x10
[2025-07-06 01:50:10.745] [debug] Writing virtual memory chunk: virt=0x114d0, size=0x1, process=0
[2025-07-06 01:50:10.745] [info] Translating virtual to physical: virt=0x114d0, process=0
[2025-07-06 01:50:10.745] [debug] Marked page dirty: virt=0x114d0
[2025-07-06 01:50:10.745] [debug] Wrote virtual memory chunk: virt=0x114d0, size=0x1
[2025-07-06 01:50:10.745] [debug] Writing virtual memory chunk: virt=0x104d0, size=0x10, process=0
[2025-07-06 01:50:10.745] [info] Translating virtual to physical: virt=0x104d0, process=0
[2025-07-06 01:50:10.745] [debug] Marked page dirty: virt=0x104d0
[2025-07-06 01:50:10.745] [debug] Wrote virtual memory chunk: virt=0x104d0, size=0x10
[2025-07-06 01:50:10.745] [debug] Writing virtual memory chunk: virt=0x114e0, size=0x1, process=0
[2025-07-06 01:50:10.745] [info] Translating virtual to physical: virt=0x114e0, process=0
[2025-07-06 01:50:10.745] [debug] Marked page dirty: virt=0x114e0
[2025-07-06 01:50:10.745] [debug] Wrote virtual memory chunk: virt=0x114e0, size=0x1
[2025-07-06 01:50:10.745] [debug] Writing virtual memory chunk: virt=0x104e0, size=0x10, process=0
[2025-07-06 01:50:10.745] [info] Translating virtual to physical: virt=0x104e0, process=0
[2025-07-06 01:50:10.745] [debug] Marked page dirty: virt=0x104e0
[2025-07-06 01:50:10.745] [debug] Wrote virtual memory chunk: virt=0x104e0, size=0x10
[2025-07-06 01:50:10.746] [debug] Writing virtual memory chunk: virt=0x114f0, size=0x1, process=0
[2025-07-06 01:50:10.746] [info] Translating virtual to physical: virt=0x114f0, process=0
[2025-07-06 01:50:10.746] [debug] Marked page dirty: virt=0x114f0
[2025-07-06 01:50:10.746] [debug] Wrote virtual memory chunk: virt=0x114f0, size=0x1
[2025-07-06 01:50:10.746] [debug] Writing virtual memory chunk: virt=0x104f0, size=0x10, process=0
[2025-07-06 01:50:10.746] [info] Translating virtual to physical: virt=0x104f0, process=0
[2025-07-06 01:50:10.746] [debug] Marked page dirty: virt=0x104f0
[2025-07-06 01:50:10.746] [debug] Wrote virtual memory chunk: virt=0x104f0, size=0x10
[2025-07-06 01:50:10.746] [debug] Writing virtual memory chunk: virt=0x11500, size=0x1, process=0
[2025-07-06 01:50:10.746] [info] Translating virtual to physical: virt=0x11500, process=0
[2025-07-06 01:50:10.746] [debug] Marked page dirty: virt=0x11500
[2025-07-06 01:50:10.746] [debug] Wrote virtual memory chunk: virt=0x11500, size=0x1
[2025-07-06 01:50:10.746] [debug] Writing virtual memory chunk: virt=0x10500, size=0x10, process=0
[2025-07-06 01:50:10.746] [info] Translating virtual to physical: virt=0x10500, process=0
[2025-07-06 01:50:10.746] [debug] Marked page dirty: virt=0x10500
[2025-07-06 01:50:10.746] [debug] Wrote virtual memory chunk: virt=0x10500, size=0x10
[2025-07-06 01:50:10.747] [debug] Writing virtual memory chunk: virt=0x11510, size=0x1, process=0
[2025-07-06 01:50:10.747] [info] Translating virtual to physical: virt=0x11510, process=0
[2025-07-06 01:50:10.747] [debug] Marked page dirty: virt=0x11510
[2025-07-06 01:50:10.747] [debug] Wrote virtual memory chunk: virt=0x11510, size=0x1
[2025-07-06 01:50:10.747] [debug] Writing virtual memory chunk: virt=0x10510, size=0x10, process=0
[2025-07-06 01:50:10.747] [info] Translating virtual to physical: virt=0x10510, process=0
[2025-07-06 01:50:10.747] [debug] Marked page dirty: virt=0x10510
[2025-07-06 01:50:10.747] [debug] Wrote virtual memory chunk: virt=0x10510, size=0x10
[2025-07-06 01:50:10.747] [debug] Writing virtual memory chunk: virt=0x11520, size=0x1, process=0
[2025-07-06 01:50:10.749] [info] Translating virtual to physical: virt=0x11520, process=0
[2025-07-06 01:50:10.749] [debug] Marked page dirty: virt=0x11520
[2025-07-06 01:50:10.749] [debug] Wrote virtual memory chunk: virt=0x11520, size=0x1
[2025-07-06 01:50:10.749] [debug] Writing virtual memory chunk: virt=0x10520, size=0x10, process=0
[2025-07-06 01:50:10.749] [info] Translating virtual to physical: virt=0x10520, process=0
[2025-07-06 01:50:10.749] [debug] Marked page dirty: virt=0x10520
[2025-07-06 01:50:10.749] [debug] Wrote virtual memory chunk: virt=0x10520, size=0x10
[2025-07-06 01:50:10.749] [debug] Writing virtual memory chunk: virt=0x11530, size=0x1, process=0
[2025-07-06 01:50:10.749] [info] Translating virtual to physical: virt=0x11530, process=0
[2025-07-06 01:50:10.749] [debug] Marked page dirty: virt=0x11530
[2025-07-06 01:50:10.749] [debug] Wrote virtual memory chunk: virt=0x11530, size=0x1
[2025-07-06 01:50:10.749] [debug] Writing virtual memory chunk: virt=0x10530, size=0x10, process=0
[2025-07-06 01:50:10.749] [info] Translating virtual to physical: virt=0x10530, process=0
[2025-07-06 01:50:10.750] [debug] Marked page dirty: virt=0x10530
[2025-07-06 01:50:10.750] [debug] Wrote virtual memory chunk: virt=0x10530, size=0x10
[2025-07-06 01:50:10.750] [debug] Writing virtual memory chunk: virt=0x11540, size=0x1, process=0
[2025-07-06 01:50:10.750] [info] Translating virtual to physical: virt=0x11540, process=0
[2025-07-06 01:50:10.750] [debug] Marked page dirty: virt=0x11540
[2025-07-06 01:50:10.750] [debug] Wrote virtual memory chunk: virt=0x11540, size=0x1
[2025-07-06 01:50:10.750] [debug] Writing virtual memory chunk: virt=0x10540, size=0x10, process=0
[2025-07-06 01:50:10.750] [info] Translating virtual to physical: virt=0x10540, process=0
[2025-07-06 01:50:10.750] [debug] Marked page dirty: virt=0x10540
[2025-07-06 01:50:10.750] [debug] Wrote virtual memory chunk: virt=0x10540, size=0x10
[2025-07-06 01:50:10.750] [debug] Writing virtual memory chunk: virt=0x11550, size=0x1, process=0
[2025-07-06 01:50:10.750] [info] Translating virtual to physical: virt=0x11550, process=0
[2025-07-06 01:50:10.750] [debug] Marked page dirty: virt=0x11550
[2025-07-06 01:50:10.750] [debug] Wrote virtual memory chunk: virt=0x11550, size=0x1
[2025-07-06 01:50:10.751] [debug] Writing virtual memory chunk: virt=0x10550, size=0x10, process=0
[2025-07-06 01:50:10.751] [info] Translating virtual to physical: virt=0x10550, process=0
[2025-07-06 01:50:10.751] [debug] Marked page dirty: virt=0x10550
[2025-07-06 01:50:10.751] [debug] Wrote virtual memory chunk: virt=0x10550, size=0x10
[2025-07-06 01:50:10.751] [debug] Writing virtual memory chunk: virt=0x11560, size=0x1, process=0
[2025-07-06 01:50:10.751] [info] Translating virtual to physical: virt=0x11560, process=0
[2025-07-06 01:50:10.751] [debug] Marked page dirty: virt=0x11560
[2025-07-06 01:50:10.751] [debug] Wrote virtual memory chunk: virt=0x11560, size=0x1
[2025-07-06 01:50:10.751] [debug] Writing virtual memory chunk: virt=0x10560, size=0x10, process=0
[2025-07-06 01:50:10.751] [info] Translating virtual to physical: virt=0x10560, process=0
[2025-07-06 01:50:10.751] [debug] Marked page dirty: virt=0x10560
[2025-07-06 01:50:10.751] [debug] Wrote virtual memory chunk: virt=0x10560, size=0x10
[2025-07-06 01:50:10.751] [debug] Writing virtual memory chunk: virt=0x11570, size=0x1, process=0
[2025-07-06 01:50:10.751] [info] Translating virtual to physical: virt=0x11570, process=0
[2025-07-06 01:50:10.751] [debug] Marked page dirty: virt=0x11570
[2025-07-06 01:50:10.751] [debug] Wrote virtual memory chunk: virt=0x11570, size=0x1
[2025-07-06 01:50:10.751] [debug] Writing virtual memory chunk: virt=0x10570, size=0x10, process=0
[2025-07-06 01:50:10.751] [info] Translating virtual to physical: virt=0x10570, process=0
[2025-07-06 01:50:10.752] [debug] Marked page dirty: virt=0x10570
[2025-07-06 01:50:10.752] [debug] Wrote virtual memory chunk: virt=0x10570, size=0x10
[2025-07-06 01:50:10.752] [debug] Writing virtual memory chunk: virt=0x11580, size=0x1, process=0
[2025-07-06 01:50:10.752] [info] Translating virtual to physical: virt=0x11580, process=0
[2025-07-06 01:50:10.752] [debug] Marked page dirty: virt=0x11580
[2025-07-06 01:50:10.752] [debug] Wrote virtual memory chunk: virt=0x11580, size=0x1
[2025-07-06 01:50:10.752] [debug] Writing virtual memory chunk: virt=0x10580, size=0x10, process=0
[2025-07-06 01:50:10.752] [info] Translating virtual to physical: virt=0x10580, process=0
[2025-07-06 01:50:10.752] [debug] Marked page dirty: virt=0x10580
[2025-07-06 01:50:10.752] [debug] Wrote virtual memory chunk: virt=0x10580, size=0x10
[2025-07-06 01:50:10.752] [debug] Writing virtual memory chunk: virt=0x11590, size=0x1, process=0
[2025-07-06 01:50:10.752] [info] Translating virtual to physical: virt=0x11590, process=0
[2025-07-06 01:50:10.752] [debug] Marked page dirty: virt=0x11590
[2025-07-06 01:50:10.752] [debug] Wrote virtual memory chunk: virt=0x11590, size=0x1
[2025-07-06 01:50:10.752] [debug] Writing virtual memory chunk: virt=0x10590, size=0x10, process=0
[2025-07-06 01:50:10.752] [info] Translating virtual to physical: virt=0x10590, process=0
[2025-07-06 01:50:10.753] [debug] Marked page dirty: virt=0x10590
[2025-07-06 01:50:10.753] [debug] Wrote virtual memory chunk: virt=0x10590, size=0x10
[2025-07-06 01:50:10.753] [debug] Writing virtual memory chunk: virt=0x115a0, size=0x1, process=0
[2025-07-06 01:50:10.753] [info] Translating virtual to physical: virt=0x115a0, process=0
[2025-07-06 01:50:10.753] [debug] Marked page dirty: virt=0x115a0
[2025-07-06 01:50:10.753] [debug] Wrote virtual memory chunk: virt=0x115a0, size=0x1
[2025-07-06 01:50:10.753] [debug] Writing virtual memory chunk: virt=0x105a0, size=0x10, process=0
[2025-07-06 01:50:10.753] [info] Translating virtual to physical: virt=0x105a0, process=0
[2025-07-06 01:50:10.753] [debug] Marked page dirty: virt=0x105a0
[2025-07-06 01:50:10.753] [debug] Wrote virtual memory chunk: virt=0x105a0, size=0x10
[2025-07-06 01:50:10.753] [debug] Writing virtual memory chunk: virt=0x115b0, size=0x1, process=0
[2025-07-06 01:50:10.753] [info] Translating virtual to physical: virt=0x115b0, process=0
[2025-07-06 01:50:10.753] [debug] Marked page dirty: virt=0x115b0
[2025-07-06 01:50:10.753] [debug] Wrote virtual memory chunk: virt=0x115b0, size=0x1
[2025-07-06 01:50:10.754] [debug] Writing virtual memory chunk: virt=0x105b0, size=0x10, process=0
[2025-07-06 01:50:10.754] [info] Translating virtual to physical: virt=0x105b0, process=0
[2025-07-06 01:50:10.754] [debug] Marked page dirty: virt=0x105b0
[2025-07-06 01:50:10.754] [debug] Wrote virtual memory chunk: virt=0x105b0, size=0x10
[2025-07-06 01:50:10.754] [debug] Writing virtual memory chunk: virt=0x115c0, size=0x1, process=0
[2025-07-06 01:50:10.754] [info] Translating virtual to physical: virt=0x115c0, process=0
[2025-07-06 01:50:10.754] [debug] Marked page dirty: virt=0x115c0
[2025-07-06 01:50:10.754] [debug] Wrote virtual memory chunk: virt=0x115c0, size=0x1
[2025-07-06 01:50:10.754] [debug] Writing virtual memory chunk: virt=0x105c0, size=0x10, process=0
[2025-07-06 01:50:10.754] [info] Translating virtual to physical: virt=0x105c0, process=0
[2025-07-06 01:50:10.754] [debug] Marked page dirty: virt=0x105c0
[2025-07-06 01:50:10.754] [debug] Wrote virtual memory chunk: virt=0x105c0, size=0x10
[2025-07-06 01:50:10.754] [debug] Writing virtual memory chunk: virt=0x115d0, size=0x1, process=0
[2025-07-06 01:50:10.754] [info] Translating virtual to physical: virt=0x115d0, process=0
[2025-07-06 01:50:10.754] [debug] Marked page dirty: virt=0x115d0
[2025-07-06 01:50:10.754] [debug] Wrote virtual memory chunk: virt=0x115d0, size=0x1
[2025-07-06 01:50:10.754] [debug] Writing virtual memory chunk: virt=0x105d0, size=0x10, process=0
[2025-07-06 01:50:10.755] [info] Translating virtual to physical: virt=0x105d0, process=0
[2025-07-06 01:50:10.755] [debug] Marked page dirty: virt=0x105d0
[2025-07-06 01:50:10.755] [debug] Wrote virtual memory chunk: virt=0x105d0, size=0x10
[2025-07-06 01:50:10.755] [debug] Writing virtual memory chunk: virt=0x115e0, size=0x1, process=0
[2025-07-06 01:50:10.755] [info] Translating virtual to physical: virt=0x115e0, process=0
[2025-07-06 01:50:10.755] [debug] Marked page dirty: virt=0x115e0
[2025-07-06 01:50:10.755] [debug] Wrote virtual memory chunk: virt=0x115e0, size=0x1
[2025-07-06 01:50:10.755] [debug] Writing virtual memory chunk: virt=0x105e0, size=0x10, process=0
[2025-07-06 01:50:10.755] [info] Translating virtual to physical: virt=0x105e0, process=0
[2025-07-06 01:50:10.755] [debug] Marked page dirty: virt=0x105e0
[2025-07-06 01:50:10.755] [debug] Wrote virtual memory chunk: virt=0x105e0, size=0x10
[2025-07-06 01:50:10.755] [debug] Writing virtual memory chunk: virt=0x115f0, size=0x1, process=0
[2025-07-06 01:50:10.755] [info] Translating virtual to physical: virt=0x115f0, process=0
[2025-07-06 01:50:10.755] [debug] Marked page dirty: virt=0x115f0
[2025-07-06 01:50:10.755] [debug] Wrote virtual memory chunk: virt=0x115f0, size=0x1
[2025-07-06 01:50:10.755] [debug] Writing virtual memory chunk: virt=0x105f0, size=0x10, process=0
[2025-07-06 01:50:10.756] [info] Translating virtual to physical: virt=0x105f0, process=0
[2025-07-06 01:50:10.756] [debug] Marked page dirty: virt=0x105f0
[2025-07-06 01:50:10.756] [debug] Wrote virtual memory chunk: virt=0x105f0, size=0x10
[2025-07-06 01:50:10.756] [debug] Writing virtual memory chunk: virt=0x11600, size=0x1, process=0
[2025-07-06 01:50:10.756] [info] Translating virtual to physical: virt=0x11600, process=0
[2025-07-06 01:50:10.756] [debug] Marked page dirty: virt=0x11600
[2025-07-06 01:50:10.756] [debug] Wrote virtual memory chunk: virt=0x11600, size=0x1
[2025-07-06 01:50:10.756] [debug] Writing virtual memory chunk: virt=0x10600, size=0x10, process=0
[2025-07-06 01:50:10.756] [info] Translating virtual to physical: virt=0x10600, process=0
[2025-07-06 01:50:10.756] [debug] Marked page dirty: virt=0x10600
[2025-07-06 01:50:10.756] [debug] Wrote virtual memory chunk: virt=0x10600, size=0x10
[2025-07-06 01:50:10.756] [debug] Writing virtual memory chunk: virt=0x11610, size=0x1, process=0
[2025-07-06 01:50:10.756] [info] Translating virtual to physical: virt=0x11610, process=0
[2025-07-06 01:50:10.756] [debug] Marked page dirty: virt=0x11610
[2025-07-06 01:50:10.756] [debug] Wrote virtual memory chunk: virt=0x11610, size=0x1
[2025-07-06 01:50:10.756] [debug] Writing virtual memory chunk: virt=0x10610, size=0x10, process=0
[2025-07-06 01:50:10.757] [info] Translating virtual to physical: virt=0x10610, process=0
[2025-07-06 01:50:10.757] [debug] Marked page dirty: virt=0x10610
[2025-07-06 01:50:10.757] [debug] Wrote virtual memory chunk: virt=0x10610, size=0x10
[2025-07-06 01:50:10.757] [debug] Writing virtual memory chunk: virt=0x11620, size=0x1, process=0
[2025-07-06 01:50:10.757] [info] Translating virtual to physical: virt=0x11620, process=0
[2025-07-06 01:50:10.757] [debug] Marked page dirty: virt=0x11620
[2025-07-06 01:50:10.757] [debug] Wrote virtual memory chunk: virt=0x11620, size=0x1
[2025-07-06 01:50:10.757] [debug] Writing virtual memory chunk: virt=0x10620, size=0x10, process=0
[2025-07-06 01:50:10.757] [info] Translating virtual to physical: virt=0x10620, process=0
[2025-07-06 01:50:10.757] [debug] Marked page dirty: virt=0x10620
[2025-07-06 01:50:10.757] [debug] Wrote virtual memory chunk: virt=0x10620, size=0x10
[2025-07-06 01:50:10.757] [debug] Writing virtual memory chunk: virt=0x11630, size=0x1, process=0
[2025-07-06 01:50:10.757] [info] Translating virtual to physical: virt=0x11630, process=0
[2025-07-06 01:50:10.757] [debug] Marked page dirty: virt=0x11630
[2025-07-06 01:50:10.757] [debug] Wrote virtual memory chunk: virt=0x11630, size=0x1
[2025-07-06 01:50:10.757] [debug] Writing virtual memory chunk: virt=0x10630, size=0x10, process=0
[2025-07-06 01:50:10.757] [info] Translating virtual to physical: virt=0x10630, process=0
[2025-07-06 01:50:10.758] [debug] Marked page dirty: virt=0x10630
[2025-07-06 01:50:10.758] [debug] Wrote virtual memory chunk: virt=0x10630, size=0x10
[2025-07-06 01:50:10.758] [debug] Writing virtual memory chunk: virt=0x11640, size=0x1, process=0
[2025-07-06 01:50:10.758] [info] Translating virtual to physical: virt=0x11640, process=0
[2025-07-06 01:50:10.758] [debug] Marked page dirty: virt=0x11640
[2025-07-06 01:50:10.758] [debug] Wrote virtual memory chunk: virt=0x11640, size=0x1
[2025-07-06 01:50:10.758] [debug] Writing virtual memory chunk: virt=0x10640, size=0x10, process=0
[2025-07-06 01:50:10.758] [info] Translating virtual to physical: virt=0x10640, process=0
[2025-07-06 01:50:10.758] [debug] Marked page dirty: virt=0x10640
[2025-07-06 01:50:10.758] [debug] Wrote virtual memory chunk: virt=0x10640, size=0x10
[2025-07-06 01:50:10.758] [info] Processed descriptor 100
[2025-07-06 01:50:10.758] [debug] Writing virtual memory chunk: virt=0x11650, size=0x1, process=0
[2025-07-06 01:50:10.758] [info] Translating virtual to physical: virt=0x11650, process=0
[2025-07-06 01:50:10.758] [debug] Marked page dirty: virt=0x11650
[2025-07-06 01:50:10.758] [debug] Wrote virtual memory chunk: virt=0x11650, size=0x1
[2025-07-06 01:50:10.758] [debug] Writing virtual memory chunk: virt=0x10650, size=0x10, process=0
[2025-07-06 01:50:10.758] [info] Translating virtual to physical: virt=0x10650, process=0
[2025-07-06 01:50:10.759] [debug] Marked page dirty: virt=0x10650
[2025-07-06 01:50:10.759] [debug] Wrote virtual memory chunk: virt=0x10650, size=0x10
[2025-07-06 01:50:10.759] [debug] Writing virtual memory chunk: virt=0x11660, size=0x1, process=0
[2025-07-06 01:50:10.759] [info] Translating virtual to physical: virt=0x11660, process=0
[2025-07-06 01:50:10.759] [debug] Marked page dirty: virt=0x11660
[2025-07-06 01:50:10.759] [debug] Wrote virtual memory chunk: virt=0x11660, size=0x1
[2025-07-06 01:50:10.759] [debug] Writing virtual memory chunk: virt=0x10660, size=0x10, process=0
[2025-07-06 01:50:10.759] [info] Translating virtual to physical: virt=0x10660, process=0
[2025-07-06 01:50:10.759] [debug] Marked page dirty: virt=0x10660
[2025-07-06 01:50:10.759] [debug] Wrote virtual memory chunk: virt=0x10660, size=0x10
[2025-07-06 01:50:10.759] [debug] Writing virtual memory chunk: virt=0x11670, size=0x1, process=0
[2025-07-06 01:50:10.759] [info] Translating virtual to physical: virt=0x11670, process=0
[2025-07-06 01:50:10.759] [debug] Marked page dirty: virt=0x11670
[2025-07-06 01:50:10.759] [debug] Wrote virtual memory chunk: virt=0x11670, size=0x1
[2025-07-06 01:50:10.759] [debug] Writing virtual memory chunk: virt=0x10670, size=0x10, process=0
[2025-07-06 01:50:10.759] [info] Translating virtual to physical: virt=0x10670, process=0
[2025-07-06 01:50:10.760] [debug] Marked page dirty: virt=0x10670
[2025-07-06 01:50:10.760] [debug] Wrote virtual memory chunk: virt=0x10670, size=0x10
[2025-07-06 01:50:10.760] [debug] Writing virtual memory chunk: virt=0x11680, size=0x1, process=0
[2025-07-06 01:50:10.760] [info] Translating virtual to physical: virt=0x11680, process=0
[2025-07-06 01:50:10.760] [debug] Marked page dirty: virt=0x11680
[2025-07-06 01:50:10.760] [debug] Wrote virtual memory chunk: virt=0x11680, size=0x1
[2025-07-06 01:50:10.760] [debug] Writing virtual memory chunk: virt=0x10680, size=0x10, process=0
[2025-07-06 01:50:10.760] [info] Translating virtual to physical: virt=0x10680, process=0
[2025-07-06 01:50:10.760] [debug] Marked page dirty: virt=0x10680
[2025-07-06 01:50:10.760] [debug] Wrote virtual memory chunk: virt=0x10680, size=0x10
[2025-07-06 01:50:10.760] [debug] Writing virtual memory chunk: virt=0x11690, size=0x1, process=0
[2025-07-06 01:50:10.760] [info] Translating virtual to physical: virt=0x11690, process=0
[2025-07-06 01:50:10.760] [debug] Marked page dirty: virt=0x11690
[2025-07-06 01:50:10.760] [debug] Wrote virtual memory chunk: virt=0x11690, size=0x1
[2025-07-06 01:50:10.760] [debug] Writing virtual memory chunk: virt=0x10690, size=0x10, process=0
[2025-07-06 01:50:10.760] [info] Translating virtual to physical: virt=0x10690, process=0
[2025-07-06 01:50:10.761] [debug] Marked page dirty: virt=0x10690
[2025-07-06 01:50:10.761] [debug] Wrote virtual memory chunk: virt=0x10690, size=0x10
[2025-07-06 01:50:10.761] [debug] Writing virtual memory chunk: virt=0x116a0, size=0x1, process=0
[2025-07-06 01:50:10.761] [info] Translating virtual to physical: virt=0x116a0, process=0
[2025-07-06 01:50:10.761] [debug] Marked page dirty: virt=0x116a0
[2025-07-06 01:50:10.761] [debug] Wrote virtual memory chunk: virt=0x116a0, size=0x1
[2025-07-06 01:50:10.761] [debug] Writing virtual memory chunk: virt=0x106a0, size=0x10, process=0
[2025-07-06 01:50:10.761] [info] Translating virtual to physical: virt=0x106a0, process=0
[2025-07-06 01:50:10.761] [debug] Marked page dirty: virt=0x106a0
[2025-07-06 01:50:10.761] [debug] Wrote virtual memory chunk: virt=0x106a0, size=0x10
[2025-07-06 01:50:10.761] [debug] Writing virtual memory chunk: virt=0x116b0, size=0x1, process=0
[2025-07-06 01:50:10.761] [info] Translating virtual to physical: virt=0x116b0, process=0
[2025-07-06 01:50:10.761] [debug] Marked page dirty: virt=0x116b0
[2025-07-06 01:50:10.761] [debug] Wrote virtual memory chunk: virt=0x116b0, size=0x1
[2025-07-06 01:50:10.761] [debug] Writing virtual memory chunk: virt=0x106b0, size=0x10, process=0
[2025-07-06 01:50:10.762] [info] Translating virtual to physical: virt=0x106b0, process=0
[2025-07-06 01:50:10.762] [debug] Marked page dirty: virt=0x106b0
[2025-07-06 01:50:10.762] [debug] Wrote virtual memory chunk: virt=0x106b0, size=0x10
[2025-07-06 01:50:10.762] [debug] Writing virtual memory chunk: virt=0x116c0, size=0x1, process=0
[2025-07-06 01:50:10.762] [info] Translating virtual to physical: virt=0x116c0, process=0
[2025-07-06 01:50:10.762] [debug] Marked page dirty: virt=0x116c0
[2025-07-06 01:50:10.762] [debug] Wrote virtual memory chunk: virt=0x116c0, size=0x1
[2025-07-06 01:50:10.762] [debug] Writing virtual memory chunk: virt=0x106c0, size=0x10, process=0
[2025-07-06 01:50:10.762] [info] Translating virtual to physical: virt=0x106c0, process=0
[2025-07-06 01:50:10.762] [debug] Marked page dirty: virt=0x106c0
[2025-07-06 01:50:10.762] [debug] Wrote virtual memory chunk: virt=0x106c0, size=0x10
[2025-07-06 01:50:10.762] [debug] Writing virtual memory chunk: virt=0x116d0, size=0x1, process=0
[2025-07-06 01:50:10.762] [info] Translating virtual to physical: virt=0x116d0, process=0
[2025-07-06 01:50:10.762] [debug] Marked page dirty: virt=0x116d0
[2025-07-06 01:50:10.762] [debug] Wrote virtual memory chunk: virt=0x116d0, size=0x1
[2025-07-06 01:50:10.762] [debug] Writing virtual memory chunk: virt=0x106d0, size=0x10, process=0
[2025-07-06 01:50:10.762] [info] Translating virtual to physical: virt=0x106d0, process=0
[2025-07-06 01:50:10.763] [debug] Marked page dirty: virt=0x106d0
[2025-07-06 01:50:10.763] [debug] Wrote virtual memory chunk: virt=0x106d0, size=0x10
[2025-07-06 01:50:10.764] [debug] Writing virtual memory chunk: virt=0x116e0, size=0x1, process=0
[2025-07-06 01:50:10.764] [info] Translating virtual to physical: virt=0x116e0, process=0
[2025-07-06 01:50:10.765] [debug] Marked page dirty: virt=0x116e0
[2025-07-06 01:50:10.765] [debug] Wrote virtual memory chunk: virt=0x116e0, size=0x1
[2025-07-06 01:50:10.765] [debug] Writing virtual memory chunk: virt=0x106e0, size=0x10, process=0
[2025-07-06 01:50:10.765] [info] Translating virtual to physical: virt=0x106e0, process=0
[2025-07-06 01:50:10.765] [debug] Marked page dirty: virt=0x106e0
[2025-07-06 01:50:10.765] [debug] Wrote virtual memory chunk: virt=0x106e0, size=0x10
[2025-07-06 01:50:10.765] [debug] Writing virtual memory chunk: virt=0x116f0, size=0x1, process=0
[2025-07-06 01:50:10.765] [info] Translating virtual to physical: virt=0x116f0, process=0
[2025-07-06 01:50:10.765] [debug] Marked page dirty: virt=0x116f0
[2025-07-06 01:50:10.765] [debug] Wrote virtual memory chunk: virt=0x116f0, size=0x1
[2025-07-06 01:50:10.765] [debug] Writing virtual memory chunk: virt=0x106f0, size=0x10, process=0
[2025-07-06 01:50:10.765] [info] Translating virtual to physical: virt=0x106f0, process=0
[2025-07-06 01:50:10.765] [debug] Marked page dirty: virt=0x106f0
[2025-07-06 01:50:10.765] [debug] Wrote virtual memory chunk: virt=0x106f0, size=0x10
[2025-07-06 01:50:10.765] [debug] Writing virtual memory chunk: virt=0x11700, size=0x1, process=0
[2025-07-06 01:50:10.765] [info] Translating virtual to physical: virt=0x11700, process=0
[2025-07-06 01:50:10.766] [debug] Marked page dirty: virt=0x11700
[2025-07-06 01:50:10.766] [debug] Wrote virtual memory chunk: virt=0x11700, size=0x1
[2025-07-06 01:50:10.766] [debug] Writing virtual memory chunk: virt=0x10700, size=0x10, process=0
[2025-07-06 01:50:10.766] [info] Translating virtual to physical: virt=0x10700, process=0
[2025-07-06 01:50:10.766] [debug] Marked page dirty: virt=0x10700
[2025-07-06 01:50:10.766] [debug] Wrote virtual memory chunk: virt=0x10700, size=0x10
[2025-07-06 01:50:10.766] [debug] Writing virtual memory chunk: virt=0x11710, size=0x1, process=0
[2025-07-06 01:50:10.766] [info] Translating virtual to physical: virt=0x11710, process=0
[2025-07-06 01:50:10.766] [debug] Marked page dirty: virt=0x11710
[2025-07-06 01:50:10.766] [debug] Wrote virtual memory chunk: virt=0x11710, size=0x1
[2025-07-06 01:50:10.766] [debug] Writing virtual memory chunk: virt=0x10710, size=0x10, process=0
[2025-07-06 01:50:10.766] [info] Translating virtual to physical: virt=0x10710, process=0
[2025-07-06 01:50:10.766] [debug] Marked page dirty: virt=0x10710
[2025-07-06 01:50:10.767] [debug] Wrote virtual memory chunk: virt=0x10710, size=0x10
[2025-07-06 01:50:10.767] [debug] Writing virtual memory chunk: virt=0x11720, size=0x1, process=0
[2025-07-06 01:50:10.767] [info] Translating virtual to physical: virt=0x11720, process=0
[2025-07-06 01:50:10.767] [debug] Marked page dirty: virt=0x11720
[2025-07-06 01:50:10.767] [debug] Wrote virtual memory chunk: virt=0x11720, size=0x1
[2025-07-06 01:50:10.767] [debug] Writing virtual memory chunk: virt=0x10720, size=0x10, process=0
[2025-07-06 01:50:10.767] [info] Translating virtual to physical: virt=0x10720, process=0
[2025-07-06 01:50:10.767] [debug] Marked page dirty: virt=0x10720
[2025-07-06 01:50:10.767] [debug] Wrote virtual memory chunk: virt=0x10720, size=0x10
[2025-07-06 01:50:10.767] [debug] Writing virtual memory chunk: virt=0x11730, size=0x1, process=0
[2025-07-06 01:50:10.767] [info] Translating virtual to physical: virt=0x11730, process=0
[2025-07-06 01:50:10.767] [debug] Marked page dirty: virt=0x11730
[2025-07-06 01:50:10.767] [debug] Wrote virtual memory chunk: virt=0x11730, size=0x1
[2025-07-06 01:50:10.768] [debug] Writing virtual memory chunk: virt=0x10730, size=0x10, process=0
[2025-07-06 01:50:10.768] [info] Translating virtual to physical: virt=0x10730, process=0
[2025-07-06 01:50:10.768] [debug] Marked page dirty: virt=0x10730
[2025-07-06 01:50:10.768] [debug] Wrote virtual memory chunk: virt=0x10730, size=0x10
[2025-07-06 01:50:10.768] [debug] Writing virtual memory chunk: virt=0x11740, size=0x1, process=0
[2025-07-06 01:50:10.768] [info] Translating virtual to physical: virt=0x11740, process=0
[2025-07-06 01:50:10.768] [debug] Marked page dirty: virt=0x11740
[2025-07-06 01:50:10.768] [debug] Wrote virtual memory chunk: virt=0x11740, size=0x1
[2025-07-06 01:50:10.768] [debug] Writing virtual memory chunk: virt=0x10740, size=0x10, process=0
[2025-07-06 01:50:10.768] [info] Translating virtual to physical: virt=0x10740, process=0
[2025-07-06 01:50:10.768] [debug] Marked page dirty: virt=0x10740
[2025-07-06 01:50:10.768] [debug] Wrote virtual memory chunk: virt=0x10740, size=0x10
[2025-07-06 01:50:10.768] [debug] Writing virtual memory chunk: virt=0x11750, size=0x1, process=0
[2025-07-06 01:50:10.768] [info] Translating virtual to physical: virt=0x11750, process=0
[2025-07-06 01:50:10.769] [debug] Marked page dirty: virt=0x11750
[2025-07-06 01:50:10.769] [debug] Wrote virtual memory chunk: virt=0x11750, size=0x1
[2025-07-06 01:50:10.769] [debug] Writing virtual memory chunk: virt=0x10750, size=0x10, process=0
[2025-07-06 01:50:10.769] [info] Translating virtual to physical: virt=0x10750, process=0
[2025-07-06 01:50:10.769] [debug] Marked page dirty: virt=0x10750
[2025-07-06 01:50:10.769] [debug] Wrote virtual memory chunk: virt=0x10750, size=0x10
[2025-07-06 01:50:10.769] [debug] Writing virtual memory chunk: virt=0x11760, size=0x1, process=0
[2025-07-06 01:50:10.769] [info] Translating virtual to physical: virt=0x11760, process=0
[2025-07-06 01:50:10.769] [debug] Marked page dirty: virt=0x11760
[2025-07-06 01:50:10.769] [debug] Wrote virtual memory chunk: virt=0x11760, size=0x1
[2025-07-06 01:50:10.769] [debug] Writing virtual memory chunk: virt=0x10760, size=0x10, process=0
[2025-07-06 01:50:10.769] [info] Translating virtual to physical: virt=0x10760, process=0
[2025-07-06 01:50:10.769] [debug] Marked page dirty: virt=0x10760
[2025-07-06 01:50:10.769] [debug] Wrote virtual memory chunk: virt=0x10760, size=0x10
[2025-07-06 01:50:10.769] [debug] Writing virtual memory chunk: virt=0x11770, size=0x1, process=0
[2025-07-06 01:50:10.769] [info] Translating virtual to physical: virt=0x11770, process=0
[2025-07-06 01:50:10.769] [debug] Marked page dirty: virt=0x11770
[2025-07-06 01:50:10.769] [debug] Wrote virtual memory chunk: virt=0x11770, size=0x1
[2025-07-06 01:50:10.769] [debug] Writing virtual memory chunk: virt=0x10770, size=0x10, process=0
[2025-07-06 01:50:10.770] [info] Translating virtual to physical: virt=0x10770, process=0
[2025-07-06 01:50:10.770] [debug] Marked page dirty: virt=0x10770
[2025-07-06 01:50:10.770] [debug] Wrote virtual memory chunk: virt=0x10770, size=0x10
[2025-07-06 01:50:10.770] [debug] Writing virtual memory chunk: virt=0x11780, size=0x1, process=0
[2025-07-06 01:50:10.770] [info] Translating virtual to physical: virt=0x11780, process=0
[2025-07-06 01:50:10.770] [debug] Marked page dirty: virt=0x11780
[2025-07-06 01:50:10.770] [debug] Wrote virtual memory chunk: virt=0x11780, size=0x1
[2025-07-06 01:50:10.770] [debug] Writing virtual memory chunk: virt=0x10780, size=0x10, process=0
[2025-07-06 01:50:10.770] [info] Translating virtual to physical: virt=0x10780, process=0
[2025-07-06 01:50:10.770] [debug] Marked page dirty: virt=0x10780
[2025-07-06 01:50:10.770] [debug] Wrote virtual memory chunk: virt=0x10780, size=0x10
[2025-07-06 01:50:10.770] [debug] Writing virtual memory chunk: virt=0x11790, size=0x1, process=0
[2025-07-06 01:50:10.770] [info] Translating virtual to physical: virt=0x11790, process=0
[2025-07-06 01:50:10.770] [debug] Marked page dirty: virt=0x11790
[2025-07-06 01:50:10.770] [debug] Wrote virtual memory chunk: virt=0x11790, size=0x1
[2025-07-06 01:50:10.770] [debug] Writing virtual memory chunk: virt=0x10790, size=0x10, process=0
[2025-07-06 01:50:10.770] [info] Translating virtual to physical: virt=0x10790, process=0
[2025-07-06 01:50:10.771] [debug] Marked page dirty: virt=0x10790
[2025-07-06 01:50:10.771] [debug] Wrote virtual memory chunk: virt=0x10790, size=0x10
[2025-07-06 01:50:10.771] [debug] Writing virtual memory chunk: virt=0x117a0, size=0x1, process=0
[2025-07-06 01:50:10.771] [info] Translating virtual to physical: virt=0x117a0, process=0
[2025-07-06 01:50:10.771] [debug] Marked page dirty: virt=0x117a0
[2025-07-06 01:50:10.771] [debug] Wrote virtual memory chunk: virt=0x117a0, size=0x1
[2025-07-06 01:50:10.771] [debug] Writing virtual memory chunk: virt=0x107a0, size=0x10, process=0
[2025-07-06 01:50:10.771] [info] Translating virtual to physical: virt=0x107a0, process=0
[2025-07-06 01:50:10.771] [debug] Marked page dirty: virt=0x107a0
[2025-07-06 01:50:10.771] [debug] Wrote virtual memory chunk: virt=0x107a0, size=0x10
[2025-07-06 01:50:10.771] [debug] Writing virtual memory chunk: virt=0x117b0, size=0x1, process=0
[2025-07-06 01:50:10.771] [info] Translating virtual to physical: virt=0x117b0, process=0
[2025-07-06 01:50:10.771] [debug] Marked page dirty: virt=0x117b0
[2025-07-06 01:50:10.771] [debug] Wrote virtual memory chunk: virt=0x117b0, size=0x1
[2025-07-06 01:50:10.771] [debug] Writing virtual memory chunk: virt=0x107b0, size=0x10, process=0
[2025-07-06 01:50:10.771] [info] Translating virtual to physical: virt=0x107b0, process=0
[2025-07-06 01:50:10.772] [debug] Marked page dirty: virt=0x107b0
[2025-07-06 01:50:10.772] [debug] Wrote virtual memory chunk: virt=0x107b0, size=0x10
[2025-07-06 01:50:10.772] [debug] Writing virtual memory chunk: virt=0x117c0, size=0x1, process=0
[2025-07-06 01:50:10.772] [info] Translating virtual to physical: virt=0x117c0, process=0
[2025-07-06 01:50:10.772] [debug] Marked page dirty: virt=0x117c0
[2025-07-06 01:50:10.772] [debug] Wrote virtual memory chunk: virt=0x117c0, size=0x1
[2025-07-06 01:50:10.772] [debug] Writing virtual memory chunk: virt=0x107c0, size=0x10, process=0
[2025-07-06 01:50:10.772] [info] Translating virtual to physical: virt=0x107c0, process=0
[2025-07-06 01:50:10.772] [debug] Marked page dirty: virt=0x107c0
[2025-07-06 01:50:10.772] [debug] Wrote virtual memory chunk: virt=0x107c0, size=0x10
[2025-07-06 01:50:10.772] [debug] Writing virtual memory chunk: virt=0x117d0, size=0x1, process=0
[2025-07-06 01:50:10.772] [info] Translating virtual to physical: virt=0x117d0, process=0
[2025-07-06 01:50:10.772] [debug] Marked page dirty: virt=0x117d0
[2025-07-06 01:50:10.772] [debug] Wrote virtual memory chunk: virt=0x117d0, size=0x1
[2025-07-06 01:50:10.772] [debug] Writing virtual memory chunk: virt=0x107d0, size=0x10, process=0
[2025-07-06 01:50:10.773] [info] Translating virtual to physical: virt=0x107d0, process=0
[2025-07-06 01:50:10.773] [debug] Marked page dirty: virt=0x107d0
[2025-07-06 01:50:10.773] [debug] Wrote virtual memory chunk: virt=0x107d0, size=0x10
[2025-07-06 01:50:10.773] [debug] Writing virtual memory chunk: virt=0x117e0, size=0x1, process=0
[2025-07-06 01:50:10.773] [info] Translating virtual to physical: virt=0x117e0, process=0
[2025-07-06 01:50:10.773] [debug] Marked page dirty: virt=0x117e0
[2025-07-06 01:50:10.773] [debug] Wrote virtual memory chunk: virt=0x117e0, size=0x1
[2025-07-06 01:50:10.773] [debug] Writing virtual memory chunk: virt=0x107e0, size=0x10, process=0
[2025-07-06 01:50:10.773] [info] Translating virtual to physical: virt=0x107e0, process=0
[2025-07-06 01:50:10.773] [debug] Marked page dirty: virt=0x107e0
[2025-07-06 01:50:10.773] [debug] Wrote virtual memory chunk: virt=0x107e0, size=0x10
[2025-07-06 01:50:10.773] [debug] Writing virtual memory chunk: virt=0x117f0, size=0x1, process=0
[2025-07-06 01:50:10.773] [info] Translating virtual to physical: virt=0x117f0, process=0
[2025-07-06 01:50:10.773] [debug] Marked page dirty: virt=0x117f0
[2025-07-06 01:50:10.773] [debug] Wrote virtual memory chunk: virt=0x117f0, size=0x1
[2025-07-06 01:50:10.773] [debug] Writing virtual memory chunk: virt=0x107f0, size=0x10, process=0
[2025-07-06 01:50:10.774] [info] Translating virtual to physical: virt=0x107f0, process=0
[2025-07-06 01:50:10.774] [debug] Marked page dirty: virt=0x107f0
[2025-07-06 01:50:10.774] [debug] Wrote virtual memory chunk: virt=0x107f0, size=0x10
[2025-07-06 01:50:10.774] [debug] Writing virtual memory chunk: virt=0x11800, size=0x1, process=0
[2025-07-06 01:50:10.774] [info] Translating virtual to physical: virt=0x11800, process=0
[2025-07-06 01:50:10.774] [debug] Marked page dirty: virt=0x11800
[2025-07-06 01:50:10.774] [debug] Wrote virtual memory chunk: virt=0x11800, size=0x1
[2025-07-06 01:50:10.774] [debug] Writing virtual memory chunk: virt=0x10800, size=0x10, process=0
[2025-07-06 01:50:10.774] [info] Translating virtual to physical: virt=0x10800, process=0
[2025-07-06 01:50:10.774] [debug] Marked page dirty: virt=0x10800
[2025-07-06 01:50:10.774] [debug] Wrote virtual memory chunk: virt=0x10800, size=0x10
[2025-07-06 01:50:10.774] [debug] Writing virtual memory chunk: virt=0x11810, size=0x1, process=0
[2025-07-06 01:50:10.774] [info] Translating virtual to physical: virt=0x11810, process=0
[2025-07-06 01:50:10.774] [debug] Marked page dirty: virt=0x11810
[2025-07-06 01:50:10.774] [debug] Wrote virtual memory chunk: virt=0x11810, size=0x1
[2025-07-06 01:50:10.774] [debug] Writing virtual memory chunk: virt=0x10810, size=0x10, process=0
[2025-07-06 01:50:10.774] [info] Translating virtual to physical: virt=0x10810, process=0
[2025-07-06 01:50:10.775] [debug] Marked page dirty: virt=0x10810
[2025-07-06 01:50:10.775] [debug] Wrote virtual memory chunk: virt=0x10810, size=0x10
[2025-07-06 01:50:10.775] [debug] Writing virtual memory chunk: virt=0x11820, size=0x1, process=0
[2025-07-06 01:50:10.775] [info] Translating virtual to physical: virt=0x11820, process=0
[2025-07-06 01:50:10.775] [debug] Marked page dirty: virt=0x11820
[2025-07-06 01:50:10.775] [debug] Wrote virtual memory chunk: virt=0x11820, size=0x1
[2025-07-06 01:50:10.775] [debug] Writing virtual memory chunk: virt=0x10820, size=0x10, process=0
[2025-07-06 01:50:10.775] [info] Translating virtual to physical: virt=0x10820, process=0
[2025-07-06 01:50:10.775] [debug] Marked page dirty: virt=0x10820
[2025-07-06 01:50:10.775] [debug] Wrote virtual memory chunk: virt=0x10820, size=0x10
[2025-07-06 01:50:10.775] [debug] Writing virtual memory chunk: virt=0x11830, size=0x1, process=0
[2025-07-06 01:50:10.775] [info] Translating virtual to physical: virt=0x11830, process=0
[2025-07-06 01:50:10.775] [debug] Marked page dirty: virt=0x11830
[2025-07-06 01:50:10.775] [debug] Wrote virtual memory chunk: virt=0x11830, size=0x1
[2025-07-06 01:50:10.775] [debug] Writing virtual memory chunk: virt=0x10830, size=0x10, process=0
[2025-07-06 01:50:10.775] [info] Translating virtual to physical: virt=0x10830, process=0
[2025-07-06 01:50:10.775] [debug] Marked page dirty: virt=0x10830
[2025-07-06 01:50:10.776] [debug] Wrote virtual memory chunk: virt=0x10830, size=0x10
[2025-07-06 01:50:10.776] [debug] Writing virtual memory chunk: virt=0x11840, size=0x1, process=0
[2025-07-06 01:50:10.776] [info] Translating virtual to physical: virt=0x11840, process=0
[2025-07-06 01:50:10.776] [debug] Marked page dirty: virt=0x11840
[2025-07-06 01:50:10.776] [debug] Wrote virtual memory chunk: virt=0x11840, size=0x1
[2025-07-06 01:50:10.776] [debug] Writing virtual memory chunk: virt=0x10840, size=0x10, process=0
[2025-07-06 01:50:10.776] [info] Translating virtual to physical: virt=0x10840, process=0
[2025-07-06 01:50:10.776] [debug] Marked page dirty: virt=0x10840
[2025-07-06 01:50:10.776] [debug] Wrote virtual memory chunk: virt=0x10840, size=0x10
[2025-07-06 01:50:10.776] [debug] Writing virtual memory chunk: virt=0x11850, size=0x1, process=0
[2025-07-06 01:50:10.776] [info] Translating virtual to physical: virt=0x11850, process=0
[2025-07-06 01:50:10.776] [debug] Marked page dirty: virt=0x11850
[2025-07-06 01:50:10.776] [debug] Wrote virtual memory chunk: virt=0x11850, size=0x1
[2025-07-06 01:50:10.776] [debug] Writing virtual memory chunk: virt=0x10850, size=0x10, process=0
[2025-07-06 01:50:10.776] [info] Translating virtual to physical: virt=0x10850, process=0
[2025-07-06 01:50:10.776] [debug] Marked page dirty: virt=0x10850
[2025-07-06 01:50:10.776] [debug] Wrote virtual memory chunk: virt=0x10850, size=0x10
[2025-07-06 01:50:10.776] [debug] Writing virtual memory chunk: virt=0x11860, size=0x1, process=0
[2025-07-06 01:50:10.776] [info] Translating virtual to physical: virt=0x11860, process=0
[2025-07-06 01:50:10.777] [debug] Marked page dirty: virt=0x11860
[2025-07-06 01:50:10.777] [debug] Wrote virtual memory chunk: virt=0x11860, size=0x1
[2025-07-06 01:50:10.777] [debug] Writing virtual memory chunk: virt=0x10860, size=0x10, process=0
[2025-07-06 01:50:10.777] [info] Translating virtual to physical: virt=0x10860, process=0
[2025-07-06 01:50:10.777] [debug] Marked page dirty: virt=0x10860
[2025-07-06 01:50:10.777] [debug] Wrote virtual memory chunk: virt=0x10860, size=0x10
[2025-07-06 01:50:10.777] [debug] Writing virtual memory chunk: virt=0x11870, size=0x1, process=0
[2025-07-06 01:50:10.777] [info] Translating virtual to physical: virt=0x11870, process=0
[2025-07-06 01:50:10.777] [debug] Marked page dirty: virt=0x11870
[2025-07-06 01:50:10.777] [debug] Wrote virtual memory chunk: virt=0x11870, size=0x1
[2025-07-06 01:50:10.777] [debug] Writing virtual memory chunk: virt=0x10870, size=0x10, process=0
[2025-07-06 01:50:10.777] [info] Translating virtual to physical: virt=0x10870, process=0
[2025-07-06 01:50:10.777] [debug] Marked page dirty: virt=0x10870
[2025-07-06 01:50:10.777] [debug] Wrote virtual memory chunk: virt=0x10870, size=0x10
[2025-07-06 01:50:10.777] [debug] Writing virtual memory chunk: virt=0x11880, size=0x1, process=0
[2025-07-06 01:50:10.777] [info] Translating virtual to physical: virt=0x11880, process=0
[2025-07-06 01:50:10.778] [debug] Marked page dirty: virt=0x11880
[2025-07-06 01:50:10.778] [debug] Wrote virtual memory chunk: virt=0x11880, size=0x1
[2025-07-06 01:50:10.778] [debug] Writing virtual memory chunk: virt=0x10880, size=0x10, process=0
[2025-07-06 01:50:10.778] [info] Translating virtual to physical: virt=0x10880, process=0
[2025-07-06 01:50:10.778] [debug] Marked page dirty: virt=0x10880
[2025-07-06 01:50:10.778] [debug] Wrote virtual memory chunk: virt=0x10880, size=0x10
[2025-07-06 01:50:10.778] [debug] Writing virtual memory chunk: virt=0x11890, size=0x1, process=0
[2025-07-06 01:50:10.778] [info] Translating virtual to physical: virt=0x11890, process=0
[2025-07-06 01:50:10.778] [debug] Marked page dirty: virt=0x11890
[2025-07-06 01:50:10.778] [debug] Wrote virtual memory chunk: virt=0x11890, size=0x1
[2025-07-06 01:50:10.780] [debug] Writing virtual memory chunk: virt=0x10890, size=0x10, process=0
[2025-07-06 01:50:10.780] [info] Translating virtual to physical: virt=0x10890, process=0
[2025-07-06 01:50:10.780] [debug] Marked page dirty: virt=0x10890
[2025-07-06 01:50:10.780] [debug] Wrote virtual memory chunk: virt=0x10890, size=0x10
[2025-07-06 01:50:10.780] [debug] Writing virtual memory chunk: virt=0x118a0, size=0x1, process=0
[2025-07-06 01:50:10.780] [info] Translating virtual to physical: virt=0x118a0, process=0
[2025-07-06 01:50:10.780] [debug] Marked page dirty: virt=0x118a0
[2025-07-06 01:50:10.780] [debug] Wrote virtual memory chunk: virt=0x118a0, size=0x1
[2025-07-06 01:50:10.780] [debug] Writing virtual memory chunk: virt=0x108a0, size=0x10, process=0
[2025-07-06 01:50:10.780] [info] Translating virtual to physical: virt=0x108a0, process=0
[2025-07-06 01:50:10.780] [debug] Marked page dirty: virt=0x108a0
[2025-07-06 01:50:10.780] [debug] Wrote virtual memory chunk: virt=0x108a0, size=0x10
[2025-07-06 01:50:10.781] [debug] Writing virtual memory chunk: virt=0x118b0, size=0x1, process=0
[2025-07-06 01:50:10.781] [info] Translating virtual to physical: virt=0x118b0, process=0
[2025-07-06 01:50:10.781] [debug] Marked page dirty: virt=0x118b0
[2025-07-06 01:50:10.781] [debug] Wrote virtual memory chunk: virt=0x118b0, size=0x1
[2025-07-06 01:50:10.781] [debug] Writing virtual memory chunk: virt=0x108b0, size=0x10, process=0
[2025-07-06 01:50:10.781] [info] Translating virtual to physical: virt=0x108b0, process=0
[2025-07-06 01:50:10.781] [debug] Marked page dirty: virt=0x108b0
[2025-07-06 01:50:10.781] [debug] Wrote virtual memory chunk: virt=0x108b0, size=0x10
[2025-07-06 01:50:10.781] [debug] Writing virtual memory chunk: virt=0x118c0, size=0x1, process=0
[2025-07-06 01:50:10.781] [info] Translating virtual to physical: virt=0x118c0, process=0
[2025-07-06 01:50:10.781] [debug] Marked page dirty: virt=0x118c0
[2025-07-06 01:50:10.781] [debug] Wrote virtual memory chunk: virt=0x118c0, size=0x1
[2025-07-06 01:50:10.781] [debug] Writing virtual memory chunk: virt=0x108c0, size=0x10, process=0
[2025-07-06 01:50:10.781] [info] Translating virtual to physical: virt=0x108c0, process=0
[2025-07-06 01:50:10.781] [debug] Marked page dirty: virt=0x108c0
[2025-07-06 01:50:10.781] [debug] Wrote virtual memory chunk: virt=0x108c0, size=0x10
[2025-07-06 01:50:10.781] [debug] Writing virtual memory chunk: virt=0x118d0, size=0x1, process=0
[2025-07-06 01:50:10.781] [info] Translating virtual to physical: virt=0x118d0, process=0
[2025-07-06 01:50:10.782] [debug] Marked page dirty: virt=0x118d0
[2025-07-06 01:50:10.782] [debug] Wrote virtual memory chunk: virt=0x118d0, size=0x1
[2025-07-06 01:50:10.782] [debug] Writing virtual memory chunk: virt=0x108d0, size=0x10, process=0
[2025-07-06 01:50:10.782] [info] Translating virtual to physical: virt=0x108d0, process=0
[2025-07-06 01:50:10.782] [debug] Marked page dirty: virt=0x108d0
[2025-07-06 01:50:10.782] [debug] Wrote virtual memory chunk: virt=0x108d0, size=0x10
[2025-07-06 01:50:10.782] [debug] Writing virtual memory chunk: virt=0x118e0, size=0x1, process=0
[2025-07-06 01:50:10.782] [info] Translating virtual to physical: virt=0x118e0, process=0
[2025-07-06 01:50:10.782] [debug] Marked page dirty: virt=0x118e0
[2025-07-06 01:50:10.782] [debug] Wrote virtual memory chunk: virt=0x118e0, size=0x1
[2025-07-06 01:50:10.782] [debug] Writing virtual memory chunk: virt=0x108e0, size=0x10, process=0
[2025-07-06 01:50:10.782] [info] Translating virtual to physical: virt=0x108e0, process=0
[2025-07-06 01:50:10.782] [debug] Marked page dirty: virt=0x108e0
[2025-07-06 01:50:10.782] [debug] Wrote virtual memory chunk: virt=0x108e0, size=0x10
[2025-07-06 01:50:10.782] [debug] Writing virtual memory chunk: virt=0x118f0, size=0x1, process=0
[2025-07-06 01:50:10.782] [info] Translating virtual to physical: virt=0x118f0, process=0
[2025-07-06 01:50:10.783] [debug] Marked page dirty: virt=0x118f0
[2025-07-06 01:50:10.783] [debug] Wrote virtual memory chunk: virt=0x118f0, size=0x1
[2025-07-06 01:50:10.783] [debug] Writing virtual memory chunk: virt=0x108f0, size=0x10, process=0
[2025-07-06 01:50:10.783] [info] Translating virtual to physical: virt=0x108f0, process=0
[2025-07-06 01:50:10.783] [debug] Marked page dirty: virt=0x108f0
[2025-07-06 01:50:10.783] [debug] Wrote virtual memory chunk: virt=0x108f0, size=0x10
[2025-07-06 01:50:10.783] [debug] Writing virtual memory chunk: virt=0x11900, size=0x1, process=0
[2025-07-06 01:50:10.783] [info] Translating virtual to physical: virt=0x11900, process=0
[2025-07-06 01:50:10.783] [debug] Marked page dirty: virt=0x11900
[2025-07-06 01:50:10.783] [debug] Wrote virtual memory chunk: virt=0x11900, size=0x1
[2025-07-06 01:50:10.783] [debug] Writing virtual memory chunk: virt=0x10900, size=0x10, process=0
[2025-07-06 01:50:10.783] [info] Translating virtual to physical: virt=0x10900, process=0
[2025-07-06 01:50:10.783] [debug] Marked page dirty: virt=0x10900
[2025-07-06 01:50:10.783] [debug] Wrote virtual memory chunk: virt=0x10900, size=0x10
[2025-07-06 01:50:10.783] [debug] Writing virtual memory chunk: virt=0x11910, size=0x1, process=0
[2025-07-06 01:50:10.783] [info] Translating virtual to physical: virt=0x11910, process=0
[2025-07-06 01:50:10.784] [debug] Marked page dirty: virt=0x11910
[2025-07-06 01:50:10.784] [debug] Wrote virtual memory chunk: virt=0x11910, size=0x1
[2025-07-06 01:50:10.784] [debug] Writing virtual memory chunk: virt=0x10910, size=0x10, process=0
[2025-07-06 01:50:10.784] [info] Translating virtual to physical: virt=0x10910, process=0
[2025-07-06 01:50:10.784] [debug] Marked page dirty: virt=0x10910
[2025-07-06 01:50:10.784] [debug] Wrote virtual memory chunk: virt=0x10910, size=0x10
[2025-07-06 01:50:10.784] [debug] Writing virtual memory chunk: virt=0x11920, size=0x1, process=0
[2025-07-06 01:50:10.784] [info] Translating virtual to physical: virt=0x11920, process=0
[2025-07-06 01:50:10.784] [debug] Marked page dirty: virt=0x11920
[2025-07-06 01:50:10.784] [debug] Wrote virtual memory chunk: virt=0x11920, size=0x1
[2025-07-06 01:50:10.784] [debug] Writing virtual memory chunk: virt=0x10920, size=0x10, process=0
[2025-07-06 01:50:10.784] [info] Translating virtual to physical: virt=0x10920, process=0
[2025-07-06 01:50:10.784] [debug] Marked page dirty: virt=0x10920
[2025-07-06 01:50:10.784] [debug] Wrote virtual memory chunk: virt=0x10920, size=0x10
[2025-07-06 01:50:10.785] [debug] Writing virtual memory chunk: virt=0x11930, size=0x1, process=0
[2025-07-06 01:50:10.785] [info] Translating virtual to physical: virt=0x11930, process=0
[2025-07-06 01:50:10.785] [debug] Marked page dirty: virt=0x11930
[2025-07-06 01:50:10.785] [debug] Wrote virtual memory chunk: virt=0x11930, size=0x1
[2025-07-06 01:50:10.785] [debug] Writing virtual memory chunk: virt=0x10930, size=0x10, process=0
[2025-07-06 01:50:10.785] [info] Translating virtual to physical: virt=0x10930, process=0
[2025-07-06 01:50:10.785] [debug] Marked page dirty: virt=0x10930
[2025-07-06 01:50:10.785] [debug] Wrote virtual memory chunk: virt=0x10930, size=0x10
[2025-07-06 01:50:10.785] [debug] Writing virtual memory chunk: virt=0x11940, size=0x1, process=0
[2025-07-06 01:50:10.785] [info] Translating virtual to physical: virt=0x11940, process=0
[2025-07-06 01:50:10.785] [debug] Marked page dirty: virt=0x11940
[2025-07-06 01:50:10.785] [debug] Wrote virtual memory chunk: virt=0x11940, size=0x1
[2025-07-06 01:50:10.785] [debug] Writing virtual memory chunk: virt=0x10940, size=0x10, process=0
[2025-07-06 01:50:10.785] [info] Translating virtual to physical: virt=0x10940, process=0
[2025-07-06 01:50:10.785] [debug] Marked page dirty: virt=0x10940
[2025-07-06 01:50:10.785] [debug] Wrote virtual memory chunk: virt=0x10940, size=0x10
[2025-07-06 01:50:10.786] [debug] Writing virtual memory chunk: virt=0x11950, size=0x1, process=0
[2025-07-06 01:50:10.786] [info] Translating virtual to physical: virt=0x11950, process=0
[2025-07-06 01:50:10.786] [debug] Marked page dirty: virt=0x11950
[2025-07-06 01:50:10.786] [debug] Wrote virtual memory chunk: virt=0x11950, size=0x1
[2025-07-06 01:50:10.786] [debug] Writing virtual memory chunk: virt=0x10950, size=0x10, process=0
[2025-07-06 01:50:10.786] [info] Translating virtual to physical: virt=0x10950, process=0
[2025-07-06 01:50:10.786] [debug] Marked page dirty: virt=0x10950
[2025-07-06 01:50:10.786] [debug] Wrote virtual memory chunk: virt=0x10950, size=0x10
[2025-07-06 01:50:10.786] [debug] Writing virtual memory chunk: virt=0x11960, size=0x1, process=0
[2025-07-06 01:50:10.786] [info] Translating virtual to physical: virt=0x11960, process=0
[2025-07-06 01:50:10.786] [debug] Marked page dirty: virt=0x11960
[2025-07-06 01:50:10.786] [debug] Wrote virtual memory chunk: virt=0x11960, size=0x1
[2025-07-06 01:50:10.786] [debug] Writing virtual memory chunk: virt=0x10960, size=0x10, process=0
[2025-07-06 01:50:10.786] [info] Translating virtual to physical: virt=0x10960, process=0
[2025-07-06 01:50:10.786] [debug] Marked page dirty: virt=0x10960
[2025-07-06 01:50:10.786] [debug] Wrote virtual memory chunk: virt=0x10960, size=0x10
[2025-07-06 01:50:10.787] [info] Processed descriptor 150
[2025-07-06 01:50:10.787] [debug] Writing virtual memory chunk: virt=0x11970, size=0x1, process=0
[2025-07-06 01:50:10.787] [info] Translating virtual to physical: virt=0x11970, process=0
[2025-07-06 01:50:10.787] [debug] Marked page dirty: virt=0x11970
[2025-07-06 01:50:10.787] [debug] Wrote virtual memory chunk: virt=0x11970, size=0x1
[2025-07-06 01:50:10.787] [debug] Writing virtual memory chunk: virt=0x10970, size=0x10, process=0
[2025-07-06 01:50:10.787] [info] Translating virtual to physical: virt=0x10970, process=0
[2025-07-06 01:50:10.787] [debug] Marked page dirty: virt=0x10970
[2025-07-06 01:50:10.787] [debug] Wrote virtual memory chunk: virt=0x10970, size=0x10
[2025-07-06 01:50:10.787] [debug] Writing virtual memory chunk: virt=0x11980, size=0x1, process=0
[2025-07-06 01:50:10.787] [info] Translating virtual to physical: virt=0x11980, process=0
[2025-07-06 01:50:10.787] [debug] Marked page dirty: virt=0x11980
[2025-07-06 01:50:10.788] [debug] Wrote virtual memory chunk: virt=0x11980, size=0x1
[2025-07-06 01:50:10.788] [debug] Writing virtual memory chunk: virt=0x10980, size=0x10, process=0
[2025-07-06 01:50:10.788] [info] Translating virtual to physical: virt=0x10980, process=0
[2025-07-06 01:50:10.788] [debug] Marked page dirty: virt=0x10980
[2025-07-06 01:50:10.788] [debug] Wrote virtual memory chunk: virt=0x10980, size=0x10
[2025-07-06 01:50:10.788] [debug] Writing virtual memory chunk: virt=0x11990, size=0x1, process=0
[2025-07-06 01:50:10.788] [info] Translating virtual to physical: virt=0x11990, process=0
[2025-07-06 01:50:10.788] [debug] Marked page dirty: virt=0x11990
[2025-07-06 01:50:10.788] [debug] Wrote virtual memory chunk: virt=0x11990, size=0x1
[2025-07-06 01:50:10.788] [debug] Writing virtual memory chunk: virt=0x10990, size=0x10, process=0
[2025-07-06 01:50:10.788] [info] Translating virtual to physical: virt=0x10990, process=0
[2025-07-06 01:50:10.788] [debug] Marked page dirty: virt=0x10990
[2025-07-06 01:50:10.788] [debug] Wrote virtual memory chunk: virt=0x10990, size=0x10
[2025-07-06 01:50:10.788] [debug] Writing virtual memory chunk: virt=0x119a0, size=0x1, process=0
[2025-07-06 01:50:10.788] [info] Translating virtual to physical: virt=0x119a0, process=0
[2025-07-06 01:50:10.789] [debug] Marked page dirty: virt=0x119a0
[2025-07-06 01:50:10.789] [debug] Wrote virtual memory chunk: virt=0x119a0, size=0x1
[2025-07-06 01:50:10.789] [debug] Writing virtual memory chunk: virt=0x109a0, size=0x10, process=0
[2025-07-06 01:50:10.789] [info] Translating virtual to physical: virt=0x109a0, process=0
[2025-07-06 01:50:10.789] [debug] Marked page dirty: virt=0x109a0
[2025-07-06 01:50:10.789] [debug] Wrote virtual memory chunk: virt=0x109a0, size=0x10
[2025-07-06 01:50:10.789] [debug] Writing virtual memory chunk: virt=0x119b0, size=0x1, process=0
[2025-07-06 01:50:10.789] [info] Translating virtual to physical: virt=0x119b0, process=0
[2025-07-06 01:50:10.789] [debug] Marked page dirty: virt=0x119b0
[2025-07-06 01:50:10.789] [debug] Wrote virtual memory chunk: virt=0x119b0, size=0x1
[2025-07-06 01:50:10.789] [debug] Writing virtual memory chunk: virt=0x109b0, size=0x10, process=0
[2025-07-06 01:50:10.789] [info] Translating virtual to physical: virt=0x109b0, process=0
[2025-07-06 01:50:10.789] [debug] Marked page dirty: virt=0x109b0
[2025-07-06 01:50:10.789] [debug] Wrote virtual memory chunk: virt=0x109b0, size=0x10
[2025-07-06 01:50:10.789] [debug] Writing virtual memory chunk: virt=0x119c0, size=0x1, process=0
[2025-07-06 01:50:10.789] [info] Translating virtual to physical: virt=0x119c0, process=0
[2025-07-06 01:50:10.790] [debug] Marked page dirty: virt=0x119c0
[2025-07-06 01:50:10.790] [debug] Wrote virtual memory chunk: virt=0x119c0, size=0x1
[2025-07-06 01:50:10.790] [debug] Writing virtual memory chunk: virt=0x109c0, size=0x10, process=0
[2025-07-06 01:50:10.790] [info] Translating virtual to physical: virt=0x109c0, process=0
[2025-07-06 01:50:10.790] [debug] Marked page dirty: virt=0x109c0
[2025-07-06 01:50:10.790] [debug] Wrote virtual memory chunk: virt=0x109c0, size=0x10
[2025-07-06 01:50:10.790] [debug] Writing virtual memory chunk: virt=0x119d0, size=0x1, process=0
[2025-07-06 01:50:10.790] [info] Translating virtual to physical: virt=0x119d0, process=0
[2025-07-06 01:50:10.790] [debug] Marked page dirty: virt=0x119d0
[2025-07-06 01:50:10.790] [debug] Wrote virtual memory chunk: virt=0x119d0, size=0x1
[2025-07-06 01:50:10.790] [debug] Writing virtual memory chunk: virt=0x109d0, size=0x10, process=0
[2025-07-06 01:50:10.790] [info] Translating virtual to physical: virt=0x109d0, process=0
[2025-07-06 01:50:10.790] [debug] Marked page dirty: virt=0x109d0
[2025-07-06 01:50:10.790] [debug] Wrote virtual memory chunk: virt=0x109d0, size=0x10
[2025-07-06 01:50:10.790] [debug] Writing virtual memory chunk: virt=0x119e0, size=0x1, process=0
[2025-07-06 01:50:10.790] [info] Translating virtual to physical: virt=0x119e0, process=0
[2025-07-06 01:50:10.790] [debug] Marked page dirty: virt=0x119e0
[2025-07-06 01:50:10.790] [debug] Wrote virtual memory chunk: virt=0x119e0, size=0x1
[2025-07-06 01:50:10.791] [debug] Writing virtual memory chunk: virt=0x109e0, size=0x10, process=0
[2025-07-06 01:50:10.791] [info] Translating virtual to physical: virt=0x109e0, process=0
[2025-07-06 01:50:10.791] [debug] Marked page dirty: virt=0x109e0
[2025-07-06 01:50:10.791] [debug] Wrote virtual memory chunk: virt=0x109e0, size=0x10
[2025-07-06 01:50:10.791] [debug] Writing virtual memory chunk: virt=0x119f0, size=0x1, process=0
[2025-07-06 01:50:10.791] [info] Translating virtual to physical: virt=0x119f0, process=0
[2025-07-06 01:50:10.791] [debug] Marked page dirty: virt=0x119f0
[2025-07-06 01:50:10.791] [debug] Wrote virtual memory chunk: virt=0x119f0, size=0x1
[2025-07-06 01:50:10.791] [debug] Writing virtual memory chunk: virt=0x109f0, size=0x10, process=0
[2025-07-06 01:50:10.791] [info] Translating virtual to physical: virt=0x109f0, process=0
[2025-07-06 01:50:10.791] [debug] Marked page dirty: virt=0x109f0
[2025-07-06 01:50:10.791] [debug] Wrote virtual memory chunk: virt=0x109f0, size=0x10
[2025-07-06 01:50:10.791] [debug] Writing virtual memory chunk: virt=0x11a00, size=0x1, process=0
[2025-07-06 01:50:10.791] [info] Translating virtual to physical: virt=0x11a00, process=0
[2025-07-06 01:50:10.791] [debug] Marked page dirty: virt=0x11a00
[2025-07-06 01:50:10.792] [debug] Wrote virtual memory chunk: virt=0x11a00, size=0x1
[2025-07-06 01:50:10.792] [debug] Writing virtual memory chunk: virt=0x10a00, size=0x10, process=0
[2025-07-06 01:50:10.792] [info] Translating virtual to physical: virt=0x10a00, process=0
[2025-07-06 01:50:10.792] [debug] Marked page dirty: virt=0x10a00
[2025-07-06 01:50:10.792] [debug] Wrote virtual memory chunk: virt=0x10a00, size=0x10
[2025-07-06 01:50:10.792] [debug] Writing virtual memory chunk: virt=0x11a10, size=0x1, process=0
[2025-07-06 01:50:10.792] [info] Translating virtual to physical: virt=0x11a10, process=0
[2025-07-06 01:50:10.792] [debug] Marked page dirty: virt=0x11a10
[2025-07-06 01:50:10.792] [debug] Wrote virtual memory chunk: virt=0x11a10, size=0x1
[2025-07-06 01:50:10.792] [debug] Writing virtual memory chunk: virt=0x10a10, size=0x10, process=0
[2025-07-06 01:50:10.792] [info] Translating virtual to physical: virt=0x10a10, process=0
[2025-07-06 01:50:10.792] [debug] Marked page dirty: virt=0x10a10
[2025-07-06 01:50:10.792] [debug] Wrote virtual memory chunk: virt=0x10a10, size=0x10
[2025-07-06 01:50:10.792] [debug] Writing virtual memory chunk: virt=0x11a20, size=0x1, process=0
[2025-07-06 01:50:10.792] [info] Translating virtual to physical: virt=0x11a20, process=0
[2025-07-06 01:50:10.793] [debug] Marked page dirty: virt=0x11a20
[2025-07-06 01:50:10.793] [debug] Wrote virtual memory chunk: virt=0x11a20, size=0x1
[2025-07-06 01:50:10.793] [debug] Writing virtual memory chunk: virt=0x10a20, size=0x10, process=0
[2025-07-06 01:50:10.793] [info] Translating virtual to physical: virt=0x10a20, process=0
[2025-07-06 01:50:10.793] [debug] Marked page dirty: virt=0x10a20
[2025-07-06 01:50:10.793] [debug] Wrote virtual memory chunk: virt=0x10a20, size=0x10
[2025-07-06 01:50:10.793] [debug] Writing virtual memory chunk: virt=0x11a30, size=0x1, process=0
[2025-07-06 01:50:10.793] [info] Translating virtual to physical: virt=0x11a30, process=0
[2025-07-06 01:50:10.793] [debug] Marked page dirty: virt=0x11a30
[2025-07-06 01:50:10.793] [debug] Wrote virtual memory chunk: virt=0x11a30, size=0x1
[2025-07-06 01:50:10.793] [debug] Writing virtual memory chunk: virt=0x10a30, size=0x10, process=0
[2025-07-06 01:50:10.793] [info] Translating virtual to physical: virt=0x10a30, process=0
[2025-07-06 01:50:10.793] [debug] Marked page dirty: virt=0x10a30
[2025-07-06 01:50:10.793] [debug] Wrote virtual memory chunk: virt=0x10a30, size=0x10
[2025-07-06 01:50:10.793] [debug] Writing virtual memory chunk: virt=0x11a40, size=0x1, process=0
[2025-07-06 01:50:10.793] [info] Translating virtual to physical: virt=0x11a40, process=0
[2025-07-06 01:50:10.794] [debug] Marked page dirty: virt=0x11a40
[2025-07-06 01:50:10.794] [debug] Wrote virtual memory chunk: virt=0x11a40, size=0x1
[2025-07-06 01:50:10.795] [debug] Writing virtual memory chunk: virt=0x10a40, size=0x10, process=0
[2025-07-06 01:50:10.795] [info] Translating virtual to physical: virt=0x10a40, process=0
[2025-07-06 01:50:10.796] [debug] Marked page dirty: virt=0x10a40
[2025-07-06 01:50:10.796] [debug] Wrote virtual memory chunk: virt=0x10a40, size=0x10
[2025-07-06 01:50:10.796] [debug] Writing virtual memory chunk: virt=0x11a50, size=0x1, process=0
[2025-07-06 01:50:10.796] [info] Translating virtual to physical: virt=0x11a50, process=0
[2025-07-06 01:50:10.796] [debug] Marked page dirty: virt=0x11a50
[2025-07-06 01:50:10.796] [debug] Wrote virtual memory chunk: virt=0x11a50, size=0x1
[2025-07-06 01:50:10.796] [debug] Writing virtual memory chunk: virt=0x10a50, size=0x10, process=0
[2025-07-06 01:50:10.796] [info] Translating virtual to physical: virt=0x10a50, process=0
[2025-07-06 01:50:10.796] [debug] Marked page dirty: virt=0x10a50
[2025-07-06 01:50:10.796] [debug] Wrote virtual memory chunk: virt=0x10a50, size=0x10
[2025-07-06 01:50:10.796] [debug] Writing virtual memory chunk: virt=0x11a60, size=0x1, process=0
[2025-07-06 01:50:10.796] [info] Translating virtual to physical: virt=0x11a60, process=0
[2025-07-06 01:50:10.796] [debug] Marked page dirty: virt=0x11a60
[2025-07-06 01:50:10.796] [debug] Wrote virtual memory chunk: virt=0x11a60, size=0x1
[2025-07-06 01:50:10.796] [debug] Writing virtual memory chunk: virt=0x10a60, size=0x10, process=0
[2025-07-06 01:50:10.796] [info] Translating virtual to physical: virt=0x10a60, process=0
[2025-07-06 01:50:10.796] [debug] Marked page dirty: virt=0x10a60
[2025-07-06 01:50:10.797] [debug] Wrote virtual memory chunk: virt=0x10a60, size=0x10
[2025-07-06 01:50:10.797] [debug] Writing virtual memory chunk: virt=0x11a70, size=0x1, process=0
[2025-07-06 01:50:10.797] [info] Translating virtual to physical: virt=0x11a70, process=0
[2025-07-06 01:50:10.797] [debug] Marked page dirty: virt=0x11a70
[2025-07-06 01:50:10.797] [debug] Wrote virtual memory chunk: virt=0x11a70, size=0x1
[2025-07-06 01:50:10.797] [debug] Writing virtual memory chunk: virt=0x10a70, size=0x10, process=0
[2025-07-06 01:50:10.797] [info] Translating virtual to physical: virt=0x10a70, process=0
[2025-07-06 01:50:10.797] [debug] Marked page dirty: virt=0x10a70
[2025-07-06 01:50:10.797] [debug] Wrote virtual memory chunk: virt=0x10a70, size=0x10
[2025-07-06 01:50:10.798] [debug] Writing virtual memory chunk: virt=0x11a80, size=0x1, process=0
[2025-07-06 01:50:10.798] [info] Translating virtual to physical: virt=0x11a80, process=0
[2025-07-06 01:50:10.798] [debug] Marked page dirty: virt=0x11a80
[2025-07-06 01:50:10.798] [debug] Wrote virtual memory chunk: virt=0x11a80, size=0x1
[2025-07-06 01:50:10.798] [debug] Writing virtual memory chunk: virt=0x10a80, size=0x10, process=0
[2025-07-06 01:50:10.798] [info] Translating virtual to physical: virt=0x10a80, process=0
[2025-07-06 01:50:10.798] [debug] Marked page dirty: virt=0x10a80
[2025-07-06 01:50:10.798] [debug] Wrote virtual memory chunk: virt=0x10a80, size=0x10
[2025-07-06 01:50:10.798] [debug] Writing virtual memory chunk: virt=0x11a90, size=0x1, process=0
[2025-07-06 01:50:10.798] [info] Translating virtual to physical: virt=0x11a90, process=0
[2025-07-06 01:50:10.798] [debug] Marked page dirty: virt=0x11a90
[2025-07-06 01:50:10.798] [debug] Wrote virtual memory chunk: virt=0x11a90, size=0x1
[2025-07-06 01:50:10.798] [debug] Writing virtual memory chunk: virt=0x10a90, size=0x10, process=0
[2025-07-06 01:50:10.798] [info] Translating virtual to physical: virt=0x10a90, process=0
[2025-07-06 01:50:10.798] [debug] Marked page dirty: virt=0x10a90
[2025-07-06 01:50:10.798] [debug] Wrote virtual memory chunk: virt=0x10a90, size=0x10
[2025-07-06 01:50:10.799] [debug] Writing virtual memory chunk: virt=0x11aa0, size=0x1, process=0
[2025-07-06 01:50:10.799] [info] Translating virtual to physical: virt=0x11aa0, process=0
[2025-07-06 01:50:10.799] [debug] Marked page dirty: virt=0x11aa0
[2025-07-06 01:50:10.799] [debug] Wrote virtual memory chunk: virt=0x11aa0, size=0x1
[2025-07-06 01:50:10.799] [debug] Writing virtual memory chunk: virt=0x10aa0, size=0x10, process=0
[2025-07-06 01:50:10.799] [info] Translating virtual to physical: virt=0x10aa0, process=0
[2025-07-06 01:50:10.799] [debug] Marked page dirty: virt=0x10aa0
[2025-07-06 01:50:10.799] [debug] Wrote virtual memory chunk: virt=0x10aa0, size=0x10
[2025-07-06 01:50:10.799] [debug] Writing virtual memory chunk: virt=0x11ab0, size=0x1, process=0
[2025-07-06 01:50:10.799] [info] Translating virtual to physical: virt=0x11ab0, process=0
[2025-07-06 01:50:10.799] [debug] Marked page dirty: virt=0x11ab0
[2025-07-06 01:50:10.799] [debug] Wrote virtual memory chunk: virt=0x11ab0, size=0x1
[2025-07-06 01:50:10.799] [debug] Writing virtual memory chunk: virt=0x10ab0, size=0x10, process=0
[2025-07-06 01:50:10.799] [info] Translating virtual to physical: virt=0x10ab0, process=0
[2025-07-06 01:50:10.799] [debug] Marked page dirty: virt=0x10ab0
[2025-07-06 01:50:10.799] [debug] Wrote virtual memory chunk: virt=0x10ab0, size=0x10
[2025-07-06 01:50:10.800] [debug] Writing virtual memory chunk: virt=0x11ac0, size=0x1, process=0
[2025-07-06 01:50:10.800] [info] Translating virtual to physical: virt=0x11ac0, process=0
[2025-07-06 01:50:10.800] [debug] Marked page dirty: virt=0x11ac0
[2025-07-06 01:50:10.800] [debug] Wrote virtual memory chunk: virt=0x11ac0, size=0x1
[2025-07-06 01:50:10.800] [debug] Writing virtual memory chunk: virt=0x10ac0, size=0x10, process=0
[2025-07-06 01:50:10.800] [info] Translating virtual to physical: virt=0x10ac0, process=0
[2025-07-06 01:50:10.800] [debug] Marked page dirty: virt=0x10ac0
[2025-07-06 01:50:10.800] [debug] Wrote virtual memory chunk: virt=0x10ac0, size=0x10
[2025-07-06 01:50:10.800] [debug] Writing virtual memory chunk: virt=0x11ad0, size=0x1, process=0
[2025-07-06 01:50:10.800] [info] Translating virtual to physical: virt=0x11ad0, process=0
[2025-07-06 01:50:10.800] [debug] Marked page dirty: virt=0x11ad0
[2025-07-06 01:50:10.801] [debug] Wrote virtual memory chunk: virt=0x11ad0, size=0x1
[2025-07-06 01:50:10.801] [debug] Writing virtual memory chunk: virt=0x10ad0, size=0x10, process=0
[2025-07-06 01:50:10.801] [info] Translating virtual to physical: virt=0x10ad0, process=0
[2025-07-06 01:50:10.801] [debug] Marked page dirty: virt=0x10ad0
[2025-07-06 01:50:10.801] [debug] Wrote virtual memory chunk: virt=0x10ad0, size=0x10
[2025-07-06 01:50:10.801] [debug] Writing virtual memory chunk: virt=0x11ae0, size=0x1, process=0
[2025-07-06 01:50:10.801] [info] Translating virtual to physical: virt=0x11ae0, process=0
[2025-07-06 01:50:10.801] [debug] Marked page dirty: virt=0x11ae0
[2025-07-06 01:50:10.801] [debug] Wrote virtual memory chunk: virt=0x11ae0, size=0x1
[2025-07-06 01:50:10.801] [debug] Writing virtual memory chunk: virt=0x10ae0, size=0x10, process=0
[2025-07-06 01:50:10.801] [info] Translating virtual to physical: virt=0x10ae0, process=0
[2025-07-06 01:50:10.801] [debug] Marked page dirty: virt=0x10ae0
[2025-07-06 01:50:10.801] [debug] Wrote virtual memory chunk: virt=0x10ae0, size=0x10
[2025-07-06 01:50:10.801] [debug] Writing virtual memory chunk: virt=0x11af0, size=0x1, process=0
[2025-07-06 01:50:10.801] [info] Translating virtual to physical: virt=0x11af0, process=0
[2025-07-06 01:50:10.801] [debug] Marked page dirty: virt=0x11af0
[2025-07-06 01:50:10.801] [debug] Wrote virtual memory chunk: virt=0x11af0, size=0x1
[2025-07-06 01:50:10.802] [debug] Writing virtual memory chunk: virt=0x10af0, size=0x10, process=0
[2025-07-06 01:50:10.802] [info] Translating virtual to physical: virt=0x10af0, process=0
[2025-07-06 01:50:10.802] [debug] Marked page dirty: virt=0x10af0
[2025-07-06 01:50:10.802] [debug] Wrote virtual memory chunk: virt=0x10af0, size=0x10
[2025-07-06 01:50:10.802] [debug] Writing virtual memory chunk: virt=0x11b00, size=0x1, process=0
[2025-07-06 01:50:10.802] [info] Translating virtual to physical: virt=0x11b00, process=0
[2025-07-06 01:50:10.802] [debug] Marked page dirty: virt=0x11b00
[2025-07-06 01:50:10.802] [debug] Wrote virtual memory chunk: virt=0x11b00, size=0x1
[2025-07-06 01:50:10.802] [debug] Writing virtual memory chunk: virt=0x10b00, size=0x10, process=0
[2025-07-06 01:50:10.802] [info] Translating virtual to physical: virt=0x10b00, process=0
[2025-07-06 01:50:10.802] [debug] Marked page dirty: virt=0x10b00
[2025-07-06 01:50:10.802] [debug] Wrote virtual memory chunk: virt=0x10b00, size=0x10
[2025-07-06 01:50:10.802] [debug] Writing virtual memory chunk: virt=0x11b10, size=0x1, process=0
[2025-07-06 01:50:10.802] [info] Translating virtual to physical: virt=0x11b10, process=0
[2025-07-06 01:50:10.802] [debug] Marked page dirty: virt=0x11b10
[2025-07-06 01:50:10.802] [debug] Wrote virtual memory chunk: virt=0x11b10, size=0x1
[2025-07-06 01:50:10.802] [debug] Writing virtual memory chunk: virt=0x10b10, size=0x10, process=0
[2025-07-06 01:50:10.803] [info] Translating virtual to physical: virt=0x10b10, process=0
[2025-07-06 01:50:10.803] [debug] Marked page dirty: virt=0x10b10
[2025-07-06 01:50:10.803] [debug] Wrote virtual memory chunk: virt=0x10b10, size=0x10
[2025-07-06 01:50:10.803] [debug] Writing virtual memory chunk: virt=0x11b20, size=0x1, process=0
[2025-07-06 01:50:10.803] [info] Translating virtual to physical: virt=0x11b20, process=0
[2025-07-06 01:50:10.803] [debug] Marked page dirty: virt=0x11b20
[2025-07-06 01:50:10.803] [debug] Wrote virtual memory chunk: virt=0x11b20, size=0x1
[2025-07-06 01:50:10.803] [debug] Writing virtual memory chunk: virt=0x10b20, size=0x10, process=0
[2025-07-06 01:50:10.803] [info] Translating virtual to physical: virt=0x10b20, process=0
[2025-07-06 01:50:10.803] [debug] Marked page dirty: virt=0x10b20
[2025-07-06 01:50:10.803] [debug] Wrote virtual memory chunk: virt=0x10b20, size=0x10
[2025-07-06 01:50:10.803] [debug] Writing virtual memory chunk: virt=0x11b30, size=0x1, process=0
[2025-07-06 01:50:10.803] [info] Translating virtual to physical: virt=0x11b30, process=0
[2025-07-06 01:50:10.803] [debug] Marked page dirty: virt=0x11b30
[2025-07-06 01:50:10.803] [debug] Wrote virtual memory chunk: virt=0x11b30, size=0x1
[2025-07-06 01:50:10.803] [debug] Writing virtual memory chunk: virt=0x10b30, size=0x10, process=0
[2025-07-06 01:50:10.804] [info] Translating virtual to physical: virt=0x10b30, process=0
[2025-07-06 01:50:10.804] [debug] Marked page dirty: virt=0x10b30
[2025-07-06 01:50:10.804] [debug] Wrote virtual memory chunk: virt=0x10b30, size=0x10
[2025-07-06 01:50:10.804] [debug] Writing virtual memory chunk: virt=0x11b40, size=0x1, process=0
[2025-07-06 01:50:10.804] [info] Translating virtual to physical: virt=0x11b40, process=0
[2025-07-06 01:50:10.804] [debug] Marked page dirty: virt=0x11b40
[2025-07-06 01:50:10.804] [debug] Wrote virtual memory chunk: virt=0x11b40, size=0x1
[2025-07-06 01:50:10.804] [debug] Writing virtual memory chunk: virt=0x10b40, size=0x10, process=0
[2025-07-06 01:50:10.804] [info] Translating virtual to physical: virt=0x10b40, process=0
[2025-07-06 01:50:10.804] [debug] Marked page dirty: virt=0x10b40
[2025-07-06 01:50:10.804] [debug] Wrote virtual memory chunk: virt=0x10b40, size=0x10
[2025-07-06 01:50:10.804] [debug] Writing virtual memory chunk: virt=0x11b50, size=0x1, process=0
[2025-07-06 01:50:10.804] [info] Translating virtual to physical: virt=0x11b50, process=0
[2025-07-06 01:50:10.804] [debug] Marked page dirty: virt=0x11b50
[2025-07-06 01:50:10.804] [debug] Wrote virtual memory chunk: virt=0x11b50, size=0x1
[2025-07-06 01:50:10.804] [debug] Writing virtual memory chunk: virt=0x10b50, size=0x10, process=0
[2025-07-06 01:50:10.805] [info] Translating virtual to physical: virt=0x10b50, process=0
[2025-07-06 01:50:10.805] [debug] Marked page dirty: virt=0x10b50
[2025-07-06 01:50:10.805] [debug] Wrote virtual memory chunk: virt=0x10b50, size=0x10
[2025-07-06 01:50:10.805] [debug] Writing virtual memory chunk: virt=0x11b60, size=0x1, process=0
[2025-07-06 01:50:10.805] [info] Translating virtual to physical: virt=0x11b60, process=0
[2025-07-06 01:50:10.805] [debug] Marked page dirty: virt=0x11b60
[2025-07-06 01:50:10.805] [debug] Wrote virtual memory chunk: virt=0x11b60, size=0x1
[2025-07-06 01:50:10.805] [debug] Writing virtual memory chunk: virt=0x10b60, size=0x10, process=0
[2025-07-06 01:50:10.805] [info] Translating virtual to physical: virt=0x10b60, process=0
[2025-07-06 01:50:10.805] [debug] Marked page dirty: virt=0x10b60
[2025-07-06 01:50:10.805] [debug] Wrote virtual memory chunk: virt=0x10b60, size=0x10
[2025-07-06 01:50:10.805] [debug] Writing virtual memory chunk: virt=0x11b70, size=0x1, process=0
[2025-07-06 01:50:10.805] [info] Translating virtual to physical: virt=0x11b70, process=0
[2025-07-06 01:50:10.805] [debug] Marked page dirty: virt=0x11b70
[2025-07-06 01:50:10.805] [debug] Wrote virtual memory chunk: virt=0x11b70, size=0x1
[2025-07-06 01:50:10.805] [debug] Writing virtual memory chunk: virt=0x10b70, size=0x10, process=0
[2025-07-06 01:50:10.805] [info] Translating virtual to physical: virt=0x10b70, process=0
[2025-07-06 01:50:10.806] [debug] Marked page dirty: virt=0x10b70
[2025-07-06 01:50:10.806] [debug] Wrote virtual memory chunk: virt=0x10b70, size=0x10
[2025-07-06 01:50:10.806] [debug] Writing virtual memory chunk: virt=0x11b80, size=0x1, process=0
[2025-07-06 01:50:10.806] [info] Translating virtual to physical: virt=0x11b80, process=0
[2025-07-06 01:50:10.806] [debug] Marked page dirty: virt=0x11b80
[2025-07-06 01:50:10.806] [debug] Wrote virtual memory chunk: virt=0x11b80, size=0x1
[2025-07-06 01:50:10.806] [debug] Writing virtual memory chunk: virt=0x10b80, size=0x10, process=0
[2025-07-06 01:50:10.806] [info] Translating virtual to physical: virt=0x10b80, process=0
[2025-07-06 01:50:10.806] [debug] Marked page dirty: virt=0x10b80
[2025-07-06 01:50:10.806] [debug] Wrote virtual memory chunk: virt=0x10b80, size=0x10
[2025-07-06 01:50:10.806] [debug] Writing virtual memory chunk: virt=0x11b90, size=0x1, process=0
[2025-07-06 01:50:10.806] [info] Translating virtual to physical: virt=0x11b90, process=0
[2025-07-06 01:50:10.806] [debug] Marked page dirty: virt=0x11b90
[2025-07-06 01:50:10.806] [debug] Wrote virtual memory chunk: virt=0x11b90, size=0x1
[2025-07-06 01:50:10.806] [debug] Writing virtual memory chunk: virt=0x10b90, size=0x10, process=0
[2025-07-06 01:50:10.806] [info] Translating virtual to physical: virt=0x10b90, process=0
[2025-07-06 01:50:10.806] [debug] Marked page dirty: virt=0x10b90
[2025-07-06 01:50:10.807] [debug] Wrote virtual memory chunk: virt=0x10b90, size=0x10
[2025-07-06 01:50:10.807] [debug] Writing virtual memory chunk: virt=0x11ba0, size=0x1, process=0
[2025-07-06 01:50:10.807] [info] Translating virtual to physical: virt=0x11ba0, process=0
[2025-07-06 01:50:10.807] [debug] Marked page dirty: virt=0x11ba0
[2025-07-06 01:50:10.807] [debug] Wrote virtual memory chunk: virt=0x11ba0, size=0x1
[2025-07-06 01:50:10.807] [debug] Writing virtual memory chunk: virt=0x10ba0, size=0x10, process=0
[2025-07-06 01:50:10.807] [info] Translating virtual to physical: virt=0x10ba0, process=0
[2025-07-06 01:50:10.807] [debug] Marked page dirty: virt=0x10ba0
[2025-07-06 01:50:10.807] [debug] Wrote virtual memory chunk: virt=0x10ba0, size=0x10
[2025-07-06 01:50:10.807] [debug] Writing virtual memory chunk: virt=0x11bb0, size=0x1, process=0
[2025-07-06 01:50:10.807] [info] Translating virtual to physical: virt=0x11bb0, process=0
[2025-07-06 01:50:10.807] [debug] Marked page dirty: virt=0x11bb0
[2025-07-06 01:50:10.807] [debug] Wrote virtual memory chunk: virt=0x11bb0, size=0x1
[2025-07-06 01:50:10.807] [debug] Writing virtual memory chunk: virt=0x10bb0, size=0x10, process=0
[2025-07-06 01:50:10.808] [info] Translating virtual to physical: virt=0x10bb0, process=0
[2025-07-06 01:50:10.808] [debug] Marked page dirty: virt=0x10bb0
[2025-07-06 01:50:10.808] [debug] Wrote virtual memory chunk: virt=0x10bb0, size=0x10
[2025-07-06 01:50:10.808] [debug] Writing virtual memory chunk: virt=0x11bc0, size=0x1, process=0
[2025-07-06 01:50:10.808] [info] Translating virtual to physical: virt=0x11bc0, process=0
[2025-07-06 01:50:10.808] [debug] Marked page dirty: virt=0x11bc0
[2025-07-06 01:50:10.808] [debug] Wrote virtual memory chunk: virt=0x11bc0, size=0x1
[2025-07-06 01:50:10.808] [debug] Writing virtual memory chunk: virt=0x10bc0, size=0x10, process=0
[2025-07-06 01:50:10.808] [info] Translating virtual to physical: virt=0x10bc0, process=0
[2025-07-06 01:50:10.808] [debug] Marked page dirty: virt=0x10bc0
[2025-07-06 01:50:10.808] [debug] Wrote virtual memory chunk: virt=0x10bc0, size=0x10
[2025-07-06 01:50:10.808] [debug] Writing virtual memory chunk: virt=0x11bd0, size=0x1, process=0
[2025-07-06 01:50:10.808] [info] Translating virtual to physical: virt=0x11bd0, process=0
[2025-07-06 01:50:10.808] [debug] Marked page dirty: virt=0x11bd0
[2025-07-06 01:50:10.808] [debug] Wrote virtual memory chunk: virt=0x11bd0, size=0x1
[2025-07-06 01:50:10.808] [debug] Writing virtual memory chunk: virt=0x10bd0, size=0x10, process=0
[2025-07-06 01:50:10.808] [info] Translating virtual to physical: virt=0x10bd0, process=0
[2025-07-06 01:50:10.809] [debug] Marked page dirty: virt=0x10bd0
[2025-07-06 01:50:10.809] [debug] Wrote virtual memory chunk: virt=0x10bd0, size=0x10
[2025-07-06 01:50:10.809] [debug] Writing virtual memory chunk: virt=0x11be0, size=0x1, process=0
[2025-07-06 01:50:10.809] [info] Translating virtual to physical: virt=0x11be0, process=0
[2025-07-06 01:50:10.809] [debug] Marked page dirty: virt=0x11be0
[2025-07-06 01:50:10.809] [debug] Wrote virtual memory chunk: virt=0x11be0, size=0x1
[2025-07-06 01:50:10.809] [debug] Writing virtual memory chunk: virt=0x10be0, size=0x10, process=0
[2025-07-06 01:50:10.809] [info] Translating virtual to physical: virt=0x10be0, process=0
[2025-07-06 01:50:10.809] [debug] Marked page dirty: virt=0x10be0
[2025-07-06 01:50:10.809] [debug] Wrote virtual memory chunk: virt=0x10be0, size=0x10
[2025-07-06 01:50:10.809] [debug] Writing virtual memory chunk: virt=0x11bf0, size=0x1, process=0
[2025-07-06 01:50:10.809] [info] Translating virtual to physical: virt=0x11bf0, process=0
[2025-07-06 01:50:10.809] [debug] Marked page dirty: virt=0x11bf0
[2025-07-06 01:50:10.809] [debug] Wrote virtual memory chunk: virt=0x11bf0, size=0x1
[2025-07-06 01:50:10.809] [debug] Writing virtual memory chunk: virt=0x10bf0, size=0x10, process=0
[2025-07-06 01:50:10.809] [info] Translating virtual to physical: virt=0x10bf0, process=0
[2025-07-06 01:50:10.810] [debug] Marked page dirty: virt=0x10bf0
[2025-07-06 01:50:10.810] [debug] Wrote virtual memory chunk: virt=0x10bf0, size=0x10
[2025-07-06 01:50:10.811] [debug] Writing virtual memory chunk: virt=0x11c00, size=0x1, process=0
[2025-07-06 01:50:10.811] [info] Translating virtual to physical: virt=0x11c00, process=0
[2025-07-06 01:50:10.811] [debug] Marked page dirty: virt=0x11c00
[2025-07-06 01:50:10.812] [debug] Wrote virtual memory chunk: virt=0x11c00, size=0x1
[2025-07-06 01:50:10.812] [debug] Writing virtual memory chunk: virt=0x10c00, size=0x10, process=0
[2025-07-06 01:50:10.812] [info] Translating virtual to physical: virt=0x10c00, process=0
[2025-07-06 01:50:10.812] [debug] Marked page dirty: virt=0x10c00
[2025-07-06 01:50:10.812] [debug] Wrote virtual memory chunk: virt=0x10c00, size=0x10
[2025-07-06 01:50:10.812] [debug] Writing virtual memory chunk: virt=0x11c10, size=0x1, process=0
[2025-07-06 01:50:10.812] [info] Translating virtual to physical: virt=0x11c10, process=0
[2025-07-06 01:50:10.812] [debug] Marked page dirty: virt=0x11c10
[2025-07-06 01:50:10.812] [debug] Wrote virtual memory chunk: virt=0x11c10, size=0x1
[2025-07-06 01:50:10.812] [debug] Writing virtual memory chunk: virt=0x10c10, size=0x10, process=0
[2025-07-06 01:50:10.812] [info] Translating virtual to physical: virt=0x10c10, process=0
[2025-07-06 01:50:10.812] [debug] Marked page dirty: virt=0x10c10
[2025-07-06 01:50:10.812] [debug] Wrote virtual memory chunk: virt=0x10c10, size=0x10
[2025-07-06 01:50:10.812] [debug] Writing virtual memory chunk: virt=0x11c20, size=0x1, process=0
[2025-07-06 01:50:10.812] [info] Translating virtual to physical: virt=0x11c20, process=0
[2025-07-06 01:50:10.812] [debug] Marked page dirty: virt=0x11c20
[2025-07-06 01:50:10.813] [debug] Wrote virtual memory chunk: virt=0x11c20, size=0x1
[2025-07-06 01:50:10.813] [debug] Writing virtual memory chunk: virt=0x10c20, size=0x10, process=0
[2025-07-06 01:50:10.813] [info] Translating virtual to physical: virt=0x10c20, process=0
[2025-07-06 01:50:10.813] [debug] Marked page dirty: virt=0x10c20
[2025-07-06 01:50:10.813] [debug] Wrote virtual memory chunk: virt=0x10c20, size=0x10
[2025-07-06 01:50:10.813] [debug] Writing virtual memory chunk: virt=0x11c30, size=0x1, process=0
[2025-07-06 01:50:10.813] [info] Translating virtual to physical: virt=0x11c30, process=0
[2025-07-06 01:50:10.813] [debug] Marked page dirty: virt=0x11c30
[2025-07-06 01:50:10.813] [debug] Wrote virtual memory chunk: virt=0x11c30, size=0x1
[2025-07-06 01:50:10.813] [debug] Writing virtual memory chunk: virt=0x10c30, size=0x10, process=0
[2025-07-06 01:50:10.813] [info] Translating virtual to physical: virt=0x10c30, process=0
[2025-07-06 01:50:10.813] [debug] Marked page dirty: virt=0x10c30
[2025-07-06 01:50:10.813] [debug] Wrote virtual memory chunk: virt=0x10c30, size=0x10
[2025-07-06 01:50:10.813] [debug] Writing virtual memory chunk: virt=0x11c40, size=0x1, process=0
[2025-07-06 01:50:10.813] [info] Translating virtual to physical: virt=0x11c40, process=0
[2025-07-06 01:50:10.813] [debug] Marked page dirty: virt=0x11c40
[2025-07-06 01:50:10.814] [debug] Wrote virtual memory chunk: virt=0x11c40, size=0x1
[2025-07-06 01:50:10.814] [debug] Writing virtual memory chunk: virt=0x10c40, size=0x10, process=0
[2025-07-06 01:50:10.814] [info] Translating virtual to physical: virt=0x10c40, process=0
[2025-07-06 01:50:10.814] [debug] Marked page dirty: virt=0x10c40
[2025-07-06 01:50:10.814] [debug] Wrote virtual memory chunk: virt=0x10c40, size=0x10
[2025-07-06 01:50:10.814] [debug] Writing virtual memory chunk: virt=0x11c50, size=0x1, process=0
[2025-07-06 01:50:10.814] [info] Translating virtual to physical: virt=0x11c50, process=0
[2025-07-06 01:50:10.814] [debug] Marked page dirty: virt=0x11c50
[2025-07-06 01:50:10.814] [debug] Wrote virtual memory chunk: virt=0x11c50, size=0x1
[2025-07-06 01:50:10.814] [debug] Writing virtual memory chunk: virt=0x10c50, size=0x10, process=0
[2025-07-06 01:50:10.814] [info] Translating virtual to physical: virt=0x10c50, process=0
[2025-07-06 01:50:10.814] [debug] Marked page dirty: virt=0x10c50
[2025-07-06 01:50:10.814] [debug] Wrote virtual memory chunk: virt=0x10c50, size=0x10
[2025-07-06 01:50:10.814] [debug] Writing virtual memory chunk: virt=0x11c60, size=0x1, process=0
[2025-07-06 01:50:10.815] [info] Translating virtual to physical: virt=0x11c60, process=0
[2025-07-06 01:50:10.815] [debug] Marked page dirty: virt=0x11c60
[2025-07-06 01:50:10.815] [debug] Wrote virtual memory chunk: virt=0x11c60, size=0x1
[2025-07-06 01:50:10.815] [debug] Writing virtual memory chunk: virt=0x10c60, size=0x10, process=0
[2025-07-06 01:50:10.815] [info] Translating virtual to physical: virt=0x10c60, process=0
[2025-07-06 01:50:10.815] [debug] Marked page dirty: virt=0x10c60
[2025-07-06 01:50:10.815] [debug] Wrote virtual memory chunk: virt=0x10c60, size=0x10
[2025-07-06 01:50:10.815] [debug] Writing virtual memory chunk: virt=0x11c70, size=0x1, process=0
[2025-07-06 01:50:10.815] [info] Translating virtual to physical: virt=0x11c70, process=0
[2025-07-06 01:50:10.815] [debug] Marked page dirty: virt=0x11c70
[2025-07-06 01:50:10.815] [debug] Wrote virtual memory chunk: virt=0x11c70, size=0x1
[2025-07-06 01:50:10.815] [debug] Writing virtual memory chunk: virt=0x10c70, size=0x10, process=0
[2025-07-06 01:50:10.815] [info] Translating virtual to physical: virt=0x10c70, process=0
[2025-07-06 01:50:10.815] [debug] Marked page dirty: virt=0x10c70
[2025-07-06 01:50:10.815] [debug] Wrote virtual memory chunk: virt=0x10c70, size=0x10
[2025-07-06 01:50:10.815] [debug] Writing virtual memory chunk: virt=0x11c80, size=0x1, process=0
[2025-07-06 01:50:10.815] [info] Translating virtual to physical: virt=0x11c80, process=0
[2025-07-06 01:50:10.816] [debug] Marked page dirty: virt=0x11c80
[2025-07-06 01:50:10.816] [debug] Wrote virtual memory chunk: virt=0x11c80, size=0x1
[2025-07-06 01:50:10.816] [debug] Writing virtual memory chunk: virt=0x10c80, size=0x10, process=0
[2025-07-06 01:50:10.816] [info] Translating virtual to physical: virt=0x10c80, process=0
[2025-07-06 01:50:10.816] [debug] Marked page dirty: virt=0x10c80
[2025-07-06 01:50:10.816] [debug] Wrote virtual memory chunk: virt=0x10c80, size=0x10
[2025-07-06 01:50:10.816] [info] Processed descriptor 200
[2025-07-06 01:50:10.816] [debug] Writing virtual memory chunk: virt=0x11c90, size=0x1, process=0
[2025-07-06 01:50:10.816] [info] Translating virtual to physical: virt=0x11c90, process=0
[2025-07-06 01:50:10.816] [debug] Marked page dirty: virt=0x11c90
[2025-07-06 01:50:10.816] [debug] Wrote virtual memory chunk: virt=0x11c90, size=0x1
[2025-07-06 01:50:10.816] [debug] Writing virtual memory chunk: virt=0x10c90, size=0x10, process=0
[2025-07-06 01:50:10.816] [info] Translating virtual to physical: virt=0x10c90, process=0
[2025-07-06 01:50:10.816] [debug] Marked page dirty: virt=0x10c90
[2025-07-06 01:50:10.817] [debug] Wrote virtual memory chunk: virt=0x10c90, size=0x10
[2025-07-06 01:50:10.817] [debug] Writing virtual memory chunk: virt=0x11ca0, size=0x1, process=0
[2025-07-06 01:50:10.817] [info] Translating virtual to physical: virt=0x11ca0, process=0
[2025-07-06 01:50:10.817] [debug] Marked page dirty: virt=0x11ca0
[2025-07-06 01:50:10.817] [debug] Wrote virtual memory chunk: virt=0x11ca0, size=0x1
[2025-07-06 01:50:10.817] [debug] Writing virtual memory chunk: virt=0x10ca0, size=0x10, process=0
[2025-07-06 01:50:10.817] [info] Translating virtual to physical: virt=0x10ca0, process=0
[2025-07-06 01:50:10.817] [debug] Marked page dirty: virt=0x10ca0
[2025-07-06 01:50:10.817] [debug] Wrote virtual memory chunk: virt=0x10ca0, size=0x10
[2025-07-06 01:50:10.817] [debug] Writing virtual memory chunk: virt=0x11cb0, size=0x1, process=0
[2025-07-06 01:50:10.817] [info] Translating virtual to physical: virt=0x11cb0, process=0
[2025-07-06 01:50:10.817] [debug] Marked page dirty: virt=0x11cb0
[2025-07-06 01:50:10.817] [debug] Wrote virtual memory chunk: virt=0x11cb0, size=0x1
[2025-07-06 01:50:10.817] [debug] Writing virtual memory chunk: virt=0x10cb0, size=0x10, process=0
[2025-07-06 01:50:10.818] [info] Translating virtual to physical: virt=0x10cb0, process=0
[2025-07-06 01:50:10.818] [debug] Marked page dirty: virt=0x10cb0
[2025-07-06 01:50:10.818] [debug] Wrote virtual memory chunk: virt=0x10cb0, size=0x10
[2025-07-06 01:50:10.818] [debug] Writing virtual memory chunk: virt=0x11cc0, size=0x1, process=0
[2025-07-06 01:50:10.818] [info] Translating virtual to physical: virt=0x11cc0, process=0
[2025-07-06 01:50:10.818] [debug] Marked page dirty: virt=0x11cc0
[2025-07-06 01:50:10.818] [debug] Wrote virtual memory chunk: virt=0x11cc0, size=0x1
[2025-07-06 01:50:10.818] [debug] Writing virtual memory chunk: virt=0x10cc0, size=0x10, process=0
[2025-07-06 01:50:10.818] [info] Translating virtual to physical: virt=0x10cc0, process=0
[2025-07-06 01:50:10.818] [debug] Marked page dirty: virt=0x10cc0
[2025-07-06 01:50:10.818] [debug] Wrote virtual memory chunk: virt=0x10cc0, size=0x10
[2025-07-06 01:50:10.818] [debug] Writing virtual memory chunk: virt=0x11cd0, size=0x1, process=0
[2025-07-06 01:50:10.818] [info] Translating virtual to physical: virt=0x11cd0, process=0
[2025-07-06 01:50:10.818] [debug] Marked page dirty: virt=0x11cd0
[2025-07-06 01:50:10.818] [debug] Wrote virtual memory chunk: virt=0x11cd0, size=0x1
[2025-07-06 01:50:10.818] [debug] Writing virtual memory chunk: virt=0x10cd0, size=0x10, process=0
[2025-07-06 01:50:10.818] [info] Translating virtual to physical: virt=0x10cd0, process=0
[2025-07-06 01:50:10.819] [debug] Marked page dirty: virt=0x10cd0
[2025-07-06 01:50:10.819] [debug] Wrote virtual memory chunk: virt=0x10cd0, size=0x10
[2025-07-06 01:50:10.819] [debug] Writing virtual memory chunk: virt=0x11ce0, size=0x1, process=0
[2025-07-06 01:50:10.819] [info] Translating virtual to physical: virt=0x11ce0, process=0
[2025-07-06 01:50:10.819] [debug] Marked page dirty: virt=0x11ce0
[2025-07-06 01:50:10.819] [debug] Wrote virtual memory chunk: virt=0x11ce0, size=0x1
[2025-07-06 01:50:10.819] [debug] Writing virtual memory chunk: virt=0x10ce0, size=0x10, process=0
[2025-07-06 01:50:10.819] [info] Translating virtual to physical: virt=0x10ce0, process=0
[2025-07-06 01:50:10.819] [debug] Marked page dirty: virt=0x10ce0
[2025-07-06 01:50:10.819] [debug] Wrote virtual memory chunk: virt=0x10ce0, size=0x10
[2025-07-06 01:50:10.819] [debug] Writing virtual memory chunk: virt=0x11cf0, size=0x1, process=0
[2025-07-06 01:50:10.819] [info] Translating virtual to physical: virt=0x11cf0, process=0
[2025-07-06 01:50:10.819] [debug] Marked page dirty: virt=0x11cf0
[2025-07-06 01:50:10.819] [debug] Wrote virtual memory chunk: virt=0x11cf0, size=0x1
[2025-07-06 01:50:10.819] [debug] Writing virtual memory chunk: virt=0x10cf0, size=0x10, process=0
[2025-07-06 01:50:10.819] [info] Translating virtual to physical: virt=0x10cf0, process=0
[2025-07-06 01:50:10.820] [debug] Marked page dirty: virt=0x10cf0
[2025-07-06 01:50:10.820] [debug] Wrote virtual memory chunk: virt=0x10cf0, size=0x10
[2025-07-06 01:50:10.820] [debug] Writing virtual memory chunk: virt=0x11d00, size=0x1, process=0
[2025-07-06 01:50:10.820] [info] Translating virtual to physical: virt=0x11d00, process=0
[2025-07-06 01:50:10.820] [debug] Marked page dirty: virt=0x11d00
[2025-07-06 01:50:10.820] [debug] Wrote virtual memory chunk: virt=0x11d00, size=0x1
[2025-07-06 01:50:10.820] [debug] Writing virtual memory chunk: virt=0x10d00, size=0x10, process=0
[2025-07-06 01:50:10.820] [info] Translating virtual to physical: virt=0x10d00, process=0
[2025-07-06 01:50:10.820] [debug] Marked page dirty: virt=0x10d00
[2025-07-06 01:50:10.820] [debug] Wrote virtual memory chunk: virt=0x10d00, size=0x10
[2025-07-06 01:50:10.820] [debug] Writing virtual memory chunk: virt=0x11d10, size=0x1, process=0
[2025-07-06 01:50:10.820] [info] Translating virtual to physical: virt=0x11d10, process=0
[2025-07-06 01:50:10.820] [debug] Marked page dirty: virt=0x11d10
[2025-07-06 01:50:10.820] [debug] Wrote virtual memory chunk: virt=0x11d10, size=0x1
[2025-07-06 01:50:10.820] [debug] Writing virtual memory chunk: virt=0x10d10, size=0x10, process=0
[2025-07-06 01:50:10.820] [info] Translating virtual to physical: virt=0x10d10, process=0
[2025-07-06 01:50:10.820] [debug] Marked page dirty: virt=0x10d10
[2025-07-06 01:50:10.821] [debug] Wrote virtual memory chunk: virt=0x10d10, size=0x10
[2025-07-06 01:50:10.821] [debug] Writing virtual memory chunk: virt=0x11d20, size=0x1, process=0
[2025-07-06 01:50:10.821] [info] Translating virtual to physical: virt=0x11d20, process=0
[2025-07-06 01:50:10.821] [debug] Marked page dirty: virt=0x11d20
[2025-07-06 01:50:10.821] [debug] Wrote virtual memory chunk: virt=0x11d20, size=0x1
[2025-07-06 01:50:10.821] [debug] Writing virtual memory chunk: virt=0x10d20, size=0x10, process=0
[2025-07-06 01:50:10.821] [info] Translating virtual to physical: virt=0x10d20, process=0
[2025-07-06 01:50:10.821] [debug] Marked page dirty: virt=0x10d20
[2025-07-06 01:50:10.821] [debug] Wrote virtual memory chunk: virt=0x10d20, size=0x10
[2025-07-06 01:50:10.821] [debug] Writing virtual memory chunk: virt=0x11d30, size=0x1, process=0
[2025-07-06 01:50:10.821] [info] Translating virtual to physical: virt=0x11d30, process=0
[2025-07-06 01:50:10.821] [debug] Marked page dirty: virt=0x11d30
[2025-07-06 01:50:10.821] [debug] Wrote virtual memory chunk: virt=0x11d30, size=0x1
[2025-07-06 01:50:10.821] [debug] Writing virtual memory chunk: virt=0x10d30, size=0x10, process=0
[2025-07-06 01:50:10.821] [info] Translating virtual to physical: virt=0x10d30, process=0
[2025-07-06 01:50:10.821] [debug] Marked page dirty: virt=0x10d30
[2025-07-06 01:50:10.822] [debug] Wrote virtual memory chunk: virt=0x10d30, size=0x10
[2025-07-06 01:50:10.822] [debug] Writing virtual memory chunk: virt=0x11d40, size=0x1, process=0
[2025-07-06 01:50:10.822] [info] Translating virtual to physical: virt=0x11d40, process=0
[2025-07-06 01:50:10.822] [debug] Marked page dirty: virt=0x11d40
[2025-07-06 01:50:10.822] [debug] Wrote virtual memory chunk: virt=0x11d40, size=0x1
[2025-07-06 01:50:10.822] [debug] Writing virtual memory chunk: virt=0x10d40, size=0x10, process=0
[2025-07-06 01:50:10.822] [info] Translating virtual to physical: virt=0x10d40, process=0
[2025-07-06 01:50:10.822] [debug] Marked page dirty: virt=0x10d40
[2025-07-06 01:50:10.822] [debug] Wrote virtual memory chunk: virt=0x10d40, size=0x10
[2025-07-06 01:50:10.822] [debug] Writing virtual memory chunk: virt=0x11d50, size=0x1, process=0
[2025-07-06 01:50:10.822] [info] Translating virtual to physical: virt=0x11d50, process=0
[2025-07-06 01:50:10.822] [debug] Marked page dirty: virt=0x11d50
[2025-07-06 01:50:10.822] [debug] Wrote virtual memory chunk: virt=0x11d50, size=0x1
[2025-07-06 01:50:10.822] [debug] Writing virtual memory chunk: virt=0x10d50, size=0x10, process=0
[2025-07-06 01:50:10.822] [info] Translating virtual to physical: virt=0x10d50, process=0
[2025-07-06 01:50:10.822] [debug] Marked page dirty: virt=0x10d50
[2025-07-06 01:50:10.822] [debug] Wrote virtual memory chunk: virt=0x10d50, size=0x10
[2025-07-06 01:50:10.823] [debug] Writing virtual memory chunk: virt=0x11d60, size=0x1, process=0
[2025-07-06 01:50:10.823] [info] Translating virtual to physical: virt=0x11d60, process=0
[2025-07-06 01:50:10.823] [debug] Marked page dirty: virt=0x11d60
[2025-07-06 01:50:10.823] [debug] Wrote virtual memory chunk: virt=0x11d60, size=0x1
[2025-07-06 01:50:10.823] [debug] Writing virtual memory chunk: virt=0x10d60, size=0x10, process=0
[2025-07-06 01:50:10.823] [info] Translating virtual to physical: virt=0x10d60, process=0
[2025-07-06 01:50:10.823] [debug] Marked page dirty: virt=0x10d60
[2025-07-06 01:50:10.823] [debug] Wrote virtual memory chunk: virt=0x10d60, size=0x10
[2025-07-06 01:50:10.823] [debug] Writing virtual memory chunk: virt=0x11d70, size=0x1, process=0
[2025-07-06 01:50:10.823] [info] Translating virtual to physical: virt=0x11d70, process=0
[2025-07-06 01:50:10.823] [debug] Marked page dirty: virt=0x11d70
[2025-07-06 01:50:10.823] [debug] Wrote virtual memory chunk: virt=0x11d70, size=0x1
[2025-07-06 01:50:10.823] [debug] Writing virtual memory chunk: virt=0x10d70, size=0x10, process=0
[2025-07-06 01:50:10.823] [info] Translating virtual to physical: virt=0x10d70, process=0
[2025-07-06 01:50:10.823] [debug] Marked page dirty: virt=0x10d70
[2025-07-06 01:50:10.823] [debug] Wrote virtual memory chunk: virt=0x10d70, size=0x10
[2025-07-06 01:50:10.824] [debug] Writing virtual memory chunk: virt=0x11d80, size=0x1, process=0
[2025-07-06 01:50:10.824] [info] Translating virtual to physical: virt=0x11d80, process=0
[2025-07-06 01:50:10.824] [debug] Marked page dirty: virt=0x11d80
[2025-07-06 01:50:10.824] [debug] Wrote virtual memory chunk: virt=0x11d80, size=0x1
[2025-07-06 01:50:10.824] [debug] Writing virtual memory chunk: virt=0x10d80, size=0x10, process=0
[2025-07-06 01:50:10.824] [info] Translating virtual to physical: virt=0x10d80, process=0
[2025-07-06 01:50:10.824] [debug] Marked page dirty: virt=0x10d80
[2025-07-06 01:50:10.824] [debug] Wrote virtual memory chunk: virt=0x10d80, size=0x10
[2025-07-06 01:50:10.824] [debug] Writing virtual memory chunk: virt=0x11d90, size=0x1, process=0
[2025-07-06 01:50:10.824] [info] Translating virtual to physical: virt=0x11d90, process=0
[2025-07-06 01:50:10.824] [debug] Marked page dirty: virt=0x11d90
[2025-07-06 01:50:10.824] [debug] Wrote virtual memory chunk: virt=0x11d90, size=0x1
[2025-07-06 01:50:10.824] [debug] Writing virtual memory chunk: virt=0x10d90, size=0x10, process=0
[2025-07-06 01:50:10.824] [info] Translating virtual to physical: virt=0x10d90, process=0
[2025-07-06 01:50:10.824] [debug] Marked page dirty: virt=0x10d90
[2025-07-06 01:50:10.824] [debug] Wrote virtual memory chunk: virt=0x10d90, size=0x10
[2025-07-06 01:50:10.824] [debug] Writing virtual memory chunk: virt=0x11da0, size=0x1, process=0
[2025-07-06 01:50:10.825] [info] Translating virtual to physical: virt=0x11da0, process=0
[2025-07-06 01:50:10.825] [debug] Marked page dirty: virt=0x11da0
[2025-07-06 01:50:10.825] [debug] Wrote virtual memory chunk: virt=0x11da0, size=0x1
[2025-07-06 01:50:10.825] [debug] Writing virtual memory chunk: virt=0x10da0, size=0x10, process=0
[2025-07-06 01:50:10.825] [info] Translating virtual to physical: virt=0x10da0, process=0
[2025-07-06 01:50:10.825] [debug] Marked page dirty: virt=0x10da0
[2025-07-06 01:50:10.825] [debug] Wrote virtual memory chunk: virt=0x10da0, size=0x10
[2025-07-06 01:50:10.825] [debug] Writing virtual memory chunk: virt=0x11db0, size=0x1, process=0
[2025-07-06 01:50:10.825] [info] Translating virtual to physical: virt=0x11db0, process=0
[2025-07-06 01:50:10.825] [debug] Marked page dirty: virt=0x11db0
[2025-07-06 01:50:10.825] [debug] Wrote virtual memory chunk: virt=0x11db0, size=0x1
[2025-07-06 01:50:10.825] [debug] Writing virtual memory chunk: virt=0x10db0, size=0x10, process=0
[2025-07-06 01:50:10.827] [info] Translating virtual to physical: virt=0x10db0, process=0
[2025-07-06 01:50:10.827] [debug] Marked page dirty: virt=0x10db0
[2025-07-06 01:50:10.827] [debug] Wrote virtual memory chunk: virt=0x10db0, size=0x10
[2025-07-06 01:50:10.827] [debug] Writing virtual memory chunk: virt=0x11dc0, size=0x1, process=0
[2025-07-06 01:50:10.827] [info] Translating virtual to physical: virt=0x11dc0, process=0
[2025-07-06 01:50:10.827] [debug] Marked page dirty: virt=0x11dc0
[2025-07-06 01:50:10.827] [debug] Wrote virtual memory chunk: virt=0x11dc0, size=0x1
[2025-07-06 01:50:10.828] [debug] Writing virtual memory chunk: virt=0x10dc0, size=0x10, process=0
[2025-07-06 01:50:10.828] [info] Translating virtual to physical: virt=0x10dc0, process=0
[2025-07-06 01:50:10.828] [debug] Marked page dirty: virt=0x10dc0
[2025-07-06 01:50:10.828] [debug] Wrote virtual memory chunk: virt=0x10dc0, size=0x10
[2025-07-06 01:50:10.828] [debug] Writing virtual memory chunk: virt=0x11dd0, size=0x1, process=0
[2025-07-06 01:50:10.828] [info] Translating virtual to physical: virt=0x11dd0, process=0
[2025-07-06 01:50:10.828] [debug] Marked page dirty: virt=0x11dd0
[2025-07-06 01:50:10.828] [debug] Wrote virtual memory chunk: virt=0x11dd0, size=0x1
[2025-07-06 01:50:10.828] [debug] Writing virtual memory chunk: virt=0x10dd0, size=0x10, process=0
[2025-07-06 01:50:10.828] [info] Translating virtual to physical: virt=0x10dd0, process=0
[2025-07-06 01:50:10.828] [debug] Marked page dirty: virt=0x10dd0
[2025-07-06 01:50:10.828] [debug] Wrote virtual memory chunk: virt=0x10dd0, size=0x10
[2025-07-06 01:50:10.828] [debug] Writing virtual memory chunk: virt=0x11de0, size=0x1, process=0
[2025-07-06 01:50:10.828] [info] Translating virtual to physical: virt=0x11de0, process=0
[2025-07-06 01:50:10.828] [debug] Marked page dirty: virt=0x11de0
[2025-07-06 01:50:10.828] [debug] Wrote virtual memory chunk: virt=0x11de0, size=0x1
[2025-07-06 01:50:10.829] [debug] Writing virtual memory chunk: virt=0x10de0, size=0x10, process=0
[2025-07-06 01:50:10.829] [info] Translating virtual to physical: virt=0x10de0, process=0
[2025-07-06 01:50:10.829] [debug] Marked page dirty: virt=0x10de0
[2025-07-06 01:50:10.829] [debug] Wrote virtual memory chunk: virt=0x10de0, size=0x10
[2025-07-06 01:50:10.829] [debug] Writing virtual memory chunk: virt=0x11df0, size=0x1, process=0
[2025-07-06 01:50:10.829] [info] Translating virtual to physical: virt=0x11df0, process=0
[2025-07-06 01:50:10.829] [debug] Marked page dirty: virt=0x11df0
[2025-07-06 01:50:10.829] [debug] Wrote virtual memory chunk: virt=0x11df0, size=0x1
[2025-07-06 01:50:10.829] [debug] Writing virtual memory chunk: virt=0x10df0, size=0x10, process=0
[2025-07-06 01:50:10.829] [info] Translating virtual to physical: virt=0x10df0, process=0
[2025-07-06 01:50:10.829] [debug] Marked page dirty: virt=0x10df0
[2025-07-06 01:50:10.829] [debug] Wrote virtual memory chunk: virt=0x10df0, size=0x10
[2025-07-06 01:50:10.829] [debug] Writing virtual memory chunk: virt=0x11e00, size=0x1, process=0
[2025-07-06 01:50:10.829] [info] Translating virtual to physical: virt=0x11e00, process=0
[2025-07-06 01:50:10.829] [debug] Marked page dirty: virt=0x11e00
[2025-07-06 01:50:10.829] [debug] Wrote virtual memory chunk: virt=0x11e00, size=0x1
[2025-07-06 01:50:10.829] [debug] Writing virtual memory chunk: virt=0x10e00, size=0x10, process=0
[2025-07-06 01:50:10.830] [info] Translating virtual to physical: virt=0x10e00, process=0
[2025-07-06 01:50:10.830] [debug] Marked page dirty: virt=0x10e00
[2025-07-06 01:50:10.830] [debug] Wrote virtual memory chunk: virt=0x10e00, size=0x10
[2025-07-06 01:50:10.830] [debug] Writing virtual memory chunk: virt=0x11e10, size=0x1, process=0
[2025-07-06 01:50:10.830] [info] Translating virtual to physical: virt=0x11e10, process=0
[2025-07-06 01:50:10.830] [debug] Marked page dirty: virt=0x11e10
[2025-07-06 01:50:10.830] [debug] Wrote virtual memory chunk: virt=0x11e10, size=0x1
[2025-07-06 01:50:10.830] [debug] Writing virtual memory chunk: virt=0x10e10, size=0x10, process=0
[2025-07-06 01:50:10.830] [info] Translating virtual to physical: virt=0x10e10, process=0
[2025-07-06 01:50:10.830] [debug] Marked page dirty: virt=0x10e10
[2025-07-06 01:50:10.830] [debug] Wrote virtual memory chunk: virt=0x10e10, size=0x10
[2025-07-06 01:50:10.830] [debug] Writing virtual memory chunk: virt=0x11e20, size=0x1, process=0
[2025-07-06 01:50:10.830] [info] Translating virtual to physical: virt=0x11e20, process=0
[2025-07-06 01:50:10.830] [debug] Marked page dirty: virt=0x11e20
[2025-07-06 01:50:10.830] [debug] Wrote virtual memory chunk: virt=0x11e20, size=0x1
[2025-07-06 01:50:10.831] [debug] Writing virtual memory chunk: virt=0x10e20, size=0x10, process=0
[2025-07-06 01:50:10.831] [info] Translating virtual to physical: virt=0x10e20, process=0
[2025-07-06 01:50:10.831] [debug] Marked page dirty: virt=0x10e20
[2025-07-06 01:50:10.831] [debug] Wrote virtual memory chunk: virt=0x10e20, size=0x10
[2025-07-06 01:50:10.831] [debug] Writing virtual memory chunk: virt=0x11e30, size=0x1, process=0
[2025-07-06 01:50:10.831] [info] Translating virtual to physical: virt=0x11e30, process=0
[2025-07-06 01:50:10.831] [debug] Marked page dirty: virt=0x11e30
[2025-07-06 01:50:10.831] [debug] Wrote virtual memory chunk: virt=0x11e30, size=0x1
[2025-07-06 01:50:10.831] [debug] Writing virtual memory chunk: virt=0x10e30, size=0x10, process=0
[2025-07-06 01:50:10.831] [info] Translating virtual to physical: virt=0x10e30, process=0
[2025-07-06 01:50:10.831] [debug] Marked page dirty: virt=0x10e30
[2025-07-06 01:50:10.831] [debug] Wrote virtual memory chunk: virt=0x10e30, size=0x10
[2025-07-06 01:50:10.831] [debug] Writing virtual memory chunk: virt=0x11e40, size=0x1, process=0
[2025-07-06 01:50:10.831] [info] Translating virtual to physical: virt=0x11e40, process=0
[2025-07-06 01:50:10.831] [debug] Marked page dirty: virt=0x11e40
[2025-07-06 01:50:10.831] [debug] Wrote virtual memory chunk: virt=0x11e40, size=0x1
[2025-07-06 01:50:10.831] [debug] Writing virtual memory chunk: virt=0x10e40, size=0x10, process=0
[2025-07-06 01:50:10.832] [info] Translating virtual to physical: virt=0x10e40, process=0
[2025-07-06 01:50:10.832] [debug] Marked page dirty: virt=0x10e40
[2025-07-06 01:50:10.832] [debug] Wrote virtual memory chunk: virt=0x10e40, size=0x10
[2025-07-06 01:50:10.832] [debug] Writing virtual memory chunk: virt=0x11e50, size=0x1, process=0
[2025-07-06 01:50:10.832] [info] Translating virtual to physical: virt=0x11e50, process=0
[2025-07-06 01:50:10.832] [debug] Marked page dirty: virt=0x11e50
[2025-07-06 01:50:10.832] [debug] Wrote virtual memory chunk: virt=0x11e50, size=0x1
[2025-07-06 01:50:10.832] [debug] Writing virtual memory chunk: virt=0x10e50, size=0x10, process=0
[2025-07-06 01:50:10.832] [info] Translating virtual to physical: virt=0x10e50, process=0
[2025-07-06 01:50:10.832] [debug] Marked page dirty: virt=0x10e50
[2025-07-06 01:50:10.832] [debug] Wrote virtual memory chunk: virt=0x10e50, size=0x10
[2025-07-06 01:50:10.832] [debug] Writing virtual memory chunk: virt=0x11e60, size=0x1, process=0
[2025-07-06 01:50:10.832] [info] Translating virtual to physical: virt=0x11e60, process=0
[2025-07-06 01:50:10.832] [debug] Marked page dirty: virt=0x11e60
[2025-07-06 01:50:10.832] [debug] Wrote virtual memory chunk: virt=0x11e60, size=0x1
[2025-07-06 01:50:10.832] [debug] Writing virtual memory chunk: virt=0x10e60, size=0x10, process=0
[2025-07-06 01:50:10.833] [info] Translating virtual to physical: virt=0x10e60, process=0
[2025-07-06 01:50:10.833] [debug] Marked page dirty: virt=0x10e60
[2025-07-06 01:50:10.833] [debug] Wrote virtual memory chunk: virt=0x10e60, size=0x10
[2025-07-06 01:50:10.833] [debug] Writing virtual memory chunk: virt=0x11e70, size=0x1, process=0
[2025-07-06 01:50:10.833] [info] Translating virtual to physical: virt=0x11e70, process=0
[2025-07-06 01:50:10.833] [debug] Marked page dirty: virt=0x11e70
[2025-07-06 01:50:10.833] [debug] Wrote virtual memory chunk: virt=0x11e70, size=0x1
[2025-07-06 01:50:10.833] [debug] Writing virtual memory chunk: virt=0x10e70, size=0x10, process=0
[2025-07-06 01:50:10.833] [info] Translating virtual to physical: virt=0x10e70, process=0
[2025-07-06 01:50:10.833] [debug] Marked page dirty: virt=0x10e70
[2025-07-06 01:50:10.833] [debug] Wrote virtual memory chunk: virt=0x10e70, size=0x10
[2025-07-06 01:50:10.833] [debug] Writing virtual memory chunk: virt=0x11e80, size=0x1, process=0
[2025-07-06 01:50:10.833] [info] Translating virtual to physical: virt=0x11e80, process=0
[2025-07-06 01:50:10.833] [debug] Marked page dirty: virt=0x11e80
[2025-07-06 01:50:10.834] [debug] Wrote virtual memory chunk: virt=0x11e80, size=0x1
[2025-07-06 01:50:10.834] [debug] Writing virtual memory chunk: virt=0x10e80, size=0x10, process=0
[2025-07-06 01:50:10.834] [info] Translating virtual to physical: virt=0x10e80, process=0
[2025-07-06 01:50:10.834] [debug] Marked page dirty: virt=0x10e80
[2025-07-06 01:50:10.834] [debug] Wrote virtual memory chunk: virt=0x10e80, size=0x10
[2025-07-06 01:50:10.834] [debug] Writing virtual memory chunk: virt=0x11e90, size=0x1, process=0
[2025-07-06 01:50:10.834] [info] Translating virtual to physical: virt=0x11e90, process=0
[2025-07-06 01:50:10.834] [debug] Marked page dirty: virt=0x11e90
[2025-07-06 01:50:10.834] [debug] Wrote virtual memory chunk: virt=0x11e90, size=0x1
[2025-07-06 01:50:10.834] [debug] Writing virtual memory chunk: virt=0x10e90, size=0x10, process=0
[2025-07-06 01:50:10.834] [info] Translating virtual to physical: virt=0x10e90, process=0
[2025-07-06 01:50:10.834] [debug] Marked page dirty: virt=0x10e90
[2025-07-06 01:50:10.834] [debug] Wrote virtual memory chunk: virt=0x10e90, size=0x10
[2025-07-06 01:50:10.834] [debug] Writing virtual memory chunk: virt=0x11ea0, size=0x1, process=0
[2025-07-06 01:50:10.834] [info] Translating virtual to physical: virt=0x11ea0, process=0
[2025-07-06 01:50:10.835] [debug] Marked page dirty: virt=0x11ea0
[2025-07-06 01:50:10.835] [debug] Wrote virtual memory chunk: virt=0x11ea0, size=0x1
[2025-07-06 01:50:10.835] [debug] Writing virtual memory chunk: virt=0x10ea0, size=0x10, process=0
[2025-07-06 01:50:10.835] [info] Translating virtual to physical: virt=0x10ea0, process=0
[2025-07-06 01:50:10.835] [debug] Marked page dirty: virt=0x10ea0
[2025-07-06 01:50:10.835] [debug] Wrote virtual memory chunk: virt=0x10ea0, size=0x10
[2025-07-06 01:50:10.835] [debug] Writing virtual memory chunk: virt=0x11eb0, size=0x1, process=0
[2025-07-06 01:50:10.835] [info] Translating virtual to physical: virt=0x11eb0, process=0
[2025-07-06 01:50:10.835] [debug] Marked page dirty: virt=0x11eb0
[2025-07-06 01:50:10.835] [debug] Wrote virtual memory chunk: virt=0x11eb0, size=0x1
[2025-07-06 01:50:10.835] [debug] Writing virtual memory chunk: virt=0x10eb0, size=0x10, process=0
[2025-07-06 01:50:10.835] [info] Translating virtual to physical: virt=0x10eb0, process=0
[2025-07-06 01:50:10.835] [debug] Marked page dirty: virt=0x10eb0
[2025-07-06 01:50:10.835] [debug] Wrote virtual memory chunk: virt=0x10eb0, size=0x10
[2025-07-06 01:50:10.836] [debug] Writing virtual memory chunk: virt=0x11ec0, size=0x1, process=0
[2025-07-06 01:50:10.836] [info] Translating virtual to physical: virt=0x11ec0, process=0
[2025-07-06 01:50:10.836] [debug] Marked page dirty: virt=0x11ec0
[2025-07-06 01:50:10.836] [debug] Wrote virtual memory chunk: virt=0x11ec0, size=0x1
[2025-07-06 01:50:10.836] [debug] Writing virtual memory chunk: virt=0x10ec0, size=0x10, process=0
[2025-07-06 01:50:10.836] [info] Translating virtual to physical: virt=0x10ec0, process=0
[2025-07-06 01:50:10.836] [debug] Marked page dirty: virt=0x10ec0
[2025-07-06 01:50:10.836] [debug] Wrote virtual memory chunk: virt=0x10ec0, size=0x10
[2025-07-06 01:50:10.836] [debug] Writing virtual memory chunk: virt=0x11ed0, size=0x1, process=0
[2025-07-06 01:50:10.836] [info] Translating virtual to physical: virt=0x11ed0, process=0
[2025-07-06 01:50:10.836] [debug] Marked page dirty: virt=0x11ed0
[2025-07-06 01:50:10.836] [debug] Wrote virtual memory chunk: virt=0x11ed0, size=0x1
[2025-07-06 01:50:10.836] [debug] Writing virtual memory chunk: virt=0x10ed0, size=0x10, process=0
[2025-07-06 01:50:10.836] [info] Translating virtual to physical: virt=0x10ed0, process=0
[2025-07-06 01:50:10.836] [debug] Marked page dirty: virt=0x10ed0
[2025-07-06 01:50:10.836] [debug] Wrote virtual memory chunk: virt=0x10ed0, size=0x10
[2025-07-06 01:50:10.837] [debug] Writing virtual memory chunk: virt=0x11ee0, size=0x1, process=0
[2025-07-06 01:50:10.837] [info] Translating virtual to physical: virt=0x11ee0, process=0
[2025-07-06 01:50:10.837] [debug] Marked page dirty: virt=0x11ee0
[2025-07-06 01:50:10.837] [debug] Wrote virtual memory chunk: virt=0x11ee0, size=0x1
[2025-07-06 01:50:10.837] [debug] Writing virtual memory chunk: virt=0x10ee0, size=0x10, process=0
[2025-07-06 01:50:10.837] [info] Translating virtual to physical: virt=0x10ee0, process=0
[2025-07-06 01:50:10.837] [debug] Marked page dirty: virt=0x10ee0
[2025-07-06 01:50:10.837] [debug] Wrote virtual memory chunk: virt=0x10ee0, size=0x10
[2025-07-06 01:50:10.837] [debug] Writing virtual memory chunk: virt=0x11ef0, size=0x1, process=0
[2025-07-06 01:50:10.837] [info] Translating virtual to physical: virt=0x11ef0, process=0
[2025-07-06 01:50:10.837] [debug] Marked page dirty: virt=0x11ef0
[2025-07-06 01:50:10.837] [debug] Wrote virtual memory chunk: virt=0x11ef0, size=0x1
[2025-07-06 01:50:10.837] [debug] Writing virtual memory chunk: virt=0x10ef0, size=0x10, process=0
[2025-07-06 01:50:10.837] [info] Translating virtual to physical: virt=0x10ef0, process=0
[2025-07-06 01:50:10.837] [debug] Marked page dirty: virt=0x10ef0
[2025-07-06 01:50:10.837] [debug] Wrote virtual memory chunk: virt=0x10ef0, size=0x10
[2025-07-06 01:50:10.838] [debug] Writing virtual memory chunk: virt=0x11f00, size=0x1, process=0
[2025-07-06 01:50:10.838] [info] Translating virtual to physical: virt=0x11f00, process=0
[2025-07-06 01:50:10.838] [debug] Marked page dirty: virt=0x11f00
[2025-07-06 01:50:10.838] [debug] Wrote virtual memory chunk: virt=0x11f00, size=0x1
[2025-07-06 01:50:10.838] [debug] Writing virtual memory chunk: virt=0x10f00, size=0x10, process=0
[2025-07-06 01:50:10.838] [info] Translating virtual to physical: virt=0x10f00, process=0
[2025-07-06 01:50:10.838] [debug] Marked page dirty: virt=0x10f00
[2025-07-06 01:50:10.838] [debug] Wrote virtual memory chunk: virt=0x10f00, size=0x10
[2025-07-06 01:50:10.838] [debug] Writing virtual memory chunk: virt=0x11f10, size=0x1, process=0
[2025-07-06 01:50:10.838] [info] Translating virtual to physical: virt=0x11f10, process=0
[2025-07-06 01:50:10.838] [debug] Marked page dirty: virt=0x11f10
[2025-07-06 01:50:10.838] [debug] Wrote virtual memory chunk: virt=0x11f10, size=0x1
[2025-07-06 01:50:10.838] [debug] Writing virtual memory chunk: virt=0x10f10, size=0x10, process=0
[2025-07-06 01:50:10.838] [info] Translating virtual to physical: virt=0x10f10, process=0
[2025-07-06 01:50:10.838] [debug] Marked page dirty: virt=0x10f10
[2025-07-06 01:50:10.838] [debug] Wrote virtual memory chunk: virt=0x10f10, size=0x10
[2025-07-06 01:50:10.839] [debug] Writing virtual memory chunk: virt=0x11f20, size=0x1, process=0
[2025-07-06 01:50:10.839] [info] Translating virtual to physical: virt=0x11f20, process=0
[2025-07-06 01:50:10.839] [debug] Marked page dirty: virt=0x11f20
[2025-07-06 01:50:10.839] [debug] Wrote virtual memory chunk: virt=0x11f20, size=0x1
[2025-07-06 01:50:10.839] [debug] Writing virtual memory chunk: virt=0x10f20, size=0x10, process=0
[2025-07-06 01:50:10.839] [info] Translating virtual to physical: virt=0x10f20, process=0
[2025-07-06 01:50:10.839] [debug] Marked page dirty: virt=0x10f20
[2025-07-06 01:50:10.839] [debug] Wrote virtual memory chunk: virt=0x10f20, size=0x10
[2025-07-06 01:50:10.839] [debug] Writing virtual memory chunk: virt=0x11f30, size=0x1, process=0
[2025-07-06 01:50:10.839] [info] Translating virtual to physical: virt=0x11f30, process=0
[2025-07-06 01:50:10.839] [debug] Marked page dirty: virt=0x11f30
[2025-07-06 01:50:10.839] [debug] Wrote virtual memory chunk: virt=0x11f30, size=0x1
[2025-07-06 01:50:10.839] [debug] Writing virtual memory chunk: virt=0x10f30, size=0x10, process=0
[2025-07-06 01:50:10.839] [info] Translating virtual to physical: virt=0x10f30, process=0
[2025-07-06 01:50:10.839] [debug] Marked page dirty: virt=0x10f30
[2025-07-06 01:50:10.840] [debug] Wrote virtual memory chunk: virt=0x10f30, size=0x10
[2025-07-06 01:50:10.840] [debug] Writing virtual memory chunk: virt=0x11f40, size=0x1, process=0
[2025-07-06 01:50:10.840] [info] Translating virtual to physical: virt=0x11f40, process=0
[2025-07-06 01:50:10.840] [debug] Marked page dirty: virt=0x11f40
[2025-07-06 01:50:10.840] [debug] Wrote virtual memory chunk: virt=0x11f40, size=0x1
[2025-07-06 01:50:10.840] [debug] Writing virtual memory chunk: virt=0x10f40, size=0x10, process=0
[2025-07-06 01:50:10.840] [info] Translating virtual to physical: virt=0x10f40, process=0
[2025-07-06 01:50:10.840] [debug] Marked page dirty: virt=0x10f40
[2025-07-06 01:50:10.840] [debug] Wrote virtual memory chunk: virt=0x10f40, size=0x10
[2025-07-06 01:50:10.840] [debug] Writing virtual memory chunk: virt=0x11f50, size=0x1, process=0
[2025-07-06 01:50:10.840] [info] Translating virtual to physical: virt=0x11f50, process=0
[2025-07-06 01:50:10.840] [debug] Marked page dirty: virt=0x11f50
[2025-07-06 01:50:10.840] [debug] Wrote virtual memory chunk: virt=0x11f50, size=0x1
[2025-07-06 01:50:10.840] [debug] Writing virtual memory chunk: virt=0x10f50, size=0x10, process=0
[2025-07-06 01:50:10.840] [info] Translating virtual to physical: virt=0x10f50, process=0
[2025-07-06 01:50:10.840] [debug] Marked page dirty: virt=0x10f50
[2025-07-06 01:50:10.840] [debug] Wrote virtual memory chunk: virt=0x10f50, size=0x10
[2025-07-06 01:50:10.841] [debug] Writing virtual memory chunk: virt=0x11f60, size=0x1, process=0
[2025-07-06 01:50:10.841] [info] Translating virtual to physical: virt=0x11f60, process=0
[2025-07-06 01:50:10.841] [debug] Marked page dirty: virt=0x11f60
[2025-07-06 01:50:10.841] [debug] Wrote virtual memory chunk: virt=0x11f60, size=0x1
[2025-07-06 01:50:10.843] [debug] Writing virtual memory chunk: virt=0x10f60, size=0x10, process=0
[2025-07-06 01:50:10.843] [info] Translating virtual to physical: virt=0x10f60, process=0
[2025-07-06 01:50:10.843] [debug] Marked page dirty: virt=0x10f60
[2025-07-06 01:50:10.843] [debug] Wrote virtual memory chunk: virt=0x10f60, size=0x10
[2025-07-06 01:50:10.843] [debug] Writing virtual memory chunk: virt=0x11f70, size=0x1, process=0
[2025-07-06 01:50:10.843] [info] Translating virtual to physical: virt=0x11f70, process=0
[2025-07-06 01:50:10.843] [debug] Marked page dirty: virt=0x11f70
[2025-07-06 01:50:10.844] [debug] Wrote virtual memory chunk: virt=0x11f70, size=0x1
[2025-07-06 01:50:10.844] [debug] Writing virtual memory chunk: virt=0x10f70, size=0x10, process=0
[2025-07-06 01:50:10.844] [info] Translating virtual to physical: virt=0x10f70, process=0
[2025-07-06 01:50:10.844] [debug] Marked page dirty: virt=0x10f70
[2025-07-06 01:50:10.844] [debug] Wrote virtual memory chunk: virt=0x10f70, size=0x10
[2025-07-06 01:50:10.844] [debug] Writing virtual memory chunk: virt=0x11f80, size=0x1, process=0
[2025-07-06 01:50:10.844] [info] Translating virtual to physical: virt=0x11f80, process=0
[2025-07-06 01:50:10.844] [debug] Marked page dirty: virt=0x11f80
[2025-07-06 01:50:10.844] [debug] Wrote virtual memory chunk: virt=0x11f80, size=0x1
[2025-07-06 01:50:10.845] [debug] Writing virtual memory chunk: virt=0x10f80, size=0x10, process=0
[2025-07-06 01:50:10.845] [info] Translating virtual to physical: virt=0x10f80, process=0
[2025-07-06 01:50:10.845] [debug] Marked page dirty: virt=0x10f80
[2025-07-06 01:50:10.845] [debug] Wrote virtual memory chunk: virt=0x10f80, size=0x10
[2025-07-06 01:50:10.845] [debug] Writing virtual memory chunk: virt=0x11f90, size=0x1, process=0
[2025-07-06 01:50:10.845] [info] Translating virtual to physical: virt=0x11f90, process=0
[2025-07-06 01:50:10.845] [debug] Marked page dirty: virt=0x11f90
[2025-07-06 01:50:10.845] [debug] Wrote virtual memory chunk: virt=0x11f90, size=0x1
[2025-07-06 01:50:10.845] [debug] Writing virtual memory chunk: virt=0x10f90, size=0x10, process=0
[2025-07-06 01:50:10.845] [info] Translating virtual to physical: virt=0x10f90, process=0
[2025-07-06 01:50:10.845] [debug] Marked page dirty: virt=0x10f90
[2025-07-06 01:50:10.845] [debug] Wrote virtual memory chunk: virt=0x10f90, size=0x10
[2025-07-06 01:50:10.845] [debug] Writing virtual memory chunk: virt=0x11fa0, size=0x1, process=0
[2025-07-06 01:50:10.845] [info] Translating virtual to physical: virt=0x11fa0, process=0
[2025-07-06 01:50:10.846] [debug] Marked page dirty: virt=0x11fa0
[2025-07-06 01:50:10.846] [debug] Wrote virtual memory chunk: virt=0x11fa0, size=0x1
[2025-07-06 01:50:10.846] [debug] Writing virtual memory chunk: virt=0x10fa0, size=0x10, process=0
[2025-07-06 01:50:10.846] [info] Translating virtual to physical: virt=0x10fa0, process=0
[2025-07-06 01:50:10.846] [debug] Marked page dirty: virt=0x10fa0
[2025-07-06 01:50:10.846] [debug] Wrote virtual memory chunk: virt=0x10fa0, size=0x10
[2025-07-06 01:50:10.846] [info] Processed descriptor 250
[2025-07-06 01:50:10.846] [debug] Writing virtual memory chunk: virt=0x11fb0, size=0x1, process=0
[2025-07-06 01:50:10.846] [info] Translating virtual to physical: virt=0x11fb0, process=0
[2025-07-06 01:50:10.846] [debug] Marked page dirty: virt=0x11fb0
[2025-07-06 01:50:10.847] [debug] Wrote virtual memory chunk: virt=0x11fb0, size=0x1
[2025-07-06 01:50:10.847] [debug] Writing virtual memory chunk: virt=0x10fb0, size=0x10, process=0
[2025-07-06 01:50:10.847] [info] Translating virtual to physical: virt=0x10fb0, process=0
[2025-07-06 01:50:10.847] [debug] Marked page dirty: virt=0x10fb0
[2025-07-06 01:50:10.847] [debug] Wrote virtual memory chunk: virt=0x10fb0, size=0x10
[2025-07-06 01:50:10.847] [debug] Writing virtual memory chunk: virt=0x11fc0, size=0x1, process=0
[2025-07-06 01:50:10.847] [info] Translating virtual to physical: virt=0x11fc0, process=0
[2025-07-06 01:50:10.847] [debug] Marked page dirty: virt=0x11fc0
[2025-07-06 01:50:10.847] [debug] Wrote virtual memory chunk: virt=0x11fc0, size=0x1
[2025-07-06 01:50:10.847] [debug] Writing virtual memory chunk: virt=0x10fc0, size=0x10, process=0
[2025-07-06 01:50:10.848] [info] Translating virtual to physical: virt=0x10fc0, process=0
[2025-07-06 01:50:10.848] [debug] Marked page dirty: virt=0x10fc0
[2025-07-06 01:50:10.848] [debug] Wrote virtual memory chunk: virt=0x10fc0, size=0x10
[2025-07-06 01:50:10.848] [debug] Writing virtual memory chunk: virt=0x11fd0, size=0x1, process=0
[2025-07-06 01:50:10.848] [info] Translating virtual to physical: virt=0x11fd0, process=0
[2025-07-06 01:50:10.848] [debug] Marked page dirty: virt=0x11fd0
[2025-07-06 01:50:10.848] [debug] Wrote virtual memory chunk: virt=0x11fd0, size=0x1
[2025-07-06 01:50:10.848] [debug] Writing virtual memory chunk: virt=0x10fd0, size=0x10, process=0
[2025-07-06 01:50:10.848] [info] Translating virtual to physical: virt=0x10fd0, process=0
[2025-07-06 01:50:10.848] [debug] Marked page dirty: virt=0x10fd0
[2025-07-06 01:50:10.848] [debug] Wrote virtual memory chunk: virt=0x10fd0, size=0x10
[2025-07-06 01:50:10.849] [debug] Writing virtual memory chunk: virt=0x11fe0, size=0x1, process=0
[2025-07-06 01:50:10.849] [info] Translating virtual to physical: virt=0x11fe0, process=0
[2025-07-06 01:50:10.849] [debug] Marked page dirty: virt=0x11fe0
[2025-07-06 01:50:10.849] [debug] Wrote virtual memory chunk: virt=0x11fe0, size=0x1
[2025-07-06 01:50:10.849] [debug] Writing virtual memory chunk: virt=0x10fe0, size=0x10, process=0
[2025-07-06 01:50:10.849] [info] Translating virtual to physical: virt=0x10fe0, process=0
[2025-07-06 01:50:10.849] [debug] Marked page dirty: virt=0x10fe0
[2025-07-06 01:50:10.849] [debug] Wrote virtual memory chunk: virt=0x10fe0, size=0x10
[2025-07-06 01:50:10.849] [debug] Writing virtual memory chunk: virt=0x11ff0, size=0x1, process=0
[2025-07-06 01:50:10.849] [info] Translating virtual to physical: virt=0x11ff0, process=0
[2025-07-06 01:50:10.849] [debug] Marked page dirty: virt=0x11ff0
[2025-07-06 01:50:10.849] [debug] Wrote virtual memory chunk: virt=0x11ff0, size=0x1
[2025-07-06 01:50:10.849] [debug] Writing virtual memory chunk: virt=0x10ff0, size=0x10, process=0
[2025-07-06 01:50:10.849] [info] Translating virtual to physical: virt=0x10ff0, process=0
[2025-07-06 01:50:10.850] [debug] Marked page dirty: virt=0x10ff0
[2025-07-06 01:50:10.850] [debug] Wrote virtual memory chunk: virt=0x10ff0, size=0x10
[2025-07-06 01:50:10.850] [info] IDT descriptors set
[2025-07-06 01:50:10.850] [info] Setting up IDT descriptor
[2025-07-06 01:50:10.850] [info] IDT descriptor set: base=0x10000, limit=0xfff
[2025-07-06 01:50:10.850] [info] Custom handlers registered
[2025-07-06 01:50:10.850] [info] Loading IDT
[2025-07-06 01:50:10.850] [info] Attempting to load IDT...
[2025-07-06 01:50:10.850] [warning] LoadIDT not implemented for MSVC; simulating IDT load
[2025-07-06 01:50:10.850] [info] X86_64CPU[0]: Set IDTR: base=0x10000, limit=0xfff
[2025-07-06 01:50:10.850] [info] LoadIDT completed
[2025-07-06 01:50:10.851] [info] IDT loaded successfully
[2025-07-06 01:50:10.851] [info] InterruptHandler initialization completed
[2025-07-06 01:50:10.858] [info] InterruptHandler initialized successfully in 156ms
[2025-07-06 01:50:10.858] [info] Step 8/10: Finalizing emulator state...
[2025-07-06 01:50:10.858] [info] Memory diagnostics reset
[2025-07-06 01:50:10.858] [info] CPU diagnostics reset
[2025-07-06 01:50:10.859] [info] CPUDiagnostics initialized
[2025-07-06 01:50:10.859] [info] CPU diagnostics reset
[2025-07-06 01:50:10.859] [info] JIT diagnostics reset
[2025-07-06 01:50:10.859] [info] JITDiagnostics initialized
[2025-07-06 01:50:10.859] [info] JIT diagnostics reset
[2025-07-06 01:50:10.859] [info] === PS4 Emulator Initialization Complete ===
[2025-07-06 01:50:10.859] [info] Total initialization time: 679ms (679732us)
[2025-07-06 01:50:10.867] [info] PS4 emulator initialized successfully
[2025-07-06 01:50:10.867] [info] InputManager constructed
[2025-07-06 01:50:10.867] [info] InputManager initialized
[2025-07-06 01:50:10.867] [error] Failed to list games in directory ./ps4_root/app0: directory_iterator::directory_iterator: The system cannot find the path specified.: "./ps4_root/app0"
[2025-07-06 01:50:10.872] [info] AudioDevice started
[2025-07-06 01:50:10.872] [info] Applied audio settings: enabled=true, master_volume=1, underrun_warning_interval=5s
[2025-07-06 01:50:10.873] [info] Applied CPU settings: threads=12, jit_enabled=true, simd_optimizations=true, branch_prediction=true, cache_size_mb=256
[2025-07-06 01:50:10.873] [warning] CPU settings like thread count, JIT, SIMD, branch prediction usually require emulator restart to take full effect.
[2025-07-06 01:50:10.873] [info] Applied GPU settings: backend=Vulkan, resolution_scale=4, anisotropic_filtering=true, anti_aliasing=true
[2025-07-06 01:50:10.873] [warning] Changing GPU backend usually requires emulator restart.
[2025-07-06 01:50:10.874] [info] Applied memory settings: size_gb=10, compression=true, swap_size_gb=6
[2025-07-06 01:50:10.874] [warning] Memory settings usually require emulator restart to take full effect.
[2025-07-06 01:50:10.874] [info] Applied filesystem settings: game_directory=C:/Users/<USER>/Downloads/ps4_root/, auto_mount_games=true
[2025-07-06 01:50:10.874] [info] Game directory set to: C:/Users/<USER>/Downloads/ps4_root/
[2025-07-06 01:50:10.874] [info] Applied debug settings: debug_mode=true, log_syscalls=true, log_gpu_commands=true, log_level=debug
[2025-07-06 01:50:10.874] [info] Applied input settings: controller_enabled=true, keyboard_enabled=true, mouse_enabled=true, deadzone=0.1
[2025-07-06 01:50:10.874] [info] Applied network settings: enabled=false, interface=auto, port=9302
[2025-07-06 01:50:10.874] [info] Applied compatibility settings: strict_mode=true, ignore_missing_imports=false, patch_games=false
[2025-07-06 01:50:10.874] [info] Applied advanced settings: emulation_speed=1, auto_save_states=true, auto_save_interval=300
[2025-07-06 01:50:10.874] [info] Controller input thread started.
[2025-07-06 01:50:14.651] [warning] Stop: Emulator not running
[2025-07-06 01:50:14.651] [info] Flushing entire pipeline
[2025-07-06 01:50:14.651] [info] X86_64CPU[0] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.651] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[1] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[2] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[3] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[4] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[5] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[6] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.652] [info] Flushing entire pipeline
[2025-07-06 01:50:14.652] [info] X86_64CPU[7] state reset to defaults: RIP=0x1000
[2025-07-06 01:50:14.653] [info] Memory diagnostics reset
[2025-07-06 01:50:14.653] [info] CPU diagnostics reset
[2025-07-06 01:50:14.653] [info] JIT diagnostics reset
[2025-07-06 01:50:14.653] [info] Emulator reset completed
[2025-07-06 01:50:15.386] [warning] Audio buffer underrun, count=850, queue_size=0, time_since_last=5s
[2025-07-06 01:50:20.396] [warning] Audio buffer underrun, count=1790, queue_size=0, time_since_last=5s
[2025-07-06 01:50:25.398] [warning] Audio buffer underrun, count=2727, queue_size=0, time_since_last=5s
[2025-07-06 01:50:30.403] [warning] Audio buffer underrun, count=3667, queue_size=0, time_since_last=5s
[2025-07-06 01:50:35.404] [warning] Audio buffer underrun, count=4604, queue_size=0, time_since_last=5s
[2025-07-06 01:50:40.407] [warning] Audio buffer underrun, count=5542, queue_size=0, time_since_last=5s
[2025-07-06 01:50:41.451] [info] LoadGame: Loading ELF/BIN file: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin
[2025-07-06 01:50:41.451] [info] ElfLoader initialized
[2025-07-06 01:50:41.452] [info] Loading ELF/SELF: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin, process ID: 1, shared: false
[2025-07-06 01:50:41.452] [debug] MapToHostPath: Starting mapping for 'D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin'
[2025-07-06 01:50:41.452] [debug] MapToHostPath: virtualPath='D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin', mountPoint result=''
[2025-07-06 01:50:41.452] [debug] MapToHostPath: using fallback, result='D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin'
[2025-07-06 01:50:41.452] [debug] File size: 5803753 bytes
[2025-07-06 01:50:41.459] [debug] Data appears to be a decrypted ELF file, not an encrypted SELF
[2025-07-06 01:50:41.460] [info] Detected decrypted ELF file: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin, no decryption needed
[2025-07-06 01:50:41.460] [debug] ELF header details: magic=0x7F454C46, class=0x02, data=0x01, version=0x01, machine=0x003E, type=0xFE00, entry=0x0000000000638580, phoff=0x0000000000000040, shoff=0x0000000000000000, phnum=10, shnum=0
[2025-07-06 01:50:41.460] [info] ELF type: 0xFE00 (Static)
[2025-07-06 01:50:41.460] [debug] LoadSegments: Starting segment loading for ELF type 0xFE00
[2025-07-06 01:50:41.460] [debug] LoadSegments: Reading 10 program headers from offset 0x0000000000000040
[2025-07-06 01:50:41.460] [debug] Program header 0: type=0x00000001, offset=0x0000000000004000, vaddr=0x0000000000400000, filesz=0x000000000041A8C0, memsz=0x000000000041A8C0, flags=0x00000005, align=0x0000000000004000
[2025-07-06 01:50:41.461] [debug] Program header 1: type=0x00000001, offset=0x0000000000420000, vaddr=0x000000000081C000, filesz=0x00000000000D9C90, memsz=0x00000000031EFA08, flags=0x00000006, align=0x0000000000004000
[2025-07-06 01:50:41.461] [debug] Program header 2: type=0x61000001, offset=0x0000000000420000, vaddr=0x000000000081C000, filesz=0x0000000000000040, memsz=0x0000000000000040, flags=0x00000006, align=0x0000000000000008
[2025-07-06 01:50:41.461] [debug] Program header 3: type=0x00000002, offset=0x0000000000587FD0, vaddr=0x0000000000000000, filesz=0x0000000000000940, memsz=0x0000000000000940, flags=0x00000006, align=0x0000000000000008
[2025-07-06 01:50:41.461] [debug] Program header 4: type=0x00000003, offset=0x0000000000004000, vaddr=0x0000000000400000, filesz=0x0000000000000015, memsz=0x0000000000000015, flags=0x00000004, align=0x0000000000000001
[2025-07-06 01:50:41.461] [debug] Program header 5: type=0x00000007, offset=0x0000000000000000, vaddr=0x0000000000000000, filesz=0x0000000000000000, memsz=0x0000000000000000, flags=0x00000004, align=0x0000000000000001
[2025-07-06 01:50:41.461] [debug] Program header 6: type=0x6474E550, offset=0x00000000003F03FC, vaddr=0x00000000007EC3FC, filesz=0x000000000002E4C4, memsz=0x000000000002E4C4, flags=0x00000004, align=0x0000000000000004
[2025-07-06 01:50:41.461] [debug] Program header 7: type=0x61000000, offset=0x00000000004F9C90, vaddr=0x0000000000000000, filesz=0x000000000008EC80, memsz=0x0000000000000000, flags=0x00000004, align=0x0000000000000010
[2025-07-06 01:50:41.461] [debug] Program header 8: type=0x6FFFFF00, offset=0x0000000000588910, vaddr=0x0000000000000000, filesz=0x0000000000000070, memsz=0x0000000000000000, flags=0x00000000, align=0x0000000000000010
[2025-07-06 01:50:41.461] [debug] Program header 9: type=0x6FFFFF01, offset=0x0000000000588980, vaddr=0x0000000000000000, filesz=0x0000000000000569, memsz=0x0000000000000000, flags=0x00000000, align=0x0000000000000010
[2025-07-06 01:50:41.461] [debug] LoadSegments: isDynamic=false, minVaddr=0x0000000000400000, maxVaddrEnd=0x0000000003A0BA08
[2025-07-06 01:50:41.461] [debug] Skipping SelfDecrypter for decrypted ELF
[2025-07-06 01:50:41.461] [debug] LoadSegments: Processing header 1 of 10, type=0x00000001
[2025-07-06 01:50:41.461] [info] LoadSegments: Processing LOAD segment 0: vaddr=0x0000000000400000, memsz=0x000000000041A8C0, filesz=0x000000000041A8C0
[2025-07-06 01:50:41.461] [info] LoadSegments: Step 1/4 - Allocating virtual memory for segment 0 (size=4MB, 4202KB, 1050 pages)
[2025-07-06 01:50:45.413] [warning] Audio buffer underrun, count=6481, queue_size=0, time_since_last=5s
[2025-07-06 01:50:50.415] [warning] Audio buffer underrun, count=7418, queue_size=0, time_since_last=5s
[2025-07-06 01:50:55.417] [warning] Audio buffer underrun, count=8356, queue_size=0, time_since_last=5s
[2025-07-06 01:51:00.427] [warning] Audio buffer underrun, count=9295, queue_size=0, time_since_last=5s
