// MultipleFiles/instruction_decoder.cpp
#include <algorithm>
#include <array>
#include <cstring>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "register.h"
#include <spdlog/spdlog.h>


namespace x86_64 {
struct DecodeException : std::runtime_error {
  explicit DecodeException(const std::string &msg) : std::runtime_error(msg) {}
};

InstructionDecoder::InstructionDecoder() {
  SPDLOG_INFO("InstructionDecoder constructed with lazy initialization");

  // Reserve space for opcode tables to improve performance
  singleByteOpcodes.reserve(256);
  twoByteOpcodes.reserve(256);
  threeByteOpcodes.reserve(512);
  vexOpcodes.reserve(1024);
  evexOpcodes.reserve(512);
  fpuOpcodes.reserve(512);
  groupOpcodes.reserve(256);

  // Initialize only the most common single-byte opcodes immediately
  // This ensures basic functionality while deferring expensive initialization
  EnsureSingleByteInitialized();

  SPDLOG_INFO("InstructionDecoder ready (using lazy initialization for advanced instruction sets)");
}

void InstructionDecoder::InitializeSingleByteOpcodes() {
  // 0x00-0x0F: Basic arithmetic and data movement
  AddOpcode(0x00, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x01, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x02, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x03, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x04, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x05, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x06, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH ES
  AddOpcode(0x07, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // POP ES
  AddOpcode(0x08, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x09, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x0A, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x0B, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x0C, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x0D, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x0E, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH CS
  // 0x0F is two-byte escape

  // 0x10-0x1F: ADC and SBB
  AddOpcode(0x10, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x11, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x12, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x13, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x14, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x15, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x16, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH SS
  AddOpcode(0x17, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // POP SS
  AddOpcode(0x18, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x19, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x1A, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x1B, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x1C, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x1D, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x1E, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH DS
  AddOpcode(0x1F, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::SEGMENT}); // POP DS

  // 0x20-0x2F: AND and SUB
  AddOpcode(0x20, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x21, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x22, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x23, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x24, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x25, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x26: ES segment override prefix
  AddOpcode(0x27, InstructionType::Unknown, 0,
            {}); // DAA (not valid in 64-bit mode)
  AddOpcode(0x28, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x29, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x2A, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x2B, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x2C, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x2D, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x2E: CS segment override prefix
  AddOpcode(0x2F, InstructionType::Unknown, 0,
            {}); // DAS (not valid in 64-bit mode)

  // 0x30-0x3F: XOR and CMP
  AddOpcode(0x30, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x31, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x32, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x33, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x34, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x35, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x36: SS segment override prefix
  AddOpcode(0x37, InstructionType::Unknown, 0,
            {}); // AAA (not valid in 64-bit mode)
  AddOpcode(0x38, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x39, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x3A, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x3B, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x3C, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x3D, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x3E: DS segment override prefix
  AddOpcode(0x3F, InstructionType::Unknown, 0,
            {}); // AAS (not valid in 64-bit mode)

  // 0x40-0x4F: REX prefixes in 64-bit mode, INC/DEC in 32-bit mode
  for (uint8_t i = 0x40; i <= 0x4F; ++i) {
    // In 64-bit mode, these are REX prefixes, handled in ParsePrefixes
    // In 32-bit mode compatibility, these would be INC/DEC
    AddOpcode(i, InstructionType::Rex_prefix, 0, {});
  }

  // 0x50-0x5F: PUSH/POP registers
  for (uint8_t i = 0x50; i <= 0x57; ++i) {
    AddOpcode(i, InstructionType::Push, 1,
              {DecodedInstruction::Operand::Type::REGISTER});
  }
  for (uint8_t i = 0x58; i <= 0x5F; ++i) {
    AddOpcode(i, InstructionType::Pop, 1,
              {DecodedInstruction::Operand::Type::REGISTER});
  }

  // 0x60-0x6F: Various instructions
  AddOpcode(0x60, InstructionType::Pusha, 0, {}); // Invalid in 64-bit mode
  AddOpcode(0x61, InstructionType::Popa, 0, {});  // Invalid in 64-bit mode
  AddOpcode(
      0x62, InstructionType::Bound, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::MEMORY}); // Invalid in 64-bit mode
  AddOpcode(0x63, InstructionType::Movsxd, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  // 0x64: FS segment override prefix
  // 0x65: GS segment override prefix
  // 0x66: Operand size override prefix
  // 0x67: Address size override prefix
  AddOpcode(0x68, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x69, InstructionType::Imul, 3,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6A, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6B, InstructionType::Imul, 3,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6C, InstructionType::Ins, 0, {});
  AddOpcode(0x6D, InstructionType::Ins, 0, {});
  AddOpcode(0x6E, InstructionType::Outs, 0, {});
  AddOpcode(0x6F, InstructionType::Outs, 0, {});

  // 0x70-0x7F: Short conditional jumps
  for (uint8_t i = 0x70; i <= 0x7F; ++i) {
    AddOpcode(i, InstructionType::Jcc, 1,
              {DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0x80-0x8F: Immediate arithmetic and MOV
  AddOpcode(0x80, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x81, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x82, InstructionType::Unknown, 0,
            {}); // Group 1 - invalid in 64-bit mode
  AddOpcode(0x83, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x84, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x85, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x86, InstructionType::Xchg, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x87, InstructionType::Xchg, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x88, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x89, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x8A, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8B, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8C, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::SEGMENT}); // Segment register
  AddOpcode(0x8D, InstructionType::Lea, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8E, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::SEGMENT,
             DecodedInstruction::Operand::Type::MEMORY}); // Segment register
  AddOpcode(0x8F, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::MEMORY}); // Group 1A

  // 0x90-0x9F: NOP, XCHG, and string operations
  AddOpcode(0x90, InstructionType::Nop, 0, {});
  for (uint8_t i = 0x91; i <= 0x97; ++i) {
    AddOpcode(i, InstructionType::Xchg, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::REGISTER});
  }
  AddOpcode(0x98, InstructionType::Movsx, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER}); // CWDE/CDQE
  AddOpcode(0x99, InstructionType::Unknown, 0, {});         // CDQ/CQO
  AddOpcode(
      0x9A, InstructionType::Call_far, 1,
      {DecodedInstruction::Operand::Type::IMMEDIATE}); // Invalid in 64-bit mode
  AddOpcode(0x9B, InstructionType::Fwait, 0, {});
  AddOpcode(0x9C, InstructionType::Pushf, 0, {});
  AddOpcode(0x9D, InstructionType::Popf, 0, {});
  AddOpcode(0x9E, InstructionType::Sahf, 0, {});
  AddOpcode(0x9F, InstructionType::Lahf, 0, {});

  // 0xA0-0xAF: String operations and TEST
  AddOpcode(0xA0, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0xA1, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0xA2, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xA3, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xA4, InstructionType::Movsb, 0, {});
  AddOpcode(0xA5, InstructionType::Movsd, 0, {});
  AddOpcode(0xA6, InstructionType::Cmpsb, 0, {});
  AddOpcode(0xA7, InstructionType::Cmpsd, 0, {});
  AddOpcode(0xA8, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xA9, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xAA, InstructionType::Stosb, 0, {});
  AddOpcode(0xAB, InstructionType::Stosd, 0, {});
  AddOpcode(0xAC, InstructionType::Lodsb, 0, {});
  AddOpcode(0xAD, InstructionType::Lodsd, 0, {});
  AddOpcode(0xAE, InstructionType::Scasb, 0, {});
  AddOpcode(0xAF, InstructionType::Scasd, 0, {});

  // 0xB0-0xBF: MOV immediate to register
  for (uint8_t i = 0xB0; i <= 0xB7; ++i) {
    AddOpcode(i, InstructionType::Mov, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::IMMEDIATE});
  }
  for (uint8_t i = 0xB8; i <= 0xBF; ++i) {
    AddOpcode(i, InstructionType::Mov, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0xC0-0xCF: Shift/rotate and control flow
  AddOpcode(0xC0, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xC1, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xC2, InstructionType::Ret, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xC3, InstructionType::Ret, 0, {});
  AddOpcode(
      0xC4, InstructionType::Les, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::MEMORY}); // LES - invalid in 64-bit
                                                    // mode / VEX prefix
  AddOpcode(
      0xC5, InstructionType::Lds, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::MEMORY}); // LDS - invalid in 64-bit
                                                    // mode / VEX prefix
  AddOpcode(0xC6, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE}); // Group 11
  AddOpcode(0xC7, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE}); // Group 11
  AddOpcode(0xC8, InstructionType::Enter, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xC9, InstructionType::Leave, 0, {});
  AddOpcode(0xCA, InstructionType::Ret_far, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xCB, InstructionType::Ret_far, 0, {});
  AddOpcode(0xCC, InstructionType::Int3, 0, {});
  AddOpcode(0xCD, InstructionType::Int, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xCE, InstructionType::Into, 0, {}); // Invalid in 64-bit mode
  AddOpcode(0xCF, InstructionType::Iret, 0, {});

  // 0xD0-0xDF: Shift/rotate and FPU escape
  AddOpcode(0xD0, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD1, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD2, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD3, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD4, InstructionType::Unknown, 0,
            {}); // AAM - invalid in 64-bit mode
  AddOpcode(0xD5, InstructionType::Unknown, 0,
            {}); // AAD - invalid in 64-bit mode
  AddOpcode(0xD6, InstructionType::Unknown, 0, {}); // SALC - undocumented
  AddOpcode(0xD7, InstructionType::Unknown, 0, {}); // XLAT
  // 0xD8-0xDF: FPU instructions - handled specially
  for (uint8_t i = 0xD8; i <= 0xDF; ++i) {
    AddOpcode(i, InstructionType::Fpu_escape, 0, {}); // Special handling needed
  }

  // 0xE0-0xEF: Loop and I/O instructions
  AddOpcode(0xE0, InstructionType::Loopne, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE1, InstructionType::Loope, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE2, InstructionType::Loop, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE3, InstructionType::Jrcxz, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE4, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE5, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE6, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xE7, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xE8, InstructionType::Call, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE9, InstructionType::Jump, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(
      0xEA, InstructionType::Jmp_far, 1,
      {DecodedInstruction::Operand::Type::IMMEDIATE}); // Invalid in 64-bit mode
  AddOpcode(0xEB, InstructionType::Jump, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xEC, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xED, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xEE, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xEF, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});

  // 0xF0-0xFF: Prefixes and miscellaneous
  // 0xF0: LOCK prefix
  AddOpcode(0xF1, InstructionType::Int, 0, {}); // INT1/ICEBP
  // 0xF2: REPNE prefix
  // 0xF3: REP/REPE prefix
  AddOpcode(0xF4, InstructionType::Hlt, 0, {});
  AddOpcode(0xF5, InstructionType::Cmc, 0, {});
  AddOpcode(0xF6, InstructionType::Unknown, 0,
            {}); // Group 3 - handled specially
  AddOpcode(0xF7, InstructionType::Unknown, 0,
            {}); // Group 3 - handled specially
  AddOpcode(0xF8, InstructionType::Clc, 0, {});
  AddOpcode(0xF9, InstructionType::Stc, 0, {});
  AddOpcode(0xFA, InstructionType::Cli, 0, {});
  AddOpcode(0xFB, InstructionType::Sti, 0, {});
  AddOpcode(0xFC, InstructionType::Cld, 0, {});
  AddOpcode(0xFD, InstructionType::Std, 0, {});
  AddOpcode(0xFE, InstructionType::Unknown, 0,
            {}); // Group 4 - handled specially
  AddOpcode(0xFF, InstructionType::Unknown, 0,
            {}); // Group 5 - handled specially
}

void InstructionDecoder::InitializeTwoByteOpcodes() {
  // 0x0F 0x00-0x0F: System instructions
  AddTwoByteOpcode(0x00, InstructionType::Unknown, 0,
                   {}); // Group 6 - SLDT, STR, LLDT, LTR, VERR, VERW
  AddTwoByteOpcode(
      0x01, InstructionType::Unknown, 0,
      {}); // Group 7 - SGDT, SIDT, LGDT, LIDT, SMSW, LMSW, INVLPG, SWAPGS
  AddTwoByteOpcode(0x02, InstructionType::Lar, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x03, InstructionType::Lsl, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x05, InstructionType::Syscall, 0, {});
  AddTwoByteOpcode(0x06, InstructionType::Clts, 0, {});
  AddTwoByteOpcode(0x07, InstructionType::Sysret, 0, {});
  AddTwoByteOpcode(0x08, InstructionType::Invd, 0, {});
  AddTwoByteOpcode(0x09, InstructionType::Wbinvd, 0, {});
  AddTwoByteOpcode(0x0B, InstructionType::Ud2, 0, {});
  AddTwoByteOpcode(
      0x0D, InstructionType::Prefetch, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Group P - NOP/PREFETCH
  AddTwoByteOpcode(0x0E, InstructionType::Unknown, 0, {}); // FEMMS
  AddTwoByteOpcode(0x0F, InstructionType::Unknown, 0, {}); // 3DNow! prefix

  // 0x0F 0x10-0x1F: SSE move instructions
  AddTwoByteOpcode(0x10, InstructionType::Movups, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x11, InstructionType::Movups, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x12, InstructionType::Movlps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x13, InstructionType::Movlps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x14, InstructionType::Unpcklps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x15, InstructionType::Unpckhps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x16, InstructionType::Movhps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x17, InstructionType::Movhps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(
      0x18, InstructionType::Prefetcht0, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Group 16 - PREFETCH
  AddTwoByteOpcode(
      0x19, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1A, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1B, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1C, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1D, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1E, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1F, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP

  // 0x0F 0x20-0x2F: Control register and debug register moves
  AddTwoByteOpcode(
      0x20, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::CONTROL}); // MOV r32, CRn
  AddTwoByteOpcode(0x21, InstructionType::Mov, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::DEBUG}); // MOV r32, DRn
  AddTwoByteOpcode(
      0x22, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::CONTROL,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV CRn, r32
  AddTwoByteOpcode(
      0x23, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::DEBUG,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV DRn, r32
  AddTwoByteOpcode(0x28, InstructionType::Movaps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x29, InstructionType::Movaps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2A, InstructionType::Cvtpi2ps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x2B, InstructionType::Movntps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2C, InstructionType::Cvttps2pi, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2D, InstructionType::Cvtps2pi, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2E, InstructionType::Ucomiss, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x2F, InstructionType::Comiss, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x30-0x3F: System instructions and WRMSR/RDMSR
  AddTwoByteOpcode(0x30, InstructionType::Wrmsr, 0, {});
  AddTwoByteOpcode(0x31, InstructionType::Rdtsc, 0, {});
  AddTwoByteOpcode(0x32, InstructionType::Rdmsr, 0, {});
  AddTwoByteOpcode(0x33, InstructionType::Rdpmc, 0, {});
  AddTwoByteOpcode(0x34, InstructionType::Sysenter, 0, {});
  AddTwoByteOpcode(0x35, InstructionType::Sysexit, 0, {});
  AddTwoByteOpcode(0x37, InstructionType::Getsec, 0, {});
  // 0x38: Three-byte escape
  // 0x3A: Three-byte escape

  // 0x0F 0x40-0x4F: Conditional moves
  for (uint8_t i = 0x40; i <= 0x4F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Cmovcc, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});
  }

  // 0x0F 0x50-0x5F: SSE arithmetic and logical
  AddTwoByteOpcode(0x50, InstructionType::Movmskps, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x51, InstructionType::Sqrtps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x52, InstructionType::Rsqrtps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x53, InstructionType::Rcpps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x54, InstructionType::Andps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x55, InstructionType::Andnps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x56, InstructionType::Orps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x57, InstructionType::Xorps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x58, InstructionType::Addps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x59, InstructionType::Mulps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5A, InstructionType::Cvtps2pd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5B, InstructionType::Cvtdq2ps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5C, InstructionType::Subps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5D, InstructionType::Minps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5E, InstructionType::Divps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5F, InstructionType::Maxps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x60-0x6F: MMX/SSE2 packed integer operations
  AddTwoByteOpcode(0x60, InstructionType::Punpcklbw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x61, InstructionType::Punpcklwd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x62, InstructionType::Punpckldq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x63, InstructionType::Packsswb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x64, InstructionType::Pcmpgtb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x65, InstructionType::Pcmpgtw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x66, InstructionType::Pcmpgtd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x67, InstructionType::Packuswb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x68, InstructionType::Punpckhbw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x69, InstructionType::Punpckhwd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6A, InstructionType::Punpckhdq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6B, InstructionType::Packssdw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6C, InstructionType::Punpcklqdq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6D, InstructionType::Punpckhqdq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6E, InstructionType::Movd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0x6F, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x70-0x7F: MMX/SSE2 shift and shuffle operations
  AddTwoByteOpcode(0x70, InstructionType::Pshufw, 3,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0x71, InstructionType::Unknown, 0,
                   {}); // Group 12 - PSRLW/PSRAW/PSLLW
  AddTwoByteOpcode(0x72, InstructionType::Unknown, 0,
                   {}); // Group 13 - PSRLD/PSRAD/PSLLD
  AddTwoByteOpcode(0x73, InstructionType::Unknown, 0,
                   {}); // Group 14 - PSRLQ/PSLLQ
  AddTwoByteOpcode(0x74, InstructionType::Pcmpeqb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x75, InstructionType::Pcmpeqw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x76, InstructionType::Pcmpeqd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x77, InstructionType::Unknown, 0, {}); // EMMS
  AddTwoByteOpcode(0x78, InstructionType::Vmread, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0x79, InstructionType::Vmwrite, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7C, InstructionType::Haddpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7D, InstructionType::Hsubpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7E, InstructionType::Movd, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MMX});
  AddTwoByteOpcode(0x7F, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MMX});

  // 0x0F 0x80-0x8F: Long conditional jumps
  for (uint8_t i = 0x80; i <= 0x8F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Jcc, 1,
                     {DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0x0F 0x90-0x9F: Byte set on condition
  for (uint8_t i = 0x90; i <= 0x9F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Setcc, 1,
                     {DecodedInstruction::Operand::Type::MEMORY});
  }

  // 0x0F 0xA0-0xAF: Push/Pop FS/GS, CPUID, BT operations
  AddTwoByteOpcode(0xA0, InstructionType::Push, 1,
                   {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH FS
  AddTwoByteOpcode(0xA1, InstructionType::Pop, 1,
                   {DecodedInstruction::Operand::Type::SEGMENT}); // POP FS
  AddTwoByteOpcode(0xA2, InstructionType::Cpuid, 0, {});
  AddTwoByteOpcode(0xA3, InstructionType::Bt, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xA4, InstructionType::Shld, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xA5, InstructionType::Shld, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xA8, InstructionType::Push, 1,
                   {DecodedInstruction::Operand::Type::SEGMENT}); // PUSH GS
  AddTwoByteOpcode(0xA9, InstructionType::Pop, 1,
                   {DecodedInstruction::Operand::Type::SEGMENT}); // POP GS
  AddTwoByteOpcode(0xAA, InstructionType::Rsm, 0, {});
  AddTwoByteOpcode(0xAB, InstructionType::Bts, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xAC, InstructionType::Shrd, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xAD, InstructionType::Shrd, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xAE, InstructionType::Unknown, 0,
                   {}); // Group 15 - FXSAVE, FXRSTOR, LDMXCSR, STMXCSR, XSAVE,
                        // XRSTOR, XSAVEOPT, CLFLUSH
  AddTwoByteOpcode(0xAF, InstructionType::Imul, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xB0-0xBF: CMPXCHG, LSS, BTR, LFS, LGS, MOVZX, MOVSX
  AddTwoByteOpcode(0xB0, InstructionType::Cmpxchg, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB1, InstructionType::Cmpxchg, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB2, InstructionType::Lss, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB3, InstructionType::Btr, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB4, InstructionType::Lfs, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB5, InstructionType::Lgs, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB6, InstructionType::Movzx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB7, InstructionType::Movzx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB8, InstructionType::Popcnt, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY}); // F3 prefix
  AddTwoByteOpcode(0xB9, InstructionType::Ud1, 0, {}); // Group 10 - UD1
  AddTwoByteOpcode(0xBA, InstructionType::Unknown, 0,
                   {}); // Group 8 - BT, BTS, BTR, BTC
  AddTwoByteOpcode(0xBB, InstructionType::Btc, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xBC, InstructionType::Bsf, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBD, InstructionType::Bsr, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBE, InstructionType::Movsx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBF, InstructionType::Movsx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xC0-0xCF: XADD, BSWAP, and SSE compare/shuffle
  AddTwoByteOpcode(0xC0, InstructionType::Xadd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC1, InstructionType::Xadd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC2, InstructionType::Cmpps, 3,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC3, InstructionType::Movnti, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC4, InstructionType::Pinsrw, 3,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC5, InstructionType::Pextrw, 3,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC6, InstructionType::Shufps, 3,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC7, InstructionType::Unknown, 0,
                   {}); // Group 9 - CMPXCHG8B, CMPXCHG16B, VMPTRLD, VMPTRST,
                        // VMCLEAR, VMXON, RDRAND, RDSEED
  for (uint8_t i = 0xC8; i <= 0xCF; ++i) {
    AddTwoByteOpcode(i, InstructionType::Bswap, 1,
                     {DecodedInstruction::Operand::Type::REGISTER});
  }

  // 0x0F 0xD0-0xDF: SSE2 and MMX operations
  AddTwoByteOpcode(0xD0, InstructionType::Addpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD1, InstructionType::Psrlw, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD2, InstructionType::Psrld, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD3, InstructionType::Psrlq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD4, InstructionType::Paddq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD5, InstructionType::Pmulhw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD6, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0xD7, InstructionType::Pmovmskb, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MMX});
  AddTwoByteOpcode(0xD8, InstructionType::Psubusb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD9, InstructionType::Psubusw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDA, InstructionType::Pminub, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDB, InstructionType::Pand, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDC, InstructionType::Paddusb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDD, InstructionType::Paddusw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDE, InstructionType::Pmaxub, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDF, InstructionType::Pandn, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xE0-0xEF: MMX average and arithmetic operations
  AddTwoByteOpcode(0xE0, InstructionType::Pavgb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE1, InstructionType::Psraw, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE2, InstructionType::Psrad, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE3, InstructionType::Pavgw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE4, InstructionType::Pmulhuw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE5, InstructionType::Pmulhw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE6, InstructionType::Cvttpd2dq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE7, InstructionType::Movnti, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MMX});
  AddTwoByteOpcode(0xE8, InstructionType::Psubsb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE9, InstructionType::Psubsw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEA, InstructionType::Pminsw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEB, InstructionType::Por, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEC, InstructionType::Paddsb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xED, InstructionType::Paddsw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEE, InstructionType::Pmaxsw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEF, InstructionType::Pxor, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xF0-0xFF: MMX arithmetic and logical operations
  AddTwoByteOpcode(0xF0, InstructionType::Lddqu, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF1, InstructionType::Psllw, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF2, InstructionType::Pslld, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF3, InstructionType::Psllq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF4, InstructionType::Pmuludq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF5, InstructionType::Pmaddwd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF6, InstructionType::Psadbw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF7, InstructionType::Maskmovdqu, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MMX});
  AddTwoByteOpcode(0xF8, InstructionType::Psubb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF9, InstructionType::Psubw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFA, InstructionType::Psubd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFB, InstructionType::Psubq, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFC, InstructionType::Paddb, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFD, InstructionType::Paddw, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFE, InstructionType::Paddd, 2,
                   {DecodedInstruction::Operand::Type::MMX,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFF, InstructionType::Ud0, 0, {});
}

void InstructionDecoder::InitializeThreeByteOpcodes() {
  // 0x0F 0x38 prefix - SSSE3, SSE4.1, SSE4.2, AES, PCLMULQDQ
  AddThreeByteOpcode(0x3800, InstructionType::Pshufb, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3801, InstructionType::Phaddw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3802, InstructionType::Phaddd, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3803, InstructionType::Phaddsw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3804, InstructionType::Pmaddubsw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3805, InstructionType::Phsubw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3806, InstructionType::Phsubd, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3807, InstructionType::Phsubsw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3808, InstructionType::Psignb, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3809, InstructionType::Psignb, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x380A, InstructionType::Psignb, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x380B, InstructionType::Pmulhrsw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});

  // SSE4.1 instructions
  AddThreeByteOpcode(0x3810, InstructionType::Pblendvb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3814, InstructionType::Blendvps, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3815, InstructionType::Blendvpd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3817, InstructionType::Ptest, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381C, InstructionType::Pabsb, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381D, InstructionType::Pabsw, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381E, InstructionType::Pabsd, 2,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3820, InstructionType::Pmovsxbw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3821, InstructionType::Pmovsxbd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3822, InstructionType::Pmovsxbq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3823, InstructionType::Pmovsxwd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3824, InstructionType::Pmovsxwq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3825, InstructionType::Pmovsxdq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3828, InstructionType::Pmuldq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3829, InstructionType::Pcmpeqq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x382A, InstructionType::Movntdqa, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x382B, InstructionType::Packusdw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3830, InstructionType::Pmovzxbw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3831, InstructionType::Pmovzxbd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3832, InstructionType::Pmovzxbq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3833, InstructionType::Pmovzxwd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3834, InstructionType::Pmovzxwq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3835, InstructionType::Pmovzxdq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3837, InstructionType::Pcmpgtq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3838, InstructionType::Pminsb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3839, InstructionType::Pminsd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383A, InstructionType::Pminuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383B, InstructionType::Pminud, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383C, InstructionType::Pmaxsb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383D, InstructionType::Pmaxsd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383E, InstructionType::Pmaxuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383F, InstructionType::Pmaxud, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3840, InstructionType::Pmulld, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3841, InstructionType::Phminposuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  // AES instructions
  AddThreeByteOpcode(0x38DB, InstructionType::Aesimc, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DC, InstructionType::Aesenc, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DD, InstructionType::Aesenclast, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DE, InstructionType::Aesdec, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DF, InstructionType::Aesdeclast, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  // CRC32
  AddThreeByteOpcode(0x38F0, InstructionType::Crc32, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38F1, InstructionType::Crc32, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x3A prefix - SSE4.1 immediate instructions
  AddThreeByteOpcode(0x3A08, InstructionType::Roundps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A09, InstructionType::Roundpd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0A, InstructionType::Roundss, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0B, InstructionType::Roundsd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0C, InstructionType::Blendps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0D, InstructionType::Blendpd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0E, InstructionType::Pblendw, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0F, InstructionType::Palignr, 3,
                     {DecodedInstruction::Operand::Type::MMX,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A14, InstructionType::Pextrb, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A15, InstructionType::Pextrw, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A16, InstructionType::Pextrd, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A17, InstructionType::Extractps, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A20, InstructionType::Pinsrb, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A21, InstructionType::Insertps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A22, InstructionType::Pinsrd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A40, InstructionType::Dpps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A41, InstructionType::Dppd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A42, InstructionType::Mpsadbw, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A60, InstructionType::Pcmpestrm, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A61, InstructionType::Pcmpestri, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A62, InstructionType::Pcmpistrm, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A63, InstructionType::Pcmpistri, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  // AES key generation
  AddThreeByteOpcode(0x3ADF, InstructionType::Aeskeygenassist, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  // PCLMULQDQ
  AddThreeByteOpcode(0x3A44, InstructionType::Pclmulqdq, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
}

void InstructionDecoder::InitializeVEXOpcodes() {
  // VEX-encoded AVX instructions
  // VEX.0F prefix space (most common AVX instructions)

  // VEX.0F.10-1F: AVX move instructions
  vexOpcodes[0x0F10] = {InstructionType::Vmovups,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F11] = {InstructionType::Vmovups,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0F12] = {InstructionType::Vmovlps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F13] = {InstructionType::Vmovlps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0F14] = {InstructionType::Vunpcklps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F15] = {InstructionType::Vunpckhps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F16] = {InstructionType::Vmovhps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F17] = {InstructionType::Vmovhps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};

  vexOpcodes[0x0F28] = {InstructionType::Vmovaps,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F29] = {InstructionType::Vmovaps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0F2A] = {InstructionType::Vcvtpi2ps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F2B] = {InstructionType::Vmovntps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0F2C] = {InstructionType::Vcvttps2pi,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2D] = {InstructionType::Vcvtps2pi,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2E] = {InstructionType::Vucomiss,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F2F] = {InstructionType::Vcomiss,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.50-5F: AVX arithmetic and logical
  vexOpcodes[0x0F50] = {InstructionType::Movmskps,
                        2,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0F51] = {InstructionType::Vsqrtps,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F52] = {InstructionType::Vrsqrtps,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F53] = {InstructionType::Vrcpps,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F54] = {InstructionType::Vandps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F55] = {InstructionType::Vandnps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F56] = {InstructionType::Vorps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F57] = {InstructionType::Vxorps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F58] = {InstructionType::Vaddps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F59] = {InstructionType::Vmulps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5A] = {InstructionType::Vcvtps2pd,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5B] = {InstructionType::Vcvtdq2ps,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5C] = {InstructionType::Vsubps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5D] = {InstructionType::Vminps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5E] = {InstructionType::Vdivps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5F] = {InstructionType::Vmaxps,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.60-6F: AVX packed integer operations
  vexOpcodes[0x0F60] = {InstructionType::Vpunpcklbw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F61] = {InstructionType::Vpunpcklwd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F62] = {InstructionType::Vpunpckldq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F63] = {InstructionType::Vpacksswb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F64] = {InstructionType::Vpcmpgtb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F65] = {InstructionType::Vpcmpgtw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F66] = {InstructionType::Vpcmpgtd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F67] = {InstructionType::Vpackuswb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F68] = {InstructionType::Vpunpckhbw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F69] = {InstructionType::Vpunpckhwd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6A] = {InstructionType::Vpunpckhdq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6B] = {InstructionType::Vpackssdw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6C] = {InstructionType::Vpunpcklqdq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6D] = {InstructionType::Vpunpckhqdq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6E] = {InstructionType::Vmovd,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::REGISTER}};
  vexOpcodes[0x0F6F] = {InstructionType::Vmovdqa,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.70-7F: AVX shuffle and conversion
  vexOpcodes[0x0F70] = {InstructionType::Vpshufd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0F74] = {InstructionType::Vpcmpeqb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F75] = {InstructionType::Vpcmpeqw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F76] = {InstructionType::Vpcmpeqd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F77] = {InstructionType::Vzeroupper, 0, {}};
  vexOpcodes[0x0F7C] = {InstructionType::Vhaddpd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F7D] = {InstructionType::Vhsubpd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F7E] = {InstructionType::Vmovd,
                        2,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F7F] = {InstructionType::Vmovdqa,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::YMM}};

  // VEX.0F.C0-CF: AVX compare and shuffle
  vexOpcodes[0x0FC2] = {InstructionType::Vcmpps,
                        4,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC4] = {InstructionType::Vpinsrw,
                        4,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC5] = {InstructionType::Vpextrw,
                        3,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC6] = {InstructionType::Vshufps,
                        4,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};

  // VEX.0F.D0-DF: More AVX operations
  vexOpcodes[0x0FD0] = {InstructionType::Vaddsubpd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD1] = {InstructionType::Vpsrlw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD2] = {InstructionType::Vpsrld,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD3] = {InstructionType::Vpsrlq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD4] = {InstructionType::Vpaddq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD5] = {InstructionType::Vpmullw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD6] = {InstructionType::Vmovq,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0FD7] = {InstructionType::Vpmovmskb,
                        2,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::YMM}};
  vexOpcodes[0x0FD8] = {InstructionType::Vpsubusb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD9] = {InstructionType::Vpsubusw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDA] = {InstructionType::Vpminub,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDB] = {InstructionType::Vpand,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDC] = {InstructionType::Vpaddusb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDD] = {InstructionType::Vpaddusw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDE] = {InstructionType::Vpmaxub,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDF] = {InstructionType::Vpandn,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // EVEX.0F.E0-EF: More AVX operations
  evexOpcodes[0x0FE0] = {InstructionType::Vpavgb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE1] = {InstructionType::Vpsraw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE2] = {InstructionType::Vpsrad,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE3] = {InstructionType::Vpavgw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE4] = {InstructionType::Vpmulhuw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE5] = {InstructionType::Vpmulhw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE6] = {InstructionType::Vcvttpd2dq,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE7] = {InstructionType::Vmovntdq,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
  evexOpcodes[0x0FE8] = {InstructionType::Vpsubsb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FE9] = {InstructionType::Vpsubsw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FEA] = {InstructionType::Vpminsw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FEB] = {InstructionType::Vpor,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FEC] = {InstructionType::Vpaddsb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FED] = {InstructionType::Vpaddsw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0FEF] = {InstructionType::Vpxor,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.F0-FF: Final AVX operations
  vexOpcodes[0x0FF0] = {InstructionType::Vlddqu,
                        2,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF1] = {InstructionType::Vpsllw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF2] = {InstructionType::Vpslld,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF3] = {InstructionType::Vpsllq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF4] = {InstructionType::Vpmuludq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF5] = {InstructionType::Vpmaddwd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF6] = {InstructionType::Vpsadbw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF7] = {InstructionType::Vmaskmovdqu,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF8] = {InstructionType::Vpsubb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF9] = {InstructionType::Vpsubw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFA] = {InstructionType::Vpsubd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFB] = {InstructionType::Vpsubq,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFC] = {InstructionType::Vpaddb,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFD] = {InstructionType::Vpaddw,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFE] = {InstructionType::Vpaddd,
                        3,
                        {DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::YMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F38 prefix space - AVX2 and other extensions
  vexOpcodes[0x0F3800] = {InstructionType::Vpshufb,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3801] = {InstructionType::Vphaddw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3802] = {InstructionType::Vphaddd,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3803] = {InstructionType::Vphaddsw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3804] = {InstructionType::Vpmaddubsw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3805] = {InstructionType::Vphsubw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3806] = {InstructionType::Vphsubd,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3807] = {InstructionType::Vphsubsw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3808] = {InstructionType::Vpsignb,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3809] = {InstructionType::Vpsignw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380A] = {InstructionType::Vpsignd,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380B] = {InstructionType::Vpmulhrsw,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380C] = {InstructionType::Vpermilps,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380D] = {InstructionType::Vpermilpd,
                          3,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380E] = {InstructionType::Vtestps,
                          2,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380F] = {InstructionType::Vtestpd,
                          2,
                          {DecodedInstruction::Operand::Type::YMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
}

void InstructionDecoder::InitializeEVEXOpcodes() {
  // EVEX-encoded AVX-512 instructions
  // EVEX.0F prefix space

  // EVEX.0F.10-1F: AVX-512 move instructions
  evexOpcodes[0x0F10] = {InstructionType::Vmovups_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F11] = {InstructionType::Vmovups_512,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
  evexOpcodes[0x0F14] = {InstructionType::Vunpcklps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F15] = {InstructionType::Vunpckhps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  evexOpcodes[0x0F28] = {InstructionType::Vmovaps_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F29] = {InstructionType::Vmovaps_512,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
  evexOpcodes[0x0F2E] = {InstructionType::Vucomiss,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F2F] = {InstructionType::Vcomiss,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // EVEX.0F.50-5F: AVX-512 arithmetic
  evexOpcodes[0x0F51] = {InstructionType::Vsqrtps_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F54] = {InstructionType::Vandps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F55] = {InstructionType::Vandnps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F56] = {InstructionType::Vorps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F57] = {InstructionType::Vxorps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F58] = {InstructionType::Vaddps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F59] = {InstructionType::Vmulps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5A] = {InstructionType::Vcvtps2pd,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5B] = {InstructionType::Vcvtdq2ps,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5C] = {InstructionType::Vsubps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5D] = {InstructionType::Vminps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5E] = {InstructionType::Vdivps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5F] = {InstructionType::Vmaxps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // More EVEX.0F opcodes for AVX-512
  evexOpcodes[0x0F60] = {InstructionType::Vpunpcklbw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F61] = {InstructionType::Vpunpcklwd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F62] = {InstructionType::Vpunpckldq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F63] = {InstructionType::Vpacksswb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F64] = {InstructionType::Vpcmpgtb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F65] = {InstructionType::Vpcmpgtw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F66] = {InstructionType::Vpcmpgtd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F67] = {InstructionType::Vpackuswb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F68] = {InstructionType::Vpunpckhbw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F69] = {InstructionType::Vpunpckhwd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6A] = {InstructionType::Vpunpckhdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6B] = {InstructionType::Vpackssdw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6C] = {InstructionType::Vpunpcklqdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6D] = {InstructionType::Vpunpckhqdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6E] = {InstructionType::Vmovd,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::REGISTER}};
  evexOpcodes[0x0F6F] = {InstructionType::Vmovdqa,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // EVEX.0F.70-7F
  evexOpcodes[0x0F70] = {InstructionType::Vpshufd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::IMMEDIATE}};
  evexOpcodes[0x0F74] = {InstructionType::Vpcmpeqb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F75] = {InstructionType::Vpcmpeqw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F76] = {InstructionType::Vpcmpeqd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F7E] = {InstructionType::Vmovd,
                         2,
                         {DecodedInstruction::Operand::Type::REGISTER,
                          DecodedInstruction::Operand::Type::XMM}};
  evexOpcodes[0x0F7F] = {InstructionType::Vmovdqa,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
}

void InstructionDecoder::InitializeFPUOpcodes() {
  // x87 FPU instructions (0xD8-0xDF prefix)

  // 0xD8: FPU arithmetic (single precision)
  fpuOpcodes[0xD800] = {
      InstructionType::Fadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD801] = {
      InstructionType::Fmul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD802] = {
      InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD803] = {
      InstructionType::Fcomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD804] = {
      InstructionType::Fsub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD805] = {
      InstructionType::Fsubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD806] = {
      InstructionType::Fdiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD807] = {
      InstructionType::Fdivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xD9: FPU load/store and transcendental
  fpuOpcodes[0xD900] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD902] = {
      InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD903] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD904] = {
      InstructionType::Fldenv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD905] = {
      InstructionType::Fldcw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD906] = {
      InstructionType::Fnstenv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD907] = {
      InstructionType::Fnstcw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // D9 C0-C7: FLD ST(i)
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xD900 | i] = {
        InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // D9 D0-D7: FST ST(i)
  for (uint8_t i = 0xD0; i <= 0xD7; ++i) {
    fpuOpcodes[0xD900 | i] = {
        InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // D9 D8-DF: FSTP ST(i)
  for (uint8_t i = 0xD8; i <= 0xDF; ++i) {
    fpuOpcodes[0xD900 | i] = {
        InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // D9 E0: FNOP
  fpuOpcodes[0xD9E0] = {InstructionType::Fnop, 0, {}};
  // D9 E1: FXCH
  fpuOpcodes[0xD9E1] = {InstructionType::Xchg,
                        2,
                        {DecodedInstruction::Operand::Type::ST,
                         DecodedInstruction::Operand::Type::ST}};
  // D9 E8: FCHS
  fpuOpcodes[0xD9E8] = {InstructionType::Fchs, 0, {}};
  // D9 E9: FABS
  fpuOpcodes[0xD9E9] = {InstructionType::Fabs, 0, {}};
  // D9 EA: FTST
  fpuOpcodes[0xD9EA] = {InstructionType::Ftst, 0, {}};
  // D9 EB: FXAM
  fpuOpcodes[0xD9EB] = {InstructionType::Fxam, 0, {}};
  // D9 ED: FSQRT
  fpuOpcodes[0xD9ED] = {InstructionType::Fsqrt, 0, {}};
  // D9 EE: FSIN
  fpuOpcodes[0xD9EE] = {InstructionType::Fsin, 0, {}};
  // D9 EF: FCOS
  fpuOpcodes[0xD9EF] = {InstructionType::Fcos, 0, {}};
  // D9 F0: FPTAN
  fpuOpcodes[0xD9F0] = {InstructionType::Fptan, 0, {}};
  // D9 F1: FPATAN
  fpuOpcodes[0xD9F1] = {InstructionType::Fpatan, 0, {}};
  // D9 F2: FXTRACT
  fpuOpcodes[0xD9F2] = {InstructionType::Fxtract, 0, {}};
  // D9 F3: FPREM1
  fpuOpcodes[0xD9F3] = {InstructionType::Fprem1, 0, {}};
  // D9 F4: FDECSTP
  fpuOpcodes[0xD9F4] = {InstructionType::Fdecstp, 0, {}};
  // D9 F5: FINCSTP
  fpuOpcodes[0xD9F5] = {InstructionType::Fincstp, 0, {}};
  // D9 F6: FPREM
  fpuOpcodes[0xD9F6] = {InstructionType::Fprem, 0, {}};
  // D9 F7: FYL2X
  fpuOpcodes[0xD9F7] = {InstructionType::Fyl2x, 0, {}};
  // D9 F8: FYL2XP1
  fpuOpcodes[0xD9F8] = {InstructionType::Fyl2xp1, 0, {}};
  // D9 F9: FSCALE
  fpuOpcodes[0xD9F9] = {InstructionType::Fscale, 0, {}};
  // D9 FA: F2XM1
  fpuOpcodes[0xD9FA] = {InstructionType::F2xm1, 0, {}};
  // D9 FB: FCLEX
  fpuOpcodes[0xD9FB] = {InstructionType::Fclex, 0, {}};
  // D9 FC: FRNDINT
  fpuOpcodes[0xD9FC] = {InstructionType::Frndint, 0, {}};
  // D9 FD: FSTSW AX
  fpuOpcodes[0xD9FD] = {InstructionType::Fnstsw,
                        1,
                        {DecodedInstruction::Operand::Type::REGISTER}};
  // D9 FE: FSTCW
  fpuOpcodes[0xD9FE] = {
      InstructionType::Fstcw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // D9 FF: FSTENV
  fpuOpcodes[0xD9FF] = {
      InstructionType::Fstenv, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDA: FPU integer operations
  fpuOpcodes[0xDA00] = {
      InstructionType::Fiadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA01] = {
      InstructionType::Fimul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA02] = {
      InstructionType::Ficom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA03] = {
      InstructionType::Ficomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA04] = {
      InstructionType::Fisub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA05] = {
      InstructionType::Fisubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA06] = {
      InstructionType::Fidiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA07] = {
      InstructionType::Fidivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DA C0-C7: FADD ST(i), ST
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xDA00 | i] = {InstructionType::Fadd,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DA D0-D7: FMUL ST(i), ST
  for (uint8_t i = 0xD0; i <= 0xD7; ++i) {
    fpuOpcodes[0xDA00 | i] = {InstructionType::Fmul,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DA E0-E7: FCOM ST(i)
  for (uint8_t i = 0xE0; i <= 0xE7; ++i) {
    fpuOpcodes[0xDA00 | i] = {
        InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DA E9: FUCOMP
  fpuOpcodes[0xDAE9] = {InstructionType::Fucomp, 0, {}};

  // 0xDB: FPU integer load/store
  fpuOpcodes[0xDB00] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB02] = {
      InstructionType::Fist, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB03] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB05] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB07] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DB E0: FENI (undocumented)
  fpuOpcodes[0xDBE0] = {InstructionType::Unknown, 0, {}};
  // DB E1: FDISI (undocumented)
  fpuOpcodes[0xDBE1] = {InstructionType::Unknown, 0, {}};
  // DB E2: FCLEX
  fpuOpcodes[0xDBE2] = {InstructionType::Fclex, 0, {}};
  // DB E3: FINIT
  fpuOpcodes[0xDBE3] = {InstructionType::Finit, 0, {}};
  // DB E4: FSETPM (undocumented)
  fpuOpcodes[0xDBE4] = {InstructionType::Unknown, 0, {}};
  // DB E8-EF: FUCOMIP ST(i)
  for (uint8_t i = 0xE8; i <= 0xEF; ++i) {
    fpuOpcodes[0xDB00 | i] = {
        InstructionType::Fucom, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DB F0-F7: FCOMIP ST(i)
  for (uint8_t i = 0xF0; i <= 0xF7; ++i) {
    fpuOpcodes[0xDB00 | i] = {
        InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::ST}};
  }

  // 0xDC: FPU arithmetic (double precision)
  fpuOpcodes[0xDC00] = {
      InstructionType::Fadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC01] = {
      InstructionType::Fmul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC02] = {
      InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC03] = {
      InstructionType::Fcomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC04] = {
      InstructionType::Fsub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC05] = {
      InstructionType::Fsubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC06] = {
      InstructionType::Fdiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC07] = {
      InstructionType::Fdivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DC C0-C7: FADD ST, ST(i)
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xDC00 | i] = {InstructionType::Fadd,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DC D0-D7: FMUL ST, ST(i)
  for (uint8_t i = 0xD0; i <= 0xD7; ++i) {
    fpuOpcodes[0xDC00 | i] = {InstructionType::Fmul,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }

  // 0xDD: FPU load/store (double precision)
  fpuOpcodes[0xDD00] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD02] = {
      InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD03] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD04] = {
      InstructionType::Frstor, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD06] = {
      InstructionType::Fnsave, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD07] = {
      InstructionType::Fnstsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DD C0-C7: FFREE ST(i)
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xDD00 | i] = {
        InstructionType::Ffree, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DD D0-D7: FST ST(i)
  for (uint8_t i = 0xD0; i <= 0xD7; ++i) {
    fpuOpcodes[0xDD00 | i] = {
        InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DD D8-DF: FSTP ST(i)
  for (uint8_t i = 0xD8; i <= 0xDF; ++i) {
    fpuOpcodes[0xDD00 | i] = {
        InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DD E0-E7: FUCOM ST(i)
  for (uint8_t i = 0xE0; i <= 0xE7; ++i) {
    fpuOpcodes[0xDD00 | i] = {
        InstructionType::Fucom, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DD E8-EF: FUCOMP ST(i)
  for (uint8_t i = 0xE8; i <= 0xEF; ++i) {
    fpuOpcodes[0xDD00 | i] = {
        InstructionType::Fucomp, 1, {DecodedInstruction::Operand::Type::ST}};
  }

  // 0xDE: FPU arithmetic (word integer)
  fpuOpcodes[0xDE00] = {
      InstructionType::Fiadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE01] = {
      InstructionType::Fimul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE02] = {
      InstructionType::Ficom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE03] = {
      InstructionType::Ficomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE04] = {
      InstructionType::Fisub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE05] = {
      InstructionType::Fisubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE06] = {
      InstructionType::Fidiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE07] = {
      InstructionType::Fidivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DE C0-C7: FADDP ST(i), ST
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fadd,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DE D0-D7: FMULP ST(i), ST
  for (uint8_t i = 0xD0; i <= 0xD7; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fmul,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DE E0-E7: FSUBP ST(i), ST
  for (uint8_t i = 0xE0; i <= 0xE7; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fsub,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DE E8-EF: FSUBRP ST(i), ST
  for (uint8_t i = 0xE8; i <= 0xEF; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fsubr,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DE F0-F7: FDIVP ST(i), ST
  for (uint8_t i = 0xF0; i <= 0xF7; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fdiv,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }
  // DE F8-FF: FDIVRP ST(i), ST
  for (uint8_t i = 0xF8; i <= 0xFF; ++i) {
    fpuOpcodes[0xDE00 | i] = {InstructionType::Fdivr,
                              2,
                              {DecodedInstruction::Operand::Type::ST,
                               DecodedInstruction::Operand::Type::ST}};
  }

  // 0xDF: FPU integer load/store (word)
  fpuOpcodes[0xDF00] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF02] = {
      InstructionType::Fist, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF03] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF04] = {
      InstructionType::Fbld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF05] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF06] = {
      InstructionType::Fbstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF07] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  // DF C0-C7: FCOMPI ST(i)
  for (uint8_t i = 0xC0; i <= 0xC7; ++i) {
    fpuOpcodes[0xDF00 | i] = {
        InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::ST}};
  }
  // DF E0-E7: FSTSW AX
  fpuOpcodes[0xDFE0] = {InstructionType::Fnstsw,
                        1,
                        {DecodedInstruction::Operand::Type::REGISTER}};
  // DF E8-EF: FUCOMPI ST(i)
  for (uint8_t i = 0xE8; i <= 0xEF; ++i) {
    fpuOpcodes[0xDF00 | i] = {
        InstructionType::Fucom, 1, {DecodedInstruction::Operand::Type::ST}};
  }
}

void InstructionDecoder::InitializeGroupOpcodes() {
  // Group opcodes (instructions that use ModR/M extension)

  // Group 1: Immediate arithmetic (0x80, 0x81, 0x83)
  groupOpcodes[0x8000] = {InstructionType::Add,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8001] = {InstructionType::Or,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8002] = {InstructionType::Adc,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8003] = {InstructionType::Sbb,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8004] = {InstructionType::And,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8005] = {InstructionType::Sub,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8006] = {InstructionType::Xor,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8007] = {InstructionType::Cmp,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2: Shift/rotate (0xC0, 0xC1, 0xD0, 0xD1, 0xD2, 0xD3)
  groupOpcodes[0xC000] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC001] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC002] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC003] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC004] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC005] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC007] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2 with 1 as operand (0xD0, 0xD1)
  groupOpcodes[0xD000] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD001] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD002] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD003] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD004] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD005] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD007] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2 with CL register as operand (0xD2, 0xD3)
  groupOpcodes[0xD200] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD201] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD202] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD203] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD204] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD205] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD207] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};

  // Group 3: TEST, NOT, NEG, MUL, IMUL, DIV, IDIV (0xF6, 0xF7)
  groupOpcodes[0xF600] = {InstructionType::Test,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xF602] = {
      InstructionType::Not, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF603] = {
      InstructionType::Neg, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF604] = {
      InstructionType::Mul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF605] = {
      InstructionType::Imul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF606] = {
      InstructionType::Div, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF607] = {
      InstructionType::Idiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 4: INC/DEC byte (0xFE)
  groupOpcodes[0xFE00] = {
      InstructionType::Inc, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFE01] = {
      InstructionType::Dec, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 5: INC/DEC/CALL/JMP/PUSH (0xFF)
  groupOpcodes[0xFF00] = {
      InstructionType::Inc, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF01] = {
      InstructionType::Dec, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF02] = {
      InstructionType::Call, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF03] = {InstructionType::Call_far,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF04] = {
      InstructionType::Jump, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF05] = {
      InstructionType::Jmp_far, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF06] = {
      InstructionType::Push, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 6: SLDT, STR, LLDT, LTR, VERR, VERW (0x0F 0x00)
  groupOpcodes[0x0000] = {
      InstructionType::Sldt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0001] = {
      InstructionType::Str, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0002] = {
      InstructionType::Lldt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0003] = {
      InstructionType::Ltr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0004] = {
      InstructionType::Verr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0005] = {
      InstructionType::Verw, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 7: SGDT, SIDT, LGDT, LIDT, SMSW, LMSW, INVLPG, SWAPGS (0x0F 0x01)
  groupOpcodes[0x0100] = {
      InstructionType::Sgdt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0101] = {
      InstructionType::Sidt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0102] = {
      InstructionType::Lgdt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0103] = {
      InstructionType::Lidt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0104] = {
      InstructionType::Smsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0106] = {
      InstructionType::Lmsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0107] = {
      InstructionType::Invlpg, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0108] = {
      InstructionType::Swapgs, 0, {}}; // SWAPGS (no operands)

  // Group 8: BT, BTS, BTR, BTC (0x0F 0xBA)
  groupOpcodes[0xBA04] = {InstructionType::Bt,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA05] = {InstructionType::Bts,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA06] = {InstructionType::Btr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA07] = {InstructionType::Btc,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 9: CMPXCHG8B, CMPXCHG16B, VMPTRLD, VMPTRST, VMCLEAR, VMXON, RDRAND,
  // RDSEED (0x0F 0xC7)
  groupOpcodes[0xC701] = {InstructionType::Cmpxchg8b,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC702] = {InstructionType::Cmpxchg16b,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC706] = {
      InstructionType::Vmptrld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC707] = {
      InstructionType::Vmptrst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC700] = {
      InstructionType::Rdrand,
      1,
      {DecodedInstruction::Operand::Type::REGISTER}}; // RDRAND (reg)
  groupOpcodes[0xC703] = {
      InstructionType::Rdseed,
      1,
      {DecodedInstruction::Operand::Type::REGISTER}}; // RDSEED (reg)
}

void InstructionDecoder::AddOpcode(
    uint8_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  singleByteOpcodes[opcode] = info;
  // Removed trace logging to speed up initialization
}

void InstructionDecoder::AddTwoByteOpcode(
    uint8_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  twoByteOpcodes[opcode] = info;
  // Removed trace logging to speed up initialization
}

void InstructionDecoder::AddThreeByteOpcode(
    uint16_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  threeByteOpcodes[opcode] = info;
  // Removed trace logging to speed up initialization
}

// Lazy initialization methods
void InstructionDecoder::EnsureSingleByteInitialized() const {
  if (!singleByteInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeSingleByteOpcodes();
    singleByteInitialized = true;
  }
}

void InstructionDecoder::EnsureTwoByteInitialized() const {
  if (!twoByteInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeTwoByteOpcodes();
    twoByteInitialized = true;
  }
}

void InstructionDecoder::EnsureThreeByteInitialized() const {
  if (!threeByteInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeThreeByteOpcodes();
    threeByteInitialized = true;
  }
}

void InstructionDecoder::EnsureVEXInitialized() const {
  if (!vexInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeVEXOpcodes();
    vexInitialized = true;
  }
}

void InstructionDecoder::EnsureEVEXInitialized() const {
  if (!evexInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeEVEXOpcodes();
    evexInitialized = true;
  }
}

void InstructionDecoder::EnsureFPUInitialized() const {
  if (!fpuInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeFPUOpcodes();
    fpuInitialized = true;
  }
}

void InstructionDecoder::EnsureGroupInitialized() const {
  if (!groupInitialized) {
    const_cast<InstructionDecoder*>(this)->InitializeGroupOpcodes();
    groupInitialized = true;
  }
}

Register InstructionDecoder::ResolveRegister(uint8_t baseRegField,
                                             uint8_t rexExtBit) {
  uint8_t reg = baseRegField & 0x7;
  Register resolved = static_cast<Register>((rexExtBit ? 8 : 0) + reg);
  if (!IsValidRegister(resolved)) {
    spdlog::warn("Invalid register resolved: base=0x{:x}, rexExt=0x{:x}",
                 baseRegField, rexExtBit);
    return Register::NONE;
  }
  return resolved;
}

DecoderErrorInfo InstructionDecoder::Decode(uint64_t addr,
                                            const uint8_t *buffer,
                                            size_t bufferSize,
                                            DecodedInstruction &instr) {
  if (!buffer || bufferSize == 0) {
    spdlog::error("Decode failed at 0x{:x}: null or empty buffer", addr);
    return {DecoderError::BufferOverflow,
            "Null or empty buffer at 0x" + std::to_string(addr)};
  }

  const uint8_t *startBuffer = buffer;
  size_t initialSize = bufferSize;
  instr.reset();

  spdlog::trace("Decoding instruction at 0x{:x}, buffer size={}", addr,
                bufferSize);
  try {
    ParsePrefixes(buffer, bufferSize, instr);
  } catch (const DecodeException &e) {
    spdlog::error("Decode failed at 0x{:x}: Prefix parsing error: {}", addr,
                  e.what());
    return {DecoderError::IncompleteInstruction, "Prefix parsing error at 0x" +
                                                     std::to_string(addr) +
                                                     ": " + e.what()};
  }

  if (bufferSize == 0) {
    instr.length = static_cast<uint8_t>(buffer - startBuffer);
    spdlog::error("Decode failed at 0x{:x}: buffer empty after prefixes", addr);
    return {DecoderError::IncompleteInstruction,
            "Buffer empty after prefixes at 0x" + std::to_string(addr)};
  }

  try {
    ParseOpcode(buffer, bufferSize, instr);
  } catch (const DecodeException &e) {
    spdlog::error("Decode failed at 0x{:x}: Opcode parsing error: {}", addr,
                  e.what());
    return {DecoderError::IncompleteInstruction, "Opcode parsing error at 0x" +
                                                     std::to_string(addr) +
                                                     ": " + e.what()};
  }

  instr.length = static_cast<uint8_t>(buffer - startBuffer);
  if (instr.length > initialSize) {
    spdlog::error(
        "Decode failed at 0x{:x}: instruction length {} exceeds buffer size {}",
        addr, instr.length, initialSize);
    return {DecoderError::BufferOverflow,
            "Instruction length " + std::to_string(instr.length) +
                " exceeds buffer size " + std::to_string(initialSize)};
  }
  if (instr.instType == InstructionType::Unknown) {
    spdlog::error("Decode failed at 0x{:x}: unknown instruction, opcode=0x{:x}",
                  addr, instr.opcode);
    return {DecoderError::InvalidInstruction,
            "Unknown instruction at 0x" + std::to_string(addr) + ", opcode=0x" +
                std::to_string(instr.opcode)};
  }

  if (!instr.validate()) {
    spdlog::error("Decode failed at 0x{:x}: invalid operands", addr);
    return {DecoderError::InvalidInstruction,
            "Invalid operands at 0x" + std::to_string(addr)};
  }

  m_stats[instr.instType]++;
  spdlog::debug("Decoded instruction at 0x{:x}:\n{}", addr, instr.to_string());
  return {DecoderError::Success, ""};
}
// Check for REX prefix (0x40-0x4F in 64-bit mode)

void InstructionDecoder::ParseVEX(const uint8_t *&buffer, size_t &remaining,
                                  DecodedInstruction &instr) {
  if (remaining < 1) { // Need at least the first VEX byte
    throw DecodeException("Buffer too small for VEX prefix");
  }

  uint8_t vex_byte1 = *buffer; // This is 0xC4 or 0xC5
  buffer++;
  remaining--;

  if (vex_byte1 == 0xC4) {
    // 3-byte VEX prefix
    if (remaining < 2) { // Need two more bytes for 3-byte VEX
      throw std::runtime_error("Buffer too small for 3-byte VEX");
    }
    uint8_t vex_byte2 = *buffer++;
    uint8_t vex_byte3 = *buffer++;
    remaining -= 2;

    instr.isVex = true;
    instr.vex.raw[0] = vex_byte1;
    instr.vex.raw[1] = vex_byte2;
    instr.vex.raw[2] = vex_byte3;

    // Parse VEX fields
    instr.vexR = !((vex_byte2 >> 7) & 1);     // R bit (inverted)
    instr.vexX = !((vex_byte2 >> 6) & 1);     // X bit (inverted)
    instr.vexB = !((vex_byte2 >> 5) & 1);     // B bit (inverted)
    instr.vexMmmm = vex_byte2 & 0x1F;         // M-mmmm field
    instr.vexW = (vex_byte3 >> 7) & 1;        // W bit
    instr.vexVvvv = (~vex_byte3 >> 3) & 0x0F; // Vvvv field (inverted)
    instr.vexL = (vex_byte3 >> 2) & 1;        // L bit
    instr.vexPp = vex_byte3 & 0x03;           // Pp field

  } else if (vex_byte1 == 0xC5) {
    // 2-byte VEX prefix
    if (remaining < 1) { // Need one more byte for 2-byte VEX
      throw DecodeException("Buffer too small for 2-byte VEX");
    }
    uint8_t vex_byte2 = *buffer++;
    remaining--;

    instr.isVex = true;
    instr.vex.raw[0] = vex_byte1;
    instr.vex.raw[1] = vex_byte2;
    instr.vex.raw[2] = 0; // Clear third byte for 2-byte VEX

    // Parse VEX fields for 2-byte form
    instr.vexR = !((vex_byte2 >> 7) & 1);     // R bit (inverted)
    instr.vexX = false;                       // Implied 0
    instr.vexB = false;                       // Implied 0
    instr.vexMmmm = 0x01;                     // Implied 0F map
    instr.vexW = false;                       // Implied 0
    instr.vexVvvv = (~vex_byte2 >> 3) & 0x0F; // Vvvv field (inverted)
    instr.vexL = (vex_byte2 >> 2) & 1;        // L bit
    instr.vexPp = vex_byte2 & 0x03;           // Pp field
  } else {
    // This should not happen if called correctly from ParsePrefixes
    throw DecodeException("Invalid VEX prefix byte encountered");
  }

  spdlog::trace("VEX prefix parsed: mmmm=0x{:x}, pp=0x{:x}, L={}, vvvv=0x{:x}, "
                "W={}, R={}, X={}, B={}",
                static_cast<int>(instr.vexMmmm), static_cast<int>(instr.vexPp),
                static_cast<int>(instr.vexL), static_cast<int>(instr.vexVvvv),
                static_cast<int>(instr.vexW), static_cast<int>(instr.vexR),
                static_cast<int>(instr.vexX), static_cast<int>(instr.vexB));
}

void InstructionDecoder::ParseModRM(const uint8_t *&buffer, size_t &remaining,
                                    DecodedInstruction &instr,
                                    uint8_t modrmByte) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for ModR/M");
  }
  // ModR/M byte is already consumed by the caller (ParseOpcode)
  // buffer++;
  // remaining--;

  uint8_t mod = (modrmByte >> 6) & 0x3;
  uint8_t reg = (modrmByte >> 3) & 0x7;
  uint8_t rm = modrmByte & 0x7;

  spdlog::trace("ModR/M: mod={}, reg={}, rm={}", mod, reg, rm);

  // Extend register fields with REX bits
  uint8_t rexR = (instr.rex & REX_R) ? 1 : 0;
  uint8_t rexX = (instr.rex & REX_X) ? 1 : 0;
  uint8_t rexB = (instr.rex & REX_B) ? 1 : 0;

  uint8_t extendedReg = reg | (rexR << 3);
  uint8_t extendedRM = rm | (rexB << 3);

  // Handle different addressing modes
  if (mod == 3) {
    // Register-to-register mode
    instr.operands[0].type = DecodedInstruction::Operand::Type::REGISTER;
    instr.operands[0].reg = static_cast<Register>(extendedRM);
    instr.operands[1].type = DecodedInstruction::Operand::Type::REGISTER;
    instr.operands[1].reg = static_cast<Register>(extendedReg);
    instr.operandCount = 2;
  } else {
    // Memory addressing mode
    ParseMemoryOperand(buffer, remaining, instr, mod, extendedRM, rexX);
    instr.operands[1].type = DecodedInstruction::Operand::Type::REGISTER;
    instr.operands[1].reg = static_cast<Register>(extendedReg);
    instr.operandCount = 2;
  }
}

void InstructionDecoder::ParseMemoryOperand(const uint8_t *&buffer,
                                            size_t &remaining,
                                            DecodedInstruction &instr,
                                            uint8_t mod, uint8_t rm,
                                            uint8_t rexX) {
  instr.operands[0].type = DecodedInstruction::Operand::Type::MEMORY;
  auto &mem = instr.operands[0].memory;

  if (rm == 4) {
    // SIB byte present
    if (remaining < 1) {
      throw DecodeException("Buffer too small for SIB byte");
    }
    uint8_t sib = *buffer++;
    remaining--;

    uint8_t scale = (sib >> 6) & 0x3;
    uint8_t index = (sib >> 3) & 0x7;
    uint8_t base = sib & 0x7;

    // Extend with REX bits
    uint8_t extendedIndex = index | (rexX << 3);
    uint8_t extendedBase = base | ((instr.rex & REX_B) ? 8 : 0);

    mem.scale = 1 << scale;

    if (index != 4) { // RSP cannot be index
      mem.index = static_cast<Register>(extendedIndex);
    }

    if (mod == 0 && base == 5) {
      // [disp32] addressing
      mem.base = Register::NONE;
      if (remaining < 4) {
        throw DecodeException("Buffer too small for displacement");
      }
      mem.displacement = *reinterpret_cast<const int32_t *>(buffer);
      buffer += 4;
      remaining -= 4;
    } else {
      mem.base = static_cast<Register>(extendedBase);
    }
  } else if (mod == 0 && rm == 5) {
    // RIP-relative addressing in 64-bit mode
    mem.base = Register::RIP;
    if (remaining < 4) {
      throw DecodeException("Buffer too small for RIP displacement");
    }
    mem.displacement = *reinterpret_cast<const int32_t *>(buffer);
    buffer += 4;
    remaining -= 4;
  } else {
    // Direct register addressing
    mem.base = static_cast<Register>(rm | ((instr.rex & REX_B) ? 8 : 0));
  }

  // Parse displacement based on mod field
  if (mod == 1) {
    // 8-bit displacement
    if (remaining < 1) {
      throw DecodeException("Buffer too small for 8-bit displacement");
    }
    mem.displacement = static_cast<int8_t>(*buffer);
    buffer++;
    remaining--;
  } else if (mod == 2) {
    // 32-bit displacement
    if (remaining < 4) {
      throw DecodeException("Buffer too small for 32-bit displacement");
    }
    mem.displacement = *reinterpret_cast<const int32_t *>(buffer);
    buffer += 4;
    remaining -= 4;
  }
}

void InstructionDecoder::ParseImmediateOperands(const uint8_t *&buffer,
                                                size_t &remaining,
                                                DecodedInstruction &instr) {
  // Determine immediate size based on instruction type and prefixes
  uint8_t immSize = 4; // Default to 32-bit

  if (instr.HasRexW()) {
    immSize = 8; // 64-bit immediate
  } else if (instr.operandSizeOverride) {
    immSize = 2; // 16-bit immediate
  }

  // Special cases for certain opcodes
  switch (instr.opcode) {
  case 0x6A: // PUSH imm8
  case 0xEB: // JMP rel8
  case 0x70:
  case 0x71:
  case 0x72:
  case 0x73: // Jcc rel8
  case 0x74:
  case 0x75:
  case 0x76:
  case 0x77:
  case 0x78:
  case 0x79:
  case 0x7A:
  case 0x7B:
  case 0x7C:
  case 0x7D:
  case 0x7E:
  case 0x7F:
  case 0xE0:
  case 0xE1:
  case 0xE2:
  case 0xE3: // LOOP variants
    immSize = 1;
    break;
  case 0xC2:
  case 0xCA: // RET imm16
    immSize = 2;
    break;
  }

  if (remaining < immSize) {
    throw DecodeException("Buffer too small for immediate operand");
  }

  uint64_t immediate = 0;
  switch (immSize) {
  case 1:
    immediate = *buffer;
    break;
  case 2:
    immediate = *reinterpret_cast<const uint16_t *>(buffer);
    break;
  case 4:
    immediate = *reinterpret_cast<const uint32_t *>(buffer);
    break;
  case 8:
    immediate = *reinterpret_cast<const uint64_t *>(buffer);
    break;
  }

  // Find the next available operand slot
  for (uint8_t i = 0; i < 4; ++i) {
    if (instr.operands[i].type == DecodedInstruction::Operand::Type::NONE) {
      instr.operands[i].type = DecodedInstruction::Operand::Type::IMMEDIATE;
      instr.operands[i].immediate = immediate;
      instr.operands[i].size = immSize * 8;
      instr.operandCount =
          std::max(instr.operandCount, static_cast<uint8_t>(i + 1));
      break;
    }
  }

  buffer += immSize;
  remaining -= immSize;
}

void InstructionDecoder::ParseOpcode(const uint8_t *&buffer, size_t &remaining,
                                     DecodedInstruction &instr) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for opcode");
  }

  instr.opcode = *buffer++;
  remaining--;

  if (instr.isVex) {
    ParseVEXInstruction(buffer, remaining, instr);
    return;
  }

  // Handle escape sequences
  if (instr.opcode == 0x0F) {
    ParseTwoByteOpcode(buffer, remaining, instr);
    return;
  }

  // Handle FPU escape sequences
  if (instr.opcode >= 0xD8 && instr.opcode <= 0xDF) {
    ParseFPUInstruction(buffer, remaining, instr);
    return;
  }

  // Handle group opcodes
  if (IsGroupOpcode(instr.opcode)) {
    ParseGroupInstruction(buffer, remaining, instr);
    return;
  }

  // Look up single-byte opcode (with lazy initialization)
  EnsureSingleByteInitialized();
  auto it = singleByteOpcodes.find(instr.opcode);
  if (it != singleByteOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    // Parse ModR/M if needed
    if (NeedsModRMParsing(instr.opcode)) {
      if (remaining < 1) {
        throw DecodeException("Buffer too small for ModR/M");
      }
      uint8_t modrmByte = *buffer++;
      remaining--;
      ParseModRM(buffer, remaining, instr, modrmByte);
    }

    // Parse immediate operands if needed
    if (HasImmediateOperand(instr.opcode)) {
      ParseImmediateOperands(buffer, remaining, instr);
    }
  } else {
    spdlog::warn("Unknown single-byte opcode: 0x{:02x}", instr.opcode);
    instr.instType = InstructionType::Unknown;
  }
}

void InstructionDecoder::ParseTwoByteOpcode(const uint8_t *&buffer,
                                            size_t &remaining,
                                            DecodedInstruction &instr) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for two-byte opcode");
  }

  uint8_t secondByte = *buffer++;
  remaining--;

  // Handle three-byte escape sequences
  if (secondByte == 0x38 || secondByte == 0x3A) {
    ParseThreeByteOpcode(buffer, remaining, instr, secondByte);
    return;
  }

  EnsureTwoByteInitialized();
  auto it = twoByteOpcodes.find(secondByte);
  if (it != twoByteOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    // Parse ModR/M if needed
    if (NeedsTwoByteModRMParsing(secondByte)) {
      if (remaining < 1) {
        throw DecodeException("Buffer too small for ModR/M");
      }
      uint8_t modrmByte = *buffer++;
      remaining--;
      ParseModRM(buffer, remaining, instr, modrmByte);
    }

    // Parse immediate operands if needed
    if (HasTwoByteImmediateOperand(secondByte)) {
      ParseImmediateOperands(buffer, remaining, instr);
    }
  } else {
    spdlog::warn("Unknown two-byte opcode: 0x0F{:02x}", secondByte);
    instr.instType = InstructionType::Unknown;
  }
}

void InstructionDecoder::ParseThreeByteOpcode(const uint8_t *&buffer,
                                              size_t &remaining,
                                              DecodedInstruction &instr,
                                              uint8_t secondByte) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for three-byte opcode");
  }

  uint8_t thirdByte = *buffer++;
  remaining--;

  uint16_t opcodeKey = (secondByte << 8) | thirdByte;
  EnsureThreeByteInitialized();
  auto it = threeByteOpcodes.find(opcodeKey);
  if (it != threeByteOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    // Most three-byte opcodes need ModR/M
    if (remaining < 1) {
      throw DecodeException("Buffer too small for ModR/M");
    }
    uint8_t modrmByte = *buffer++;
    remaining--;
    ParseModRM(buffer, remaining, instr, modrmByte);

    // Parse immediate if needed (many SSE4.1 instructions have immediates)
    if (HasThreeByteImmediateOperand(opcodeKey)) {
      ParseImmediateOperands(buffer, remaining, instr);
    }
  } else {
    spdlog::warn("Unknown three-byte opcode: 0x0F{:02x}{:02x}", secondByte,
                 thirdByte);
    instr.instType = InstructionType::Unknown;
  }
}

void InstructionDecoder::ParseVEXInstruction(const uint8_t *&buffer,
                                             size_t &remaining,
                                             DecodedInstruction &instr) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for VEX opcode");
  }

  uint8_t vexOpcode = *buffer++;
  remaining--;

  // Build VEX opcode key based on map
  uint16_t opcodeKey = 0;
  switch (instr.vexMmmm) {
  case 0x01: // 0F map
    opcodeKey = 0x0F00 | vexOpcode;
    break;
  case 0x02: // 0F 38 map
    opcodeKey = 0x0F38;
    if (remaining < 1) {
      throw DecodeException("Buffer too small for VEX 0F38 opcode");
    }
    opcodeKey = (opcodeKey << 8) | (*buffer++);
    remaining--;
    break;
  case 0x03: // 0F 3A map
    opcodeKey = 0x0F3A;
    if (remaining < 1) {
      throw DecodeException("Buffer too small for VEX 0F3A opcode");
    }
    opcodeKey = (opcodeKey << 8) | (*buffer++);
    remaining--;
    break;
  default:
    throw DecodeException("Invalid VEX map field");
  }

  EnsureVEXInitialized();
  auto it = vexOpcodes.find(opcodeKey);
  if (it != vexOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    // VEX instructions typically need ModR/M
    if (remaining < 1) {
      throw DecodeException("Buffer too small for VEX ModR/M");
    }
    uint8_t modrmByte = *buffer++;
    remaining--;
    ParseModRM(buffer, remaining, instr, modrmByte);

    // Parse immediate if needed
    if (HasVEXImmediateOperand(opcodeKey)) {
      ParseImmediateOperands(buffer, remaining, instr);
    }
  } else {
    spdlog::warn("Unknown VEX opcode: map=0x{:02x}, opcode=0x{:04x}",
                 instr.vexMmmm, opcodeKey);
    instr.instType = InstructionType::Unknown;
  }
}

void InstructionDecoder::ParseFPUInstruction(const uint8_t *&buffer,
                                             size_t &remaining,
                                             DecodedInstruction &instr) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for FPU ModR/M");
  }

  uint8_t modrmByte = *buffer++;
  remaining--;

  uint8_t mod = (modrmByte >> 6) & 0x3;
  uint8_t reg = (modrmByte >> 3) & 0x7;
  uint8_t rm = modrmByte & 0x7;

  // Build FPU opcode key
  uint16_t fpuKey = (instr.opcode << 8) | reg;

  if (mod == 3) {
    // Register-register operation, use full ModR/M byte
    fpuKey = (instr.opcode << 8) | modrmByte;
  }

  EnsureFPUInitialized();
  auto it = fpuOpcodes.find(fpuKey);
  if (it != fpuOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    if (mod != 3) {
      // Memory operand
      ParseMemoryOperand(buffer, remaining, instr, mod, rm, 0);
    } else {
      // Register operands
      if (instr.operandCount > 0) {
        instr.operands[0].type = DecodedInstruction::Operand::Type::ST;
        instr.operands[0].reg =
            static_cast<Register>(static_cast<int>(Register::ST0) + rm);
      }
      if (instr.operandCount > 1) {
        instr.operands[1].type = DecodedInstruction::Operand::Type::ST;
        instr.operands[1].reg =
            Register::ST0; // ST(0) is implicit second operand
      }
    }
  } else {
    spdlog::warn("Unknown FPU opcode: 0x{:02x} with ModR/M 0x{:02x}",
                 instr.opcode, modrmByte);
    instr.instType = InstructionType::Unknown;
  }
}

void InstructionDecoder::ParseGroupInstruction(const uint8_t *&buffer,
                                               size_t &remaining,
                                               DecodedInstruction &instr) {
  if (remaining < 1) {
    throw DecodeException("Buffer too small for Group ModR/M");
  }

  uint8_t modrmByte = *buffer++;
  remaining--;

  uint8_t reg = (modrmByte >> 3) & 0x7;

  // Build group opcode key
  uint16_t groupKey = (instr.opcode << 8) | reg;

  EnsureGroupInitialized();
  auto it = groupOpcodes.find(groupKey);
  if (it != groupOpcodes.end()) {
    const auto &opcodeInfo = it->second;
    instr.instType = opcodeInfo.type;
    instr.operandCount = opcodeInfo.operandCount;

    // Copy operand types
    for (uint8_t i = 0; i < opcodeInfo.operandCount && i < 4; ++i) {
      instr.operands[i].type = opcodeInfo.operandTypes[i];
    }

    // Parse the actual operands based on ModR/M
    ParseModRM(buffer, remaining, instr, modrmByte);

    // Parse immediate if needed
    if (HasGroupImmediateOperand(groupKey)) {
      ParseImmediateOperands(buffer, remaining, instr);
    }
  } else {
    spdlog::warn("Unknown group opcode: 0x{:02x} with reg field {}",
                 instr.opcode, reg);
    instr.instType = InstructionType::Unknown;
  }
}

bool InstructionDecoder::NeedsModRMParsing(uint8_t opcode) {
  static const std::unordered_set<uint8_t> modrmOpcodes = {
      // Arithmetic with ModR/M
      0x00, 0x01, 0x02, 0x03, 0x08, 0x09, 0x0A, 0x0B, 0x10, 0x11, 0x12, 0x13,
      0x18, 0x19, 0x1A, 0x1B, 0x20, 0x21, 0x22, 0x23, 0x28, 0x29, 0x2A, 0x2B,
      0x30, 0x31, 0x32, 0x33, 0x38, 0x39, 0x3A, 0x3B,
      // MOV and other data movement
      0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E,
      // TEST and XCHG
      0x84, 0x85, 0x86, 0x87,
      // LEA
      0x8D,
      // IMUL with ModR/M
      0x69, 0x6B,
      // Conditional moves (handled in two-byte)
      // Group opcodes (handled separately)
  };

  return modrmOpcodes.find(opcode) != modrmOpcodes.end();
}

bool InstructionDecoder::NeedsTwoByteModRMParsing(uint8_t secondByte) {
  // Most two-byte opcodes need ModR/M, exceptions are rare
  static const std::unordered_set<uint8_t> noModrmOpcodes = {
      0x05, 0x06, 0x07, 0x08, 0x09, 0x0B,       // System instructions
      0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x37, // MSR and system
      0x77,                                     // EMMS
      0xA2, 0xAA,                               // CPUID, RSM
  };

  return noModrmOpcodes.find(secondByte) == noModrmOpcodes.end();
}

bool InstructionDecoder::IsGroupOpcode(uint8_t opcode) {
  static const std::unordered_set<uint8_t> groupOpcodes = {
      0x80, 0x81, 0x82, 0x83,             // Group 1
      0xC0, 0xC1, 0xD0, 0xD1, 0xD2, 0xD3, // Group 2
      0xF6, 0xF7,                         // Group 3
      0xFE,                               // Group 4
      0xFF,                               // Group 5
      0x8F,                               // Group 1A
      0xC6, 0xC7,                         // Group 11
  };

  return groupOpcodes.find(opcode) != groupOpcodes.end();
}

bool InstructionDecoder::HasImmediateOperand(uint8_t opcode) {
  static const std::unordered_set<uint8_t> immOpcodes = {
      // Immediate arithmetic
      0x04,
      0x05,
      0x0C,
      0x0D,
      0x14,
      0x15,
      0x1C,
      0x1D,
      0x24,
      0x25,
      0x2C,
      0x2D,
      0x34,
      0x35,
      0x3C,
      0x3D,
      // MOV immediate to register
      0xB0,
      0xB1,
      0xB2,
      0xB3,
      0xB4,
      0xB5,
      0xB6,
      0xB7,
      0xB8,
      0xB9,
      0xBA,
      0xBB,
      0xBC,
      0xBD,
      0xBE,
      0xBF,
      // PUSH immediate
      0x68,
      0x6A,
      // Jumps and calls
      0xE8,
      0xE9,
      0xEB,
      // Conditional jumps
      0x70,
      0x71,
      0x72,
      0x73,
      0x74,
      0x75,
      0x76,
      0x77,
      0x78,
      0x79,
      0x7A,
      0x7B,
      0x7C,
      0x7D,
      0x7E,
      0x7F,
      // Loop instructions
      0xE0,
      0xE1,
      0xE2,
      0xE3,
      // RET with immediate
      0xC2,
      0xCA,
      // INT
      0xCD,
      // TEST immediate
      0xA8,
      0xA9,
      // I/O
      0xE4,
      0xE5,
      0xE6,
      0xE7,
      // ENTER
      0xC8,
  };

  return immOpcodes.find(opcode) != immOpcodes.end();
}

bool InstructionDecoder::HasTwoByteImmediateOperand(uint8_t secondByte) {
  static const std::unordered_set<uint8_t> immOpcodes = {
      // Conditional jumps
      0x80,
      0x81,
      0x82,
      0x83,
      0x84,
      0x85,
      0x86,
      0x87,
      0x88,
      0x89,
      0x8A,
      0x8B,
      0x8C,
      0x8D,
      0x8E,
      0x8F,
      // SSE compare with immediate
      0xC2,
      // PINSRW, PEXTRW with immediate
      0xC4,
      0xC5,
      // SHUFPS with immediate
      0xC6,
      // PSHUFW with immediate
      0x70,
  };

  return immOpcodes.find(secondByte) != immOpcodes.end();
}

bool InstructionDecoder::HasThreeByteImmediateOperand(uint16_t opcodeKey) {
  static const std::unordered_set<uint16_t> immOpcodes = {
      // SSE4.1 instructions with immediate
      0x3A08, 0x3A09, 0x3A0A, 0x3A0B, // ROUND*
      0x3A0C, 0x3A0D, 0x3A0E,         // BLEND*, PBLENDW
      0x3A0F,                         // PALIGNR
      0x3A14, 0x3A15, 0x3A16, 0x3A17, // PEXTR*
      0x3A20, 0x3A21, 0x3A22,         // PINSR*, INSERTPS
      0x3A40, 0x3A41, 0x3A42,         // DPP*, MPSADBW
      0x3A44,                         // PCLMULQDQ
      0x3A60, 0x3A61, 0x3A62, 0x3A63, // PCMP*
      0x3ADF,                         // AESKEYGENASSIST
  };

  return immOpcodes.find(opcodeKey) != immOpcodes.end();
}

bool InstructionDecoder::HasVEXImmediateOperand(uint16_t opcodeKey) {
  static const std::unordered_set<uint16_t> immOpcodes = {
      // VEX instructions with immediate
      0x0FC2,                 // VCMPPS
      0x0FC4, 0x0FC5, 0x0FC6, // VPINSRW, VPEXTRW, VSHUFPS
      0x0F70,                 // VPSHUFD
  };

  return immOpcodes.find(opcodeKey) != immOpcodes.end();
}

bool InstructionDecoder::HasGroupImmediateOperand(uint16_t groupKey) {
  uint8_t opcode = (groupKey >> 8) & 0xFF;

  static const std::unordered_set<uint8_t> immGroupOpcodes = {
      0x80, 0x81, 0x83, // Group 1 arithmetic with immediate
      0xC0, 0xC1,       // Group 2 shifts with immediate count
      0xF6, 0xF7,       // Group 3 TEST with immediate (reg=0)
      0xC6, 0xC7,       // Group 11 MOV with immediate
  };

  return immGroupOpcodes.find(opcode) != immGroupOpcodes.end();
}

const std::unordered_map<InstructionType, uint64_t> &
InstructionDecoder::GetStats() const {
  return m_stats;
}

void InstructionDecoder::ResetStats() { m_stats.clear(); }

// Missing function implementations

void InstructionDecoder::ParsePrefixes(const uint8_t *&buffer, size_t &remaining,
                                       DecodedInstruction &instr) {
  while (remaining > 0) {
    uint8_t byte = *buffer;

    // Check for prefix bytes
    switch (byte) {
    case 0x26: // ES segment override
      instr.segmentOverride = 0x26;
      break;
    case 0x2E: // CS segment override
      instr.segmentOverride = 0x2E;
      break;
    case 0x36: // SS segment override
      instr.segmentOverride = 0x36;
      break;
    case 0x3E: // DS segment override
      instr.segmentOverride = 0x3E;
      break;
    case 0x64: // FS segment override
      instr.segmentOverride = 0x64;
      break;
    case 0x65: // GS segment override
      instr.segmentOverride = 0x65;
      break;
    case 0x66: // Operand size override
      instr.operandSizeOverride = true;
      break;
    case 0x67: // Address size override
      instr.addressSizeOverride = true;
      break;
    case 0xF0: // LOCK prefix
      instr.lockPrefix = true;
      break;
    case 0xF2: // REPNE/REPNZ prefix
      instr.repnePrefix = true;
      break;
    case 0xF3: // REP/REPE/REPZ prefix
      instr.repePrefix = true;
      instr.repPrefix = true;
      break;
    case 0xC4: // VEX 3-byte prefix
    case 0xC5: // VEX 2-byte prefix
      ParseVEX(buffer, remaining, instr);
      return; // VEX parsing handles the rest
    default:
      // Check for REX prefix (0x40-0x4F in 64-bit mode)
      if ((byte >= 0x40 && byte <= 0x4F)) {
        instr.rex = byte;
        spdlog::trace("REX prefix: 0x{:02x}", byte);
      } else {
        // Not a prefix, stop parsing prefixes
        return;
      }
      break;
    }

    // If we processed a prefix, advance buffer
    if (byte != *buffer || (byte >= 0x40 && byte <= 0x4F) ||
        byte == 0x26 || byte == 0x2E || byte == 0x36 || byte == 0x3E ||
        byte == 0x64 || byte == 0x65 || byte == 0x66 || byte == 0x67 ||
        byte == 0xF0 || byte == 0xF2 || byte == 0xF3) {
      buffer++;
      remaining--;

      // Store prefix in array if there's space
      for (int i = 0; i < 4; ++i) {
        if (instr.prefixes[i] == 0) {
          instr.prefixes[i] = byte;
          break;
        }
      }
    } else {
      break;
    }
  }
}

bool InstructionDecoder::IsValidRegister(Register reg) const {
  // Check if register is within valid range
  return reg != Register::NONE &&
         static_cast<int>(reg) >= 0 &&
         static_cast<int>(reg) < static_cast<int>(REGISTER_COUNT);
}

} // namespace x86_64
