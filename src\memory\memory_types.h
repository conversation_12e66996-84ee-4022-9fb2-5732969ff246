// Copyright 2025 <Copyright Owner>

#pragma once

namespace ps4 {

/**
 * @brief Enum for memory types supported by the PS4 memory system.
 * @note This is the unified MemoryType enum used throughout the memory system.
 */
enum class MemoryType {
  Default,    ///< General-purpose memory
  SYSTEM,     ///< System-reserved memory (read-only)
  VIDEO,      ///< Video memory for GPU
  IO,         ///< I/O mapped memory
  Video,      ///< Video memory (alias for VIDEO)
  Shared,     ///< Shared memory
  Executable, ///< Executable memory
  Stack,      ///< Stack memory
  Heap        ///< Heap memory
};

} // namespace ps4
