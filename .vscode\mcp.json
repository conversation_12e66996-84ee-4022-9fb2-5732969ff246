{"servers": {"my-mcp-server-7e67b1b2": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"], "env": {"EVERYTHING_SDK_PATH": "D:/sss/Everything_SDK/dll/Everything64.dll"}}, "my-mcp-server-1b9c230d": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "code-reasoning": {"command": "npx", "args": ["-y", "@mettamatt/code-reasoning"]}, "semgrep": {"command": "uvx", "args": ["semgrep-mcp"], "env": {"SEMGREP_APP_TOKEN": "${input:semgrepToken}"}}, "p": {"url": "http://localhost:39300/model_context_protocol/2024-11-05/sse"}, "my-mcp-server-e534feff": {"url": "https://mcp.supermemory.ai/7T1XZlgWnxZnKReOmV4fW/sse"}}}