//Tales table


//check


Table = PBTable:Totan
	Resources, RSID_TTOTAN_START, RSID_TTOTAN_END, RSID_TTOTAN_MODELS,
	Dil, RSID_TTOTAN_PLACEMENT, 0,
	<PERSON><PERSON>, <PERSON>lip<PERSON><PERSON><PERSON><PERSON>, 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>2, 5, <PERSON><PERSON><PERSON><PERSON><PERSON>2, 7,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 31, 32, <PERSON><PERSON><PERSON><PERSON><PERSON>, 29, 30, <PERSON>lip<PERSON><PERSON><PERSON><PERSON>2, -1, -1, <PERSON><PERSON><PERSON><PERSON><PERSON>2, -1, -1,

	<PERSON><PERSON>, 4, 3,  9, 1248.0, -2256.0, 1015.0, 75,0,0, 345, 
	
//           Slow Gravity     Table Rotation   Fast Gravity       Max Speed for Slow Gravity
	Gravity, 0.0,0.0,-60.0,       -1.3,            0.0,0.0,-32.0,               90.0,
	HDR, Middle<PERSON><PERSON><PERSON><PERSON>, 0.3369, BloomS<PERSON>, 1.47, TightDeviation, .3309, BroadDeviation, 1.0, <PERSON>ight<PERSON><PERSON><PERSON>, 4.32, <PERSON><PERSON><PERSON><PERSON>, 1.03, <PERSON><PERSON><PERSON>, 1.5, <PERSON><PERSON><PERSON><PERSON>nan<PERSON><PERSON><PERSON>, 4.7, <PERSON>der<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ype, GLT_NATURAL, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 0.56, <PERSON><PERSON><PERSON><PERSON>ffset, 10.76, BlueShift, TRUE, End, 
	HDR_ps3, MiddleGrayKey, 0.3369, BloomScale, 0.8, TightDeviation, 1.0, BroadDeviation, 1.0, TightBrightness, 4.32, BroadBrightness, 1.03, StarScale, 1.5, AdaptLuminanceRate, 160, RenderStar, FALSE, GlareDefType, GLT_NATURAL, BrightnessThreshhold, 0.56, BrightPassOffset, 10.76, BlueShift, TRUE, End,
	SPU_FX, GScale0, 0.00, Source0, 0.50, Bright0, 1.00, Bright3, 0.00, ThresholdR, 0.00, ThresholdG, 0.00, ThresholdB, 0.00, TintR, 1.00, TintG, 1.00, TintB, 1.00, BaseR, 0.00, BaseG, 0.00, BaseB, 0.00, End,
	
	
//	Balls, 1, 3,  9, 0.0, 0.0, 1150.0, 75,0,0,	// testing
//	Gravity, 0.0, 0.0, -0.5, 0.0,
	LightsFile, Dev/Tales/LightSets.txt, RSID_TTOTAN_LIGHTS
	Emulated,	
	PlungeOnFlipper,
	Dimensions, 10, 21,
	Display, Dot, 
	Friction, 0.003,
	CamerasFile, Dev/Tales/TalesCameras.csv, RSID_TTOTAN_CAMERAS,
	DefaultPhysics, 0.5, 0, 10, 0,
	TiltSwitch, 14,
	SkillShotTime, 3000,
	MipBias, -1.0,
	BallTrough, 4, 32, 33, 34, 35, 
	FlipperSteps, 6, 
	KnockerSolenoid, 7,
	ReflectionAlpha, 250,
	VibrateEject, 0.2, 1.5, 120,
	DemoScore, 3684580,
	FreeCam, -1500, 1500, -3000, 2000, 2800, 4800, 90, 180,
	BallRender, Main, EnvironmentBlend, 0.59, EnvBlendMode, Normal, Reflection, BlendMode, Normal, End,
					// Off rgb	dark rgb	med rgb			light rgb
	DMDColor,		0,0,0,		135,77,38,	180,102,51,		225,128,64,
	MiniDMDColor,	30,30,0,	153,76,0,	204,102,0,		255,128,0,

	LightingData, RSID_TTOTAN_PLACEMENT, 1,
		LightFlasher,		17,		F18A,0,			11.935, 1.597, 4.177,		0.909,		1.000000,
		LightLamp,		0,		L11A,0,			8.913, 4.539, 4.190,		0.539,		0.000027,
		LightLamp,		1,		L12A,0,			2.389, 2.489, 1.715,		3.389,		0.000119,
		LightLamp,		2,		L13A,0,			5.250, 6.810, 0.500,		0.459,		1.000000,
		LightLamp,		3,		L14A,0,			10.590, 2.739, 0.500,		0.539,		0.000026,
		LightLamp,		4,		L15A,0,			6.840, 2.519, 3.182,		0.609,		0.000044,
		LightLamp,		5,		L16A,0,			8.340, 10.460, 12.590,		0.609,		0.000041,
		LightLamp,		6,		L17A,0,			0.500, 9.310, 2.125,		0.509,		1.000000,
		LightLamp,		7,		L18A,0,			17.559, 4.006, 2.125,		0.689,		1.000000,
		LightGI1,		-1,		G1A,0,			2.529, 1.488, 2.246,		0.309,		1.000000,
		LightGI1,		-1,		G1A,1,			4.479, 3.409, 2.815,		1.565,		1.000000,
		LightGI1,		-1,		G1A,2,			4.356, 2.496, 0.459,		5.230,		1.000000,
		LightGI1,		-1,		G1A,3,			3.085, 2.661, 0.749,		0.889,		1.000000,
		LightGI1,		-1,		G1A,4,			5.070, 0.489, 0.500,		0.889,		1.000000,
		LightGI1,		-1,		G1A,5,			9.788, 1.526, 1.506,		0.739,		1.000000,
		LightGI1,		-1,		G1A,6,			5.900, 4.006, 0.500,		1.148,		1.000000,
		LightGI1,		-1,		G1A,7,			4.756, 1.208, 2.419,		2.049,		1.000000,
		LightLamp,		15,		L28A,0,			7.858, 2.125, 2.845,		0.725,		1.000000,
		LightGI3,		-1,		G3A,0,			6.880, 1.788, 2.009,		0.349,		1.000000,
		LightGI3,		-1,		G3A,1,			6.030, 2.019, 0.909,		0.749,		1.000000,
		LightGI3,		-1,		G3A,2,			7.697, 1.799, 0.500,		0.978,		1.000000,
		LightGI3,		-1,		G3A,3,			5.618, 4.990, 8.902,		1.565,		1.000000,
		LightGI3,		-1,		G3A,4,			7.410, 3.909, 0.969,		5.530,		1.000000,
		LightGI3,		-1,		G3A,5,			8.069, 1.799, 0.500,		7.760,		0.000000,
		LightGI3,		-1,		G3A,6,			8.140, 4.657, 0.490,		0.789,		0.000009,
		LightGI3,		-1,		G3A,7,			7.618, 1.388, 0.500,		0.609,		1.000000,
		LightGI3,		-1,		G3A,8,			3.651, 1.546, 0.638,		0.839,		1.000000,
		LightGI3,		-1,		G3A,9,			4.447, 3.193, 5.039,		1.299,		1.000000,
		LightGI3,		-1,		G3A,10,			2.499, 1.618, 4.360,		2.089,		1.000000,
		LightGI3,		-1,		G3A,11,			1.526, 0.699, 5.430,		1.845,		1.000000,
		LightLamp,		15,		L28B,0,			13.020, 4.046, 3.355,		0.709,		1.000000,
		LightLamp,		20,		L35A,0,			0.827, 4.230, 5.615,		0.240,		0.000174,
		LightLamp,		21,		L36A,0,			0.788, 6.130, 7.677,		0.309,		1.000000,
		LightLamp,		22,		L37A,0,			0.717, 4.300, 6.310,		0.279,		1.000000,
		LightLamp,		36,		L55A,0,			0.500, 5.250, 7.370,		0.289,		1.000000,
		LightLamp,		23,		L38A,0,			4.970, 1.378, 1.588,		0.289,		1.000000,
		LightLamp,		55,		L78A,0,			5.000, 1.136, 0.399,		0.279,		1.000000,
		LightLamp,		55,		L78A,1,			6.519, 2.826, 1.432,		2.155,		1.000000,
		LightLamp,		54,		L77A,0,			6.330, 2.039, 0.500,		0.489,		1.000000,
		LightLamp,		54,		L77A,1,			5.610, 1.968, 0.819,		1.299,		1.000000,
		LightLamp,		53,		L76A,0,			2.625, 1.618, 0.500,		1.055,		1.000000,
		LightLamp,		39,		L58A,0,			3.105, 3.809, 1.046,		0.259,		1.000000,
		LightLamp,		39,		L58A,1,			0.500, 5.210, 3.662,		0.998,		0.000017,
		LightLamp,		37,		L56A,0,			0.500, 8.660, 9.020,		0.226,		1.000000,
		LightLamp,		38,		L57A,0,			3.979, 5.190, 3.305,		0.180,		1.000000,
		LightLamp,		46,		L67A,0,			6.030, 5.460, 3.615,		0.469,		1.000000,
		LightLamp,		45,		L66A,0,			5.730, 4.157, 3.069,		0.428,		1.000000,
		LightLamp,		44,		L65A,0,			5.550, 4.400, 3.182,		0.809,		1.000000,
		LightLamp,		43,		L64A,0,			6.510, 5.010, 2.892,		0.749,		1.000000,
		LightLamp,		42,		L63A,0,			7.370, 5.890, 3.182,		0.628,		1.000000,
		LightLamp,		41,		L62A,0,			8.483, 4.756, 2.585,		0.448,		1.000000,
		LightLamp,		40,		L61A,0,			5.330, 5.256, 4.065,		0.329,		1.000000,
		LightLamp,		56,		L81A,0,			3.207, 10.538, 3.095,		0.389,		1.000000,
		LightLamp,		57,		L82A,0,			8.069, 6.526, 0.500,		1.325,		1.000000,
		LightLamp,		58,		L83A,0,			6.264, 0.793, 0.500,		0.939,		1.000000,
		LightLamp,		59,		L84A,0,			6.090, 1.779, 2.989,		0.869,		1.000000,
		LightLamp,		60,		L85A,0,			1.919, 1.433, 0.500,		0.689,		1.000000,
		LightLamp,		24,		L41A,0,			0.509, 7.118, 8.140,		0.309,		1.000000,
		LightLamp,		25,		L42A,0,			1.323, 7.784, 8.699,		0.299,		1.000000,
		LightLamp,		26,		L43A,0,			0.500, 13.928, 11.329,		0.270,		1.000000,
		LightLamp,		27,		L44A,0,			0.500, 11.934, 9.150,		0.226,		1.000000,
		LightLamp,		28,		L45A,0,			0.500, 7.860, 9.310,		0.359,		1.000000,
		LightLamp,		29,		L46A,0,			0.599, 11.210, 8.750,		0.270,		1.000000,
		LightLamp,		30,		L47A,0,			0.500, 12.928, 9.710,		0.210,		1.000000,
		LightLamp,		31,		L48A,0,			0.500, 11.199, 13.420,		0.330,		1.000000,
		LightLamp,		32,		L51A,0,			0.500, 11.578, 13.734,		0.299,		1.000000,
		LightLamp,		39,		L58B,0,			1.689, 10.269, 13.560,		0.279,		1.000000,
		LightLamp,		52,		L75A,0,			4.236, 4.810, 0.989,		0.569,		1.000000,
		LightLamp,		51,		L74A,0,			5.570, 0.559, 0.133,		0.469,		1.000000,
		LightLamp,		50,		L73A,0,			4.610, 3.105, 0.225,		0.889,		1.000000,
		LightLamp,		49,		L72A,0,			2.069, 14.474, 0.869,		0.329,		1.000000,
		LightLamp,		49,		L72A,1,			13.199, 11.405, 9.569,		0.849,		1.000000,
		LightLamp,		48,		L71A,0,			5.578, 4.360, 0.500,		0.628,		1.000000,
		LightLamp,		62,		L87A,0,			3.163, 2.479, 2.039,		0.628,		1.000000,
		LightLamp,		16,		L31A,0,			9.048, 4.144, 2.646,		0.777,		0.000128,
		LightLamp,		33,		L52A,0,			0.309, 4.430, 5.539,		0.919,		1.000000,
		LightLamp,		34,		L53A,0,			0.500, 3.539, 4.360,		0.819,		1.000000,
		LightLamp,		35,		L54A,0,			0.500, 3.835, 5.350,		0.690,		1.000000,
		LightLamp,		17,		L32A,0,			7.370, 10.390, 0.500,		1.355,		1.000000,
		LightLamp,		19,		L34A,0,			10.060, 4.026, 5.256,		0.448,		1.000000,
		LightLamp,		18,		L33A,0,			5.190, 1.853, 3.143,		0.669,		1.000000,
		LightLamp,		14,		L27A,0,			0.525, 3.153, 2.809,		0.869,		1.000000,
		LightLamp,		61,		L86A,0,			9.420, 8.100, 0.500,		1.526,		1.000000,
		LightFlasher,		24,		F25A,0,			5.328, 6.960, 9.189,		0.376,		1.000000,
		LightFlasher,		22,		F23A,0,			8.829, 0.619, 2.735,		9.928,		0.000450,
		LightLamp,		47,		L68A,0,			1.365, 7.440, 1.148,		0.549,		1.000000,
		LightFlasher,		23,		F24A,0,			9.588, 6.710, 3.095,		6.940,		1.000000,
		LightGI2,		-1,		G2A,0,			3.184, 4.348, 6.570,		1.729,		1.000000,
		LightGI2,		-1,		G2A,1,			2.516, 4.045, 4.960,		3.189,		1.000000,
		LightGI2,		-1,		G2A,2,			2.855, 3.217, 8.000,		1.629,		1.000000,
		LightGI2,		-1,		G2A,3,			8.982, 4.236, 3.634,		0.729,		1.000000,
		LightGI2,		-1,		G2A,4,			2.918, 1.826, 0.985,		2.959,		1.000000,
		LightGI2,		-1,		G2A,5,			2.489, 1.268, 1.861,		1.999,		1.000000,
		LightGI2,		-1,		G2A,6,			2.145, 1.128, 2.535,		0.669,		1.000000,
		LightGI2,		-1,		G2A,7,			2.769, 0.859, 0.309,		3.149,		1.000000,
		LightGI2,		-1,		G2A,8,			4.046, 2.355, 1.628,		1.559,		1.000000,
		LightGI2,		-1,		G2A,9,			6.125, 4.519, 3.975,		1.329,		1.000000,
		LightGI2,		-1,		G2A,10,			6.820, 6.006, 4.478,		1.259,		1.000000,
		LightGI2,		-1,		G2A,11,			2.661, 0.569, 0.949,		1.718,		1.000000,
		LightGI2,		-1,		G2A,12,			2.489, 0.678, 2.575,		1.969,		1.000000,
		LightGI2,		-1,		G2A,13,			3.789, 1.376, 1.826,		0.829,		1.000000,
		LightGI2,		-1,		G2A,14,			4.157, 1.738, 2.345,		0.729,		1.000000,
		LightGI2,		-1,		G2A,15,			2.239, 1.321, 8.119,		3.159,		1.000000,
		LightGI2,		-1,		G2A,16,			7.282, 2.184, 4.438,		1.020,		0.000057,
		LightGI2,		-1,		G2A,17,			10.920, 1.863, 5.848,		1.080,		0.000067,
		LightLamp,		13,		L26A,0,			2.355, 0.977, 3.429,		0.479,		1.000000,
		LightLamp,		12,		L25A,0,			4.756, 1.476, 3.469,		0.459,		1.000000,
		LightLamp,		11,		L24A,0,			4.657, 0.500, 3.863,		0.469,		1.000000,
		LightLamp,		10,		L23A,0,			5.910, 0.500, 6.400,		0.609,		1.000000,
		LightLamp,		9,		L22A,0,			4.157, 0.969, 3.959,		0.749,		1.000000,
		LightFlasher,		27,		F28A,0,			6.630, 3.717, 3.565,		3.009,		1.000000,
		LightFlasher,		18,		F19A,0,			14.248, 7.519, 5.205,		4.990,		1.000000,
		LightLamp,		8,		L21A,0,			9.729, 0.500, 3.909,		1.299,		1.000000,
		LightFlasher,		26,		F27A,0,			7.820, 3.595, 9.048,		13.510,		1.000000,
		LightFlasher,		15,		F16B,0,			14.149, 7.026, 4.939,		9.589,		0.000001,
		LightFlasher,		16,		F17B,0,			19.649, 6.506, 11.369,		1.656,		0.000001,
		LightFlasher,		16,		F17A,0,			19.989, 6.418, 14.699,		1.659,		0.000001,
		LightON,		-1,		OnDMDA,0,			4.860, 1.851, 0.330,		3.085,		1.000000,
		LightON,		-1,		OnDMDB,0,			9.150, 3.939, 0.649,		1.841,		1.000000,
		LightON,		-1,		OnDMDC,0,			6.890, 2.845, 0.320,		2.059,		1.000000,
		LightON,		-1,		CoindoorA,0,			3.709, 0.919, 0.110,		1.758,		0.000270,
		LightON,		-1,		CoindoorB,0,			3.141, 0.909, 0.270,		2.518,		0.000230,
		LightON,		-1,		Start,0,			2.469, 1.678, 0.500,		1.000,		0.000044,
		LightFlasher,		15,		F16A,0,			18.549, 8.468, 2.709,		2.429,		0.000000,
		LightFlasher,		21,		F22A,0,			4.585, 15.809, 20.000,		1.699,		0.000004,
		LightFlasher,		19,		F20A,0,			11.428, 7.868, 4.664,		1.768,		1.000000,
		LightON,		-1,		OnBackglassA,0,			0.788, 1.851, 1.427,		4.007,		1.000000,
		LightON,		-1,		OnBackglassA,1,			4.860, 1.851, 0.628,		1.909,		1.000000,
		LightON,		-1,		OnBackglassA,2,			2.929, 3.723, 2.097,		1.538,		1.000000,
		LightON,		-1,		OnBackglassA,3,			4.848, 1.851, 0.330,		1.268,		1.000000,
		LightON,		-1,		OnBackglassA,4,			4.860, 1.243, 0.330,		2.289,		1.000000,
		LightON,		-1,		OnBackglassA,5,			4.860, 1.851, 1.169,		2.687,		1.000000,
		LightON,		-1,		OnBackglassA,6,			4.860, 1.562, 0.939,		0.897,		1.000000,
		LightON,		-1,		OnBackglassA,7,			4.860, 1.851, 0.320,		3.559,		1.000000,
		LightON,		-1,		OnBackglassA,8,			1.837, 1.851, 1.567,		2.049,		1.000000,
		LightON,		-1,		OnBackglassA,9,			4.860, 1.851, 0.919,		1.689,		1.000000,
		LightON,		-1,		OnBackglassA,10,			2.177, 2.483, 0.939,		1.928,		1.000000,
		LightON,		-1,		OnBackglassA,11,			1.067, 3.391, 1.299,		2.647,		1.000000,
		LightON,		-1,		OnBackglassA,12,			4.860, 1.851, 1.689,		2.529,		1.000000,
		LightON,		-1,		OnBackglassA,13,			4.860, 0.823, 0.330,		3.357,		1.000000,
		LightON,		-1,		OnBackglassA,14,			4.860, 1.332, 0.330,		2.607,		1.000000,
		LightON,		-1,		OnBackglassA,15,			4.860, 0.792, 0.330,		1.598,		1.000000,
		LightON,		-1,		OnBackglassA,16,			6.460, 0.904, 0.969,		1.748,		1.000000,
		LightON,		-1,		OnBackglassA,17,			4.860, 1.851, 0.330,		1.337,		1.000000,
		LightON,		-1,		OnBackglassA,18,			2.759, 2.703, 1.218,		3.559,		1.000000,
		LightON,		-1,		OnBackglassA,19,			6.710, 1.851, 0.330,		4.107,		1.000000,
		LightON,		-1,		OnBackglassA,20,			4.860, 0.674, 0.330,		4.570,		1.000000,
		LightON,		-1,		OnBackglassA,21,			0.169, 2.263, 1.768,		3.657,		1.000000,
		LightON,		-1,		OnBackglassA,22,			4.860, 2.111, 0.330,		1.847,		1.000000,
		LightON,		-1,		OnBackglassA,23,			4.860, 1.851, 0.330,		3.157,		1.000000,
		LightON,		-1,		OnBackglassA,24,			2.969, 0.609, 0.329,		2.769,		1.000000,
		LightON,		-1,		OnBackglassA,25,			4.860, 1.851, 0.330,		7.570,		1.000000,
		LightON,		-1,		OnBackglassA,26,			4.860, 1.851, 0.330,		3.897,		1.000000,
		LightON,		-1,		OnBackglassA,27,			4.860, 1.851, 0.330,		7.078,		1.000000,
		LightON,		-1,		OnBackglassA,28,			4.860, 2.641, 0.320,		1.148,		1.000000,
		LightON,		-1,		OnBackglassA,29,			1.347, 2.743, 2.137,		1.789,		1.000000,
		LightON,		-1,		OnBackglassA,30,			4.860, 0.884, 0.330,		4.630,		1.000000,
		LightON,		-1,		OnBackglassA,31,			4.860, 1.851, 0.330,		4.107,		1.000000,
		LightON,		-1,		OnBackglassA,32,			4.860, 1.851, 0.330,		5.178,		1.000000,
		LightON,		-1,		OnBackglassA,33,			4.860, 1.851, 0.330,		2.319,		1.000000,
		LightON,		-1,		OnAA,0,			3.939, 1.851, 0.649,		8.269,		1.000000,
		LightON,		-1,		OnAB,0,			3.077, 1.851, 1.718,		6.820,		1.000000,
		LightON,		-1,		OnAC,0,			4.860, 1.851, 2.077,		2.569,		1.000000,
		LightON,		-1,		OnAD,0,			4.860, 1.851, 1.837,		2.585,		1.000000,
		LightON,		-1,		OnAE,0,			4.860, 1.851, 1.367,		1.478,		1.000000,
		LightON,		-1,		OnAF,0,			4.860, 1.851, 2.019,		1.195,		1.000000,
		LightON,		-1,		OnAG,0,			4.860, 1.851, 2.489,		1.388,		1.000000,
		LightON,		-1,		OnAH,0,			4.860, 1.851, 5.880,		0.628,		1.000000,
		LightON,		-1,		OnAI,0,			4.860, 1.851, 4.678,		2.809,		1.000000,
		LightON,		-1,		OnAJ,0,			4.860, 1.851, 0.369,		1.538,		1.000000,
		LightON,		-1,		OnAK,0,			4.860, 1.851, 0.330,		1.208,		1.000000,
		LightON,		-1,		OnAL,0,			4.860, 1.851, 0.628,		1.033,		1.000000,
		LightON,		-1,		OnAM,0,			3.989, 2.231, 0.989,		1.418,		0.000005,
		LightON,		-1,		OnAN,0,			4.169, 2.381, 0.947,		2.337,		0.000001,
		LightON,		-1,		OnAO,0,			3.689, 1.851, 0.489,		3.487,		0.000001,
		LightON,		-1,		OnAP,0,			4.860, 1.851, 0.439,		1.968,		1.000000,
		LightON,		-1,		OnAQ,0,			4.860, 1.851, 1.367,		2.367,		1.000000,
		LightON,		-1,		OnAR,0,			4.860, 1.851, 0.420,		1.268,		1.000000,
		LightON,		-1,		OnAS,0,			4.860, 0.884, 1.327,		0.839,		1.000000,
		LightON,		-1,		OnAT,0,			4.860, 0.402, 0.330,		2.049,		1.000000,
		LightON,		-1,		OnAU,0,			4.860, 1.851, 2.167,		3.137,		1.000000,
		LightON,		-1,		OnAV,0,			4.860, 1.851, 2.077,		3.532,		1.000000,
		LightON,		-1,		OnAW,0,			4.860, 1.851, 2.219,		3.085,		1.000000,
		LightON,		-1,		OnAX,0,			1.999, 0.954, 2.157,		4.380,		1.000000,
		LightON,		-1,		OnAY,0,			4.860, 2.322, 5.430,		5.470,		1.000000,
		LightON,		-1,		OnAZ,0,			3.739, 2.753, 4.450,		4.570,		1.000000,
		LightON,		-1,		OnAAA,0,			4.860, 1.851, 3.019,		6.848,		1.000000,
		LightON,		-1,		OnAAB,0,			4.860, 3.032, 6.920,		4.890,		1.000000,
		LightON,		-1,		OnAAC,0,			4.860, 1.851, 2.657,		3.059,		1.000000,
		LightON,		-1,		OnAAD,0,			4.860, 1.851, 4.190,		3.085,		1.000000,
		LightFlasher,		26,		F27B,0,			3.155, 0.149, 0.000,		0.270,		1.000000,
		LightFlasher,		26,		F27C,0,			11.428, 1.638, 0.000,		0.059,		1.000000,
		


	EndLightingData


TableEnd = PBTable:Totan

////////////////////////////////////flippers
Table = PBObject:FlipperRight
	Object, PB_OBJECTTYPE_FLIPPER, 2500,
	Switch, 11, 
//	DilPos, flipper R, 0,
	Pos, 360.710083,-2138.04053,955.487671,   1.0, 1.0, 1.0,   -1.95524752,-3.37774730,123,
	Models, 1, RSID_TTOTAN_MODELS, 0,
	EnvMapReflection, True,
	Lights, Def_Flippers,
	Collision, Mesh, 2, RSID_TTOTAN_COLLISIONS, 9, RSID_TTOTAN_COLLISIONS, 10,
	SwitchEOS, 4, 
	Vars, 2, -50.0, -75.0,
	//1(min bal velocity) 2(angle max in unit vector coord) 
	LiveCatchPhysics, 125.0, -0.25 		
	//1(length) 2(angle speed down) 3(elasticity when flipper static) 4 (elasticity when dropping) 5(bottom speed mult) 6(number of trnsfer pts)
	Physics, Right, 380, -25, 0.62, 0.15, 0.3, 16, 
 		TransferPoint, 0.15, -31,   175, 150,  0,  0,
		TransferPoint, 0.20, -15,   200, 170,  0,  0,
		TransferPoint, 0.25,  -9,   300, 200,  0,  0,
		TransferPoint, 0.33,  -3,   380, 270,  0,  0,
		TransferPoint, 0.45,   1.5,   390, 300,  0,  0,
		TransferPoint, 0.50,   3.5,   400, 300,  0,  0,		
		TransferPoint, 0.57,   6.55,   410, 300,  0,  0,		
		TransferPoint, 0.70,   6.85,   410, 320,  0,  0,		
		TransferPoint, 0.76,  11,   350, 350,  0,  0,		
		TransferPoint, 0.80,  15,   300, 320,  0,  0,		
		TransferPoint, 0.83,  19,   275, 320,  0,  0,
		TransferPoint, 0.90,  27,   195, 320,  0,  0,
		TransferPoint, 0.95,  45,   130, 300,  0,  0,
		TransferPoint, 0.97,  65,   115, 270,  0,  0,
		TransferPoint, 0.99,  80,   110, 200,  0,  0,		
		TransferPoint, 1.00,  90,    80, 150,  0,  0,	
TableEnd = PBObject:FlipperRight



Table = PBObject:FlipperLeft
	Object, PB_OBJECTTYPE_FLIPPER, 2500,
	Switch, 12, 
//	DilPos, flipper L, 0,
	Pos, -603.555420,-2143.65674,957.234741,   1.0, 1.0, 1.0,   =-1.93746126,3.50023103,-123,
	Models, 1, RSID_TTOTAN_MODELS, 2,
	EnvMapReflection, True,
	Lights, Def_Flippers,
	SwitchEOS, 2, 
	Collision, Mesh, 2, RSID_TTOTAN_COLLISIONS, 7, RSID_TTOTAN_COLLISIONS, 8,	
	Vars, 2, 50.0, 75.0,
	//1(min bal velocity) 2(angle max in unit vector coord) 
	LiveCatchPhysics, 125.0, -0.25 	
	//1(length) 2(angle speed down) 3(elasticity when flipper static) 4 (elasticity when dropping) 5(bottom speed mult) 6(number of trnsfer pts)
	Physics, Left, 360, 25, 0.65, 0.15, 0.3, 15, 
		TransferPoint, 0.15, -34,   175, 150,  0,  0,
		TransferPoint, 0.20, -15,   200, 170,  0,  0,
		TransferPoint, 0.25,  -9,   255, 200,  0,  0,
		TransferPoint, 0.33,  -3,   400, 220,  0,  0,
		TransferPoint, 0.50,   1,   415, 300,  0,  0,
		TransferPoint, 0.58,   6,   425, 310,  0,  0,		
		TransferPoint, 0.66,   9,   425, 320,  0,  0,		
		TransferPoint, 0.72,  12,   350, 350,  0,  0,		
		TransferPoint, 0.80,  15,   330, 320,  0,  0,		
		TransferPoint, 0.83,  19,   275, 320,  0,  0,
		TransferPoint, 0.90,  27,   195, 320,  0,  0,
		TransferPoint, 0.95,  65,   110, 300,  0,  0,
		TransferPoint, 0.97,  75,   105, 270,  0,  0,
		TransferPoint, 0.99,  80,   100, 200,  0,  0,		
		TransferPoint, 1.00,  90,    80, 150,  0,  0,		
TableEnd = PBObject:FlipperLeft


//visible objects

Table = PBObject:VisibleClip
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 35,
	Lights, Def_Plastic2,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleClip

//Table = PBObject:VisibleGlass
//	Object, PB_OBJECTTYPE_VISUAL, 2000,
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	Models, 1, RSID_TTOTAN_MODELS, 37,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleGlass

Table = PBObject:VisibleTable
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 38,
	Lights, Def_Cabinet,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTable

Table = PBObject:VisibleMetal
	Object, PB_OBJECTTYPE_VISUAL, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 39,
	Lights, Def_emissives_Harley,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleMetal

Table = PBObject:VisibleTrans1
	Object, PB_OBJECTTYPE_VISUAL, 2220,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 41,
	Lights, Def_Plasticpost,
TableEnd = PBObject:VisibleTrans1

Table = PBObject:VisibleTrans2
	Object, PB_OBJECTTYPE_VISUAL, 2210,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 42,
	Lights, Def_Floorlights,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTrans2

Table = PBObject:VisibleTrans3
	Object, PB_OBJECTTYPE_VISUAL, 2200,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 43,
	Lights, Def_floor_Harley,
	EnvMapReflection, True,
TableEnd = PBObject:VisibleTrans3

Table = PBObject:VisibleLights
	Object, PB_OBJECTTYPE_LAMPSET, 2599,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 40,
	EnvMapReflection, True,
	Flags, zWrite, False, zTest, False, End,
	Lights, Def_Bulbs,
	//cmd lampSet	lampNum	 onOffset		offOffset	RSID							Type (EmuLamp, EmuFlasher, Scripted)
	Lamp, 0,		17,		 0,				1,			RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		15,		 2,				3,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		0,		 4,				5,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		1,		 6,				7,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		2,		 8,				9,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		3,		10,				11,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		4,		12,				13,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		5,		14,				15,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		6,		16,				17,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		7,		18,				19,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		15,		20,				21,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		39,		22,				23,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		38,		24,				25,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		20,		26,				27,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		21,		28,				29,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		22,		30,				31,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		36,		32,				33,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		37,		34,				35,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		53,		36,				37,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		54,		38,				39,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		55,		40,				41,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		23,		42,				43,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		46,		44,				45,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		45,		46,				47,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		44,		48,				49,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		43,		50,				51,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		42,		52,				53,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		41,		54,				55,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		40,		56,				57,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		24,		58,				59,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		25,		60,				61,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		26,		62,				63,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		27,		64,				65,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		28,		66,				67,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		29,		68,				69,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		30,		70,				71,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		31,		72,				73,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		32,		74,				75,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		21,		76,				77,			RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,	
	Lamp, 0,		52,		78,				79,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		51,		80,				81,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		50,		82,				83,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		49,		84,				85,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		48,		86,				87,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		62,		88,				89,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		16,		90,				91,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		17,		92,				93,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		19,		94,				95,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		18,		96,				97,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		60,		98,				99,			RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		59,		100,			101,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		58,		102,			103,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		57,		104,			105,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		56,		106,			107,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		23,		108,			109,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,	
	Lamp, 0,		33,		110,			111,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		34,		112,			113,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		35,		114,			115,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		24,		116,			117,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		14,		118,			119,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		61,		120,			121,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		47,		122,			123,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		18,		124,			125,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		27,		126,			127,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		9,		128,			129,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		10,		130,			131,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		11,		132,			133,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		12,		134,			135,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		13,		136,			137,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,
	Lamp, 0,		8,		138,			139,		RSID_TTOTAN_LAMP_TEXTURES,		EmuLamp,	
	Lamp, 0,		26,		140,			141,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		25,		142,			143,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
	Lamp, 0,		22,		144,			145,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		12,		146,			147,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		13,		148,			149,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
//	Lamp, 0,		11,		150,			151,		RSID_TTOTAN_LAMP_TEXTURES,		EmuFlasher,
TableEnd = PBObject:VisibleLights

///////////////////////////////////////////////pinball objects


//////////////////////////////////////////////////Plunger
Table = PBObject:Plunger
	Object, PB_OBJECTTYPE_PLUNGER, 2500,
	DilPos, plunger, 0,
	Models, 1, RSID_TTOTAN_MODELS, 4,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 11,
	Vars, 8, 1265.0, -2350.0, 420.0, 0, 600.0, 0, 300,  -26,
	Vibrate, Solenoid, 1.5, 1.5, 150, End,
TableEnd = PBObject:Plunger

//////////////////////////////////////////////////Tiles
// Originally these were Tiles, but they really act as Targets since they don't drop.  Keeping them named Tile so the code matches, but they are Target Object types
Table = PBObject:Tile1
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 0,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:Tile1

Table = PBObject:Tile2
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 1,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile2

Table = PBObject:Tile3
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 2,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 61, //TOTAN_PF_SW_STANDUPS_L
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile3

Table = PBObject:Tile4
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 3,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile4

Table = PBObject:Tile5
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 4,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile5

Table = PBObject:Tile6
	Object, PB_OBJECTTYPE_TARGET, 2500,
	DilPos, tile, 5,
	Models, 1, RSID_TTOTAN_MODELS, 5,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 12,
	Switch, 62, //TOTAN_PF_SW_STANDUPS_R
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:Tile6

//////////////////////////////////////////////////Targets

Table = PBObject:TargetA
	Object, PB_OBJECTTYPE_TARGET, 2500,
//	Switch, 58, // TOTAN_PF_SW_CAPTIVEBALL_L
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target A, 0,
	Lights, Def_Plastic3,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetA

Table = PBObject:TargetB
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 23, // TOTAN_PF_SW_GENIE_STANDUP
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target B, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Physics, Elasticity, 0.3, End,	
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetB

Table = PBObject:TargetC
	Object, PB_OBJECTTYPE_TARGET, 2500,
//	Switch, 48, // TOTAN_PF_SW_CAPTIVEBALL_R
	Models, 1, RSID_TTOTAN_MODELS, 6,
	DilPos, target C, 0,
	Lights, Def_Plastic3,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 13,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetC

Table = PBObject:TargetD
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 46, // TOTAN_PF_SW_MINISTANDUPS
	Models, 1, RSID_TTOTAN_MODELS, 7,
	DilPos, target D, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 14,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetD

Table = PBObject:TargetE
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 46, // TOTAN_PF_SW_MINISTANDUPS
	Models, 1, RSID_TTOTAN_MODELS, 7,
	DilPos, target E, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 14,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
TableEnd = PBObject:TargetE

Table = PBObject:TargetF
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 65, // TOTAN_PF_SW_SKILL_BOT
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target F, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetF

Table = PBObject:TargetG
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 64, // TOTAN_PF_SW_SKILL_MID
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target G, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetG

Table = PBObject:TargetH
	Object, PB_OBJECTTYPE_TARGET, 2500,
	Switch, 63, // TOTAN_PF_SW_SKILL_TOP
	Models, 1, RSID_TTOTAN_MODELS, 8,
	DilPos, target H, 0,
	Lights, Def_Plastic3,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 15,
	//AffectPhysics, False,
TableEnd = PBObject:TargetH

//////////////////////////////////////////////////Slingshots

Table = PBObject:SlingShotLeft
	Object, PB_OBJECTTYPE_SLINGSHOT, 2500,
	Solenoid, 10,
	Switch, 51, // TOTAN_PF_SW_SLING_L
	Models, 2, RSID_TTOTAN_MODELS, 9, RSID_TTOTAN_MODELS, 10,
	DilPos, slingshot A, 0,
	Lights, Def_popbumpers,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 16,
	Physics, Friction,0.45, MaxTransferVel,145,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT
	Vibrate, Solenoid, 0.75, 2.55, 120, End,
TableEnd = PBObject:SlingShotLeft

Table = PBObject:SlingShotRight
	Object, PB_OBJECTTYPE_SLINGSHOT, 2500,
	Solenoid, 11,
	Switch, 52, // TOTAN_PF_SW_SLING_R
	Models, 2, RSID_TTOTAN_MODELS, 11, RSID_TTOTAN_MODELS, 12,
	DilPos, slingshot B, 0,
	Lights, Def_popbumpers,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 17,
	Physics, Friction,0.45, MaxTransferVel,145,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT
	Vibrate, Solenoid, 0.75, 2.55, 120, End,
TableEnd = PBObject:SlingShotRight


//////////////////////////////////////////////////PopBumpers
// left = 12, right = 13, middle = 14
// switch 
Table = PBObject:PopBumper1
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 53,  // TOTAN_PF_SW_RIGHTJET 
	Solenoid, 12,
	Models, 1, RSID_TTOTAN_MODELS, 13,
	DilPos, pop bumper A, 0,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper1

Table = PBObject:PopBumper2
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 55,  // TOTAN_PF_SW_MIDJET 
	Solenoid, 14,
	Models, 1, RSID_TTOTAN_MODELS, 13,
	DilPos, pop bumper A, 1,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper2

Table = PBObject:PopBumper3
	Object, PB_OBJECTTYPE_POPBUMPER, 2500,
	Switch, 54, // TOTAN_PF_SW_LEFTJET 
	Solenoid, 13,
	Models, 1, RSID_TTOTAN_MODELS, 13, 
	DilPos, pop bumper A, 2,
	Lights, 2, Def_Metal, Def_BumperBlues,
	Vars, 2, 50, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 18,
	Physics, Elasticity,0.15, Friction,0.3, MaxTransferVel,140, End,
	Vibrate, Solenoid, 0.2, 1.5, 40, End,
TableEnd = PBObject:PopBumper3


//////////////////////////////////////////////////Wires

Table = PBObject:WireA1
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 13,
	Switch, 41, // TOTAN_PF_SW_RAMPMADE_L,
TableEnd = PBObject:WireA1

Table = PBObject:WireA2
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 12,
	Switch, 12, // TOTAN_PF_SW_VANISH
TableEnd = PBObject:WireA2
Table = PBObject:WireA3
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 11,
	Switch, 11, // TOTAN_PF_SW_HAREM,
TableEnd = PBObject:WireA3

Table = PBObject:WireA4
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 10,
	Switch, 28, //TOTAN_PF_SW_LEFTWIRE 
TableEnd = PBObject:WireA4

Table = PBObject:WireA5
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 9,
	Switch, 18, // TOTAN_PF_SW_SHOOTER,
TableEnd = PBObject:WireA5

Table = PBObject:WireA6
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 8,
	Switch, 43, //TOTAN_PF_SW_LEFTLOOP_O,
TableEnd = PBObject:WireA6

Table = PBObject:WireA7
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 7,
	Switch, 44, // TOTAN_PF_SW_LEFTLOOP_I,
TableEnd = PBObject:WireA7

Table = PBObject:WireA8
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 6,
	Switch, 45, // TOTAN_PF_SW_RIGHTLOOP_I,
TableEnd = PBObject:WireA8

Table = PBObject:WireA9
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 5,
	Switch, 0, // Ball lock 2, handled in lock stack
TableEnd = PBObject:WireA9

Table = PBObject:WireA10
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 4,
	Switch, 17, // TOTAN_PF_SW_INLANE_R
TableEnd = PBObject:WireA10

Table = PBObject:WireA11
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 3,
	Switch, 26, // TOTAN_PF_SW_INLANE_L, 
TableEnd = PBObject:WireA11

Table = PBObject:WireA12
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 2,
	Switch, 47, //TOTAN_PF_SW_RAMPMADE_R  
TableEnd = PBObject:WireA12

Table = PBObject:WireA13
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 14,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, wire A, 1,
	Switch, 68, // TOTAN_PF_SW_BALLLOCK_3,
TableEnd = PBObject:WireA13

//Table = PBObject:WireA14
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
//	DilPos, wire A, 13,
//	Switch, 25 // Bazzar, handled in trap
//TableEnd = PBObject:WireA14

// There are only 14 wires in the dil file
//Table = PBObject:WireA15
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	DilPos, wire A, 13,
//	Switch, 47, // TOTAN_PF_SW_RAMPMADE_R,
//TableEnd = PBObject:WireA15

//Table = PBObject:WireA16
//	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 14,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 19,
//	DilPos, wire A, 13,
//	Switch, 18, // TOTAN_PF_SW_SHOOTER,
//TableEnd = PBObject:WireA16


Table = PBObject:WireB1
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Sphere, Manual, 0.0,0.0,0.0,100,
	DilPos, wire B, 0,
	Switch, 37, //TOTAN_PF_SW_R_CAGE_OPTO
TableEnd = PBObject:WireB1

Table = PBObject:WireB2
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Sphere, Manual, 0.0,0.0,0.0,100,
	DilPos, wire B, 1,
	Switch, 36, //TOTAN_PF_SW_L_CAGE_OPTO
TableEnd = PBObject:WireB2


//////////////////////////////////////////////////Magnets

//Table = PBObject:MagnetA
//	Object, PB_OBJECTTYPE_MAGNET, 2500,
//	Models, 1, RSID_TTOTAN_MODELS, 15,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 21,
//	DilPos, magnet A, 0,
//	Solenoid, 39, // TOTAN_SOLENOID_VANISH_MAGNET
//	Vars, 5, 1, 70, 400, 0.8, 9000,
//	AffectPhysics, False,
//TableEnd = PBObject:MagnetA

Table = PBObject:MagnetB
	Object, PB_OBJECTTYPE_MAGNET, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 16,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 22,
	DilPos, Magnet B, 0,
	Solenoid, 6, // TOTAN_SOLENOID_LOCK_MAGNET
//		     Dampening True	  DampeningMax    Divisor    Percent    InvMulti-Stength of Magnet
	Vars, 5,       1,             70,           1000,       0.3,          12000,
	AffectPhysics, False,
	Vibrate, Solenoid, 0.25, 1.0, 1000, End,
TableEnd = PBObject:MagnetB

//Table = PBObject:MagnetC
//	Object, PB_OBJECTTYPE_MAGNET, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 23,
//	DilPos, magnet C, 0,
//	Solenoid, 8, // TOTAN_SOLENOID_RAMP_MAGNET
//	Vars, 5, 0, 0, 100, 1.0, 100,
//	AffectPhysics, False,
//TableEnd = PBObject:MagnetC


//////////////////////////////////////////////////Captive Balls

//Table = PBObject:VisibleTrappedBumper1
//	Object, PB_OBJECTTYPE_VISUAL, 2350,
//	DilPos, generic K, 0,
//	Models, 1, RSID_TTOTAN_MODELS, 30,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleTrappedBumper1

//Table = PBObject:VisibleTrappedBumper2
//	Object, PB_OBJECTTYPE_VISUAL, 2350,
//	DilPos, generic K, 1,
//	Models, 1, RSID_TTOTAN_MODELS, 30,
//	Lights, Def_Cabinet,
//TableEnd = PBObject:VisibleTrappedBumper2


// Generic K
Table = PBObject:TrappedBallBumper1
	Object, PB_OBJECTTYPE_TRAPPED_BUMPER, 2340,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 46,
	//Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Physics, Elasticity,0.1, End,	
	Pos, 832.215, -348.288, 1126.686,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	DilPos, generic K, 0,
	Link, TrappedBall1,
	Vibrate, Collision, 0.25, 1.0, 50, End, 
	EnvMap, 0, 0.59,
TableEnd = PBObject:TrappedBallBumper1

// Generic L
Table = PBObject:TrappedBall1
	Object, PB_OBJECTTYPE_TRAPPED_BALL, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 31,
	DilPos, generic L, 0,
	Vars, 4,  70, 304.0, 10.0, -1,
	Verts, 1, 1016.0, 127.949, 1138.466,
	Switch, 58, // TOTAN_PF_SW_CAPTIVEBALL_L
	Sound, Generic, GEN_SOUND_HIT_METAL,
	EnvMap, 1, 0.59,
TableEnd = PBObject:TrappedBall1

// Generic K
Table = PBObject:TrappedBallBumper2
	Object, PB_OBJECTTYPE_TRAPPED_BUMPER, 2340,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 38,
	Pos, -811.034, 527.855, 1198.128,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	DilPos, generic K, 1,
	Physics, Elasticity,0.1, End,
	Link, TrappedBall2,
	Sound, Generic, GEN_SOUND_HIT_METAL,
	Vibrate, Collision, 0.25, 1.0, 50, End,
	EnvMap, 2, 0.59,
TableEnd = PBObject:TrappedBallBumper2

// Generic L
Table = PBObject:TrappedBall2
	Object, PB_OBJECTTYPE_TRAPPED_BALL, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 31,
	DilPos, generic L, 1,
	Vars, 4, 100, 124.0, 10.0, -1,
	Verts, 1, -860.893, 810.342, 1209.908, 
	Switch, 48, // TOTAN_PF_SW_CAPTIVEBALL_R
	Sound, Generic, GEN_SOUND_HIT_Metal,
	EnvMap, 3, 0.59,
TableEnd = PBObject:TrappedBall2



//////////////////////////////////////////////////Traps

//ORB
Table = PBObject:TrapA
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 17,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 24,
	DilPos, Trap A, 0,
	Switch, 38,
	Solenoid, 15,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 0,-132,40,   12.0,-30.0,15.0, 10.0, 0,
TableEnd = PBObject:TrapA

//Multiball
// Trap settle speed of 10,000 to instantly trap
Table = PBObject:TrapB
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 18,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 25,
	DilPos, Trap B, 0,
	Solenoid, 4,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 72,-132,70,   10,-80.0,20.0,  100000.0, 0,		
	BallStack, 3, 66, 67, 68,
TableEnd = PBObject:TrapB

//Bazaar
Table = PBObject:TrapC
	Object, PB_OBJECTTYPE_TRAP, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 19,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 26,
	DilPos, trap c, 0,
	Switch, 25,
	Solenoid, 5,
	Physics, Elasticity,0.0, Dampening,0.8,0.8,1,End,
	Vars, 8, 517.0,309.0,1172.0,   -16.0,-70.0,65.0, 10.0, 0,
	Vibrate, Collision, 1.0, 1.5, 70, Solenoid, 1.0, 1.5, 120, End,
TableEnd = PBObject:TrapC


//////////////////////////////////////////////////Generics

//generic H = 1 outlane left   TOTAN_PF_SW_OUTLANE_L
Table = PBObject:GenericH
	Models, 1, RSID_TTOTAN_MODELS, 27,
	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 27,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, generic H, 0,
	Switch, 16, //TOTAN_PF_SW_OUTLANE_L
TableEnd = PBObject:GenericH

//generic I = 1 outlane right  TOTAN_PF_SW_OUTLANE_R
Table = PBObject:GenericI
	Models, 1, RSID_TTOTAN_MODELS, 28,
	Object, PB_OBJECTTYPE_WIRE, 2500,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 28,
	Collision, Sphere, Manual, 0.0,0.0,0.0,50,
	DilPos, generic I, 0,
	Switch, 27, //TOTAN_PF_SW_OUTLANE_R
TableEnd = PBObject:GenericI

//generic Q = 1 left loop TOTAN_PF_SW_LEFTLOOP_O
Table = PBObject:GenericQ
	Object, PB_OBJECTTYPE_WIRE, 2500,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 42,
	DilPos, generic Q, 0,
	Switch, 43, //TOTAN_PF_SW_LEFTLOOP_O
TableEnd = PBObject:GenericQ

//generic J = 1 Rollunder spinner typ  TOTAN_PF_SW_RAMP_ENTER
Table = PBObject:GenericJ
	Object, PB_OBJECTTYPE_GATE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 29,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 36,
	DilPos, generic J, 0,
	Switch, 15, //TOTAN_PF_SW_RAMP_ENTER
	Vars, 4,	90.0,	-0.01,	0.83,   75.0,
TableEnd = PBObject:GenericJ

//generic B = 1 left cage
Table = PBObject:GenericB
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic B, 0,
	Models, 1, RSID_TTOTAN_MODELS, 21,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 28,
	Vars, 2, 150, 150,
	Solenoid, 1, 
	SolenoidSound, Unique, RSID_TTOTAN_MECH_SOUNDS, 7, RSID_TTOTAN_MECH_SOUNDS, 7,
	Vibrate, Solenoid, 0.25, 1.5, 1200, End, 
	EnvMapReflection, True,
TableEnd = PBObject:GenericB

//generic C = 1 right cage
Table = PBObject:GenericC
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic C, 0,
	Models, 1, RSID_TTOTAN_MODELS, 22,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 29,
	Vars, 2, 150, 150,
	Solenoid, 2, 
	SolenoidSound, Unique, RSID_TTOTAN_MECH_SOUNDS, 7, RSID_TTOTAN_MECH_SOUNDS, 7, 
	Vibrate, Solenoid, 0.25, 1.5, 1200, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericC


//generic O = 1 diverter collision closed
Table = PBObject:GenericO
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 40,
	DilPos, generic O, 0,
TableEnd = PBObject:GenericO

//generic P = 1 diverter collision open
Table = PBObject:GenericP
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 41,
	DilPos, generic P, 0,
TableEnd = PBObject:GenericP

//generic N = 1 left loop diverter (uses generic O and P for collisions) 
Table = PBObject:GenericN
	Object, PB_OBJECTTYPE_DIVERTER, 2500,
	DilPos, generic N, 0,
	Models, 1, RSID_TTOTAN_MODELS, 33,
	Vars, 3, -20, -20, 15,
	Link, GenericO;GenericP, 
	Solenoid, 34, //TOTAN_SOLENOID_LEFT_DIV_HOLD
TableEnd = PBObject:GenericN


//generic M = 1 loop post diverter
Table = PBObject:GenericM
	Object, PB_OBJECTTYPE_STOPPER, 2500,
	DilPos, generic M, 0,
	Models, 1, RSID_TTOTAN_MODELS, 32,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 37,
	Vars, 3, 150, 150, 15
	Solenoid, 36, //TOTAN_SOLENOID_LOOP_POST_DIV
TableEnd = PBObject:GenericM

//generic R = 1 ball lock wall
Table = PBObject:GenericR
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 43,
	DilPos, generic R, 0,
TableEnd = PBObject:GenericR


//generic D = 1 lamp
Table = PBObject:GenericD
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic D, 0,
	Models, 1, RSID_TTOTAN_MODELS, 23,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 30,
	Func, CPBTalesLampSetCB,
	Sound, Generic, GEN_SOUND_HIT_default
	Lights, Def_Plastic2,
	Vibrate, Collision, 0.25, 0.50, 50, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericD

//Magnet A - Unique type cause it has special code vs regular magnet must be before generic A
Table = PBObject:MagnetA
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 15,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 21,
	DilPos, magnet A, 0,
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Func, CPBTalesMagnetASetCB,
	Solenoid, 35, // TOTAN_SOLENOID_VANISH_MAGNET
	//Vars, 5, 1, 70, 400, 0.65, 18000,	// old way
	Vars, 5, 1, 70, 900, 0.5, 18000,	// ball wobbles less
	AffectPhysics, False,
	Vibrate, Solenoid, 1.5, 0.25, 50, End,
TableEnd = PBObject:MagnetA

//generic A = 1 drop magnet (drops -1000 on z when solenoid event TOTAN_SOLENOID_VANISH_DROP) (Also drops magnet A)
Table = PBObject:GenericA
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic A, 0,
	Models, 1, RSID_TTOTAN_MODELS, 20,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 27,
	Func, CPBTalesDropMagnetSetCB,
	Solenoid, 3, // TOTAN_SOLENOID_VANISH_DROP
	Vibrate, Solenoid, 1.5, 1.0, 1000, End,
TableEnd = PBObject:GenericA

//generic E = 1 genie  TOTAN_PF_SW_GENIE
Table = PBObject:GenericE
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	DilPos, generic E, 0,
	Models, 1, RSID_TTOTAN_MODELS, 24,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 31,
	Func, CPBTalesGenieSetCB,
	Switch, 42, //TOTAN_PF_SW_GENIE
	Sound, Generic, GEN_SOUND_HIT_PLASTIC
	Lights, Def_Cabinet,
	Vibrate, Collision, 0.2, 1.0, 40, End,
	EnvMapReflection, True,
TableEnd = PBObject:GenericE


//generic G = 1 Round magnet ball gets pulled inside of along the fireball ramp
Table = PBObject:GenericG
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Models, 1, RSID_TTOTAN_MODELS, 26,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 33,
	DilPos, generic G, 0,
	Func, CPBTalesRingMagnetSetCB,
	AffectPhysics, False,
TableEnd = PBObject:GenericG

//Magnet C - Unique type cause it has special code vs regular magnet
Table = PBObject:MagnetC
	Object, PB_OBJECTTYPE_UNIQUE, 2500,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 23,
	DilPos, magnet C, 0,
	Solenoid, 8, // TOTAN_SOLENOID_RAMP_MAGNET
	Vars, 5, 0, 0, 100, 1.0, 100,
	AffectPhysics, False,
	Func, CPBTalesMagnetCSetCB,
	Vibrate, Solenoid, 0.25, 1.5, 100, End,
TableEnd = PBObject:MagnetC

//////////////////////////////////////////////////Collisions


Table = PBObject:PlungerArea
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	DilPos, plunger, 0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 44,
	AffectPhysics, False,	// used to change from plunger cam to normal
	Physics, Elasticity,0.25, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-30,0 Dampening,1,1,1,End,	
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:PlungerArea

Table = PBObject:AltSkill
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 5,
	Physics, Elasticity,0.25, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-25,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:AltSkill

Table = PBObject:AltWall
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 4,
	Physics, Elasticity,0.6, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-35,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:AltWall

// Ramp

Table = PBObject:Vanish
	Object, PB_OBJECTTYPE_UNIQUE, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 58,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Func, CPBTalesVanishRampSetCB,
	Vars, 2, 25.0, 60,	// speed which ball drops, delay
TableEnd = PBObject:Vanish

//generic F = 1 Ramp diverter
Table = PBObject:GenericF
	Object, PB_OBJECTTYPE_DIVERTER, 2500,
	DilPos, generic F, 0,
	Models, 1, RSID_TTOTAN_MODELS, 25,
	//Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 32,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-22,0 Dampening,1,1,1,End,
	Vars, 3, 30, 30, 10,
	Solenoid, 21, //TOTAN_SOLENOID_RAMP_DIVERTER
TableEnd = PBObject:GenericF

Table = PBObject:Col_Diverter_Vanish
	Object, PB_OBJECTTYPE_DIVERTER_ONOFF, 2500,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 50,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Vars, 4, 30,30,10,1,
	Solenoid, 21, //TOTAN_SOLENOID_RAMP_DIVERTER
TableEnd = PBObject:Col_Diverter_Vanish

Table = PBObject:RampMetal
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 47,
	Physics, Elasticity,0.0001, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,0.000000001, Gravity,0,0,-60,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:RampMetal

Table = PBObject:RampFrontLeft
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 53,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampFrontLeft

Table = PBObject:RampFrontRight
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 56,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampFrontRight

Table = PBObject:RampCol_HabbiTrail
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 51,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_HabbiTrail

Table = PBObject:RampCol_RampLeft
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 52,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampLeft

Table = PBObject:RampCol_RampMiddle
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 54,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-22,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampMiddle

Table = PBObject:RampCol_RampRight
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 55,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-32,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampRight

Table = PBObject:RampCol_RampSpiral
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 57,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-15,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
TableEnd = PBObject:RampCol_RampSpiral



//Table = PBObject:Ramp
//	Object, PB_OBJECTTYPE_COLLISION, 10000, 
//	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
//	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 3,
//	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,-24,0 Dampening,1,1,1,End,
//	Sound, Generic, GEN_SOUND_HIT_PLASTIC,
//TableEnd = PBObject:Ramp


Table = PBObject:Arc
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 2,
	Physics, Elasticity,0.35, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,.5 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:Arc

Table = PBObject:Collision_Flipper_Lane
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 49,
	Physics, Elasticity,0.35, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,.5 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:Collision_Flipper_Lane

Table = PBObject:Wall
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 0,
	Physics, Elasticity,0.1, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-35,0 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
TableEnd = PBObject:Wall

Table = PBObject:Platform
	Object, PB_OBJECTTYPE_BALLDRAIN, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 45,
	Physics, Elasticity,0.2, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,0,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_DEFAULT,
	Vibrate, Collision, 0.20, 1.0, 200, End,
TableEnd = PBObject:Platform

Table = PBObject:SkillShot
	Object, PB_OBJECTTYPE_COLLISION, 10000, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 48,
	Physics, Elasticity,0.1, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,2, Gravity,0,0,-70,0 Dampening,0.1,0.1,0.1,End,
	Sound, Generic, GEN_SOUND_HIT_METAL,
TableEnd = PBObject:SkillShot

Table = PBObject:Floor
	Object, PB_OBJECTTYPE_FLOOR, 2600, 
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	Models, 1, RSID_TTOTAN_MODELS, 34,
	Lights, Def_Floor,
	Collision, Mesh, 1, RSID_TTOTAN_COLLISIONS, 1,
	Physics, Elasticity,0.3, Friction,0.0, RollNormalVel,10, RollTransferAmt,0, MaxTransferVel,0,RollNormal,30, Gravity,0,0,0,0 Dampening,1,1,1,End,
	Sound, Generic, GEN_SOUND_HIT_GROUND,
	EnvMapReflection, True,
TableEnd = PBObject:Floor

Table = PBObject:CameraTrigger
	Object, PB_OBJECTTYPE_CAMERA_TRIGGER, 1000,
	Pos, 0,0,0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	AffectPhysics, False,
//               	
	Vars, 8, 1237.0,0.0,1650.0,50, 1250,165,1415,60,
TableEnd = PBObject:CameraTrigger

Table = PBObject:PlungerExit
	Object, PB_OBJECTTYPE_PLUNGER_EXIT, 1000,
	Pos, 0,0,0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	AffectPhysics, False,
	Vars, 8, 1237.0, -835.0, 1346.0,50, 1236.0, -1603.0, 1074.0,50,
TableEnd = PBObject:PlungerExit

// Environment
Table = PBObject:GenericEnvironment
	Object, PB_OBJECTTYPE_VISUAL, 150,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	EnvironmentModel, 0,
	Lights, Def_generic_env,
	EnvMapReflection, False,
TableEnd = PBObject:GenericEnvironment

Table = PBObject:GenericEnvironmentCeiling
	Object, PB_OBJECTTYPE_VISUAL, 150,
	Pos, 0.0, 0.0, 0.0,   1.0, 1.0, 1.0,   0.0, 0.0, 0.0,
	EnvironmentModel, 1,
	Lights, Def_generic_env,
	EnvMapReflection, True,
TableEnd = PBObject:GenericEnvironmentCeiling



// flipper R = 360.7101, -2138.0405, 955.4877
// flipper L = -603.5554, -2143.6567, 957.2347
// plunger = 1266.1338, -3558.0581, 910.6148
// tile = -1134.4805, -261.8982, 1121.5990
// tile = -1118.4543, -55.4221, 1136.0372
// tile = -1149.5691, -469.3819, 1107.0903
// tile = 188.1027, 1498.1730, 1244.6752
// tile = 263.0561, 1304.7662, 1231.1508
// tile = 370.4385, 1132.6183, 1219.1130
// target A = -889.3380, 919.0559, 1197.2909
// target B = 0.9089, 2257.7563, 1290.9012
// target C = 1037.2719, 236.6223, 1149.5702
// target D = -481.8063, 1376.3110, 1235.6205
// target E = 671.6626, 251.2317, 1156.9476
// target F = 1239.0867, 275.4537, 1527.9266
// target G = 1224.7332, 662.2930, 1566.4452
// target H = 1212.1437, 1062.9045, 1605.4091
// slingshot A = -792.4070, -1471.6940, 1060.4587
// slingshot B = 528.7260, -1471.6940, 1060.4587
// pop bumper A = 347.7016, 2563.0825, 1398.3002
// pop bumper A = 942.6722, 2514.5400, 1394.9061
// pop bumper A = 876.5354, 1856.6926, 1348.9052
// wire A = -976.0285, 2501.9219, 1601.9269
// wire A = -8.6627, 1333.0244, 979.6151
// wire A = -109.4578, 2260.1436, 1052.6184
// wire A = -927.8615, 1891.9006, 1573.1903
// wire A = 1267.9352, -2366.4795, 947.2161
// wire A = -1141.9889, 2271.9053, 1256.8906
// wire A = -548.9714, 2533.7197, 1275.1985
// wire A = 626.9449, 1744.9133, 1220.0399
// wire A = 310.7825, 1499.4248, 1210.4258
// wire A = 756.3146, -1427.1373, 998.2285
// wire A = -1034.5651, -1438.1260, 997.4601
// wire A = 779.9927, 2023.6858, 1722.2205
// wire A = 311.6658, 1688.9109, 1227.1195
// wire B = 750.7159, -1023.2439, 1013.4198
// wire B = -1030.3644, -1023.2439, 1013.4198
// Magnet B = 323.2439, 2089.7759, 1220.2484
// generic K = -808.3806, 522.6459, 1198.1283
// generic L = 896.8094, -173.9697, 1138.4657
// generic K = 829.4173, -329.5883, 1126.6859
// generic L = -836.6783, 678.2645, 1209.9081
// Trap A = -782.1801, 379.9615, 1091.9789
// Trap B = 399.6355, 1371.1245, 1153.1566
// trap c = 0.0000, 0.0000, -0.0001
// generic H = -1256.6378, -1936.9868, 939.2474
// generic I = 951.7297, -1915.1860, 940.7714
// generic Q = 0.0000, 0.0000, 0.0000
// generic J = -905.9058, 1744.3557, 1420.7572
// generic B = -1030.0459, -1021.7445, 909.0013
// generic C = 753.3507, -1016.2350, 907.8347
// generic O = 0.0000, 0.0000, 0.0000
// generic P = 0.0000, 0.0000, 0.0000
// generic N = -1074.7311, 2218.3667, 1267.5443
// generic F = 337.2099, 2269.6528, 1716.6156
// generic M = 934.3212, 2811.0874, 850.3143
// generic R = 0.0000, 0.0000, 0.0000
// generic D = 126.3894, 640.9906, 1488.8861
// magnet A = -231.4876, 1663.6775, 1191.0629
// generic A = -232.3552, 1663.4944, 1197.7211
// generic E = -353.6015, 2226.3193, 1675.0170
// generic G = -972.8077, 2179.8677, 1737.1039
// magnet C = -952.7455, 2170.3081, 1736.5258
// plunger = 1266.1338, -3558.0581, 910.6148




















