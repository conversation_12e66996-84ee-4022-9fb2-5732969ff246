#pragma once

#include "memory_compressor.h"
#include "memory_prefetcher.h"
#include "memory_types.h"
#include "swap_manager.h"
#include "tlb.h"
#include <atomic>
#include <chrono>
#include <cstdint>
#include <fmt/format.h>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

#ifdef STRICT
#undef STRICT
#endif

namespace ps4 {
class Memory;
class MemoryCompressor;
class MemoryPrefetcher;
class SwapManager;
class PS4Emulator;

static constexpr uint64_t PAGE_SIZE = 4096;           ///< Page size (bytes)
static constexpr uint64_t USERLAND_BASE = 0x10000;    ///< Userland base address
static constexpr uint64_t USERLAND_SIZE = 0x10000000; ///< Userland size
static constexpr uint64_t TOTAL_SIZE = 1ULL * 1024 * 1024 * 1024; ///< 1GB
static constexpr uint64_t MAX_PAGES_PER_PROCESS =
    USERLAND_SIZE / PAGE_SIZE; ///< Maximum pages per process
static constexpr uint64_t ALLOC_FAILED =
    UINT64_MAX; ///< Allocation failure indicator for PhysicalMemoryAllocator

struct PhysicalMemoryBlock {
  uint64_t startAddr = 0; ///< Starting address
  uint64_t size = 0;      ///< Size (bytes)
};

struct AllocationStats {
  uint64_t totalAllocations = 0;
  uint64_t totalFrees = 0;
  uint64_t currentAllocations = 0;
  uint64_t peakAllocations = 0;
  uint64_t latencyUs = 0;
  uint64_t totalLatencyUs = 0;
  uint64_t allocationCount = 0;
  uint64_t freeCount = 0;
  uint64_t fragmentationCount = 0;
  uint64_t cacheHits = 0;
  uint64_t cacheMisses = 0;
};

class PhysicalMemoryAllocator {
public:
  PhysicalMemoryAllocator();
  ~PhysicalMemoryAllocator() = default;
  void Initialize(uint64_t totalSize);
  uint64_t Allocate(uint64_t size, int priority = 0);
  void Free(uint64_t addr, uint64_t size);
  std::pair<uint64_t, uint64_t> GetUsageStats() const;
  AllocationStats GetStats() const;
  void ResetStats();
  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);

private:
  std::vector<PhysicalMemoryBlock> freeBlocks;
  std::vector<PhysicalMemoryBlock> usedBlocks;
  mutable std::shared_mutex mutex_;
  AllocationStats stats_;
};

struct MemoryRegion {
  uint64_t start;
  uint64_t size;
  MemoryType type;
  bool dynamic;
};

enum MemoryProtection {
  PROT_NONE = 0,
  PROT_READ = 1 << 0,
  PROT_WRITE = 1 << 1,
  PROT_EXEC = 1 << 2
};

struct X86_64PageTableEntry {
  union {
    uint64_t raw;
    struct {
      uint64_t present : 1;
      uint64_t writable : 1;
      uint64_t user : 1;
      uint64_t writeThrough : 1;
      uint64_t cacheDisable : 1;
      uint64_t accessed : 1;
      uint64_t dirty : 1;
      uint64_t pageSize : 1;
      uint64_t global : 1;
      uint64_t available1 : 3; // Reserved for custom metadata
      uint64_t physAddr : 40;
      uint64_t available2 : 11; // Reserved for custom metadata
      uint64_t executeDisable : 1;
    } bits;
  };

  X86_64PageTableEntry() : raw(0) {}
  explicit X86_64PageTableEntry(uint64_t value) : raw(value) {}

  uint64_t GetPhysicalAddress() const { return bits.physAddr << 12; }
  void SetPhysicalAddress(uint64_t addr) {
    bits.physAddr = (addr >> 12) & 0xFFFFFFFFF;
  }
};

struct X86_64PageTable {
  uint64_t cr3;
  std::vector<X86_64PageTableEntry> pml4;
  std::vector<std::vector<X86_64PageTableEntry>> pdpt;
  std::vector<std::vector<X86_64PageTableEntry>> pd;
  std::vector<std::vector<X86_64PageTableEntry>> pt;

  X86_64PageTable() : cr3(0) { pml4.resize(512); }
};

struct PageTableEntry {
  uint64_t physAddr = 0;
  int protection = PROT_NONE;
  bool present = false;
  bool writable = false; // Add missing writable field
  bool user = true;
  bool accessed = false; // Add missing accessed field
  bool dirty = false;
  bool shared = false;
  uint64_t processId = 0;
  MemoryType type = MemoryType::Default;
  uint64_t compressedId = 0;
  uint64_t accessCount = 0;
  std::chrono::steady_clock::time_point lastAccessTime =
      std::chrono::steady_clock::now();
  bool swapped = false;
  bool copyOnWrite = false;
  bool writeThrough = false;
  bool cacheDisable = false;
  bool global = false;
  bool executeDisable = false;
};

struct MemoryStats {
  std::atomic<uint64_t> totalPages{0};
  std::atomic<uint64_t> usedPages{0};
  std::atomic<uint64_t> freePages{0};
  std::atomic<uint64_t> compressedPages{0};
  std::atomic<uint64_t> swappedPages{0};
  std::atomic<float> compressionRatio{1.0f};
  std::atomic<float> swapUsageRatio{0.0f};
  std::atomic<uint64_t> pageFaults{0};
  std::atomic<uint64_t> totalLatencyUs{0};
  std::atomic<uint64_t> hits{0};
  std::atomic<uint64_t> misses{0};

  // Copy constructor for atomic members
  MemoryStats() = default;
  MemoryStats(const MemoryStats &other)
      : totalPages(other.totalPages.load()), usedPages(other.usedPages.load()),
        freePages(other.freePages.load()),
        compressedPages(other.compressedPages.load()),
        swappedPages(other.swappedPages.load()),
        compressionRatio(other.compressionRatio.load()),
        swapUsageRatio(other.swapUsageRatio.load()),
        pageFaults(other.pageFaults.load()),
        totalLatencyUs(other.totalLatencyUs.load()), hits(other.hits.load()),
        misses(other.misses.load()) {}

  // Assignment operator for atomic members
  MemoryStats &operator=(const MemoryStats &other) {
    if (this != &other) {
      totalPages.store(other.totalPages.load());
      usedPages.store(other.usedPages.load());
      freePages.store(other.freePages.load());
      compressedPages.store(other.compressedPages.load());
      swappedPages.store(other.swappedPages.load());
      compressionRatio.store(other.compressionRatio.load());
      swapUsageRatio.store(other.swapUsageRatio.load());
      pageFaults.store(other.pageFaults.load());
      totalLatencyUs.store(other.totalLatencyUs.load());
      hits.store(other.hits.load());
      misses.store(other.misses.load());
    }
    return *this;
  }
};

enum class PrefetchHint {
  PREFETCH_NONE = 0,
  PREFETCH_CONSERVATIVE = 1,
  PREFETCH_MODERATE = 2,
  PREFETCH_AGGRESSIVE = 3
};

enum class SecurityLevel { BASIC = 0, ENHANCED = 1, STRICT = 2 };

class PS4MMU {
public:
  explicit PS4MMU(ps4::Memory &memory);
  explicit PS4MMU(ps4::Memory &memory, PS4Emulator &emulator);
  PS4MMU();
  bool Initialize();
  void Shutdown();
  uint64_t GetReadCount() const;
  uint64_t GetWriteCount() const;
  uint64_t GetPageFaultCount() const;
  uint64_t GetCompressedPageCount() const;
  float GetCompressionRatio() const;
  uint64_t GetCompressionCycles() const;
  uint64_t GetDecompressionCycles() const;
  uint64_t AllocateVirtual(uint64_t processId, uint64_t size,
                           uint64_t alignment, int protection,
                           bool shared = false,
                           MemoryType type = MemoryType::Default);
  void FreeVirtual(uint64_t virtAddr, uint64_t processId);
  bool PreallocateMemoryRegions(const std::vector<MemoryRegion> &regions);
  bool DefragmentMemory();
  bool SetMemoryQuota(uint64_t processId, uint64_t maxMemory);
  bool EnableMemoryCompression(bool enable);
  bool EnableMemoryDeduplication(bool enable);
  bool MapMemory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                 uint32_t protection, uint64_t processId);
  bool MapMemory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                 int protection, uint64_t processId);
  bool UnmapMemory(uint64_t virtAddr, size_t size, uint64_t processId);
  bool ProtectMemory(uint64_t virtAddr, size_t size, int protection,
                     uint64_t processId);
  bool ReadVirtual(uint64_t virtAddr, void *data, size_t size,
                   uint64_t processId) const;
  bool WriteVirtual(uint64_t virtAddr, const void *data, size_t size,
                    uint64_t processId);
  bool WriteVirtualChunk(uint64_t virtAddr, const void *data, size_t size,
                         uint64_t processId);
  bool ReadVirt(uint64_t virtAddr, void *data, size_t size,
                uint64_t processId) const {
    return ReadVirtual(virtAddr, data, size, processId);
  }
  bool WriteVirt(uint64_t virtAddr, const void *data, size_t size,
                 uint64_t processId) {
    return WriteVirtual(virtAddr, data, size, processId);
  }
  bool ReadPhysical(uint64_t physAddr, void *data, size_t size) const;
  bool WritePhysical(uint64_t physAddr, const void *data, size_t size);
  uint64_t VirtualToPhysical(uint64_t virtAddr, uint64_t processId,
                             bool write) const;
  /**
   * @brief Maps a virtual address range to a physical address range.
   * @param virtAddr Virtual address to map.
   * @param physAddr Physical address to map to.
   * @param size Size of the mapping.
   * @param processId Process ID for the mapping.
   * @return True on success, false otherwise.
   */
  bool MapVirtualToPhysical(uint64_t virtAddr, uint64_t physAddr, uint64_t size,
                            uint64_t processId);

  /**
   * @brief Sets up a page table entry for x86-64 architecture.
   * @param virtAddr Virtual address.
   * @param physAddr Physical address.
   * @param processId Process ID.
   * @param protection Memory protection flags.
   * @param cr3 CR3 register value (page table base).
   * @return True on success, false otherwise.
   */
  bool SetupPageTableEntry(uint64_t virtAddr, uint64_t physAddr,
                           uint64_t processId, int protection, uint64_t cr3);

  MemoryType GetMemoryType(uint64_t virtAddr, uint64_t processId) const;
  bool SetMemoryType(uint64_t virtAddr, uint64_t size, uint64_t processId,
                     MemoryType type);
  int GetPageProtection(uint64_t virtAddr, uint64_t processId) const;
  uint64_t GetBaseAddress() const { return USERLAND_BASE; }
  uint64_t GetTotalMemorySize() const { return m_size; }
  ps4::TLB &GetTLB() { return *m_tlb; }
  float GetBandwidthUsage() const;
  template <typename T> T Read(uint64_t address, uint64_t processId = 1) {
    T value;
    if (!ReadVirtual(address, &value, sizeof(T), processId)) {
      throw std::runtime_error("Failed to read from virtual memory");
    }
    return value;
  }
  template <typename T>
  void Write(uint64_t address, const T &value, uint64_t processId = 1) {
    if (!WriteVirtual(address, &value, sizeof(T), processId)) {
      throw std::runtime_error("Failed to write to virtual memory");
    }
  }
  void ResetBandwidthCounters();
  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);
  void SetPrefetchHint(PrefetchHint hint);
  PrefetchHint GetPrefetchHint() const { return m_prefetchHint; }
  MemoryPrefetcher::Stats GetPrefetcherStats() const;
  CompressionStats GetCompressionStats() const;
  void SetCompressionPolicy(MemoryCompressor::CompressionPolicy policy);
  void
  SetCompressionAlgorithm(MemoryCompressor::CompressionAlgorithm algorithm);
  void EnableCompression(bool enabled);
  bool IsCompressionEnabled() const { return m_compressionEnabled; }
  bool HandlePageFault(uint64_t virtAddr, uint64_t processId, bool write);
  MemoryStats GetMemoryStats() const;
  MemoryStats GetStats() const;
  uint64_t CompressEligiblePages();
  uint64_t DecompressAllPages(uint64_t processId);
  bool ReconfigureMemoryRegion(uint64_t start, uint64_t size, MemoryType type,
                               bool dynamic);
  bool ExportMemoryStats(const std::string &filePath) const;

  // FIX: Added declarations for Get... functions
  uint64_t GetTotalAllocated() const;
  uint64_t GetTotalFreed() const;
  uint64_t GetCurrentAllocated() const;
  uint64_t GetPeakAllocated() const;
  uint64_t GetAllocationCount() const;
  uint64_t GetFreeCount() const;

  // TODO #11: CRITICAL FIX: Multi-Level Page Table Support
  bool InitializeX86_64PageTables(uint64_t processId);
  bool MapX86_64Memory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                       int protection, uint64_t processId);
  uint64_t TranslateX86_64Address(uint64_t virtAddr, uint64_t processId,
                                  bool write) const;

  // TODO #12: CRITICAL FIX: Memory Protection Enhancements
  bool EnhancedProtectMemory(uint64_t virtAddr, size_t size, int protection,
                             uint64_t processId,
                             MemoryType memoryType = MemoryType::Default);
  bool ValidateProtectionFlags(int protection) const;
  bool ValidateMemoryTypeProtection(MemoryType memoryType,
                                    int protection) const;
  bool EnhancedProtectX86_64Memory(uint64_t virtAddr, size_t size,
                                   int protection, uint64_t processId,
                                   MemoryType memoryType);
  bool EnhancedProtectSimpleMemory(uint64_t virtAddr, size_t size,
                                   int protection, uint64_t processId,
                                   MemoryType memoryType);

private:
  enum class FaultType {
    GUARD_PAGE,
    COPY_ON_WRITE,
    DEMAND_PAGING,
    PROTECTION_FAULT,
    UNKNOWN
  };
  FaultType AnalyzeFaultType(uint64_t virtAddr, uint64_t processId,
                             bool write) const;
  bool HandleGuardPageFault(uint64_t virtAddr, uint64_t processId);
  bool HandleDemandPaging(uint64_t virtAddr, uint64_t processId);
  void AllocatePage(uint64_t virtAddr, uint64_t processId, int protection,
                    bool shared, MemoryType type);
  uint64_t AllocatePageTableLevel(int level);
  uint64_t WalkPageTable(uint64_t virtAddr, uint64_t processId, bool write,
                         uint64_t cr3) const;
  uint64_t CreateX86PageTable(uint64_t processId);
  void MapPageX86(uint64_t virtAddr, uint64_t physAddr, uint64_t processId,
                  int protection, bool user = true,
                  bool executeDisable = false);
  void CheckProtectionEnhanced(const PageTableEntry &entry, bool write,
                               bool user, bool execute,
                               uint64_t virtAddr) const;
  bool HandleCopyOnWrite(uint64_t virtAddr, uint64_t processId);
  uint64_t FindFreePhysicalBlock(uint64_t size);
  uint64_t FindFreeVirtualRange(uint64_t processId, uint64_t size,
                                uint64_t alignment);
  void MarkPageDirty(uint64_t virtAddr, uint64_t processId);

  // LOGIC FIX: Helper to calculate latency and avoid consteval issues
  inline uint64_t CalculateLatencyMicroseconds(
      const std::chrono::steady_clock::time_point &start,
      const std::chrono::steady_clock::time_point &end) const {
    auto duration = end - start;
    return std::chrono::duration_cast<std::chrono::microseconds>(duration)
        .count();
  }
  void CheckProtection(const PageTableEntry &page, bool write,
                       uint64_t virtAddr) const;
  void CheckMemoryTypeAccess(MemoryType type, bool write, uint64_t virtAddr,
                             int protection) const;
  void CheckAndCompressPage(uint64_t virtAddr, uint64_t processId);
  bool DecompressPageIfNeeded(uint64_t virtAddr, uint64_t processId);
  bool TryCompressPage(uint64_t virtAddr, uint64_t processId);
  void UpdatePageAccess(uint64_t virtAddr, uint64_t processId);
  void HandleAccess(uint64_t virtAddr, size_t size, bool isWrite,
                    uint64_t processId);
  bool SwapPageOut(uint64_t virtAddr, uint64_t processId);
  bool SwapPageIn(uint64_t virtAddr, uint64_t processId);

  // Fiber management integration helper methods
  bool HandleFiberManagement(uint64_t operationComplexity,
                             uint64_t currentFiberId);
  uint64_t
  WalkPageTableEnhanced(uint64_t virtAddr, uint64_t processId, bool write,
                        uint64_t cr3,
                        SecurityLevel securityLevel = SecurityLevel::BASIC);
  bool UpdatePageTableEntryFields(PageTableEntry &entry, uint64_t physAddr,
                                  int protection, MemoryType memoryType,
                                  uint64_t processId);

  // Member variables reordered to minimize padding (optimal order suggested by compiler)
  alignas(64) std::vector<uint8_t> m_physicalMemory;
  std::unique_ptr<PhysicalMemoryAllocator> m_physAlloc;
  size_t m_size = 0;
  std::unique_ptr<ps4::TLB> m_tlb;
  std::atomic<uint64_t> m_nextPhysAddr{TOTAL_SIZE / 2};
  mutable std::shared_mutex m_mutex;
  mutable std::atomic<uint64_t> m_readBytes{0};
  mutable std::atomic<uint64_t> m_writeBytes{0};
  ps4::Memory *m_memory{nullptr};
  PS4Emulator *m_emulator{nullptr};
  std::unique_ptr<MemoryPrefetcher> m_prefetcher;
  std::unique_ptr<MemoryCompressor> m_compressor;
  std::unique_ptr<SwapManager> m_swapManager;
  mutable std::shared_mutex m_accessMutex;
  std::vector<MemoryRegion> m_memoryRegions;
  std::unordered_map<uint64_t, std::vector<PageTableEntry>> m_pageTables;
  std::unordered_map<uint64_t, X86_64PageTable> m_x86PageTables;
  std::unordered_map<uint64_t, uint64_t> m_processCR3;
  std::unordered_map<uint64_t,
                     std::unordered_map<uint64_t, ps4::PageAccessInfo>>
      m_pageAccessInfo;
  mutable MemoryStats m_stats;
  PrefetchHint m_prefetchHint = PrefetchHint::PREFETCH_NONE;
  std::atomic<bool> m_compressionEnabled{false};
};

} // namespace ps4
